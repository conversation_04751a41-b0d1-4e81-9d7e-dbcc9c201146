package com.embrate.cloud.core.api.application.mobile;

import com.lernen.cloud.core.api.user.Module;

import java.util.List;
import java.util.UUID;

public class UserModulePermissionPayload {

    private List<UUID> userIdList;

    private List<Module> moduleList;

    public UserModulePermissionPayload() {
    }

    public UserModulePermissionPayload(List<UUID> userIdList, List<Module> moduleList) {
        this.userIdList = userIdList;
        this.moduleList = moduleList;
    }

    public List<UUID> getUserIdList() {
        return userIdList;
    }

    public void setUserIdList(List<UUID> userIdList) {
        this.userIdList = userIdList;
    }

    public List<Module> getModuleList() {
        return moduleList;
    }

    public void setModuleList(List<Module> moduleList) {
        this.moduleList = moduleList;
    }

    @Override
    public String toString() {
        return "UserModulePermissionPayload{" +
                "userIdList=" + userIdList +
                ", moduleList=" + moduleList +
                '}';
    }
}
