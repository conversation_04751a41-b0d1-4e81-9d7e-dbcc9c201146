package com.embrate.cloud.core.api.attendance.notification;

import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.user.UserType;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class UserAttendanceNotificationData {

    private final UserType userType;

    private final UUID userId;

    private final int attendanceDate;

    private final Map<Integer, AttendanceStatus> attendanceStatus;

    public UserAttendanceNotificationData(UserType userType, UUID userId, int attendanceDate, Map<Integer, AttendanceStatus> attendanceStatus) {
        this.userType = userType;
        this.userId = userId;
        this.attendanceDate = attendanceDate;
        this.attendanceStatus = attendanceStatus;
    }

    public UserType getUserType() {
        return userType;
    }

    public UUID getUserId() {
        return userId;
    }

    public int getAttendanceDate() {
        return attendanceDate;
    }

    public Map<Integer, AttendanceStatus> getAttendanceStatus() {
        return attendanceStatus;
    }

    @Override
    public String toString() {
        return "UserAttendanceNotificationData{" +
                "userType=" + userType +
                ", userId=" + userId +
                ", attendanceDate=" + attendanceDate +
                ", attendanceStatus=" + attendanceStatus +
                '}';
    }
}
