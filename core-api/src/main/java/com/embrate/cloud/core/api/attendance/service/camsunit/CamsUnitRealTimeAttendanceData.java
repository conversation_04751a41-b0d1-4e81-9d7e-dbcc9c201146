package com.embrate.cloud.core.api.attendance.service.camsunit;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CamsUnitRealTimeAttendanceData {

    @JsonProperty("OperationID")
    private String operationID;

    @JsonProperty("PunchLog")
    private CamsUnitPunchLogData punchLogData;

    @JsonProperty("UserUpdated")
    private CamsUnitUserUpdatedData userUpdated;

    @JsonProperty("AuthToken")
    private String authToken;

    @JsonProperty("Time")
    private String time;

    public String getOperationID() {
        return operationID;
    }

    public void setOperationID(String operationID) {
        this.operationID = operationID;
    }

    public CamsUnitPunchLogData getPunchLogData() {
        return punchLogData;
    }

    public void setPunchLogData(CamsUnitPunchLogData punchLogData) {
        this.punchLogData = punchLogData;
    }

    public CamsUnitUserUpdatedData getUserUpdated() {
        return userUpdated;
    }

    public void setUserUpdated(CamsUnitUserUpdatedData userUpdated) {
        this.userUpdated = userUpdated;
    }

    public String getAuthToken() {
        return authToken;
    }

    public void setAuthToken(String authToken) {
        this.authToken = authToken;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    @Override
    public String toString() {
        return "CamsUnitRealTimeAttendanceData{" +
                "operationID='" + operationID + '\'' +
                ", punchLogData=" + punchLogData +
                ", userUpdated=" + userUpdated +
                ", authToken='" + authToken + '\'' +
                ", time='" + time + '\'' +
                '}';
    }
}
