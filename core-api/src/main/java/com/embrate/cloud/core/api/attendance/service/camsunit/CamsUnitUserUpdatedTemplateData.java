package com.embrate.cloud.core.api.attendance.service.camsunit;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CamsUnitUserUpdatedTemplateData {

    @JsonProperty("Type")
    private String dataType;

    @JsonProperty("UserID")
    private String userId;

    @JsonProperty("Size")
    private String size;

    @JsonProperty("Index")
    private String index;

    @JsonProperty("Data")
    private String data;

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "CamsUnitUserUpdatedTemplateData{" +
                "dataType='" + dataType + '\'' +
                ", userId='" + userId + '\'' +
                ", size='" + size + '\'' +
                ", index='" + index + '\'' +
                ", data='" + data + '\'' +
                '}';
    }
}
