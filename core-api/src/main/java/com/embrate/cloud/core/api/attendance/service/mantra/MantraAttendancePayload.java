package com.embrate.cloud.core.api.attendance.service.mantra;

import com.embrate.cloud.core.api.attendance.IDeviceAttendancePayload;
import com.embrate.cloud.core.api.attendance.service.camsunit.CamsUnitRealTimeAttendanceData;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class MantraAttendancePayload implements IDeviceAttendancePayload {

    private String deviceId;
    @JsonProperty("trans")
    private List<MantraAttendanceTransaction> transactionList;

    public MantraAttendancePayload() {
    }

    public MantraAttendancePayload(String deviceId, List<MantraAttendanceTransaction> transactionList) {
        this.deviceId = deviceId;
        this.transactionList = transactionList;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public List<MantraAttendanceTransaction> getTransactionList() {
        return transactionList;
    }

    public void setTransactionList(List<MantraAttendanceTransaction> transactionList) {
        this.transactionList = transactionList;
    }

    @Override
    public String toString() {
        return "MantraAttendancePayload{" +
                "transactionList=" + transactionList +
                '}';
    }
}
