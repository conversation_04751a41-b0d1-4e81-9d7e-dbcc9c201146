package com.embrate.cloud.core.api.auth;

import java.util.Set;

/**
 * <AUTHOR>
 */

public class OAuthTokenData {

    private final String accessToken;

    private final String tokenType;

    private final int expiryTime;

    private final Set<String> scope;

    public OAuthTokenData() {
        this.accessToken = null;
        this.tokenType = null;
        this.expiryTime = 0;
        this.scope = null;
    }

    public OAuthTokenData(String accessToken, String tokenType, int expiryTime, Set<String> scope) {
        this.accessToken = accessToken;
        this.tokenType = tokenType;
        this.expiryTime = expiryTime;
        this.scope = scope;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public String getTokenType() {
        return tokenType;
    }

    public int getExpiryTime() {
        return expiryTime;
    }

    public boolean isExpired(){
        return System.currentTimeMillis() >=  expiryTime * 1000l;
    }

    public Set<String> getScope() {
        return scope;
    }

    @Override
    public String toString() {
        return "OAuthTokenData{" +
                "accessToken='" + accessToken + '\'' +
                ", tokenType='" + tokenType + '\'' +
                ", expiryTime=" + expiryTime +
                ", scope='" + scope + '\'' +
                '}';
    }
}
