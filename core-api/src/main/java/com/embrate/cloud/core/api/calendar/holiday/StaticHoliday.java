package com.embrate.cloud.core.api.calendar.holiday;

import com.lernen.cloud.core.api.configurations.Entity;

/**
 * <AUTHOR>
 */

public class StaticHoliday extends Holiday {

    public StaticHoliday(int holidayId, HolidayType holidayType, int start, int end, String name, String description, long addedAt) {
        super(holidayId, holidayType, start, end, name, description, addedAt);
    }

    @Override
    public String toString() {
        return "FixedHoliday{} " + super.toString();
    }
}
