package com.embrate.cloud.core.api.calendar.holiday.template.assignment;

import com.embrate.cloud.core.api.calendar.holiday.template.HolidayTemplate;

/**
 * <AUTHOR>
 */

public class HolidayTemplateDuration {

    private final HolidayTemplate holidayTemplate;

    private final int start;

    private final int end;

    public HolidayTemplateDuration(HolidayTemplate holidayTemplate, int start, int end) {
        this.holidayTemplate = holidayTemplate;
        this.start = start;
        this.end = end;
    }

    public HolidayTemplate getHolidayTemplate() {
        return holidayTemplate;
    }

    public int getStart() {
        return start;
    }

    public int getEnd() {
        return end;
    }

    @Override
    public String toString() {
        return "HolidayTemplateDuration{" +
                "holidayTemplate=" + holidayTemplate +
                ", start=" + start +
                ", end=" + end +
                '}';
    }
}
