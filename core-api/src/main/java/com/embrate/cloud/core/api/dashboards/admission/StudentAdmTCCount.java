package com.embrate.cloud.core.api.dashboards.admission;

/**
 * <AUTHOR>
 */
public class StudentAdmTCCount {

    private final int instituteId;
    private final int admissionCount;
    private final int relieveCount;

    public StudentAdmTCCount(int instituteId, int admissionCount, int relieveCount) {
        this.instituteId = instituteId;
        this.admissionCount = admissionCount;
        this.relieveCount = relieveCount;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public int getAdmissionCount() {
        return admissionCount;
    }

    public int getRelieveCount() {
        return relieveCount;
    }

    @Override
    public String toString() {
        return "StudentAdmTCCount{" +
                "instituteId=" + instituteId +
                ", admissionCount=" + admissionCount +
                ", relieveCount=" + relieveCount +
                '}';
    }
}
