package com.embrate.cloud.core.api.dashboards.attendance;

import com.lernen.cloud.core.api.attendance.AttendanceStatus;

/**
 * <AUTHOR>
 */
public class AttendanceCount {

	private final AttendanceStatus attendanceStatus;
	private final int count;
	private final int total;

	public AttendanceCount(AttendanceStatus attendanceStatus, int count, int total) {
		this.attendanceStatus = attendanceStatus;
		this.count = count;
		this.total = total;
	}

	public AttendanceStatus getAttendanceStatus() {
		return attendanceStatus;
	}

	public int getCount() {
		return count;
	}

	public int getTotal() {
		return total;
	}

	@Override
	public String toString() {
		return "AttendanceCount{" +
				"attendanceStatus=" + attendanceStatus +
				", count=" + count +
				", total=" + total +
				'}';
	}
}
