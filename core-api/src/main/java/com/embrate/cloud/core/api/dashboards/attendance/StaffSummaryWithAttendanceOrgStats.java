package com.embrate.cloud.core.api.dashboards.attendance;

import com.embrate.cloud.core.api.dashboards.admission.InstituteStaffCountByGender;
import com.embrate.cloud.core.api.dashboards.admission.StaffCountByGender;
import com.embrate.cloud.core.api.dashboards.common.InstituteValue;

import java.util.List;

/**
 * <AUTHOR>
 */
public class StaffSummaryWithAttendanceOrgStats {

	private final List<InstituteValue> staffCount;
	private final List<InstituteStaffCountByGender> staffCountByGender;
	private final int totalStaff;
	private final List<AttendanceTypeOrgCounts> staffAttendanceCounts;

	public StaffSummaryWithAttendanceOrgStats(List<InstituteValue> staffCount, List<InstituteStaffCountByGender> staffCountByGender, int totalStaff, List<AttendanceTypeOrgCounts> staffAttendanceCounts) {
		this.staffCount = staffCount;
		this.staffCountByGender = staffCountByGender;
		this.totalStaff = totalStaff;
		this.staffAttendanceCounts = staffAttendanceCounts;
	}

	public List<InstituteValue> getStaffCount() {
		return staffCount;
	}

	public List<InstituteStaffCountByGender> getStaffCountByGender() {
		return staffCountByGender;
	}

	public int getTotalStaff() {
		return totalStaff;
	}

	public List<AttendanceTypeOrgCounts> getStaffAttendanceCounts() {
		return staffAttendanceCounts;
	}

	@Override
	public String toString() {
		return "StaffSummaryWithAttendanceOrgStats{" +
				"staffCount=" + staffCount +
				", staffCountByGender=" + staffCountByGender +
				", totalStaff=" + totalStaff +
				", staffAttendanceCounts=" + staffAttendanceCounts +
				'}';
	}
}
