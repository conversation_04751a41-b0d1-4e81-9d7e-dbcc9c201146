package com.embrate.cloud.core.api.dashboards.attendance;

import com.embrate.cloud.core.api.dashboards.admission.StaffCountByGender;
import com.embrate.cloud.core.api.dashboards.common.InstituteValue;

import java.util.List;

/**
 * <AUTHOR>
 */
public class StudentSummaryWithAttendanceOrgStats {

	private final List<InstituteValue> studentCount;
	private final int totalStudent;
	private final List<AttendanceTypeOrgCounts> studentAttendanceCounts;

	public StudentSummaryWithAttendanceOrgStats(List<InstituteValue> studentCount, int totalStudent, List<AttendanceTypeOrgCounts> studentAttendanceCounts) {
		this.studentCount = studentCount;
		this.totalStudent = totalStudent;
		this.studentAttendanceCounts = studentAttendanceCounts;
	}

	public List<InstituteValue> getStudentCount() {
		return studentCount;
	}

	public int getTotalStudent() {
		return totalStudent;
	}

	public List<AttendanceTypeOrgCounts> getStudentAttendanceCounts() {
		return studentAttendanceCounts;
	}

	@Override
	public String toString() {
		return "StudentSummaryWithAttendanceOrgStats{" +
				"studentCount=" + studentCount +
				", totalStudent=" + totalStudent +
				", studentAttendanceCounts=" + studentAttendanceCounts +
				'}';
	}
}
