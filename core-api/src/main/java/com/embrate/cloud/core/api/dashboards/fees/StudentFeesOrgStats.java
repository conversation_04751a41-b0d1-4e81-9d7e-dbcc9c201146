package com.embrate.cloud.core.api.dashboards.fees;

import com.embrate.cloud.core.api.dashboards.common.InstituteValue;

import java.util.List;

/**
 * <AUTHOR>
 */
public class StudentFeesOrgStats {

	private final List<PaymentModeCollection> paymentModeCollections;

	private final List<FeeHeadCollection> feeHeadCollections;

	private final double totalAmount;

	private final List<StudentFeesInstituteStats> instituteStats;

	public StudentFeesOrgStats(List<PaymentModeCollection> paymentModeCollections, List<FeeHeadCollection> feeHeadCollections, double totalAmount, List<StudentFeesInstituteStats> instituteStats) {
		this.paymentModeCollections = paymentModeCollections;
		this.feeHeadCollections = feeHeadCollections;
		this.totalAmount = totalAmount;
		this.instituteStats = instituteStats;
	}

	public List<PaymentModeCollection> getPaymentModeCollections() {
		return paymentModeCollections;
	}

	public List<FeeHeadCollection> getFeeHeadCollections() {
		return feeHeadCollections;
	}

	public double getTotalAmount() {
		return totalAmount;
	}

	public List<StudentFeesInstituteStats> getInstituteStats() {
		return instituteStats;
	}

	@Override
	public String toString() {
		return "StudentFeesOrgStats{" +
				"paymentModeCollections=" + paymentModeCollections +
				", feeHeadCollections=" + feeHeadCollections +
				", totalAmount=" + totalAmount +
				", instituteStats=" + instituteStats +
				'}';
	}
}
