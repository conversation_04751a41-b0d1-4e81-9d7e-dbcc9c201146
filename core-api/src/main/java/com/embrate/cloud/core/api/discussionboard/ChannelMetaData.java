/**
 * 
 */
package com.embrate.cloud.core.api.discussionboard;

import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class ChannelMetaData {

	private UUID channelId;

	private DiscussionBoardEntity discussionBoardEntity;

	private String entityId;

	private String channelName;

	private String description;

	/**
	 * @param channelId
	 * @param discussionBoardEntity
	 * @param entityId
	 * @param channelName
	 * @param description
	 */
	public ChannelMetaData(UUID channelId,
			DiscussionBoardEntity discussionBoardEntity, String entityId,
			String channelName, String description) {
		this.channelId = channelId;
		this.discussionBoardEntity = discussionBoardEntity;
		this.entityId = entityId;
		this.channelName = channelName;
		this.description = description;
	}

	/**
	 * 
	 */
	public ChannelMetaData() {
	}

	/**
	 * @return the channelId
	 */
	public UUID getChannelId() {
		return channelId;
	}

	/**
	 * @param channelId
	 *            the channelId to set
	 */
	public void setChannelId(UUID channelId) {
		this.channelId = channelId;
	}

	/**
	 * @return the discussionBoardEntity
	 */
	public DiscussionBoardEntity getDiscussionBoardEntity() {
		return discussionBoardEntity;
	}

	/**
	 * @param discussionBoardEntity
	 *            the discussionBoardEntity to set
	 */
	public void setDiscussionBoardEntity(
			DiscussionBoardEntity discussionBoardEntity) {
		this.discussionBoardEntity = discussionBoardEntity;
	}

	/**
	 * @return the entityId
	 */
	public String getEntityId() {
		return entityId;
	}

	/**
	 * @param entityId
	 *            the entityId to set
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	/**
	 * @return the channelName
	 */
	public String getChannelName() {
		return channelName;
	}

	/**
	 * @param channelName
	 *            the channelName to set
	 */
	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}

	/**
	 * @return the description
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * @param description
	 *            the description to set
	 */
	public void setDescription(String description) {
		this.description = description;
	}

	@Override
	public String toString() {
		return "ChannelMetaData [channelId=" + channelId
				+ ", discussionBoardEntity=" + discussionBoardEntity
				+ ", entityId=" + entityId + ", channelName=" + channelName
				+ ", description=" + description + "]";
	}
}
