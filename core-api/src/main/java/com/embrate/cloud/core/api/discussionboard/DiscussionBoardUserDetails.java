/**
 * 
 */
package com.embrate.cloud.core.api.discussionboard;

import java.util.UUID;

import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.UserType;

/**
 * <AUTHOR>
 *
 */
public class DiscussionBoardUserDetails {
	
	private UUID userId;
	
	private String userInstituteId;
	
	private UserType userType;
	
	private String fullName;
	
	private Gender gender;

	/**
	 * @param userId
	 * @param userInstituteId
	 * @param userType
	 * @param fullName
	 * @param gender
	 */
	public DiscussionBoardUserDetails(UUID userId, String userInstituteId, UserType userType, String fullName,
			Gender gender) {
		this.userId = userId;
		this.userInstituteId = userInstituteId;
		this.userType = userType;
		this.fullName = fullName;
		this.gender = gender;
	}

	/**
	 * 
	 */
	public DiscussionBoardUserDetails() {
	}

	/**
	 * @return the userId
	 */
	public UUID getUserId() {
		return userId;
	}

	/**
	 * @param userId the userId to set
	 */
	public void setUserId(UUID userId) {
		this.userId = userId;
	}

	/**
	 * @return the userInstituteId
	 */
	public String getUserInstituteId() {
		return userInstituteId;
	}

	/**
	 * @param userInstituteId the userInstituteId to set
	 */
	public void setUserInstituteId(String userInstituteId) {
		this.userInstituteId = userInstituteId;
	}

	/**
	 * @return the userType
	 */
	public UserType getUserType() {
		return userType;
	}

	/**
	 * @param userType the userType to set
	 */
	public void setUserType(UserType userType) {
		this.userType = userType;
	}

	/**
	 * @return the firstName
	 */
	public String getFullName() {
		return fullName;
	}

	/**
	 * @param firstName the firstName to set
	 */
	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	/**
	 * @return the gender
	 */
	public Gender getGender() {
		return gender;
	}

	/**
	 * @param gender the gender to set
	 */
	public void setGender(Gender gender) {
		this.gender = gender;
	}

	@Override
	public String toString() {
		return "DiscussionBoardUserDetails [userId=" + userId + ", userInstituteId=" + userInstituteId + ", userType="
				+ userType + ", fullName=" + fullName + ", gender=" + gender + "]";
	}
}
