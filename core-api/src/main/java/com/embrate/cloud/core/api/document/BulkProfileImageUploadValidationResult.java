package com.embrate.cloud.core.api.document;

import java.util.List;

/**
 * <AUTHOR>
 */

public class BulkProfileImageUploadValidationResult {

    private final int instituteId;

    private final List<String> admissionNumberNotExistList;

    private final List<String> imageNameNotExistList;

    private final List<ImageNotExistEntry> imageDoesNotExistEntries;

    private final  List<FileEntryError> failedLines ;

    public BulkProfileImageUploadValidationResult(int instituteId, List<String> admissionNumberNotExistList, List<String> imageNameNotExistList, List<ImageNotExistEntry> imageDoesNotExistEntries, List<FileEntryError> failedLines) {
        this.instituteId = instituteId;
        this.admissionNumberNotExistList = admissionNumberNotExistList;
        this.imageNameNotExistList = imageNameNotExistList;
        this.imageDoesNotExistEntries = imageDoesNotExistEntries;
        this.failedLines = failedLines;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public List<String> getAdmissionNumberNotExistList() {
        return admissionNumberNotExistList;
    }

    public List<String> getImageNameNotExistList() {
        return imageNameNotExistList;
    }

    public List<ImageNotExistEntry> getImageDoesNotExistEntries() {
        return imageDoesNotExistEntries;
    }

    public List<FileEntryError> getFailedLines() {
        return failedLines;
    }

    @Override
    public String toString() {
        return "BulkProfileImageUploadValidationResult{" +
                "instituteId=" + instituteId +
                ", admissionNumberNotExistList=" + admissionNumberNotExistList +
                ", imageNameNotExistList=" + imageNameNotExistList +
                ", imageDoesNotExistEntries=" + imageDoesNotExistEntries +
                ", failedLines=" + failedLines +
                '}';
    }
}
