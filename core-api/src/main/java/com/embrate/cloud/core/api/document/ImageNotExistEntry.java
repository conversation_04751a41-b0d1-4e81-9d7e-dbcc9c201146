package com.embrate.cloud.core.api.document;

/**
 * <AUTHOR>
 */

public class ImageNotExistEntry {

    private final String admissionNumber;

    private final String imageName;

    public ImageNotExistEntry(String admissionNumber, String imageName) {
        this.admissionNumber = admissionNumber;
        this.imageName = imageName;
    }

    public String getAdmissionNumber() {
        return admissionNumber;
    }

    public String getImageName() {
        return imageName;
    }

    @Override
    public String toString() {
        return "ImageNotExistEntry{" +
                "admissionNumber='" + admissionNumber + '\'' +
                ", imageName='" + imageName + '\'' +
                '}';
    }
}
