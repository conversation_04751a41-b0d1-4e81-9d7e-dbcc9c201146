package com.embrate.cloud.core.api.examination.management;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.embrate.cloud.core.api.examination.utility.StandardExaminationGrades;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.user.DataUpdationAction;

public class GradeUpdatePayload {

    private List<UUID> standardIdList;
    private boolean marksRangeStartInclusive;
	private boolean marksRangeEndInclusive;
    private Map<CourseType, List<StandardExaminationGrades>> courseTypeExaminationGradesMap;
    private DataUpdationAction dataUpdationAction;

    public GradeUpdatePayload(){

    }
    
    public GradeUpdatePayload(List<UUID> standardIdList, boolean marksRangeStartInclusive,
            boolean marksRangeEndInclusive,
            Map<CourseType, List<StandardExaminationGrades>> courseTypeExaminationGradesMap,
            DataUpdationAction dataUpdationAction) {
        this.standardIdList = standardIdList;
        this.marksRangeStartInclusive = marksRangeStartInclusive;
        this.marksRangeEndInclusive = marksRangeEndInclusive;
        this.courseTypeExaminationGradesMap = courseTypeExaminationGradesMap;
        this.dataUpdationAction = dataUpdationAction;
    }

    public List<UUID> getStandardIdList() {
        return standardIdList;
    }
    public void setStandardIdList(List<UUID> standardIdList) {
        this.standardIdList = standardIdList;
    }
    public boolean isMarksRangeStartInclusive() {
        return marksRangeStartInclusive;
    }
    public DataUpdationAction getDataUpdationAction() {
        return dataUpdationAction;
    }

    public void setDataUpdationAction(DataUpdationAction dataUpdationAction) {
        this.dataUpdationAction = dataUpdationAction;
    }

    public void setMarksRangeStartInclusive(boolean marksRangeStartInclusive) {
        this.marksRangeStartInclusive = marksRangeStartInclusive;
    }
    public boolean isMarksRangeEndInclusive() {
        return marksRangeEndInclusive;
    }
    public void setMarksRangeEndInclusive(boolean marksRangeEndInclusive) {
        this.marksRangeEndInclusive = marksRangeEndInclusive;
    }
    public Map<CourseType, List<StandardExaminationGrades>> getCourseTypeExaminationGradesMap() {
        return courseTypeExaminationGradesMap;
    }
    public void setCourseTypeExaminationGradesMap(
            Map<CourseType, List<StandardExaminationGrades>> courseTypeExaminationGradesMap) {
        this.courseTypeExaminationGradesMap = courseTypeExaminationGradesMap;
    }

    @Override
    public String toString() {
        return "GradeUpdatePayload [standardIdList=" + standardIdList
                + ", marksRangeStartInclusive=" + marksRangeStartInclusive + ", marksRangeEndInclusive="
                + marksRangeEndInclusive + ", courseTypeExaminationGradesMap=" + courseTypeExaminationGradesMap
                + ", dataUpdationAction=" + dataUpdationAction + "]";
    }

    
}
