package com.embrate.cloud.core.api.examination.utility;

import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamCourseStructure {

	private String courseName;

	private Map<String, Double> dimensionMarks;

	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public Map<String, Double> getDimensionMarks() {
		return dimensionMarks;
	}

	public void setDimensionMarks(Map<String, Double> dimensionMarks) {
		this.dimensionMarks = dimensionMarks;
	}

}
