package com.embrate.cloud.core.api.feature.preference;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */

public class FeaturePreferenceEntity {

    private final String id;

    private final String name;

    private final String description;

    private final PreferenceDataType dataType;

    private final String configType;

    private final String configKey;

    private final IFeaturePreferenceExecutor featurePreferenceExecutor;

    private final String value;

    private final List<String> possibleValues;

    public FeaturePreferenceEntity(String id, String name, String description, String value, PreferenceDataType dataType, List<String> possibleValues) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.dataType = dataType;
        this.configType = null;
        this.configKey = null;
        this.featurePreferenceExecutor = null;
        this.value = value;
        this.possibleValues = possibleValues;
    }

    public FeaturePreferenceEntity(String id, String name, String description, PreferenceDataType dataType, String configType, String configKey) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.dataType = dataType;
        this.configType = configType;
        this.configKey = configKey;
        this.featurePreferenceExecutor = null;
        this.value = null;
        this.possibleValues = null;
    }

    public FeaturePreferenceEntity(String id, String name, String description, PreferenceDataType dataType, String configType, String configKey, List<String> possibleValues) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.dataType = dataType;
        this.configType = configType;
        this.configKey = configKey;
        this.featurePreferenceExecutor = null;
        this.value = null;
        this.possibleValues = possibleValues;
    }

    public FeaturePreferenceEntity(IFeaturePreferenceExecutor featurePreferenceExecutor, String id, String name, String description, PreferenceDataType dataType) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.dataType = dataType;
        this.configType = null;
        this.configKey = null;
        this.featurePreferenceExecutor = featurePreferenceExecutor;
        this.value = null;
        this.possibleValues = null;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public PreferenceDataType getDataType() {
        return dataType;
    }

    public String getConfigType() {
        return configType;
    }

    public String getConfigKey() {
        return configKey;
    }

    public IFeaturePreferenceExecutor getFeaturePreferenceExecutor() {
        return featurePreferenceExecutor;
    }

    public String getValue() {
        return value;
    }

    public List<String> getPossibleValues() {
        return possibleValues;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof FeaturePreferenceEntity)) return false;

        FeaturePreferenceEntity that = (FeaturePreferenceEntity) o;

        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "FeaturePreferenceEntity{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", dataType=" + dataType +
                ", configType='" + configType + '\'' +
                ", configKey='" + configKey + '\'' +
                ", featurePreferenceExecutor=" + featurePreferenceExecutor +
                ", value='" + value + '\'' +
                ", possibleValues=" + possibleValues +
                '}';
    }
}
