package com.embrate.cloud.core.api.fee;

import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 * @created_at 12/04/24 : 15:42
 **/
public class BulkStudentFeeStructureAssignmentReadablePayload {
    private Set<String> admissionNumberSet;
    private Set<String> feesStructureNameSet;

    public Set<String> getAdmissionNumberSet() {
        return admissionNumberSet;
    }

    public void setAdmissionNumberSet(Set<String> admissionNumberSet) {
        this.admissionNumberSet = admissionNumberSet;
    }

    public Set<String> getFeesStructureNameSet() {
        return feesStructureNameSet;
    }

    public void setFeesStructureNameSet(Set<String> feesStructureNameSet) {
        this.feesStructureNameSet = feesStructureNameSet;
    }

    @Override
    public String toString() {
        return "BulkStudentFeeStructureAssignmentReadablePayload{" +
                "admissionNumberSet=" + admissionNumberSet +
                ", feesStructureNameSet=" + feesStructureNameSet +
                '}';
    }
}
