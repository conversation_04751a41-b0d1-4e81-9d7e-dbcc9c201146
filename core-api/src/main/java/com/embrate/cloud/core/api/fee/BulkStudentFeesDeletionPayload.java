package com.embrate.cloud.core.api.fee;

import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 * @created_at 12/04/24 : 15:42
 **/
public class BulkStudentFeesDeletionPayload {

    private Set<UUID> studentIds;
    private Set<UUID> feesIds;

    public BulkStudentFeesDeletionPayload() {
    }

    public BulkStudentFeesDeletionPayload(Set<UUID> studentIds, Set<UUID> feesIds) {
        this.studentIds = studentIds;
        this.feesIds = feesIds;
    }

    public Set<UUID> getStudentIds() {
        return studentIds;
    }

    public void setStudentIds(Set<UUID> studentIds) {
        this.studentIds = studentIds;
    }

    public Set<UUID> getFeesIds() {
        return feesIds;
    }

    public void setFeesIds(Set<UUID> feesIds) {
        this.feesIds = feesIds;
    }

    @Override
    public String toString() {
        return "BulkStudentFeesDeletionPayload{" +
                "studentIds=" + studentIds +
                ", feesIds=" + feesIds +
                '}';
    }
}
