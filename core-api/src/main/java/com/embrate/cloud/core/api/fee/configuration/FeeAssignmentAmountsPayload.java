package com.embrate.cloud.core.api.fee.configuration;

import com.lernen.cloud.core.api.fees.FeeIdFeeHead;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class FeeAssignmentAmountsPayload {

	private UUID studentId;

	private List<FeeIdFeeHead> feeIdFeeHeadList;

	public UUID getStudentId() {
		return studentId;
	}

	public void setStudentId(UUID studentId) {
		this.studentId = studentId;
	}

	public List<FeeIdFeeHead> getFeeIdFeeHeadList() {
		return feeIdFeeHeadList;
	}

	public void setFeeIdFeeHeadList(List<FeeIdFeeHead> feeIdFeeHeadList) {
		this.feeIdFeeHeadList = feeIdFeeHeadList;
	}

	@Override
	public String toString() {
		return "FeeAssignmentAmountsPayload{" +
				"studentId=" + studentId +
				", feeIdFeeHeadList=" + feeIdFeeHeadList +
				'}';
	}
}
