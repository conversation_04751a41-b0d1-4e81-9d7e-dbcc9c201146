package com.embrate.cloud.core.api.fee.discount.structure;

import java.util.List;

import com.lernen.cloud.core.api.fees.FeeEntity;

/**
 *
 * <AUTHOR>
 *
 */
public class FeeDiscountEntityStructure {

	private final String entityId;

	private final FeeEntity feeEntity;

	private final List<FeeIdDiscountStructure> feeIdDiscountStructures;

	public FeeDiscountEntityStructure(String entityId, FeeEntity feeEntity,
			List<FeeIdDiscountStructure> feeIdDiscountStructures) {
		this.entityId = entityId;
		this.feeEntity = feeEntity;
		this.feeIdDiscountStructures = feeIdDiscountStructures;
	}

	public String getEntityId() {
		return entityId;
	}

	public FeeEntity getFeeEntity() {
		return feeEntity;
	}

	public List<FeeIdDiscountStructure> getFeeIdDiscountStructures() {
		return feeIdDiscountStructures;
	}

	@Override
	public String toString() {
		return "FeeDiscountEntityStructure [entityId=" + entityId
				+ ", feeEntity=" + feeEntity + ", feeIdDiscountStructures="
				+ feeIdDiscountStructures + "]";
	}

}
