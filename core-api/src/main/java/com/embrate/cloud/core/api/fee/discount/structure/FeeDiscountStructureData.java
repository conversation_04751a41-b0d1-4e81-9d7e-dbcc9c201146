package com.embrate.cloud.core.api.fee.discount.structure;

import com.lernen.cloud.core.api.fees.FeeIdFeeHead;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class FeeDiscountStructureData {

    private final UUID structureId;

    // Here assuming the fee entity to be institute level only.
    private final List<FeeIdFeeHead> feeIdFeeHeadList;

    public FeeDiscountStructureData(UUID structureId, List<FeeIdFeeHead> feeIdFeeHeadList) {
        this.structureId = structureId;
        this.feeIdFeeHeadList = feeIdFeeHeadList;
    }

    public UUID getStructureId() {
        return structureId;
    }

    public List<FeeIdFeeHead> getFeeIdFeeHeadList() {
        return feeIdFeeHeadList;
    }

    @Override
    public String toString() {
        return "FeeDiscountStructureData{" +
                "structureId=" + structureId +
                ", feeIdFeeHeadList=" + feeIdFeeHeadList +
                '}';
    }
}
