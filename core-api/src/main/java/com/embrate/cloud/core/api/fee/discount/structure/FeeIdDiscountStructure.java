package com.embrate.cloud.core.api.fee.discount.structure;

import java.util.List;

import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.fees.FeeConfigurationBasicInfo;

/**
 *
 * <AUTHOR>
 *
 */
public class FeeIdDiscountStructure {

	private final FeeConfigurationBasicInfo feeConfigurationBasicInfo;

	private final List<FeeHeadDiscountStructure> feeHeadDiscountStructures;

	private final double totalAmount;

	public FeeIdDiscountStructure(
			FeeConfigurationBasicInfo feeConfigurationBasicInfo,
			List<FeeHeadDiscountStructure> feeHeadDiscountStructures) {
		this.feeConfigurationBasicInfo = feeConfigurationBasicInfo;
		this.feeHeadDiscountStructures = feeHeadDiscountStructures;
		this.totalAmount = computeTotalAmount();
	}

	public FeeConfigurationBasicInfo getFeeConfigurationBasicInfo() {
		return feeConfigurationBasicInfo;
	}

	public List<FeeHeadDiscountStructure> getFeeHeadDiscountStructures() {
		return feeHeadDiscountStructures;
	}

	public double getTotalAmount() {
		return totalAmount;
	}

	private double computeTotalAmount() {
		double sum = 0d;
		if (CollectionUtils.isEmpty(feeHeadDiscountStructures)) {
			return sum;
		}
		for (final FeeHeadDiscountStructure feeHeadDiscountStructure : feeHeadDiscountStructures) {
			sum += feeHeadDiscountStructure.getAmount();
		}
		return sum;
	}

	@Override
	public String toString() {
		return "FeeIdDiscountStructure [feeConfigurationBasicInfo="
				+ feeConfigurationBasicInfo + ", feeHeadDiscountStructures="
				+ feeHeadDiscountStructures + ", totalAmount=" + totalAmount
				+ "]";
	}

}
