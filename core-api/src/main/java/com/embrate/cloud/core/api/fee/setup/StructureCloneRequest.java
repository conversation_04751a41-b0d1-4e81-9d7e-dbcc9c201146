package com.embrate.cloud.core.api.fee.setup;

import java.util.Set;

/**
 * <AUTHOR>
 */
public class StructureCloneRequest {

	private int instituteId;
	private int srcAcademicSessionId;
	private int destAcademicSessionId;
	private StructureCloneType cloneType;
	/**
	 * Only applicable if clone type is CUSTOM
	 */
	private Set<String> structureNames;

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public int getSrcAcademicSessionId() {
		return srcAcademicSessionId;
	}

	public void setSrcAcademicSessionId(int srcAcademicSessionId) {
		this.srcAcademicSessionId = srcAcademicSessionId;
	}

	public int getDestAcademicSessionId() {
		return destAcademicSessionId;
	}

	public void setDestAcademicSessionId(int destAcademicSessionId) {
		this.destAcademicSessionId = destAcademicSessionId;
	}

	public StructureCloneType getCloneType() {
		return cloneType;
	}

	public void setCloneType(StructureCloneType cloneType) {
		this.cloneType = cloneType;
	}

	public Set<String> getStructureNames() {
		return structureNames;
	}

	public void setStructureNames(Set<String> structureNames) {
		this.structureNames = structureNames;
	}

	@Override
	public String toString() {
		return "FeeStructureCloneRequest{" +
				"instituteId=" + instituteId +
				", srcSessionId=" + srcAcademicSessionId +
				", destSessionId=" + destAcademicSessionId +
				", cloneType=" + cloneType +
				", structureNames=" + structureNames +
				'}';
	}
}
