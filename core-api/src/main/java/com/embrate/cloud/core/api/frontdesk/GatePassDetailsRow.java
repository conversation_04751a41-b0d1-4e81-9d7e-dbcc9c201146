package com.embrate.cloud.core.api.frontdesk;

import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.transport.Vehicle;

import java.util.UUID;

public class GatePassDetailsRow {

    private final int instituteId;

    private final int academicSessionId;

    private final UUID gatePassId;

    private final String gatePassNumber;

    private final Integer gatePassDate;

    private final String name;

    private final String contactNumber;

    private final String emailId;

    private final String address;

    private final String reason;

    private final String relation;

    private final GatePassStatus gatePassStatus;

    private final StudentLite studentLite;

    private final Vehicle vehicle;

    private Integer departureDate;

    private Integer expectedReturnDate;

    private Boolean hosteller;

    public GatePassDetailsRow(int instituteId, int academicSessionId, UUID gatePassId, String gatePassNumber, Integer gatePassDate, String name, String contactNumber, String emailId, String address, String reason, Integer departureDate, Integer expectedReturnDate, Boolean hosteller, String relation, GatePassStatus gatePassStatus, StudentLite studentLite, Vehicle vehicle) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.gatePassId = gatePassId;
        this.gatePassNumber = gatePassNumber;
        this.gatePassDate = gatePassDate;
        this.name = name;
        this.contactNumber = contactNumber;
        this.emailId = emailId;
        this.address = address;
        this.reason = reason;
        this.departureDate = departureDate;
        this.expectedReturnDate = expectedReturnDate;
        this.hosteller = hosteller;
        this.relation = relation;
        this.gatePassStatus = gatePassStatus;
        this.studentLite = studentLite;
        this.vehicle = vehicle;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public UUID getGatePassId() {
        return gatePassId;
    }

    public String getGatePassNumber() {
        return gatePassNumber;
    }

    public Integer getGatePassDate() {
        return gatePassDate;
    }

    public String getName() {
        return name;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public String getEmailId() {
        return emailId;
    }

    public String getAddress() {
        return address;
    }

    public String getReason() {
        return reason;
    }

    public String getRelation() {
        return relation;
    }

    public GatePassStatus getGatePassStatus() {
        return gatePassStatus;
    }

    public StudentLite getStudentLite() {
        return studentLite;
    }

    public Vehicle getVehicle() {
        return vehicle;
    }

    public Integer getDepartureDate() {return departureDate;}

    public Integer getExpectedReturnDate() {return expectedReturnDate;}

    public Boolean getHosteller() {return hosteller;}

    @Override
    public String toString() {
        return "GatePassDetailsRow{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", gatePassId=" + gatePassId +
                ", gatePassNumber='" + gatePassNumber + '\'' +
                ", gatePassDate=" + gatePassDate +
                ", name='" + name + '\'' +
                ", contactNumber='" + contactNumber + '\'' +
                ", emailId='" + emailId + '\'' +
                ", address='" + address + '\'' +
                ", reason='" + reason + '\'' +
                ", relation='" + relation + '\'' +
                ", gatePassStatus=" + gatePassStatus +
                ", studentLite=" + studentLite +
                ", vehicle=" + vehicle +
                ", departureDate=" + departureDate +
                ", expectedReturnDate=" + expectedReturnDate +
                ", hosteller=" + hosteller +
                '}';
    }
}
