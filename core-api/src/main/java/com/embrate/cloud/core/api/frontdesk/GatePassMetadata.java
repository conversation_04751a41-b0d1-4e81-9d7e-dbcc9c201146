package com.embrate.cloud.core.api.frontdesk;

import java.util.UUID;

public class GatePassMetadata {

    private int instituteId;

    private int academicSessionId;

    private UUID gatePassId;

    private String gatePassNumber;

    private Integer gatePassDate;

    private String name;

    private String contactNumber;

    private String emailId;

    private String address;

    private String reason;

    private String relation;

    private GatePassStatus gatePassStatus;

    public GatePassMetadata(int instituteId, int academicSessionId, UUID gatePassId, String gatePassNumber, Integer gatePassDate, String name, String contactNumber, String emailId, String address, String reason, String relation, GatePassStatus gatePassStatus) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.gatePassId = gatePassId;
        this.gatePassNumber = gatePassNumber;
        this.gatePassDate = gatePassDate;
        this.name = name;
        this.contactNumber = contactNumber;
        this.emailId = emailId;
        this.address = address;
        this.reason = reason;
        this.relation = relation;
        this.gatePassStatus = gatePassStatus;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public void setAcademicSessionId(int academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public UUID getGatePassId() {
        return gatePassId;
    }

    public void setGatePassId(UUID gatePassId) {
        this.gatePassId = gatePassId;
    }

    public String getGatePassNumber() {
        return gatePassNumber;
    }

    public void setGatePassNumber(String gatePassNumber) {
        this.gatePassNumber = gatePassNumber;
    }

    public Integer getGatePassDate() {
        return gatePassDate;
    }

    public void setGatePassDate(Integer gatePassDate) {
        this.gatePassDate = gatePassDate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String emailId) {
        this.emailId = emailId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public GatePassStatus getGatePassStatus() {
        return gatePassStatus;
    }

    public void setGatePassStatus(GatePassStatus gatePassStatus) {
        this.gatePassStatus = gatePassStatus;
    }

    @Override
    public String toString() {
        return "GatePassMetadata{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", gatePassId=" + gatePassId +
                ", gatePassNumber='" + gatePassNumber + '\'' +
                ", gatePassDate=" + gatePassDate +
                ", name='" + name + '\'' +
                ", contactNumber='" + contactNumber + '\'' +
                ", emailId='" + emailId + '\'' +
                ", address='" + address + '\'' +
                ", reason='" + reason + '\'' +
                ", relation='" + relation + '\'' +
                ", gatePassStatus=" + gatePassStatus +
                '}';
    }
}
