package com.embrate.cloud.core.api.frontdesk;

import org.apache.commons.lang3.StringUtils;

public enum GatePassStatus {
    ACTIVE, CANCELLED;

    public static GatePassStatus getGatePassStatus(String gatePassStatus) {
        if (StringUtils.isBlank(gatePassStatus)) {
            return null;
        }
        for (GatePassStatus gatePassStatusEnum : GatePassStatus.values()) {
            if (gatePassStatusEnum.name().equalsIgnoreCase(gatePassStatus)) {
                return gatePassStatusEnum;
            }
        }
        return null;
    }

}
