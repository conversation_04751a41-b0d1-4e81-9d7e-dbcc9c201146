/**
 * 
 */
package com.embrate.cloud.core.api.homework;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class HomeworkAndSubmissionDetails {

	private final HomeworkDetails homeworkDetails;
	
	private List<HomeworkSubmissionDetails> homeworkSubmissionDetailsList;

	/**
	 * @param homeworkDetails
	 * @param homeworkSubmissionDetailsList
	 */
	public HomeworkAndSubmissionDetails(HomeworkDetails homeworkDetails,
			List<HomeworkSubmissionDetails> homeworkSubmissionDetailsList) {
		this.homeworkDetails = homeworkDetails;
		this.homeworkSubmissionDetailsList = homeworkSubmissionDetailsList;
	}

	/**
	 * @return the homeworkDetails
	 */
	public HomeworkDetails getHomeworkDetails() {
		return homeworkDetails;
	}

	/**
	 * @return the homeworkSubmissionDetailsList
	 */
	public List<HomeworkSubmissionDetails> getHomeworkSubmissionDetailsList() {
		return homeworkSubmissionDetailsList;
	}

	public void setHomeworkSubmissionDetailsList(List<HomeworkSubmissionDetails> homeworkSubmissionDetailsList) {
		this.homeworkSubmissionDetailsList = homeworkSubmissionDetailsList;
	}

	@Override
	public String toString() {
		return "HomeworkAndSubmissionDetails [homeworkDetails=" + homeworkDetails + ", homeworkSubmissionDetailsList="
				+ homeworkSubmissionDetailsList + "]";
	}
	
}
