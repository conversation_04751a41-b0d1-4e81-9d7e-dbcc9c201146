/**
 *
 */
package com.embrate.cloud.core.api.homework;

import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.user.Document;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;

import java.util.List;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class HomeworkDetails implements Comparable<HomeworkDetails> {

	public static final TimeZone DEFAULT_TIMEZONE = TimeZone.getTimeZone("Asia/Kolkata");

	private final int instituteId;

	private final int academicSessionId;

	private final UUID homeworkId;

	private final Standard standard;

	private final Set<Integer> sectionIdList;

	private final Course course;

	private final String chapter;

	private final String title;

	private UUID createdUserId;

	private Integer createdTimestamp;

	private UUID updatedUserId;

	private Integer updatedTimestamp;

	private final String description;

	private final Integer dueDate;

	private final HomeworkStatus homeworkStatus;

	private final HomeworkType homeworkType;

	private final boolean allowMobileAppSubmissions;

	private final boolean attachDiscussionForum;

	private final UUID broadcastedUserId;

	private final Integer broadcastedTimestamp;

	private final UUID facultyUserId;

	private final String facultyName;

	private final boolean allowEditAfterSubmit;

	private final boolean isGrade;

	private final double maxMarks;

	private final Integer scheduledTimestamp;

	private int attachmentCount;

	private long attachmentSize;

	private String allowedMimeTypes;

	private final List<Document<HomeworkDocumentType>> homeworkAttachments;

	/**
	 * @param instituteId
	 * @param academicSessionId
	 * @param homeworkId
	 * @param standard
	 * @param course
	 * @param chapter
	 * @param title
	 * @param createdUserId
	 * @param createdTimestamp
	 * @param updatedUserId
	 * @param updatedTimestamp
	 * @param description
	 * @param dueDate
	 * @param homeworkStatus
	 * @param homeworkType
	 * @param broadcastedUserId
	 * @param broadcastedTimestamp
	 * @param facultyUserId
	 * @param facultyName
	 * @param allowEditAfterSubmit
	 * @param isGrade
	 * @param maxMarks
	 * @param scheduledTimestamp
	 * @param attachmentCount
	 * @param attachmentSize
	 * @param allowedMimeTypes
	 * @param homeworkAttachments
	 */
	public HomeworkDetails(int instituteId,int academicSessionId, UUID homeworkId, Standard standard, Set<Integer> sectionIdList, Course course, String chapter,
			String title, UUID createdUserId, Integer createdTimestamp, UUID updatedUserId, Integer updatedTimestamp,
			String description, Integer dueDate, HomeworkStatus homeworkStatus,HomeworkType homeworkType,
			boolean allowMobileAppSubmissions, boolean attachDiscussionForum, UUID broadcastedUserId,
			Integer broadcastedTimestamp, UUID facultyUserId, String facultyName, boolean allowEditAfterSubmit,
			boolean isGrade, double maxMarks, Integer scheduledTimestamp, int attachmentCount, long attachmentSize,
			String allowedMimeTypes, List<Document<HomeworkDocumentType>> homeworkAttachments) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.homeworkId = homeworkId;
		this.standard = standard;
		this.sectionIdList = sectionIdList;
		this.course = course;
		this.chapter = chapter;
		this.title = title;
		this.createdUserId = createdUserId;
		this.createdTimestamp = createdTimestamp;
		this.updatedUserId = updatedUserId;
		this.updatedTimestamp = updatedTimestamp;
		this.description = description;
		this.dueDate = dueDate;
		this.homeworkStatus = homeworkStatus;
		this.homeworkType = homeworkType;
		this.allowMobileAppSubmissions = allowMobileAppSubmissions;
		this.attachDiscussionForum = attachDiscussionForum;
		this.broadcastedUserId = broadcastedUserId;
		this.broadcastedTimestamp = broadcastedTimestamp;
		this.facultyUserId = facultyUserId;
		this.facultyName = facultyName;
		this.allowEditAfterSubmit = allowEditAfterSubmit;
		this.isGrade = isGrade;
		this.maxMarks = maxMarks;
		this.scheduledTimestamp = scheduledTimestamp;
		this.attachmentCount = attachmentCount;
		this.attachmentSize = attachmentSize;
		this.allowedMimeTypes = allowedMimeTypes;
		this.homeworkAttachments = homeworkAttachments;
	}

	/**
	 * @return the createdUserId
	 */
	public UUID getCreatedUserId() {
		return createdUserId;
	}

	/**
	 * @param createdUserId the createdUserId to set
	 */
	public void setCreatedUserId(UUID createdUserId) {
		this.createdUserId = createdUserId;
	}

	/**
	 * @return the createdTimestamp
	 */
	public Integer getCreatedTimestamp() {
		return createdTimestamp;
	}

	/**
	 * @param createdTimestamp the createdTimestamp to set
	 */
	public void setCreatedTimestamp(Integer createdTimestamp) {
		this.createdTimestamp = createdTimestamp;
	}

	/**
	 * @return the updatedUserId
	 */
	public UUID getUpdatedUserId() {
		return updatedUserId;
	}

	/**
	 * @param updatedUserId the updatedUserId to set
	 */
	public void setUpdatedUserId(UUID updatedUserId) {
		this.updatedUserId = updatedUserId;
	}

	/**
	 * @return the updatedTimestamp
	 */
	public Integer getUpdatedTimestamp() {
		return updatedTimestamp;
	}

	/**
	 * @param updatedTimestamp the updatedTimestamp to set
	 */
	public void setUpdatedTimestamp(Integer updatedTimestamp) {
		this.updatedTimestamp = updatedTimestamp;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @return the academicSessionId
	 */
	public int getAcademicSessionId() {
		return academicSessionId;
	}

	/**
	 * @return the homeworkId
	 */
	public UUID getHomeworkId() {
		return homeworkId;
	}

	/**
	 * @return the standard
	 */
	public Standard getStandard() {
		return standard;
	}

	/**
	 * @return the course
	 */
	public Course getCourse() {
		return course;
	}

	/**
	 * @return the chapter
	 */
	public String getChapter() {
		return chapter;
	}

	/**
	 * @return the title
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * @return the description
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * @return the dueDate
	 */
	public Integer getDueDate() {
		return dueDate;
	}

	/**
	 * @return the homeworkStatus
	 */
	public HomeworkStatus getHomeworkStatus() {
		return homeworkStatus;
	}

	/**
	 * @return the homeWorkType
	 */
	public HomeworkType getHomeworkType() {
		return homeworkType;
	}

	public boolean isAllowMobileAppSubmissions() {
		return allowMobileAppSubmissions;
	}

	public boolean isAttachDiscussionForum() {
		return attachDiscussionForum;
	}

	/**
	 * @return the broadcastedUserId
	 */
	public UUID getBroadcastedUserId() {
		return broadcastedUserId;
	}

	/**
	 * @return the broadcastedTimestamp
	 */
	public Integer getBroadcastedTimestamp() {
		return broadcastedTimestamp;
	}

	/**
	 * @return the facultyUserId
	 */
	public UUID getFacultyUserId() {
		return facultyUserId;
	}

	/**
	 * @return the facultyName
	 */
	public String getFacultyName() {
		return facultyName;
	}

	/**
	 * @return the allowEditAfterSubmit
	 */
	public boolean isAllowEditAfterSubmit() {
		return allowEditAfterSubmit;
	}

	/**
	 * @return the isGrade
	 */
	public boolean isGrade() {
		return isGrade;
	}

	/**
	 * @return the maxMarks
	 */
	public double getMaxMarks() {
		return maxMarks;
	}

	/**
	 * @return the scheduledTimestamp
	 */
	public Integer getScheduledTimestamp() {
		return scheduledTimestamp;
	}

	/**
	 * @return the homeworkAttachemnts
	 */
	public List<Document<HomeworkDocumentType>> getHomeworkAttachments() {
		return homeworkAttachments;
	}

	/**
	 * @return the attachmentCount
	 */
	public int getAttachmentCount() {
		return attachmentCount;
	}

	/**
	 * @param attachmentCount the attachmentCount to set
	 */
	public void setAttachmentCount(int attachmentCount) {
		this.attachmentCount = attachmentCount;
	}

	/**
	 * @return the allowedMimeTypes
	 */
	public String getAllowedMimeTypes() {
		return allowedMimeTypes;
	}

	/**
	 * @param allowedMimeTypes the allowedMimeTypes to set
	 */
	public void setAllowedMimeTypes(String allowedMimeTypes) {
		this.allowedMimeTypes = allowedMimeTypes;
	}

	/**
	 * @return the attachmentSize
	 */
	public long getAttachmentSize() {
		return attachmentSize;
	}

	/**
	 * @param attachmentSize the attachmentSize to set
	 */
	public void setAttachmentSize(int attachmentSize) {
		this.attachmentSize = attachmentSize;
	}

	public Set<Integer> getSectionIdList() {
		return sectionIdList;
	}

	public void setAttachmentSize(long attachmentSize) {
		this.attachmentSize = attachmentSize;
	}

	@Override
	public String toString() {
		return "HomeworkDetails{" +
				"instituteId=" + instituteId +
				", academicSessionId=" + academicSessionId +
				", homeworkId=" + homeworkId +
				", standard=" + standard +
				", sectionIdList=" + sectionIdList +
				", course=" + course +
				", chapter='" + chapter + '\'' +
				", title='" + title + '\'' +
				", createdUserId=" + createdUserId +
				", createdTimestamp=" + createdTimestamp +
				", updatedUserId=" + updatedUserId +
				", updatedTimestamp=" + updatedTimestamp +
				", description='" + description + '\'' +
				", dueDate=" + dueDate +
				", homeworkStatus=" + homeworkStatus +
				", homeworkType=" + homeworkType +
				", broadcastedUserId=" + broadcastedUserId +
				", broadcastedTimestamp=" + broadcastedTimestamp +
				", facultyUserId=" + facultyUserId +
				", facultyName='" + facultyName + '\'' +
				", allowEditAfterSubmit=" + allowEditAfterSubmit +
				", isGrade=" + isGrade +
				", maxMarks=" + maxMarks +
				", scheduledTimestamp=" + scheduledTimestamp +
				", attachmentCount=" + attachmentCount +
				", attachmentSize=" + attachmentSize +
				", allowedMimeTypes='" + allowedMimeTypes + '\'' +
				", homeworkAttachments=" + homeworkAttachments +
				'}';
	}

	@Override
	public int compareTo(HomeworkDetails other) {
		// First sort by broadcasted timestamp if available, otherwise by created timestamp (ascending order)
		Integer broadcastTs1 = this.getBroadcastedTimestamp();
		Integer broadcastTs2 = other.getBroadcastedTimestamp();
		Integer createdTs1 = this.getCreatedTimestamp();
		Integer createdTs2 = other.getCreatedTimestamp();

		// Use broadcasted timestamp if available, otherwise use created timestamp
		Integer ts1 = (broadcastTs1 != null) ? broadcastTs1 : createdTs1;
		Integer ts2 = (broadcastTs2 != null) ? broadcastTs2 : createdTs2;

		if (ts1 != null && ts2 != null) {
			// Convert timestamps to day start times to compare only the date part, not the time part
			int dayStartTs1 = getDayStart(ts1, DEFAULT_TIMEZONE);
			int dayStartTs2 = getDayStart(ts2, DEFAULT_TIMEZONE);
			int tsCompare = Integer.compare(dayStartTs1, dayStartTs2);
			if (tsCompare != 0) return tsCompare;
		} else if (ts1 == null && ts2 != null) {
			return 1; // Null timestamps come after non-null
		} else if (ts1 != null && ts2 == null) {
			return -1; // Non-null timestamps come before null
		}

		// Then sort by standard level
		int standardCompare = this.getStandard().compareTo(other.getStandard());
		if (standardCompare != 0) return standardCompare;

		// Then sort by section name
		String section1 = StandardSections.getSectionsNameStr(this.getSectionIdList(), this.getStandard().getStandardSectionList());
		String section2 = StandardSections.getSectionsNameStr(other.getSectionIdList(), other.getStandard().getStandardSectionList());
		int sectionCompare = section1.compareTo(section2);
		if (sectionCompare != 0) return sectionCompare;

		// Sort by course sequence
		Course course1 = this.getCourse();
		Course course2 = other.getCourse();

		if (course1 == null && course2 == null) {
			// If both courses are null, sort by homework type
			return HomeworkType.compare(this.getHomeworkType(), other.getHomeworkType());
		}
		if (course1 == null) return 1;
		if (course2 == null) return -1;

		int courseCompare = course1.compareTo(course2);
		if (courseCompare != 0) return courseCompare;

		// Finally sort by homework type
		return HomeworkType.compare(this.getHomeworkType(), other.getHomeworkType());
	}

	/**
	 * Static compare method for backward compatibility
	 * @param s1 First HomeworkDetails to compare
	 * @param s2 Second HomeworkDetails to compare
	 * @return comparison result
	 */
	public static int compare(HomeworkDetails s1, HomeworkDetails s2) {
		return s1.compareTo(s2);
	}

	public static int getDayStart(int time, TimeZone timezone) {
		DateTime givenDate = new DateTime(time * 1000l, DateTimeZone.forTimeZone(timezone));
		DateTime startDate = givenDate.withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0);
		return (int) (startDate.getMillis() / 1000l);
	}
}
