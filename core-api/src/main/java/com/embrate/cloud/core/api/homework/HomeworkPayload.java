/**
 * 
 */
package com.embrate.cloud.core.api.homework;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class HomeworkPayload {
	
	private final int instituteId;

	private int academicSessionId;

	private final UUID homeworkId;
	
	private final UUID standardId;

	private final List<Integer> sectionIdList;
	
	private final UUID courseId;
	
	private final String chapter;
	
	private final String title;

	private HomeworkType homeworkType;

	private final boolean allowMobileAppSubmissions;

	private final boolean attachDiscussionForum;
	
	private UUID createdUserId;
	
	private UUID updatedUserId;
	
	private final String description;
	
	private final Integer dueDate;
	
	private final HomeworkStatus homeworkStatus;
	
	private UUID broadcastedUserId;
	
	private final UUID facultyUserId;
	
	private final String facultyName;
	
	private final boolean allowEditAfterSubmit;
	
	private final boolean isGrade;
	
	private final Double maxMarks;
	
	private final Integer scheduledTimestamp;

	/**
	 * @param instituteId
	 * @param homeworkId
	 * @param standardId
	 * @param courseId
	 * @param chapter
	 * @param title
	 * @param createdUserId
	 * @param updatedUserId
	 * @param description
	 * @param dueDate
	 * @param homeworkStatus
	 * @param broadcastedUserId
	 * @param facultyUserId
	 * @param facultyName
	 * @param allowEditAfterSubmit
	 * @param isGrade
	 * @param maxMarks
	 * @param scheduledTimestamp
	 */
	public HomeworkPayload(int instituteId,int academicSessionId, UUID homeworkId, UUID standardId, List<Integer> sectionIdList, UUID courseId, String chapter,
						   String title,HomeworkType homeworkType, boolean allowMobileAppSubmissions, boolean attachDiscussionForum, UUID createdUserId, UUID updatedUserId, String description, Integer dueDate,
						   HomeworkStatus homeworkStatus, UUID broadcastedUserId, UUID facultyUserId, String facultyName,
						   boolean allowEditAfterSubmit, boolean isGrade, Double maxMarks, Integer scheduledTimestamp) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.homeworkId = homeworkId;
		this.standardId = standardId;
		this.sectionIdList = sectionIdList;
		this.courseId = courseId;
		this.chapter = chapter;
		this.title = title;
		this.homeworkType = homeworkType;
		this.allowMobileAppSubmissions = allowMobileAppSubmissions;
		this.attachDiscussionForum = attachDiscussionForum;
		this.createdUserId = createdUserId;
		this.updatedUserId = updatedUserId;
		this.description = description;
		this.dueDate = dueDate;
		this.homeworkStatus = homeworkStatus;
		this.broadcastedUserId = broadcastedUserId;
		this.facultyUserId = facultyUserId;
		this.facultyName = facultyName;
		this.allowEditAfterSubmit = allowEditAfterSubmit;
		this.isGrade = isGrade;
		this.maxMarks = maxMarks;
		this.scheduledTimestamp = scheduledTimestamp;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @return the academicSessionId
	 */
	public int getAcademicSessionId() {
		return academicSessionId;
	}

	/**
	 * @return the homeworkId
	 */
	public UUID getHomeworkId() {
		return homeworkId;
	}

	/**
	 * @return the standardId
	 */
	public UUID getStandardId() {
		return standardId;
	}

	public List<Integer> getSectionIdList() {
		return sectionIdList;
	}

	/**
	 * @return the courseId
	 */
	public UUID getCourseId() {
		return courseId;
	}

	/**
	 * @return the chapter
	 */
	public String getChapter() {
		return chapter;
	}

	/**
	 * @return the title
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * @return the homework type
	 */
	public HomeworkType getHomeworkType() {
		return homeworkType;
	}

	public boolean isAllowMobileAppSubmissions() {
		return allowMobileAppSubmissions;
	}

	public boolean isAttachDiscussionForum() {
		return attachDiscussionForum;
	}

	/**
	 * @return the createdUserId
	 */
	public UUID getCreatedUserId() {
		return createdUserId;
	}

	/**
	 * @return the updatedUserId
	 */
	public UUID getUpdatedUserId() {
		return updatedUserId;
	}

	/**
	 * @return the description
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * @return the dueDate
	 */
	public Integer getDueDate() {
		return dueDate;
	}

	/**
	 * @return the homeworkStatus
	 */
	public HomeworkStatus getHomeworkStatus() {
		return homeworkStatus;
	}

	/**
	 * @return the broadcastedUserId
	 */
	public UUID getBroadcastedUserId() {
		return broadcastedUserId;
	}

	/**
	 * @return the facultyUserId
	 */
	public UUID getFacultyUserId() {
		return facultyUserId;
	}

	/**
	 * @return the facultyName
	 */
	public String getFacultyName() {
		return facultyName;
	}

	/**
	 * @return the allowEditAfterSubmit
	 */
	public boolean isAllowEditAfterSubmit() {
		return allowEditAfterSubmit;
	}

	/**
	 * @return the isGrade
	 */
	public boolean isGrade() {
		return isGrade;
	}

	/**
	 * @return the maxMarks
	 */
	public Double getMaxMarks() {
		return maxMarks;
	}

	/**
	 * @return the scheduledTimestamp
	 */
	public Integer getScheduledTimestamp() {
		return scheduledTimestamp;
	}

	/**
	 * @param createdUserId the createdUserId to set
	 */
	public void setCreatedUserId(UUID createdUserId) {
		this.createdUserId = createdUserId;
	}

	/**
	 * @param updatedUserId the updatedUserId to set
	 */
	public void setUpdatedUserId(UUID updatedUserId) {
		this.updatedUserId = updatedUserId;
	}

	/**
	 * @param broadcastedUserId the broadcastedUserId to set
	 */
	public void setBroadcastedUserId(UUID broadcastedUserId) {
		this.broadcastedUserId = broadcastedUserId;
	}

	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	public void setHomeworkType(HomeworkType homeworkType) {
		this.homeworkType = homeworkType;
	}

	@Override
	public String toString() {
		return "HomeworkPayload{" +
				"instituteId=" + instituteId +
				", academicSessionId=" + academicSessionId +
				", homeworkId=" + homeworkId +
				", standardId=" + standardId +
				", sectionIdList=" + sectionIdList +
				", courseId=" + courseId +
				", chapter='" + chapter + '\'' +
				", title='" + title + '\'' +
				", homeworkType=" + homeworkType +
				", allowMobileAppSubmissions=" + allowMobileAppSubmissions +
				", attachDiscussionForum=" + attachDiscussionForum +
				", createdUserId=" + createdUserId +
				", updatedUserId=" + updatedUserId +
				", description='" + description + '\'' +
				", dueDate=" + dueDate +
				", homeworkStatus=" + homeworkStatus +
				", broadcastedUserId=" + broadcastedUserId +
				", facultyUserId=" + facultyUserId +
				", facultyName='" + facultyName + '\'' +
				", allowEditAfterSubmit=" + allowEditAfterSubmit +
				", isGrade=" + isGrade +
				", maxMarks=" + maxMarks +
				", scheduledTimestamp=" + scheduledTimestamp +
				'}';
	}
}
