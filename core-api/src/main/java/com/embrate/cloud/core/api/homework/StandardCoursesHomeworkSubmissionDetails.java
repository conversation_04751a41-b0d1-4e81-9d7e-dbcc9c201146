/**
 * 
 */
package com.embrate.cloud.core.api.homework;

import java.util.List;

import com.lernen.cloud.core.api.course.Course;

/**
 * <AUTHOR>
 *
 */
public class StandardCoursesHomeworkSubmissionDetails {

	private final Course course;
	
	private final List<HomeworkAndSubmissionDetails> homeworkAndSubmissionDetailsList;

	/**
	 * @param course
	 * @param homeworkSubmissionDetailsList
	 */
	public StandardCoursesHomeworkSubmissionDetails(Course course,
			List<HomeworkAndSubmissionDetails> homeworkAndSubmissionDetailsList) {
		this.course = course;
		this.homeworkAndSubmissionDetailsList = homeworkAndSubmissionDetailsList;
	}

	/**
	 * @return the course
	 */
	public Course getCourse() {
		return course;
	}

	/**
	 * @return the homeworkSubmissionDetailsList
	 */
	public List<HomeworkAndSubmissionDetails> getHomeworkAndSubmissionDetailsList() {
		return homeworkAndSubmissionDetailsList;
	}

	@Override
	public String toString() {
		return "StandardCoursesHomeworkSubmissionDetails [course=" + course + ", homeworkSubmissionDetailsList="
				+ homeworkAndSubmissionDetailsList + "]";
	}
	
}
