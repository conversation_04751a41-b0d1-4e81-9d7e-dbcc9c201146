/**
 * 
 */
package com.embrate.cloud.core.api.homework;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class StudentHomeworkDetails {

	private final int instituteId;
	
	private List<CoursesHomeworkDetails> coursesHomeworkDetails;

	/**
	 * @param instituteId
	 * @param coursesLectureDetails
	 */
	public StudentHomeworkDetails(int instituteId, List<CoursesHomeworkDetails> coursesLectureDetails) {
		this.instituteId = instituteId;
		this.coursesHomeworkDetails = coursesLectureDetails;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @return the coursesLectureDetails
	 */
	public List<CoursesHomeworkDetails> getCoursesHomeworkDetails() {
		return coursesHomeworkDetails;
	}

	public void setCoursesHomeworkDetails(List<CoursesHomeworkDetails> coursesHomeworkDetails) {
		this.coursesHomeworkDetails = coursesHomeworkDetails;
	}

	@Override
	public String toString() {
		return "StudentHomeworkDetails [instituteId=" + instituteId + ", coursesLectureDetails=" + coursesHomeworkDetails
				+ "]";
	}
	
}
