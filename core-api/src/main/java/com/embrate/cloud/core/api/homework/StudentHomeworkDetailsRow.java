/**
 * 
 */
package com.embrate.cloud.core.api.homework;

import com.lernen.cloud.core.api.course.Course;

/**
 * <AUTHOR>
 *
 */
public class StudentHomeworkDetailsRow {

	private int instituteId;
	
	private final Course course;
	
	private final HomeworkDetails homeworkDetails;
	
	private final HomeworkSubmissionDetails homeworkSubmissionDetails;

	/**
	 * @param instituteId
	 * @param course
	 * @param homeworkDetails
	 * @param homeworkSubmissionDetails
	 */
	public StudentHomeworkDetailsRow(int instituteId, Course course, HomeworkDetails homeworkDetails,
			HomeworkSubmissionDetails homeworkSubmissionDetails) {
		this.instituteId = instituteId;
		this.course = course;
		this.homeworkDetails = homeworkDetails;
		this.homeworkSubmissionDetails = homeworkSubmissionDetails;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @return the course
	 */
	public Course getCourse() {
		return course;
	}

	/**
	 * @return the homeworkDetails
	 */
	public HomeworkDetails getHomeworkDetails() {
		return homeworkDetails;
	}

	/**
	 * @return the homeworkSubmissionDetails
	 */
	public HomeworkSubmissionDetails getHomeworkSubmissionDetails() {
		return homeworkSubmissionDetails;
	}

	@Override
	public String toString() {
		return "StudentHomeworkDetailsRow [instituteId=" + instituteId + ", course=" + course + ", homeworkDetails="
				+ homeworkDetails + ", homeworkSubmissionDetails=" + homeworkSubmissionDetails + "]";
	}
	
}
