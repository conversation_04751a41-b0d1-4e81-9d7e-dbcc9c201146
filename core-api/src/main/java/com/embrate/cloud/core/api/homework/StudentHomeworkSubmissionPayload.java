package com.embrate.cloud.core.api.homework;

import java.util.UUID;

public class StudentHomeworkSubmissionPayload {

    private UUID studentId;

    private UUID homeworkSubmissionId;

    public StudentHomeworkSubmissionPayload(){

    }

    public StudentHomeworkSubmissionPayload(UUID studentId, UUID homeworkSubmissionId) {
        this.studentId = studentId;
        this.homeworkSubmissionId = homeworkSubmissionId;
    }

    public UUID getStudentId() {
        return studentId;
    }

    public void setStudentId(UUID studentId) {
        this.studentId = studentId;
    }

    public UUID getHomeworkSubmissionId() {
        return homeworkSubmissionId;
    }

    public void setHomeworkSubmissionId(UUID homeworkSubmissionId) {
        this.homeworkSubmissionId = homeworkSubmissionId;
    }

    @Override
    public String toString() {
        return "StudentHomeworkSubmissionPayload{" +
                "studentId=" + studentId +
                ", homeworkSubmissionId=" + homeworkSubmissionId +
                '}';
    }
}
