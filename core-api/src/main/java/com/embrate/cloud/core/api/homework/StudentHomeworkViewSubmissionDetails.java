package com.embrate.cloud.core.api.homework;

import com.lernen.cloud.core.api.student.StudentLite;

public class StudentHomeworkViewSubmissionDetails {

    private final StudentLite studentLite;

    private final HomeworkViewDetails homeworkViewDetails;

    private final HomeworkSubmissionDetails homeworkSubmissionDetails;

    /**
     * @param studentLite
     * @param homeworkViewDetails
     * @param homeworkSubmissionDetails
     ***/

    public StudentHomeworkViewSubmissionDetails(StudentLite studentLite, HomeworkViewDetails homeworkViewDetails, HomeworkSubmissionDetails homeworkSubmissionDetails) {
        this.studentLite = studentLite;
        this.homeworkViewDetails = homeworkViewDetails;
        this.homeworkSubmissionDetails = homeworkSubmissionDetails;
    }

    public StudentLite getStudentLite() {
        return studentLite;
    }

    public HomeworkViewDetails getHomeworkViewDetails() {
        return homeworkViewDetails;
    }

    public HomeworkSubmissionDetails getHomeworkSubmissionDetails() {
        return homeworkSubmissionDetails;
    }

    @Override
    public String toString() {
        return "StudentHomeworkViewDetails{" +
                "studentLite=" + studentLite +
                ", homeworkViewDetails=" + homeworkViewDetails +
                ", homeworkSubmissionDetails=" + homeworkSubmissionDetails +
                '}';
    }
}
