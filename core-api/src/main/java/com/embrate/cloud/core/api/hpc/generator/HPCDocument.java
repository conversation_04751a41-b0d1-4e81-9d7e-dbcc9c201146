package com.embrate.cloud.core.api.hpc.generator;

import com.itextpdf.layout.Document;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;

/**
 * <AUTHOR>
 */
public class HPCDocument {

	private final DocumentOutput output;

	private final Document document;

	private final DocumentLayoutSetup documentLayoutSetup;

	private final HPCDocumentLayout hpcDocumentLayout;

	public HPCDocument(DocumentOutput output, Document document, DocumentLayoutSetup documentLayoutSetup, HPCDocumentLayout hpcDocumentLayout) {
		this.output = output;
		this.document = document;
		this.documentLayoutSetup = documentLayoutSetup;
		this.hpcDocumentLayout = hpcDocumentLayout;
	}

	public DocumentOutput getOutput() {
		return output;
	}

	public Document getDocument() {
		return document;
	}

	public DocumentLayoutSetup getDocumentLayoutSetup() {
		return documentLayoutSetup;
	}

	public HPCDocumentLayout getHpcDocumentLayout() {
		return hpcDocumentLayout;
	}
}
