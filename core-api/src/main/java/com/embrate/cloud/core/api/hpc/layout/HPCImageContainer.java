package com.embrate.cloud.core.api.hpc.layout;

import com.embrate.cloud.core.api.hpc.payload.HPCDocumentType;
import com.lernen.cloud.core.api.user.Document;

/**
 * <AUTHOR>
 */
public class HPCImageContainer extends HPCElement {

	private String id;

	private Float imageHeight;

	private Float imageWidth;

	private HPCDocumentType imageType;

	private Document<HPCDocumentType> image;

	@Override
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Float getImageHeight() {
		return imageHeight;
	}

	public void setImageHeight(Float imageHeight) {
		this.imageHeight = imageHeight;
	}

	public Float getImageWidth() {
		return imageWidth;
	}

	public void setImageWidth(Float imageWidth) {
		this.imageWidth = imageWidth;
	}

	public HPCDocumentType getImageType() {
		return imageType;
	}

	public void setImageType(HPCDocumentType imageType) {
		this.imageType = imageType;
	}

	public Document<HPCDocumentType> getImage() {
		return image;
	}

	public void setImage(Document<HPCDocumentType> image) {
		this.image = image;
	}

	@Override
	public String toString() {
		return "HPCImageContainer{" +
				"id='" + id + '\'' +
				", imageHeight=" + imageHeight +
				", imageWidth=" + imageWidth +
				", imageType=" + imageType +
				", image=" + image +
				'}';
	}
}
