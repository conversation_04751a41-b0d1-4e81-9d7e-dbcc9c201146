package com.embrate.cloud.core.api.hpc.layout;

import com.embrate.cloud.core.api.hpc.utils.HPCExamType;

/**
 * <AUTHOR>
 */
public class HPCTableHeader {

	private HPCTableHeaderType headerType;
	private String text;

	private HPCExamType examType;

	private float widthRatio;

	private HPCPDFAttributes pdfAttributes;

	public HPCTableHeaderType getHeaderType() {
		return headerType;
	}

	public void setHeaderType(HPCTableHeaderType headerType) {
		this.headerType = headerType;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public HPCExamType getExamType() {
		return examType;
	}

	public void setExamType(HPCExamType examType) {
		this.examType = examType;
	}

	public float getWidthRatio() {
		return widthRatio;
	}

	public void setWidthRatio(float widthRatio) {
		this.widthRatio = widthRatio;
	}

	public HPCPDFAttributes getPdfAttributes() {
		return pdfAttributes;
	}

	public void setPdfAttributes(HPCPDFAttributes pdfAttributes) {
		this.pdfAttributes = pdfAttributes;
	}

	@Override
	public String toString() {
		return "HPCTableHeader{" +
				"headerType=" + headerType +
				", text='" + text + '\'' +
				", examType=" + examType +
				", widthRatio=" + widthRatio +
				", pdfAttributes=" + pdfAttributes +
				'}';
	}
}
