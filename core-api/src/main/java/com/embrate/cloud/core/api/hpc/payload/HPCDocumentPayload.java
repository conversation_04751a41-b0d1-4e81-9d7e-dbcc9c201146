package com.embrate.cloud.core.api.hpc.payload;

import com.lernen.cloud.core.api.user.DataUpdationAction;
import com.lernen.cloud.core.api.user.Document;

import java.util.UUID;

public class HPCDocumentPayload {

    private String id;

    private Document<HPCDocumentType> document;

    private DataUpdationAction dataUpdationAction;

    public HPCDocumentPayload() {
    }

    public HPCDocumentPayload(String id, Document<HPCDocumentType> document, DataUpdationAction dataUpdationAction) {
        this.id = id;
        this.document = document;
        this.dataUpdationAction = dataUpdationAction;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Document<HPCDocumentType> getDocument() {
        return document;
    }

    public void setDocument(Document<HPCDocumentType> document) {
        this.document = document;
    }

    public DataUpdationAction getDataUpdationAction() {
        return dataUpdationAction;
    }

    public void setDataUpdationAction(DataUpdationAction dataUpdationAction) {
        this.dataUpdationAction = dataUpdationAction;
    }

    @Override
    public String toString() {
        return "HPCDocumentPayload{" +
                "id='" + id + '\'' +
                ", document=" + document +
                ", dataUpdationAction=" + dataUpdationAction +
                '}';
    }
}
