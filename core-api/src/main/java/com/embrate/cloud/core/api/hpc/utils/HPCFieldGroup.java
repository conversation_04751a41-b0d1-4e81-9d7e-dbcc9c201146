package com.embrate.cloud.core.api.hpc.utils;

import java.util.List;

/**
 * <AUTHOR>
 */
public class HPCFieldGroup {

	private List<HPCFieldValue> fields;

	public HPCFieldGroup() {
	}

	public HPCFieldGroup(List<HPCFieldValue> fields) {
		this.fields = fields;
	}

	public List<HPCFieldValue> getFields() {
		return fields;
	}

	public void setFields(List<HPCFieldValue> fields) {
		this.fields = fields;
	}

	@Override
	public String toString() {
		return "HPCFieldGroup{" +
				"fields=" + fields +
				'}';
	}
}
