package com.embrate.cloud.core.api.hpc.web;

import com.embrate.cloud.core.api.hpc.payload.HPCDocumentType;
import com.lernen.cloud.core.api.user.Document;

/**
 * <AUTHOR>
 */
public class HPCWebImageContainer {

	private final String id;

	private final HPCDocumentType imageType;

	private final Document<HPCDocumentType> image;

	private final HPCFilledStatus hpcFilledStatus;

	public HPCWebImageContainer(String id, HPCDocumentType imageType, Document<HPCDocumentType> image) {
		this.id = id;
		this.imageType = imageType;
		this.image = image;
		this.hpcFilledStatus = computeHPCFilledStatus();
	}

	private HPCFilledStatus computeHPCFilledStatus() {
		if(image == null || image.getDocumentId() == null) {
			return HPCFilledStatus.PENDING;
		}
		return HPCFilledStatus.COMPLETED;
	}

	public String getId() {
		return id;
	}

	public HPCDocumentType getImageType() {
		return imageType;
	}

	public Document<HPCDocumentType> getImage() {
		return image;
	}

	public HPCFilledStatus getHpcFilledStatus() {
		return hpcFilledStatus;
	}

	@Override
	public String toString() {
		return "HPCWebImageContainer{" +
				"id='" + id + '\'' +
				", imageType=" + imageType +
				", image=" + image +
				", hpcFilledStatus=" + hpcFilledStatus +
				'}';
	}
}
