package com.embrate.cloud.core.api.hpc.web;

import com.embrate.cloud.core.api.hpc.utils.HPCUserType;

/**
 * <AUTHOR>
 */
public class HPCWebSection {

	private final String id;

	private final String heading;

	private final String subHeading;

	private final HPCUserType associatedUserType;

	private final HPCWebContainer container;

	public HPCWebSection(String id, String heading, String subHeading, HPCUserType associatedUserType, HPCWebContainer container) {
		this.id = id;
		this.heading = heading;
		this.subHeading = subHeading;
		this.associatedUserType = associatedUserType;
		this.container = container;
	}

	public String getId() {
		return id;
	}

	public String getHeading() {
		return heading;
	}

	public String getSubHeading() {
		return subHeading;
	}

	public HPCUserType getAssociatedUserType() {
		return associatedUserType;
	}

	public HPCWebContainer getContainer() {
		return container;
	}

	@Override
	public String toString() {
		return "HPCWebSection{" +
				"id='" + id + '\'' +
				", heading='" + heading + '\'' +
				", subHeading='" + subHeading + '\'' +
				", associatedUserType=" + associatedUserType +
				", container=" + container +
				'}';
	}
}
