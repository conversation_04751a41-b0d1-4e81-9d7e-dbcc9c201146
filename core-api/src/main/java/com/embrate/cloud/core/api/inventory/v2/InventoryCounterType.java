package com.embrate.cloud.core.api.inventory.v2;

import com.lernen.cloud.core.api.inventory.InventoryTransactionType;

/**
 * <AUTHOR>
 */
public enum InventoryCounterType {
    SALE("Sale"), PURCHASE("Purchase"), PURCHASE_RETURN("Purchase Return"), SALE_RETURN("Sale Return"), ISSUE("Issue");

    private String displayName;

    InventoryCounterType(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
}
