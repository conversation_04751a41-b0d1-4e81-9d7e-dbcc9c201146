package com.embrate.cloud.core.api.inventory.v2;

import com.lernen.cloud.core.api.inventory.Color;
import com.lernen.cloud.core.api.inventory.UserGroup;
import com.lernen.cloud.core.api.user.Gender;

import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class ProductVariationsRequest {

    private String name;

    private Set<String> sizes;

    private Set<String> colors;

    private Set<Gender> genders;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Set<String> getSizes() {
        return sizes;
    }

    public void setSizes(Set<String> sizes) {
        this.sizes = sizes;
    }

    public Set<String> getColors() {
        return colors;
    }

    public void setColors(Set<String> colors) {
        this.colors = colors;
    }

    public Set<Gender> getGenders() {
        return genders;
    }

    public void setGenders(Set<Gender> genders) {
        this.genders = genders;
    }

    @Override
    public String toString() {
        return "ProductVariationsRequest{" +
                "name='" + name + '\'' +
                ", sizes=" + sizes +
                ", colors=" + colors +
                ", genders=" + genders +
                '}';
    }
}
