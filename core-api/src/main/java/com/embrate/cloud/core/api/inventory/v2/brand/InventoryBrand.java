package com.embrate.cloud.core.api.inventory.v2.brand;

import java.util.UUID;

/**
 * <AUTHOR>
 */

public class InventoryBrand {

    private UUID brandId;

    private String brandName;

    public InventoryBrand() {
    }

    public InventoryBrand(UUID brandId, String brandName) {
        this.brandId = brandId;
        this.brandName = brandName;
    }

    public UUID getBrandId() {
        return brandId;
    }

    public void setBrandId(UUID brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    @Override
    public String toString() {
        return "InventoryBrand{" +
                "brandId=" + brandId +
                ", brandName='" + brandName + '\'' +
                '}';
    }
}


