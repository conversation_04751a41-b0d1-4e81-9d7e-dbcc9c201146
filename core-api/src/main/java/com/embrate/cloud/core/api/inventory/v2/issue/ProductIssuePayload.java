package com.embrate.cloud.core.api.inventory.v2.issue;

import com.embrate.cloud.core.api.inventory.v2.TradeProductPayload;
import com.lernen.cloud.core.api.inventory.InventoryUserType;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ProductIssuePayload {

    private Integer inventoryUserInstituteId;

    private InventoryUserType inventoryUserType;

    private String issuedTo;

    private String reference;

    private String email;

    private long transactionDate;

    private String description;

    private List<TradeProductPayload> issueProductPayloadList;

    public ProductIssuePayload() {
    }

    public ProductIssuePayload(Integer inventoryUserInstituteId, InventoryUserType inventoryUserType, String issuedTo, String reference, String email, long transactionDate, String description, List<TradeProductPayload> issueProductPayloadList) {
        this.inventoryUserInstituteId = inventoryUserInstituteId;
        this.inventoryUserType = inventoryUserType;
        this.issuedTo = issuedTo;
        this.reference = reference;
        this.email = email;
        this.transactionDate = transactionDate;
        this.description = description;
        this.issueProductPayloadList = issueProductPayloadList;
    }

    public Integer getInventoryUserInstituteId() {
        return inventoryUserInstituteId;
    }

    public void setInventoryUserInstituteId(Integer inventoryUserInstituteId) {
        this.inventoryUserInstituteId = inventoryUserInstituteId;
    }

    public InventoryUserType getInventoryUserType() {
        return inventoryUserType;
    }

    public void setInventoryUserType(InventoryUserType inventoryUserType) {
        this.inventoryUserType = inventoryUserType;
    }

    public String getIssuedTo() {
        return issuedTo;
    }

    public void setIssuedTo(String issuedTo) {
        this.issuedTo = issuedTo;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public long getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(long transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<TradeProductPayload> getIssueProductPayloadList() {
        return issueProductPayloadList;
    }

    public void setIssueProductPayloadList(List<TradeProductPayload> issueProductPayloadList) {
        this.issueProductPayloadList = issueProductPayloadList;
    }

    @Override
    public String toString() {
        return "ProductIssuePayload{" +
                "inventoryUserInstituteId=" + inventoryUserInstituteId +
                ", inventoryUserType=" + inventoryUserType +
                ", issuedTo='" + issuedTo + '\'' +
                ", reference='" + reference + '\'' +
                ", email='" + email + '\'' +
                ", transactionDate=" + transactionDate +
                ", description='" + description + '\'' +
                ", issueProductPayloadList=" + issueProductPayloadList +
                '}';
    }
}
