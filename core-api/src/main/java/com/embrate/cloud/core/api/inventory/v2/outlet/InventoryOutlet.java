package com.embrate.cloud.core.api.inventory.v2.outlet;

import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class InventoryOutlet {

    private UUID outletId;

    private String name;

    private UUID organisationId;

    private Set<Integer> instituteScope;
    
    private String description;

    public InventoryOutlet() {
    }

    public InventoryOutlet(UUID outletId, String name, UUID organisationId, Set<Integer> instituteScope, String description) {
        this.outletId = outletId;
        this.name = name;
        this.organisationId = organisationId;
        this.instituteScope = instituteScope;
        this.description = description;
    }

    public UUID getOutletId() {
        return outletId;
    }

    public void setOutletId(UUID outletId) {
        this.outletId = outletId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public UUID getOrganisationId() {
        return organisationId;
    }

    public void setOrganisationId(UUID organisationId) {
        this.organisationId = organisationId;
    }

    public Set<Integer> getInstituteScope() {
        return instituteScope;
    }

    public void setInstituteScope(Set<Integer> instituteScope) {
        this.instituteScope = instituteScope;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "InventoryOutlet{" + "outletId=" + outletId + ", name='" + name + '\'' + ", organisationId=" + organisationId + ", instituteScope=" + instituteScope + ", description='" + description + '\'' + '}';
    }
}