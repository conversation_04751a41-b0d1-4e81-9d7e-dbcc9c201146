package com.embrate.cloud.core.api.inventory.v2.product.group;

import java.util.UUID;

/**
 * <AUTHOR>
 */

public class InventoryProductGroupBasicInfo {

    private UUID groupId;

    private String groupName;

    /**
     * Assumed in %
     */
    private Double discount;

    private String description;

    public InventoryProductGroupBasicInfo() {
    }

    public InventoryProductGroupBasicInfo(UUID groupId, String groupName, Double discount, String description) {
        this.groupId = groupId;
        this.groupName = groupName;
        this.discount = discount;
        this.description = description;
    }

    public UUID getGroupId() {
        return groupId;
    }

    public void setGroupId(UUID groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "InventoryProductGroupBasicInfo{" +
                "groupId=" + groupId +
                ", groupName='" + groupName + '\'' +
                ", discount=" + discount +
                ", description='" + description + '\'' +
                '}';
    }
}
