package com.embrate.cloud.core.api.inventory.v2.product.group;

import com.embrate.cloud.core.api.inventory.v2.ProductBatchData;
import com.embrate.cloud.core.api.inventory.v2.ProductMetadata;
import com.embrate.cloud.core.api.inventory.v2.brand.InventoryBrand;
import com.embrate.cloud.core.api.inventory.v2.category.InventoryCategory;
import com.lernen.cloud.core.api.inventory.ProductDetails;
import com.lernen.cloud.core.api.inventory.ProductGroupBasicInfo;

/**
 * 
 * <AUTHOR>
 *
 */
public class InventoryProductGroupDetailedRow {

	private final InventoryProductGroupBasicInfo productGroupBasicInfo;

	private final ProductMetadata productMetadata;

	private final InventoryCategory category;

	private final InventoryBrand brand;

	private final ProductBatchData productBatchData;

	private final double quantity;

	/**
	 * Discount is assumed in %
	 */
	private final double discount;

	public InventoryProductGroupDetailedRow(InventoryProductGroupBasicInfo productGroupBasicInfo, ProductMetadata productMetadata, InventoryCategory category, InventoryBrand brand, ProductBatchData productBatchData, double quantity, double discount) {
		this.productGroupBasicInfo = productGroupBasicInfo;
		this.productMetadata = productMetadata;
		this.category = category;
		this.brand = brand;
		this.productBatchData = productBatchData;
		this.quantity = quantity;
		this.discount = discount;
	}

	public InventoryProductGroupBasicInfo getProductGroupBasicInfo() {
		return productGroupBasicInfo;
	}

	public ProductMetadata getProductMetadata() {
		return productMetadata;
	}

	public InventoryCategory getCategory() {
		return category;
	}

	public InventoryBrand getBrand() {
		return brand;
	}

	public ProductBatchData getProductBatchData() {
		return productBatchData;
	}

	public double getQuantity() {
		return quantity;
	}

	public double getDiscount() {
		return discount;
	}

	@Override
	public String toString() {
		return "InventoryProductGroupDetailedRow{" +
				"productGroupBasicInfo=" + productGroupBasicInfo +
				", productMetadata=" + productMetadata +
				", category=" + category +
				", brand=" + brand +
				", productBatchData=" + productBatchData +
				", quantity=" + quantity +
				", discount=" + discount +
				'}';
	}
}
