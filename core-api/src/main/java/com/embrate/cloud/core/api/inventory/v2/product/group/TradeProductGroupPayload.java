package com.embrate.cloud.core.api.inventory.v2.product.group;

import com.embrate.cloud.core.api.inventory.v2.ProductMetadataPayload;
import com.embrate.cloud.core.api.inventory.v2.TradeProductBatchPayload;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class TradeProductGroupPayload {

    private UUID groupId;

    private int quantity;

    /**
     * Total purchase price of batch. Quantity*PricePerGroup =
     * TotalPrice
     */
    private double totalPrice;

    /**
     * Total discount given on purchase quantity
     */
    private double totalDiscount;

    /**
     * Total tax amount added on purchase quantity
     */
    private double totalTax;

    public TradeProductGroupPayload() {
    }

    public UUID getGroupId() {
        return groupId;
    }

    public void setGroupId(UUID groupId) {
        this.groupId = groupId;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public double getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(double totalPrice) {
        this.totalPrice = totalPrice;
    }

    public double getTotalDiscount() {
        return totalDiscount;
    }

    public void setTotalDiscount(double totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    public double getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(double totalTax) {
        this.totalTax = totalTax;
    }

    @Override
    public String toString() {
        return "TradeProductGroupPayload{" +
                "groupId=" + groupId +
                ", quantity=" + quantity +
                ", totalPrice=" + totalPrice +
                ", totalDiscount=" + totalDiscount +
                ", totalTax=" + totalTax +
                '}';
    }
}
