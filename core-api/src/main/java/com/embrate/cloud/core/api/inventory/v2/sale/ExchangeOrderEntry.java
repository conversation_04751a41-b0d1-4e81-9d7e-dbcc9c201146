package com.embrate.cloud.core.api.inventory.v2.sale;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class ExchangeOrderEntry {

	private UUID returnTransactionId;

	private UUID newTradeTransactionId;

	private int exchangeSystemTime;

	public ExchangeOrderEntry() {
	}

	public ExchangeOrderEntry(UUID returnTransactionId, UUID newTradeTransactionId, int exchangeSystemTime) {
		this.returnTransactionId = returnTransactionId;
		this.newTradeTransactionId = newTradeTransactionId;
		this.exchangeSystemTime = exchangeSystemTime;
	}

	public UUID getReturnTransactionId() {
		return returnTransactionId;
	}

	public void setReturnTransactionId(UUID returnTransactionId) {
		this.returnTransactionId = returnTransactionId;
	}

	public UUID getNewTradeTransactionId() {
		return newTradeTransactionId;
	}

	public void setNewTradeTransactionId(UUID newTradeTransactionId) {
		this.newTradeTransactionId = newTradeTransactionId;
	}

	public int getExchangeSystemTime() {
		return exchangeSystemTime;
	}

	public void setExchangeSystemTime(int exchangeSystemTime) {
		this.exchangeSystemTime = exchangeSystemTime;
	}

	@Override
	public String toString() {
		return "ExchangeOrderEntry{" +
				"returnTransactionId=" + returnTransactionId +
				", newTradeTransactionId=" + newTradeTransactionId +
				", exchangeSystemTime=" + exchangeSystemTime +
				'}';
	}
}
