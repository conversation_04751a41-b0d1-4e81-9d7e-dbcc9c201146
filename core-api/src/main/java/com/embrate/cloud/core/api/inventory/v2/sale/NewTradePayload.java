package com.embrate.cloud.core.api.inventory.v2.sale;

import com.embrate.cloud.core.api.inventory.v2.TradeProductBatchPayload;
import com.embrate.cloud.core.api.inventory.v2.TradeProductPayload;
import com.embrate.cloud.core.api.inventory.v2.product.group.TradeProductGroupPayload;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.inventory.InventoryTransactionType;
import com.lernen.cloud.core.api.inventory.InventoryUserType;
import com.lernen.cloud.core.api.inventory.PaymentStatus;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
public class NewTradePayload {

    private Integer inventoryUserInstituteId;

    private InventoryUserType inventoryUserType;

    private InventoryTransactionType inventoryTransactionType;

    private TransactionMode transactionMode;

    private PaymentStatus paymentStatus;

    private String purchasedBy;

    private String reference;

    private String email;

    private long transactionDate;

    private String description;

    private Double additionalCost;

    private Double additionalDiscount;

    private boolean useWallet;

    private Double usedWalletAmount;

    private Double walletCreditAmount;

    private Double paidAmount;

    private List<TradeProductPayload> saleProductPayloadList;

    private List<TradeProductGroupPayload> saleProductGroupPayloadList;

    public NewTradePayload() {
    }

    public NewTradePayload(Integer inventoryUserInstituteId, InventoryUserType inventoryUserType, InventoryTransactionType inventoryTransactionType, TransactionMode transactionMode, PaymentStatus paymentStatus, String purchasedBy, String reference, String email, long transactionDate, String description, Double additionalCost, Double additionalDiscount, boolean useWallet, Double usedWalletAmount, Double walletCreditAmount, Double paidAmount, List<TradeProductPayload> saleProductPayloadList, List<TradeProductGroupPayload> saleProductGroupPayloadList) {
        this.inventoryUserInstituteId = inventoryUserInstituteId;
        this.inventoryUserType = inventoryUserType;
        this.inventoryTransactionType = inventoryTransactionType;
        this.transactionMode = transactionMode;
        this.paymentStatus = paymentStatus;
        this.purchasedBy = purchasedBy;
        this.reference = reference;
        this.email = email;
        this.transactionDate = transactionDate;
        this.description = description;
        this.additionalCost = additionalCost;
        this.additionalDiscount = additionalDiscount;
        this.useWallet = useWallet;
        this.usedWalletAmount = usedWalletAmount;
        this.walletCreditAmount = walletCreditAmount;
        this.paidAmount = paidAmount;
        this.saleProductPayloadList = saleProductPayloadList;
        this.saleProductGroupPayloadList = saleProductGroupPayloadList;
    }

    public Integer getInventoryUserInstituteId() {
        return inventoryUserInstituteId;
    }

    public void setInventoryUserInstituteId(Integer inventoryUserInstituteId) {
        this.inventoryUserInstituteId = inventoryUserInstituteId;
    }

    public InventoryUserType getInventoryUserType() {
        return inventoryUserType;
    }

    public void setInventoryUserType(InventoryUserType inventoryUserType) {
        this.inventoryUserType = inventoryUserType;
    }

    public InventoryTransactionType getInventoryTransactionType() {
        return inventoryTransactionType;
    }

    public void setInventoryTransactionType(InventoryTransactionType inventoryTransactionType) {
        this.inventoryTransactionType = inventoryTransactionType;
    }

    public TransactionMode getTransactionMode() {
        return transactionMode;
    }

    public void setTransactionMode(TransactionMode transactionMode) {
        this.transactionMode = transactionMode;
    }

    public PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getPurchasedBy() {
        return purchasedBy;
    }

    public void setPurchasedBy(String purchasedBy) {
        this.purchasedBy = purchasedBy;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public long getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(long transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Double getAdditionalCost() {
        return additionalCost;
    }

    public void setAdditionalCost(Double additionalCost) {
        this.additionalCost = additionalCost;
    }

    public Double getAdditionalDiscount() {
        return additionalDiscount;
    }

    public void setAdditionalDiscount(Double additionalDiscount) {
        this.additionalDiscount = additionalDiscount;
    }

    public boolean isUseWallet() {
        return useWallet;
    }

    public void setUseWallet(boolean useWallet) {
        this.useWallet = useWallet;
    }

    public Double getUsedWalletAmount() {
        return usedWalletAmount;
    }

    public void setUsedWalletAmount(Double usedWalletAmount) {
        this.usedWalletAmount = usedWalletAmount;
    }

    public Double getWalletCreditAmount() {
        return walletCreditAmount;
    }

    public void setWalletCreditAmount(Double walletCreditAmount) {
        this.walletCreditAmount = walletCreditAmount;
    }

    public Double getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(Double paidAmount) {
        this.paidAmount = paidAmount;
    }

    public List<TradeProductPayload> getSaleProductPayloadList() {
        return saleProductPayloadList;
    }

    public void setSaleProductPayloadList(List<TradeProductPayload> saleProductPayloadList) {
        this.saleProductPayloadList = saleProductPayloadList;
    }

    public List<TradeProductGroupPayload> getSaleProductGroupPayloadList() {
        return saleProductGroupPayloadList;
    }

    public void setSaleProductGroupPayloadList(List<TradeProductGroupPayload> saleProductGroupPayloadList) {
        this.saleProductGroupPayloadList = saleProductGroupPayloadList;
    }

    public double getNetPayableAmount() {
        double totalAmount = 0d;
        if (CollectionUtils.isEmpty(saleProductPayloadList) && CollectionUtils.isEmpty(saleProductGroupPayloadList)) {
            return totalAmount;
        }

        if(CollectionUtils.isNotEmpty(saleProductPayloadList)){
            for (TradeProductPayload tradeProductPayload : saleProductPayloadList) {
                if (CollectionUtils.isEmpty(tradeProductPayload.getTradeProductBatchPayloadList())) {
                    continue;
                }
                for (TradeProductBatchPayload tradeProductBatchPayload : tradeProductPayload.getTradeProductBatchPayloadList()) {
                    totalAmount += (tradeProductBatchPayload.getTotalPrice()
                            + tradeProductBatchPayload.getTotalTax()
                            - tradeProductBatchPayload.getTotalDiscount());
                }

            }
        }

        if(CollectionUtils.isNotEmpty(saleProductGroupPayloadList)){
            for (TradeProductGroupPayload tradeProductGroupPayload : saleProductGroupPayloadList) {
                    totalAmount += (tradeProductGroupPayload.getTotalPrice()
                            + tradeProductGroupPayload.getTotalTax()
                            - tradeProductGroupPayload.getTotalDiscount());
            }
        }


        totalAmount += (additionalCost == null || additionalCost < 0d
                ? 0d
                : additionalCost);

        totalAmount -= (additionalDiscount == null || additionalDiscount < 0d
                ? 0d
                : additionalDiscount);

        return totalAmount;
    }

    @Override
    public String toString() {
        return "NewTradePayload{" +
                "inventoryUserInstituteId=" + inventoryUserInstituteId +
                ", inventoryUserType=" + inventoryUserType +
                ", inventoryTransactionType=" + inventoryTransactionType +
                ", transactionMode=" + transactionMode +
                ", paymentStatus=" + paymentStatus +
                ", purchasedBy='" + purchasedBy + '\'' +
                ", reference='" + reference + '\'' +
                ", email='" + email + '\'' +
                ", transactionDate=" + transactionDate +
                ", description='" + description + '\'' +
                ", additionalCost=" + additionalCost +
                ", additionalDiscount=" + additionalDiscount +
                ", useWallet=" + useWallet +
                ", usedWalletAmount=" + usedWalletAmount +
                ", walletCreditAmount=" + walletCreditAmount +
                ", paidAmount=" + paidAmount +
                ", saleProductPayloadList=" + saleProductPayloadList +
                ", saleProductGroupPayloadList=" + saleProductGroupPayloadList +
                '}';
    }
}
