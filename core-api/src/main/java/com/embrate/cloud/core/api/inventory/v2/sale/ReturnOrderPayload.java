package com.embrate.cloud.core.api.inventory.v2.sale;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class ReturnOrderPayload {

	private UUID returnTransactionId;

	private List<ReturnedProduct> returnedProductList;

	public UUID getReturnTransactionId() {
		return returnTransactionId;
	}

	public void setReturnTransactionId(UUID returnTransactionId) {
		this.returnTransactionId = returnTransactionId;
	}

	public List<ReturnedProduct> getReturnedProductList() {
		return returnedProductList;
	}

	public void setReturnedProductList(List<ReturnedProduct> returnedProductList) {
		this.returnedProductList = returnedProductList;
	}

	@Override
	public String toString() {
		return "ReturnOrderPayload{" +
				"returnTransactionId=" + returnTransactionId +
				", returnedProductList=" + returnedProductList +
				'}';
	}
}
