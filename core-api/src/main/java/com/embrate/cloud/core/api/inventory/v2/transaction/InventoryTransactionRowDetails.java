package com.embrate.cloud.core.api.inventory.v2.transaction;

import com.embrate.cloud.core.api.inventory.v2.supplier.InventorySupplier;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.inventory.*;
import com.lernen.cloud.core.api.student.StudentLite;

import java.util.Map;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class InventoryTransactionRowDetails extends InventoryTransactionRow {

	private final String transactionToName;

	private final String transactionByName;

	private final InventorySupplier supplier;

	private final StudentLite studentLite;

	public InventoryTransactionRowDetails(UUID transactionId, String invoiceId, Integer inventoryUserInstituteId, String reference, String email, UUID skuId, String productName, UUID batchId, String batchName, InventoryTransactionType transactionType, InventoryUserType inventoryUserType, double quantity, double totalPrice, double totalDiscount, double totalTax, String transactionTo, long transactionDate, long transactionAddedAt, String transactionBy, PaymentStatus paymentStatus, String description, double additionalCost, double additionalDiscount, TransactionMode transactionMode, InventoryTransactionStatus inventoryTransactionStatus, Double usedWalletAmount, Double walletCreditAmount, Double paidAmount, Map<String, String> metadata, String transactionToName, String transactionByName, InventorySupplier supplier, StudentLite studentLite) {
		super(transactionId, invoiceId, inventoryUserInstituteId, reference, email, skuId, productName, batchId, batchName, transactionType, inventoryUserType, quantity, totalPrice, totalDiscount, totalTax, transactionTo, transactionDate, transactionAddedAt, transactionBy, paymentStatus, description, additionalCost, additionalDiscount, transactionMode, inventoryTransactionStatus, usedWalletAmount, walletCreditAmount, paidAmount, metadata);
		this.transactionToName = transactionToName;
		this.transactionByName = transactionByName;
		this.supplier = supplier;
		this.studentLite = studentLite;
	}

	public String getTransactionToName() {
		return transactionToName;
	}

	public String getTransactionByName() {
		return transactionByName;
	}

	public InventorySupplier getSupplier() {
		return supplier;
	}

	public StudentLite getStudentLite() {
		return studentLite;
	}
}
