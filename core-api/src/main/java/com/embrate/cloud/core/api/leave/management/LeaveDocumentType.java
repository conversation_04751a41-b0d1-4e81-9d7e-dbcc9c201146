package com.embrate.cloud.core.api.leave.management;

import com.lernen.cloud.core.api.common.DocumentType;

/**
 * <AUTHOR>
 * @created_at 01/01/24 : 15:14
 **/
public enum LeaveDocumentType  implements DocumentType {

    APPLY_LEAVE_ATTACHMENTS("Apply Leave Attachments", "Apply Leave Attachments", false),
    OTHER(null, "Other", false);

    private String documentName;
    private String displayName;
    private boolean isThumbnail;

    private LeaveDocumentType(String documentName, String displayName, boolean isThumbnail) {
        this.documentName = documentName;
        this.displayName = displayName;
        this.isThumbnail = isThumbnail;
    }

    @Override
    public String getDocumentName() {
        return documentName;
    }

    @Override
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public boolean isThumbnail() { return isThumbnail; }

}
