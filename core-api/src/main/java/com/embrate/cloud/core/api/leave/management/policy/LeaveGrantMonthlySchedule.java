package com.embrate.cloud.core.api.leave.management.policy;

/**
 * <AUTHOR>
 */

public class LeaveGrantMonthlySchedule {

    private int month;
    private int year;

    private Integer dayOfMonth;

    private Double leaveCount;

    // Exact date of expiry in epoch
    private Integer expireAt;

    public LeaveGrantMonthlySchedule() {
    }

    public LeaveGrantMonthlySchedule(int month, int year, Integer dayOfMonth, Double leaveCount, Integer expireAt) {
        this.month = month;
        this.year = year;
        this.dayOfMonth = dayOfMonth;
        this.leaveCount = leaveCount;
        this.expireAt = expireAt;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    public Integer getDayOfMonth() {
        return dayOfMonth;
    }

    public void setDayOfMonth(Integer dayOfMonth) {
        this.dayOfMonth = dayOfMonth;
    }

    public Double getLeaveCount() {
        return leaveCount;
    }

    public void setLeaveCount(Double leaveCount) {
        this.leaveCount = leaveCount;
    }

    public Integer getExpireAt() {
        return expireAt;
    }

    public void setExpireAt(Integer expireAt) {
        this.expireAt = expireAt;
    }

    public boolean isEmpty() {
        return getDayOfMonth() == null || getLeaveCount() == null || getExpireAt() == null;
    }

    @Override
    public String toString() {
        return "LeaveGrantMonthlySchedule{" +
                "month=" + month +
                ", year=" + year +
                ", dayOfMonth=" + dayOfMonth +
                ", leaveCount=" + leaveCount +
                ", expireAt=" + expireAt +
                '}';
    }
}
