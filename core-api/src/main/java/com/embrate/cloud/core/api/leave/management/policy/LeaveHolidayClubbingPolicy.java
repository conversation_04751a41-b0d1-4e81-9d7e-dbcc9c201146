package com.embrate.cloud.core.api.leave.management.policy;

/**
 * <AUTHOR>
 */

public class LeaveHolidayClubbingPolicy {

    private boolean considerSandwichHolidayAsLeave;


    public LeaveHolidayClubbingPolicy() {
    }

    public LeaveHolidayClubbingPolicy(boolean considerSandwichHolidayAsLeave) {
        this.considerSandwichHolidayAsLeave = considerSandwichHolidayAsLeave;
    }

    public boolean isConsiderSandwichHolidayAsLeave() {
        return considerSandwichHolidayAsLeave;
    }

    public void setConsiderSandwichHolidayAsLeave(boolean considerSandwichHolidayAsLeave) {
        this.considerSandwichHolidayAsLeave = considerSandwichHolidayAsLeave;
    }

    @Override
    public String toString() {
        return "LeaveHolidayClubbingPolicy{" +
                "considerSandwichHolidayAsLeave=" + considerSandwichHolidayAsLeave +
                '}';
    }
}
