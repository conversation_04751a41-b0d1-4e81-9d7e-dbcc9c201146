package com.embrate.cloud.core.api.leave.management.policy;

import com.embrate.cloud.core.api.leave.management.LeaveType;

import java.util.Map;

/**
 * <AUTHOR>
 */

public class LeaveTypePolicyDetails {

    private final LeaveType leaveType;

    private final LeaveTypePolicies leaveTypePolicies;

    private final Map<String, Object> metadata;

    public LeaveTypePolicyDetails(LeaveType leaveType, LeaveTypePolicies leaveTypePolicies, Map<String, Object> metadata) {
        this.leaveType = leaveType;
        this.leaveTypePolicies = leaveTypePolicies;
        this.metadata = metadata;
    }

    public LeaveType getLeaveType() {
        return leaveType;
    }

    public LeaveTypePolicies getLeaveTypePolicies() {
        return leaveTypePolicies;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    @Override
    public String toString() {
        return "LeaveTypePolicyDetails{" +
                "leaveType=" + leaveType +
                ", leaveTypePolicies=" + leaveTypePolicies +
                ", metadata=" + metadata +
                '}';
    }
}
