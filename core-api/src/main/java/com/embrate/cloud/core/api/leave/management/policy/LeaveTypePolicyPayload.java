package com.embrate.cloud.core.api.leave.management.policy;

import java.util.Map;

/**
 * <AUTHOR>
 */

public class LeaveTypePolicyPayload {

    private int leaveTypeId;

    private LeaveTypePolicies leaveTypePolicies;

    private Map<String, Object> metadata;

    public LeaveTypePolicyPayload() {
    }

    public LeaveTypePolicyPayload(int leaveTypeId, LeaveTypePolicies leaveTypePolicies, Map<String, Object> metadata) {
        this.leaveTypeId = leaveTypeId;
        this.leaveTypePolicies = leaveTypePolicies;
        this.metadata = metadata;
    }

    public int getLeaveTypeId() {
        return leaveTypeId;
    }

    public void setLeaveTypeId(int leaveTypeId) {
        this.leaveTypeId = leaveTypeId;
    }

    public LeaveTypePolicies getLeaveTypePolicies() {
        return leaveTypePolicies;
    }

    public void setLeaveTypePolicies(LeaveTypePolicies leaveTypePolicies) {
        this.leaveTypePolicies = leaveTypePolicies;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    @Override
    public String toString() {
        return "LeaveTypePolicyPayload{" +
                "leaveTypeId=" + leaveTypeId +
                ", leaveTypePolicies=" + leaveTypePolicies +
                ", metadata=" + metadata +
                '}';
    }
}
