package com.embrate.cloud.core.api.leave.management.policy.user;

import com.embrate.cloud.core.api.leave.management.policy.LeaveTypePolicyDetails;
import com.lernen.cloud.core.api.user.UserType;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class UserLeavePolicy {

    private final UUID userId;

    private final List<LeaveTypePolicyDetails> leaveTypePolicyDetailsList;

    public UserLeavePolicy(UUID userId,  List<LeaveTypePolicyDetails> leaveTypePolicyDetailsList) {
        this.userId = userId;
        this.leaveTypePolicyDetailsList = leaveTypePolicyDetailsList;
    }

    public UUID getUserId() {
        return userId;
    }


    public List<LeaveTypePolicyDetails> getLeaveTypePolicyDetailsList() {
        return leaveTypePolicyDetailsList;
    }

    @Override
    public String toString() {
        return "UserLeavePolicy{" +
                "userId=" + userId +
                ", leaveTypePolicyDetailsList=" + leaveTypePolicyDetailsList +
                '}';
    }
}
