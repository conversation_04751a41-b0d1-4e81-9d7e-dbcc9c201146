package com.embrate.cloud.core.api.leave.management.staff;

import com.embrate.cloud.core.api.leave.management.LeaveDocumentType;
import com.embrate.cloud.core.api.leave.management.LeaveType;
import com.embrate.cloud.core.api.leave.management.transaction.LeaveTransactionStatus;
import com.lernen.cloud.core.api.user.Document;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @created_at 21/09/24 : 11:00
 **/
public class StaffLeavePayload {
    private UUID transactionId;
    private  int academicSessionId;
    private int startDate;
    private int endDate;
    /**
     * id of staff whose leave is applied
     */
    private  UUID staffId;
    private LeaveTransactionStatus transactionStatus;
    /**
     * id of user who have applied leave for userId
     */
    private  UUID appliedBy;
    private  int appliedAt;
    private  String description;
    private List<Document<LeaveDocumentType>> leaveAttachments;
    private int leaveId;

    public StaffLeavePayload(UUID transactionId, int academicSessionId, int startDate, int endDate, UUID staffId, LeaveTransactionStatus transactionStatus, UUID appliedBy, int appliedAt, int leaveId, String description, List<Document<LeaveDocumentType>> leaveAttachments) {
        this.transactionId = transactionId;
        this.academicSessionId = academicSessionId;
        this.startDate = startDate;
        this.endDate = endDate;
        this.staffId = staffId;
        this.transactionStatus = transactionStatus;
        this.appliedBy = appliedBy;
        this.appliedAt = appliedAt;
        this.description = description;
        this.leaveAttachments = leaveAttachments;
        this.leaveId = leaveId;
    }

    public UUID getTransactionId() {
        return transactionId;
    }

    public int getLeaveId() {
        return leaveId;
    }

    public void setLeaveType(int leaveType) {
        this.leaveId = leaveType;
    }

    public void setTransactionId(UUID transactionId) {
        this.transactionId = transactionId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public void setAcademicSessionId(int academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public int getStartDate() {
        return startDate;
    }

    public void setStartDate(int startDate) {
        this.startDate = startDate;
    }

    public int getEndDate() {
        return endDate;
    }

    public void setEndDate(int endDate) {
        this.endDate = endDate;
    }

    public UUID getStaffId() {
        return staffId;
    }

    public void setStaffId(UUID studentId) {
        this.staffId = studentId;
    }

    public LeaveTransactionStatus getTransactionStatus() {
        return transactionStatus;
    }

    public void setTransactionStatus(LeaveTransactionStatus transactionStatus) {
        this.transactionStatus = transactionStatus;
    }

    public UUID getAppliedBy() {
        return appliedBy;
    }

    public void setAppliedBy(UUID appliedBy) {
        this.appliedBy = appliedBy;
    }

    public int getAppliedAt() {
        return appliedAt;
    }

    public void setAppliedAt(int appliedAt) {
        this.appliedAt = appliedAt;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<Document<LeaveDocumentType>> getLeaveAttachments() {
        return leaveAttachments;
    }

    public void setLeaveAttachments(List<Document<LeaveDocumentType>> leaveAttachments) {
        this.leaveAttachments = leaveAttachments;
    }

    @Override
    public String toString() {
        return "StaffLeavePayload{" +
                "transactionId=" + transactionId +
                ", academicSessionId=" + academicSessionId +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", staffId=" + staffId +
                ", transactionStatus=" + transactionStatus +
                ", appliedBy=" + appliedBy +
                ", appliedAt=" + appliedAt +
                ", leaveId=" + leaveId +
                ", description='" + description + '\'' +
                ", leaveAttachments=" + leaveAttachments +
                '}';
    }
}
