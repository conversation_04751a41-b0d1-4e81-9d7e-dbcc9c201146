package com.embrate.cloud.core.api.leave.management.transaction;

/**
 * <AUTHOR>
 */

public class UserLeaveSchedule {

    private final int leaveTypeId;

    private final LeaveTransactionCategory category;

    private final LeaveTransactionType transactionType;

    private final int dayStart;

    private final double leaveCount;

    public UserLeaveSchedule(int leaveTypeId, LeaveTransactionCategory category, LeaveTransactionType transactionType, int dayStart, double leaveCount) {
        this.leaveTypeId = leaveTypeId;
        this.category = category;
        this.transactionType = transactionType;
        this.dayStart = dayStart;
        this.leaveCount = leaveCount;
    }

    public int getLeaveTypeId() {
        return leaveTypeId;
    }

    public LeaveTransactionCategory getCategory() {
        return category;
    }

    public LeaveTransactionType getTransactionType() {
        return transactionType;
    }

    public int getDayStart() {
        return dayStart;
    }

    public double getLeaveCount() {
        return leaveCount;
    }

    @Override
    public String toString() {
        return "UserLeaveSchedule{" +
                "leaveTypeId=" + leaveTypeId +
                ", category=" + category +
                ", transactionType=" + transactionType +
                ", dayStart=" + dayStart +
                ", leaveCount=" + leaveCount +
                '}';
    }
}
