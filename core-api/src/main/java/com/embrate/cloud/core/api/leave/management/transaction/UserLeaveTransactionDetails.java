package com.embrate.cloud.core.api.leave.management.transaction;

import java.util.List;

/**
 * <AUTHOR>
 */

public class UserLeaveTransactionDetails {

    private UserLeaveTransactionMetadata metadata;

    private List<UserLeaveTypeTransactionData> leaveTypeTransactionDataList;

    public UserLeaveTransactionDetails() {
    }

    public UserLeaveTransactionDetails(UserLeaveTransactionMetadata metadata, List<UserLeaveTypeTransactionData> leaveTypeTransactionDataList) {
        this.metadata = metadata;
        this.leaveTypeTransactionDataList = leaveTypeTransactionDataList;
    }

    public UserLeaveTransactionMetadata getMetadata() {
        return metadata;
    }

    public List<UserLeaveTypeTransactionData> getLeaveTypeTransactionDataList() {
        return leaveTypeTransactionDataList;
    }

    public void setMetadata(UserLeaveTransactionMetadata metadata) {
        this.metadata = metadata;
    }

    public void setLeaveTypeTransactionDataList(List<UserLeaveTypeTransactionData> leaveTypeTransactionDataList) {
        this.leaveTypeTransactionDataList = leaveTypeTransactionDataList;
    }

    @Override
    public String toString() {
        return "UserLeaveTransactionDetails{" +
                "metadata=" + metadata +
                ", leaveTypeTransactionDataList=" + leaveTypeTransactionDataList +
                '}';
    }
}
