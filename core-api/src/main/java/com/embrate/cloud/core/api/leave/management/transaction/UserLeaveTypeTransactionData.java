package com.embrate.cloud.core.api.leave.management.transaction;

import com.embrate.cloud.core.api.leave.management.LeaveType;

/**
 * <AUTHOR>
 */

public class UserLeaveTypeTransactionData {

    private LeaveType leaveType;

    private int startDate;

    private double leaveCount;

    public UserLeaveTypeTransactionData() {
    }

    public UserLeaveTypeTransactionData(LeaveType leaveType, int startDate, double leaveCount) {
        this.leaveType = leaveType;
        this.startDate = startDate;
        this.leaveCount = leaveCount;
    }

    public LeaveType getLeaveType() {
        return leaveType;
    }

    public int getStartDate() {
        return startDate;
    }

    public void setLeaveType(LeaveType leaveType) {
        this.leaveType = leaveType;
    }

    public void setStartDate(int startDate) {
        this.startDate = startDate;
    }

    public void setLeaveCount(double leaveCount) {
        this.leaveCount = leaveCount;
    }

    public double getLeaveCount() {
        return leaveCount;
    }

    @Override
    public String toString() {
        return "UserLeaveTypeTransactionData{" +
                "leaveType=" + leaveType +
                ", startDate=" + startDate +
                ", leaveCount=" + leaveCount +
                '}';
    }
}
