/**
 * 
 */
package com.embrate.cloud.core.api.lecture;

import java.util.Map;

import com.lernen.cloud.core.api.course.Course;

/**
 * <AUTHOR>
 *
 */
public class CourseWiseStudentLectureStats {
	
	private Course course;
	
	private Map<StudentLecturesState, Integer> studentLecturesStateMap;

	/**
	 * @param course
	 * @param studentLecturesStateMap
	 */
	public CourseWiseStudentLectureStats(Course course, Map<StudentLecturesState, Integer> studentLecturesStateMap) {
		this.course = course;
		this.studentLecturesStateMap = studentLecturesStateMap;
	}

	/**
	 * @return the course
	 */
	public Course getCourse() {
		return course;
	}

	/**
	 * @param course the course to set
	 */
	public void setCourse(Course course) {
		this.course = course;
	}

	/**
	 * @return the studentLecturesStateMap
	 */
	public Map<StudentLecturesState, Integer> getStudentLecturesStateMap() {
		return studentLecturesStateMap;
	}

	/**
	 * @param studentLecturesStateMap the studentLecturesStateMap to set
	 */
	public void setStudentLecturesStateMap(Map<StudentLecturesState, Integer> studentLecturesStateMap) {
		this.studentLecturesStateMap = studentLecturesStateMap;
	}

	@Override
	public String toString() {
		return "CourseWiseStudentLectureStats [course=" + course + ", studentLecturesStateMap="
				+ studentLecturesStateMap + "]";
	}

}
