/**
 * 
 */
package com.embrate.cloud.core.api.lecture;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
public enum LectureReportType {
	BROADCASTED_LECTURE_REPORT;
	
	public static LectureReportType getLectureReportType(String lectureReportType) {
		if (StringUtils.isBlank(lectureReportType)) {
			return null;
		}
		for (LectureReportType lectureReportTypeEnum : LectureReportType.values()) {
			if (lectureReportTypeEnum.name().equalsIgnoreCase(lectureReportType)) {
				return lectureReportTypeEnum;
			}
		}
		return null;
	}
}
