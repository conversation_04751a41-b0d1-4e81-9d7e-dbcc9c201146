/**
 * 
 */
package com.embrate.cloud.core.api.lecture;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
public enum LectureStatus {
	
	SAVED, BROADCASTED;
	
	public static LectureStatus getLectureStatus(String lectureStatus){
		if(StringUtils.isBlank(lectureStatus)){
			return null;
		}
		for(LectureStatus lectureStatusEnum : LectureStatus.values()){
			if(lectureStatusEnum.name().equalsIgnoreCase(lectureStatus)){
				return lectureStatusEnum;
			}
		}
		return null;
	}

}
