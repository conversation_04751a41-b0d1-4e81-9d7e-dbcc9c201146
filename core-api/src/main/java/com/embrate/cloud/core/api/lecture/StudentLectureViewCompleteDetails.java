/**
 * 
 */
package com.embrate.cloud.core.api.lecture;

import java.util.List;

import com.lernen.cloud.core.api.student.StudentLite;

/**
 * <AUTHOR>
 *
 */
public class StudentLectureViewCompleteDetails {
	
	private StudentLite studentLite;
	
	private List<StudentLectureViewDetails> studentLectureViewDetails;
	
	private StudentLectureCompleteDetails studentLectureCompleteDetails;

	/**
	 * @param studentLite
	 * @param studentLectureViewDetails
	 * @param studentLectureCompleteDetails
	 */
	public StudentLectureViewCompleteDetails(StudentLite studentLite,
			List<StudentLectureViewDetails> studentLectureViewDetails,
			StudentLectureCompleteDetails studentLectureCompleteDetails) {
		this.studentLite = studentLite;
		this.studentLectureViewDetails = studentLectureViewDetails;
		this.studentLectureCompleteDetails = studentLectureCompleteDetails;
	}

	/**
	 * 
	 */
	public StudentLectureViewCompleteDetails() {
	}

	/**
	 * @return the studentLite
	 */
	public StudentLite getStudentLite() {
		return studentLite;
	}

	/**
	 * @param studentLite the studentLite to set
	 */
	public void setStudentLite(StudentLite studentLite) {
		this.studentLite = studentLite;
	}

	/**
	 * @return the studentLectureViewDetails
	 */
	public List<StudentLectureViewDetails> getStudentLectureViewDetails() {
		return studentLectureViewDetails;
	}

	/**
	 * @param studentLectureViewDetails the studentLectureViewDetails to set
	 */
	public void setStudentLectureViewDetails(List<StudentLectureViewDetails> studentLectureViewDetails) {
		this.studentLectureViewDetails = studentLectureViewDetails;
	}

	/**
	 * @return the studentLectureCompleteDetails
	 */
	public StudentLectureCompleteDetails getStudentLectureCompleteDetails() {
		return studentLectureCompleteDetails;
	}

	/**
	 * @param studentLectureCompleteDetails the studentLectureCompleteDetails to set
	 */
	public void setStudentLectureCompleteDetails(StudentLectureCompleteDetails studentLectureCompleteDetails) {
		this.studentLectureCompleteDetails = studentLectureCompleteDetails;
	}

	@Override
	public String toString() {
		return "StudentLectureViewCompleteDetails [studentLite=" + studentLite + ", studentLectureViewDetails="
				+ studentLectureViewDetails + ", studentLectureCompleteDetails=" + studentLectureCompleteDetails + "]";
	}

}
