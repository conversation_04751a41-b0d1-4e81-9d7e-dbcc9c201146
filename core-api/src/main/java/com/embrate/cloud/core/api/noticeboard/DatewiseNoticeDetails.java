package com.embrate.cloud.core.api.noticeboard;

import com.embrate.cloud.core.api.homework.DatewiseHomeworkDetails;
import com.embrate.cloud.core.api.homework.HomeworkDetails;

import java.util.List;

/**
 * <AUTHOR>
 * @created_at 21/11/23 : 01:46
 **/
public class DatewiseNoticeDetails implements Comparable<DatewiseNoticeDetails> {

    private final int date;
    private final List<NoticeDetails> noticeDetailsList;

    public DatewiseNoticeDetails(int date, List<NoticeDetails> noticeDetailsList) {
        this.date = date;
        this.noticeDetailsList = noticeDetailsList;
    }

    public int getDate() {
        return date;
    }

    public List<NoticeDetails> getNoticeDetailsList() {
        return noticeDetailsList;
    }

    @Override
    public String toString() {
        return "DatewiseNoticeDetails{" +
                "date=" + date +
                ", noticeDetailsList=" + noticeDetailsList +
                '}';
    }

    @Override
    public int compareTo(DatewiseNoticeDetails o) {
        return Integer.compare(o.date, this.date);
    }
}
