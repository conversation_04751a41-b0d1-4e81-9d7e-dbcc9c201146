/**
 * 
 */
package com.embrate.cloud.core.api.noticeboard;

import java.util.List;
import java.util.UUID;

import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.UserType;

/**
 * <AUTHOR>
 *
 */
public class NoticeBoardDetailsRow {

	private final int instituteId;

	private final int academicSessionId;
	
	private final UUID noticeId;
	
	private final String title;
	
	private final String body;
	
	private final UUID createdBy;
	
	private final Integer createdTimestamp;
	
	private final UUID updatedBy;
	
	private final Integer updatedTimestamp;
	
	private final Integer expiryDate;
	
	private final Boolean pinned;
	
	private final UUID pinnedBy;
	
	private final Integer pinnedTimestamp;
	
	private final NoticeBoardStatus status;
	
	private final UUID broadcastedBy;
	
	private final Integer broadcastedTimestamp;
	
	private final UserType userType;
	
	private final String entityName;
	
	private final String entityId;
	
	private int attachmentCount;
	
	private long attachmentSize;
	
	private String allowedMimeTypes;
	
	private final List<Document<NoticeBoardDocumentType>> noticeBoardAttachments;

	/**
	 * @param instituteId
	 * @param noticeId
	 * @param title
	 * @param body
	 * @param createdBy
	 * @param createdTimestamp
	 * @param updatedBy
	 * @param updatedTimestamp
	 * @param expiryDate
	 * @param pinned
	 * @param pinnedBy
	 * @param pinnedTimestamp
	 * @param status
	 * @param broadcastedBy
	 * @param broadcastedTimestamp
	 * @param userType
	 * @param entityId
	 * @param attachmentCount
	 * @param attachmentSize
	 * @param allowedMimeTypes
	 * @param noticeBoardAttachments
	 */
	public NoticeBoardDetailsRow(int instituteId, int academicSessionId, UUID noticeId, String title, String body, UUID createdBy,
			Integer createdTimestamp, UUID updatedBy, Integer updatedTimestamp, Integer expiryDate, Boolean pinned,
			UUID pinnedBy, Integer pinnedTimestamp, NoticeBoardStatus status, UUID broadcastedBy,
			Integer broadcastedTimestamp, UserType userType, String entityName, String entityId, int attachmentCount, long attachmentSize,
			String allowedMimeTypes, List<Document<NoticeBoardDocumentType>> noticeBoardAttachments) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.noticeId = noticeId;
		this.title = title;
		this.body = body;
		this.createdBy = createdBy;
		this.createdTimestamp = createdTimestamp;
		this.updatedBy = updatedBy;
		this.updatedTimestamp = updatedTimestamp;
		this.expiryDate = expiryDate;
		this.pinned = pinned;
		this.pinnedBy = pinnedBy;
		this.pinnedTimestamp = pinnedTimestamp;
		this.status = status;
		this.broadcastedBy = broadcastedBy;
		this.broadcastedTimestamp = broadcastedTimestamp;
		this.userType = userType;
		this.entityName = entityName;
		this.entityId = entityId;
		this.attachmentCount = attachmentCount;
		this.attachmentSize = attachmentSize;
		this.allowedMimeTypes = allowedMimeTypes;
		this.noticeBoardAttachments = noticeBoardAttachments;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	/**
	 * @return the attachmentCount
	 */
	public int getAttachmentCount() {
		return attachmentCount;
	}

	/**
	 * @param attachmentCount the attachmentCount to set
	 */
	public void setAttachmentCount(int attachmentCount) {
		this.attachmentCount = attachmentCount;
	}

	/**
	 * @return the attachmentSize
	 */
	public long getAttachmentSize() {
		return attachmentSize;
	}

	/**
	 * @param attachmentSize the attachmentSize to set
	 */
	public void setAttachmentSize(long attachmentSize) {
		this.attachmentSize = attachmentSize;
	}

	/**
	 * @return the allowedMimeTypes
	 */
	public String getAllowedMimeTypes() {
		return allowedMimeTypes;
	}

	/**
	 * @param allowedMimeTypes the allowedMimeTypes to set
	 */
	public void setAllowedMimeTypes(String allowedMimeTypes) {
		this.allowedMimeTypes = allowedMimeTypes;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @return the noticeId
	 */
	public UUID getNoticeId() {
		return noticeId;
	}

	/**
	 * @return the title
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * @return the body
	 */
	public String getBody() {
		return body;
	}

	/**
	 * @return the createdBy
	 */
	public UUID getCreatedBy() {
		return createdBy;
	}

	/**
	 * @return the createdTimestamp
	 */
	public Integer getCreatedTimestamp() {
		return createdTimestamp;
	}

	/**
	 * @return the updatedBy
	 */
	public UUID getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @return the updatedTimestamp
	 */
	public Integer getUpdatedTimestamp() {
		return updatedTimestamp;
	}

	/**
	 * @return the expiryDate
	 */
	public Integer getExpiryDate() {
		return expiryDate;
	}

	/**
	 * @return the pinned
	 */
	public Boolean getPinned() {
		return pinned;
	}

	/**
	 * @return the pinnedBy
	 */
	public UUID getPinnedBy() {
		return pinnedBy;
	}

	/**
	 * @return the pinnedTimestamp
	 */
	public Integer getPinnedTimestamp() {
		return pinnedTimestamp;
	}

	/**
	 * @return the status
	 */
	public NoticeBoardStatus getStatus() {
		return status;
	}

	/**
	 * @return the broadcastedBy
	 */
	public UUID getBroadcastedBy() {
		return broadcastedBy;
	}

	/**
	 * @return the broadcastedTimestamp
	 */
	public Integer getBroadcastedTimestamp() {
		return broadcastedTimestamp;
	}

	/**
	 * @return the userType
	 */
	public UserType getUserType() {
		return userType;
	}

	/**
	 * @return the entityName
	 */
	public String getEntityName() {
		return entityName;
	}

	/**
	 * @return the entityId
	 */
	public String getEntityId() {
		return entityId;
	}

	/**
	 * @return the noticeBoardAttachments
	 */
	public List<Document<NoticeBoardDocumentType>> getNoticeBoardAttachments() {
		return noticeBoardAttachments;
	}

	@Override
	public String toString() {
		return "NoticeBoardDetailsRow{" +
				"instituteId=" + instituteId +
				", academicSessionId=" + academicSessionId +
				", noticeId=" + noticeId +
				", title='" + title + '\'' +
				", body='" + body + '\'' +
				", createdBy=" + createdBy +
				", createdTimestamp=" + createdTimestamp +
				", updatedBy=" + updatedBy +
				", updatedTimestamp=" + updatedTimestamp +
				", expiryDate=" + expiryDate +
				", pinned=" + pinned +
				", pinnedBy=" + pinnedBy +
				", pinnedTimestamp=" + pinnedTimestamp +
				", status=" + status +
				", broadcastedBy=" + broadcastedBy +
				", broadcastedTimestamp=" + broadcastedTimestamp +
				", userType=" + userType +
				", entityName='" + entityName + '\'' +
				", entityId='" + entityId + '\'' +
				", attachmentCount=" + attachmentCount +
				", attachmentSize=" + attachmentSize +
				", allowedMimeTypes='" + allowedMimeTypes + '\'' +
				", noticeBoardAttachments=" + noticeBoardAttachments +
				'}';
	}

}
