package com.embrate.cloud.core.api.noticeboard;

import com.lernen.cloud.core.api.student.StudentLite;

import java.util.UUID;

public class NoticeStudentViewDetails {

    private UUID noticeId;

    private Integer viewedOn;

    private StudentLite studentLite;

    public NoticeStudentViewDetails(UUID noticeId, Integer viewedOn, StudentLite studentLite) {
        this.noticeId = noticeId;
        this.viewedOn = viewedOn;
        this.studentLite = studentLite;
    }

    public UUID getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(UUID noticeId) {
        this.noticeId = noticeId;
    }

    public Integer getViewedOn() {
        return viewedOn;
    }

    public void setViewedOn(Integer viewedOn) {
        this.viewedOn = viewedOn;
    }

    public StudentLite getStudentLite() {
        return studentLite;
    }

    public void setStudentLite(StudentLite studentLite) {
        this.studentLite = studentLite;
    }

    @Override
    public String toString() {
        return "NoticeStudentViewDetails{" +
                "noticeId=" + noticeId +
                ", viewedOn=" + viewedOn +
                ", studentLite=" + studentLite +
                '}';
    }
}
