package com.embrate.cloud.core.api.onboarding.institute;

/**
 *
 * <AUTHOR>
 *
 */
public class InstituteOnBoardingStudentDataField {

	private InstituteOnBoardingStudentDataFieldName studentDataFieldName;

	private Integer index;

	private Boolean mandatory;

	public InstituteOnBoardingStudentDataFieldName getStudentDataFieldName() {
		return studentDataFieldName;
	}

	public void setStudentDataFieldName(
			InstituteOnBoardingStudentDataFieldName studentDataFieldName) {
		this.studentDataFieldName = studentDataFieldName;
	}

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public Boolean isMandatory() {
		return mandatory;
	}

	public void setMandatory(Boolean mandatory) {
		this.mandatory = mandatory;
	}

}
