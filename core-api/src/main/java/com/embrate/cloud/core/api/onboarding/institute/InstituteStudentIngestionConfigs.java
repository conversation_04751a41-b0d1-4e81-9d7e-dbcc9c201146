package com.embrate.cloud.core.api.onboarding.institute;

import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
public class InstituteStudentIngestionConfigs {

	private List<InstituteOnBoardingStudentDataField> studentDataFieldList;

	private String studentDataDelimiter;

	private boolean studentDataHeader;

	private InstituteOnBoardingStudentClassColumnField studentClassColumnField;

	private String dobFormat;

	private String admissionDateFormat;

	public InstituteStudentIngestionConfigs() {
	}

	public InstituteStudentIngestionConfigs(
			List<InstituteOnBoardingStudentDataField> studentDataFieldList,
			String studentDataDelimiter, boolean studentDataHeader,
			InstituteOnBoardingStudentClassColumnField studentClassColumnField,
			String dobFormat, String admissionDateFormat) {
		this.studentDataFieldList = studentDataFieldList;
		this.studentDataDelimiter = studentDataDelimiter;
		this.studentDataHeader = studentDataHeader;
		this.studentClassColumnField = studentClassColumnField;
		this.dobFormat = dobFormat;
		this.admissionDateFormat = admissionDateFormat;
	}

	public List<InstituteOnBoardingStudentDataField> getStudentDataFieldList() {
		return studentDataFieldList;
	}

	public void setStudentDataFieldList(
			List<InstituteOnBoardingStudentDataField> studentDataFieldList) {
		this.studentDataFieldList = studentDataFieldList;
	}

	public String getStudentDataDelimiter() {
		return studentDataDelimiter;
	}

	public void setStudentDataDelimiter(String studentDataDelimiter) {
		this.studentDataDelimiter = studentDataDelimiter;
	}

	public boolean isStudentDataHeader() {
		return studentDataHeader;
	}

	public void setStudentDataHeader(boolean studentDataHeader) {
		this.studentDataHeader = studentDataHeader;
	}

	public InstituteOnBoardingStudentClassColumnField getStudentClassColumnField() {
		return studentClassColumnField;
	}

	public void setStudentClassColumnField(
			InstituteOnBoardingStudentClassColumnField studentClassColumnField) {
		this.studentClassColumnField = studentClassColumnField;
	}

	public String getDobFormat() {
		return dobFormat;
	}

	public void setDobFormat(String dobFormat) {
		this.dobFormat = dobFormat;
	}

	public String getAdmissionDateFormat() {
		return admissionDateFormat;
	}

	public void setAdmissionDateFormat(String admissionDateFormat) {
		this.admissionDateFormat = admissionDateFormat;
	}

	@Override
	public String toString() {
		return "InstituteStudentIngestionConfigs [studentDataFieldList="
				+ studentDataFieldList + ", studentDataDelimiter="
				+ studentDataDelimiter + ", studentDataHeader="
				+ studentDataHeader + ", studentClassColumnField="
				+ studentClassColumnField + ", dobFormat=" + dobFormat
				+ ", admissionDateFormat=" + admissionDateFormat + "]";
	}

}
