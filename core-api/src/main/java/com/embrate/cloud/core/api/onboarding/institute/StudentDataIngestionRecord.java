package com.embrate.cloud.core.api.onboarding.institute;

import java.util.ArrayList;
import java.util.List;

import com.lernen.cloud.core.api.student.RegisterStudentPayload;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentDataIngestionRecord {

	private final int lineNumber;

	private final String line;

	private final RegisterStudentPayload registerStudentPayload;

	private final boolean success;

	private List<String> errors = new ArrayList<>();

	private final Boolean feeStructuresAssigned;

	public StudentDataIngestionRecord(int lineNumber, String line,
			RegisterStudentPayload registerStudentPayload, boolean success,
			List<String> errors, Boolean feeStructuresAssigned) {
		this.lineNumber = lineNumber;
		this.line = line;
		this.registerStudentPayload = registerStudentPayload;
		this.success = success;
		this.errors = errors;
		this.feeStructuresAssigned = feeStructuresAssigned;
	}

	public int getLineNumber() {
		return lineNumber;
	}

	public String getLine() {
		return line;
	}

	public RegisterStudentPayload getRegisterStudentPayload() {
		return registerStudentPayload;
	}

	public boolean isSuccess() {
		return success;
	}

	public List<String> getErrors() {
		return errors;
	}

	public Boolean getFeeStructuresAssigned() {
		return feeStructuresAssigned;
	}

	public void setErrors(List<String> errors) {
		this.errors = errors;
	}

	public static StudentDataIngestionRecord forSuccess(int lineNumber,
			String line, RegisterStudentPayload registerStudentPayload,
			Boolean feeStructuresAssigned) {
		return new StudentDataIngestionRecord(lineNumber, line,
				registerStudentPayload, true, new ArrayList<>(),
				feeStructuresAssigned);

	}

	public static StudentDataIngestionRecord forFailure(int lineNumber,
			String line, List<String> errors) {
		return new StudentDataIngestionRecord(lineNumber, line, null, false,
				errors, null);

	}

	@Override
	public String toString() {
		return "InstituteOnBoardingStudentDataIngestionRecord [lineNumber="
				+ lineNumber + ", line=" + line + ", registerStudentPayload="
				+ registerStudentPayload + ", success=" + success + ", errors="
				+ errors + ", feeStructuresAssigned=" + feeStructuresAssigned
				+ "]";
	}

}
