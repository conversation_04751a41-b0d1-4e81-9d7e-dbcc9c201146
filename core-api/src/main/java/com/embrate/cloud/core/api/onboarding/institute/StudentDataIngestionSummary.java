package com.embrate.cloud.core.api.onboarding.institute;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentDataIngestionSummary {

	private final int totalCount;

	private final int successCount;

	private final int failureCount;

	private final List<StudentDataIngestionRecord> successStudentDataIngestionRecords;

	private final List<StudentDataIngestionRecord> failureStudentDataIngestionRecords;

	public StudentDataIngestionSummary(
			List<StudentDataIngestionRecord> successStudentDataIngestionRecords,
			List<StudentDataIngestionRecord> failureStudentDataIngestionRecords) {
		this.successStudentDataIngestionRecords = successStudentDataIngestionRecords;
		this.failureStudentDataIngestionRecords = failureStudentDataIngestionRecords;
		this.successCount = CollectionUtils
				.isEmpty(successStudentDataIngestionRecords)
						? 0
						: successStudentDataIngestionRecords.size();

		this.failureCount = CollectionUtils
				.isEmpty(failureStudentDataIngestionRecords)
						? 0
						: failureStudentDataIngestionRecords.size();
		this.totalCount = successCount + failureCount;
	}

	public int getTotalCount() {
		return totalCount;
	}

	public int getSuccessCount() {
		return successCount;
	}

	public int getFailureCount() {
		return failureCount;
	}

	public List<StudentDataIngestionRecord> getSuccessStudentDataIngestionRecords() {
		return successStudentDataIngestionRecords;
	}

	public List<StudentDataIngestionRecord> getFailureStudentDataIngestionRecords() {
		return failureStudentDataIngestionRecords;
	}

	@Override
	public String toString() {
		return "InstituteOnBoardingStudentDataIngestionSummary [totalCount="
				+ totalCount + ", successCount=" + successCount
				+ ", failureCount=" + failureCount
				+ ", successStudentDataIngestionRecords="
				+ successStudentDataIngestionRecords
				+ ", failureStudentDataIngestionRecords="
				+ failureStudentDataIngestionRecords + "]";
	}

}
