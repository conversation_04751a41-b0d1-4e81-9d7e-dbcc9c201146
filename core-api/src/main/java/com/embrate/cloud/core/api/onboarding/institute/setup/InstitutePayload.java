package com.embrate.cloud.core.api.onboarding.institute.setup;

import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.InstituteMetadataVariables;

import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class InstitutePayload {

	private int instituteId;

	private String instituteName;

	private String branchName;

	private String letterHeadLine1;

	private String letterHeadLine2;

	private String addressLine1;

	private String addressLine2;

	private String city;

	private String state;

	private String country;

	private String zipcode;

	private String landmark;

	private String logoUrl;

	private String secondLogoUrl;
	private String email;

	private String phoneNumber;

	private Map<InstituteMetadataVariables, String> instituteMetadataVariablesMap;

    public InstitutePayload(){

	}
	public InstitutePayload(int instituteId, String instituteName, String branchName, String letterHeadLine1,
							String letterHeadLine2, String addressLine1, String addressLine2, String city, String state,
							String country, String zipcode, String landmark, String logoUrl, String secondLogoUrl, String email, String phoneNumber,
							Map<InstituteMetadataVariables, String> instituteMetadataVariablesMap) {
		this.instituteId = instituteId;
		this.instituteName = instituteName;
		this.branchName = branchName;
		this.letterHeadLine1 = letterHeadLine1;
		this.letterHeadLine2 = letterHeadLine2;
		this.addressLine1 = addressLine1;
		this.addressLine2 = addressLine2;
		this.city = city;
		this.state = state;
		this.country = country;
		this.zipcode = zipcode;
		this.landmark = landmark;
		this.logoUrl = logoUrl;
		this.secondLogoUrl = secondLogoUrl;
		this.email = email;
		this.phoneNumber = phoneNumber;
		this.instituteMetadataVariablesMap = instituteMetadataVariablesMap;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public String getInstituteName() {
		return instituteName;
	}

	public void setInstituteName(String instituteName) {
		this.instituteName = instituteName;
	}

	public String getBranchName() {
		return branchName;
	}

	public void setBranchName(String branchName) {
		this.branchName = branchName;
	}

	public String getLetterHeadLine1() {
		return letterHeadLine1;
	}

	public void setLetterHeadLine1(String letterHeadLine1) {
		this.letterHeadLine1 = letterHeadLine1;
	}

	public String getLetterHeadLine2() {
		return letterHeadLine2;
	}

	public void setLetterHeadLine2(String letterHeadLine2) {
		this.letterHeadLine2 = letterHeadLine2;
	}

	public String getAddressLine1() {
		return addressLine1;
	}

	public void setAddressLine1(String addressLine1) {
		this.addressLine1 = addressLine1;
	}

	public String getAddressLine2() {
		return addressLine2;
	}

	public void setAddressLine2(String addressLine2) {
		this.addressLine2 = addressLine2;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getZipcode() {
		return zipcode;
	}

	public void setZipcode(String zipcode) {
		this.zipcode = zipcode;
	}

	public String getLandmark() {
		return landmark;
	}

	public void setLandmark(String landmark) {
		this.landmark = landmark;
	}

	public String getLogoUrl() {
		return logoUrl;
	}

	public void setLogoUrl(String logoUrl) {
		this.logoUrl = logoUrl;
	}

	public String getSecondLogoUrl() {
		return secondLogoUrl;
	}

	public void setSecondLogoUrl(String secondLogoUrl) {
		this.secondLogoUrl = secondLogoUrl;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public Map<InstituteMetadataVariables, String> getInstituteMetadataVariablesMap() {
		return instituteMetadataVariablesMap;
	}

	public void setInstituteMetadataVariablesMap(Map<InstituteMetadataVariables, String> instituteMetadataVariablesMap) {
		this.instituteMetadataVariablesMap = instituteMetadataVariablesMap;
	}

	/**
	 * this method should only be called when institute is added for the first time as it will remove any images(logo, watermark, second logo) from the institute
	 * NOTE: Do not use this method for updating institute details
	 * @param institutePayload
	 * @return
	 */
	public static Institute getInstituteCreationPayload(InstitutePayload institutePayload){
		return new Institute(institutePayload.getInstituteId(), null, institutePayload.getInstituteName(),
				institutePayload.getBranchName(), institutePayload.getLetterHeadLine1(), institutePayload.getLetterHeadLine2(),
				institutePayload.getAddressLine1(), institutePayload.getAddressLine2(), institutePayload.getCity(),
				institutePayload.getState(), institutePayload.getCountry(), institutePayload.getZipcode(),
				institutePayload.getLandmark(), institutePayload.getLogoUrl(), institutePayload.getSecondLogoUrl(), institutePayload.getEmail(), institutePayload.getPhoneNumber(),
				null, null, null, institutePayload.getInstituteMetadataVariablesMap(), null, true);
					}
				
	public String toString() {
		return "InstitutePayload [instituteId=" + instituteId + ", instituteName=" + instituteName + ", branchName="
				+ branchName + ", letterHeadLine1=" + letterHeadLine1 + ", letterHeadLine2=" + letterHeadLine2
				+ ", addressLine1=" + addressLine1 + ", addressLine2=" + addressLine2 + ", city=" + city + ", state="
				+ state + ", country=" + country + ", zipcode=" + zipcode + ", landmark=" + landmark + ", logoUrl="
				+ logoUrl + ", secondLogoUrl=" + secondLogoUrl + ", email=" + email + ", phoneNumber=" + phoneNumber
				+ ", instituteMetadataVariablesMap=" + instituteMetadataVariablesMap + "]";
	}
}
