package com.embrate.cloud.core.api.onboarding.institute.setup;

import java.util.List;

/**
 * <AUTHOR>
 */

public class InstituteSetupResult {

    private final boolean success;

    private final Integer errorCode;

    private final String errorReason;

    private final UserCredentials userCredentials;

    private final List<InstituteSetupSummary> instituteSetupSummaryList;

    public InstituteSetupResult(boolean success, Integer errorCode, String errorReason, UserCredentials userCredentials, List<InstituteSetupSummary> instituteSetupSummaryList) {
        this.success = success;
        this.errorCode = errorCode;
        this.errorReason = errorReason;
        this.userCredentials = userCredentials;
        this.instituteSetupSummaryList = instituteSetupSummaryList;
    }

    public boolean isSuccess() {
        return success;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public String getErrorReason() {
        return errorReason;
    }

    public UserCredentials getUserCredentials() {
        return userCredentials;
    }

    public List<InstituteSetupSummary> getInstituteSetupSummaryList() {
        return instituteSetupSummaryList;
    }

    @Override
    public String toString() {
        return "InstituteSetupResult{" +
                "success=" + success +
                ", errorCode=" + errorCode +
                ", errorReason='" + errorReason + '\'' +
                ", userCredentials=" + userCredentials +
                ", instituteSetupSummaryList=" + instituteSetupSummaryList +
                '}';
    }
}
