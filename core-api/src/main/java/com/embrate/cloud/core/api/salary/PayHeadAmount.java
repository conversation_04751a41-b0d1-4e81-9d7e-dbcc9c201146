/**
 * 
 */
package com.embrate.cloud.core.api.salary;

/**
 * <AUTHOR>
 *
 */
public class PayHeadAmount {
	
	private PayHeadConfiguration payHeadConfiguration;
	
	private Double amount;

	private boolean systemComputed;

	/**
	 * @param payHeadConfiguration
	 * @param amount
	 */
	public PayHeadAmount(PayHeadConfiguration payHeadConfiguration, Double amount) {
		this.payHeadConfiguration = payHeadConfiguration;
		this.amount = amount;
	}

	public PayHeadAmount(PayHeadConfiguration payHeadConfiguration, Double amount, boolean systemComputed) {
		this.payHeadConfiguration = payHeadConfiguration;
		this.amount = amount;
		this.systemComputed = systemComputed;
	}

	/**
	 * 
	 */
	public PayHeadAmount() {
	}

	/**
	 * @return the payHeadConfiguration
	 */
	public PayHeadConfiguration getPayHeadConfiguration() {
		return payHeadConfiguration;
	}

	/**
	 * @param payHeadConfiguration the payHeadConfiguration to set
	 */
	public void setPayHeadConfiguration(PayHeadConfiguration payHeadConfiguration) {
		this.payHeadConfiguration = payHeadConfiguration;
	}

	/**
	 * @return the amount
	 */
	public Double getAmount() {
		return amount;
	}

	/**
	 * @param amount the amount to set
	 */
	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public boolean isSystemComputed() {
		return systemComputed;
	}

	public void setSystemComputed(boolean systemComputed) {
		this.systemComputed = systemComputed;
	}

	@Override
	public String toString() {
		return "PayHeadAmount{" +
				"payHeadConfiguration=" + payHeadConfiguration +
				", amount=" + amount +
				", systemComputed=" + systemComputed +
				'}';
	}

}
