package com.embrate.cloud.core.api.salary;

public class PayHeadAmountMapping {

	private int payHeadId;
	
	private double amount;

	public PayHeadAmountMapping(int payHeadId, double amount) {
		this.payHeadId = payHeadId;
		this.amount = amount;
	}

	public PayHeadAmountMapping() {
	}

	public int getPayHeadId() {
		return payHeadId;
	}

	public void setPayHeadId(int payHeadId) {
		this.payHeadId = payHeadId;
	}

	public double getAmount() {
		return amount;
	}

	public void setAmount(double amount) {
		this.amount = amount;
	}

	@Override
	public String toString() {
		return "PayHeadAmountMapping{" +
				"payHeadId=" + payHeadId +
				", amount=" + amount +
				'}';
	}
}
