/**
 * 
 */
package com.embrate.cloud.core.api.salary;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
public enum PayHeadType {
	ADDITION, DEDUCTION;
	
	public static PayHeadType getPayHeadType(String payHeadType){
		if(StringUtils.isBlank(payHeadType)){
			return null;
		}
		for(PayHeadType payHeadTypeEnum : PayHeadType.values()){
			if(payHeadTypeEnum.name().equalsIgnoreCase(payHeadType)){
				return payHeadTypeEnum;
			}
		}
		return null;
	}
}
