package com.embrate.cloud.core.api.salary;

public class SalaryCycleDetailsPayload {

	private final int startDate;

	private final int endDate;

	private final String cycleName;

	public SalaryCycleDetailsPayload(int startDate, int endDate, String cycleName) {
		this.startDate = startDate;
		this.endDate = endDate;
		this.cycleName = cycleName;
	}

	public int getStartDate() {
		return startDate;
	}

	public int getEndDate() {
		return endDate;
	}

	public String getCycleName() {
		return cycleName;
	}

	@Override
	public String toString() {
		return "SalaryCycleDetailsPayload{" +
				"startDate=" + startDate +
				", endDate=" + endDate +
				", cycleName='" + cycleName + '\'' +
				'}';
	}
}
