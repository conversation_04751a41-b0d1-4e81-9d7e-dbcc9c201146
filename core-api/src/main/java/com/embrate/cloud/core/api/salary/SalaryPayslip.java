/**
 * 
 */
package com.embrate.cloud.core.api.salary;

import java.util.List;
import java.util.UUID;

import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffBasicInfo;

/**
 * <AUTHOR>
 *
 */
public class SalaryPayslip {

	private Integer instituteId;
	
	private UUID payslipId;
	
	private Staff staff;
	
	private FeePaymentTransactionStatus status;
	
	private Integer salaryCycleStart;
	
	private List<PayHeadAmount> payHeadAmountMap;
	
	private Double advanceAmount;
	
	private UUID advanceTransactionId;
	
	private String description;

	/**
	 * @param instituteId
	 * @param payslipId
	 * @param staff
	 * @param status
	 * @param salaryCycleStart
	 * @param payHeadAmountMap
	 * @param advanceAmount
	 * @param advanceTransactionId
	 * @param description
	 */
	public SalaryPayslip(Integer instituteId, UUID payslipId, Staff staff, FeePaymentTransactionStatus status,
			Integer salaryCycleStart, List<PayHeadAmount> payHeadAmountMap, Double advanceAmount,
			UUID advanceTransactionId, String description) {
		this.instituteId = instituteId;
		this.payslipId = payslipId;
		this.staff = staff;
		this.status = status;
		this.salaryCycleStart = salaryCycleStart;
		this.payHeadAmountMap = payHeadAmountMap;
		this.advanceAmount = advanceAmount;
		this.advanceTransactionId = advanceTransactionId;
		this.description = description;
	}

	/**
	 * 
	 */
	public SalaryPayslip() {
	}

	/**
	 * @return the instituteId
	 */
	public Integer getInstituteId() {
		return instituteId;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(Integer instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @return the payslipId
	 */
	public UUID getPayslipId() {
		return payslipId;
	}

	/**
	 * @param payslipId the payslipId to set
	 */
	public void setPayslipId(UUID payslipId) {
		this.payslipId = payslipId;
	}

	/**
	 * @return the staff
	 */
	public Staff getStaff() {
		return staff;
	}

	/**
	 * @param staff the staff to set
	 */
	public void setStaff(Staff staff) {
		this.staff = staff;
	}

	/**
	 * @return the status
	 */
	public FeePaymentTransactionStatus getStatus() {
		return status;
	}

	/**
	 * @param status the status to set
	 */
	public void setStatus(FeePaymentTransactionStatus status) {
		this.status = status;
	}

	/**
	 * @return the salaryCycleStart
	 */
	public Integer getSalaryCycleStart() {
		return salaryCycleStart;
	}

	/**
	 * @param salaryCycleStart the salaryCycleStart to set
	 */
	public void setSalaryCycleStart(Integer salaryCycleStart) {
		this.salaryCycleStart = salaryCycleStart;
	}

	/**
	 * @return the payHeadAmountMap
	 */
	public List<PayHeadAmount> getPayHeadAmountMap() {
		return payHeadAmountMap;
	}

	/**
	 * @param payHeadAmountMap the payHeadAmountMap to set
	 */
	public void setPayHeadAmountMap(List<PayHeadAmount> payHeadAmountMap) {
		this.payHeadAmountMap = payHeadAmountMap;
	}

	/**
	 * @return the advanceAmount
	 */
	public Double getAdvanceAmount() {
		return advanceAmount;
	}

	/**
	 * @param advanceAmount the advanceAmount to set
	 */
	public void setAdvanceAmount(Double advanceAmount) {
		this.advanceAmount = advanceAmount;
	}

	/**
	 * @return the advanceTransactionId
	 */
	public UUID getAdvanceTransactionId() {
		return advanceTransactionId;
	}

	/**
	 * @param advanceTransactionId the advanceTransactionId to set
	 */
	public void setAdvanceTransactionId(UUID advanceTransactionId) {
		this.advanceTransactionId = advanceTransactionId;
	}

	/**
	 * @return the description
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * @param description the description to set
	 */
	public void setDescription(String description) {
		this.description = description;
	}

	@Override
	public String toString() {
		return "SalaryPayslip [instituteId=" + instituteId + ", payslipId=" + payslipId + ", staff=" + staff
				+ ", status=" + status + ", salaryCycleStart=" + salaryCycleStart + ", payHeadAmountMap="
				+ payHeadAmountMap + ", advanceAmount=" + advanceAmount + ", advanceTransactionId="
				+ advanceTransactionId + ", description=" + description + "]";
	}
}
