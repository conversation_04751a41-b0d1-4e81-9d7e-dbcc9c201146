package com.embrate.cloud.core.api.salary.v2;

/**
 * <AUTHOR>
 */

public class SalaryCycle {

    private int cycleId;
    private int cycleStart;

    // End would be next day start Eg : Start: 1st Jan 2023 00:00:00 : End 1st Feb 2023 00:00:00
    private int cycleEnd;

    private String name;

    public SalaryCycle() {
    }

    public SalaryCycle(int cycleId, int cycleStart, int cycleEnd, String name) {
        this.cycleId = cycleId;
        this.cycleStart = cycleStart;
        this.cycleEnd = cycleEnd;
        this.name = name;
    }

    public int getCycleId() {
        return cycleId;
    }

    public void setCycleId(int cycleId) {
        this.cycleId = cycleId;
    }

    public int getCycleStart() {
        return cycleStart;
    }

    public void setCycleStart(int cycleStart) {
        this.cycleStart = cycleStart;
    }

    public int getCycleEnd() {
        return cycleEnd;
    }

    public void setCycleEnd(int cycleEnd) {
        this.cycleEnd = cycleEnd;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "SalaryCycle{" +
                "cycleId=" + cycleId +
                ", cycleStart=" + cycleStart +
                ", cycleEnd=" + cycleEnd +
                ", name='" + name + '\'' +
                '}';
    }
}
