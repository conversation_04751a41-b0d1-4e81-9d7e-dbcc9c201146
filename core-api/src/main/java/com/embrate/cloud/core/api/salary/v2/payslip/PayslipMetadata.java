package com.embrate.cloud.core.api.salary.v2.payslip;

import com.embrate.cloud.core.api.salary.v2.user.payslip.PayslipStatus;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class PayslipMetadata {

    private final UUID payslipId;

    private final UUID staffId;

    private final int cycleId;

    private final PayslipStatus status;

    private final int payslipDate;

    private final double advanceAmount;

    private final UUID advanceTransactionId;

    private final double totalEarnings;

    private final double totalDeductions;
    private final double netDeductions;
    private final double netSalary;
    private final double totalDays;
    private final double lopDays;
    private final String description;
    private final UUID addedBy;
    private final int addedAt;
    private final Map<String, String> metadata;


    public PayslipMetadata(UUID payslipId, UUID staffId, int cycleId, PayslipStatus status, int payslipDate, double advanceAmount, UUID advanceTransactionId, double totalEarnings, double totalDeductions, double totalDays, double lopDays, String description, UUID addedBy, int addedAt, Map<String, String> metadata) {
        this.payslipId = payslipId;
        this.staffId = staffId;
        this.cycleId = cycleId;
        this.status = status;
        this.payslipDate = payslipDate;
        this.advanceAmount = advanceAmount;
        this.advanceTransactionId = advanceTransactionId;
        this.totalEarnings = totalEarnings;
        this.totalDeductions = totalDeductions;
        this.netDeductions = totalDeductions + advanceAmount;
        this.netSalary = totalEarnings - netDeductions;
        this.totalDays = totalDays;
        this.lopDays = lopDays;
        this.description = description;
        this.addedBy = addedBy;
        this.addedAt = addedAt;
        this.metadata = metadata;
    }

    public UUID getPayslipId() {
        return payslipId;
    }

    public UUID getStaffId() {
        return staffId;
    }

    public int getCycleId() {
        return cycleId;
    }

    public PayslipStatus getStatus() {
        return status;
    }

    public int getPayslipDate() {
        return payslipDate;
    }

    public double getAdvanceAmount() {
        return advanceAmount;
    }

    public UUID getAdvanceTransactionId() {
        return advanceTransactionId;
    }

    public double getTotalEarnings() {
        return totalEarnings;
    }

    public double getTotalDeductions() {
        return totalDeductions;
    }

    public double getNetDeductions() {
        return netDeductions;
    }

    public double getNetSalary() {
        return netSalary;
    }

    public double getTotalDays() {
        return totalDays;
    }

    public double getLopDays() {
        return lopDays;
    }

    public String getDescription() {
        return description;
    }

    public UUID getAddedBy() {
        return addedBy;
    }

    public int getAddedAt() {
        return addedAt;
    }

    public Map<String, String> getMetadata() {
        return metadata;
    }

    @Override
    public String toString() {
        return "PayslipMetadata{" +
                "payslipId=" + payslipId +
                ", staffId=" + staffId +
                ", cycleId=" + cycleId +
                ", status=" + status +
                ", payslipDate=" + payslipDate +
                ", advanceAmount=" + advanceAmount +
                ", advanceTransactionId=" + advanceTransactionId +
                ", totalEarnings=" + totalEarnings +
                ", totalDeductions=" + totalDeductions +
                ", netDeductions=" + netDeductions +
                ", netSalary=" + netSalary +
                ", totalDays=" + totalDays +
                ", lopDays=" + lopDays +
                ", description='" + description + '\'' +
                ", addedBy=" + addedBy +
                ", addedAt=" + addedAt +
                ", metadata=" + metadata +
                '}';
    }
}
