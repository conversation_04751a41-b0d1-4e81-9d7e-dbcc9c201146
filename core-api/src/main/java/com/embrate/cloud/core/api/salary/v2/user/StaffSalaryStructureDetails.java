/**
 *
 */
package com.embrate.cloud.core.api.salary.v2.user;

import com.embrate.cloud.core.api.salary.v2.SalaryCycleDetails;
import com.embrate.cloud.core.api.salary.v2.SalaryStructureMetadata;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class StaffSalaryStructureDetails {

    private final SalaryStructureMetadata metadata;

    private final List<SalaryCycleDetails> salaryCycleDetailsList;


    public StaffSalaryStructureDetails(SalaryStructureMetadata metadata, List<SalaryCycleDetails> salaryCycleDetailsList) {
        this.metadata = metadata;
        this.salaryCycleDetailsList = salaryCycleDetailsList;
    }

    public SalaryStructureMetadata getMetadata() {
        return metadata;
    }

    public List<SalaryCycleDetails> getSalaryCycleDetailsList() {
        return salaryCycleDetailsList;
    }

    @Override
    public String toString() {
        return "SalaryStructureTemplate{" +
                "metadata=" + metadata +
                ", salaryCycleDetailsList=" + salaryCycleDetailsList +
                '}';
    }
}
