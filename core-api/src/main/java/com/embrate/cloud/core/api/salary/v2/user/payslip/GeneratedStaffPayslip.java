package com.embrate.cloud.core.api.salary.v2.user.payslip;

import com.embrate.cloud.core.api.salary.v2.user.StaffSalaryCycleStructureDetails;
import com.embrate.cloud.core.api.salary.v2.utils.StaffSalaryAttendanceSummary;
import com.lernen.cloud.core.api.staff.Staff;

/**
 * <AUTHOR>
 */

public class GeneratedStaffPayslip {

    private final Staff staff;

    private final StaffSalaryCycleStructureDetails salaryCycleStructureDetails;

    private final StaffSalaryAttendanceSummary attendanceSummary;

    private final double advanceDue;

    private final double advanceRecoveryAmount;

    private final double totalAdditionAmount;

    private final double totalDeductionAmount;

    private final double netSalaryAmount;

    public GeneratedStaffPayslip(Staff staff, StaffSalaryCycleStructureDetails salaryCycleStructureDetails, StaffSalaryAttendanceSummary attendanceSummary, double advanceDue, double advanceRecoveryAmount) {
        this.staff = staff;
        this.salaryCycleStructureDetails = salaryCycleStructureDetails;
        this.attendanceSummary = attendanceSummary;
        this.advanceDue = advanceDue;
        this.advanceRecoveryAmount = advanceRecoveryAmount;
        this.totalAdditionAmount = salaryCycleStructureDetails == null ? 0d : salaryCycleStructureDetails.getSalaryCycleDetails().getTotalAddition();
        this.totalDeductionAmount = salaryCycleStructureDetails == null ? 0d : salaryCycleStructureDetails.getSalaryCycleDetails().getTotalDeduction() + advanceRecoveryAmount;
        this.netSalaryAmount = totalAdditionAmount - totalDeductionAmount;
    }

    public Staff getStaff() {
        return staff;
    }

    public StaffSalaryCycleStructureDetails getSalaryCycleStructureDetails() {
        return salaryCycleStructureDetails;
    }

    public StaffSalaryAttendanceSummary getAttendanceSummary() {
        return attendanceSummary;
    }

    public double getAdvanceDue() {
        return advanceDue;
    }

    public double getAdvanceRecoveryAmount() {
        return advanceRecoveryAmount;
    }

    public double getTotalAdditionAmount() {
        return totalAdditionAmount;
    }

    public double getTotalDeductionAmount() {
        return totalDeductionAmount;
    }

    public double getNetSalaryAmount() {
        return netSalaryAmount;
    }

    @Override
    public String toString() {
        return "GeneratedStaffPayslip{" +
                "staff=" + staff +
                ", salaryCycleStructureDetails=" + salaryCycleStructureDetails +
                ", attendanceSummary=" + attendanceSummary +
                ", advanceDue=" + advanceDue +
                ", advanceRecoveryAmount=" + advanceRecoveryAmount +
                ", totalAdditionAmount=" + totalAdditionAmount +
                ", totalDeductionAmount=" + totalDeductionAmount +
                ", netSalaryAmount=" + netSalaryAmount +
                '}';
    }
}
