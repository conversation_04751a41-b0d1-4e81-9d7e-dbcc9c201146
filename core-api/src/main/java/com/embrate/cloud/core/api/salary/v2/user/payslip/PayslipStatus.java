package com.embrate.cloud.core.api.salary.v2.user.payslip;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */

public enum PayslipStatus {

    SAVED, APPROVED;


    public static PayslipStatus get(String payslipStatus) {
        if (StringUtils.isBlank(payslipStatus)) {
            return null;
        }
        for (PayslipStatus payslipStatusEnum : PayslipStatus.values()) {
            if (payslipStatusEnum.name().equalsIgnoreCase(payslipStatus)) {
                return payslipStatusEnum;
            }
        }
        return null;
    }
}
