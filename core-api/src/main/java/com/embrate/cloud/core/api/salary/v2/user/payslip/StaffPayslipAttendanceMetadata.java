package com.embrate.cloud.core.api.salary.v2.user.payslip;

/**
 * <AUTHOR>
 */

public class StaffPayslipAttendanceMetadata {

    private double totalDays;

    private double totalHolidays;

    private double presentDays;

    private double overTimeDays;

    private double absentDays;

    private double leaveDays;

    private double totalWorkingDays;

    private double totalLOPDays;

    public StaffPayslipAttendanceMetadata() {
    }

    public StaffPayslipAttendanceMetadata(double totalDays, double totalHolidays, double presentDays, double overTimeDays, double absentDays, double leaveDays, double totalWorkingDays, double totalLOPDays) {
        this.totalDays = totalDays;
        this.totalHolidays = totalHolidays;
        this.presentDays = presentDays;
        this.overTimeDays = overTimeDays;
        this.absentDays = absentDays;
        this.leaveDays = leaveDays;
        this.totalWorkingDays = totalWorkingDays;
        this.totalLOPDays = totalLOPDays;
    }

    public double getTotalDays() {
        return totalDays;
    }

    public void setTotalDays(double totalDays) {
        this.totalDays = totalDays;
    }

    public double getTotalHolidays() {
        return totalHolidays;
    }

    public void setTotalHolidays(double totalHolidays) {
        this.totalHolidays = totalHolidays;
    }

    public double getPresentDays() {
        return presentDays;
    }

    public void setPresentDays(double presentDays) {
        this.presentDays = presentDays;
    }

    public double getOverTimeDays() {
        return overTimeDays;
    }

    public void setOverTimeDays(double overTimeDays) {
        this.overTimeDays = overTimeDays;
    }

    public double getAbsentDays() {
        return absentDays;
    }

    public void setAbsentDays(double absentDays) {
        this.absentDays = absentDays;
    }

    public double getLeaveDays() {
        return leaveDays;
    }

    public void setLeaveDays(double leaveDays) {
        this.leaveDays = leaveDays;
    }

    public double getTotalWorkingDays() {
        return totalWorkingDays;
    }

    public void setTotalWorkingDays(double totalWorkingDays) {
        this.totalWorkingDays = totalWorkingDays;
    }

    public double getTotalLOPDays() {
        return totalLOPDays;
    }

    public void setTotalLOPDays(double totalLOPDays) {
        this.totalLOPDays = totalLOPDays;
    }

    @Override
    public String toString() {
        return "StaffPayslipAttendanceMetadata{" +
                "totalDays=" + totalDays +
                ", totalHolidays=" + totalHolidays +
                ", presentDays=" + presentDays +
                ", overTimeDays=" + overTimeDays +
                ", absentDays=" + absentDays +
                ", leaveDays=" + leaveDays +
                ", totalWorkingDays=" + totalWorkingDays +
                ", totalLOPDays=" + totalLOPDays +
                '}';
    }
}
