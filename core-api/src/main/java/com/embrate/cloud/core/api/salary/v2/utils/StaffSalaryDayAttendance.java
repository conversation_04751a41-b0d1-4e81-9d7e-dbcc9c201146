package com.embrate.cloud.core.api.salary.v2.utils;

/**
 * <AUTHOR>
 */

public class StaffSalaryDayAttendance {

    private final int dayStart;

    private final boolean holiday;
    private SalaryAttendanceStatus attendanceStatus;

//    private final LeaveCount leaveCount;

    // Keeping total count for now
    private double leaveCount;

    public StaffSalaryDayAttendance(int dayStart, boolean holiday, SalaryAttendanceStatus attendanceStatus, double leaveCount) {
        this.dayStart = dayStart;
        this.holiday = holiday;
        this.attendanceStatus = attendanceStatus;
        this.leaveCount = leaveCount;
    }

    public int getDayStart() {
        return dayStart;
    }

    public SalaryAttendanceStatus getAttendanceStatus() {
        return attendanceStatus;
    }

    public boolean isHoliday() {
        return holiday;
    }

    public double getLeaveCount() {
        return leaveCount;
    }

    public void setAttendanceStatus(SalaryAttendanceStatus attendanceStatus) {
        this.attendanceStatus = attendanceStatus;
    }

    public void setLeaveCount(double leaveCount) {
        this.leaveCount = leaveCount;
    }

    @Override
    public String toString() {
        return "StaffSalaryDayAttendance{" +
                "dayStart=" + dayStart +
                ", holiday=" + holiday +
                ", attendanceStatus=" + attendanceStatus +
                ", leaveCount=" + leaveCount +
                '}';
    }
}
