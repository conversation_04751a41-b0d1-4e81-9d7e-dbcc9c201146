package com.embrate.cloud.core.api.scratchcard;

import java.util.Map;
import java.util.UUID;

public class RewardDetails {

    private final UUID rewardId;

    private final RewardType rewardType;

    private final String entityId; //Id of rewardType (can be null, in case of cashback)

    private final Map<String, Object> metaData; //{"amount": "10"}

    private final String description;

    private Integer dueDate;

    private Integer expiryDate;

    public RewardDetails(UUID rewardId, RewardType rewardType, String entityId, Map<String, Object> metaData, String description, Integer dueDate, Integer expiryDate) {
        this.rewardId = rewardId;
        this.rewardType = rewardType;
        this.entityId = entityId;
        this.metaData = metaData;
        this.description = description;
        this.dueDate = dueDate;
        this.expiryDate = expiryDate;
    }

    public UUID getRewardId() {
        return rewardId;
    }

    public RewardType getRewardType() {
        return rewardType;
    }

    public String getEntityId() {
        return entityId;
    }

    public Map<String, Object> getMetaData() {
        return metaData;
    }

    public String getDescription() {
        return description;
    }

    public Integer getDueDate() {
        return dueDate;
    }

    public Integer getExpiryDate() {
        return expiryDate;
    }

    public void setDueDate(Integer dueDate) {
        this.dueDate = dueDate;
    }

    public void setExpiryDate(Integer expiryDate) {
        this.expiryDate = expiryDate;
    }

    @Override
    public String toString() {
        return "RewardDetails{" +
                "rewardId=" + rewardId +
                ", rewardType=" + rewardType +
                ", entityId='" + entityId + '\'' +
                ", metaData=" + metaData +
                ", description='" + description + '\'' +
                ", dueDate=" + dueDate +
                ", expiryDate=" + expiryDate +
                '}';
    }
}
