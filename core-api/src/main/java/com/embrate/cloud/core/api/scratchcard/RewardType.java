package com.embrate.cloud.core.api.scratchcard;

import org.apache.commons.lang3.StringUtils;

public enum RewardType {

    CASHBACK, GIFTS, VOUCHER;

    public static RewardType getRewardType(String rewardType) {
        if (StringUtils.isBlank(rewardType)) {
            return null;
        }
        for (RewardType rewardTypeEnum : RewardType.values()) {
            if (rewardTypeEnum.name().equalsIgnoreCase(rewardType)) {
                return rewardTypeEnum;
            }
        }
        return null;
    }
}
