package com.embrate.cloud.core.api.service.communication;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class CommunicationServiceActionResponse {

    public final String uniqueReferenceId;

    public final String destinationChannelId;

    private final Integer creditCount;

    private final Map<String, Object> metadata;

    public CommunicationServiceActionResponse(String uniqueReferenceId, String destinationChannelId, Integer creditCount, Map<String, Object> metadata) {
        this.uniqueReferenceId = uniqueReferenceId;
        this.destinationChannelId = destinationChannelId;
        this.creditCount = creditCount;
        this.metadata = metadata;
    }

    public String getUniqueReferenceId() {
        return uniqueReferenceId;
    }

    public String getDestinationChannelId() {
        return destinationChannelId;
    }

    public Integer getCreditCount() {
        return creditCount;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    @Override
    public String toString() {
        return "CommunicationServiceActionResponse{" +
                "uniqueReferenceId='" + uniqueReferenceId + '\'' +
                ", destinationChannelId='" + destinationChannelId + '\'' +
                ", creditCount=" + creditCount +
                ", metadata=" + metadata +
                '}';
    }
}
