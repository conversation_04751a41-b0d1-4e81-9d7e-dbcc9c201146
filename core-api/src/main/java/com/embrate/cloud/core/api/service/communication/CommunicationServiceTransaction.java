package com.embrate.cloud.core.api.service.communication;

import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.user.UserType;
/**
 * <AUTHOR>
 *
 */
public class CommunicationServiceTransaction {

	private int instituteId;
	
	private UUID transactionId;

	private DeliveryMode deliveryMode;

	private CommunicationServiceProvider serviceProvider;
	
	private UserType userType;
	
	private UUID transactionBy;
	
	private Integer transactionAddedAt;
	
	private Double amount;
	
	private Integer creditUpdatedCount;
	
	private CommunicationServiceTransactionType transactionType;
	
	private CommunicationServiceTransactionStatus status;
	
	private Map<String, Object> metaData;
	
	private String description;

	public CommunicationServiceTransaction() {
	}

	public CommunicationServiceTransaction(int instituteId, UUID transactionId, CommunicationServiceProvider serviceProvider, UserType userType, UUID transactionBy, Integer transactionAddedAt, Double amount, Integer creditUpdatedCount, CommunicationServiceTransactionType transactionType, CommunicationServiceTransactionStatus status, Map<String, Object> metaData, String description) {
		this.instituteId = instituteId;
		this.transactionId = transactionId;
		this.deliveryMode = serviceProvider.getDeliveryMode();
		this.serviceProvider = serviceProvider;
		this.userType = userType;
		this.transactionBy = transactionBy;
		this.transactionAddedAt = transactionAddedAt;
		this.amount = amount;
		this.creditUpdatedCount = creditUpdatedCount;
		this.transactionType = transactionType;
		this.status = status;
		this.metaData = metaData;
		this.description = description;
	}

	public CommunicationServiceTransaction(int instituteId, CommunicationServiceProvider serviceProvider, UserType userType, UUID transactionBy, Double amount, Integer creditUpdatedCount, CommunicationServiceTransactionType transactionType, CommunicationServiceTransactionStatus status, Map<String, Object> metaData, String description) {
		this.instituteId = instituteId;
		this.deliveryMode = serviceProvider.getDeliveryMode();
		this.serviceProvider = serviceProvider;
		this.userType = userType;
		this.transactionBy = transactionBy;
		this.amount = amount;
		this.creditUpdatedCount = creditUpdatedCount;
		this.transactionType = transactionType;
		this.status = status;
		this.metaData = metaData;
		this.description = description;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getTransactionId() {
		return transactionId;
	}

	public void setTransactionId(UUID transactionId) {
		this.transactionId = transactionId;
	}

	public DeliveryMode getDeliveryMode() {
		return deliveryMode;
	}

	public CommunicationServiceProvider getServiceProvider() {
		return serviceProvider;
	}

	public UserType getUserType() {
		return userType;
	}

	public UUID getTransactionBy() {
		return transactionBy;
	}

	public Integer getTransactionAddedAt() {
		return transactionAddedAt;
	}

	public void setTransactionAddedAt(Integer transactionAddedAt) {
		this.transactionAddedAt = transactionAddedAt;
	}

	public Double getAmount() {
		return amount;
	}

	public Integer getCreditUpdatedCount() {
		return creditUpdatedCount;
	}

	public CommunicationServiceTransactionType getTransactionType() {
		return transactionType;
	}

	public CommunicationServiceTransactionStatus getStatus() {
		return status;
	}

	public Map<String, Object> getMetaData() {
		return metaData;
	}

	public String getDescription() {
		return description;
	}

	public void setDeliveryMode(DeliveryMode deliveryMode) {
		this.deliveryMode = deliveryMode;
	}

	public void setServiceProvider(CommunicationServiceProvider serviceProvider) {
		this.serviceProvider = serviceProvider;
	}

	public void setUserType(UserType userType) {
		this.userType = userType;
	}

	public void setTransactionBy(UUID transactionBy) {
		this.transactionBy = transactionBy;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public void setCreditUpdatedCount(Integer creditUpdatedCount) {
		this.creditUpdatedCount = creditUpdatedCount;
	}

	public void setTransactionType(CommunicationServiceTransactionType transactionType) {
		this.transactionType = transactionType;
	}

	public void setStatus(CommunicationServiceTransactionStatus status) {
		this.status = status;
	}

	public void setMetaData(Map<String, Object> metaData) {
		this.metaData = metaData;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Override
	public String toString() {
		return "CommunicationServiceTransactionPayload{" +
				"instituteId=" + instituteId +
				", transactionId=" + transactionId +
				", deliveryMode=" + deliveryMode +
				", serviceProvider=" + serviceProvider +
				", userType=" + userType +
				", transactionBy=" + transactionBy +
				", transactionAddedAt=" + transactionAddedAt +
				", amount=" + amount +
				", creditUpdatedCount=" + creditUpdatedCount +
				", transactionType=" + transactionType +
				", status=" + status +
				", metaData=" + metaData +
				", description='" + description + '\'' +
				'}';
	}
}
