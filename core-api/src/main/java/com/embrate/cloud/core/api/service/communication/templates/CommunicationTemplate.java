package com.embrate.cloud.core.api.service.communication.templates;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.embrate.cloud.core.api.audio.AudioFileProperties;
import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.configurations.Entity;

/**
 * 
 * <AUTHOR>
 *
 */
public class CommunicationTemplate implements ITemplate {

	public static final String AUDIO_METADATA = "audio_metadata";

	private final UUID templateId;
	private final Entity entityName;
	private final String entityId;
	private final DeliveryMode deliveryMode;
	private final TemplateType templateType;
	private final int version;
	private final String templateValue;
	private final String templateName;
	private final String locale;
	private final TemplateStatus templateStatus;
	private final TemplateVariableDetails templateVariableDetails;
	private final Map<String, Object> metadata;
	private final UUID templateAddedBy;
	private final Integer templateAddedAt;
	private final String description;
	private Integer credits;

	public CommunicationTemplate(Entity entityName, String entityId, DeliveryMode deliveryMode, TemplateType templateType, int version, String templateValue, String templateName, String locale, TemplateStatus templateStatus, TemplateVariableDetails templateVariableDetails, Map<String, Object> metadata, UUID templateAddedBy, String description) {
		this.templateId = null;
		this.entityName = entityName;
		this.entityId = entityId;
		this.deliveryMode = deliveryMode;
		this.templateType = templateType;
		this.version = version;
		this.templateValue = templateValue;
		this.templateName = templateName;
		this.locale = locale;
		this.templateStatus = templateStatus;
		this.templateVariableDetails = templateVariableDetails;
		this.metadata = metadata;
		this.templateAddedBy = templateAddedBy;
		this.templateAddedAt = null;
		this.description = description;
	}
	public CommunicationTemplate(UUID templateId, Entity entityName, String entityId, DeliveryMode deliveryMode, TemplateType templateType, int version, String templateValue, String templateName, String locale, TemplateStatus templateStatus, TemplateVariableDetails templateVariableDetails, Map<String, Object> metadata, UUID templateAddedBy, int templateAddedAt, String description) {
		this.templateId = templateId;
		this.entityName = entityName;
		this.entityId = entityId;
		this.deliveryMode = deliveryMode;
		this.templateType = templateType;
		this.version = version;
		this.templateValue = templateValue;
		this.templateName = templateName;
		this.locale = locale;
		this.templateStatus = templateStatus;
		this.templateVariableDetails = templateVariableDetails;
		this.metadata = metadata;
		this.templateAddedBy = templateAddedBy;
		this.templateAddedAt = templateAddedAt;
		this.description = description;

	}

	public UUID getTemplateId() {
		return templateId;
	}

	public Entity getEntityName() {
		return entityName;
	}

	public String getEntityId() {
		return entityId;
	}

	public DeliveryMode getDeliveryMode() {
		return deliveryMode;
	}

	public TemplateType getTemplateType() {
		return templateType;
	}

	public int getVersion() {
		return version;
	}

	public String getTemplateValue() {
		return templateValue;
	}

	public String getTemplateName() {
		return templateName;
	}

	public String getLocale() {
		return locale;
	}

	public TemplateStatus getTemplateStatus() {
		return templateStatus;
	}

	public TemplateVariableDetails getTemplateVariableDetails() {
		return templateVariableDetails;
	}

	public Map<String, Object> getMetadata() {
		return metadata;
	}

	public UUID getTemplateAddedBy() {
		return templateAddedBy;
	}

	public Integer getTemplateAddedAt() {
		return templateAddedAt;
	}

	public String getDescription() {
		return description;
	}

	public Integer getCredits() {
		return credits;
	}

	public void setCredits(Integer credits) {
		this.credits = credits;
	}


	@Override
	public String toString() {
		return "CommunicationTemplate{" +
				"templateId=" + templateId +
				", entityName=" + entityName +
				", entityId='" + entityId + '\'' +
				", deliveryMode=" + deliveryMode +
				", templateType=" + templateType +
				", version=" + version +
				", templateValue='" + templateValue + '\'' +
				", templateName='" + templateName + '\'' +
				", locale='" + locale + '\'' +
				", templateStatus=" + templateStatus +
				", templateVariableDetails=" + templateVariableDetails +
				", metadata=" + metadata +
				", templateAddedBy=" + templateAddedBy +
				", templateAddedAt=" + templateAddedAt +
				", description='" + description + '\'' +
				", credits=" + credits +
				'}';
	}

}
