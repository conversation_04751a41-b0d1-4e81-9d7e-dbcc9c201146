package com.embrate.cloud.core.api.service.communication.templates;

import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.configurations.Entity;

import java.util.Map;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class CommunicationTemplatePayload {

	private Entity entityName;
	private String entityId;
	private DeliveryMode deliveryMode;
	private TemplateType templateType;
	private String templateValue;
	private String templateName;
	private String locale;
	private TemplateVariableDetails templateVariableDetails;
	private Map<String, Object> metadata;
	private String description;

	public Entity getEntityName() {
		return entityName;
	}

	public void setEntityName(Entity entityName) {
		this.entityName = entityName;
	}

	public String getEntityId() {
		return entityId;
	}

	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	public DeliveryMode getDeliveryMode() {
		return deliveryMode;
	}

	public void setDeliveryMode(DeliveryMode deliveryMode) {
		this.deliveryMode = deliveryMode;
	}

	public TemplateType getTemplateType() {
		return templateType;
	}

	public void setTemplateType(TemplateType templateType) {
		this.templateType = templateType;
	}

	public String getTemplateValue() {
		return templateValue;
	}

	public void setTemplateValue(String templateValue) {
		this.templateValue = templateValue;
	}

	public String getTemplateName() {
		return templateName;
	}

	public void setTemplateName(String templateName) {
		this.templateName = templateName;
	}

	public String getLocale() {
		return locale;
	}

	public void setLocale(String locale) {
		this.locale = locale;
	}

	public TemplateVariableDetails getTemplateVariableDetails() {
		return templateVariableDetails;
	}

	public void setTemplateVariableDetails(TemplateVariableDetails templateVariableDetails) {
		this.templateVariableDetails = templateVariableDetails;
	}

	public Map<String, Object> getMetadata() {
		return metadata;
	}

	public void setMetadata(Map<String, Object> metadata) {
		this.metadata = metadata;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Override
	public String toString() {
		return "CommunicationTemplatePayload{" +
				"entityName=" + entityName +
				", entityId='" + entityId + '\'' +
				", deliveryMode=" + deliveryMode +
				", templateType=" + templateType +
				", templateValue='" + templateValue + '\'' +
				", templateName='" + templateName + '\'' +
				", locale='" + locale + '\'' +
				", templateVariableDetails=" + templateVariableDetails +
				", metadata=" + metadata +
				", description='" + description + '\'' +
				'}';
	}
}
