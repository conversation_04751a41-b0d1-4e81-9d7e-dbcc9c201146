package com.embrate.cloud.core.api.service.communication.templates;

import java.util.List;

/**
 * <AUTHOR>
 */
public class TemplateVariableDetails {

    private List<TemplateVariable> templateVariableList;

    public TemplateVariableDetails(){

    }
    public TemplateVariableDetails(List<TemplateVariable> templateVariableList) {
        this.templateVariableList = templateVariableList;
    }

    public List<TemplateVariable> getTemplateVariableList() {
        return templateVariableList;
    }

    public void setTemplateVariableList(List<TemplateVariable> templateVariableList) {
        this.templateVariableList = templateVariableList;
    }

    @Override
    public String toString() {
        return "TemplateVariableDetails{" +
                "templateVariableList=" + templateVariableList +
                '}';
    }
}
