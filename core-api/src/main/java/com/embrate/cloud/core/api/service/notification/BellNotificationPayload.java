/**
 * 
 */
package com.embrate.cloud.core.api.service.notification;

import java.util.*;

import com.lernen.cloud.core.api.user.User;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 *
 */
public class BellNotificationPayload {
	
	private final int instituteId;
	
	private final List<User> users;
	
	private final String title;
	
	private final String body;
	
	private final NotificationEntity entityName;
	
	private final UUID entityId;
	
	private final Map<String, String> metaData;

	/**
	 * @param instituteId
	 * @param users
	 * @param title
	 * @param body
	 * @param entityName
	 * @param entityId
	 * @param metaData
	 */
	public BellNotificationPayload(int instituteId, List<User> users, String title, String body,
                                   NotificationEntity entityName, UUID entityId, Map<String, String> metaData) {
		this.instituteId = instituteId;
		this.users = users;
		this.title = title;
		this.body = body;
		this.entityName = entityName;
		this.entityId = entityId;
		this.metaData = metaData;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @return the users
	 */
	public List<User> getUsers() {
		return users;
	}

	/**
	 * @return the title
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * @return the body
	 */
	public String getBody() {
		return body;
	}

	/**
	 * @return the entityName
	 */
	public NotificationEntity getEntityName() {
		return entityName;
	}

	/**
	 * @return the entityId
	 */
	public UUID getEntityId() {
		return entityId;
	}

	/**
	 * @return the metaData
	 */
	public Map<String, String> getMetaData() {
		return metaData;
	}

	@Override
	public String toString() {
		return "BellNotificationPayload [instituteId=" + instituteId + ", users=" + users + ", title=" + title
				+ ", body=" + body + ", entityName=" + entityName + ", entityId=" + entityId + ", metaData=" + metaData
				+ "]";
	}
}
