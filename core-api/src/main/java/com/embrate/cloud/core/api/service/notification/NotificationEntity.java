/**
 * 
 */
package com.embrate.cloud.core.api.service.notification;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
public enum NotificationEntity {
	
	ONLINE_LECTURE, HOMEWORK, ONLINE_LECTURE_DISCUSSION, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_DISCUSSION,
	NOTI<PERSON>, <PERSON><PERSON><PERSON>H_CARD, ATTENDANCE, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_MARKING, CUSTOM, TIMET<PERSON>LE_PUBLISHED,
	GATE_PASS, GATE_PASS_CANCELLED, STUDENT_TRANSPORT_TRACKING,
	STUDENT_DIARY_REMARK, FEE_PAYMENT, FEE_PAYMENT_REMINDER, BIRTHDAY, TRA<PERSON>PORT_CUSTOM,
	ADMIN_FEE_PAYMENT, ADMIN_HOMEWORK, ADMIN_NOTICE, STUDENT_LEAVE_REVIEWED, STAFF_LEAVE_REVIEWED, STAFF_ATTENDANCE,
	STUDENT_COMPLAIN_RESPONSE, ADMIN_STAFF_ATTENDANCE, STAFF_DIARY_REMARK, STUDENT_REPORT_CARD_PUBLISHED, STUDENT_ADD_COMPLAIN, STUDENT_ADD_APPOINTMENT,
	STUDENT_APPOINTMENT_STATUS_UPDATE, ADMIN_APPOINTMENT_STATUS_UPDATE, STUDENT_LEAVE_APPLICATION, STAFF_LEAVE_APPLICATION, ADD_VISITOR, VISITOR_APPOINTMENT_STATUS_UPDATE,
	VEHICLE_DOCUMENT_RENEWAL_REMINDER, STAFF_ATTENDANCE_STATUS, ADMIN_STAFF_ATTENDANCE_STATUS;

	public static NotificationEntity getNotificationEntity(
			String notificationEntity) {
		if (StringUtils.isBlank(notificationEntity)) {
			return null;
		}
		for (NotificationEntity notificationEntityEnum : NotificationEntity
				.values()) {
			if (notificationEntityEnum.name()
					.equalsIgnoreCase(notificationEntity)) {
				return notificationEntityEnum;
			}
		}
		return null;
	}
}
