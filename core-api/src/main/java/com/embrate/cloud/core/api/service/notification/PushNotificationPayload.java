package com.embrate.cloud.core.api.service.notification;

import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class PushNotificationPayload {

	private final String title;

	private final String body;

	private final String imageURL;

	private final String destinationDeviceAddress;

	private final Map<String, String> payload;

	public PushNotificationPayload(String title, String body, String imageURL,
			String destinationDeviceAddress, Map<String, String> payload) {
		this.title = title;
		this.body = body;
		this.imageURL = imageURL;
		this.destinationDeviceAddress = destinationDeviceAddress;
		this.payload = payload;
	}

	public String getTitle() {
		return title;
	}

	public String getBody() {
		return body;
	}

	public String getImageURL() {
		return imageURL;
	}

	public String getDestinationDeviceAddress() {
		return destinationDeviceAddress;
	}

	public Map<String, String> getPayload() {
		return payload;
	}

	@Override
	public String toString() {
		return "PushNotificationPayload [title=" + title + ", body=" + body
				+ ", imageURL=" + imageURL + ", destinationDeviceAddress="
				+ destinationDeviceAddress + ", payload=" + payload + "]";
	}

}
