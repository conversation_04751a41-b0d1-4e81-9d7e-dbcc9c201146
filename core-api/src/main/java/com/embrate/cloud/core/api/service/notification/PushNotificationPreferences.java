package com.embrate.cloud.core.api.service.notification;

import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 * @created_at 25/08/23 : 17:18
 **/
public class PushNotificationPreferences {

    public static final String DUE_FEE_PUSH_NOTIFICATION_TEMPLATE_ID = "due_fee_reminder_push_notification_template_id";
    public static final String BIRTHDAY_MOBILE_NOTIFICATION_ENABLED = "birthday_mobile_notification_enabled";
    public static final String ADMIN_FEE_PAYMENT_NOTIFICATION_USER_NAMES = "admin_fee_payment_notification_user_names";
    public static final String ADMIN_HOMEWORK_NOTIFICATION_USER_NAMES = "admin_homework_notification_user_names";
    public static final String ADMIN_NOTICE_NOTIFICATION_USER_NAMES = "admin_notice_notification_user_names";
    public static final String STAFF_ATTENDANCE_MOBILE_NOTIFICATION_ENABLED = "staff_attendance_mobile_notification_enabled";
    public static final String ADMIN_STAFF_ATTENDANCE_NOTIFICATION_USER_NAMES = "admin_staff_attendance_notification_user_names";

    private UUID dueFeeReminderPushNotificationTemplateId;
    private boolean birthdayMobileNotificationEnabled;
    private boolean staffAttendanceMobileNotificationEnabled;
    private Set<String> adminFeePaymentNotificationUserNames;
    private Set<String> adminHomeworkNotificationUserNames;
    private Set<String> adminNoticeNotificationUserNames;
    private Set<String> adminStaffAttendanceNotificationUserNames;

    public PushNotificationPreferences() {
    }

    public static String getConfigType() {
        return "push_notification_preferences";
    }

    public UUID getDueFeeReminderPushNotificationTemplateId() {
        return dueFeeReminderPushNotificationTemplateId;
    }

    public void setDueFeeReminderPushNotificationTemplateId(UUID dueFeeReminderPushNotificationTemplateId) {
        this.dueFeeReminderPushNotificationTemplateId = dueFeeReminderPushNotificationTemplateId;
    }

    public boolean isBirthdayMobileNotificationEnabled() {
        return birthdayMobileNotificationEnabled;
    }

    public void setBirthdayMobileNotificationEnabled(boolean birthdayMobileNotificationEnabled) {
        this.birthdayMobileNotificationEnabled = birthdayMobileNotificationEnabled;
    }

    public boolean isStaffAttendanceMobileNotificationEnabled() {
        return staffAttendanceMobileNotificationEnabled;
    }

    public void setStaffAttendanceMobileNotificationEnabled(boolean staffAttendanceMobileNotificationEnabled) {
        this.staffAttendanceMobileNotificationEnabled = staffAttendanceMobileNotificationEnabled;
    }

    public Set<String> getAdminFeePaymentNotificationUserNames() {
        return adminFeePaymentNotificationUserNames;
    }

    public void setAdminFeePaymentNotificationUserNames(Set<String> adminFeePaymentNotificationUserNames) {
        this.adminFeePaymentNotificationUserNames = adminFeePaymentNotificationUserNames;
    }

    public Set<String> getAdminHomeworkNotificationUserNames() {
        return adminHomeworkNotificationUserNames;
    }

    public void setAdminHomeworkNotificationUserNames(Set<String> adminHomeworkNotificationUserNames) {
        this.adminHomeworkNotificationUserNames = adminHomeworkNotificationUserNames;
    }

    public Set<String> getAdminStaffAttendanceNotificationUserNames() {
        return adminStaffAttendanceNotificationUserNames;
    }

    public void setAdminStaffAttendanceNotificationUserNames(Set<String> adminStaffAttendanceNotificationUserNames) {
        this.adminStaffAttendanceNotificationUserNames = adminStaffAttendanceNotificationUserNames;
    }

    public Set<String> getAdminNoticeNotificationUserNames() {
        return adminNoticeNotificationUserNames;
    }

    public void setAdminNoticeNotificationUserNames(Set<String> adminNoticeNotificationUserNames) {
        this.adminNoticeNotificationUserNames = adminNoticeNotificationUserNames;
    }

    @Override
    public String toString() {
        return "PushNotificationPreferences{" +
                "dueFeeReminderPushNotificationTemplateId=" + dueFeeReminderPushNotificationTemplateId +
                ", birthdayMobileNotificationEnabled=" + birthdayMobileNotificationEnabled +
                ", staffAttendanceMobileNotificationEnabled=" + staffAttendanceMobileNotificationEnabled +
                ", adminFeePaymentNotificationUserNames=" + adminFeePaymentNotificationUserNames +
                ", adminHomeworkNotificationUserNames=" + adminHomeworkNotificationUserNames +
                ", adminNoticeNotificationUserNames=" + adminNoticeNotificationUserNames +
                ", adminStaffAttendanceNotificationUserNames=" + adminStaffAttendanceNotificationUserNames +
                '}';
    }
}
