package com.embrate.cloud.core.api.service.notification;

/**
 * 
 * <AUTHOR>
 *
 */
public class PushNotificationResponse {

	private final boolean success;

	private final String messageId;

	private final String errorMessage;

	public PushNotificationResponse(boolean success, String messageId,
			String errorMessage) {
		this.success = success;
		this.messageId = messageId;
		this.errorMessage = errorMessage;
	}

	public boolean isSuccess() {
		return success;
	}

	public String getMessageId() {
		return messageId;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public static PushNotificationResponse forSuccess(String messageId) {
		return new PushNotificationResponse(true, messageId, null);
	}

	public static PushNotificationResponse forFailure(String errorMessage) {
		return new PushNotificationResponse(false, null, errorMessage);
	}

	@Override
	public String toString() {
		return "PushNotificationResponse [success=" + success + ", messageId="
				+ messageId + ", errorMessage=" + errorMessage + "]";
	}

}
