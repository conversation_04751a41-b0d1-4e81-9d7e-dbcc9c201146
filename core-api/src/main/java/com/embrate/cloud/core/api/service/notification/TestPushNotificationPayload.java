package com.embrate.cloud.core.api.service.notification;

import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class TestPushNotificationPayload {

	private String title;

	private String body;

	private String imageURL;

	private String destinationDeviceAddress;

	private Map<String, String> payload;

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getBody() {
		return body;
	}

	public void setBody(String body) {
		this.body = body;
	}

	public String getImageURL() {
		return imageURL;
	}

	public void setImageURL(String imageURL) {
		this.imageURL = imageURL;
	}

	public String getDestinationDeviceAddress() {
		return destinationDeviceAddress;
	}

	public void setDestinationDeviceAddress(String destinationDeviceAddress) {
		this.destinationDeviceAddress = destinationDeviceAddress;
	}

	public Map<String, String> getPayload() {
		return payload;
	}

	public void setPayload(Map<String, String> payload) {
		this.payload = payload;
	}

}
