package com.embrate.cloud.core.api.service.payment.gateway;

import com.lernen.cloud.core.api.fees.payment.FeePaymentInvoiceSummary;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class PGInitiateTransactionPayload {

    private UUID userId;

    private PGTransactionType transactionType;

    private double paymentGatewayAmount;

    private double walletAmount;

    private double instantDiscountAmount;

    private double fineAmount;

    private PaymentGatewayCurrency currency;

    private PaymentGatewayStage paymentGatewayStage;

    private String description;

    private PGStudentFeePaymentMetadata studentFeePaymentMetadata;

    private String callbackUrl;

    public PGInitiateTransactionPayload() {
    }

    public PGInitiateTransactionPayload(UUID userId, PGTransactionType transactionType, double paymentGatewayAmount,
                                        double walletAmount, double instantDiscountAmount, double fineAmount,
                                        PaymentGatewayCurrency currency, PaymentGatewayStage paymentGatewayStage,
                                        String description, PGStudentFeePaymentMetadata studentFeePaymentMetadata,
                                        String callbackUrl) {
        this.userId = userId;
        this.transactionType = transactionType;
        this.paymentGatewayAmount = paymentGatewayAmount;
        this.walletAmount = walletAmount;
        this.instantDiscountAmount = instantDiscountAmount;
        this.fineAmount = fineAmount;
        this.currency = currency;
        this.paymentGatewayStage = paymentGatewayStage;
        this.description = description;
        this.studentFeePaymentMetadata = studentFeePaymentMetadata;
        this.callbackUrl = callbackUrl;
    }

    public UUID getUserId() {
        return userId;
    }

    public void setUserId(UUID userId) {
        this.userId = userId;
    }

    public PGTransactionType getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(PGTransactionType transactionType) {
        this.transactionType = transactionType;
    }

    public double getPaymentGatewayAmount() {
        return paymentGatewayAmount;
    }

    public void setPaymentGatewayAmount(double paymentGatewayAmount) {
        this.paymentGatewayAmount = paymentGatewayAmount;
    }

    public double getWalletAmount() {
        return walletAmount;
    }

    public void setWalletAmount(double walletAmount) {
        this.walletAmount = walletAmount;
    }

    public double getInstantDiscountAmount() {
        return instantDiscountAmount;
    }

    public void setInstantDiscountAmount(double instantDiscountAmount) {
        this.instantDiscountAmount = instantDiscountAmount;
    }

    public double getFineAmount() {
        return fineAmount;
    }

    public void setFineAmount(double fineAmount) {
        this.fineAmount = fineAmount;
    }

    public PaymentGatewayStage getPaymentGatewayStage() {
        return paymentGatewayStage;
    }

    public void setPaymentGatewayStage(PaymentGatewayStage paymentGatewayStage) {
        this.paymentGatewayStage = paymentGatewayStage;
    }

    public PaymentGatewayCurrency getCurrency() {
        return currency;
    }

    public void setCurrency(PaymentGatewayCurrency currency) {
        this.currency = currency;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public PGStudentFeePaymentMetadata getStudentFeePaymentMetadata() {
        return studentFeePaymentMetadata;
    }

    public void setStudentFeePaymentMetadata(PGStudentFeePaymentMetadata studentFeePaymentMetadata) {
        this.studentFeePaymentMetadata = studentFeePaymentMetadata;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    @Override
    public String toString() {
        return "PGInitiateTransactionPayload{" +
                "userId=" + userId +
                ", transactionType=" + transactionType +
                ", paymentGatewayAmount=" + paymentGatewayAmount +
                ", walletAmount=" + walletAmount +
                ", instantDiscountAmount=" + instantDiscountAmount +
                ", fineAmount=" + fineAmount +
                ", currency=" + currency +
                ", paymentGatewayStage=" + paymentGatewayStage +
                ", description='" + description + '\'' +
                ", studentFeePaymentMetadata=" + studentFeePaymentMetadata +
                ", callbackUrl='" + callbackUrl + '\'' +
                '}';
    }
}
