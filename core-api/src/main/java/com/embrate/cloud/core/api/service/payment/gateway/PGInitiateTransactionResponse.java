package com.embrate.cloud.core.api.service.payment.gateway;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class PGInitiateTransactionResponse {

    private final boolean success;

    private final int errorCode;

    private final String errorDescription;

    private final PaymentGatewayServiceProvider serviceProvider;

    private final String clientId;

    private final UUID transactionId;

    private final String transactionToken;

    private final double paymentGatewayAmount;

    private final Double requestedWalletAmount;

    private final Double currentWalletAmount;

    private final String name;

    private final String email;

    private final String mobileNumber;

    private final String notifyUrl;

    private final String paymentModes;

    private final String description;

    private final String instituteName;

    private final Map<String, String> metadata;

    public PGInitiateTransactionResponse(boolean success, int errorCode, String errorDescription, PaymentGatewayServiceProvider serviceProvider, String clientId, UUID transactionId, String transactionToken, double paymentGatewayAmount, Double requestedWalletAmount, Double currentWalletAmount, String name, String email, String mobileNumber, String notifyUrl, String paymentModes, String description, String instituteName, Map<String, String> metadata) {
        this.success = success;
        this.errorCode = errorCode;
        this.errorDescription = errorDescription;
        this.serviceProvider = serviceProvider;
        this.clientId = clientId;
        this.transactionId = transactionId;
        this.transactionToken = transactionToken;
        this.paymentGatewayAmount = paymentGatewayAmount;
        this.requestedWalletAmount = requestedWalletAmount;
        this.currentWalletAmount = currentWalletAmount;
        this.name = name;
        this.email = email;
        this.mobileNumber = mobileNumber;
        this.notifyUrl = notifyUrl;
        this.paymentModes = paymentModes;
        this.description = description;
        this.instituteName = instituteName;
        this.metadata = metadata;
    }

    public boolean isSuccess() {
        return success;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public String getErrorDescription() {
        return errorDescription;
    }

    public PaymentGatewayServiceProvider getServiceProvider() {
        return serviceProvider;
    }

    public String getClientId() {
        return clientId;
    }

    public UUID getTransactionId() {
        return transactionId;
    }

    public String getTransactionToken() {
        return transactionToken;
    }

    public double getPaymentGatewayAmount() {
        return paymentGatewayAmount;
    }

    public Double getRequestedWalletAmount() {
        return requestedWalletAmount;
    }

    public Double getCurrentWalletAmount() {
        return currentWalletAmount;
    }

    public String getName() {
        return name;
    }

    public String getEmail() {
        return email;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public String getPaymentModes() {
        return paymentModes;
    }

    public String getDescription() {
        return description;
    }

    public String getInstituteName() {
        return instituteName;
    }

    public Map<String, String> getMetadata() {
        return metadata;
    }

    public static PGInitiateTransactionResponse forFailure(int errorCode, String errorDescription){
        return new PGInitiateTransactionResponse(false, errorCode, errorDescription, null, null, null, null, 0d, null, null, null, null, null, null, null, null, null, null);
    }

    public static PGInitiateTransactionResponse forFailure(int errorCode, String errorDescription, Double requestedWalletAmount, Double currentWalletAmount, double paymentGatewayAmount){
        return new PGInitiateTransactionResponse(false, errorCode, errorDescription, null, null, null, null, paymentGatewayAmount, requestedWalletAmount, currentWalletAmount, null, null, null, null, null, null, null, null);
    }

    @Override
    public String toString() {
        return "PGInitiateTransactionResponse{" +
                "success=" + success +
                ", errorCode=" + errorCode +
                ", errorDescription='" + errorDescription + '\'' +
                ", serviceProvider=" + serviceProvider +
                ", clientId='" + clientId + '\'' +
                ", transactionId=" + transactionId +
                ", transactionToken='" + transactionToken + '\'' +
                ", paymentGatewayAmount=" + paymentGatewayAmount +
                ", requestedWalletAmount=" + requestedWalletAmount +
                ", currentWalletAmount=" + currentWalletAmount +
                ", name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", mobileNumber='" + mobileNumber + '\'' +
                ", notifyUrl='" + notifyUrl + '\'' +
                ", paymentModes='" + paymentModes + '\'' +
                ", description='" + description + '\'' +
                ", instituteName='" + instituteName + '\'' +
                ", metadata=" + metadata +
                '}';
    }
}
