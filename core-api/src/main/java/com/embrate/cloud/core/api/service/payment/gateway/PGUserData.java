package com.embrate.cloud.core.api.service.payment.gateway;

/**
 * <AUTHOR>
 */

public class PGUserData {

    private final String userId;

    private final String userName;

    private final String userEmail;

    private final String userPhone;

    public PGUserData(String userId, String userName, String userEmail, String userPhone) {
        this.userId = userId;
        this.userName = userName;
        this.userEmail = userEmail;
        this.userPhone = userPhone;
    }

    public String getUserId() {
        return userId;
    }

    public String getUserName() {
        return userName;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public String getUserPhone() {
        return userPhone;
    }

    @Override
    public String toString() {
        return "PGUserData{" +
                "userId='" + userId + '\'' +
                ", userName='" + userName + '\'' +
                ", userEmail='" + userEmail + '\'' +
                ", userPhone='" + userPhone + '\'' +
                '}';
    }
}
