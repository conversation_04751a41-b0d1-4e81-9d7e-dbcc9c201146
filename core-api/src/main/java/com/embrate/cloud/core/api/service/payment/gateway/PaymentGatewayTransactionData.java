package com.embrate.cloud.core.api.service.payment.gateway;

import com.google.gson.Gson;
import com.lernen.cloud.core.api.user.UserType;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class PaymentGatewayTransactionData {

    public static final String STUDENT_FEE_PAYMENT_METADATA_KEY = "student_fee_payment_metadata";

    private final int instituteId;

    private final PaymentGatewayServiceProvider serviceProvider;

    private final UUID userId;

    private final UserType userType;

    private final UUID transactionId;

    private final UUID merchantId;

    private final PGTransactionType transactionType;

    private final double paymentGatewayAmount;

    private final double walletAmount;

    private final double instantDiscountAmount;

    private final double fineAmount;

    private final PaymentGatewayCurrency currency;

    private final PaymentGatewayStage paymentGatewayStage;

    private final String transactionToken;

    private final PaymentGatewayTransactionStatus transactionStatus;

    private final Map<String, Object> metadata;

    private final long addedAt;

    private final long updatedAt;

    private final String description;

    private final Integer errorCode;

    private final String errorReason;

    private final boolean lockAcquired;

    public PaymentGatewayTransactionData(int instituteId, PaymentGatewayServiceProvider serviceProvider,
                                         UUID userId, UserType userType, UUID transactionId, UUID merchantId,
                                         PGTransactionType transactionType, double paymentGatewayAmount, double walletAmount,
                                         double instantDiscountAmount, double fineAmount,
                                         PaymentGatewayCurrency currency, PaymentGatewayStage paymentGatewayStage,
                                         String transactionToken, PaymentGatewayTransactionStatus transactionStatus,
                                         Map<String, Object> metadata, long addedAt, long updatedAt, String description,
                                         Integer errorCode, String errorReason, boolean lockAcquired) {
        this.instituteId = instituteId;
        this.serviceProvider = serviceProvider;
        this.userId = userId;
        this.userType = userType;
        this.transactionId = transactionId;
        this.merchantId = merchantId;
        this.transactionType = transactionType;
        this.paymentGatewayAmount = paymentGatewayAmount;
        this.walletAmount = walletAmount;
        this.instantDiscountAmount = instantDiscountAmount;
        this.fineAmount = fineAmount;
        this.currency = currency;
        this.paymentGatewayStage = paymentGatewayStage;
        this.transactionToken = transactionToken;
        this.transactionStatus = transactionStatus;
        this.metadata = metadata;
        this.addedAt = addedAt;
        this.updatedAt = updatedAt;
        this.description = description;
        this.errorCode = errorCode;
        this.errorReason = errorReason;
        this.lockAcquired = lockAcquired;
    }

    public PaymentGatewayTransactionData(int instituteId, PaymentGatewayServiceProvider serviceProvider, UUID userId, UserType userType,
                                         UUID transactionId, UUID merchantId, PGTransactionType transactionType, double paymentGatewayAmount,
                                         double walletAmount,  double instantDiscountAmount, double fineAmount, PaymentGatewayCurrency currency, PaymentGatewayStage paymentGatewayStage,
                                         String transactionToken, PaymentGatewayTransactionStatus transactionStatus,
                                         Map<String, Object> metadata, String description) {
        this.instituteId = instituteId;
        this.serviceProvider = serviceProvider;
        this.userId = userId;
        this.userType = userType;
        this.transactionId = transactionId;
        this.merchantId = merchantId;
        this.transactionType = transactionType;
        this.paymentGatewayAmount = paymentGatewayAmount;
        this.walletAmount = walletAmount;
        this.instantDiscountAmount = instantDiscountAmount;
        this.fineAmount = fineAmount;
        this.currency = currency;
        this.paymentGatewayStage = paymentGatewayStage;
        this.transactionToken = transactionToken;
        this.transactionStatus = transactionStatus;
        this.metadata = metadata;
        this.description = description;
        this.addedAt = 0;
        this.updatedAt = 0;
        this.errorCode = null;
        this.errorReason = null;
        this.lockAcquired = false;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public PaymentGatewayServiceProvider getServiceProvider() {
        return serviceProvider;
    }

    public UUID getUserId() {
        return userId;
    }

    public UserType getUserType() {
        return userType;
    }

    public UUID getTransactionId() {
        return transactionId;
    }

    public UUID getMerchantId() {
        return merchantId;
    }

    public PGTransactionType getTransactionType() {
        return transactionType;
    }

    public double getPaymentGatewayAmount() {
        return paymentGatewayAmount;
    }

    public double getWalletAmount() {
        return walletAmount;
    }

    public double getInstantDiscountAmount() {
        return instantDiscountAmount;
    }

    public double getFineAmount() {
        return fineAmount;
    }

    public double getOriginalFeesAmount(){
        return paymentGatewayAmount + walletAmount + instantDiscountAmount - fineAmount;
    }
    public PaymentGatewayCurrency getCurrency() {
        return currency;
    }

    public PaymentGatewayStage getPaymentGatewayStage() {
        return paymentGatewayStage;
    }

    public String getTransactionToken() {
        return transactionToken;
    }

    public PaymentGatewayTransactionStatus getTransactionStatus() {
        return transactionStatus;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public long getAddedAt() {
        return addedAt;
    }

    public long getUpdatedAt() {
        return updatedAt;
    }

    public String getDescription() {
        return description;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public String getErrorReason() {
        return errorReason;
    }

    public boolean isLockAcquired() {
        return lockAcquired;
    }

    public static PGStudentFeePaymentMetadata getStudentFeePaymentMetadata(Map<String, Object> metadata, Gson gson){
        if(metadata == null || metadata.get(STUDENT_FEE_PAYMENT_METADATA_KEY) == null){
            return null;
        }
        return gson.fromJson(String.valueOf(metadata.get(STUDENT_FEE_PAYMENT_METADATA_KEY)), PGStudentFeePaymentMetadata.class);
    }


    @Override
    public String toString() {
        return "PaymentGatewayTransactionData{" +
                "instituteId=" + instituteId +
                ", serviceProvider=" + serviceProvider +
                ", userId=" + userId +
                ", userType=" + userType +
                ", transactionId=" + transactionId +
                ", merchantId=" + merchantId +
                ", transactionType=" + transactionType +
                ", paymentGatewayAmount=" + paymentGatewayAmount +
                ", walletAmount=" + walletAmount +
                ", instantDiscountAmount=" + instantDiscountAmount +
                ", fineAmount=" + fineAmount +
                ", currency=" + currency +
                ", paymentGatewayStage=" + paymentGatewayStage +
                ", transactionToken='" + transactionToken + '\'' +
                ", transactionStatus=" + transactionStatus +
                ", metadata=" + metadata +
                ", addedAt=" + addedAt +
                ", updatedAt=" + updatedAt +
                ", description='" + description + '\'' +
                ", errorCode=" + errorCode +
                ", errorReason='" + errorReason + '\'' +
                ", lockAcquired=" + lockAcquired +
                '}';
    }
}
