package com.embrate.cloud.core.api.service.payment.gateway;

import com.embrate.cloud.core.api.payment.PaymentApplicableDiscount;
import com.embrate.cloud.core.api.payment.PaymentApplicableFine;

/**
 * <AUTHOR>
 */

public class UserPaymentGatewayMetadata {

    private final Double walletAmount;

    private final boolean paymentGatewayCustomerCharges;

    private final String paymentGatewayChargesDisclaimer;

    private final PaymentApplicableDiscount paymentApplicableDiscount;

    private final PaymentApplicableFine paymentApplicableFine;

    private final boolean restrictFutureSessionPayment;

    private final boolean restrictStudentUseWalletFeesPayment;

    public UserPaymentGatewayMetadata(Double walletAmount, boolean paymentGatewayCustomerCharges, String paymentGatewayChargesDisclaimer, PaymentApplicableDiscount paymentApplicableDiscount, PaymentApplicableFine paymentApplicableFine,
                                      boolean restrictFutureSessionPayment, boolean restrictStudentUseWalletFeesPayment) {
        this.walletAmount = walletAmount;
        this.paymentGatewayCustomerCharges = paymentGatewayCustomerCharges;
        this.paymentGatewayChargesDisclaimer = paymentGatewayChargesDisclaimer;
        this.paymentApplicableDiscount = paymentApplicableDiscount;
        this.paymentApplicableFine = paymentApplicableFine;
        this.restrictFutureSessionPayment = restrictFutureSessionPayment;
        this.restrictStudentUseWalletFeesPayment = restrictStudentUseWalletFeesPayment;
    }

    public Double getWalletAmount() {
        return walletAmount;
    }

    public boolean isPaymentGatewayCustomerCharges() {
        return paymentGatewayCustomerCharges;
    }

    public String getPaymentGatewayChargesDisclaimer() {
        return paymentGatewayChargesDisclaimer;
    }

    public PaymentApplicableDiscount getPaymentApplicableDiscount() {
        return paymentApplicableDiscount;
    }

    public PaymentApplicableFine getPaymentApplicableFine() {
        return paymentApplicableFine;
    }

    public boolean isRestrictFutureSessionPayment() {
        return restrictFutureSessionPayment;
    }

    public boolean isRestrictStudentUseWalletFeesPayment() {
        return restrictStudentUseWalletFeesPayment;
    }

    @Override
    public String toString() {
        return "UserPaymentGatewayMetadata{" +
                "walletAmount=" + walletAmount +
                ", paymentGatewayCustomerCharges=" + paymentGatewayCustomerCharges +
                ", paymentGatewayChargesDisclaimer='" + paymentGatewayChargesDisclaimer + '\'' +
                ", paymentApplicableDiscount=" + paymentApplicableDiscount +
                ", paymentApplicableFine=" + paymentApplicableFine +
                ", restrictFutureSessionPayment=" + restrictFutureSessionPayment +
                ", restrictStudentUseWalletFeesPayment=" + restrictStudentUseWalletFeesPayment +
                '}';
    }
}
