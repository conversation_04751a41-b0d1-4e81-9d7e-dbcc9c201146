package com.embrate.cloud.core.api.service.payment.gateway.atom;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AtomPaymentWebhookResponse {

    @JsonProperty("MerchantID")
    private String merchantID;

    @JsonProperty("MerchantTxnID")
    private String merchantTxnID;

    @JsonProperty("AMT")
    private String amt;

    @JsonProperty("VERIFIED")
    private String verified;

    @JsonProperty("BID")
    private String bid;

    @JsonProperty("BankName")
    private String bankName;

    @JsonProperty("AtomTxnId")
    private String atomTxnId;

    @JsonProperty("Discriminator")
    private String discriminator;

    @JsonProperty("Surcharge")
    private String surcharge;

    @JsonProperty("CardNumber")
    private String cardNumber;

    @JsonProperty("TxnDate")
    private String txnDate;

    @JsonProperty("CustomerAccNo")
    private String customerAccNo;

    @JsonProperty("Clientcode")
    private String clientcode;

    public AtomPaymentWebhookResponse() {

    }
    public AtomPaymentWebhookResponse(String merchantID, String merchantTxnID, String amt, String verified, String bid, String bankName, String atomTxnId, String discriminator, String surcharge, String cardNumber, String txnDate, String customerAccNo, String clientcode) {
        this.merchantID = merchantID;
        this.merchantTxnID = merchantTxnID;
        this.amt = amt;
        this.verified = verified;
        this.bid = bid;
        this.bankName = bankName;
        this.atomTxnId = atomTxnId;
        this.discriminator = discriminator;
        this.surcharge = surcharge;
        this.cardNumber = cardNumber;
        this.txnDate = txnDate;
        this.customerAccNo = customerAccNo;
        this.clientcode = clientcode;
    }

    public String getMerchantID() {
        return merchantID;
    }

    public void setMerchantID(String merchantID) {
        this.merchantID = merchantID;
    }

    public String getMerchantTxnID() {
        return merchantTxnID;
    }

    public void setMerchantTxnID(String merchantTxnID) {
        this.merchantTxnID = merchantTxnID;
    }

    public String getAmt() {
        return amt;
    }

    public void setAmt(String amt) {
        this.amt = amt;
    }

    public String getVerified() {
        return verified;
    }

    public void setVerified(String verified) {
        this.verified = verified;
    }

    public String getBid() {
        return bid;
    }

    public void setBid(String bid) {
        this.bid = bid;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getAtomTxnId() {
        return atomTxnId;
    }

    public void setAtomTxnId(String atomTxnId) {
        this.atomTxnId = atomTxnId;
    }

    public String getDiscriminator() {
        return discriminator;
    }

    public void setDiscriminator(String discriminator) {
        this.discriminator = discriminator;
    }

    public String getSurcharge() {
        return surcharge;
    }

    public void setSurcharge(String surcharge) {
        this.surcharge = surcharge;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getTxnDate() {
        return txnDate;
    }

    public void setTxnDate(String txnDate) {
        this.txnDate = txnDate;
    }

    public String getCustomerAccNo() {
        return customerAccNo;
    }

    public void setCustomerAccNo(String customerAccNo) {
        this.customerAccNo = customerAccNo;
    }

    public String getClientcode() {
        return clientcode;
    }

    public void setClientcode(String clientcode) {
        this.clientcode = clientcode;
    }

    @Override
    public String toString() {
        return "AtomPaymentWebhookResponse{" +
                "merchantID='" + merchantID + '\'' +
                ", merchantTxnID='" + merchantTxnID + '\'' +
                ", amt='" + amt + '\'' +
                ", verified='" + verified + '\'' +
                ", bid='" + bid + '\'' +
                ", bankName='" + bankName + '\'' +
                ", atomTxnId='" + atomTxnId + '\'' +
                ", discriminator='" + discriminator + '\'' +
                ", surcharge='" + surcharge + '\'' +
                ", cardNumber='" + cardNumber + '\'' +
                ", txnDate='" + txnDate + '\'' +
                ", customerAccNo='" + customerAccNo + '\'' +
                ", clientcode='" + clientcode + '\'' +
                '}';
    }
}
