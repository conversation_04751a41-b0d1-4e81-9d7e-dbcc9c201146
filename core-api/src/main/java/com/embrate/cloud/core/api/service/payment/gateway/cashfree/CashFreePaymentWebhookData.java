package com.embrate.cloud.core.api.service.payment.gateway.cashfree;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CashFreePaymentWebhookData {

    @JsonProperty("order")
    private CashFreePaymentWebhookOrder order;

    @JsonProperty("payment")
    private CashFreePaymentWebhookPaymentDetails payment;

    @JsonProperty("customer_details")
    private CashFreePaymentWebhookCustomerDetails customerDetails;


    public CashFreePaymentWebhookOrder getOrder() {
        return order;
    }

    public void setOrder(CashFreePaymentWebhookOrder order) {
        this.order = order;
    }

    public CashFreePaymentWebhookPaymentDetails getPayment() {
        return payment;
    }

    public void setPayment(CashFreePaymentWebhookPaymentDetails payment) {
        this.payment = payment;
    }

    public CashFreePaymentWebhookCustomerDetails getCustomerDetails() {
        return customerDetails;
    }

    public void setCustomerDetails(CashFreePaymentWebhookCustomerDetails customerDetails) {
        this.customerDetails = customerDetails;
    }

    @Override
    public String toString() {
        return "CashFreePaymentWebhookData{" +
                "order=" + order +
                ", payment=" + payment +
                ", customerDetails=" + customerDetails +
                '}';
    }
}
