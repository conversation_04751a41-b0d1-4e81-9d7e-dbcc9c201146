package com.embrate.cloud.core.api.service.payment.gateway.cashfree;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CashFreePaymentWebhookNetBankingMethod {

    @JsonProperty("channel")
    private String channel;

    @JsonProperty("netbanking_bank_code")
    private String netBankingBankCode;

    @JsonProperty("netbanking_bank_name")
    private String netBankingBankName;

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getNetBankingBankCode() {
        return netBankingBankCode;
    }

    public void setNetBankingBankCode(String netBankingBankCode) {
        this.netBankingBankCode = netBankingBankCode;
    }

    public String getNetBankingBankName() {
        return netBankingBankName;
    }

    public void setNetBankingBankName(String netBankingBankName) {
        this.netBankingBankName = netBankingBankName;
    }

    @Override
    public String toString() {
        return "CashFreePaymentWebhookNetBankingMethod{" +
                "channel='" + channel + '\'' +
                ", netBankingBankCode='" + netBankingBankCode + '\'' +
                ", netBankingBankName='" + netBankingBankName + '\'' +
                '}';
    }
}
