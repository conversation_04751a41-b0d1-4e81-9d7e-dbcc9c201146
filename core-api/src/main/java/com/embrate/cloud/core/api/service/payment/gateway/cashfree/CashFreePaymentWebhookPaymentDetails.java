package com.embrate.cloud.core.api.service.payment.gateway.cashfree;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CashFreePaymentWebhookPaymentDetails {

    @JsonProperty("cf_payment_id")
    private String cfPaymentId;

    @JsonProperty("payment_status")
    private String paymentStatus;

    @JsonProperty("payment_amount")
    private Double paymentAmount;

    @JsonProperty("payment_currency")
    private String paymentCurrency;

    @JsonProperty("payment_message")
    private String paymentMessage;

    @JsonProperty("payment_time")
    private String paymentTime;

    @JsonProperty("bank_reference")
    private String bankReference;

    @JsonProperty("auth_id")
    private String authId;

    @JsonProperty("payment_group")
    private String paymentGroup;

    @JsonProperty("payment_method")
    private CashFreePaymentWebhookPaymentMethod paymentMethod;

    public String getCfPaymentId() {
        return cfPaymentId;
    }

    public void setCfPaymentId(String cfPaymentId) {
        this.cfPaymentId = cfPaymentId;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public Double getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(Double paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getPaymentCurrency() {
        return paymentCurrency;
    }

    public void setPaymentCurrency(String paymentCurrency) {
        this.paymentCurrency = paymentCurrency;
    }

    public String getPaymentMessage() {
        return paymentMessage;
    }

    public void setPaymentMessage(String paymentMessage) {
        this.paymentMessage = paymentMessage;
    }

    public String getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentTime(String paymentTime) {
        this.paymentTime = paymentTime;
    }

    public String getBankReference() {
        return bankReference;
    }

    public void setBankReference(String bankReference) {
        this.bankReference = bankReference;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getPaymentGroup() {
        return paymentGroup;
    }

    public void setPaymentGroup(String paymentGroup) {
        this.paymentGroup = paymentGroup;
    }

    public CashFreePaymentWebhookPaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(CashFreePaymentWebhookPaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    @Override
    public String toString() {
        return "CashFreePaymentWebhookPaymentDetails{" +
                "cfPaymentId='" + cfPaymentId + '\'' +
                ", paymentStatus='" + paymentStatus + '\'' +
                ", paymentAmount=" + paymentAmount +
                ", paymentCurrency='" + paymentCurrency + '\'' +
                ", paymentMessage='" + paymentMessage + '\'' +
                ", paymentTime='" + paymentTime + '\'' +
                ", bankReference='" + bankReference + '\'' +
                ", authId='" + authId + '\'' +
                ", paymentGroup='" + paymentGroup + '\'' +
                ", paymentMethod=" + paymentMethod +
                '}';
    }
}

