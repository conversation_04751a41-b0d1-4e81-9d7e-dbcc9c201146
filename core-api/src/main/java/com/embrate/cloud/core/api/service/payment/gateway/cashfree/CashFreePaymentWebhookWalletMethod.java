package com.embrate.cloud.core.api.service.payment.gateway.cashfree;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CashFreePaymentWebhookWalletMethod {

    @JsonProperty("channel")
    private String channel;

    @JsonProperty("upi_id")
    private String upiId;

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getUpiId() {
        return upiId;
    }

    public void setUpiId(String upiId) {
        this.upiId = upiId;
    }

    @Override
    public String toString() {
        return "CashFreePaymentWebhookWalletMethod{" +
                "channel='" + channel + '\'' +
                ", upiId='" + upiId + '\'' +
                '}';
    }
}
