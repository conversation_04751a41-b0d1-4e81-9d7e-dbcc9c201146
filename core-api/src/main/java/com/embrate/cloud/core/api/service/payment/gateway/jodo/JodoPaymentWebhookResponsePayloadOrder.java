package com.embrate.cloud.core.api.service.payment.gateway.jodo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @created_at 22/09/23 : 14:55
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class JodoPaymentWebhookResponsePayloadOrder {

    @JsonProperty("name")
    private String name;

    @JsonProperty("phone")
    private String phone;

    @JsonProperty("identifier")
    private String identifier;

    @JsonProperty("email")
    private String email;

    @JsonProperty("paid_at")
    private String paid_at;

    @JsonProperty("status")
    private String status;

    @JsonProperty("details")
    private List<JodoPaymentWebhookResponsePayloadOrderDetails> jodoPaymentWebhookResponsePayloadOrderDetailsList;

    public JodoPaymentWebhookResponsePayloadOrder(String name, String phone, String identifier, String email, String paid_at, String status, List<JodoPaymentWebhookResponsePayloadOrderDetails> jodoPaymentWebhookResponsePayloadOrderDetailsList) {
        this.name = name;
        this.phone = phone;
        this.identifier = identifier;
        this.email = email;
        this.paid_at = paid_at;
        this.status = status;
        this.jodoPaymentWebhookResponsePayloadOrderDetailsList = jodoPaymentWebhookResponsePayloadOrderDetailsList;
    }

    public JodoPaymentWebhookResponsePayloadOrder() {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPaid_at() {
        return paid_at;
    }

    public void setPaid_at(String paid_at) {
        this.paid_at = paid_at;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<JodoPaymentWebhookResponsePayloadOrderDetails> getJodoPaymentWebhookResponsePayloadOrderDetails() {
        return jodoPaymentWebhookResponsePayloadOrderDetailsList;
    }

    public void setJodoPaymentWebhookResponsePayloadOrderDetails(List<JodoPaymentWebhookResponsePayloadOrderDetails> jodoPaymentWebhookResponsePayloadOrderDetailsList) {
        this.jodoPaymentWebhookResponsePayloadOrderDetailsList = jodoPaymentWebhookResponsePayloadOrderDetailsList;
    }

    @Override
    public String toString() {
        return "JodoPaymentWebhookResponsePayloadOrder{" +
                "name='" + name + '\'' +
                ", phone='" + phone + '\'' +
                ", identifier='" + identifier + '\'' +
                ", email='" + email + '\'' +
                ", paid_at='" + paid_at + '\'' +
                ", status='" + status + '\'' +
                ", jodoPaymentWebhookResponsePayloadOrderDetailsList=" + jodoPaymentWebhookResponsePayloadOrderDetailsList +
                '}';
    }
}
