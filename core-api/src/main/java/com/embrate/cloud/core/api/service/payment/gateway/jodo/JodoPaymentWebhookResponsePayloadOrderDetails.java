package com.embrate.cloud.core.api.service.payment.gateway.jodo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @created_at 22/09/23 : 16:12
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class JodoPaymentWebhookResponsePayloadOrderDetails {

    @JsonProperty("component_type")
    private String componentType;

    @JsonProperty("amount")
    private Double amount;

    public JodoPaymentWebhookResponsePayloadOrderDetails() {
    }

    public JodoPaymentWebhookResponsePayloadOrderDetails(String componentType, Double amount) {
        this.componentType = componentType;
        this.amount = amount;
    }

    public String getComponentType() {
        return componentType;
    }

    public void setComponentType(String componentType) {
        this.componentType = componentType;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    @Override
    public String toString() {
        return "JodoPaymentWebhookResponsePayloadOrderDetails{" +
                "componentType='" + componentType + '\'' +
                ", amount=" + amount +
                '}';
    }
}
