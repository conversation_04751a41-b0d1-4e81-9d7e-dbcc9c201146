package com.embrate.cloud.core.api.service.payment.gateway.merchants;

import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayServiceProvider;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public enum PGMerchantStatus {
    ACTIVE, INACTIVE;

    public static PGMerchantStatus getStatus(
            String status) {
        if (StringUtils.isBlank(status)) {
            return null;
        }
        for (PGMerchantStatus merchantStatus : PGMerchantStatus
                .values()) {
            if (merchantStatus.name()
                    .equalsIgnoreCase(status)) {
                return merchantStatus;
            }
        }
        return null;
    }
}
