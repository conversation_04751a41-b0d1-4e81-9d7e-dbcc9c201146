package com.embrate.cloud.core.api.service.payment.gateway.razorpay;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @created_at 12/09/24 : 15:28
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class RazorpayPaymentWebhookResponseAcquirerDataPayload {

    @JsonProperty("auth_code")
    private String auth_code;

    @JsonProperty("rrn")
    private String rrn;

    @JsonProperty("bank_transaction_id")
    private String bank_transaction_id;

    @JsonProperty("transaction_id")
    private String transaction_id;

    public RazorpayPaymentWebhookResponseAcquirerDataPayload() {
    }

    public RazorpayPaymentWebhookResponseAcquirerDataPayload(String auth_code, String rrn, String bank_transaction_id, String transaction_id) {
        this.auth_code = auth_code;
        this.rrn = rrn;
        this.bank_transaction_id = bank_transaction_id;
        this.transaction_id = transaction_id;
    }

    public String getAuth_code() {
        return auth_code;
    }

    public void setAuth_code(String auth_code) {
        this.auth_code = auth_code;
    }

    public String getRrn() {
        return rrn;
    }

    public void setRrn(String rrn) {
        this.rrn = rrn;
    }

    public String getBank_transaction_id() {
        return bank_transaction_id;
    }

    public void setBank_transaction_id(String bank_transaction_id) {
        this.bank_transaction_id = bank_transaction_id;
    }

    public String getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(String transaction_id) {
        this.transaction_id = transaction_id;
    }

    @Override
    public String toString() {
        return "RazorpayPaymentWebhookResponseAcquirerDataPayload{" +
                "authCode='" + auth_code + '\'' +
                ", rrn='" + rrn + '\'' +
                ", bankTransactionId='" + bank_transaction_id + '\'' +
                ", transactionId='" + transaction_id + '\'' +
                '}';
    }
}
