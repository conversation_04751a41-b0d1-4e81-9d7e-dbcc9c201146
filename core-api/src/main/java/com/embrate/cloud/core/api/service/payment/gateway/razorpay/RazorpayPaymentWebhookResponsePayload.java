package com.embrate.cloud.core.api.service.payment.gateway.razorpay;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @created_at 12/09/24 : 11:09
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class RazorpayPaymentWebhookResponsePayload {

    @JsonProperty("payment")
    private RazorpayPaymentWebhookResponsePaymentPayload payment;

    public RazorpayPaymentWebhookResponsePayload() {
    }

    public RazorpayPaymentWebhookResponsePayload(RazorpayPaymentWebhookResponsePaymentPayload payment) {
        this.payment = payment;
    }

    public RazorpayPaymentWebhookResponsePaymentPayload getPayment() {
        return payment;
    }

    public void setPayment(RazorpayPaymentWebhookResponsePaymentPayload payment) {
        this.payment = payment;
    }

    @Override
    public String toString() {
        return "RazorpayPaymentWebhookResponsePayload{" +
                "razorpayPaymentWebhookResponsePaymentPayload=" + payment +
                '}';
    }
}
