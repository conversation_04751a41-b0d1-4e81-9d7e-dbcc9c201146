package com.embrate.cloud.core.api.sms.webpay;

import com.lernen.cloud.core.api.notification.NotificationStatus;

/**
 *
 * <AUTHOR>
 *
 */
public enum WebPaySMSStatus {

	DELIVERED("Delivered", NotificationStatus.DELIVERED), UNDELIVERED(
			"Undelivered", NotificationStatus.FAILED), FAILED("Failed",
					NotificationStatus.FAILED), SENT("Submitted",
							NotificationStatus.SENT), OTHER("Other",
									NotificationStatus.SENT);

	private final String statusValue;
	private final NotificationStatus notificationStatus;

	private WebPaySMSStatus(String statusValue,
			NotificationStatus notificationStatus) {
		this.statusValue = statusValue;
		this.notificationStatus = notificationStatus;
	}

	public String getStatusValue() {
		return statusValue;
	}

	public NotificationStatus getNotificationStatus() {
		return notificationStatus;
	}

	public static NotificationStatus getNotificationStatus(String statusValue) {
		for (final WebPaySMSStatus webPaySMSStatus : WebPaySMSStatus.values()) {
			if (webPaySMSStatus.getStatusValue()
					.equalsIgnoreCase(statusValue)) {
				return webPaySMSStatus.getNotificationStatus();
			}
		}
		return null;
	}
}
