package com.embrate.cloud.core.api.student.management;

import java.util.List;

/**
 * <AUTHOR>
 */
public class StudentManagementInputField {

	private final StudentManagementField field;

	private final String displayName;

	public StudentManagementInputField(StudentManagementField field, String displayName) {
		this.field = field;
		this.displayName = displayName;
	}

	public StudentManagementField getField() {
		return field;
	}

	public String getDisplayName() {
		return displayName;
	}

	@Override
	public String toString() {
		return "StudentManagementInputField{" +
				"field=" + field +
				", displayName='" + displayName + '\'' +
				'}';
	}
}
