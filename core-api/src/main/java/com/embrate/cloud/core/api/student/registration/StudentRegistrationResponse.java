package com.embrate.cloud.core.api.student.registration;

import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayServiceProvider;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class StudentRegistrationResponse {

	private final boolean registrationSuccess;
	private final UUID registrationId;

	private final String studentRegistrationNumber;

	private final boolean registrationPaymentRequired;

	private final PaymentGatewayServiceProvider paymentGatewayServiceProvider;

	private final double registrationPaymentAmount;

	private final String paymentRemark;

	private final String paymentURL;

	public StudentRegistrationResponse(boolean registrationSuccess, UUID registrationId, String studentRegistrationNumber, boolean registrationPaymentRequired, PaymentGatewayServiceProvider paymentGatewayServiceProvider, double registrationPaymentAmount, String paymentRemark, String paymentURL) {
		this.registrationSuccess = registrationSuccess;
		this.registrationId = registrationId;
		this.studentRegistrationNumber = studentRegistrationNumber;
		this.registrationPaymentRequired = registrationPaymentRequired;
		this.paymentGatewayServiceProvider = paymentGatewayServiceProvider;
		this.registrationPaymentAmount = registrationPaymentAmount;
		this.paymentRemark = paymentRemark;
		this.paymentURL = paymentURL;
	}

	public boolean isRegistrationSuccess() {
		return registrationSuccess;
	}

	public UUID getRegistrationId() {
		return registrationId;
	}

	public String getStudentRegistrationNumber() {
		return studentRegistrationNumber;
	}

	public boolean isRegistrationPaymentRequired() {
		return registrationPaymentRequired;
	}

	public PaymentGatewayServiceProvider getPaymentGatewayServiceProvider() {
		return paymentGatewayServiceProvider;
	}

	public double getRegistrationPaymentAmount() {
		return registrationPaymentAmount;
	}

	public String getPaymentRemark() {
		return paymentRemark;
	}

	public static StudentRegistrationResponse forFailure() {
		return new StudentRegistrationResponse(false, null, null, false, null, 0d, null, null);
	}

	public String getPaymentURL() {
		return paymentURL;
	}

	@Override
	public String toString() {
		return "StudentRegistrationResponse{" +
				"registrationSuccess=" + registrationSuccess +
				", registrationId=" + registrationId +
				", studentRegistrationNumber='" + studentRegistrationNumber + '\'' +
				", registrationPaymentRequired=" + registrationPaymentRequired +
				", paymentGatewayServiceProvider=" + paymentGatewayServiceProvider +
				", registrationPaymentAmount=" + registrationPaymentAmount +
				", paymentRemark='" + paymentRemark + '\'' +
				", paymentURL='" + paymentURL + '\'' +
				'}';
	}
}
