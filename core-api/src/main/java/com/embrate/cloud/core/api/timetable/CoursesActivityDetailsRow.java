package com.embrate.cloud.core.api.timetable;

import com.lernen.cloud.core.api.staff.Staff;

import java.util.UUID;

public class CoursesActivityDetailsRow {

    private final UUID standardId;

    private final Integer sectionId;

    private final Entity entity;

    private final UUID entityId;

    private final String entityIdVal;

    private final Staff staff;

    public CoursesActivityDetailsRow(UUID standardId, Integer sectionId, Entity entity, UUID entityId, String entityIdVal, Staff staff) {
        this.standardId = standardId;
        this.sectionId = sectionId;
        this.entity = entity;
        this.entityId = entityId;
        this.entityIdVal = entityIdVal;
        this.staff = staff;
    }

    public UUID getStandardId() {
        return standardId;
    }

    public Integer getSectionId() {
        return sectionId;
    }

    public Entity getEntity() {
        return entity;
    }

    public UUID getEntityId() {
        return entityId;
    }

    public String getEntityIdVal() {
        return entityIdVal;
    }

    public Staff getStaff() {
        return staff;
    }

    @Override
    public String toString() {
        return "CoursesActivityDetailsRow{" +
                "standardId=" + standardId +
                ", sectionId=" + sectionId +
                ", entity=" + entity +
                ", entityId=" + entityId +
                ", entityIdVal='" + entityIdVal + '\'' +
                ", staff=" + staff +
                '}';
    }
}
