/**
 * 
 */
package com.embrate.cloud.core.api.timetable;

import java.time.DayOfWeek;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class StudentTimetableDetails {
	
	private final List<ShiftPeriodDetails> shiftPeriodDetailsList;
	
	private final Map<DayOfWeek, List<StudentTimetableShiftPeriodDetails>> studentTimetableShiftPeriodWeekDayMap;

	/**
	 * @param shiftPeriodDetailsList
	 * @param studentTimetableShiftPeriodWeekDayMap
	 */
	public StudentTimetableDetails(List<ShiftPeriodDetails> shiftPeriodDetailsList,
			Map<DayOfWeek, List<StudentTimetableShiftPeriodDetails>> studentTimetableShiftPeriodWeekDayMap) {
		this.shiftPeriodDetailsList = shiftPeriodDetailsList;
		this.studentTimetableShiftPeriodWeekDayMap = studentTimetableShiftPeriodWeekDayMap;
	}

	/**
	 * @return the shiftPeriodDetailsList
	 */
	public List<ShiftPeriodDetails> getShiftPeriodDetailsList() {
		return shiftPeriodDetailsList;
	}

	/**
	 * @return the studentTimetableShiftPeriodWeekDayMap
	 */
	public Map<DayOfWeek, List<StudentTimetableShiftPeriodDetails>> getStudentTimetableShiftPeriodWeekDayMap() {
		return studentTimetableShiftPeriodWeekDayMap;
	}

	@Override
	public String toString() {
		return "StudentTimetableDetails [shiftPeriodDetailsList=" + shiftPeriodDetailsList
				+ ", studentTimetableShiftPeriodWeekDayMap=" + studentTimetableShiftPeriodWeekDayMap + "]";
	}
 
}
