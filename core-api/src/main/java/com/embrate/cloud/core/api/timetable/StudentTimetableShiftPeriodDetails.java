/**
 * 
 */
package com.embrate.cloud.core.api.timetable;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class StudentTimetableShiftPeriodDetails {
	
	private ShiftPeriodDetails shiftPeriodDetails;
	
	private List<PeriodSlotDetails> periodSlotDetailsList;

	/**
	 * @param shiftPeriodDetails
	 * @param periodSlotDetailsList
	 */
	public StudentTimetableShiftPeriodDetails(ShiftPeriodDetails shiftPeriodDetails,
			List<PeriodSlotDetails> periodSlotDetailsList) {
		this.shiftPeriodDetails = shiftPeriodDetails;
		this.periodSlotDetailsList = periodSlotDetailsList;
	}

	/**
	 * @return the shiftPeriodDetails
	 */
	public ShiftPeriodDetails getShiftPeriodDetails() {
		return shiftPeriodDetails;
	}

	/**
	 * @param shiftPeriodDetails the shiftPeriodDetails to set
	 */
	public void setShiftPeriodDetails(ShiftPeriodDetails shiftPeriodDetails) {
		this.shiftPeriodDetails = shiftPeriodDetails;
	}

	public List<PeriodSlotDetails> getPeriodSlotDetailsList() {
		return periodSlotDetailsList;
	}

	public void setPeriodSlotDetailsList(List<PeriodSlotDetails> periodSlotDetailsList) {
		this.periodSlotDetailsList = periodSlotDetailsList;
	}

	@Override
	public String toString() {
		return "StudentTimetableShiftPeriodDetails{" +
				"shiftPeriodDetails=" + shiftPeriodDetails +
				", periodSlotDetailsList=" + periodSlotDetailsList +
				'}';
	}
}
