/**
 * 
 */
package com.embrate.cloud.core.api.timetable;

import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class TimetableMetaDataPayload {
	
	private int instituteId;
	
	private int academicSessionId;
	
	private UUID shiftId;

	private UUID standardId;

	private Integer sectionId;

	private UUID timetableId;
	
	private String timetableName;
	
	private TimetableStatus timetableStatus;
	
	private UUID createdBy;
	
	private UUID updatedBy;

	/**
	 * 
	 */
	public TimetableMetaDataPayload() {
	}

	/**
	 * @param instituteId
	 * @param academicSessionId
	 * @param shiftId
	 * @param timetableId
	 * @param standardId
	 * @param sectionId
	 * @param timetableStatus
	 * @param createdBy
	 * @param updatedBy
	 */
	public TimetableMetaDataPayload(int instituteId, int academicSessionId, UUID shiftId, UUID timetableId, String timetableName,
			UUID standardId, Integer sectionId, TimetableStatus timetableStatus, UUID createdBy, UUID updatedBy) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.shiftId = shiftId;
		this.timetableId = timetableId;
		this.timetableName = timetableName;
		this.standardId = standardId;
		this.sectionId = sectionId;
		this.timetableStatus = timetableStatus;
		this.createdBy = createdBy;
		this.updatedBy = updatedBy;
	}

	/**
	 * @return the createdBy
	 */
	public UUID getCreatedBy() {
		return createdBy;
	}

	/**
	 * @param createdBy the createdBy to set
	 */
	public void setCreatedBy(UUID createdBy) {
		this.createdBy = createdBy;
	}

	/**
	 * @return the updatedBy
	 */
	public UUID getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(UUID updatedBy) {
		this.updatedBy = updatedBy;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @return the academicSessionId
	 */
	public int getAcademicSessionId() {
		return academicSessionId;
	}

	/**
	 * @return the shiftId
	 */
	public UUID getShiftId() {
		return shiftId;
	}

	/**
	 * @return the timetableId
	 */
	public UUID getTimetableId() {
		return timetableId;
	}

	/**
	 * @return the timetableName
	 */
	public String getTimetableName() {
		return timetableName;
	}

	/**
	 * @param timetableName the timetableName to set
	 */
	public void setTimetableName(String timetableName) {
		this.timetableName = timetableName;
	}

	/**
	 * @return the standardId
	 */
	public UUID getStandardId() {
		return standardId;
	}

	/**
	 * @return the sectionId
	 */
	public Integer getSectionId() {
		return sectionId;
	}

	/**
	 * @return the timetableStatus
	 */
	public TimetableStatus getTimetableStatus() {
		return timetableStatus;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @param academicSessionId the academicSessionId to set
	 */
	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	/**
	 * @param shiftId the shiftId to set
	 */
	public void setShiftId(UUID shiftId) {
		this.shiftId = shiftId;
	}

	/**
	 * @param timetableId the timetableId to set
	 */
	public void setTimetableId(UUID timetableId) {
		this.timetableId = timetableId;
	}

	/**
	 * @param standardId the standardId to set
	 */
	public void setStandardId(UUID standardId) {
		this.standardId = standardId;
	}

	/**
	 * @param sectionId the sectionId to set
	 */
	public void setSectionId(Integer sectionId) {
		this.sectionId = sectionId;
	}

	/**
	 * @param timetableStatus the timetableStatus to set
	 */
	public void setTimetableStatus(TimetableStatus timetableStatus) {
		this.timetableStatus = timetableStatus;
	}

	@Override
	public String toString() {
		return "TimetableMetaDataPayload [instituteId=" + instituteId + ", academicSessionId=" + academicSessionId
				+ ", shiftId=" + shiftId + ", timetableId=" + timetableId + ", timetableName=" + timetableName
				+ ", standardId=" + standardId + ", sectionId=" + sectionId + ", timetableStatus=" + timetableStatus
				+ ", createdBy=" + createdBy + ", updatedBy=" + updatedBy + "]";
	}
}
