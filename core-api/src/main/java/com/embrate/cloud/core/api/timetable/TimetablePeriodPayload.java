/**
 * 
 */
package com.embrate.cloud.core.api.timetable;

import java.time.DayOfWeek;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class TimetablePeriodPayload {

	private UUID timetableId;

	private UUID periodId;
	
	private DayOfWeek day;
	
	private List<PeriodSlotPayload> periodSlotPayloadList;
	
	/**
	 * 
	 */
	public TimetablePeriodPayload() {
	}

	/**
	 * @param periodId
	 * @param day
	 * @param periodSlotPayloadList
	 */
	public TimetablePeriodPayload(UUID timetableId, UUID periodId, DayOfWeek day,
			List<PeriodSlotPayload> periodSlotPayloadList) {
		this.timetableId = timetableId;
		this.periodId = periodId;
		this.day = day;
		this.periodSlotPayloadList = periodSlotPayloadList;
	}

	public UUID getTimetableId() {
		return timetableId;
	}

	public void setTimetableId(UUID timetableId) {
		this.timetableId = timetableId;
	}

	/**
	 * @return the periodId
	 */
	public UUID getPeriodId() {
		return periodId;
	}

	/**
	 * @param periodId the periodId to set
	 */
	public void setPeriodId(UUID periodId) {
		this.periodId = periodId;
	}

	/**
	 * @return the day
	 */
	public DayOfWeek getDay() {
		return day;
	}

	/**
	 * @param day the day to set
	 */
	public void setDay(DayOfWeek day) {
		this.day = day;
	}

	/**
	 * @return the periodDetailsPayload
	 */
	public List<PeriodSlotPayload> getPeriodSlotPayloadList() {
		return periodSlotPayloadList;
	}

	/**
	 * @param periodSlotPayloadList the periodSlotPayloadList to set
	 */
	public void setPeriodSlotPayloadList(List<PeriodSlotPayload> periodSlotPayloadList) {
		this.periodSlotPayloadList = periodSlotPayloadList;
	}

	@Override
	public String toString() {
		return "TimetablePeriodPayload{" +
				"timetableId=" + timetableId +
				", periodId=" + periodId +
				", day=" + day +
				", periodSlotPayloadList=" + periodSlotPayloadList +
				'}';
	}
}
