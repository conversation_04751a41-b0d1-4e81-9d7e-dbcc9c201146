/**
 * 
 */
package com.embrate.cloud.core.api.timetable;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class TimetableShiftPeriodDetails {
	
	private UUID periodID;
	
	private List<PeriodSlotDetails> periodSlotDetailsList;

	
	/**
	 * 
	 */
	public TimetableShiftPeriodDetails() {
	}


	/**
	 * @param periodID
	 * @param periodSlotDetailsList
	 */
	public TimetableShiftPeriodDetails(UUID periodID, List<PeriodSlotDetails> periodSlotDetailsList) {
		this.periodID = periodID;
		this.periodSlotDetailsList = periodSlotDetailsList;
	}


	/**
	 * @return the periodID
	 */
	public UUID getPeriodID() {
		return periodID;
	}


	/**
	 * @param periodID the periodID to set
	 */
	public void setPeriodID(UUID periodID) {
		this.periodID = periodID;
	}

	public List<PeriodSlotDetails> getPeriodSlotDetailsList() {
		return periodSlotDetailsList;
	}

	public void setPeriodSlotDetailsList(List<PeriodSlotDetails> periodSlotDetailsList) {
		this.periodSlotDetailsList = periodSlotDetailsList;
	}

	@Override
	public String toString() {
		return "TimetableShiftPeriodDetails{" +
				"periodID=" + periodID +
				", periodSlotDetailsList=" + periodSlotDetailsList +
				'}';
	}
}
