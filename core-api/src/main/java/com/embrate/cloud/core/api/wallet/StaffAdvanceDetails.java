/**
 * 
 */
package com.embrate.cloud.core.api.wallet;

import java.util.UUID;

import com.lernen.cloud.core.api.staff.StaffBasicInfo;

/**
 * <AUTHOR>
 *
 */
public class StaffAdvanceDetails {
	
	private Integer instituteId;
	
	private UUID staffId;
	
	private StaffBasicInfo staffBasicInfo;
	
	private Double amount;

	
	/**
	 * @param instituteId
	 * @param staffId
	 * @param staffBasicInfo
	 * @param amount
	 */
	public StaffAdvanceDetails(Integer instituteId, UUID staffId, StaffBasicInfo staffBasicInfo, Double amount) {
		this.instituteId = instituteId;
		this.staffId = staffId;
		this.staffBasicInfo = staffBasicInfo;
		this.amount = amount;
	}

	/**
	 * 
	 */
	public StaffAdvanceDetails() {
	}

	/**
	 * @return the instituteId
	 */
	public Integer getInstituteId() {
		return instituteId;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(Integer instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @return the staffId
	 */
	public UUID getStaffId() {
		return staffId;
	}

	/**
	 * @param staffId the staffId to set
	 */
	public void setStaffId(UUID staffId) {
		this.staffId = staffId;
	}

	/**
	 * @return the staffBasicInfo
	 */
	public StaffBasicInfo getStaffBasicInfo() {
		return staffBasicInfo;
	}

	/**
	 * @param staffBasicInfo the staffBasicInfo to set
	 */
	public void setStaffBasicInfo(StaffBasicInfo staffBasicInfo) {
		this.staffBasicInfo = staffBasicInfo;
	}

	/**
	 * @return the amount
	 */
	public Double getAmount() {
		return amount;
	}

	/**
	 * @param amount the amount to set
	 */
	public void setAmount(Double amount) {
		this.amount = amount;
	}

	@Override
	public String toString() {
		return "StaffWalletDetails [instituteId=" + instituteId + ", staffId=" + staffId + ", staffBasicInfo="
				+ staffBasicInfo + ", amount=" + amount + "]";
	}

}
