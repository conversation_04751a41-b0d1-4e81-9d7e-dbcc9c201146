package com.embrate.cloud.core.api.wallet;

import java.util.List;

/**
 * <AUTHOR>
 */

public class UserWalletDetails {

    private final Double walletAmount;

    private final List<WalletTransactionPayload> walletTransactions;

    public UserWalletDetails(Double walletAmount, List<WalletTransactionPayload> walletTransactions) {
        this.walletAmount = walletAmount;
        this.walletTransactions = walletTransactions;
    }

    public Double getWalletAmount() {
        return walletAmount;
    }

    public List<WalletTransactionPayload> getWalletTransactions() {
        return walletTransactions;
    }

    @Override
    public String toString() {
        return "UserWalletDetails{" +
                "walletAmount=" + walletAmount +
                ", walletTransactions=" + walletTransactions +
                '}';
    }
}
