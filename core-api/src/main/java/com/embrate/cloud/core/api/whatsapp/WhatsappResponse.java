package com.embrate.cloud.core.api.whatsapp;

/**
 * 
 * <AUTHOR>
 *
 */
public class WhatsappResponse {

	private final boolean success;

	private final String failureReason;

	public WhatsappResponse(boolean success, String failureReason) {
		this.success = success;
		this.failureReason = failureReason;
	}

	public boolean isSuccess() {
		return success;
	}

	public String getFailureReason() {
		return failureReason;
	}

	public static WhatsappResponse successResponse() {
		return new WhatsappResponse(true, null);
	}

	public static WhatsappResponse failureResponse(String failureReason) {
		return new WhatsappResponse(false, failureReason);
	}

	@Override
	public String toString() {
		return "WhatsappResponse{" +
				"success=" + success +
				", failureReason='" + failureReason + '\'' +
				'}';
	}
}
