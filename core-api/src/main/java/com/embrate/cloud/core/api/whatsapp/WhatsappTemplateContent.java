package com.embrate.cloud.core.api.whatsapp;

import com.embrate.cloud.core.api.service.communication.templates.TemplateVariableDetails;

/**
 * <AUTHOR>
 */
public class WhatsappTemplateContent {

    private final String template;

    private final TemplateVariableDetails templateVariableDetails;

    private final String dltTemplateId;

    public WhatsappTemplateContent(String template, TemplateVariableDetails templateVariableDetails, String dltTemplateId) {
        this.template = template;
        this.templateVariableDetails = templateVariableDetails;
        this.dltTemplateId = dltTemplateId;
    }

    public String getTemplate() {
        return template;
    }

    public TemplateVariableDetails getTemplateVariableDetails() {
        return templateVariableDetails;
    }

    public String getDltTemplateId() {
        return dltTemplateId;
    }

    @Override
    public String toString() {
        return "SMSTemplateContent{" +
                "template='" + template + '\'' +
                ", templateVariableDetails=" + templateVariableDetails +
                ", dltTemplateId='" + dltTemplateId + '\'' +
                '}';
    }
}
