package com.embrate.cloud.core.api.whatsapp.webpay;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class WebPayBalanceResponse {

    @JsonProperty("WalletBalance")
    private Double WalletBalance;



    public Double getWalletBalance() {
        return WalletBalance;
    }

    public void setWalletBalance(Double walletBalance) {
        WalletBalance = walletBalance;
    }

    @Override
    public String toString() {
        return "WebPayBalanceResponse{" +
                "WalletBalance=" + WalletBalance +
                '}';
    }
}
