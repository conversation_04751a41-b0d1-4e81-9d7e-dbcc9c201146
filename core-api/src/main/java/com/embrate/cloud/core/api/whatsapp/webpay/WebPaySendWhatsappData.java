package com.embrate.cloud.core.api.whatsapp.webpay;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class WebPaySendWhatsappData {

    @JsonProperty("Number")
    private String Number;

    @JsonProperty("MaskId")
    private String MaskId;

    public String getNumber() {
        return Number;
    }

    public void setNumber(String number) {
        Number = number;
    }

    public String getMaskId() {
        return MaskId;
    }

    public void setMaskId(String maskId) {
        MaskId = maskId;
    }

    @Override
    public String toString() {
        return "WebPaySendWhatsappData{" +
                "Number='" + Number + '\'' +
                ", MaskId='" + MaskId + '\'' +
                '}';
    }
}
