package com.lernen.cloud.core.api.assessment;

import java.util.List;
import java.util.UUID;

import com.lernen.cloud.core.api.examination.ExamCoursePublishedStatus;
import com.lernen.cloud.core.api.institute.Time;

public class AssessmentMetaDataPayload {

    private UUID assessmentId;
    private String assessmentName;
    private Integer startTime;
    private Integer assessmentDate;
    private Time duration;
    private String passkey;
    private String assessmentInstruction;
    private boolean autoSubmit;
    private boolean showResult;
    private ExamCoursePublishedStatus publishedStatus;
    private List<AssessmentEntityMapping> assessmentEntityMappings;
    
    public AssessmentMetaDataPayload() {
    }

    

    public AssessmentMetaDataPayload(UUID assessmentId, String assessmentName, Integer startTime, Integer assessmentDate, Time duration,
            String passkey, String assessmentInstruction, boolean autoSubmit, boolean showResult,
            ExamCoursePublishedStatus publishedStatus, List<AssessmentEntityMapping> assessmentEntityMappings) {
        this.assessmentId = assessmentId;
        this.assessmentName = assessmentName;
        this.startTime = startTime;
        this.assessmentDate = assessmentDate;
        this.duration = duration;
        this.passkey = passkey;
        this.assessmentInstruction = assessmentInstruction;
        this.autoSubmit = autoSubmit;
        this.showResult = showResult;
        this.publishedStatus = publishedStatus;
        this.assessmentEntityMappings = assessmentEntityMappings;
    }



    public String getAssessmentName() {
        return assessmentName;
    }

    public UUID getAssessmentId() {
        return assessmentId;
    }

    public void setAssessmentId(UUID assessmentId) {
        this.assessmentId = assessmentId;
    }

    public void setAssessmentName(String assessmentName) {
        this.assessmentName = assessmentName;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getAssessmentDate() {
        return assessmentDate;
    }

    public void setAssessmentDate(Integer assessmentDate) {
        this.assessmentDate = assessmentDate;
    }

    public Time getDuration() {
        return duration;
    }

    public void setDuration(Time duration) {
        this.duration = duration;
    }

    public String getPasskey() {
        return passkey;
    }

    public void setPasskey(String passkey) {
        this.passkey = passkey;
    }

    public String getAssessmentInstruction() {
        return assessmentInstruction;
    }

    public void setAssessmentInstruction(String assessmentInstruction) {
        this.assessmentInstruction = assessmentInstruction;
    }

    public boolean isAutoSubmit() {
        return autoSubmit;
    }

    public void setAutoSubmit(boolean autoSubmit) {
        this.autoSubmit = autoSubmit;
    }

    public boolean isShowResult() {
        return showResult;
    }

    public void setShowResult(boolean showResult) {
        this.showResult = showResult;
    }

    public ExamCoursePublishedStatus getPublishedStatus() {
        return publishedStatus;
    }

    public void setPublishedStatus(ExamCoursePublishedStatus publishedStatus) {
        this.publishedStatus = publishedStatus;
    }

    public List<AssessmentEntityMapping> getAssessmentEntityMappings() {
        return assessmentEntityMappings;
    }

    public void setAssessmentEntityMappings(List<AssessmentEntityMapping> assessmentEntityMappings) {
        this.assessmentEntityMappings = assessmentEntityMappings;
    }

    @Override
    public String toString() {
        return "AssessmentMetaDataPayload [assessmentId=" + assessmentId + ", assessmentName=" + assessmentName
                + ", startTime=" + startTime + ", assessmentDate=" + assessmentDate + ", duration=" + duration
                + ", passkey=" + passkey + ", assessmentInstruction=" + assessmentInstruction + ", autoSubmit="
                + autoSubmit + ", showResult=" + showResult + ", publishedStatus=" + publishedStatus
                + ", assessmentEntityMappings=" + assessmentEntityMappings + "]";
    }

}
