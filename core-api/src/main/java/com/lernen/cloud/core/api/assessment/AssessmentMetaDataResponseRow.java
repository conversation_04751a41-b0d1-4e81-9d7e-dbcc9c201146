package com.lernen.cloud.core.api.assessment;

import java.util.UUID;

import com.lernen.cloud.core.api.examination.ExamCoursePublishedStatus;
import com.lernen.cloud.core.api.institute.Time;

public class AssessmentMetaDataResponseRow {

    private final UUID assessmentId;
    private final String assessmentName;
    private final double assessmentTotalMarks;
    private final Integer startTime;
    private final Integer assessmentDate;
    private final int duration;
    private final String passkey;
    private final String assessmentInstruction;
    private final boolean autoSubmit;
    private final boolean showResult;
    private final ExamCoursePublishedStatus publishedStatus;
    private final AssessmentStatus assessmentStatus;
    private final Integer createdAt;
    private final AssessmentEntityMappingResponse assessmentEntityMappingResponse;
    
    public AssessmentMetaDataResponseRow(UUID assessmentId, String assessmentName, double assessmentTotalMarks, Integer startTime, Integer assessmentDate,
            int duration, String passkey, String assessmentInstruction, boolean autoSubmit, boolean showResult,
            ExamCoursePublishedStatus publishedStatus, Integer createdAt,
            AssessmentEntityMappingResponse assessmentEntityMappingResponse, AssessmentStatus assessmentStatus) {
        this.assessmentId = assessmentId;
        this.assessmentName = assessmentName;
        this.assessmentTotalMarks = assessmentTotalMarks;
        this.startTime = startTime;
        this.assessmentDate = assessmentDate;
        this.duration = duration;
        this.passkey = passkey;
        this.assessmentInstruction = assessmentInstruction;
        this.autoSubmit = autoSubmit;
        this.showResult = showResult;
        this.publishedStatus = publishedStatus;
        this.createdAt = createdAt;
        this.assessmentEntityMappingResponse = assessmentEntityMappingResponse;
        this.assessmentStatus = assessmentStatus;
    }

    public UUID getAssessmentId() {
        return assessmentId;
    }

    public String getAssessmentName() {
        return assessmentName;
    }

    public double getAssessmentTotalMarks() {
        return assessmentTotalMarks;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public Integer getAssessmentDate() {
        return assessmentDate;
    }

    public int getDuration() {
        return duration;
    }

    public String getPasskey() {
        return passkey;
    }

    public String getAssessmentInstruction() {
        return assessmentInstruction;
    }

    public boolean isAutoSubmit() {
        return autoSubmit;
    }

    public boolean isShowResult() {
        return showResult;
    }

    public ExamCoursePublishedStatus getPublishedStatus() {
        return publishedStatus;
    }

    public AssessmentEntityMappingResponse getAssessmentEntityMappingResponse() {
        return assessmentEntityMappingResponse;
    }

    public AssessmentStatus getAssessmentStatus() {
        return assessmentStatus;
    }

    public Integer getCreatedAt() {
        return createdAt;
    }

    @Override
    public String toString() {
        return "AssessmentMetaDataResponseRow [assessmentId=" + assessmentId + ", assessmentName=" + assessmentName
                + ", assessmentTotalMarks=" + assessmentTotalMarks + ", startTime=" + startTime + ", assessmentDate="
                + assessmentDate + ", duration=" + duration + ", passkey=" + passkey + ", assessmentInstruction="
                + assessmentInstruction + ", autoSubmit=" + autoSubmit + ", showResult=" + showResult
                + ", publishedStatus=" + publishedStatus + ", assessmentStatus=" + assessmentStatus + ", createdAt="
                + createdAt + ", assessmentEntityMappingResponse=" + assessmentEntityMappingResponse + "]";
    }

}
