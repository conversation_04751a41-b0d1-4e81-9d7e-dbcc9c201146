package com.lernen.cloud.core.api.assessment;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class QuestionBank {

    private UUID questionId;
    private String questionText;
    private QuestionType questionType;
    private List<UUID> correctAnswerId;
    private Integer marks;
    private boolean isActive;
    private QuestionLevel questionLevel;
    private String tags;
    private QuestionTypeAnswerSelect questionTypeAnswerSelect;
    private List<QuestionOption> questionOptions;
    private int sequence;


    public QuestionBank() {
    }

    public QuestionBank(UUID questionId, String questionText, QuestionType questionType,
            List<UUID> correctAnswerId, Integer marks, boolean isActive, QuestionLevel questionLevel, String tags, List<QuestionOption> questionOptions, int sequence) {
        this.questionId = questionId;
        this.questionText = questionText;
        this.questionType = questionType;
        this.correctAnswerId = correctAnswerId;
        this.marks = marks;
        this.isActive = isActive;
        this.questionLevel = questionLevel;
        this.tags = tags;
        this.questionOptions = questionOptions;
        this.questionTypeAnswerSelect = getTypeAnswerSelect(questionType, correctAnswerId);
        this.sequence = sequence;
    }

    public UUID getQuestionId() {
        return questionId;
    }

    public void setQuestionId(UUID questionId) {
        this.questionId = questionId;
    }

    public String getQuestionText() {
        return questionText;
    }

    public QuestionTypeAnswerSelect getQuestionTypeAnswerSelect() {
        return questionTypeAnswerSelect;
    }

    public void setQuestionTypeAnswerSelect(QuestionTypeAnswerSelect questionTypeAnswerSelect) {
        this.questionTypeAnswerSelect = questionTypeAnswerSelect;
    }

    public void setQuestionText(String questionText) {
        this.questionText = questionText;
    }

    public QuestionType getQuestionType() {
        return questionType;
    }

    public void setQuestionType(QuestionType questionType) {
        this.questionType = questionType;
    }

    public List<UUID> getCorrectAnswerId() {
        return correctAnswerId;
    }

    public void setCorrectAnswerId(List<UUID> correctAnswerId) {
        this.correctAnswerId = correctAnswerId;
    }

    public Integer getMarks() {
        return marks;
    }

    public void setMarks(Integer marks) {
        this.marks = marks;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean isActive) {
        this.isActive = isActive;
    }

    public QuestionLevel getQuestionLevel() {
        return questionLevel;
    }

    public void setQuestionLevel(QuestionLevel questionLevel) {
        this.questionLevel = questionLevel;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public List<QuestionOption> getQuestionOptions() {
        return questionOptions;
    }

    public void setQuestionOptions(List<QuestionOption> questionOptions) {
        this.questionOptions = questionOptions;
    }

    public int getSequence() {
        return sequence;
    }

    public void setSequence(int sequence) {
        this.sequence = sequence;
    }

    private QuestionTypeAnswerSelect getTypeAnswerSelect(QuestionType questionType, List<UUID> correctAnswerIds){
        if(questionType == QuestionType.SUBJECTIVE){
            return QuestionTypeAnswerSelect.STRING;
        }
        if(correctAnswerIds.size()>1){
            return QuestionTypeAnswerSelect.MULTIPLE;
        }
        return QuestionTypeAnswerSelect.SINGLE;
    }

    public static List<QuestionBank> getQuestionBankDetailsFromMapper(List<QuestionBankRow> questionBankRows){
        
        List<QuestionBank> questions = new ArrayList<>();

        for(QuestionBankRow questionBankRow : questionBankRows){
            UUID questionId = questionBankRow.getQuestionId();

            QuestionBank question = null;
            for (QuestionBank q : questions) {
                if (q.getQuestionId().equals(questionId)) {
                    question = q;
                    break;
                }
            }
            if(question == null){
                List<QuestionOption> questionOptions = new ArrayList<>();
                questionOptions.add(questionBankRow.getQuestionOptions());
                question = new QuestionBank(questionId, questionBankRow.getQuestionText(), questionBankRow.getQuestionType(), questionBankRow.getCorrectAnswerId(), questionBankRow.getMarks(), questionBankRow.isActive(), questionBankRow.getQuestionLevel(), questionBankRow.getTags(), questionOptions, questionBankRow.getSequence());
                questions.add(question);
                continue;
            }
            question.getQuestionOptions().add(questionBankRow.getQuestionOptions());
        }
        return questions;
    }

    @Override
    public String toString() {
        return "QuestionBank [questionId=" + questionId + ", questionText=" + questionText + ", questionType="
                + questionType + ", correctAnswerId=" + correctAnswerId + ", marks=" + marks + ", isActive=" + isActive
                + ", questionLevel=" + questionLevel + ", tags=" + tags + ", questionTypeAnswerSelect="
                + questionTypeAnswerSelect + ", questionOptions=" + questionOptions + ", sequence=" + sequence + "]";
    }
    
    
}
