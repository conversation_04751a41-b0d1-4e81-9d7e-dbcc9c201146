package com.lernen.cloud.core.api.assessment;

import java.util.List;
import java.util.UUID;

public class QuestionBankRow {

    private final UUID questionId;
    private final String questionText;
    private final QuestionType questionType;
    private final List<UUID> correctAnswerId;
    private final Integer marks;
    private final boolean isActive;
    private final QuestionLevel questionLevel;
    private final String tags;
    private final QuestionOption questionOptions;
    private final int sequence;
    
    public QuestionBankRow(UUID questionId, String questionText, QuestionType questionType,
            List<UUID> correctAnswerId, Integer marks, boolean isActive, QuestionLevel questionLevel, String tags,
            QuestionOption questionOptions, int sequence) {
        this.questionId = questionId;
        this.questionText = questionText;
        this.questionType = questionType;
        this.correctAnswerId = correctAnswerId;
        this.marks = marks;
        this.isActive = isActive;
        this.questionLevel = questionLevel;
        this.tags = tags;
        this.questionOptions = questionOptions;
        this.sequence = sequence;
    }

    public UUID getQuestionId() {
        return questionId;
    }

    public String getQuestionText() {
        return questionText;
    }

    public QuestionType getQuestionType() {
        return questionType;
    }

    public List<UUID> getCorrectAnswerId() {
        return correctAnswerId;
    }

    public Integer getMarks() {
        return marks;
    }

    public boolean isActive() {
        return isActive;
    }

    public QuestionLevel getQuestionLevel() {
        return questionLevel;
    }

    public String getTags() {
        return tags;
    }

    public QuestionOption getQuestionOptions() {
        return questionOptions;
    }

    public int getSequence() {
        return sequence;
    }

    @Override
    public String toString() {
        return "QuestionBankRow [questionId=" + questionId + ", questionText=" + questionText + ", questionType="
                + questionType + ", correctAnswerId=" + correctAnswerId + ", marks=" + marks + ", isActive=" + isActive
                + ", questionLevel=" + questionLevel + ", tags=" + tags + ", questionOptions=" + questionOptions
                + ", sequence=" + sequence + "]";
    }

}
