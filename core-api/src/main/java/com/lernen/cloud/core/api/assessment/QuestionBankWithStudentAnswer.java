package com.lernen.cloud.core.api.assessment;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;

public class QuestionBankWithStudentAnswer {

    private final UUID questionId;
    private final String questionText;
    private final Integer marks;
    private final List<QuestionOptionWithAnswerFlag> questionOptions;
    private final int sequence;
    
    public QuestionBankWithStudentAnswer(UUID questionId, String questionText, Integer marks,
            List<QuestionOptionWithAnswerFlag> questionOptions, int sequence) {
        this.questionId = questionId;
        this.questionText = questionText;
        this.marks = marks;
        this.questionOptions = questionOptions;
        this.sequence = sequence;
    }

    public UUID getQuestionId() {
        return questionId;
    }

    public String getQuestionText() {
        return questionText;
    }

    public Integer getMarks() {
        return marks;
    }

    public List<QuestionOptionWithAnswerFlag> getQuestionOptions() {
        return questionOptions;
    }

    public int getSequence() {
        return sequence;
    }

    public static List<QuestionBankWithStudentAnswer> sortBySequence(List<QuestionBankWithStudentAnswer> questionBankWithStudentAnswers){
        Collections.sort(questionBankWithStudentAnswers, new Comparator<QuestionBankWithStudentAnswer>() {
            @Override
            public int compare(QuestionBankWithStudentAnswer q1, QuestionBankWithStudentAnswer q2) {
                return Integer.compare(q1.getSequence(), q2.getSequence());
            }
        });
        return questionBankWithStudentAnswers;
    }

    @Override
    public String toString() {
        return "QuestionBankWithStudentAnswer [questionId=" + questionId + ", questionText=" + questionText + ", marks="
                + marks + ", questionOptions=" + questionOptions + ", sequence=" + sequence + "]";
    }
}
