package com.lernen.cloud.core.api.assessment;

import org.apache.commons.lang3.StringUtils;

public enum QuestionLevel {
    
    EASY , MEDIUM, HARD;

    public static QuestionLevel getQuestionType(String questionLevel) {
		if (StringUtils.isBlank(questionLevel)) {
			return null;
		}
		for (QuestionLevel questionLevelEnum : QuestionLevel.values()) {
			if (questionLevelEnum.name().equalsIgnoreCase(questionLevel)) {
				return questionLevelEnum;
			}
		}
		return null;
	}
}
