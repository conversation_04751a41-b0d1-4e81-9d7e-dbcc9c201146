/**
 * 
 */
package com.lernen.cloud.core.api.attendance;

import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class AttendanceRawRecord {

	private final UUID studentId;

	private final int academicSessionId;

	private final int attendanceTypeId;

	private final AttendanceStatus attendanceStatus;

	private final int attendanceDateTS;

	private final String remarks;

	private final UUID createdBy;

	private final UUID updatedBy;

	private final Integer createdAt;

	private final Integer updatedAt;

	public AttendanceRawRecord(UUID studentId, int academicSessionId, int attendanceTypeId, AttendanceStatus attendanceStatus, int attendanceDateTS, String remarks, UUID createdBy, UUID updatedBy, Integer createdAt, Integer updatedAt) {
		this.studentId = studentId;
		this.academicSessionId = academicSessionId;
		this.attendanceTypeId = attendanceTypeId;
		this.attendanceStatus = attendanceStatus;
		this.attendanceDateTS = attendanceDateTS;
		this.remarks = remarks;
		this.createdBy = createdBy;
		this.updatedBy = updatedBy;
		this.createdAt = createdAt;
		this.updatedAt = updatedAt;
	}

	public UUID getStudentId() {
		return studentId;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public int getAttendanceTypeId() {
		return attendanceTypeId;
	}

	public AttendanceStatus getAttendanceStatus() {
		return attendanceStatus;
	}

	public int getAttendanceDateTS() {
		return attendanceDateTS;
	}

	public String getRemarks() {
		return remarks;
	}

	public UUID getCreatedBy() {
		return createdBy;
	}

	public UUID getUpdatedBy() {
		return updatedBy;
	}

	public Integer getCreatedAt() {
		return createdAt;
	}

	public Integer getUpdatedAt() {
		return updatedAt;
	}

	@Override
	public String toString() {
		return "AttendanceRawRecord{" +
				"studentId=" + studentId +
				", academicSessionId=" + academicSessionId +
				", attendanceTypeId=" + attendanceTypeId +
				", attendanceStatus=" + attendanceStatus +
				", attendanceDateTS=" + attendanceDateTS +
				", remarks='" + remarks + '\'' +
				", createdBy=" + createdBy +
				", updatedBy=" + updatedBy +
				", createdAt=" + createdAt +
				", updatedAt=" + updatedAt +
				'}';
	}
}
