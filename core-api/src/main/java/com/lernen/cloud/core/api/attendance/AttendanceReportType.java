/**
 * 
 */
package com.lernen.cloud.core.api.attendance;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
public enum AttendanceReportType {
	ATTENDANCE_DETAILS_IN_A_DAY,
	ATTENDANCE_SUMMARY,
	ATTENDANCE_DETAILS_IN_A_MONTH,
	SINGLE_DAY_ATTENDANCE_REPORT,
	MONTHLY_ATTENDANCE_SUMMARY_REPORT;
	
	public static AttendanceReportType getAttendanceReportType(String attendanceReportType) {
		if (StringUtils.isBlank(attendanceReportType)) {
			return null;
		}
		for (AttendanceReportType attendanceReportTypeEnum : AttendanceReportType.values()) {
			if (attendanceReportTypeEnum.name().equalsIgnoreCase(attendanceReportType)) {
				return attendanceReportTypeEnum;
			}
		}
		return null;
	}
}
