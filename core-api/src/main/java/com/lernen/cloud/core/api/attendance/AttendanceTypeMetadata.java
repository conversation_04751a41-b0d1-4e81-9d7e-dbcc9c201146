package com.lernen.cloud.core.api.attendance;

import com.lernen.cloud.core.api.common.HourMinuteRange;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class AttendanceTypeMetadata {

    private Map<AttendanceSeason, HourMinuteRange> attendanceTimings;

    public AttendanceTypeMetadata() {
    }

    public AttendanceTypeMetadata(Map<AttendanceSeason, HourMinuteRange> attendanceTimings) {
        this.attendanceTimings = attendanceTimings;
    }

    public Map<AttendanceSeason, HourMinuteRange> getAttendanceTimings() {
        return attendanceTimings;
    }

    public void setAttendanceTimings(Map<AttendanceSeason, HourMinuteRange> attendanceTimings) {
        this.attendanceTimings = attendanceTimings;
    }

    @Override
    public String toString() {
        return "AttendanceTypeMetadata{" +
                "attendanceTimings=" + attendanceTimings +
                '}';
    }
}
