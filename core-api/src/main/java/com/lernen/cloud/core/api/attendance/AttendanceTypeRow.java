package com.lernen.cloud.core.api.attendance;

import java.util.Set;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class AttendanceTypeRow {

	private final int instituteId;

	private final int academicSessionId;

	private final int attendanceTypeId;

	private final String name;

	private final String description;

	private final AttendanceTypeMetadata attendanceTypeMetadata;

	private final UUID standardId;

	public AttendanceTypeRow(int instituteId, int academicSessionId, int attendanceTypeId, String name, String description, AttendanceTypeMetadata attendanceTypeMetadata, UUID standardId) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.attendanceTypeId = attendanceTypeId;
		this.name = name;
		this.description = description;
		this.attendanceTypeMetadata = attendanceTypeMetadata;
		this.standardId = standardId;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public int getAttendanceTypeId() {
		return attendanceTypeId;
	}

	public String getName() {
		return name;
	}

	public String getDescription() {
		return description;
	}

	public AttendanceTypeMetadata getAttendanceTypeMetadata() {
		return attendanceTypeMetadata;
	}

	public UUID getStandardId() {
		return standardId;
	}

	@Override
	public String toString() {
		return "AttendanceTypeRow{" +
				"instituteId=" + instituteId +
				", academicSessionId=" + academicSessionId +
				", attendanceTypeId=" + attendanceTypeId +
				", name='" + name + '\'' +
				", description='" + description + '\'' +
				", attendanceTypeMetadata=" + attendanceTypeMetadata +
				", standardId=" + standardId +
				'}';
	}
}
