package com.lernen.cloud.core.api.attendance;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @created_at 01/06/23 : 19:28
 **/
public class ClassAttendanceDataBuilder {
    private final int instituteId;

    private final String standardId;

    private final Integer sectionId;

    private final String standardName;

    private final int level;

    private final int attendanceDay;

    private final Integer attendanceType;
    private final List<Pair<AttendanceStudentData, AttendanceDateStatus>> attendanceDateStatusList;

    public ClassAttendanceDataBuilder(int instituteId, String standardId, Integer sectionId, String standardName, int level, int attendanceDay, Integer attendanceType, List<Pair<AttendanceStudentData, AttendanceDateStatus>> attendanceDateStatusList) {
        this.instituteId = instituteId;
        this.standardId = standardId;
        this.sectionId = sectionId;
        this.standardName = standardName;
        this.level = level;
        this.attendanceDay = attendanceDay;
        this.attendanceType = attendanceType;
        this.attendanceDateStatusList = attendanceDateStatusList;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public String getStandardId() {
        return standardId;
    }

    public Integer getSectionId() {
        return sectionId;
    }

    public String getStandardName() {
        return standardName;
    }

    public int getLevel() {
        return level;
    }

    public int getAttendanceDay() {
        return attendanceDay;
    }

    public Integer getAttendanceType() {
        return attendanceType;
    }

    public List<Pair<AttendanceStudentData, AttendanceDateStatus>> getAttendanceDateStatusList() {
        return attendanceDateStatusList;
    }

    @Override
    public String toString() {
        return "ClassAttendanceDataBuilder{" +
                "instituteId=" + instituteId +
                ", standardId='" + standardId + '\'' +
                ", sectionId=" + sectionId +
                ", standardName='" + standardName + '\'' +
                ", level=" + level +
                ", attendanceDay=" + attendanceDay +
                ", attendanceType=" + attendanceType +
                ", attendanceDateStatusList=" + attendanceDateStatusList +
                '}';
    }
}
