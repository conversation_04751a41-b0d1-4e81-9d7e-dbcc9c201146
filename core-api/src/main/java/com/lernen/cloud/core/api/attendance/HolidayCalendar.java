package com.lernen.cloud.core.api.attendance;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */

public class HolidayCalendar {

	private int instituteId;
	
	private int academicSessionId;
	
	private HolidayType holidayType;
	
	private List<EntityHolidayDates> entityHolidayDates;

	public HolidayCalendar() {}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}
	
	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	public HolidayType getHolidayType() {
		return holidayType;
	}

	public void setHolidayType(HolidayType holidayType) {
		this.holidayType = holidayType;
	}

	public List<EntityHolidayDates> getEntityHolidayDates() {
		return entityHolidayDates;
	}

	public void setEntityHolidayDates(List<EntityHolidayDates> entityHolidayDates) {
		this.entityHolidayDates = entityHolidayDates;
	}

}
