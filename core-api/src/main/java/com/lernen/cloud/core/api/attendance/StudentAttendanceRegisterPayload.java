/**
 * 
 */
package com.lernen.cloud.core.api.attendance;

import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class StudentAttendanceRegisterPayload {
	
	private int instituteId;
	
	private UUID studentId;
	
	private int academicSessionId;
	
	private Integer attendanceTypeId;
	
	private Integer attendanceDateTS;
	
	private AttendanceStatus attendanceStatus;

	private UUID createdBy;

	private UUID updatedBy;

	private String remarks;
	
	private Integer createdAt;

	private Integer updatedAt;

	/**
	 * @param instituteId
	 * @param studentId
	 * @param academicSessionId
	 * @param attendanceTypeId
	 * @param attendanceDateTS
	 * @param attendanceStatus
	 * @param createdBy
	 * @param updatedBy
	 * @param remarks
	 * @param createdAt
	 * @param updatedAt
	 */
	public StudentAttendanceRegisterPayload(int instituteId, UUID studentId, int academicSessionId,
			Integer attendanceTypeId, Integer attendanceDateTS, AttendanceStatus attendanceStatus, UUID createdBy,
			UUID updatedBy, String remarks, Integer createdAt, Integer updatedAt) {
		this.instituteId = instituteId;
		this.studentId = studentId;
		this.academicSessionId = academicSessionId;
		this.attendanceTypeId = attendanceTypeId;
		this.attendanceDateTS = attendanceDateTS;
		this.attendanceStatus = attendanceStatus;
		this.createdBy = createdBy;
		this.updatedBy = updatedBy;
		this.remarks = remarks;
		this.createdAt = createdAt;
		this.updatedAt = updatedAt;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @return the studentId
	 */
	public UUID getStudentId() {
		return studentId;
	}

	/**
	 * @param studentId the studentId to set
	 */
	public void setStudentId(UUID studentId) {
		this.studentId = studentId;
	}

	/**
	 * @return the academicSessionId
	 */
	public int getAcademicSessionId() {
		return academicSessionId;
	}

	/**
	 * @param academicSessionId the academicSessionId to set
	 */
	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	/**
	 * @return the attendanceTypeId
	 */
	public Integer getAttendanceTypeId() {
		return attendanceTypeId;
	}

	/**
	 * @param attendanceTypeId the attendanceTypeId to set
	 */
	public void setAttendanceTypeId(Integer attendanceTypeId) {
		this.attendanceTypeId = attendanceTypeId;
	}

	/**
	 * @return the attendanceDateTS
	 */
	public Integer getAttendanceDateTS() {
		return attendanceDateTS;
	}

	/**
	 * @param attendanceDateTS the attendanceDateTS to set
	 */
	public void setAttendanceDateTS(Integer attendanceDateTS) {
		this.attendanceDateTS = attendanceDateTS;
	}

	/**
	 * @return the attendanceStatus
	 */
	public AttendanceStatus getAttendanceStatus() {
		return attendanceStatus;
	}

	/**
	 * @param attendanceStatus the attendanceStatus to set
	 */
	public void setAttendanceStatus(AttendanceStatus attendanceStatus) {
		this.attendanceStatus = attendanceStatus;
	}

	/**
	 * @return the createdBy
	 */
	public UUID getCreatedBy() {
		return createdBy;
	}

	/**
	 * @param createdBy the createdBy to set
	 */
	public void setCreatedBy(UUID createdBy) {
		this.createdBy = createdBy;
	}

	/**
	 * @return the updatedBy
	 */
	public UUID getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(UUID updatedBy) {
		this.updatedBy = updatedBy;
	}

	/**
	 * @return the remarks
	 */
	public String getRemarks() {
		return remarks;
	}

	/**
	 * @param remarks the remarks to set
	 */
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	/**
	 * @return the createdAt
	 */
	public Integer getCreatedAt() {
		return createdAt;
	}

	/**
	 * @param createdAt the createdAt to set
	 */
	public void setCreatedAt(Integer createdAt) {
		this.createdAt = createdAt;
	}

	/**
	 * @return the updatedAt
	 */
	public Integer getUpdatedAt() {
		return updatedAt;
	}

	/**
	 * @param updatedAt the updatedAt to set
	 */
	public void setUpdatedAt(Integer updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	public String toString() {
		return "StudentAttendanceRegisterPayload [instituteId=" + instituteId + ", studentId=" + studentId
				+ ", academicSessionId=" + academicSessionId + ", attendanceTypeId=" + attendanceTypeId
				+ ", attendanceDateTS=" + attendanceDateTS + ", attendanceStatus=" + attendanceStatus + ", createdBy="
				+ createdBy + ", updatedBy=" + updatedBy + ", remarks=" + remarks + ", createdAt=" + createdAt
				+ ", updatedAt=" + updatedAt + "]";
	}
	
}
