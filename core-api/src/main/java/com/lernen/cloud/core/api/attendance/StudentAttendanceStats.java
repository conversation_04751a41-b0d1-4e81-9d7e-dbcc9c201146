package com.lernen.cloud.core.api.attendance;

import java.time.Month;
import java.util.Map;

public class StudentAttendanceStats {

    private int instituteId;

    private int academicSessionId;

    private AttendanceStatus todayAttendance;

    private Map<AttendanceStatus, Double> totalAttendanceStatusMap;

    private Map<Month, Map<AttendanceStatus, Double>> monthlyAttendanceStatusMap;

    public StudentAttendanceStats(int instituteId, int academicSessionId, AttendanceStatus todayAttendance, Map<AttendanceStatus, Double> totalAttendanceStatusMap, Map<Month, Map<AttendanceStatus, Double>> monthlyAttendanceStatusMap) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.todayAttendance = todayAttendance;
        this.totalAttendanceStatusMap = totalAttendanceStatusMap;
        this.monthlyAttendanceStatusMap = monthlyAttendanceStatusMap;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public void setAcademicSessionId(int academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public Map<AttendanceStatus, Double> getTotalAttendanceStatusMap() {
        return totalAttendanceStatusMap;
    }

    public void setTotalAttendanceStatusMap(Map<AttendanceStatus, Double> totalAttendanceStatusMap) {
        this.totalAttendanceStatusMap = totalAttendanceStatusMap;
    }

    public Map<Month, Map<AttendanceStatus, Double>> getMonthlyAttendanceStatusMap() {
        return monthlyAttendanceStatusMap;
    }

    public void setMonthlyAttendanceStatusMap(Map<Month, Map<AttendanceStatus, Double>> monthlyAttendanceStatusMap) {
        this.monthlyAttendanceStatusMap = monthlyAttendanceStatusMap;
    }

    public AttendanceStatus getTodayAttendance() {
        return todayAttendance;
    }

    public void setTodayAttendance(AttendanceStatus todayAttendance) {
        this.todayAttendance = todayAttendance;
    }

    @Override
    public String toString() {
        return "StudentAttendanceStats{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", todayAttendance=" + todayAttendance +
                ", totalAttendanceStatusMap=" + totalAttendanceStatusMap +
                ", monthlyAttendanceStatusMap=" + monthlyAttendanceStatusMap +
                '}';
    }
}
