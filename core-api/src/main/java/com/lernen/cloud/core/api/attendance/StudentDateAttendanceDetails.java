package com.lernen.cloud.core.api.attendance;

import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentDateAttendanceDetails {
    private final int instituteId;

    private final int academicSessionId;

    private final int attendanceDay;

    private final Integer attendanceType;

    private final double totalAttendance;

    private final double totalPresentAttendance;

    private final double totalAbsentAttendance;

    private final double totalLeaveAttendance;

    private final double totalHalfDayAttendance;

    private final double totalHoliday;

    private final double unmarkedDays;

    private final List<ClassAttendanceAggregatedData> classAttendanceAggregatedDataList;

    private final List<AttendanceType> attendanceTypeList;

    private final double totalPresentPercentage;

    public StudentDateAttendanceDetails(int instituteId, int academicSessionId, int attendanceDay, Integer attendanceType, double totalAttendance, double totalPresentAttendance, double totalAbsentAttendance, double totalLeaveAttendance, double totalHalfDayAttendance, double totalHoliday, double unmarkedDays, List<ClassAttendanceAggregatedData> classAttendanceAggregatedDataList, List<AttendanceType> attendanceTypeList, double totalPresentPercentage) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.attendanceDay = attendanceDay;
        this.attendanceType = attendanceType;
        this.totalAttendance = totalAttendance;
        this.totalPresentAttendance = totalPresentAttendance;
        this.totalAbsentAttendance = totalAbsentAttendance;
        this.totalLeaveAttendance = totalLeaveAttendance;
        this.totalHalfDayAttendance = totalHalfDayAttendance;
        this.totalHoliday = totalHoliday;
        this.unmarkedDays = unmarkedDays;
        this.classAttendanceAggregatedDataList = classAttendanceAggregatedDataList;
        this.attendanceTypeList = attendanceTypeList;
		this.totalPresentPercentage = totalPresentPercentage;
	}

    public int getInstituteId() {
        return instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public int getAttendanceDay() {
        return attendanceDay;
    }

    public Integer getAttendanceType() {
        return attendanceType;
    }

    public double getTotalAttendance() {
        return totalAttendance;
    }

    public double getTotalPresentAttendance() {
        return totalPresentAttendance;
    }

    public double getTotalAbsentAttendance() {
        return totalAbsentAttendance;
    }

    public double getTotalLeaveAttendance() {
        return totalLeaveAttendance;
    }

    public double getTotalHalfDayAttendance() {
        return totalHalfDayAttendance;
    }

    public double getTotalHoliday() {
        return totalHoliday;
    }

    public double getUnmarkedDays() {
        return unmarkedDays;
    }

    public List<ClassAttendanceAggregatedData> getClassAttendanceAggregatedDataList() {
        return classAttendanceAggregatedDataList;
    }

    public List<AttendanceType> getAttendanceTypeList() {
        return attendanceTypeList;
    }

    public double getTotalPresentPercentage() { return totalPresentPercentage;}

    @Override
    public String toString() {
        return "StudentDateAttendanceDetails{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", attendanceDay=" + attendanceDay +
                ", attendanceType=" + attendanceType +
                ", totalAttendance=" + totalAttendance +
                ", totalPresentAttendance=" + totalPresentAttendance +
                ", totalAbsentAttendance=" + totalAbsentAttendance +
                ", totalLeaveAttendance=" + totalLeaveAttendance +
                ", totalHalfDayAttendance=" + totalHalfDayAttendance +
                ", totalHoliday=" + totalHoliday +
                ", unmarkedDays=" + unmarkedDays +
                ", classAttendanceAggregatedDataList=" + classAttendanceAggregatedDataList +
                ", attendanceTypeList=" + attendanceTypeList +
                ", totalPresentPercentage=" + totalPresentPercentage +
                '}';
    }
}
