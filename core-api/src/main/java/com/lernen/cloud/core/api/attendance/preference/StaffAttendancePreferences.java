package com.lernen.cloud.core.api.attendance.preference;

import com.lernen.cloud.core.api.attendance.AttendanceSeason;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;

import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class StaffAttendancePreferences {

	public static final String ADMIN_STAFF_ATTENDANCE_SMS_USER_NAMES = "admin_staff_attendance_sms_user_names";
	public static final String ADMIN_STAFF_ATTENDANCE_SMS_TEMPLATE_ID = "admin_staff_attendance_sms_template_id";

	public static String getConfigType() {
		return "staff_attendance_preferences";
	}

	private Set<String> adminStaffAttendanceSMSUserNames;
	private UUID adminStaffAttendanceSMSTemplateId;

	public Set<String> getAdminStaffAttendanceSMSUserNames() {
		return adminStaffAttendanceSMSUserNames;
	}

	public void setAdminStaffAttendanceSMSUserNames(Set<String> adminStaffAttendanceSMSUserNames) {
		this.adminStaffAttendanceSMSUserNames = adminStaffAttendanceSMSUserNames;
	}

	public UUID getAdminStaffAttendanceSMSTemplateId() {
		return adminStaffAttendanceSMSTemplateId;
	}

	public void setAdminStaffAttendanceSMSTemplateId(UUID adminStaffAttendanceSMSTemplateId) {
		this.adminStaffAttendanceSMSTemplateId = adminStaffAttendanceSMSTemplateId;
	}

	@Override
	public String toString() {
		return "StaffAttendancePreferences{" +
				"adminStaffAttendanceSMSUserNames=" + adminStaffAttendanceSMSUserNames +
				", adminStaffAttendanceSMSTemplateId='" + adminStaffAttendanceSMSTemplateId + '\'' +
				'}';
	}
}
