package com.lernen.cloud.core.api.attendance.staff;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class StaffAttendanceLog {

    private final UUID staffId;

    private final List<StaffAttendanceDayLog> staffAttendanceDayLogs;

    public StaffAttendanceLog(UUID staffId, List<StaffAttendanceDayLog> staffAttendanceDayLogs) {
        this.staffId = staffId;
        this.staffAttendanceDayLogs = staffAttendanceDayLogs;
    }

    public UUID getStaffId() {
        return staffId;
    }

    public List<StaffAttendanceDayLog> getStaffAttendanceDayLogs() {
        return staffAttendanceDayLogs;
    }

    @Override
    public String toString() {
        return "StaffAttendanceLog{" +
                "staffId=" + staffId +
                ", staffAttendanceDayLogs=" + staffAttendanceDayLogs +
                '}';
    }
}
