package com.lernen.cloud.core.api.attendance.staff;

import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffTimingDetails;

/**
 * <AUTHOR>
 */
public class StaffAttendanceRegisterDetails {

    private final int instituteId;

    private final Integer attendanceDate;

    private final Staff staff;

    private final Double totalDuration;

    private final StaffAttendanceStatus staffAttendanceStatus;

    private final StaffTimingDetails staffTimingDetails;

    public StaffAttendanceRegisterDetails(int instituteId, Integer attendanceDate, Staff staff, Double totalDuration, StaffAttendanceStatus staffAttendanceStatus, StaffTimingDetails staffTimingDetails) {
        this.instituteId = instituteId;
        this.attendanceDate = attendanceDate;
        this.staff = staff;
        this.totalDuration = totalDuration;
        this.staffAttendanceStatus = staffAttendanceStatus;
        this.staffTimingDetails = staffTimingDetails;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public Integer getAttendanceDate() {
        return attendanceDate;
    }

    public Staff getStaff() {
        return staff;
    }

    public Double getTotalDuration() {
        return totalDuration;
    }

    public StaffAttendanceStatus getStaffAttendanceStatus() {
        return staffAttendanceStatus;
    }

    public StaffTimingDetails getStaffTimingDetails() {
        return staffTimingDetails;
    }

    @Override
    public String toString() {
        return "StaffAttendanceRegisterDetails{" +
                "instituteId=" + instituteId +
                ", attendanceDate=" + attendanceDate +
                ", staff=" + staff +
                ", totalDuration=" + totalDuration +
                ", staffAttendanceStatus=" + staffAttendanceStatus +
                ", staffTimingDetails=" + staffTimingDetails +
                '}';
    }
}

