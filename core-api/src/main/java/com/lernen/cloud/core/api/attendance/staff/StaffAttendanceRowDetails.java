package com.lernen.cloud.core.api.attendance.staff;

import com.lernen.cloud.core.api.institute.Time;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffTimingDetails;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class StaffAttendanceRowDetails {

    private int instituteId;

    private Integer attendanceDate;

    private Staff staff;

    private Double totalDuration;

    private StaffAttendanceStatus staffAttendanceStatus;

    private StaffTimingDetails staffTimingDetails;

    private StaffAttendanceType staffAttendanceType;

    private Time timeOfAction;

    private UUID addedBy;

    private Integer addedAt;

    private String remarks;

    public StaffAttendanceRowDetails(int instituteId, Integer attendanceDate, Staff staff, Double totalDuration, StaffAttendanceStatus staffAttendanceStatus, StaffTimingDetails staffTimingDetails, StaffAttendanceType staffAttendanceType, Time timeOfAction, UUID addedBy, Integer addedAt, String remarks) {
        this.instituteId = instituteId;
        this.attendanceDate = attendanceDate;
        this.staff = staff;
        this.totalDuration = totalDuration;
        this.staffAttendanceStatus = staffAttendanceStatus;
        this.staffTimingDetails = staffTimingDetails;
        this.staffAttendanceType = staffAttendanceType;
        this.timeOfAction = timeOfAction;
        this.addedBy = addedBy;
        this.addedAt = addedAt;
        this.remarks = remarks;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public Integer getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(Integer attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public Staff getStaff() {
        return staff;
    }

    public void setStaff(Staff staff) {
        this.staff = staff;
    }

    public Double getTotalDuration() {
        return totalDuration;
    }

    public void setTotalDuration(Double totalDuration) {
        this.totalDuration = totalDuration;
    }

    public StaffAttendanceStatus getStaffAttendanceStatus() {
        return staffAttendanceStatus;
    }

    public void setStaffAttendanceStatus(StaffAttendanceStatus staffAttendanceStatus) {
        this.staffAttendanceStatus = staffAttendanceStatus;
    }

    public StaffTimingDetails getStaffTimingDetails() {
        return staffTimingDetails;
    }

    public void setStaffTimingDetails(StaffTimingDetails staffTimingDetails) {
        this.staffTimingDetails = staffTimingDetails;
    }

    public StaffAttendanceType getStaffAttendanceType() {
        return staffAttendanceType;
    }

    public void setStaffAttendanceType(StaffAttendanceType staffAttendanceType) {
        this.staffAttendanceType = staffAttendanceType;
    }

    public Time getTimeOfAction() {
        return timeOfAction;
    }

    public void setTimeOfAction(Time timeOfAction) {
        this.timeOfAction = timeOfAction;
    }

    public UUID getAddedBy() {
        return addedBy;
    }

    public void setAddedBy(UUID addedBy) {
        this.addedBy = addedBy;
    }

    public Integer getAddedAt() {
        return addedAt;
    }

    public void setAddedAt(Integer addedAt) {
        this.addedAt = addedAt;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @Override
    public String toString() {
        return "StaffAttendanceRowDetails{" +
                "instituteId=" + instituteId +
                ", attendanceDate=" + attendanceDate +
                ", staff=" + staff +
                ", totalDuration=" + totalDuration +
                ", staffAttendanceStatus=" + staffAttendanceStatus +
                ", staffTimingDetails=" + staffTimingDetails +
                ", staffAttendanceType=" + staffAttendanceType +
                ", timeOfAction=" + timeOfAction +
                ", addedBy=" + addedBy +
                ", addedAt=" + addedAt +
                ", remarks='" + remarks + '\'' +
                '}';
    }
}
