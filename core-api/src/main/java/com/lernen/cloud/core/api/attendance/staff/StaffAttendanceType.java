package com.lernen.cloud.core.api.attendance.staff;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public enum StaffAttendanceType {

    IN, OUT;

    public static StaffAttendanceType getStaffAttendanceType(String staffAttendanceType) {
        if (StringUtils.isBlank(staffAttendanceType)) {
            return null;
        }
        for (StaffAttendanceType staffAttendanceTypeEnum : StaffAttendanceType.values()) {
            if (staffAttendanceTypeEnum.name().equalsIgnoreCase(staffAttendanceType)) {
                return staffAttendanceTypeEnum;
            }
        }
        return null;
    }
}
