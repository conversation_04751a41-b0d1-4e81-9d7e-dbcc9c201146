package com.lernen.cloud.core.api.attendance.staff.v2;

import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceStatus;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceType;
import com.lernen.cloud.core.api.staff.StaffTimingDetails;

import java.util.List;

/**
 * <AUTHOR>
 */

public class StaffAttendanceDaySummary implements Comparable<StaffAttendanceDaySummary>{

    private final Integer attendanceDate;

    private final Double totalDurationInSec;

    private final StaffAttendanceStatus staffAttendanceStatus;

    private final StaffAttendanceDayMetadata staffAttendanceDayMetadata;

    private final String remarks;

    public StaffAttendanceDaySummary(Integer attendanceDate, Double totalDurationInSec, StaffAttendanceStatus staffAttendanceStatus, StaffAttendanceDayMetadata staffAttendanceDayMetadata, String remarks) {
        this.attendanceDate = attendanceDate;
        this.totalDurationInSec = totalDurationInSec;
        this.staffAttendanceStatus = staffAttendanceStatus;
        this.staffAttendanceDayMetadata = staffAttendanceDayMetadata;
        this.remarks = remarks;
    }

    public Integer getAttendanceDate() {
        return attendanceDate;
    }

    public Double getTotalDurationInSec() {
        return totalDurationInSec;
    }

    public StaffAttendanceStatus getStaffAttendanceStatus() {
        return staffAttendanceStatus;
    }

    public StaffAttendanceDayMetadata getStaffAttendanceDayMetadata() {
        return staffAttendanceDayMetadata;
    }

    public String getRemarks() {
        return remarks;
    }

    @Override
    public String toString() {
        return "StaffAttendanceDaySummary{" +
                "attendanceDate=" + attendanceDate +
                ", totalDurationInSec=" + totalDurationInSec +
                ", staffAttendanceStatus=" + staffAttendanceStatus +
                ", staffAttendanceDayMetadata=" + staffAttendanceDayMetadata +
                ", remarks='" + remarks + '\'' +
                '}';
    }

    @Override
    public int compareTo(StaffAttendanceDaySummary o) {
        if(getAttendanceDate() == null){
            if(o.getAttendanceDate() == null){
                return 0;
            }
            return 1;
        }

        if(o.getAttendanceDate() == null){
            return -1;
        }

        return getAttendanceDate() - o.getAttendanceDate();
    }
}
