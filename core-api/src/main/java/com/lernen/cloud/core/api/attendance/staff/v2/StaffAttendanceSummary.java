package com.lernen.cloud.core.api.attendance.staff.v2;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class StaffAttendanceSummary {

    private final UUID staffId;

    private final List<StaffAttendanceDaySummary> staffAttendanceDaySummaryList;

    public StaffAttendanceSummary(UUID staffId, List<StaffAttendanceDaySummary> staffAttendanceDaySummaryList) {
        this.staffId = staffId;
        this.staffAttendanceDaySummaryList = staffAttendanceDaySummaryList;
    }

    public UUID getStaffId() {
        return staffId;
    }

    public List<StaffAttendanceDaySummary> getStaffAttendanceDaySummaryList() {
        return staffAttendanceDaySummaryList;
    }

    @Override
    public String toString() {
        return "StaffAttendanceSummary{" +
                "staffId=" + staffId +
                ", staffAttendanceDaySummaryList=" + staffAttendanceDaySummaryList +
                '}';
    }
}
