package com.lernen.cloud.core.api.attendance.staff.v2;

import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceStatus;
import com.lernen.cloud.core.api.staff.Staff;

import java.util.UUID;

/**
 * <AUTHOR>
 */

public class StaffIdAttendanceRegisterRow {

    private final UUID staffId;

    private final Integer attendanceDate;

    private final Double totalDurationInSec;

    private final StaffAttendanceStatus staffAttendanceStatus;

    private final StaffAttendanceDayMetadata staffAttendanceDayMetadata;

    private final String remarks;

    public StaffIdAttendanceRegisterRow(UUID staffId, Integer attendanceDate, Double totalDurationInSec, StaffAttendanceStatus staffAttendanceStatus, StaffAttendanceDayMetadata staffAttendanceDayMetadata, String remarks) {
        this.staffId = staffId;
        this.attendanceDate = attendanceDate;
        this.totalDurationInSec = totalDurationInSec;
        this.staffAttendanceStatus = staffAttendanceStatus;
        this.staffAttendanceDayMetadata = staffAttendanceDayMetadata;
        this.remarks = remarks;
    }

    public UUID getStaffId() {
        return staffId;
    }

    public Integer getAttendanceDate() {
        return attendanceDate;
    }

    public Double getTotalDurationInSec() {
        return totalDurationInSec;
    }

    public StaffAttendanceStatus getStaffAttendanceStatus() {
        return staffAttendanceStatus;
    }

    public StaffAttendanceDayMetadata getStaffAttendanceDayMetadata() {
        return staffAttendanceDayMetadata;
    }

    public String getRemarks() {
        return remarks;
    }

    @Override
    public String toString() {
        return "StaffIdAttendanceRegisterRow{" +
                "staffId=" + staffId +
                ", attendanceDate=" + attendanceDate +
                ", totalDurationInSec=" + totalDurationInSec +
                ", staffAttendanceStatus=" + staffAttendanceStatus +
                ", staffAttendanceDayMetadata=" + staffAttendanceDayMetadata +
                ", remarks='" + remarks + '\'' +
                '}';
    }
}

