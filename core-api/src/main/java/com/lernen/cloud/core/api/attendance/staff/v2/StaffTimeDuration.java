package com.lernen.cloud.core.api.attendance.staff.v2;

import com.lernen.cloud.core.api.institute.Time;

/**
 * <AUTHOR>
 */

public class StaffTimeDuration {

    private Time inTime;

    private Time outTime;

    public StaffTimeDuration() {
    }

    public StaffTimeDuration(Time inTime, Time outTime) {
        this.inTime = inTime;
        this.outTime = outTime;
    }

    public Time getInTime() {
        return inTime;
    }

    public void setInTime(Time inTime) {
        this.inTime = inTime;
    }

    public Time getOutTime() {
        return outTime;
    }

    public void setOutTime(Time outTime) {
        this.outTime = outTime;
    }

    @Override
    public String toString() {
        return "StaffTimeDuration{" +
                "inTime=" + inTime +
                ", outTime=" + outTime +
                '}';
    }
}
