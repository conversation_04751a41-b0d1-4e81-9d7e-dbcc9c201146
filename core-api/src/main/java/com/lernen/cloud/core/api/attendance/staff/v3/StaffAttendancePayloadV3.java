package com.lernen.cloud.core.api.attendance.staff.v3;

import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendanceInput;

import java.util.List;

public class StaffAttendancePayloadV3 {
    private Integer attendanceDate;

    private List<StaffAttendanceInputV3> staffAttendanceInputList;

    public StaffAttendancePayloadV3() {

    }

    public StaffAttendancePayloadV3(Integer attendanceDate, List<StaffAttendanceInputV3> staffAttendanceInputList) {
        this.attendanceDate = attendanceDate;
        this.staffAttendanceInputList = staffAttendanceInputList;
    }

    public Integer getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(Integer attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public List<StaffAttendanceInputV3> getStaffAttendanceInputList() {
        return staffAttendanceInputList;
    }

    public void setStaffAttendanceInputList(List<StaffAttendanceInputV3> staffAttendanceInputList) {
        this.staffAttendanceInputList = staffAttendanceInputList;
    }

    @Override
    public String toString() {
        return "StaffAttendancePayloadV3{" +
                "attendanceDate=" + attendanceDate +
                ", staffAttendanceInputList=" + staffAttendanceInputList +
                '}';
    }
}
