package com.lernen.cloud.core.api.attendance.staff.v3;

import java.util.List;

public class StaffMarkAttendancePayload {
    private Integer attendanceDate;

    private List<StaffAttendanceStatusInput> staffAttendanceStatusInputs;

    public StaffMarkAttendancePayload() {

    }

    public StaffMarkAttendancePayload(Integer attendanceDate, List<StaffAttendanceStatusInput> staffAttendanceStatusInputs) {
        this.attendanceDate = attendanceDate;
        this.staffAttendanceStatusInputs = staffAttendanceStatusInputs;
    }

    public Integer getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(Integer attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public List<StaffAttendanceStatusInput> getStaffAttendanceStatusInputs() {
        return staffAttendanceStatusInputs;
    }

    public void setStaffAttendanceStatusInputs(List<StaffAttendanceStatusInput> staffAttendanceStatusInputs) {
        this.staffAttendanceStatusInputs = staffAttendanceStatusInputs;
    }

    @Override
    public String toString() {
        return "StaffMarkAttendancePayload{" +
                "attendanceDate=" + attendanceDate +
                ", staffAttendanceStatusInputs=" + staffAttendanceStatusInputs +
                '}';
    }
}
