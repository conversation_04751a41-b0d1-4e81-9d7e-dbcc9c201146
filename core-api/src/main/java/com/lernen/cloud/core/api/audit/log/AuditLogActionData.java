package com.lernen.cloud.core.api.audit.log;

import java.util.Map;

import com.lernen.cloud.core.api.user.Module;

/**
 * 
 * <AUTHOR>
 *
 */
public class AuditLogActionData {

	private final AuditLogAction auditLogAction;

	private final Map<String, AuditLogVariableValue> auditLogVariableMap;

	public AuditLogActionData(AuditLogAction auditLogAction,
			Map<String, AuditLogVariableValue> auditLogVariableMap) {
		this.auditLogAction = auditLogAction;
		this.auditLogVariableMap = auditLogVariableMap;
	}

	public AuditLogAction getAuditLogAction() {
		return auditLogAction;
	}

	public String getAuditLogActionDisplay() {
		return auditLogAction.getDisplayName();
	}

	public AuditLogActionType getLogActionType() {
		return auditLogAction.getAuditLogActionType();
	}

	public Module getModule() {
		return auditLogAction.getModule();
	}

	public Map<String, AuditLogVariableValue> getAuditLogVariableMap() {
		return auditLogVariableMap;
	}

	@Override
	public String toString() {
		return "AuditLogActionData [auditLogAction=" + auditLogAction
				+ ", auditLogVariableMap=" + auditLogVariableMap + "]";
	}

}
