package com.lernen.cloud.core.api.audit.log;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class AuditLogPayload {

	private final int instituteId;

	private final UUID userId;

	private final AuditLogActionData auditLogActionData;

	private final String miniLogStatement;

	private final String detailedLogStatement;

	public AuditLogPayload(int instituteId, UUID userId,
			AuditLogActionData auditLogActionData, String miniLogStatement,
			String detailedLogStatement) {
		this.instituteId = instituteId;
		this.userId = userId;
		this.auditLogActionData = auditLogActionData;
		this.miniLogStatement = miniLogStatement;
		this.detailedLogStatement = detailedLogStatement;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public UUID getUserId() {
		return userId;
	}

	public AuditLogActionData getAuditLogActionData() {
		return auditLogActionData;
	}

	public String getMiniLogStatement() {
		return miniLogStatement;
	}

	public String getDetailedLogStatement() {
		return detailedLogStatement;
	}

	@Override
	public String toString() {
		return "AuditLogPayload [instituteId=" + instituteId + ", userId="
				+ userId + ", auditLogActionData=" + auditLogActionData
				+ ", miniLogStatement=" + miniLogStatement
				+ ", detailedLogStatement=" + detailedLogStatement + "]";
	}

}
