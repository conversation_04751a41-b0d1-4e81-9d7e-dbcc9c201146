package com.lernen.cloud.core.api.common;

/**
 * 
 * <AUTHOR>
 *
 */
public enum BooleanDisplay {

	YES(true, "Yes"), NO(false, "No"), NA(null, "NA");

	private Boolean booleanStatus;
	private String display;

	private BooleanDisplay(Boolean booleanStatus, String display) {
		this.booleanStatus = booleanStatus;
		this.display = display;
	}

	public Boolean getBooleanStatus() {
		return booleanStatus;
	}

	public String getDisplay() {
		return display;
	}

	public static BooleanDisplay getBooleanDisplay(Boolean bool) {

		for (BooleanDisplay booleanDisplay : BooleanDisplay.values()) {
			if (booleanDisplay.getBooleanStatus() == bool) {
				return booleanDisplay;
			}
		}
		return null;
	}
}
