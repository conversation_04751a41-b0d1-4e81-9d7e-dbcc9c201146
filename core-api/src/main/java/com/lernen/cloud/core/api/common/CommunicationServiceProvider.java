package com.lernen.cloud.core.api.common;

/**
 *
 * <AUTHOR>
 *
 */
public enum CommunicationServiceProvider {
	MSG91_SMS(DeliveryMode.SMS), PC_EXPERT_SMS(DeliveryMode.SMS), WEBPAY_SMS(DeliveryMode.SMS), <PERSON><PERSON><PERSON><PERSON>_AUDIO_VOICECALL(DeliveryMode.CALL), DATAGEN_TEXT_VOICECALL(DeliveryMode.CALL), DLT_SMS(DeliveryMode.SMS),
	WEBPAY_WHATSAPP(DeliveryMode.WHATSAPP);

	private final DeliveryMode deliveryMode;

	CommunicationServiceProvider(DeliveryMode deliveryMode) {
		this.deliveryMode = deliveryMode;
	}

	public DeliveryMode getDeliveryMode() {
		return deliveryMode;
	}

	@Override
	public String toString() {
		return "CommunicationServiceProvider{" +
				"deliveryMode=" + deliveryMode +
				'}';
	}
}

