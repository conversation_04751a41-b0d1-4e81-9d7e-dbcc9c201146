package com.lernen.cloud.core.api.common;

/**
 * 
 * Atomic operations :
 * https://dev.mysql.com/doc/refman/8.0/en/innodb-locking-reads.html
 * 
 * <AUTHOR>
 *
 */
public enum DBLockMode {

	FOR_SHARE(" LOCK IN SHARE MODE "), FOR_UPDATE(" FOR UPDATE "), NONE("");

	private final String command;

	private DBLockMode(String command) {
		this.command = command;
	}

	public String getCommand() {
		return command;
	}

}
