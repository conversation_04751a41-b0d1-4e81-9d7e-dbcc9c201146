package com.lernen.cloud.core.api.common;

import org.apache.commons.lang3.StringUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public enum DeliveryMode {
	
	EMAIL, SMS, CALL, HTTP, APP, WHATSAPP;
	
	public static DeliveryMode getDeliveryMode(String deliveryMode) {
		if (StringUtils.isBlank(deliveryMode)) {
			return null;
		}
		for (DeliveryMode deliveryModeEnum : DeliveryMode.values()) {
			if (deliveryModeEnum.name().equalsIgnoreCase(deliveryMode)) {
				return deliveryModeEnum;
			}
		}
		return null;
	}
}
