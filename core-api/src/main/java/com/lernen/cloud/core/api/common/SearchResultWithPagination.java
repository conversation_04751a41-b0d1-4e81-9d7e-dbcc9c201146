package com.lernen.cloud.core.api.common;

import java.util.List;

public class SearchResultWithPagination<T> {

	private PaginationInfo paginationInfo;

	private List<T> result;

	public SearchResultWithPagination(PaginationInfo paginationInfo, List<T> result) {
		this.paginationInfo = paginationInfo;
		this.result = result;
	}

	public PaginationInfo getPaginationInfo() {
		return paginationInfo;
	}

	public void setPaginationInfo(PaginationInfo paginationInfo) {
		this.paginationInfo = paginationInfo;
	}

	public List<T> getResult() {
		return result;
	}

	public void setResult(List<T> result) {
		this.result = result;
	}

	@Override
	public String toString() {
		return "SearchResultWithPagination [paginationInfo=" + paginationInfo + ", result=" + result + "]";
	}

}
