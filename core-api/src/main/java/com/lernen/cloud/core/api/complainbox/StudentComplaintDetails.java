package com.lernen.cloud.core.api.complainbox;

import java.util.List;

public class StudentComplaintDetails {
    private StudentComplaintMetadataDetails studentComplaintMetadata;
    private List<StudentComplaintResponses> studentComplaintResponsesList;

    public StudentComplaintDetails() {

    }


    public StudentComplaintDetails(StudentComplaintMetadataDetails studentComplaintMetadata, List<StudentComplaintResponses> studentComplaintResponsesList) {
        this.studentComplaintMetadata = studentComplaintMetadata;
        this.studentComplaintResponsesList = studentComplaintResponsesList;
    }

    
    public StudentComplaintMetadataDetails getStudentComplaintMetadata() {
        return studentComplaintMetadata;
    }


    public void setStudentComplaintMetadata(StudentComplaintMetadataDetails studentComplaintMetadata) {
        this.studentComplaintMetadata = studentComplaintMetadata;
    }

    public List<StudentComplaintResponses> getStudentComplaintResponseList() {
        return studentComplaintResponsesList;
    }


    public void setStudentComplaintResponseList(List<StudentComplaintResponses> studentComplaintResponsesList) {
        this.studentComplaintResponsesList = studentComplaintResponsesList;
    }


    @Override
    public String toString() {
        return "StudentComplaintDetails " +
                "[studentComplaintMetadata=" + studentComplaintMetadata +
                ", studentComplaintResponsesList=" + studentComplaintResponsesList +
                "]";
    }

}
