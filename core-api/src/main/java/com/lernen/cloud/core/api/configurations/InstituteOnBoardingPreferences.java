package com.lernen.cloud.core.api.configurations;

import java.util.List;

import com.embrate.cloud.core.api.onboarding.institute.InstituteOnBoardingStudentClassColumnField;
import com.embrate.cloud.core.api.onboarding.institute.InstituteOnBoardingStudentDataField;

/**
 *
 * <AUTHOR>
 *
 */
public class InstituteOnBoardingPreferences {

	public static final String STUDENT_DATA_FIELD_MAPPINGS = "student_data_field_mappings";
	public static final String STUDENT_DATA_DELIMITER = "student_data_delimiter";
	public static final String STUDENT_DATA_HEADER = "student_data_header";
	public static final String STUDENT_CLASS_COLUMN_CONFIGS = "student_class_column_configs";
	public static final String DOB_FORMAT = "dob_format";
	public static final String ADMISSION_DATE_FORMAT = "admission_date_format";

	private List<InstituteOnBoardingStudentDataField> studentDataFieldList;

	private String studentDataDelimiter;

	private boolean studentDataHeader;

	private InstituteOnBoardingStudentClassColumnField studentClassColumnField;

	private String dobFormat;

	private String admissionDateFormat;

	public static String getConfigType() {
		return "institute_onboarding_preferences";
	}

	public List<InstituteOnBoardingStudentDataField> getStudentDataFieldList() {
		return studentDataFieldList;
	}

	public void setStudentDataFieldList(
			List<InstituteOnBoardingStudentDataField> studentDataFieldList) {
		this.studentDataFieldList = studentDataFieldList;
	}

	public String getStudentDataDelimiter() {
		return studentDataDelimiter;
	}

	public void setStudentDataDelimiter(String studentDataDelimiter) {
		this.studentDataDelimiter = studentDataDelimiter;
	}

	public boolean isStudentDataHeader() {
		return studentDataHeader;
	}

	public void setStudentDataHeader(boolean studentDataHeader) {
		this.studentDataHeader = studentDataHeader;
	}

	public InstituteOnBoardingStudentClassColumnField getStudentClassColumnField() {
		return studentClassColumnField;
	}

	public void setStudentClassColumnField(
			InstituteOnBoardingStudentClassColumnField studentClassColumnField) {
		this.studentClassColumnField = studentClassColumnField;
	}

	public String getDobFormat() {
		return dobFormat;
	}

	public void setDobFormat(String dobFormat) {
		this.dobFormat = dobFormat;
	}

	public String getAdmissionDateFormat() {
		return admissionDateFormat;
	}

	public void setAdmissionDateFormat(String admissionDateFormat) {
		this.admissionDateFormat = admissionDateFormat;
	}

}
