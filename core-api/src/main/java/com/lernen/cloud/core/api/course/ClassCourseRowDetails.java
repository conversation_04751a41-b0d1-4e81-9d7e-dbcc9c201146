package com.lernen.cloud.core.api.course;

import com.lernen.cloud.core.api.institute.StandardRowDetails;

/**
 * 
 * <AUTHOR>
 *
 */
public class ClassCourseRowDetails {
	private final int academicSessionId;

	private final Course course;

	private final StandardRowDetails standardRowDetails;

	public ClassCourseRowDetails(int academicSessionId, Course course, StandardRowDetails standardRowDetails) {
		this.academicSessionId = academicSessionId;
		this.course = course;
		this.standardRowDetails = standardRowDetails;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public Course getCourse() {
		return course;
	}

	public StandardRowDetails getStandardRowDetails() {
		return standardRowDetails;
	}

	@Override
	public String toString() {
		return "ClassCourseRowDetails [academicSessionId=" + academicSessionId + ", course=" + course
				+ ", standardRowDetails=" + standardRowDetails + "]";
	}

}
