package com.lernen.cloud.core.api.course;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

public class ClassCoursesPayload {

	private int academicSessionId;
	
	private UUID standardId;
	
	private List<Course> courses;
	
	public ClassCoursesPayload() {
		
	}
	public ClassCoursesPayload(int academicSessionId, UUID standardId,  List<Course> courses) {
		this.academicSessionId = academicSessionId;
		this.standardId = standardId;
		this.courses = Course.sortCoursesBySequence(courses);
	}
	

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	public UUID getStandardId() {
		return standardId;
	}

	public void setStandardId(UUID standardId) {
		this.standardId = standardId;
	}

	public List<Course> getCourses() {
		return courses;
	}

	public void setCourses(List<Course> courses) {
		this.courses = courses;
	}
	@Override
	public String toString() {
		return "ClassCourses [academicSessionId=" + academicSessionId + ", standardId=" + standardId + ", courses="
				+ courses + "]";
	}
	
}