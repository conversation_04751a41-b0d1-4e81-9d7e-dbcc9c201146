package com.lernen.cloud.core.api.course;

import org.apache.commons.lang3.StringUtils;

public enum CourseType {
	SCHOLASTIC(0, "Scholastic"), COSCHOLASTIC(1, "Co-Scholastic");

	private final int priority;
	private final String displayName;

	private CourseType(int priority, String displayName) {
		this.priority = priority;
		this.displayName = displayName;
	}

	public int getPriority() {
		return priority;
	}

	public String getDisplayName() {
		return displayName;
	}

	public static CourseType getCourseType(String courseTypeValue) {
		if (StringUtils.isBlank(courseTypeValue)) {
			return null;
		}

		for (CourseType courseType : CourseType.values()) {
			if (courseType.name().equalsIgnoreCase(courseTypeValue)) {
				return courseType;
			}
		}
		return null;
	}
}
