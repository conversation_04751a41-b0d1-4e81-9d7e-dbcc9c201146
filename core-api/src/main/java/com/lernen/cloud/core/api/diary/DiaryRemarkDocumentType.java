/**
 * 
 */
package com.lernen.cloud.core.api.diary;

import com.lernen.cloud.core.api.common.DocumentType;

/**
 * <AUTHOR>
 *
 */
public enum DiaryRemarkDocumentType implements DocumentType{

	DIARY_REMARK_ATTACHMENTS("Diary Remark Attachments", "Diary Remark Attachments"),
	OTHER(null, "Other");

	private String documentName;
	private String displayName;
	private boolean isThumbnail;

	private DiaryRemarkDocumentType(String documentName, String displayName, boolean isThumbnail) {
		this.documentName = documentName;
		this.displayName = displayName;
		this.isThumbnail = isThumbnail;
	}

	private DiaryRemarkDocumentType(String documentName, String displayName) {
		this.documentName = documentName;
		this.displayName = displayName;
		this.isThumbnail = false;
	}
	
	@Override
	public String getDocumentName() {
		return documentName;
	}
	
	@Override
	public String getDisplayName() {
		return displayName;
	}

	@Override
	public boolean isThumbnail() { return isThumbnail; }

}
