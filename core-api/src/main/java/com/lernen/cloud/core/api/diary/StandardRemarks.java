package com.lernen.cloud.core.api.diary;

import java.util.UUID;

public class StandardRemarks {
    private int instituteId;

    private UUID standardRemarkId;

    private String title;

    private String description;

    private int categoryId;
    private RemarkUserType remarkUserType;

    private UUID addedBy;
    private Integer addedAt;

    private UUID updatedBy;
    private Integer updatedAt;

    public StandardRemarks(){

    }

    public StandardRemarks(int instituteId, UUID standardRemarkId, String title, String description, int categoryId, RemarkUserType remarkUserType, UUID addedBy, Integer addedAt, UUID updatedBy, Integer updatedAt) {
        this.instituteId = instituteId;
        this.standardRemarkId = standardRemarkId;
        this.title = title;
        this.description = description;
        this.categoryId = categoryId;
        this.remarkUserType = remarkUserType;
        this.addedBy = addedBy;
        this.addedAt = addedAt;
        this.updatedBy = updatedBy;
        this.updatedAt = updatedAt;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public UUID getStandardRemarkId() {
        return standardRemarkId;
    }

    public void setStandardRemarkId(UUID standardRemarkId) {
        this.standardRemarkId = standardRemarkId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public RemarkUserType getRemarkUserType() {
        return remarkUserType;
    }

    public void setRemarkUserType(RemarkUserType remarkUserType) {
        this.remarkUserType = remarkUserType;
    }

    public UUID getAddedBy() {
        return addedBy;
    }

    public void setAddedBy(UUID addedBy) {
        this.addedBy = addedBy;
    }

    public Integer getAddedAt() {
        return addedAt;
    }

    public void setAddedAt(Integer addedAt) {
        this.addedAt = addedAt;
    }

    public UUID getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Integer getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Integer updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "StandardRemarks{" +
                "instituteId=" + instituteId +
                ", standardRemarkId=" + standardRemarkId +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", categoryId=" + categoryId +
                ", remarkUserType=" + remarkUserType +
                ", addedBy=" + addedBy +
                ", addedAt=" + addedAt +
                ", updatedBy=" + updatedBy +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
