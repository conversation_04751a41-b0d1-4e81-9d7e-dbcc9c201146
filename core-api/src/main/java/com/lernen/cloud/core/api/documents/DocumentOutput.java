package com.lernen.cloud.core.api.documents;

import java.io.ByteArrayOutputStream;

/**
 * 
 * <AUTHOR>
 *
 */
public class DocumentOutput {

	private final String name;

	private final ByteArrayOutputStream content;

	public DocumentOutput(String name, ByteArrayOutputStream content) {
		this.name = name;
		this.content = content;
	}

	public String getName() {
		return name;
	}

	public ByteArrayOutputStream getContent() {
		return content;
	}

	@Override
	public String toString() {
		return "DocumentOutput [name=" + name + "]";
	}

}
