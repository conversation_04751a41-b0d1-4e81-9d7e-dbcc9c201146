package com.lernen.cloud.core.api.email;

/**
 * 
 * <AUTHOR>
 *
 */
public class EmailData {

	private final String fromAddress;

	private final String fromName;

	private final String subject;

	private final String textContent;

	private final String htmlContent;

	private final EmailDestination emailDestination;

	public EmailData(String fromAddress, String fromName, String subject,
			String textContent, String htmlContent,
			EmailDestination emailDestination) {
		this.fromAddress = fromAddress;
		this.fromName = fromName;
		this.subject = subject;
		this.textContent = textContent;
		this.htmlContent = htmlContent;
		this.emailDestination = emailDestination;
	}

	public String getFromAddress() {
		return fromAddress;
	}

	public String getFromName() {
		return fromName;
	}

	public String getSubject() {
		return subject;
	}

	public String getTextContent() {
		return textContent;
	}

	public String getHtmlContent() {
		return htmlContent;
	}

	public EmailDestination getEmailDestination() {
		return emailDestination;
	}

	@Override
	public String toString() {
		return "EmailData [fromAddress=" + fromAddress + ", fromName="
				+ fromName + ", subject=" + subject + ", textContent="
				+ textContent + ", htmlContent=" + htmlContent
				+ ", emailDestination=" + emailDestination + "]";
	}

}
