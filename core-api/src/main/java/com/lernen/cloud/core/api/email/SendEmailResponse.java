package com.lernen.cloud.core.api.email;

/**
 * 
 * <AUTHOR>
 *
 */
public class SendEmailResponse {

	private final String messageId;

	private final boolean success;

	private SendEmailResponse(String messageId, boolean success) {
		this.messageId = messageId;
		this.success = success;
	}

	public String getMessageId() {
		return messageId;
	}

	public boolean isSuccess() {
		return success;
	}

	public static SendEmailResponse getSuccessResponse(String messageId) {
		return new SendEmailResponse(messageId, true);
	}

	public static SendEmailResponse getFailedResponse(String messageId) {
		return new SendEmailResponse(messageId, false);
	}

	public static SendEmailResponse getFailedResponse() {
		return new SendEmailResponse(null, false);
	}

	@Override
	public String toString() {
		return "SendEmailResponse [messageId=" + messageId + ", success="
				+ success + "]";
	}

}
