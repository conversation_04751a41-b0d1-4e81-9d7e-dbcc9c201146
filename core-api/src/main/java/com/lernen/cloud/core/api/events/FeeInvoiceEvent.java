package com.lernen.cloud.core.api.events;

import com.lernen.cloud.core.api.fees.payment.FeePaymentInvoiceSummary;

/**
 * Fee invoice event
 * 
 * <AUTHOR>
 *
 */
public class FeeInvoiceEvent {

	private FeePaymentInvoiceSummary feePaymentInvoiceSummary;

	public FeeInvoiceEvent(FeePaymentInvoiceSummary feePaymentInvoiceSummary) {
		this.feePaymentInvoiceSummary = feePaymentInvoiceSummary;
	}

	public FeePaymentInvoiceSummary getFeePaymentInvoiceSummary() {
		return feePaymentInvoiceSummary;
	}

	@Override
	public String toString() {
		return "FeeInvoiceEvent [feePaymentInvoiceSummary=" + feePaymentInvoiceSummary + "]";
	}

}
