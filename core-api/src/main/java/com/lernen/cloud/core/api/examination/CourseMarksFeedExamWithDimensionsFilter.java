package com.lernen.cloud.core.api.examination;

import java.util.List;

import com.lernen.cloud.core.api.course.Course;

public class CourseMarksFeedExamWithDimensionsFilter {
    private final Course course;

    private final boolean isCoscholasticGradingEnabled;

    private final boolean isScholasticGradingEnabled;

    private final List<ExamDimensionCoursesData> examDimensionCoursesDataList;

    public CourseMarksFeedExamWithDimensionsFilter(Course course, boolean isCoscholasticGradingEnabled, boolean isScholasticGradingEnabled, List<ExamDimensionCoursesData> examDimensionCoursesDataList) {
        this.course = course;
        this.isCoscholasticGradingEnabled = isCoscholasticGradingEnabled;
        this.isScholasticGradingEnabled = isScholasticGradingEnabled;
        this.examDimensionCoursesDataList = examDimensionCoursesDataList;
    }

    public Course getCourse() {
        return course;
    }

    public boolean isCoscholasticGradingEnabled() {
        return isCoscholasticGradingEnabled;
    }

    public boolean isScholasticGradingEnabled() {
        return isScholasticGradingEnabled;
    }

    public List<ExamDimensionCoursesData> getExamDimensionCoursesDataList() {
        return examDimensionCoursesDataList;
    }

    @Override
    public String toString() {
        return "CourseMarksFeedExamWithDimensions{" +
                "course=" + course +
                ", isCoscholasticGradingEnabled=" + isCoscholasticGradingEnabled +
                ", isScholasticGradingEnabled=" + isScholasticGradingEnabled +
                ", examDimensionCoursesDataList=" + examDimensionCoursesDataList +
                '}';
    }
}
