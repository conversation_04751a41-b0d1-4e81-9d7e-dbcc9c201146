package com.lernen.cloud.core.api.examination;

import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @created_at 11/11/23 : 10:48
 **/
public class DimensionSectionMarksStatusDetails {

    private final ExamDimensionValues examDimensionValues;
    private List<SectionMarksStatusDetails> sectionMarksStatusDetailsList;
    private int sectionCount;

    public DimensionSectionMarksStatusDetails(ExamDimensionValues examDimensionValues, List<SectionMarksStatusDetails> sectionMarksStatusDetailsList) {
        this.examDimensionValues = examDimensionValues;
        this.sectionMarksStatusDetailsList = sectionMarksStatusDetailsList;
        this.sectionCount = CollectionUtils.isEmpty(this.sectionMarksStatusDetailsList) ? 0 : this.sectionMarksStatusDetailsList.size();
    }

    public ExamDimensionValues getExamDimensionValues() {
        return examDimensionValues;
    }

    public List<SectionMarksStatusDetails> getSectionMarksStatusDetailsList() {
        return sectionMarksStatusDetailsList;
    }

    public void setSectionMarksStatusDetailsList(List<SectionMarksStatusDetails> sectionMarksStatusDetailsList) {
        this.sectionMarksStatusDetailsList = sectionMarksStatusDetailsList;
    }

    public void setSectionCount(int sectionCount) {
        this.sectionCount = sectionCount;
    }

    public int getSectionCount() {
        return sectionCount;
    }

    public void setSectionCountManual() {
        this.sectionCount = CollectionUtils.isEmpty(this.sectionMarksStatusDetailsList) ? 0 : this.sectionMarksStatusDetailsList.size();
    }

    @Override
    public String toString() {
        return "DimensionSectionMarksStatusDetails{" +
                "examDimensionValues=" + examDimensionValues +
                ", sectionMarksStatusDetailsList=" + sectionMarksStatusDetailsList +
                ", sectionCount=" + sectionCount +
                '}';
    }
}
