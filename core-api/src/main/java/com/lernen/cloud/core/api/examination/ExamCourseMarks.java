package com.lernen.cloud.core.api.examination;

import java.util.List;

import com.lernen.cloud.core.api.course.Course;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamCourseMarks {

	private final Course course;

	private final List<ExamDimensionObtainedValues> examDimensionObtainedValues;

	public ExamCourseMarks(Course course, List<ExamDimensionObtainedValues> examDimensionObtainedValues) {
		this.course = course;
		this.examDimensionObtainedValues = examDimensionObtainedValues;
	}

	public Course getCourse() {
		return course;
	}

	public List<ExamDimensionObtainedValues> getExamDimensionObtainedValues() {
		return examDimensionObtainedValues;
	}
	@Override
	public String toString() {
		return "ExamCourseMarks [course=" + course + ", examDimensionObtainedValues=" + examDimensionObtainedValues
				+ "]";
	}

}
