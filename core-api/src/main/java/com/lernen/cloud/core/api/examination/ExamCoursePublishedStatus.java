/**
 * 
 */
package com.lernen.cloud.core.api.examination;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
//TODO:created an enum called studentExamDisplayDataStatus, which is basically used for storing
// display status (PUBLISH/UNPUBLISH) of student exam data, need to remove this status and use that one everywhere
public enum ExamCoursePublishedStatus {
	
	UNPUBLISHED, PUBLISHED;

	public static ExamCoursePublishedStatus getExamCoursePublishedStatus(String examCoursePublishedStatus) {
		if (StringUtils.isBlank(examCoursePublishedStatus)) {
			return null;
		}
		for (ExamCoursePublishedStatus examCoursePublishedStatusEnum : ExamCoursePublishedStatus.values()) {
			if (examCoursePublishedStatusEnum.name().equalsIgnoreCase(examCoursePublishedStatus)) {
				return examCoursePublishedStatusEnum;
			}
		}
		return null;
	}
}
