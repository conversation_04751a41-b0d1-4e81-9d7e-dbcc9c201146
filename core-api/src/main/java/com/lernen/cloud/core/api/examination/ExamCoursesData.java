package com.lernen.cloud.core.api.examination;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamCoursesData {
	private final ExamMetaData examMetaData;

	private final List<ExamCourse> examCourses;
	
	private final ExamCoursePublishedStatus examCoursePublishedStatus;

	public ExamCoursesData(ExamMetaData examMetaData, List<ExamCourse> examCourses) {
		this.examMetaData = examMetaData;
		this.examCourses = sortExamCoursesBySequence(examCourses);
		this.examCoursePublishedStatus = setExamMetaDataPublishedStatus();
	}

	public ExamMetaData getExamMetaData() {
		return examMetaData;
	}

	public List<ExamCourse> getExamCourses() {
		return examCourses;
	}
	
	/**
	 * @return the examCoursePublishedStatus
	 */
	public ExamCoursePublishedStatus getExamCoursePublishedStatus() {
		return examCoursePublishedStatus;
	}
	
	public ExamCoursePublishedStatus setExamMetaDataPublishedStatus() {
		for (ExamCourse examCourse : this.examCourses) {
			for (ExamDimensionValues examDimensionValue : examCourse.getExamDimensionValues()) {
				if(examDimensionValue.getExamCoursePublishedStatus() == ExamCoursePublishedStatus.PUBLISHED) {
					return ExamCoursePublishedStatus.PUBLISHED;
				}
			}
		}
		return ExamCoursePublishedStatus.UNPUBLISHED;
	}

	public static List<ExamCourse> sortExamCoursesBySequence(List<ExamCourse> examCourseList) {
		if(CollectionUtils.isEmpty(examCourseList)) {
			return new ArrayList<>();
		}
		Collections.sort(examCourseList, new Comparator<ExamCourse>() {
			@Override
			public int compare(ExamCourse e1, ExamCourse e2) {
				return e1.getCourse().compareTo(e2.getCourse());
			}
		});
		return examCourseList;
	}

	public static List<ExamCoursesData> sortExamCoursesDataByExamStartDate(List<ExamCoursesData> examCoursesDataList) {
		if(CollectionUtils.isEmpty(examCoursesDataList)) {
			return new ArrayList<>();
		}
		examCoursesDataList.sort(new Comparator<ExamCoursesData>() {
            @Override
            public int compare(ExamCoursesData e1, ExamCoursesData e2) {

				if(e1.getExamMetaData().getExamStartDate() == null && e2.getExamMetaData().getExamStartDate() == null) {
					return e1.getExamMetaData().getExamName().compareToIgnoreCase(e2.getExamMetaData().getExamName());
				}

				if(e1.getExamMetaData().getExamStartDate() == null) {
					return 1;
				}

				if(e2.getExamMetaData().getExamStartDate() == null) {
					return -1;
				}

				if (e1.getExamMetaData().getExamStartDate().equals(e2.getExamMetaData().getExamStartDate())) {
					return e1.getExamMetaData().getExamName().compareToIgnoreCase(e2.getExamMetaData().getExamName());
				}

				if (e1.getExamMetaData().getExamStartDate() < e2.getExamMetaData().getExamStartDate()) {
					return -1;
				}
				return 1;
            }
        });
		return examCoursesDataList;
	}

	public static List<ExamCoursesData> sortExamCoursesDataByExamName(List<ExamCoursesData> examCoursesDataList) {
		if(CollectionUtils.isEmpty(examCoursesDataList)) {
			return null;
		}
		Collections.sort(examCoursesDataList, new Comparator<ExamCoursesData>() {
			@Override
			public int compare(ExamCoursesData e1, ExamCoursesData e2) {
				return e1.getExamMetaData().getExamName().compareToIgnoreCase(e2.getExamMetaData().getExamName());
			}
		});
		return examCoursesDataList;
	}

}
