/**
 * 
 */
package com.lernen.cloud.core.api.examination;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class ExamMarksDetails {

	private final UUID courseId;
	
	private final String courseName;
	
	private final ExamGrade publishObtainedGrade;
	
	private final ExamGrade publishMaxGrade;

    private final Double publishObtainedMarks;
    
    private final Double publishGraceMarks;
    
    private final Double publishTotalMarks;

	private final ExamAttendanceStatus publishExamAttendanceStatus;

	private final int maxStars;

	private final double obtainedStars;

	private final List<ExamDimensionMarksDetails> examDimensionMarksDetailsList;

	public ExamMarksDetails(UUID courseId, String courseName, ExamGrade publishObtainedGrade, ExamGrade publishMaxGrade, Double publishObtainedMarks, Double publishGraceMarks, Double publishTotalMarks, ExamAttendanceStatus publishExamAttendanceStatus, int maxStars, double obtainedStars, List<ExamDimensionMarksDetails> examDimensionMarksDetailsList) {
		this.courseId = courseId;
		this.courseName = courseName;
		this.publishObtainedGrade = publishObtainedGrade;
		this.publishMaxGrade = publishMaxGrade;
		this.publishObtainedMarks = publishObtainedMarks;
		this.publishGraceMarks = publishGraceMarks;
		this.publishTotalMarks = publishTotalMarks;
		this.publishExamAttendanceStatus = publishExamAttendanceStatus;
		this.maxStars = maxStars;
		this.obtainedStars = obtainedStars;
		this.examDimensionMarksDetailsList = examDimensionMarksDetailsList;
	}

	/**
	 * @return the courseId
	 */
	public UUID getCourseId() {
		return courseId;
	}

	/**
	 * @return the courseName
	 */
	public String getCourseName() {
		return courseName;
	}

	/**
	 * @return the publishObtainedGradeName
	 */
	public ExamGrade getPublishObtainedGrade() {
		return publishObtainedGrade;
	}

	/**
	 * @return the publishGraceMarks
	 */
	public Double getPublishGraceMarks() {
		return publishGraceMarks;
	}

	/**
	 * @return the publishObtainedMarks
	 */
	public Double getPublishObtainedMarks() {
		return publishObtainedMarks;
	}

	/**
	 * @return the publishTotalMarks
	 */
	public Double getPublishTotalMarks() {
		return publishTotalMarks;
	}

	/**
	 * @return the examDimensionMarksDetailsList
	 */
	public List<ExamDimensionMarksDetails> getExamDimensionMarksDetailsList() {
		return examDimensionMarksDetailsList;
	}
	
	
	/**
	 * @return the publishMaxGrade
	 */
	public ExamGrade getPublishMaxGrade() {
		return publishMaxGrade;
	}

	public ExamAttendanceStatus getPublishExamAttendanceStatus() {
		return publishExamAttendanceStatus;
	}

	public int getMaxStars() {
		return maxStars;
	}

	public double getObtainedStars() {
		return obtainedStars;
	}

	@Override
	public String toString() {
		return "ExamMarksDetails{" +
				"courseId=" + courseId +
				", courseName='" + courseName + '\'' +
				", publishObtainedGrade=" + publishObtainedGrade +
				", publishMaxGrade=" + publishMaxGrade +
				", publishObtainedMarks=" + publishObtainedMarks +
				", publishGraceMarks=" + publishGraceMarks +
				", publishTotalMarks=" + publishTotalMarks +
				", publishExamAttendanceStatus=" + publishExamAttendanceStatus +
				", maxStars=" + maxStars +
				", obtainedStars=" + obtainedStars +
				", examDimensionMarksDetailsList=" + examDimensionMarksDetailsList +
				'}';
	}

}
