package com.lernen.cloud.core.api.examination;

import java.util.List;
import java.util.Set;
import java.util.UUID;

import com.lernen.cloud.core.api.course.Course;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamNodeData {

	private final ExamMetaData examMetaData;

	private final List<Course> courses;

	private final Set<UUID> childIds;

	public ExamNodeData(ExamMetaData examMetaData, List<Course> courses, Set<UUID> childIds) {
		this.examMetaData = examMetaData;
		this.courses = Course.sortCoursesBySequence(courses);
		this.childIds = childIds;
	}

	public ExamMetaData getExamMetaData() {
		return examMetaData;
	}

	public List<Course> getCourses() {
		return courses;
	}

	public Set<UUID> getChildIds() {
		return childIds;
	}

	@Override
	public String toString() {
		return "ExamNodeData [examMetaData=" + examMetaData + ", courses=" + courses + ", childIds=" + childIds + "]";
	}

	
	
}
