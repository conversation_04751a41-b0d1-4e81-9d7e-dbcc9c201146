package com.lernen.cloud.core.api.examination;

import com.lernen.cloud.core.api.examination.report.ExamResultStatus;
import com.lernen.cloud.core.api.student.StudentLite;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @created_at 23/08/24 : 21:13
 **/
public class ExamResultAnalysisDetails {

    private final Map<UUID, StudentLite> studentLiteMap;
    private final Map<UUID, Map<UUID, Double>> studentCourseObtainedMarksMap;
    private final Map<UUID, Map<UUID, Double>> studentCourseMaxMarksMap;
    private final Map<UUID, Map<UUID, Double>> studentCoursePercentageMap;
    private final Map<UUID, Map<UUID, Integer>> courseStudentRankMap;
    private final Map<UUID, Double> studentObtainedMarksMap;
    private final Map<UUID, Double> studentMaxMarksMap;
    private final Map<UUID, Double> studentPercentageMap;
    private final Map<UUID, Integer> studentRankMap;
    private final Map<UUID, Double> courseAvgMarksMap;
    private final Map<UUID, Double> courseHighMarksMap;
    private final Map<UUID, Integer> courseWiseTotalStudentCount;
    private final Map<UUID, Integer> courseWisePassStudentCount;
    private final Map<UUID, Integer> courseWiseFailStudentCount;
    private final Map<UUID, Integer> courseWiseDistinctStudentCount;
    private final Map<UUID, Map<ExamDivision, Integer>> courseWiseDivisionStudentCount;
    private final int totalStudentCount;
    private final int passStudentCount;
    private final int failStudentCount;
    private final int distinctionStudentCount;
    private final Map<ExamDivision, Integer> divisionStudentCount;
    private final double avgPercentage;
    private final double highPercentage;
    private final Map<UUID, String> studentGradeMap;
    private final Map<UUID, ExamDivision> studentDivisionMap;
    private final Map<UUID, ExamResultStatus> studentExamResultStatusMap;
    private final Map<UUID, Map<UUID, Double>> studentCourseMinMarksMap;
    private final Map<UUID, Map<UUID, ExamAttendanceStatus>> studentCourseExamAttendanceStatusMap;

    public ExamResultAnalysisDetails(Map<UUID, StudentLite> studentLiteMap, Map<UUID, Map<UUID, Double>> studentCourseObtainedMarksMap, Map<UUID, Map<UUID, Double>> studentCourseMaxMarksMap, Map<UUID, Map<UUID, Double>> studentCoursePercentageMap, Map<UUID, Map<UUID, Integer>> courseStudentRankMap, Map<UUID, Double> studentObtainedMarksMap, Map<UUID, Double> studentMaxMarksMap, Map<UUID, Double> studentPercentageMap, Map<UUID, Integer> studentRankMap, Map<UUID, Double> courseAvgMarksMap, Map<UUID, Double> courseHighMarksMap, Map<UUID, Integer> courseWiseTotalStudentCount, Map<UUID, Integer> courseWisePassStudentCount, Map<UUID, Integer> courseWiseFailStudentCount, Map<UUID, Integer> courseWiseDistinctStudentCount, Map<UUID, Map<ExamDivision, Integer>> courseWiseDivisionStudentCount, int totalStudentCount, int passStudentCount, int failStudentCount, int distinctionStudentCount, Map<ExamDivision, Integer> divisionStudentCount, double avgPercentage, double highPercentage, Map<UUID, String> studentGradeMap, Map<UUID, ExamDivision> studentDivisionMap, Map<UUID, ExamResultStatus> studentExamResultStatusMap, Map<UUID, Map<UUID, Double>> studentCourseMinMarksMap, Map<UUID, Map<UUID, ExamAttendanceStatus>> studentCourseExamAttendanceStatusMap) {
        this.studentLiteMap = studentLiteMap;
        this.studentCourseObtainedMarksMap = studentCourseObtainedMarksMap;
        this.studentCourseMaxMarksMap = studentCourseMaxMarksMap;
        this.studentCoursePercentageMap = studentCoursePercentageMap;
        this.courseStudentRankMap = courseStudentRankMap;
        this.studentObtainedMarksMap = studentObtainedMarksMap;
        this.studentMaxMarksMap = studentMaxMarksMap;
        this.studentPercentageMap = studentPercentageMap;
        this.studentRankMap = studentRankMap;
        this.courseAvgMarksMap = courseAvgMarksMap;
        this.courseHighMarksMap = courseHighMarksMap;
        this.courseWiseTotalStudentCount = courseWiseTotalStudentCount;
        this.courseWisePassStudentCount = courseWisePassStudentCount;
        this.courseWiseFailStudentCount = courseWiseFailStudentCount;
        this.courseWiseDistinctStudentCount = courseWiseDistinctStudentCount;
        this.courseWiseDivisionStudentCount = courseWiseDivisionStudentCount;
        this.totalStudentCount = totalStudentCount;
        this.passStudentCount = passStudentCount;
        this.failStudentCount = failStudentCount;
        this.distinctionStudentCount = distinctionStudentCount;
        this.divisionStudentCount = divisionStudentCount;
        this.avgPercentage = avgPercentage;
        this.highPercentage = highPercentage;
        this.studentGradeMap = studentGradeMap;
        this.studentDivisionMap = studentDivisionMap;
        this.studentExamResultStatusMap = studentExamResultStatusMap;
        this.studentCourseMinMarksMap = studentCourseMinMarksMap;
        this.studentCourseExamAttendanceStatusMap = studentCourseExamAttendanceStatusMap;
    }

    public Map<UUID, StudentLite> getStudentLiteMap() {
        return studentLiteMap;
    }

    public Map<UUID, Map<UUID, Double>> getStudentCourseObtainedMarksMap() {
        return studentCourseObtainedMarksMap;
    }

    public Map<UUID, Map<UUID, Double>> getStudentCourseMaxMarksMap() {
        return studentCourseMaxMarksMap;
    }

    public Map<UUID, Map<UUID, Double>> getStudentCoursePercentageMap() {
        return studentCoursePercentageMap;
    }

    public Map<UUID, Map<UUID, Integer>> getCourseStudentRankMap() {
        return courseStudentRankMap;
    }

    public Map<UUID, Double> getStudentObtainedMarksMap() {
        return studentObtainedMarksMap;
    }

    public Map<UUID, Double> getStudentMaxMarksMap() {
        return studentMaxMarksMap;
    }

    public Map<UUID, Double> getStudentPercentageMap() {
        return studentPercentageMap;
    }

    public Map<UUID, Integer> getStudentRankMap() {
        return studentRankMap;
    }

    public Map<UUID, Double> getCourseAvgMarksMap() {
        return courseAvgMarksMap;
    }

    public Map<UUID, Double> getCourseHighMarksMap() {
        return courseHighMarksMap;
    }

    public Map<UUID, Integer> getCourseWiseTotalStudentCount() {
        return courseWiseTotalStudentCount;
    }

    public Map<UUID, Integer> getCourseWisePassStudentCount() {
        return courseWisePassStudentCount;
    }

    public Map<UUID, Integer> getCourseWiseFailStudentCount() {
        return courseWiseFailStudentCount;
    }

    public Map<UUID, Integer> getCourseWiseDistinctStudentCount() {
        return courseWiseDistinctStudentCount;
    }

    public Map<UUID, Map<ExamDivision, Integer>> getCourseWiseDivisionStudentCount() {
        return courseWiseDivisionStudentCount;
    }

    public int getTotalStudentCount() {
        return totalStudentCount;
    }

    public int getPassStudentCount() {
        return passStudentCount;
    }

    public int getFailStudentCount() {
        return failStudentCount;
    }

    public int getDistinctionStudentCount() {
        return distinctionStudentCount;
    }

    public Map<ExamDivision, Integer> getDivisionStudentCount() {
        return divisionStudentCount;
    }

    public double getAvgPercentage() {
        return avgPercentage;
    }

    public double getHighPercentage() {
        return highPercentage;
    }

    public Map<UUID, String> getStudentGradeMap() {
        return studentGradeMap;
    }

    public Map<UUID, ExamDivision> getStudentDivisionMap() {
        return studentDivisionMap;
    }

    public Map<UUID, ExamResultStatus> getStudentExamResultStatusMap() {
        return studentExamResultStatusMap;
    }

    public Map<UUID, Map<UUID, Double>> getStudentCourseMinMarksMap() {
        return studentCourseMinMarksMap;
    }

    public Map<UUID, Map<UUID, ExamAttendanceStatus>> getStudentCourseExamAttendanceStatusMap() {
        return studentCourseExamAttendanceStatusMap;
    }

    @Override
    public String toString() {
        return "ExamResultAnalysisDetails{" +
                "studentLiteMap=" + studentLiteMap +
                ", studentCourseObtainedMarksMap=" + studentCourseObtainedMarksMap +
                ", studentCourseMaxMarksMap=" + studentCourseMaxMarksMap +
                ", studentCoursePercentageMap=" + studentCoursePercentageMap +
                ", courseStudentRankMap=" + courseStudentRankMap +
                ", studentObtainedMarksMap=" + studentObtainedMarksMap +
                ", studentMaxMarksMap=" + studentMaxMarksMap +
                ", studentPercentageMap=" + studentPercentageMap +
                ", studentRankMap=" + studentRankMap +
                ", courseAvgMarksMap=" + courseAvgMarksMap +
                ", courseHighMarksMap=" + courseHighMarksMap +
                ", courseWiseTotalStudentCount=" + courseWiseTotalStudentCount +
                ", courseWisePassStudentCount=" + courseWisePassStudentCount +
                ", courseWiseFailStudentCount=" + courseWiseFailStudentCount +
                ", courseWiseDistinctStudentCount=" + courseWiseDistinctStudentCount +
                ", courseWiseDivisionStudentCount=" + courseWiseDivisionStudentCount +
                ", totalStudentCount=" + totalStudentCount +
                ", passStudentCount=" + passStudentCount +
                ", failStudentCount=" + failStudentCount +
                ", distinctionStudentCount=" + distinctionStudentCount +
                ", divisionStudentCount=" + divisionStudentCount +
                ", avgPercentage=" + avgPercentage +
                ", highPercentage=" + highPercentage +
                ", studentGradeMap=" + studentGradeMap +
                ", studentDivisionMap=" + studentDivisionMap +
                ", studentExamResultStatusMap=" + studentExamResultStatusMap +
                ", studentCourseMinMarksMap=" + studentCourseMinMarksMap +
                ", studentCourseExamAttendanceStatusMap=" + studentCourseExamAttendanceStatusMap +
                '}';
    }
}