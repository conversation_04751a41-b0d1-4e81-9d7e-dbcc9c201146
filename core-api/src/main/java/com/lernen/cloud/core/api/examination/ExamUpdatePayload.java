package com.lernen.cloud.core.api.examination;

import com.embrate.cloud.core.api.examination.config.ExamMarksDisplayType;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamUpdatePayload {

	private UUID examId;

	private String examName;

	private ExamType examType;
	
	private MarksComputationOperation operation;

	private Integer attendanceStartDate;

	private Integer attendanceEndDate;

	private Integer examStartDate;

	private Integer dateOfResultDeclaration;

	private ExamMarksDisplayType scholasticExamMarksDisplayType;

	private ExamMarksDisplayType coScholasticExamMarksDisplayType;

	public UUID getExamId() {
		return examId;
	}

	public void setExamId(UUID examId) {
		this.examId = examId;
	}

	public String getExamName() {
		return examName;
	}

	public void setExamName(String examName) {
		this.examName = examName;
	}

	public ExamType getExamType() {
		return examType;
	}

	public void setExamType(ExamType examType) {
		this.examType = examType;
	}

	/**
	 * @return the operation
	 */
	public MarksComputationOperation getOperation() {
		return operation;
	}

	/**
	 * @param operation the operation to set
	 */
	public void setOperation(MarksComputationOperation operation) {
		this.operation = operation;
	}

	public Integer getAttendanceStartDate() {
		return attendanceStartDate;
	}

	public void setAttendanceStartDate(Integer attendanceStartDate) {
		this.attendanceStartDate = attendanceStartDate;
	}

	public Integer getAttendanceEndDate() {
		return attendanceEndDate;
	}

	public void setAttendanceEndDate(Integer attendanceEndDate) {
		this.attendanceEndDate = attendanceEndDate;
	}

	public Integer getExamStartDate() {
		return examStartDate;
	}

	public void setExamStartDate(Integer examStartDate) {
		this.examStartDate = examStartDate;
	}

	public Integer getDateOfResultDeclaration() {
		return dateOfResultDeclaration;
	}

	public void setDateOfResultDeclaration(Integer dateOfResultDeclaration) {
		this.dateOfResultDeclaration = dateOfResultDeclaration;
	}

	public ExamMarksDisplayType getScholasticExamMarksDisplayType() {
		return scholasticExamMarksDisplayType;
	}

	public void setScholasticExamMarksDisplayType(ExamMarksDisplayType scholasticExamMarksDisplayType) {
		this.scholasticExamMarksDisplayType = scholasticExamMarksDisplayType;
	}

	public ExamMarksDisplayType getCoScholasticExamMarksDisplayType() {
		return coScholasticExamMarksDisplayType;
	}

	public void setCoScholasticExamMarksDisplayType(ExamMarksDisplayType coScholasticExamMarksDisplayType) {
		this.coScholasticExamMarksDisplayType = coScholasticExamMarksDisplayType;
	}

	@Override
	public String toString() {
		return "ExamUpdatePayload{" +
				"examId=" + examId +
				", examName='" + examName + '\'' +
				", examType=" + examType +
				", operation=" + operation +
				", attendanceStartDate=" + attendanceStartDate +
				", attendanceEndDate=" + attendanceEndDate +
				", examStartDate=" + examStartDate +
				", dateOfResultDeclaration=" + dateOfResultDeclaration +
				", scholasticExamMarksDisplayType=" + scholasticExamMarksDisplayType +
				", coScholasticExamMarksDisplayType=" + coScholasticExamMarksDisplayType +
				'}';
	}
}
