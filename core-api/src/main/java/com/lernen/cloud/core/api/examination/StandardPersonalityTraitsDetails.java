package com.lernen.cloud.core.api.examination;

import com.lernen.cloud.core.api.institute.Standard;

import java.util.List;

/**
 * <AUTHOR>
 * @created_at 05/03/24 : 14:22
 **/
public class StandardPersonalityTraitsDetails {

    private final Standard standard;
    private final List<PersonalityTraitsDetails> personalityTraitsDetailsList;

    public StandardPersonalityTraitsDetails(Standard standard, List<PersonalityTraitsDetails> personalityTraitsDetailsList) {
        this.standard = standard;
        this.personalityTraitsDetailsList = PersonalityTraitsDetails.sortPersonalityTraitsDetailsBySequence(personalityTraitsDetailsList);
    }

    public Standard getStandard() {
        return standard;
    }

    public List<PersonalityTraitsDetails> getPersonalityTraitsDetailsList() {
        return personalityTraitsDetailsList;
    }

    @Override
    public String toString() {
        return "StandardPersonalityTraitsDetails{" +
                "standard=" + standard +
                ", personalityTraitsDetailsList=" + personalityTraitsDetailsList +
                '}';
    }


}
