package com.lernen.cloud.core.api.examination;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.course.CourseType;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentExamMarksDetailsLite {

	private final UUID studentId;

	private final ExamMetaData examMetaData;

	private final Map<CourseType, List<ExamDimensionValues>> examDimensionValues;

	private final List<ExamCourseMarks> examCoursesAllDimensionsMarks;

	private final Map<CourseType, List<ExamCourseMarks>> courseMarksMatrix;

	public StudentExamMarksDetailsLite(
			StudentExamMarksDetails studentExamMarksDetails) {
		this.studentId = studentExamMarksDetails.getStudent().getStudentId();
		this.examMetaData = studentExamMarksDetails.getExamMetaData();
		this.examDimensionValues = studentExamMarksDetails
				.getExamDimensionValues();
		this.examCoursesAllDimensionsMarks = studentExamMarksDetails
				.getExamCoursesAllDimensionsMarks();
		this.courseMarksMatrix = studentExamMarksDetails.getCourseMarksMatrix();
	}

	public UUID getStudentId() {
		return studentId;
	}

	public ExamMetaData getExamMetaData() {
		return examMetaData;
	}

	public Map<CourseType, List<ExamDimensionValues>> getExamDimensionValues() {
		return examDimensionValues;
	}

	public List<ExamCourseMarks> getExamCoursesAllDimensionsMarks() {
		return examCoursesAllDimensionsMarks;
	}

	public Map<CourseType, List<ExamCourseMarks>> getCourseMarksMatrix() {
		return courseMarksMatrix;
	}

	@Override
	public String toString() {
		return "StudentExamMarksDetailsLite [studentId=" + studentId
				+ ", examMetaData=" + examMetaData + ", examDimensionValues="
				+ examDimensionValues + ", examCoursesAllDimensionsMarks="
				+ examCoursesAllDimensionsMarks + ", courseMarksMatrix="
				+ courseMarksMatrix + "]";
	}

}
