package com.lernen.cloud.core.api.examination;

import com.lernen.cloud.core.api.student.StudentLite;

public class StudentMarksDetails {

    private final StudentLite studentLite;

    private final ExamDimensionObtainedValues examDimensionObtainedValues;

    public StudentMarksDetails(StudentLite student, ExamDimensionObtainedValues examDimensionObtainedValues) {
        this.studentLite = student;
        this.examDimensionObtainedValues = examDimensionObtainedValues;
    }

    public StudentLite getStudentLite() {
        return studentLite;
    }

    public ExamDimensionObtainedValues getExamDimensionObtainedValues() {
        return examDimensionObtainedValues;
    }

    @Override
    public String toString() {
        return "StudentMarksDetails{" +
                "student=" + studentLite +
                ", examDimensionObtainedValues=" + examDimensionObtainedValues +
                '}';
    }
}
