package com.lernen.cloud.core.api.examination;

import java.util.List;

import com.lernen.cloud.core.api.student.StudentLite;

/**
 * This class represents the marks details for a student in a specific dimension 
 * within a particular exam and course. It contains only the data relevant to 
 * the student's performance in the specified exam dimension.
 */

public class StudentMarksDetailsDimensionsData {
    
    private final StudentLite studentLite;

    private final List<ExamDimensionObtainedValues> examDimensionObtainedValues;

    public StudentMarksDetailsDimensionsData(StudentLite student, List<ExamDimensionObtainedValues> examDimensionObtainedValues) {
        this.studentLite = student;
        this.examDimensionObtainedValues = examDimensionObtainedValues;
    }

    public StudentLite getStudentLite() {
        return studentLite;
    }

    public List<ExamDimensionObtainedValues> getExamDimensionObtainedValues() {
        return examDimensionObtainedValues;
    }

    @Override
    public String toString() {
        return "StudentMarksDetails{" +
                "student=" + studentLite +
                ", examDimensionObtainedValues=" + examDimensionObtainedValues +
                '}';
    }
}

