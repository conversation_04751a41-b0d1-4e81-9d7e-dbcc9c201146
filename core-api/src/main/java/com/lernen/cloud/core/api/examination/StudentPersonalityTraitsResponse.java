package com.lernen.cloud.core.api.examination;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @created_at 05/03/24 : 22:40
 **/
public class StudentPersonalityTraitsResponse implements Comparable<StudentPersonalityTraitsResponse> {

    private final String response;
    private final PersonalityTraitsDetails personalityTraitsDetails;

    public StudentPersonalityTraitsResponse(String response, PersonalityTraitsDetails personalityTraitsDetails) {
        this.response = response;
        this.personalityTraitsDetails = personalityTraitsDetails;
    }

    public String getResponse() {
        return response;
    }

    public PersonalityTraitsDetails getPersonalityTraitsDetails() {
        return personalityTraitsDetails;
    }

    @Override
    public String toString() {
        return "StudentPersonalityTraitsResponse{" +
                "response='" + response + '\'' +
                ", personalityTraitsDetails=" + personalityTraitsDetails +
                '}';
    }

    @Override
    public int compareTo(StudentPersonalityTraitsResponse o) {

        if (this.personalityTraitsDetails.getPersonalityTraitSequence() == o.personalityTraitsDetails.getPersonalityTraitSequence()) {
            return compareStudentPersonalityTraitsResponseByName(o);
        }

        if (this.personalityTraitsDetails.getPersonalityTraitSequence() < o.personalityTraitsDetails.getPersonalityTraitSequence()) {
            return -1;
        }

        return 1;
    }

    public int compareStudentPersonalityTraitsResponseByName(StudentPersonalityTraitsResponse o) {
        return this.personalityTraitsDetails.getPersonalityTraitName().compareToIgnoreCase(o.personalityTraitsDetails.getPersonalityTraitName());
    }

    public static List<StudentPersonalityTraitsResponse> sortStudentPersonalityTraitsResponseBySequence(List<StudentPersonalityTraitsResponse> studentPersonalityTraitsResponseList) {
        if(CollectionUtils.isEmpty(studentPersonalityTraitsResponseList)) {
            return new ArrayList<>();
        }
        Collections.sort(studentPersonalityTraitsResponseList);
        return studentPersonalityTraitsResponseList;
    }

}
