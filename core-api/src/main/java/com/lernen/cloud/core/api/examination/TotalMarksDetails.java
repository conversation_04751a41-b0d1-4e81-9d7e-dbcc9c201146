/**
 * 
 */
package com.lernen.cloud.core.api.examination;

/**
 * <AUTHOR>
 *
 */
public class TotalMarksDetails {
	
	private final ExamGrade obtainedGrade;
	
	private final ExamGrade maxGrade;

    private final Double obtainedMarks;
    
    private final Double graceMarks;
    
    private final Double totalMarks;

	private final ExamAttendanceStatus examAttendanceStatus;

	private final int maxStars;

	private final double obtainedStars;

	/**
	 * @param obtainedGrade
	 * @param obtainedMarks
	 * @param graceMarks
	 * @param totalMarks
	 */
	public TotalMarksDetails(ExamGrade obtainedGrade, ExamGrade maxGrade, Double obtainedMarks, Double graceMarks, Double totalMarks, ExamAttendanceStatus examAttendanceStatus, int maxStars, double obtainedStars) {
		this.obtainedGrade = obtainedGrade;
		this.maxGrade = maxGrade;
		this.obtainedMarks = obtainedMarks;
		this.graceMarks = graceMarks;
		this.totalMarks = totalMarks;
		this.examAttendanceStatus = examAttendanceStatus;
		this.maxStars = maxStars;
		this.obtainedStars = obtainedStars;
	}

	/**
	 * @return the obtainedGrade
	 */
	public ExamGrade getObtainedGrade() {
		return obtainedGrade;
	}

	/**
	 * @return the obtainedMarks
	 */
	public Double getObtainedMarks() {
		return obtainedMarks;
	}

	/**
	 * @return the graceMarks
	 */
	public Double getGraceMarks() {
		return graceMarks;
	}

	/**
	 * @return the totalMarks
	 */
	public Double getTotalMarks() {
		return totalMarks;
	}

	/**
	 * @return the maxGrade
	 */
	public ExamGrade getMaxGrade() {
		return maxGrade;
	}

	public ExamAttendanceStatus getExamAttendanceStatus() {
		return examAttendanceStatus;
	}

	public int getMaxStars() {
		return maxStars;
	}

	public double getObtainedStars() {
		return obtainedStars;
	}

	@Override
	public String toString() {
		return "TotalMarksDetails{" +
				"obtainedGrade=" + obtainedGrade +
				", maxGrade=" + maxGrade +
				", obtainedMarks=" + obtainedMarks +
				", graceMarks=" + graceMarks +
				", totalMarks=" + totalMarks +
				", examAttendanceStatus=" + examAttendanceStatus +
				", maxStars=" + maxStars +
				", obtainedStars=" + obtainedStars +
				'}';
	}

}
