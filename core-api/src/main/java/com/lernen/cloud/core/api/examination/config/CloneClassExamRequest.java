package com.lernen.cloud.core.api.examination.config;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class CloneClassExamRequest {

	private int srcInstituteId;
	private int destInstituteId;

	private int srcAcademicSessionId;
	private int destAcademicSessionId;

	//these are for internal use
	private UUID srcStandardId;
	private List<UUID> destStandardId;

	//these are for user input
	private String srcStandardName;
	private List<String> destStandardName;

	private boolean cloneOnlyForScholasticSourceCourses;
	private boolean cloneOnlyForCoScholasticSourceCourses;

	public CloneClassExamRequest() {
	}


	public int getSrcInstituteId() { return srcInstituteId;}

	public void setSrcInstituteId(int srcInstituteId) { this.srcInstituteId = srcInstituteId;}

	public int getDestInstituteId() { return destInstituteId;}

	public void setDestInstituteId(int destInstituteId) { this.destInstituteId = destInstituteId;}

	public int getSrcAcademicSessionId() {
		return srcAcademicSessionId;
	}

	public void setSrcAcademicSessionId(int srcAcademicSessionId) {
		this.srcAcademicSessionId = srcAcademicSessionId;
	}

	public int getDestAcademicSessionId() {
		return destAcademicSessionId;
	}

	public void setDestAcademicSessionId(int destAcademicSessionId) {
		this.destAcademicSessionId = destAcademicSessionId;
	}

	public UUID getSrcStandardId() {
		return srcStandardId;
	}

	public void setSrcStandardId(UUID srcStandardId) {
		this.srcStandardId = srcStandardId;
	}

	public List<UUID> getDestStandardId() { return destStandardId;}

	public void setDestStandardId(List<UUID> destStandardId) { this.destStandardId = destStandardId;}

	public boolean isCloneOnlyForScholasticSourceCourses() {
		return cloneOnlyForScholasticSourceCourses;
	}

	public void setCloneOnlyForScholasticSourceCourses(boolean cloneOnlyForScholasticSourceCourses) {
		this.cloneOnlyForScholasticSourceCourses = cloneOnlyForScholasticSourceCourses;
	}

	public boolean isCloneOnlyForCoScholasticSourceCourses() {
		return cloneOnlyForCoScholasticSourceCourses;
	}

	public void setCloneOnlyForCoScholasticSourceCourses(boolean cloneOnlyForCoScholasticSourceCourses) {
		this.cloneOnlyForCoScholasticSourceCourses = cloneOnlyForCoScholasticSourceCourses;
	}

	public String getSrcStandardName() { return srcStandardName;}

	public void setSrcStandardName(String srcStandardName) { this.srcStandardName = srcStandardName;}

	public List<String> getDestStandardName() { return destStandardName; }

	public void setDestStandardName(List<String> destStandardName) { this.destStandardName = destStandardName; }

	@Override
	public String toString() {
		return "CloneClassExamRequest{" +
				"srcInstituteId=" + srcInstituteId +
				", destInstituteId=" + destInstituteId +
				", srcAcademicSessionId=" + srcAcademicSessionId +
				", destAcademicSessionId=" + destAcademicSessionId +
				", srcStandardId=" + srcStandardId +
				", destStandardId=" + destStandardId +
				", srcStandardName='" + srcStandardName + '\'' +
				", destStandardName=" + destStandardName +
				", cloneOnlyForScholasticSourceCourses=" + cloneOnlyForScholasticSourceCourses +
				", cloneOnlyForCoScholasticSourceCourses=" + cloneOnlyForCoScholasticSourceCourses +
				'}';
	}
}
