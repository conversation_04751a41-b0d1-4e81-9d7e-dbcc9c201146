package com.lernen.cloud.core.api.examination.report;

/**
 * <AUTHOR>
 */
public class CourseResultStatus {

    private final ExamResultStatus resultStatus;
    private final Double graceMarks;

    public CourseResultStatus(ExamResultStatus resultStatus, Double graceMarks) {
        this.resultStatus = resultStatus;
        this.graceMarks = graceMarks;
    }

    public ExamResultStatus getResultStatus() {
        return resultStatus;
    }

    public Double getGraceMarks() {
        return graceMarks;
    }

    @Override
    public String toString() {
        return "CourseResultStatus{" +
                "resultStatus=" + resultStatus +
                ", graceMarks=" + graceMarks +
                '}';
    }
}
