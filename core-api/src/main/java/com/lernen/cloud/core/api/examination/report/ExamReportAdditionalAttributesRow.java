package com.lernen.cloud.core.api.examination.report;

import com.embrate.cloud.core.api.examination.utility.ExamGridRowAttribute;
import com.lernen.cloud.core.api.course.Course;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportAdditionalAttributesRow {

	private final ExamGridRowAttribute attribute;

	private final List<ExamReportMarksColumn> examReportMarksColumns;

	public ExamReportAdditionalAttributesRow(ExamGridRowAttribute attribute, List<ExamReportMarksColumn> examReportMarksColumns) {
		this.attribute = attribute;
		this.examReportMarksColumns = examReportMarksColumns;
	}

	public ExamGridRowAttribute getAttribute() {
		return attribute;
	}

	public List<ExamReportMarksColumn> getExamReportMarksColumns() {
		return examReportMarksColumns;
	}

	@Override
	public String toString() {
		return "ExamReportAdditionalAttributesRow{" +
				"attribute=" + attribute +
				", examReportMarksColumns=" + examReportMarksColumns +
				'}';
	}
}
