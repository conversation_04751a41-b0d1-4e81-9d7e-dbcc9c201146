package com.lernen.cloud.core.api.examination.report;

import java.util.Arrays;
import java.util.Map;

import com.embrate.cloud.core.api.institute.AssetContent;
import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportCardLayoutData extends DocumentLayoutData {

	private byte[] buildingImage;

	private PdfFont hindiRegularFont;

	private PdfFont hindiBoldFont;

	public ExamReportCardLayoutData(Document document,
			DocumentLayoutSetup documentLayoutSetup, PdfFont regularFont,
			PdfFont boldFont, Float contentFontSize, Float defaultBorderWidth,
			Float logoWidth, Float logoHeight, byte[] logo) {

		super(document, documentLayoutSetup, regularFont, boldFont,
				contentFontSize, defaultBorderWidth, logoWidth, logoHeight,
				logo, null, null);
	}

	public byte[] getBuildingImage() {
		return buildingImage;
	}

	public void setBuildingImage(byte[] buildingImage) {
		this.buildingImage = buildingImage;
	}

	public PdfFont getHindiRegularFont() {
		return hindiRegularFont;
	}

	public void setHindiRegularFont(PdfFont hindiRegularFont) {
		this.hindiRegularFont = hindiRegularFont;
	}

	public PdfFont getHindiBoldFont() {
		return hindiBoldFont;
	}

	public void setHindiBoldFont(PdfFont hindiBoldFont) {
		this.hindiBoldFont = hindiBoldFont;
	}

	@Override
	public String toString() {
		return "ExamReportCardLayoutData [buildingImage=" + Arrays.toString(buildingImage) + ", hindiRegularFont="
				+ hindiRegularFont + ", hindiBoldFont=" + hindiBoldFont + "]";
	}
}
