package com.lernen.cloud.core.api.examination.report;

import java.util.List;
import java.util.Map;

import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.StudentExamMarksDetailsLite;
import com.lernen.cloud.core.api.examination.StudentPersonalityTraitsResponse;
import com.lernen.cloud.core.api.institute.Standard;
import com.embrate.cloud.core.api.institute.StandardMetadata;
import com.lernen.cloud.core.api.student.StudentLite;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportData {

	private final StudentLite studentLite;

	private ExamResultStatus examResultStatus;

	private Integer totalWorkingDays;

	private Integer totalAttendedDays;

	private ExamGrade totalGrade;

	private Double totalObtainedMarks;

	private Double totalMaxMarks;

	private Double totalOriginalMaxMarks;

	private String height;

	private String weight;

	private String remarks;

	private String principalRemarks;

	private Standard promotedTo;

	private Integer rank;

	private Integer dateOfResultDeclaration;

	private Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid;

	private Map<CourseType, ExamReportMarksGrid> additionalCourseTypeExamReportMarksGrid;

	private Map<CourseType, List<ExamGrade>> courseTypeExamGrades;

	private ExamReportStructure examReportStructure;

	private StandardMetadata standardMetaData;

	private List<StudentExamMarksDetailsLite> studentExamMarksDetailsLiteList;

	private List<StudentPersonalityTraitsResponse> studentPersonalityTraitsResponseList;

	public ExamReportData(StudentLite studentLite) {
		this.studentLite = studentLite;
	}

	public StudentLite getStudentLite() {
		return studentLite;
	}

	public ExamResultStatus getExamResultStatus() {
		return examResultStatus;
	}

	public void setExamResultStatus(ExamResultStatus examResultStatus) {
		this.examResultStatus = examResultStatus;
	}

	public Integer getTotalWorkingDays() {
		return totalWorkingDays;
	}

	public void setTotalWorkingDays(Integer totalWorkingDays) {
		this.totalWorkingDays = totalWorkingDays;
	}

	public Integer getTotalAttendedDays() {
		return totalAttendedDays;
	}

	public Map<CourseType, ExamReportMarksGrid> getAdditionalCourseTypeExamReportMarksGrid() {
		return additionalCourseTypeExamReportMarksGrid;
	}

	public void setAdditionalCourseTypeExamReportMarksGrid(
			Map<CourseType, ExamReportMarksGrid> additionalCourseTypeExamReportMarksGrid) {
		this.additionalCourseTypeExamReportMarksGrid = additionalCourseTypeExamReportMarksGrid;
	}

	public void setTotalAttendedDays(Integer totalAttendedDays) {
		this.totalAttendedDays = totalAttendedDays;
	}

	public ExamGrade getTotalGrade() {
		return totalGrade;
	}

	public void setTotalGrade(ExamGrade totalGrade) {
		this.totalGrade = totalGrade;
	}

	public Double getTotalObtainedMarks() {
		return totalObtainedMarks;
	}

	public void setTotalObtainedMarks(Double totalObtainedMarks) {
		this.totalObtainedMarks = totalObtainedMarks;
	}

	public Double getTotalMaxMarks() {
		return totalMaxMarks;
	}

	public void setTotalMaxMarks(Double totalMaxMarks) {
		this.totalMaxMarks = totalMaxMarks;
	}

	public Double getTotalOriginalMaxMarks() {
		return totalOriginalMaxMarks;
	}

	public void setTotalOriginalMaxMarks(Double totalOriginalMaxMarks) {
		this.totalOriginalMaxMarks = totalOriginalMaxMarks;
	}

	public String getHeight() {
		return height;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getPrincipalRemarks() {
		return principalRemarks;
	}

	public void setPrincipalRemarks(String principalRemarks) {
		this.principalRemarks = principalRemarks;
	}

	public Standard getPromotedTo() {
		return promotedTo;
	}

	public void setPromotedTo(Standard promotedTo) {
		this.promotedTo = promotedTo;
	}

	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	public Map<CourseType, ExamReportMarksGrid> getCourseTypeExamReportMarksGrid() {
		return courseTypeExamReportMarksGrid;
	}

	public void setCourseTypeExamReportMarksGrid(
			Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid) {
		this.courseTypeExamReportMarksGrid = courseTypeExamReportMarksGrid;
	}

	public Map<CourseType, List<ExamGrade>> getCourseTypeExamGrades() {
		return courseTypeExamGrades;
	}

	public void setCourseTypeExamGrades(
			Map<CourseType, List<ExamGrade>> courseTypeExamGrades) {
		this.courseTypeExamGrades = courseTypeExamGrades;
	}

	public ExamReportStructure getExamReportStructure() {
		return examReportStructure;
	}

	public void setExamReportStructure(
			ExamReportStructure examReportStructure) {
		this.examReportStructure = examReportStructure;
	}

	public StandardMetadata getStandardMetaData() {
		return standardMetaData;
	}

	public void setStandardMetaData(StandardMetadata standardMetaData) {
		this.standardMetaData = standardMetaData;
	}

	public List<StudentExamMarksDetailsLite> getStudentExamMarksDetailsLiteList() {
		return studentExamMarksDetailsLiteList;
	}

	public void setStudentExamMarksDetailsLiteList(
			List<StudentExamMarksDetailsLite> studentExamMarksDetailsLiteList) {
		this.studentExamMarksDetailsLiteList = studentExamMarksDetailsLiteList;
	}

	public Double getPercentage() {
		if (totalObtainedMarks == null || totalMaxMarks == null) {
			return null;
		}
		return totalObtainedMarks * 100 / totalMaxMarks;
	}

	public Integer getDateOfResultDeclaration() {
		return dateOfResultDeclaration;
	}

	public void setDateOfResultDeclaration(Integer dateOfResultDeclaration) {
		this.dateOfResultDeclaration = dateOfResultDeclaration;
	}

	public List<StudentPersonalityTraitsResponse> getStudentPersonalityTraitsResponseList() {
		return studentPersonalityTraitsResponseList;
	}

	public void setStudentPersonalityTraitsResponseList(List<StudentPersonalityTraitsResponse> studentPersonalityTraitsResponseList) {
		this.studentPersonalityTraitsResponseList = studentPersonalityTraitsResponseList;
	}

	@Override
	public String toString() {
		return "ExamReportData{" +
				"studentLite=" + studentLite +
				", examResultStatus=" + examResultStatus +
				", totalWorkingDays=" + totalWorkingDays +
				", totalAttendedDays=" + totalAttendedDays +
				", totalGrade=" + totalGrade +
				", totalObtainedMarks=" + totalObtainedMarks +
				", totalMaxMarks=" + totalMaxMarks +
				", totalOriginalMaxMarks=" + totalOriginalMaxMarks +
				", height='" + height + '\'' +
				", weight='" + weight + '\'' +
				", remarks='" + remarks + '\'' +
				", principalRemarks='" + principalRemarks + '\'' +
				", promotedTo=" + promotedTo +
				", rank=" + rank +
				", dateOfResultDeclaration=" + dateOfResultDeclaration +
				", courseTypeExamReportMarksGrid=" + courseTypeExamReportMarksGrid +
				", courseTypeExamGrades=" + courseTypeExamGrades +
				", examReportStructure=" + examReportStructure +
				", standardMetaData=" + standardMetaData +
				", studentExamMarksDetailsLiteList=" + studentExamMarksDetailsLiteList +
				", studentPersonalityTraitsResponseList=" + studentPersonalityTraitsResponseList +
				", additionalCourseTypeExamReportMarksGrid=" + additionalCourseTypeExamReportMarksGrid +
				'}';
	}

}
