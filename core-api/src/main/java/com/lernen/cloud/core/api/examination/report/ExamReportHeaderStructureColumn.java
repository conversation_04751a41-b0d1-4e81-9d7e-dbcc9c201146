package com.lernen.cloud.core.api.examination.report;

import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportHeaderStructureColumn {

	private String id;

	private String title;

	private String subTitle;

	private String colorHexCode;

	private boolean displayTotalMarks;

	private ExamReportGridColumnType examReportGridColumnType;

	private Boolean roundOff;

	private UUID examId;

	private List<ExamReportHeaderStructureColumn> childExamReportHeaderStructureColumns;

	/**
	 * Should be empty if total is to be shown
	 */
	private List<Integer> obtainedMarksDimensions;
	private boolean hideTotalDimensionMaxMarks;
	private boolean hideRemainingDimensionMaxMarks;

	private Set<String> computationColumns;

	private String courseWiseClassMetricsComputeColumn;

	private ExamReportCourseGainColumns courseWiseGainMetricsComputeColumns;

	private String columnValue;

	private boolean hide;

	public ExamReportHeaderStructureColumn() {
	}

	public ExamReportHeaderStructureColumn(String id, String title, String subTitle, String colorHexCode, boolean displayTotalMarks, ExamReportGridColumnType examReportGridColumnType, Boolean roundOff, UUID examId, List<ExamReportHeaderStructureColumn> childExamReportHeaderStructureColumns, List<Integer> obtainedMarksDimensions, boolean hideTotalDimensionMaxMarks, boolean hideRemainingDimensionMaxMarks, Set<String> computationColumns, String courseWiseClassMetricsComputeColumn, ExamReportCourseGainColumns courseWiseGainMetricsComputeColumns, String columnValue, boolean hide) {
		this.id = id;
		this.title = title;
		this.subTitle = subTitle;
		this.colorHexCode = colorHexCode;
		this.displayTotalMarks = displayTotalMarks;
		this.examReportGridColumnType = examReportGridColumnType;
		this.roundOff = roundOff;
		this.examId = examId;
		this.childExamReportHeaderStructureColumns = childExamReportHeaderStructureColumns;
		this.obtainedMarksDimensions = obtainedMarksDimensions;
		this.hideTotalDimensionMaxMarks = hideTotalDimensionMaxMarks;
		this.hideRemainingDimensionMaxMarks = hideRemainingDimensionMaxMarks;
		this.computationColumns = computationColumns;
		this.courseWiseClassMetricsComputeColumn = courseWiseClassMetricsComputeColumn;
		this.courseWiseGainMetricsComputeColumns = courseWiseGainMetricsComputeColumns;
		this.columnValue = columnValue;
		this.hide = hide;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getSubTitle() {
		return subTitle;
	}

	public void setSubTitle(String subTitle) {
		this.subTitle = subTitle;
	}

	public String getColorHexCode() {
		return colorHexCode;
	}

	public void setColorHexCode(String colorHexCode) {
		this.colorHexCode = colorHexCode;
	}

	public boolean isDisplayTotalMarks() {
		return displayTotalMarks;
	}

	public void setDisplayTotalMarks(boolean displayTotalMarks) {
		this.displayTotalMarks = displayTotalMarks;
	}

	public ExamReportGridColumnType getExamReportGridColumnType() {
		return examReportGridColumnType;
	}

	public void setExamReportGridColumnType(
			ExamReportGridColumnType examReportGridColumnType) {
		this.examReportGridColumnType = examReportGridColumnType;
	}

	public UUID getExamId() {
		return examId;
	}

	public void setExamId(UUID examId) {
		this.examId = examId;
	}

	public List<ExamReportHeaderStructureColumn> getChildExamReportHeaderStructureColumns() {
		return childExamReportHeaderStructureColumns;
	}

	public void setChildExamReportHeaderStructureColumns(
			List<ExamReportHeaderStructureColumn> childExamReportHeaderStructureColumns) {
		this.childExamReportHeaderStructureColumns = childExamReportHeaderStructureColumns;
	}

	public List<Integer> getObtainedMarksDimensions() {
		return obtainedMarksDimensions;
	}

	public void setObtainedMarksDimensions(
			List<Integer> obtainedMarksDimensions) {
		this.obtainedMarksDimensions = obtainedMarksDimensions;
	}

	public Set<String> getComputationColumns() {
		return computationColumns;
	}

	public void setComputationColumns(Set<String> computationColumns) {
		this.computationColumns = computationColumns;
	}

	public Boolean getRoundOff() {
		return roundOff;
	}

	public void setRoundOff(Boolean roundOff) {
		this.roundOff = roundOff;
	}

	public String getCourseWiseClassMetricsComputeColumn() {
		return courseWiseClassMetricsComputeColumn;
	}

	public void setCourseWiseClassMetricsComputeColumn(String courseWiseClassMetricsComputeColumn) {
		this.courseWiseClassMetricsComputeColumn = courseWiseClassMetricsComputeColumn;
	}

	public ExamReportCourseGainColumns getCourseWiseGainMetricsComputeColumns() {
		return courseWiseGainMetricsComputeColumns;
	}

	public void setCourseWiseGainMetricsComputeColumns(ExamReportCourseGainColumns courseWiseGainMetricsComputeColumns) {
		this.courseWiseGainMetricsComputeColumns = courseWiseGainMetricsComputeColumns;
	}

	public String getColumnValue() {
		return columnValue;
	}

	public void setColumnValue(String columnValue) {
		this.columnValue = columnValue;
	}

	public boolean isHide() {
		return hide;
	}

	public void setHide(boolean hide) {
		this.hide = hide;
	}

	public boolean isHideTotalDimensionMaxMarks() {
		return hideTotalDimensionMaxMarks;
	}

	public void setHideTotalDimensionMaxMarks(boolean hideTotalDimensionMaxMarks) {
		this.hideTotalDimensionMaxMarks = hideTotalDimensionMaxMarks;
	}

	public boolean isHideRemainingDimensionMaxMarks() {
		return hideRemainingDimensionMaxMarks;
	}

	public void setHideRemainingDimensionMaxMarks(boolean hideRemainingDimensionMaxMarks) {
		this.hideRemainingDimensionMaxMarks = hideRemainingDimensionMaxMarks;
	}

	@Override
	public String toString() {
		return "ExamReportHeaderStructureColumn{" +
				"id='" + id + '\'' +
				", title='" + title + '\'' +
				", subTitle='" + subTitle + '\'' +
				", colorHexCode='" + colorHexCode + '\'' +
				", displayTotalMarks=" + displayTotalMarks +
				", examReportGridColumnType=" + examReportGridColumnType +
				", roundOff=" + roundOff +
				", examId=" + examId +
				", childExamReportHeaderStructureColumns=" + childExamReportHeaderStructureColumns +
				", obtainedMarksDimensions=" + obtainedMarksDimensions +
				", hideTotalDimensionMaxMarks=" + hideTotalDimensionMaxMarks +
				", hideRemainingDimensionMaxMarks=" + hideRemainingDimensionMaxMarks +
				", computationColumns=" + computationColumns +
				", courseWiseClassMetricsComputeColumn='" + courseWiseClassMetricsComputeColumn + '\'' +
				", courseWiseGainMetricsComputeColumns=" + courseWiseGainMetricsComputeColumns +
				", columnValue='" + columnValue + '\'' +
				", hide=" + hide +
				'}';
	}

}
