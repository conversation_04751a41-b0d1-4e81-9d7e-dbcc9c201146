package com.lernen.cloud.core.api.examination.report;

import java.util.List;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportHeaderStructureColumnPayload {

	private String id;

	private String title;

	private String subTitle;

	private String colorHexCode;

	private boolean displayTotalMarks;

	private ExamReportGridColumnType examReportGridColumnType;

	private Boolean roundOff;

	private String examName;

	private List<ExamReportHeaderStructureColumnPayload> childExamReportHeaderStructureColumns;

	/**
	 * Should be empty if total is to be shown
	 */
	private List<String> obtainedMarksDimensions;
	private boolean hideTotalDimensionMaxMarks;
	private boolean hideRemainingDimensionMaxMarks;

	private Set<String> computationColumns;

	private String courseWiseClassMetricsComputeColumn;

	private ExamReportCourseGainColumns courseWiseGainMetricsComputeColumns;

	private String columnValue;

	private boolean hide;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getSubTitle() {
		return subTitle;
	}

	public void setSubTitle(String subTitle) {
		this.subTitle = subTitle;
	}

	public String getColorHexCode() {
		return colorHexCode;
	}

	public void setColorHexCode(String colorHexCode) {
		this.colorHexCode = colorHexCode;
	}

	public boolean isDisplayTotalMarks() {
		return displayTotalMarks;
	}

	public void setDisplayTotalMarks(boolean displayTotalMarks) {
		this.displayTotalMarks = displayTotalMarks;
	}

	public ExamReportGridColumnType getExamReportGridColumnType() {
		return examReportGridColumnType;
	}

	public void setExamReportGridColumnType(
			ExamReportGridColumnType examReportGridColumnType) {
		this.examReportGridColumnType = examReportGridColumnType;
	}

	public String getExamName() {
		return examName;
	}

	public void setExamName(String examName) {
		this.examName = examName;
	}

	public List<ExamReportHeaderStructureColumnPayload> getChildExamReportHeaderStructureColumns() {
		return childExamReportHeaderStructureColumns;
	}

	public void setChildExamReportHeaderStructureColumns(
			List<ExamReportHeaderStructureColumnPayload> childExamReportHeaderStructureColumns) {
		this.childExamReportHeaderStructureColumns = childExamReportHeaderStructureColumns;
	}

	public List<String> getObtainedMarksDimensions() {
		return obtainedMarksDimensions;
	}

	public void setObtainedMarksDimensions(
			List<String> obtainedMarksDimensions) {
		this.obtainedMarksDimensions = obtainedMarksDimensions;
	}

	public Set<String> getComputationColumns() {
		return computationColumns;
	}

	public void setComputationColumns(Set<String> computationColumns) {
		this.computationColumns = computationColumns;
	}

	public Boolean getRoundOff() {
		return roundOff;
	}

	public void setRoundOff(Boolean roundOff) {
		this.roundOff = roundOff;
	}

	public String getCourseWiseClassMetricsComputeColumn() {
		return courseWiseClassMetricsComputeColumn;
	}

	public void setCourseWiseClassMetricsComputeColumn(String courseWiseClassMetricsComputeColumn) {
		this.courseWiseClassMetricsComputeColumn = courseWiseClassMetricsComputeColumn;
	}

	public ExamReportCourseGainColumns getCourseWiseGainMetricsComputeColumns() {
		return courseWiseGainMetricsComputeColumns;
	}

	public void setCourseWiseGainMetricsComputeColumns(ExamReportCourseGainColumns courseWiseGainMetricsComputeColumns) {
		this.courseWiseGainMetricsComputeColumns = courseWiseGainMetricsComputeColumns;
	}

	public String getColumnValue() {
		return columnValue;
	}

	public void setColumnValue(String columnValue) {
		this.columnValue = columnValue;
	}

	public boolean isHide() {
		return hide;
	}

	public void setHide(boolean hide) {
		this.hide = hide;
	}

	public boolean isHideTotalDimensionMaxMarks() {
		return hideTotalDimensionMaxMarks;
	}

	public void setHideTotalDimensionMaxMarks(boolean hideTotalDimensionMaxMarks) {
		this.hideTotalDimensionMaxMarks = hideTotalDimensionMaxMarks;
	}

	public boolean isHideRemainingDimensionMaxMarks() {
		return hideRemainingDimensionMaxMarks;
	}

	public void setHideRemainingDimensionMaxMarks(boolean hideRemainingDimensionMaxMarks) {
		this.hideRemainingDimensionMaxMarks = hideRemainingDimensionMaxMarks;
	}
}

