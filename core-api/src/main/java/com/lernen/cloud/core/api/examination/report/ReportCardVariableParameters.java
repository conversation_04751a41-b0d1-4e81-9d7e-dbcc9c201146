package com.lernen.cloud.core.api.examination.report;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @created_at 07/07/23 : 12:55
 **/
public enum ReportCardVariableParameters {

    ATTENDED_DAYS, REMARKS, PRINCIPAL_REMARKS, HEIGHT, WEIGHT, TOTAL_DAYS, DATE_OF_RESULT_DECLARATION;

    public static ReportCardVariableParameters getReportCardVariableParameters(String reportCardVariableParameter) {
        if (StringUtils.isBlank(reportCardVariableParameter)) {
            return null;
        }
        for (ReportCardVariableParameters reportCardVariableParametersEnum : ReportCardVariableParameters
                .values()) {
            if (reportCardVariableParametersEnum.name().equalsIgnoreCase(reportCardVariableParameter)) {
                return reportCardVariableParametersEnum;
            }
        }
        return null;
    }

}
