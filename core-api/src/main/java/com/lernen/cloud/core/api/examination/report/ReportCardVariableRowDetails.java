/**
 * 
 */
package com.lernen.cloud.core.api.examination.report;

import com.lernen.cloud.core.api.student.Student;

/**
 * <AUTHOR>
 *
 */
public class ReportCardVariableRowDetails {
	
	private int instituteId;
	
	private String reportType;
	
	private int academicSessionId;
	
	private Student student;

	private Double attendedDays;

	private Double totalDays;    //Can be total days

	private String remarks;

	private String princiaplRemarks;

	private String height;

	private String weight;

	private Integer dateOfResultDeclaration;

	public ReportCardVariableRowDetails(int instituteId, String reportType, int academicSessionId, Student student,
			Double attendedDays, Double totalDays, String remarks, String princiaplRemarks, String height, String weight, Integer dateOfResultDeclaration) {
		this.instituteId = instituteId;
		this.reportType = reportType;
		this.academicSessionId = academicSessionId;
		this.student = student;
		this.attendedDays = attendedDays;
		this.totalDays = totalDays;
		this.remarks = remarks;
		this.princiaplRemarks = princiaplRemarks;
		this.height = height;
		this.weight = weight;
		this.dateOfResultDeclaration = dateOfResultDeclaration;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public String getReportType() {
		return reportType;
	}

	public void setReportType(String reportType) {
		this.reportType = reportType;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	public Student getStudent() {
		return student;
	}

	public void setStudent(Student student) {
		this.student = student;
	}

	public Double getAttendedDays() {
		return attendedDays;
	}

	public void setAttendedDays(Double attendedDays) {
		this.attendedDays = attendedDays;
	}

	public Double getTotalDays() {
		return totalDays;
	}

	public void setTotalDays(Double totalDays) {
		this.totalDays = totalDays;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getHeight() {
		return height;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public Integer getDateOfResultDeclaration() {
		return dateOfResultDeclaration;
	}

	public void setDateOfResultDeclaration(Integer dateOfResultDeclaration) {
		this.dateOfResultDeclaration = dateOfResultDeclaration;
	}

	public String getPrinciaplRemarks() {
		return princiaplRemarks;
	}

	public void setPrinciaplRemarks(String princiaplRemarks) {
		this.princiaplRemarks = princiaplRemarks;
	}

	@Override
	public String toString() {
		return "ReportCardVariableRowDetails{" +
				"instituteId=" + instituteId +
				", reportType='" + reportType + '\'' +
				", academicSessionId=" + academicSessionId +
				", student=" + student +
				", attendedDays=" + attendedDays +
				", totalDays=" + totalDays +
				", remarks='" + remarks + '\'' +
				", princiaplRemarks='" + princiaplRemarks + '\'' +
				", height='" + height + '\'' +
				", weight='" + weight + '\'' +
				", dateOfResultDeclaration=" + dateOfResultDeclaration +
				'}';
	}

}
