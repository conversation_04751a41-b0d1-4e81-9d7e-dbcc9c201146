/**
 * 
 */
package com.lernen.cloud.core.api.examination.report;

import com.lernen.cloud.core.api.student.Student;

/**
 * <AUTHOR>
 *
 */
public class StudentReportCardVariableDetails {

	private Student student;
	
	private Double totalDays;   

	private Double attendedDays;

	private String remarks;

	private String principalRemarks;

	/**
	 * String as there is no unit associated
	 */
	private String height;

	/**
	 * String as there is no unit associated
	 */
	private String weight;

	private Integer dateOfResultDeclaration;

	public StudentReportCardVariableDetails(Student student, Double totalDays, Double attendedDays, String remarks,
											String principalRemarks, String height, String weight, Integer dateOfResultDeclaration) {
		this.student = student;
		this.totalDays = totalDays;
		this.attendedDays = attendedDays;
		this.remarks = remarks;
		this.principalRemarks = principalRemarks;
		this.height = height;
		this.weight = weight;
		this.dateOfResultDeclaration = dateOfResultDeclaration;
	}

	public StudentReportCardVariableDetails() {
	}

	public Student getStudent() {
		return student;
	}

	public void setStudent(Student student) {
		this.student = student;
	}

	public Double getTotalDays() {
		return totalDays;
	}

	public void setTotalDays(Double totalDays) {
		this.totalDays = totalDays;
	}

	public Double getAttendedDays() {
		return attendedDays;
	}

	public void setAttendedDays(Double attendedDays) {
		this.attendedDays = attendedDays;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getHeight() {
		return height;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public Integer getDateOfResultDeclaration() {
		return dateOfResultDeclaration;
	}

	public void setDateOfResultDeclaration(Integer dateOfResultDeclaration) {
		this.dateOfResultDeclaration = dateOfResultDeclaration;
	}

	public String getPrincipalRemarks() {
		return principalRemarks;
	}

	public void setPrincipalRemarks(String principalRemarks) {
		this.principalRemarks = principalRemarks;
	}

	@Override
	public String toString() {
		return "StudentReportCardVariableDetails{" +
				"student=" + student +
				", totalDays=" + totalDays +
				", attendedDays=" + attendedDays +
				", remarks='" + remarks + '\'' +
				", principalRemarks='" + principalRemarks + '\'' +
				", height='" + height + '\'' +
				", weight='" + weight + '\'' +
				", dateOfResultDeclaration=" + dateOfResultDeclaration +
				'}';
	}
}
