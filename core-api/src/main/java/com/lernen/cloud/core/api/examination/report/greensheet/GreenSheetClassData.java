package com.lernen.cloud.core.api.examination.report.greensheet;

import java.util.List;

import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Standard;

/**
 * 
 * <AUTHOR>
 *
 */
public class GreenSheetClassData {

	private final AcademicSession academicSession;

	private final Standard standard;
	
	private final GreenSheetClass greenSheetClass;

	private final List<StudentGreenSheetData> studentGreenSheetDataList;

	private final Integer totalAttendanceDays;

	public GreenSheetClassData(AcademicSession academicSession,
			Standard standard, GreenSheetClass greenSheetClass,
			List<StudentGreenSheetData> studentGreenSheetDataList,
			Integer totalAttendanceDays) {
		this.academicSession = academicSession;
		this.standard = standard;
		this.greenSheetClass = greenSheetClass;
		this.studentGreenSheetDataList = studentGreenSheetDataList;
		this.totalAttendanceDays = totalAttendanceDays;
	}

	public AcademicSession getAcademicSession() {
		return academicSession;
	}

	public Standard getStandard() {
		return standard;
	}

	public GreenSheetClass getGreenSheetClass() {
		return greenSheetClass;
	}

	public List<StudentGreenSheetData> getStudentGreenSheetDataList() {
		return studentGreenSheetDataList;
	}

	public Integer getTotalAttendanceDays() {
		return totalAttendanceDays;
	}

	@Override
	public String toString() {
		return "GreenSheetClassData [academicSession=" + academicSession + ", standard=" + standard
				+ ", greenSheetClass=" + greenSheetClass + ", studentGreenSheetDataList=" + studentGreenSheetDataList
				+ ", totalAttendanceDays=" + totalAttendanceDays + "]";
	}
	
	

}
