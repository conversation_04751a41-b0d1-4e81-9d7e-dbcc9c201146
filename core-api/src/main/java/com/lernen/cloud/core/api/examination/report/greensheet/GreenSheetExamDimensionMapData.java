package com.lernen.cloud.core.api.examination.report.greensheet;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class GreenSheetExamDimensionMapData
		implements
			IGreenSheetExamDimensionMapData {

	private UUID examId;

	private UUID courseId;

	private Integer dimensionId;

	private int fractionNumerator;

	private int fractionDenominator;

	public GreenSheetExamDimensionMapData() {
	}

	public GreenSheetExamDimensionMapData(UUID examId, UUID courseId,
			Integer dimensionId, int fractionNumerator,
			int fractionDenominator) {
		this.examId = examId;
		this.courseId = courseId;
		this.dimensionId = dimensionId;
		this.fractionNumerator = fractionNumerator;
		this.fractionDenominator = fractionDenominator;
	}

	public UUID getExamId() {
		return examId;
	}

	public void setExamId(UUID examId) {
		this.examId = examId;
	}

	public UUID getCourseId() {
		return courseId;
	}

	public void setCourseId(UUID courseId) {
		this.courseId = courseId;
	}

	public Integer getDimensionId() {
		return dimensionId;
	}

	public void setDimensionId(Integer dimensionId) {
		this.dimensionId = dimensionId;
	}

	public int getFractionNumerator() {
		return fractionNumerator;
	}

	public void setFractionNumerator(int fractionNumerator) {
		this.fractionNumerator = fractionNumerator;
	}

	public int getFractionDenominator() {
		return fractionDenominator;
	}

	public void setFractionDenominator(int fractionDenominator) {
		this.fractionDenominator = fractionDenominator;
	}

	public Double getFraction() {
		if (getFractionDenominator() <= 0d || getFractionNumerator() < 0d) {
			return null;
		}
		return getFractionNumerator() * 1d / getFractionDenominator();
	}
}
