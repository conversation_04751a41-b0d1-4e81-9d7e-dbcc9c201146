package com.lernen.cloud.core.api.examination.report.greensheet;

/**
 * 
 * <AUTHOR>
 *
 */
public class GreenSheetExamDimensionMarks {

	private final String dimensionName;

	private final Double totalMarks;

	private final Double obtainedMarks;

	private final String grade;

	public GreenSheetExamDimensionMarks(String dimensionName, Double totalMarks,
			Double obtainedMarks, String grade) {
		this.dimensionName = dimensionName;
		this.totalMarks = totalMarks;
		this.obtainedMarks = obtainedMarks;
		this.grade = grade;
	}

	public String getDimensionName() {
		return dimensionName;
	}

	public Double getTotalMarks() {
		return totalMarks;
	}

	public Double getObtainedMarks() {
		return obtainedMarks;
	}

	public String getGrade() {
		return grade;
	}

	@Override
	public String toString() {
		return "GreenSheetExamDimensionMarks [dimensionName=" + dimensionName + ", totalMarks=" + totalMarks
				+ ", obtainedMarks=" + obtainedMarks + ", grade=" + grade + "]";
	}

	
}
