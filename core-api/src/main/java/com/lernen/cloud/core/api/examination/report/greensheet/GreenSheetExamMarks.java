package com.lernen.cloud.core.api.examination.report.greensheet;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class GreenSheetExamMarks {

	private final String examName;

	private final List<GreenSheetExamDimensionMarks> greenSheetExamDimensionMarksList;

	public GreenSheetExamMarks(String examName,
			List<GreenSheetExamDimensionMarks> greenSheetExamDimensionMarksList) {
		this.examName = examName;
		this.greenSheetExamDimensionMarksList = greenSheetExamDimensionMarksList;
	}

	public String getExamName() {
		return examName;
	}

	public List<GreenSheetExamDimensionMarks> getGreenSheetExamDimensionMarksList() {
		return greenSheetExamDimensionMarksList;
	}

	@Override
	public String toString() {
		return "GreenSheetExamMarks [examName=" + examName + ", greenSheetExamDimensionMarksList="
				+ greenSheetExamDimensionMarksList + "]";
	}

	
}
