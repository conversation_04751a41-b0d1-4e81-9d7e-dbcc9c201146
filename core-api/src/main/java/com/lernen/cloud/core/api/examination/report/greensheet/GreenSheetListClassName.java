/**
 *
 */
package com.lernen.cloud.core.api.examination.report.greensheet;

import com.lernen.cloud.core.api.course.CourseType;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class GreenSheetListClassName {

    private GreenSheetClass greenSheetClass;

    private List<List<GreenSheetColumn>> greenSheetColumnList;

    //CourseType, Map<CourseName, Map<GradeName, StudentCount>>>
    private LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> courseGradeMap;

    //CourseType, Map<CourseName, Map<DivisionName, StudentCount>>>
    private LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> courseDivisionMap;

    private String totalAttendanceDays;

    private Integer resultDeclarationDate;

    public GreenSheetListClassName(GreenSheetClass greenSheetClass, List<List<GreenSheetColumn>> greenSheetColumnList,
                                   LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> courseGradeMap,
                                   LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> courseDivisionMap, String totalAttendanceDays,
                                   Integer resultDeclarationDate) {
        this.greenSheetClass = greenSheetClass;
        this.greenSheetColumnList = greenSheetColumnList;
        this.courseGradeMap = courseGradeMap;
        this.courseDivisionMap = courseDivisionMap;
        this.totalAttendanceDays = totalAttendanceDays;
        this.resultDeclarationDate = resultDeclarationDate;
    }

    /**
     *
     */
    public GreenSheetListClassName() {
    }

    public GreenSheetClass getGreenSheetClass() {
        return greenSheetClass;
    }

    public void setGreenSheetClass(GreenSheetClass greenSheetClass) {
        this.greenSheetClass = greenSheetClass;
    }

    public List<List<GreenSheetColumn>> getGreenSheetColumnList() {
        return greenSheetColumnList;
    }

    public void setGreenSheetColumnList(List<List<GreenSheetColumn>> greenSheetColumnList) {
        this.greenSheetColumnList = greenSheetColumnList;
    }

    public LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> getCourseGradeMap() {
        return courseGradeMap;
    }

    public void setCourseGradeMap(LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> courseGradeMap) {
        this.courseGradeMap = courseGradeMap;
    }

    public LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> getCourseDivisionMap() {
        return courseDivisionMap;
    }

    public void setCourseDivisionMap(LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> courseDivisionMap) {
        this.courseDivisionMap = courseDivisionMap;
    }


    public String getTotalAttendanceDays() {
        return totalAttendanceDays;
    }

    public void setTotalAttendanceDays(String totalAttendanceDays) {
        this.totalAttendanceDays = totalAttendanceDays;
    }

    public Integer getResultDeclarationDate() {
        return resultDeclarationDate;
    }

    public void setResultDeclarationDate(Integer resultDeclarationDate) {
        this.resultDeclarationDate = resultDeclarationDate;
    }

    @Override
    public String toString() {
        return "GreenSheetListClassName{" +
                "greenSheetClass=" + greenSheetClass +
                ", greenSheetColumnList=" + greenSheetColumnList +
                ", courseGradeMap=" + courseGradeMap +
                ", courseDivisionMap=" + courseDivisionMap +
                ", totalAttendanceDays='" + totalAttendanceDays + '\'' +
                ", resultDeclarationDate=" + resultDeclarationDate +
                '}';
    }
}
