package com.lernen.cloud.core.api.exception;

public enum DatabaseErrorCode {

	DUPLICATE_ENTRY("400.1500"),
	FOREIGN_KEY("400.1501");

	int statusCode;
	int errorCode;
	String databaseErrorCode;

	private DatabaseErrorCode(String errorCode) {
		final String[] code = errorCode.split("\\.");
		this.statusCode = Integer.valueOf(code[0]);
		this.errorCode = Integer.valueOf(code[1]);
		this.databaseErrorCode = errorCode;
	}

	private DatabaseErrorCode(int statusCode, int errorCode) {
		this.statusCode = statusCode;
		this.errorCode = errorCode;
	}

	public int getStatusCode() {
		return statusCode;
	}

	public int getErrorCode() {
		return errorCode;
	}

	public String getCode() {
		return databaseErrorCode;
	}

	public static DatabaseErrorCode getDatabaseErrorCode(int statusCode, int errorCode) {
		for (final DatabaseErrorCode code : DatabaseErrorCode.values()) {
			if (code.getErrorCode() == errorCode && code.getStatusCode() == statusCode) {
				return code;
			}
		}
		throw new IllegalArgumentException(
				"Could not find DatabaseErrorCode enum with statusCode: " + statusCode + " errorCode: " + errorCode);
	}

}

