package com.lernen.cloud.core.api.exception;

public class ErrorResponse {
		
	private int statusCode;
	
	private int errorCode;
	
	private String message;

	public ErrorResponse(ApplicationErrorCode applicationError, String message) {
		this.statusCode = applicationError.getStatusCode();
		this.errorCode = applicationError.getErrorCode();
		this.message = message;
	}
	
	public ErrorResponse(DatabaseErrorCode databaseError, String message) {
		this.statusCode = databaseError.getStatusCode();
		this.errorCode = databaseError.getErrorCode();
		this.message = message;
	}

	public int getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(int statusCode) {
		this.statusCode = statusCode;
	}

	public int getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(int errorCode) {
		this.errorCode = errorCode;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	@Override
	public String toString() {
		return "ErrorResponse [statusCode=" + statusCode + ", errorCode="
				+ errorCode + ", message=" + message + "]";
	}



}