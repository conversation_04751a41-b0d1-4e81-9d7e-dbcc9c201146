package com.lernen.cloud.core.api.fee.discount;

import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
public class FeeDiscountAssignmentDetails {

	private final FeeDiscountMetadata feeDiscountMetadata;

	private final List<FeeIdDiscountAssignmentDetails> feeIdDiscountAssignmentDetailsList;

	public FeeDiscountAssignmentDetails(
			FeeDiscountMetadata feeDiscountMetadata,
			List<FeeIdDiscountAssignmentDetails> feeIdDiscountAssignmentDetailsList) {
		this.feeDiscountMetadata = feeDiscountMetadata;
		this.feeIdDiscountAssignmentDetailsList = feeIdDiscountAssignmentDetailsList;
	}

	public FeeDiscountMetadata getFeeDiscountMetadata() {
		return feeDiscountMetadata;
	}

	public List<FeeIdDiscountAssignmentDetails> getFeeIdDiscountAssignmentDetailsList() {
		return feeIdDiscountAssignmentDetailsList;
	}

	@Override
	public String toString() {
		return "StudentFeeDiscountAssignmentDetails [feeDiscountMetadata="
				+ feeDiscountMetadata + ", feeIdDiscountAssignmentDetailsList="
				+ feeIdDiscountAssignmentDetailsList + "]";
	}

}
