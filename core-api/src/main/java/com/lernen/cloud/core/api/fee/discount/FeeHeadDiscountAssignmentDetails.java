package com.lernen.cloud.core.api.fee.discount;

import com.lernen.cloud.core.api.fees.FeeHeadConfiguration;

/**
 *
 * <AUTHOR>
 *
 */
public class FeeHeadDiscountAssignmentDetails {

	private final FeeHeadConfiguration feeHeadConfiguration;

	private final boolean isPercent;

	private final double amount;

	private final Double appliedDiscountAmount;

	public FeeHeadDiscountAssignmentDetails(
			FeeHeadConfiguration feeHeadConfiguration, boolean isPercent,
			double amount, Double appliedDiscountAmount) {
		this.feeHeadConfiguration = feeHeadConfiguration;
		this.isPercent = isPercent;
		this.amount = amount;
		this.appliedDiscountAmount = appliedDiscountAmount;
	}

	public FeeHeadConfiguration getFeeHeadConfiguration() {
		return feeHeadConfiguration;
	}

	public boolean isPercent() {
		return isPercent;
	}

	public double getAmount() {
		return amount;
	}

	public Double getAppliedDiscountAmount() {
		return appliedDiscountAmount;
	}

	@Override
	public String toString() {
		return "FeeHeadDiscountAssignmentDetails [feeHeadConfiguration="
				+ feeHeadConfiguration + ", isPercent=" + isPercent
				+ ", amount=" + amount + ", appliedDiscountAmount="
				+ appliedDiscountAmount + "]";
	}

}
