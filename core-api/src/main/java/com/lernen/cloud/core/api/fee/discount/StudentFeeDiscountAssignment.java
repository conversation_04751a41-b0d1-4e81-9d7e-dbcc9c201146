package com.lernen.cloud.core.api.fee.discount;

import java.util.List;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentFeeDiscountAssignment {

	private final int instituteId;

	private final int academicSessionId;

	private final UUID studentId;

	private final List<StudentDiscountData> studentDiscountDataList;

	public StudentFeeDiscountAssignment(int instituteId, int academicSessionId,
			UUID studentId, List<StudentDiscountData> studentDiscountDataList) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.studentId = studentId;
		this.studentDiscountDataList = studentDiscountDataList;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public UUID getStudentId() {
		return studentId;
	}

	public List<StudentDiscountData> getStudentDiscountDataList() {
		return studentDiscountDataList;
	}

	@Override
	public String toString() {
		return "StudentFeeDiscountAssignment [instituteId=" + instituteId
				+ ", academicSessionId=" + academicSessionId + ", studentId="
				+ studentId + ", studentDiscountDataList="
				+ studentDiscountDataList + "]";
	}

}
