package com.lernen.cloud.core.api.fee.discount;

import java.util.List;

import com.lernen.cloud.core.api.student.StudentLite;

public class StudentFeeDiscountAssignmentMetadata {

	private final StudentLite studentLite;

	private final List<FeeDiscountMetadata> feeDiscountMetadataList;

	public StudentFeeDiscountAssignmentMetadata(StudentLite studentLite,
			List<FeeDiscountMetadata> feeDiscountMetadataList) {
		this.studentLite = studentLite;
		this.feeDiscountMetadataList = feeDiscountMetadataList;
	}

	public StudentLite getStudentLite() {
		return studentLite;
	}

	public List<FeeDiscountMetadata> getFeeDiscountMetadataList() {
		return feeDiscountMetadataList;
	}

	@Override
	public String toString() {
		return "StudentFeeDiscountAssignmentMetadata [studentLite="
				+ studentLite + ", feeDiscountMetadataList="
				+ feeDiscountMetadataList + "]";
	}

}
