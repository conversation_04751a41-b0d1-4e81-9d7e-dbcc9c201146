package com.lernen.cloud.core.api.fee.discount;

import java.util.UUID;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentFeeDiscountAssignmentRow {

	private final int instituteId;

	private final int academicSessionId;

	private final UUID discountId;

	private final String discountName;

	private final UUID studentId;

	private final UUID feeId;

	private final int feeHeadId;

	private final boolean isPercent;

	private final Double amount;

	public StudentFeeDiscountAssignmentRow(int instituteId,
			int academicSessionId, UUID discountId, String discountName,
			UUID studentId, UUID feeId, int feeHeadId, boolean isPercent,
			Double amount) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.discountId = discountId;
		this.discountName = discountName;
		this.studentId = studentId;
		this.feeId = feeId;
		this.feeHeadId = feeHeadId;
		this.isPercent = isPercent;
		this.amount = amount;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public UUID getDiscountId() {
		return discountId;
	}

	public String getDiscountName() {
		return discountName;
	}

	public UUID getStudentId() {
		return studentId;
	}

	public UUID getFeeId() {
		return feeId;
	}

	public int getFeeHeadId() {
		return feeHeadId;
	}

	public boolean isPercent() {
		return isPercent;
	}

	public Double getAmount() {
		return amount;
	}

	@Override
	public String toString() {
		return "StudentFeeDiscountAssignmentRow [instituteId=" + instituteId
				+ ", academicSessionId=" + academicSessionId + ", discountId="
				+ discountId + ", discountName=" + discountName + ", studentId="
				+ studentId + ", feeId=" + feeId + ", feeHeadId=" + feeHeadId
				+ ", isPercent=" + isPercent + ", amount=" + amount + "]";
	}

}
