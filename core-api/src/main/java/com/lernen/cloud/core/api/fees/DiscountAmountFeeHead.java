package com.lernen.cloud.core.api.fees;

public class DiscountAmountFeeHead {

	private int feeHeadId;
	
	private boolean percentage;
	
	private double amount;

	public DiscountAmountFeeHead() {
	}

	public DiscountAmountFeeHead(int feeHeadId, boolean percentage, double amount) {
		this.feeHeadId = feeHeadId;
		this.percentage = percentage;
		this.amount = amount;
	}

	public int getFeeHeadId() {
		return feeHeadId;
	}

	public void setFeeHeadId(int feeHeadId) {
		this.feeHeadId = feeHeadId;
	}

	public boolean isPercentage() {
		return percentage;
	}

	public void setPercentage(boolean percentage) {
		this.percentage = percentage;
	}

	public double getAmount() {
		return amount;
	}

	public void setAmount(double amount) {
		this.amount = amount;
	}

	@Override
	public String toString() {
		return "DiscountAmountFeeHead [feeHeadId=" + feeHeadId + ", percentage=" + percentage + ", amount=" + amount
				+ "]";
	}
	
	
}
