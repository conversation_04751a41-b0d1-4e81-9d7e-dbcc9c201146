package com.lernen.cloud.core.api.fees;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class DiscountBasicInfo {

	private int instituteId;

	private UUID discountId;

	private String discountName;

	private boolean variable;

	private String description;

	public DiscountBasicInfo(int instituteId, UUID discountId, String discountName, boolean variable,
			String description) {
		this.instituteId = instituteId;
		this.discountId = discountId;
		this.discountName = discountName;
		this.variable = variable;
		this.description = description;
	}

	// Dummy Constructor
	public DiscountBasicInfo() {
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getDiscountId() {
		return discountId;
	}

	public void setDiscountId(UUID discountId) {
		this.discountId = discountId;
	}

	public String getDiscountName() {
		return discountName;
	}

	public void setDiscountName(String discountName) {
		this.discountName = discountName;
	}

	public boolean isVariable() {
		return variable;
	}

	public void setVariable(boolean variable) {
		this.variable = variable;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Override
	public String toString() {
		return "DiscountBasicInfo [instituteId=" + instituteId + ", discountId=" + discountId + ", discountName="
				+ discountName + ", variable=" + variable + ", description=" + description + "]";
	}

}
