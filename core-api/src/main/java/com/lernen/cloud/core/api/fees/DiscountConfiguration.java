package com.lernen.cloud.core.api.fees;

import java.util.List;

public class DiscountConfiguration {

	private DiscountBasicInfo discountBasicInfo;
	
	private List<DiscountAmountFeeHead> discountAmountFeeHeadList;
	
	//Dummy Constructor
	public DiscountConfiguration() {
	}
	
	public DiscountConfiguration(DiscountBasicInfo discountBasicInfo, List<DiscountAmountFeeHead> discountAmountFeeHeadList) {
		this.discountBasicInfo = discountBasicInfo;
		this.discountAmountFeeHeadList = discountAmountFeeHeadList;
	}

	public DiscountBasicInfo getDiscountBasicInfo() {
		return discountBasicInfo;
	}

	public void setDiscountBasicInfo(DiscountBasicInfo discountBasicInfo) {
		this.discountBasicInfo = discountBasicInfo;
	}

	public List<DiscountAmountFeeHead> getDiscountAmountFeeHeadList() {
		return discountAmountFeeHeadList;
	}

	public void setDiscountAmountFeeHeadList(List<DiscountAmountFeeHead> discountAmountFeeHeadList) {
		this.discountAmountFeeHeadList = discountAmountFeeHeadList;
	}	
}
