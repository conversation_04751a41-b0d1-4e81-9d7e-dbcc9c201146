package com.lernen.cloud.core.api.fees;

import java.util.UUID;
/**
 * 
 * <AUTHOR>
 *
 */
public class DiscountPriority {

	private int instituteId;
	
	private UUID discountId;
	
	private int priorityLevel;

	public DiscountPriority(int instituteId, UUID discountId, int priorityLevel) {
		this.instituteId = instituteId;
		this.discountId = discountId;
		this.priorityLevel = priorityLevel;
	}
	
	//Dummy Constructor
	public DiscountPriority() {	
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getDiscountId() {
		return discountId;
	}

	public void setDiscountId(UUID discountId) {
		this.discountId = discountId;
	}

	public int getPriorityLevel() {
		return priorityLevel;
	}

	public void setPriorityLevel(int priorityLevel) {
		this.priorityLevel = priorityLevel;
	}
}
