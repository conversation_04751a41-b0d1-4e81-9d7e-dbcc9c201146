package com.lernen.cloud.core.api.fees;

import java.util.List;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class EntityFeeAssignment {

	private final int instituteId;

	private final String entityId;

	private final FeeEntity feeEntity;

	private final List<FeeIdFeeHeadDetails> feeIdFeeHeadDetailsList;

	public EntityFeeAssignment(int instituteId, String entityId,
			FeeEntity feeEntity,
			List<FeeIdFeeHeadDetails> feeIdFeeHeadDetailsList) {
		this.instituteId = instituteId;
		this.entityId = entityId;
		this.feeEntity = feeEntity;
		this.feeIdFeeHeadDetailsList = FeeIdFeeHeadDetails.sortFeeIdFeeHeadDetails(feeIdFeeHeadDetailsList);
	}

	public int getInstituteId() {
		return instituteId;
	}

	public String getEntityId() {
		return entityId;
	}

	public FeeEntity getFeeEntity() {
		return feeEntity;
	}

	public List<FeeIdFeeHeadDetails> getFeeIdFeeHeadDetailsList() {
		return feeIdFeeHeadDetailsList;
	}

	@Override
	public String toString() {
		return "EntityFeeAssignment [instituteId=" + instituteId + ", entityId="
				+ entityId + ", feeEntity=" + feeEntity
				+ ", feeIdFeeHeadDetailsList=" + feeIdFeeHeadDetailsList + "]";
	}

}
