package com.lernen.cloud.core.api.fees;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class EntityFees {

	private String entityId;

	private FeeEntity feeEntity;

	private List<FeeHeadAmount> feeHeadAmount;

	public EntityFees(String entityId, FeeEntity feeEntity, List<FeeHeadAmount> feeHeadAmount) {
		this.entityId = entityId;
		this.feeEntity = feeEntity;
		this.feeHeadAmount = feeHeadAmount;
	}

	public EntityFees() {
	}

	public String getEntityId() {
		return entityId;
	}

	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	public FeeEntity getFeeEntity() {
		return feeEntity;
	}

	public void setFeeEntity(FeeEntity feeEntity) {
		this.feeEntity = feeEntity;
	}

	public List<FeeHeadAmount> getFeeHeadAmount() {
		return feeHeadAmount;
	}

	public void setFeeHeadAmount(List<FeeHeadAmount> feeHeadAmount) {
		this.feeHeadAmount = feeHeadAmount;
	}

	@Override
	public String toString() {
		return "EntityFees [entityId=" + entityId + ", feeEntity=" + feeEntity + ", feeHeadAmount=" + feeHeadAmount
				+ "]";
	}

}
