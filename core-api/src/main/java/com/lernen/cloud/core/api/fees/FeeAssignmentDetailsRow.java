package com.lernen.cloud.core.api.fees;

import com.lernen.cloud.core.api.user.Module;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeeAssignmentDetailsRow {

	private final int instituteId;

	private final FeeConfigurationResponse feeConfigurationResponse;

	private final String entityId;

	private final FeeEntity feeEntity;

	private final FeeHeadConfiguration feeHeadConfiguration;
	
	private final double amount;

	private final Module module;

	public FeeAssignmentDetailsRow(int instituteId,
			FeeConfigurationResponse feeConfigurationResponse, String entityId,
			FeeEntity feeEntity, FeeHeadConfiguration feeHeadConfiguration,
			double amount, Module module) {
		this.instituteId = instituteId;
		this.feeConfigurationResponse = feeConfigurationResponse;
		this.entityId = entityId;
		this.feeEntity = feeEntity;
		this.feeHeadConfiguration = feeHeadConfiguration;
		this.amount = amount;
		this.module = module;
	}
	
	public int getInstituteId() {
		return instituteId;
	}

	public FeeConfigurationResponse getFeeConfigurationResponse() {
		return feeConfigurationResponse;
	}

	public String getEntityId() {
		return entityId;
	}

	public FeeEntity getFeeEntity() {
		return feeEntity;
	}

	public FeeHeadConfiguration getFeeHeadConfiguration() {
		return feeHeadConfiguration;
	}

	public double getAmount() {
		return amount;
	}

	public Module getModule() {
		return module;
	}

	@Override
	public String toString() {
		return "FeeAssignmentDetailsRow [instituteId=" + instituteId
				+ ", feeConfigurationResponse=" + feeConfigurationResponse
				+ ", entityId=" + entityId + ", feeHeadConfiguration="
				+ feeHeadConfiguration + ", amount=" + amount + ", module="
				+ module + "]";
	}

}
