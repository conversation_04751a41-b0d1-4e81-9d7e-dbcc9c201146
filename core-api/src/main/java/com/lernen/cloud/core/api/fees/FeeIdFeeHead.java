package com.lernen.cloud.core.api.fees;

import java.util.List;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeeIdFeeHead {

	private UUID feeId;

	private List<FeeHeadAmount> feeHeadAmountList;

	public FeeIdFeeHead() {

	}

	public FeeIdFeeHead(UUID feeId, List<FeeHeadAmount> feeHeadAmountList) {
		this.feeId = feeId;
		this.feeHeadAmountList = feeHeadAmountList;
	}

	public UUID getFeeId() {
		return feeId;
	}

	public List<FeeHeadAmount> getFeeHeadAmountList() {
		return feeHeadAmountList;
	}

	public void setFeeId(UUID feeId) {
		this.feeId = feeId;
	}

	public void setFeeHeadAmountList(List<FeeHeadAmount> feeHeadAmountList) {
		this.feeHeadAmountList = feeHeadAmountList;
	}

	@Override
	public String toString() {
		return "FeeIdFeeHead [feeId=" + feeId + ", feeHeadAmountList=" + feeHeadAmountList + "]";
	}

}
