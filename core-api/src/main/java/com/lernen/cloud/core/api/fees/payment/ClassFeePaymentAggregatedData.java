package com.lernen.cloud.core.api.fees.payment;

import com.lernen.cloud.core.api.fees.FeeEntity;

/**
 * 
 * <AUTHOR>
 *
 */
public class ClassFeePaymentAggregatedData {

	private final int instituteId;

	private final String standardId;

	private final String standardName;
	
	private final int level;

	private final double assignedAmount;

	private final double discountAmount;

	private final double collectedAmount;

	private final double dueAmount;

	public ClassFeePaymentAggregatedData(int instituteId, String standardId, String standardName,int level, double assignedAmount,
			double discountAmount, double collectedAmount) {
		this.instituteId = instituteId;
		this.standardId = standardId;
		this.standardName = standardName;
		this.level = level;
		this.assignedAmount = assignedAmount;
		this.discountAmount = discountAmount;
		this.collectedAmount = collectedAmount;
		dueAmount = assignedAmount - collectedAmount - discountAmount;
	}

	public ClassFeePaymentAggregatedData(int instituteId, String standardId, String standardName,int level, double assignedAmount,
										 double discountAmount, double collectedAmount, double dueAmount) {
		this.instituteId = instituteId;
		this.standardId = standardId;
		this.standardName = standardName;
		this.level = level;
		this.assignedAmount = assignedAmount;
		this.discountAmount = discountAmount;
		this.collectedAmount = collectedAmount;
		this.dueAmount = dueAmount;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public String getStandardId() {
		return standardId;
	}

	public String getStandardName() {
		return standardName;
	}
	
	

	public int getLevel() {
		return level;
	}

	public double getAssignedAmount() {
		return assignedAmount;
	}

	public double getDiscountAmount() {
		return discountAmount;
	}

	public double getCollectedAmount() {
		return collectedAmount;
	}

	public double getDueAmount() {
		return dueAmount;
	}

	@Override
	public String toString() {
		return "ClassFeePaymentAggregatedData [instituteId=" + instituteId + ", standardId=" + standardId
				+ ", standardName=" + standardName + ", level=" + level + ", assignedAmount=" + assignedAmount
				+ ", discountAmount=" + discountAmount + ", collectedAmount=" + collectedAmount + ", dueAmount="
				+ dueAmount + "]";
	}

}
