package com.lernen.cloud.core.api.fees.payment;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeeHeadInvoice {

	private final FeeHeadPaymentDetails feeHeadPaymentDetails;

	private final FeeHeadTransactionAmounts feeHeadTransactionAmounts;

	private final double dueAmountBeforeTransaction;

	public FeeHeadInvoice(FeeHeadPaymentDetails feeHeadPaymentDetails,
			FeeHeadTransactionAmounts feeHeadTransactionAmounts) {
		this.feeHeadPaymentDetails = feeHeadPaymentDetails;
		this.feeHeadTransactionAmounts = feeHeadTransactionAmounts;
		this.dueAmountBeforeTransaction = computeDueAmountBeforeTransaction();
	}

	public FeeHeadPaymentDetails getFeeHeadPaymentDetails() {
		return feeHeadPaymentDetails;
	}

	public FeeHeadTransactionAmounts getFeeHeadTransactionAmounts() {
		return feeHeadTransactionAmounts;
	}

	public double getDueAmountBeforeTransaction() {
		return dueAmountBeforeTransaction;
	}

	private double computeDueAmountBeforeTransaction() {

		return feeHeadPaymentDetails.getBalanceAmount()
				+ feeHeadTransactionAmounts.getPaidAmount()
				+ feeHeadTransactionAmounts.getInstantDiscount();
	}

	@Override
	public String toString() {
		return "FeeHeadInvoice [feeHeadPaymentDetails=" + feeHeadPaymentDetails
				+ ", feeHeadTransactionAmounts=" + feeHeadTransactionAmounts
				+ "]";
	}

}
