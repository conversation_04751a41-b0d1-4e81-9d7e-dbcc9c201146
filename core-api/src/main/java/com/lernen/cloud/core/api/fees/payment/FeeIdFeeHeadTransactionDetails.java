package com.lernen.cloud.core.api.fees.payment;

import java.util.List;

import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.fees.FeeConfigurationBasicInfo;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeeIdFeeHeadTransactionDetails {

	private final FeeConfigurationBasicInfo feeConfigurationBasicInfo;

	private final List<FeeHeadTransactionAmountsDetails> feeHeadTransactionAmountsDetails;

	private final double totalPaidAmount;

	private final double totalFineAmount;

	private final double totalInstantDiscountAmount;

	public FeeIdFeeHeadTransactionDetails(
			FeeConfigurationBasicInfo feeConfigurationBasicInfo,
			List<FeeHeadTransactionAmountsDetails> feeHeadTransactionAmountsDetails) {
		this.feeConfigurationBasicInfo = feeConfigurationBasicInfo;
		this.feeHeadTransactionAmountsDetails = feeHeadTransactionAmountsDetails;
		this.totalPaidAmount = computeTotalPaidAmount();
		this.totalFineAmount = computeTotalFineAmount();
		this.totalInstantDiscountAmount = computeTotalInstantDiscountAmount();
	}

	private double computeTotalPaidAmount() {
		double totalAmount = 0d;
		if (CollectionUtils.isEmpty(feeHeadTransactionAmountsDetails)) {
			return totalAmount;
		}

		for (FeeHeadTransactionAmountsDetails feeHeadTransactionAmountsDetails : feeHeadTransactionAmountsDetails) {
			totalAmount += feeHeadTransactionAmountsDetails.getPaidAmount();
		}
		return totalAmount;
	}

	private double computeTotalFineAmount() {
		double totalFine = 0d;
		if (CollectionUtils.isEmpty(feeHeadTransactionAmountsDetails)) {
			return totalFine;
		}

		for (FeeHeadTransactionAmountsDetails feeHeadTransactionAmountsDetails : feeHeadTransactionAmountsDetails) {
			totalFine += feeHeadTransactionAmountsDetails.getFineAmount();
		}
		return totalFine;
	}

	private double computeTotalInstantDiscountAmount() {
		double totalInstantDiscountAmount = 0d;
		if (CollectionUtils.isEmpty(feeHeadTransactionAmountsDetails)) {
			return totalInstantDiscountAmount;
		}

		for (FeeHeadTransactionAmountsDetails feeHeadTransactionAmountsDetails : feeHeadTransactionAmountsDetails) {
			totalInstantDiscountAmount += feeHeadTransactionAmountsDetails
					.getInstantDiscount();
		}
		return totalInstantDiscountAmount;
	}

	public FeeConfigurationBasicInfo getFeeConfigurationBasicInfo() {
		return feeConfigurationBasicInfo;
	}

	public List<FeeHeadTransactionAmountsDetails> getFeeHeadTransactionAmountsDetails() {
		return feeHeadTransactionAmountsDetails;
	}

	public double getTotalPaidAmount() {
		return totalPaidAmount;
	}

	public double getTotalFineAmount() {
		return totalFineAmount;
	}

	public double getTotalInstantDiscountAmount() {
		return totalInstantDiscountAmount;
	}

	@Override
	public String toString() {
		return "FeeIdFeeHeadTransactionDetails [feeConfigurationBasicInfo="
				+ feeConfigurationBasicInfo
				+ ", feeHeadTransactionAmountsDetails="
				+ feeHeadTransactionAmountsDetails + ", totalPaidAmount="
				+ totalPaidAmount + ", totalFineAmount=" + totalFineAmount
				+ ", totalInstantDiscountAmount=" + totalInstantDiscountAmount
				+ "]";
	}

}