package com.lernen.cloud.core.api.fees.payment;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeePaymentTransactionRow {

	private final FeePaymentTransactionMetaData feePaymentTransactionMetaData;

	private final UUID feeId;

	private final int feeHeadId;

	private final double paidAmount;

	private final double instantDiscountAmount;
	
	private final double fineAmount;

	public FeePaymentTransactionRow(FeePaymentTransactionMetaData feePaymentTransactionMetaData, UUID feeId,
			int feeHeadId, double paidAmount, double instantDiscountAmount, double fineAmount) {
		this.feePaymentTransactionMetaData = feePaymentTransactionMetaData;
		this.feeId = feeId;
		this.feeHeadId = feeHeadId;
		this.paidAmount = paidAmount;
		this.instantDiscountAmount = instantDiscountAmount;
		this.fineAmount = fineAmount;
	}

	public FeePaymentTransactionMetaData getFeePaymentTransactionMetaData() {
		return feePaymentTransactionMetaData;
	}

	public UUID getFeeId() {
		return feeId;
	}

	public int getFeeHeadId() {
		return feeHeadId;
	}

	public double getPaidAmount() {
		return paidAmount;
	}

	public double getInstantDiscountAmount() {
		return instantDiscountAmount;
	}

	
	public double getFineAmount() {
		return fineAmount;
	}

	@Override
	public String toString() {
		return "FeePaymentTransactionRow [feePaymentTransactionMetaData=" + feePaymentTransactionMetaData + ", feeId="
				+ feeId + ", feeHeadId=" + feeHeadId + ", paidAmount=" + paidAmount + ", instantDiscountAmount="
				+ instantDiscountAmount + ", fineAmount=" + fineAmount + "]";
	}

}
