package com.lernen.cloud.core.api.fees.payment;

import java.util.List;

import com.lernen.cloud.core.api.fees.FeeIdFeeHeadDetails;
import com.lernen.cloud.core.api.fees.StudentDiscountConfigurationData;
import com.lernen.cloud.core.api.student.Student;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentDueFeesData {

	private final Student student;
	private final List<StudentFeesLevelPaymentData> studentFeesLevelPaymentDataList;
	private final double totalDueAmount;
	private final double totalDueAmountWithFine;

	public StudentDueFeesData(Student student, List<StudentFeesLevelPaymentData> studentFeesLevelPaymentDataList) {
		this.student = student;
		this.studentFeesLevelPaymentDataList = studentFeesLevelPaymentDataList;
		this.totalDueAmount = computeTotalDueAmount();
		this.totalDueAmountWithFine = computeTotalDueAmountWithFine();
	}

	public Student getStudent() {
		return student;
	}

	public List<StudentFeesLevelPaymentData> getStudentFeesLevelPaymentDataList() {
		return studentFeesLevelPaymentDataList;
	}

	public double getTotalDueAmount() {
		return totalDueAmount;
	}

	public double getTotalDueAmountWithFine() {
		return totalDueAmountWithFine;
	}

	public double getDueFineAmount() {
		return getTotalDueAmountWithFine() - getTotalDueAmount();
	}

	/**
	 * 
	 * @return
	 */
	private double computeTotalDueAmount() {
		double totalDueAmount = 0d;
		for (StudentFeesLevelPaymentData studentFeesLevelPaymentData : studentFeesLevelPaymentDataList) {
			totalDueAmount += studentFeesLevelPaymentData.getDueAmount();
		}
		return totalDueAmount;
	}

	/**
	 * 
	 * @return
	 */
	private double computeTotalDueAmountWithFine() {
		double totalDueAmountWithFine = 0d;
		for (StudentFeesLevelPaymentData studentFeesLevelPaymentData : studentFeesLevelPaymentDataList) {
			totalDueAmountWithFine += studentFeesLevelPaymentData.getTotalDueAmountWithFine();
		}
		return totalDueAmountWithFine;
	}

}
