package com.lernen.cloud.core.api.fees.payment;

import com.lernen.cloud.core.api.student.StudentStatus;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentFeePaymentAggregatedPaidAmount {

	private final int instituteId;

	private final UUID studentId;

	private final String admissionNumber;

	private final String name;

	private final String standardName;

	private final String sectionName;

	private final UUID feeId;

	private final String feeName;

	private final int feeHeadId;

	private final double paidAmount;

	private final double instantDiscountAmount;

	private final double assignedDiscountAmount;

	private final double paidFineAmount;

	private final StudentStatus status;

	public StudentFeePaymentAggregatedPaidAmount(int instituteId, UUID studentId, String admissionNumber, String name,
			String standardName, String sectionName, UUID feeId, String feeName, int feeHeadId, double paidAmount,
			double instantDiscountAmount, double assignedDiscountAmount, double paidFineAmount, StudentStatus status) {
		this.instituteId = instituteId;
		this.studentId = studentId;
		this.admissionNumber = admissionNumber;
		this.name = name;
		this.standardName = standardName;
		this.sectionName = sectionName;
		this.feeId = feeId;
		this.feeName = feeName;
		this.feeHeadId = feeHeadId;
		this.paidAmount = paidAmount;
		this.instantDiscountAmount = instantDiscountAmount;
		this.assignedDiscountAmount = assignedDiscountAmount;
		this.paidFineAmount = paidFineAmount;
		this.status = status;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public UUID getStudentId() {
		return studentId;
	}

	public String getName() {
		return name;
	}

	public String getStandardName() {
		return standardName;
	}

	public String getSectionName() {
		return sectionName;
	}

	public UUID getFeeId() {
		return feeId;
	}

	public String getFeeName() {
		return feeName;
	}

	public int getFeeHeadId() {
		return feeHeadId;
	}

	public double getPaidAmount() {
		return paidAmount;
	}

	public double getInstantDiscountAmount() {
		return instantDiscountAmount;
	}

	public double getAssignedDiscountAmount() {
		return assignedDiscountAmount;
	}

	public String getAdmissionNumber() {
		return admissionNumber;
	}

	public double getPaidFineAmount() {
		return paidFineAmount;
	}

	public StudentStatus getStatus() {
		return status;
	}

	@Override
	public String toString() {
		return "StudentFeePaymentAggregatedPaidAmount{" +
				"instituteId=" + instituteId +
				", studentId=" + studentId +
				", admissionNumber='" + admissionNumber + '\'' +
				", name='" + name + '\'' +
				", standardName='" + standardName + '\'' +
				", sectionName='" + sectionName + '\'' +
				", feeId=" + feeId +
				", feeName='" + feeName + '\'' +
				", feeHeadId=" + feeHeadId +
				", paidAmount=" + paidAmount +
				", instantDiscountAmount=" + instantDiscountAmount +
				", assignedDiscountAmount=" + assignedDiscountAmount +
				", paidFineAmount=" + paidFineAmount +
				", status=" + status +
				'}';
	}

}
