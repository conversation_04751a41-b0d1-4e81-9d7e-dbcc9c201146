package com.lernen.cloud.core.api.fees.payment;

import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.fees.FeeType;
import com.lernen.cloud.core.api.fees.MonthYear;
import com.lernen.cloud.core.api.institute.Stream;
import com.lernen.cloud.core.api.student.StudentStatus;

import java.util.UUID;

public class StudentFeePaymentData implements
        Comparable<StudentFeePaymentData> {

    private final int instituteId;

    private final UUID studentId;

    private final String registrationNumber;
    private final String admissionNumber;

    private final String studentFullName;

    private final String fatherName;

    private final String primaryContactNumber;

    private final String whatsappNumber;

    private final String fatherContactNumber;

    private final String motherContactNumber;

    private final UUID standardId;

    private final String standardName;

    private final Stream stream;

    private final int standardLevel;

    private final String sectionName;

    private final Integer sectionId;

    private final StudentStatus status;

    private final UUID feeId;

    private final String feeName;

    private final FeeType feeType;

    private final Integer dueDate;

    private final MonthYear startMonthYear;

    private final double assignedAmount;

    private final double amountCollected;

    private final double givenDiscount;

    private final double remainingDiscountToBeGiven;

    private final double dueAmount;

    private final double dueAmountTillToday;

    private final double totalDueFineAmount;

    private final double totalPaidFineAmount;

    public StudentFeePaymentData(int instituteId, UUID studentId, String registrationNumber, String admissionNumber, String studentFullName, String fatherName, String primaryContactNumber, String whatsappNumber, String fatherContactNumber, String motherContactNumber, UUID standardId, String standardName, Stream stream, int standardLevel, String sectionName, Integer sectionId, StudentStatus status, UUID feeId, String feeName, FeeType feeType, Integer dueDate, MonthYear startMonthYear, double assignedAmount, double amountCollected, double givenDiscount, double remainingDiscountToBeGiven, double dueAmount,
                                 double dueAmountTillToday, double totalDueFineAmount, double totalPaidFineAmount) {
        this.instituteId = instituteId;
        this.studentId = studentId;
        this.registrationNumber = registrationNumber;
        this.admissionNumber = admissionNumber;
        this.studentFullName = studentFullName;
        this.fatherName = fatherName;
        this.primaryContactNumber = primaryContactNumber;
        this.whatsappNumber = whatsappNumber;
        this.fatherContactNumber = fatherContactNumber;
        this.motherContactNumber = motherContactNumber;
        this.standardId = standardId;
        this.standardName = standardName;
        this.stream = stream;
        this.standardLevel = standardLevel;
        this.sectionName = sectionName;
        this.sectionId = sectionId;
        this.status = status;
        this.feeId = feeId;
        this.feeName = feeName;
        this.feeType = feeType;
        this.dueDate = dueDate;
        this.dueAmountTillToday = dueAmountTillToday;
        this.startMonthYear = startMonthYear;
        this.assignedAmount = assignedAmount;
        this.amountCollected = amountCollected;
        this.givenDiscount = givenDiscount;
        this.remainingDiscountToBeGiven = remainingDiscountToBeGiven;
        this.dueAmount = dueAmount;
        this.totalDueFineAmount = totalDueFineAmount;
        this.totalPaidFineAmount = totalPaidFineAmount;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public UUID getStudentId() {
        return studentId;
    }

    public String getRegistrationNumber() {
        return registrationNumber;
    }

    public String getAdmissionNumber() {
        return admissionNumber;
    }

    public String getStudentFullName() {
        return studentFullName;
    }

    public String getFatherName() {
        return fatherName;
    }

    public String getPrimaryContactNumber() {
        return primaryContactNumber;
    }

    public String getWhatsappNumber() {
        return whatsappNumber;
    }

    public String getFatherContactNumber() {
        return fatherContactNumber;
    }

    public String getMotherContactNumber() {
        return motherContactNumber;
    }

    public UUID getStandardId() {
        return standardId;
    }

    public String getStandardName() {
        return standardName;
    }

    public Stream getStream() {
        return stream;
    }

    public int getStandardLevel() {
        return standardLevel;
    }

    public String getSectionName() {
        return sectionName;
    }

    public Integer getSectionId(){
        return sectionId;
    }

    public StudentStatus getStatus() {
        return status;
    }

    public UUID getFeeId() {
        return feeId;
    }

    public String getFeeName() {
        return feeName;
    }

    public FeeType getFeeType() {
        return feeType;
    }

    public Integer getDueDate() {
        return dueDate;
    }

    public MonthYear getStartMonthYear() {
        return startMonthYear;
    }

    public double getAssignedAmount() {
        return assignedAmount;
    }

    public double getAmountCollected() {
        return amountCollected;
    }

    public double getGivenDiscount() {
        return givenDiscount;
    }

    public double getRemainingDiscountToBeGiven() {
        return remainingDiscountToBeGiven;
    }

    public double getDueAmount() {
        return dueAmount;
    }

    public double getTotalDueFineAmount() {
        return totalDueFineAmount;
    }

    public double getTotalPaidFineAmount() {
        return totalPaidFineAmount;
    }

    public double getDueAmountTillToday() {
        return dueAmountTillToday;
    }

    @Override
    public String toString() {
        return "StudentFeePaymentData{" +
                "instituteId=" + instituteId +
                ", studentId=" + studentId +
                ", registrationNumber='" + registrationNumber + '\'' +
                ", admissionNumber='" + admissionNumber + '\'' +
                ", studentFullName='" + studentFullName + '\'' +
                ", fatherName='" + fatherName + '\'' +
                ", primaryContactNumber='" + primaryContactNumber + '\'' +
                ", whatsappNumber='" + whatsappNumber + '\'' +
                ", fatherContactNumber='" + fatherContactNumber + '\'' +
                ", motherContactNumber='" + motherContactNumber + '\'' +
                ", standardId=" + standardId +
                ", standardName='" + standardName + '\'' +
                ", stream=" + stream +
                ", standardLevel=" + standardLevel +
                ", sectionName='" + sectionName + '\'' +
                ", sectionId=" + sectionId +
                ", status=" + status +
                ", feeId=" + feeId +
                ", feeName='" + feeName + '\'' +
                ", feeType=" + feeType +
                ", dueDate=" + dueDate +
                ", startMonthYear=" + startMonthYear +
                ", assignedAmount=" + assignedAmount +
                ", amountCollected=" + amountCollected +
                ", givenDiscount=" + givenDiscount +
                ", remainingDiscountToBeGiven=" + remainingDiscountToBeGiven +
                ", dueAmount=" + dueAmount +
                ", dueAmountTillToday=" + dueAmountTillToday +
                ", totalDueFineAmount=" + totalDueFineAmount +
                ", totalPaidFineAmount=" + totalPaidFineAmount +
                '}';
    }

    @Override
    public int compareTo(StudentFeePaymentData o) {
        if (this.standardLevel < o.standardLevel) {
            return -1;
        }
        if (this.standardLevel > o.standardLevel) {
            return 1;
        }
        int sectionCompare = StringHelper.compareIgnoreCase(this.sectionName,
                o.sectionName);
        if (sectionCompare != 0) {
            return sectionCompare;
        }
        return StringHelper.compareIgnoreCase(this.studentFullName,
                o.studentFullName);
    }
}
