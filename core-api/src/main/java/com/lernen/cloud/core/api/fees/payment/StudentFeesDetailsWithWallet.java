package com.lernen.cloud.core.api.fees.payment;

import java.util.List;

public class StudentFeesDetailsWithWallet {

    private final List<StudentFeesDetails> studentFeesDetailsList;

    private final Double walletAmount;

    public StudentFeesDetailsWithWallet(List<StudentFeesDetails> studentFeesDetailsList, Double walletAmount) {
        this.studentFeesDetailsList = studentFeesDetailsList;
        this.walletAmount = walletAmount;
    }

    public List<StudentFeesDetails> getStudentFeesDetailsList() {
        return studentFeesDetailsList;
    }

    public Double getWalletAmount() {
        return walletAmount;
    }

    @Override
    public String toString() {
        return "StudentFeesDetailsWithWallet{" +
                "studentFeesDetailsList=" + studentFeesDetailsList +
                ", walletAmount=" + walletAmount +
                '}';
    }
}
