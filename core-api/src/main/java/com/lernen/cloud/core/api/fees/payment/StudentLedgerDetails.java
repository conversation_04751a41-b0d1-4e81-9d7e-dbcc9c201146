/**
 * 
 */
package com.lernen.cloud.core.api.fees.payment;

import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.student.Student;

/**
 * <AUTHOR>
 *
 */
public class StudentLedgerDetails {
	
	private final Student student;
	
	private Map<UUID, FeesInvoiceTransactionDetails> feesInvoiceTransactionDetailsMap;

	/**
	 * @param student
	 * @param feesInvoiceTransactionDetailsMap
	 */
	public StudentLedgerDetails(Student student,
			Map<UUID, FeesInvoiceTransactionDetails> feesInvoiceTransactionDetailsMap) {
		this.student = student;
		this.feesInvoiceTransactionDetailsMap = feesInvoiceTransactionDetailsMap;
	}

	/**
	 * @return the student
	 */
	public Student getStudent() {
		return student;
	}

	/**
	 * @return the feesInvoiceTransactionDetailsMap
	 */
	public Map<UUID, FeesInvoiceTransactionDetails> getFeesInvoiceTransactionDetailsMap() {
		return feesInvoiceTransactionDetailsMap;
	}

	/**
	 * @param feesInvoiceTransactionDetailsMap the feesInvoiceTransactionDetailsMap to set
	 */
	public void setFeesInvoiceTransactionDetailsMap(
			Map<UUID, FeesInvoiceTransactionDetails> feesInvoiceTransactionDetailsMap) {
		this.feesInvoiceTransactionDetailsMap = feesInvoiceTransactionDetailsMap;
	}
	
	
}
