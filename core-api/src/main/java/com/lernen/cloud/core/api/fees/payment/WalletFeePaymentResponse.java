package com.lernen.cloud.core.api.fees.payment;

/**
 * <AUTHOR>
 */

public class WalletFeePaymentResponse {

    private final boolean success;

    private final String message;

    private final StudentFeePaymentTransactionDetails studentFeePaymentTransactionDetails;

    public WalletFeePaymentResponse(boolean success, String message, StudentFeePaymentTransactionDetails studentFeePaymentTransactionDetails) {
        this.success = success;
        this.message = message;
        this.studentFeePaymentTransactionDetails = studentFeePaymentTransactionDetails;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getMessage() {
        return message;
    }

    public StudentFeePaymentTransactionDetails getStudentFeePaymentTransactionDetails() {
        return studentFeePaymentTransactionDetails;
    }

    @Override
    public String toString() {
        return "WalletFeePaymentResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", studentFeePaymentTransactionDetails=" + studentFeePaymentTransactionDetails +
                '}';
    }

}
