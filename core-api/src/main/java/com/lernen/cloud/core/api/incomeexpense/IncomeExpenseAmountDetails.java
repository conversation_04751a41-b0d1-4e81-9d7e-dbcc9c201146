/**
 * 
 */
package com.lernen.cloud.core.api.incomeexpense;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class IncomeExpenseAmountDetails {
	
	private int instituteId;
	
	private UUID categoryId;
	
	private String categoryName;
	
	private Integer transactionDate;
	
	private Double amount;

	public IncomeExpenseAmountDetails(int instituteId, UUID categoryId, String categoryName, Integer transactionDate,
			Double amount) {
		this.instituteId = instituteId;
		this.categoryId = categoryId;
		this.categoryName = categoryName;
		this.transactionDate = transactionDate;
		this.amount = amount;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(UUID categoryId) {
		this.categoryId = categoryId;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public Integer getTransactionDate() {
		return transactionDate;
	}

	public void setTransactionDate(Integer transactionDate) {
		this.transactionDate = transactionDate;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	@Override
	public String toString() {
		return "IncomeExpenseAmountDetails [instituteId=" + instituteId + ", categoryId=" + categoryId
				+ ", categoryName=" + categoryName + ", transactionDate=" + transactionDate + ", amount=" + amount
				+ "]";
	}
}
