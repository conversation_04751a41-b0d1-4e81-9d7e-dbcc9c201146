/**
 * 
 */
package com.lernen.cloud.core.api.incomeexpense;

import java.util.UUID;

import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.user.Document;

/**
 * <AUTHOR>
 *
 */
public class IncomeExpenseTransactionDetails {

	private int instituteId;

	private int academicSessionId;

	private UUID transactionId;

	private String transactionTitle;

	private TransactionMode transactionMode;

	private Double amount;

	private Integer transactionDate;

	private IncomeExpenseCategory incomeExpenseCategory;

	private IncomeExpenseEntity incomeExpenseEntity;

	private boolean addToTotal;

	private String transactionDescription;

	private Document<IncomeExpenseTransactionDocumentType> incomeExpenseTransactionDocument;

	private IncomeExpenseTransactionStatus incomeExpenseTransactionStatus;

	private IncomeExpenseTransactionType incomeExpenseTransactionType;

	private Integer createdTimestamp;

	private UUID createddUserId;

	private UUID updatedUserId;

	private Integer updatedTimestamp;

	private UUID transactionBy;

	private Staff transactionByStaff;

	public IncomeExpenseTransactionDetails(int instituteId,
			int academicSessionId, UUID transactionId, String transactionTitle,
			TransactionMode transactionMode, Double amount,
			Integer transactionDate,
			IncomeExpenseCategory incomeExpenseCategory,
			IncomeExpenseEntity incomeExpenseEntity, boolean addToTotal,
			String transactionDescription,
			Document<IncomeExpenseTransactionDocumentType> incomeExpenseTransactionDocument,
			IncomeExpenseTransactionStatus incomeExpenseTransactionStatus,
			IncomeExpenseTransactionType incomeExpenseTransactionType,
			Integer createdTimestamp, UUID createddUserId, UUID updatedUserId,
			Integer updatedTimestamp, UUID transactionBy, Staff transactionByStaff) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.transactionId = transactionId;
		this.transactionTitle = transactionTitle;
		this.transactionMode = transactionMode;
		this.amount = amount;
		this.transactionDate = transactionDate;
		this.incomeExpenseCategory = incomeExpenseCategory;
		this.incomeExpenseEntity = incomeExpenseEntity;
		this.addToTotal = addToTotal;
		this.transactionDescription = transactionDescription;
		this.incomeExpenseTransactionDocument = incomeExpenseTransactionDocument;
		this.incomeExpenseTransactionStatus = incomeExpenseTransactionStatus;
		this.incomeExpenseTransactionType = incomeExpenseTransactionType;
		this.createdTimestamp = createdTimestamp;
		this.createddUserId = createddUserId;
		this.updatedUserId = updatedUserId;
		this.updatedTimestamp = updatedTimestamp;
		this.transactionBy = transactionBy;
		this.transactionByStaff = transactionByStaff;
	}

	public IncomeExpenseTransactionDetails() {

	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	public UUID getTransactionId() {
		return transactionId;
	}

	public void setTransactionId(UUID transactionId) {
		this.transactionId = transactionId;
	}

	public String getTransactionTitle() {
		return transactionTitle;
	}

	public void setTransactionTitle(String transactionTitle) {
		this.transactionTitle = transactionTitle;
	}

	public TransactionMode getTransactionMode() {
		return transactionMode;
	}

	public String getTransactionModeDisplayName() {
		return transactionMode.getDisplayName();
	}

	public void setTransactionMode(TransactionMode transactionMode) {
		this.transactionMode = transactionMode;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public Integer getTransactionDate() {
		return transactionDate;
	}

	public void setTransactionDate(Integer transactionDate) {
		this.transactionDate = transactionDate;
	}

	public IncomeExpenseCategory getIncomeExpenseCategory() {
		return incomeExpenseCategory;
	}

	public void setIncomeExpenseCategory(
			IncomeExpenseCategory incomeExpenseCategory) {
		this.incomeExpenseCategory = incomeExpenseCategory;
	}

	public IncomeExpenseEntity getIncomeExpenseEntity() {
		return incomeExpenseEntity;
	}

	public void setIncomeExpenseEntity(
			IncomeExpenseEntity incomeExpenseEntity) {
		this.incomeExpenseEntity = incomeExpenseEntity;
	}

	public boolean getAddToTotal() {
		return addToTotal;
	}

	public void setAddToTotal(boolean addToTotal) {
		this.addToTotal = addToTotal;
	}

	public String getTransactionDescription() {
		return transactionDescription;
	}

	public void setTransactionDescription(String transactionDescription) {
		this.transactionDescription = transactionDescription;
	}

	public Document<IncomeExpenseTransactionDocumentType> getIncomeExpenseTransactionDocument() {
		return incomeExpenseTransactionDocument;
	}

	public void setIncomeExpenseTransactionDocument(
			Document<IncomeExpenseTransactionDocumentType> incomeExpenseTransactionDocument) {
		this.incomeExpenseTransactionDocument = incomeExpenseTransactionDocument;
	}

	public IncomeExpenseTransactionStatus getIncomeExpenseTransactionStatus() {
		return incomeExpenseTransactionStatus;
	}

	public void setIncomeExpenseTransactionStatus(
			IncomeExpenseTransactionStatus incomeExpenseTransactionStatus) {
		this.incomeExpenseTransactionStatus = incomeExpenseTransactionStatus;
	}

	public IncomeExpenseTransactionType getIncomeExpenseTransactionType() {
		return incomeExpenseTransactionType;
	}

	public void setIncomeExpenseTransactionType(
			IncomeExpenseTransactionType incomeExpenseTransactionType) {
		this.incomeExpenseTransactionType = incomeExpenseTransactionType;
	}

	public Integer getCreatedTimestamp() {
		return createdTimestamp;
	}

	public void setCreatedTimestamp(Integer createdTimestamp) {
		this.createdTimestamp = createdTimestamp;
	}

	public UUID getCreateddUserId() {
		return createddUserId;
	}

	public void setCreateddUserId(UUID createddUserId) {
		this.createddUserId = createddUserId;
	}

	public UUID getUpdatedUserId() {
		return updatedUserId;
	}

	public void setUpdatedUserId(UUID updatedUserId) {
		this.updatedUserId = updatedUserId;
	}

	public Integer getUpdatedTimestamp() {
		return updatedTimestamp;
	}

	public void setUpdatedTimestamp(Integer updatedTimestamp) {
		this.updatedTimestamp = updatedTimestamp;
	}

	public UUID getTransactionBy() {
		return transactionBy;
	}

	public void setTransactionBy(UUID transactionBy) {
		this.transactionBy = transactionBy;
	}

	public boolean isAddToTotal() {
		return addToTotal;
	}

	public Staff getTransactionByStaff() {
		return transactionByStaff;
	}

	public void setTransactionByStaff(Staff transactionByStaff) {
		this.transactionByStaff = transactionByStaff;
	}

	@Override
	public String toString() {
		return "IncomeExpenseTransactionDetails{" +
				"instituteId=" + instituteId +
				", academicSessionId=" + academicSessionId +
				", transactionId=" + transactionId +
				", transactionTitle='" + transactionTitle + '\'' +
				", transactionMode=" + transactionMode +
				", amount=" + amount +
				", transactionDate=" + transactionDate +
				", incomeExpenseCategory=" + incomeExpenseCategory +
				", incomeExpenseEntity=" + incomeExpenseEntity +
				", addToTotal=" + addToTotal +
				", transactionDescription='" + transactionDescription + '\'' +
				", incomeExpenseTransactionDocument=" + incomeExpenseTransactionDocument +
				", incomeExpenseTransactionStatus=" + incomeExpenseTransactionStatus +
				", incomeExpenseTransactionType=" + incomeExpenseTransactionType +
				", createdTimestamp=" + createdTimestamp +
				", createddUserId=" + createddUserId +
				", updatedUserId=" + updatedUserId +
				", updatedTimestamp=" + updatedTimestamp +
				", transactionBy=" + transactionBy +
				", transactionByStaff=" + transactionByStaff +
				'}';
	}

}
