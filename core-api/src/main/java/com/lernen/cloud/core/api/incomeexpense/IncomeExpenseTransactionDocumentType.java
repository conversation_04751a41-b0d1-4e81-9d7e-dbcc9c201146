/**
 * 
 */
package com.lernen.cloud.core.api.incomeexpense;

import com.lernen.cloud.core.api.common.DocumentType;

/**
 * <AUTHOR>
 *
 */
public enum IncomeExpenseTransactionDocumentType implements DocumentType {

	RECIEPT("Reciept", "Reciept"),
	PAYSLIP("PaySlip", "PaySlip"),
	OTHER(null, "Other");

	private String documentName;
	private String displayName;
	private boolean isThumbnail;

	private IncomeExpenseTransactionDocumentType(String documentName,
			String displayName, boolean isThumbnail) {
		this.documentName = documentName;
		this.displayName = displayName;
		this.isThumbnail = isThumbnail;
	}

	private IncomeExpenseTransactionDocumentType(String documentName,
												 String displayName) {
		this.documentName = documentName;
		this.displayName = displayName;
		this.isThumbnail = false;
	}

	@Override
	public String getDocumentName() {
		return documentName;
	}
	@Override
	public String getDisplayName() {
		return displayName;
	}

	@Override
	public boolean isThumbnail() { return isThumbnail; }
}
