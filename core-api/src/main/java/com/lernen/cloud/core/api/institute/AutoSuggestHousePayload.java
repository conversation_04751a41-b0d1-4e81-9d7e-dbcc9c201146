package com.lernen.cloud.core.api.institute;

import com.lernen.cloud.core.api.user.Gender;

import java.util.UUID;

/**
 * <AUTHOR>
 * @created_at 26/07/23 : 09:40
 **/
public class AutoSuggestHousePayload {
    private UUID studentId;
    private UUID studentStandardId;
    private Gender gender;
    private UUID siblingStudentId;
    private UUID siblingGroupId;
    private boolean clearCurrentHouse;

    public AutoSuggestHousePayload() {
    }

    public AutoSuggestHousePayload(UUID studentId, UUID studentStandardId, Gender gender, UUID siblingStudentId, UUID siblingGroupId, boolean clearCurrentHouse) {
        this.studentId = studentId;
        this.studentStandardId = studentStandardId;
        this.gender = gender;
        this.siblingStudentId = siblingStudentId;
        this.siblingGroupId = siblingGroupId;
        this.clearCurrentHouse = clearCurrentHouse;
    }

    public UUID getStudentId() {
        return studentId;
    }

    public void setStudentId(UUID studentId) {
        this.studentId = studentId;
    }

    public UUID getStudentStandardId() {
        return studentStandardId;
    }

    public void setStudentStandardId(UUID studentStandardId) {
        this.studentStandardId = studentStandardId;
    }

    public Gender getGender() {
        return gender;
    }

    public void setGender(Gender gender) {
        this.gender = gender;
    }

    public UUID getSiblingStudentId() {
        return siblingStudentId;
    }

    public void setSiblingStudentId(UUID siblingStudentId) {
        this.siblingStudentId = siblingStudentId;
    }

    public UUID getSiblingGroupId() {
        return siblingGroupId;
    }

    public void setSiblingGroupId(UUID siblingGroupId) {
        this.siblingGroupId = siblingGroupId;
    }

    public boolean isClearCurrentHouse() {
        return clearCurrentHouse;
    }

    public void setClearCurrentHouse(boolean clearCurrentHouse) {
        this.clearCurrentHouse = clearCurrentHouse;
    }

    @Override
    public String toString() {
        return "AutoSuggestHousePayload{" +
                "studentId=" + studentId +
                ", studentStandardId=" + studentStandardId +
                ", gender=" + gender +
                ", siblingStudentId=" + siblingStudentId +
                ", siblingGroupId=" + siblingGroupId +
                ", clearCurrentHouse=" + clearCurrentHouse +
                '}';
    }
}
