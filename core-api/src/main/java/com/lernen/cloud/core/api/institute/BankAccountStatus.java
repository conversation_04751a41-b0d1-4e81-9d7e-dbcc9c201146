package com.lernen.cloud.core.api.institute;

import com.lernen.cloud.core.api.staff.AccountType;
import org.apache.commons.lang3.StringUtils;

public enum BankAccountStatus {
	ACTIVE, INACTIVE;

	public static BankAccountStatus getBankAccountStatus(String bankAccountType) {
		if (StringUtils.isBlank(bankAccountType)) {
			return null;
		}
		for (BankAccountStatus bankAccountTypeEnum : BankAccountStatus.values()) {
			if (bankAccountTypeEnum.name().equalsIgnoreCase(bankAccountType)) {
				return bankAccountTypeEnum;
			}
		}
		return null;
	}
}
