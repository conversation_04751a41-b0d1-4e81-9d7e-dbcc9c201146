package com.lernen.cloud.core.api.institute;

public class CloneStandardWithStaffDetailsPayload {

    private int srcInstituteId;
    private int destInstituteId;
    private int srcAcademicSessionId;
    private int destAcademicSessionId;

    public CloneStandardWithStaffDetailsPayload() {
    }
    
    public CloneStandardWithStaffDetailsPayload(int srcInstituteId, int destInstituteId, int srcAcademicSessionId,
            int destAcademicSessionId) {
        this.srcInstituteId = srcInstituteId;
        this.destInstituteId = destInstituteId;
        this.srcAcademicSessionId = srcAcademicSessionId;
        this.destAcademicSessionId = destAcademicSessionId;
    }

    public int getSrcInstituteId() {
        return srcInstituteId;
    }

    public void setSrcInstituteId(int srcInstituteId) {
        this.srcInstituteId = srcInstituteId;
    }

    public int getDestInstituteId() {
        return destInstituteId;
    }

    public void setDestInstituteId(int destInstituteId) {
        this.destInstituteId = destInstituteId;
    }

    public int getSrcAcademicSessionId() {
        return srcAcademicSessionId;
    }

    public void setSrcAcademicSessionId(int srcAcademicSessionId) {
        this.srcAcademicSessionId = srcAcademicSessionId;
    }

    public int getDestAcademicSessionId() {
        return destAcademicSessionId;
    }

    public void setDestAcademicSessionId(int destAcademicSessionId) {
        this.destAcademicSessionId = destAcademicSessionId;
    }

    @Override
    public String toString() {
        return "CloneStandardWithStaffDetailsPayload [srcInstituteId=" + srcInstituteId + ", destInstituteId="
                + destInstituteId + ", srcAcademicSessionId=" + srcAcademicSessionId
                + ", destAcademicSessionId=" + destAcademicSessionId + "]";
    }
}
