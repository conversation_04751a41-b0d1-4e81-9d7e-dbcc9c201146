package com.lernen.cloud.core.api.institute;

import com.lernen.cloud.core.api.course.ClassCourses;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffLite;

import java.util.List;

/**
 * <AUTHOR>
 * @created_at 29/02/24 : 14:53
 **/
public class CoursesStaffDetailsLite {

    private final List<ClassCourses> classCoursesList;

    private final List<StaffLite> staffLiteList;

    public CoursesStaffDetailsLite(List<ClassCourses> classCoursesList, List<StaffLite> staffLiteList) {
        this.classCoursesList = classCoursesList;
        this.staffLiteList = staffLiteList;
    }

    public List<ClassCourses> getClassCoursesList() {
        return classCoursesList;
    }

    public List<StaffLite> getStaffLiteList() {
        return staffLiteList;
    }

    @Override
    public String toString() {
        return "CoursesStaffDetailsLite{" +
                "classCoursesList=" + classCoursesList +
                ", staffLiteList=" + staffLiteList +
                '}';
    }
}
