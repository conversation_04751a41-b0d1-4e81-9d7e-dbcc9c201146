package com.lernen.cloud.core.api.institute;

import com.lernen.cloud.core.api.staff.AccountType;

import java.util.UUID;

public class InstituteBankAccountDetails {

	private int instituteId;

	private UUID accountId;

	private AccountType accountType;

	private String bankName;

	private String branchName;

	private String accountHolderName;

	private String accountNumber;

	private String ifscCode;

	private BankAccountStatus status;

	private boolean primary;

	private Integer createdAt;

	private Integer updatedAt;


	public InstituteBankAccountDetails(int instituteId, UUID accountId, AccountType accountType, String bankName, String branchName, String accountHolderName, String accountNumber, String ifscCode, BankAccountStatus status, boolean primary, Integer createdAt, Integer updatedAt) {
		this.instituteId = instituteId;
		this.accountId = accountId;
		this.accountType = accountType;
		this.bankName = bankName;
		this.branchName = branchName;
		this.accountHolderName = accountHolderName;
		this.accountNumber = accountNumber;
		this.ifscCode = ifscCode;
		this.status = status;
		this.primary = primary;
		this.createdAt = createdAt;
		this.updatedAt = updatedAt;
	}

	public InstituteBankAccountDetails() {
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getAccountId() {
		return accountId;
	}

	public void setAccountId(UUID accountId) {
		this.accountId = accountId;
	}

	public AccountType getAccountType() {
		return accountType;
	}

	public void setAccountType(AccountType accountType) {
		this.accountType = accountType;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBranchName() {
		return branchName;
	}

	public void setBranchName(String branchName) {
		this.branchName = branchName;
	}

	public String getAccountHolderName() {
		return accountHolderName;
	}

	public void setAccountHolderName(String accountHolderName) {
		this.accountHolderName = accountHolderName;
	}

	public String getAccountNumber() {
		return accountNumber;
	}

	public void setAccountNumber(String accountNumber) {
		this.accountNumber = accountNumber;
	}

	public String getIfscCode() {
		return ifscCode;
	}

	public void setIfscCode(String ifscCode) {
		this.ifscCode = ifscCode;
	}

	public BankAccountStatus getStatus() {
		return status;
	}

	public void setStatus(BankAccountStatus status) {
		this.status = status;
	}

	public boolean isPrimary() {
		return primary;
	}

	public void setPrimary(boolean primary) {
		this.primary = primary;
	}

	public Integer getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Integer createdAt) {
		this.createdAt = createdAt;
	}

	public Integer getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Integer updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	public String toString() {
		return "InstituteBankAccountDetails{" +
				"instituteId=" + instituteId +
				", accountId=" + accountId +
				", accountType=" + accountType +
				", bankName='" + bankName + '\'' +
				", branchName='" + branchName + '\'' +
				", accountHolderName='" + accountHolderName + '\'' +
				", accountNumber='" + accountNumber + '\'' +
				", ifscCode='" + ifscCode + '\'' +
				", status=" + status +
				", primary=" + primary +
				", createdAt=" + createdAt +
				", updatedAt=" + updatedAt +
				'}';
	}
}
