package com.lernen.cloud.core.api.institute;

import com.lernen.cloud.core.api.course.ClassCourses;
import com.lernen.cloud.core.api.staff.Staff;

import java.util.List;

public class InstituteClassCoursesStaffDetails {

    private final List<ClassCourses> classCoursesList;

    private final List<Staff> staffList;

    public InstituteClassCoursesStaffDetails(List<ClassCourses> classCoursesList, List<Staff> staffList) {
        this.classCoursesList = classCoursesList;
        this.staffList = staffList;
    }

    public List<ClassCourses> getClassCoursesList() {
        return classCoursesList;
    }

    public List<Staff> getStaffList() {
        return staffList;
    }

    @Override
    public String toString() {
        return "InstituteClassCoursesStaffDetails{" +
                "classCoursesList=" + classCoursesList +
                ", staffList=" + staffList +
                '}';
    }
}
