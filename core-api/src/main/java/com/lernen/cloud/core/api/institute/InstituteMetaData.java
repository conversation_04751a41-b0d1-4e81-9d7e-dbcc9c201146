package com.lernen.cloud.core.api.institute;

import com.lernen.cloud.core.api.configurations.MetaDataPreferences;

/**
 * 
 * <AUTHOR>
 *
 */
public class InstituteMetaData {

	private final MetaDataPreferences metaDataPreferences;

	private final String nextRegistrationNumber;

	private final String nextAdmissionNumber;

	private final String nextStaffNumber;

	public InstituteMetaData(MetaDataPreferences metaDataPreferences,
			String nextRegistrationNumber, String nextAdmissionNumber,
							 String nextStaffNumber) {
		this.metaDataPreferences = metaDataPreferences;
		this.nextRegistrationNumber = nextRegistrationNumber;
		this.nextAdmissionNumber = nextAdmissionNumber;
		this.nextStaffNumber = nextStaffNumber;
	}

	public MetaDataPreferences getMetaDataPreferences() {
		return metaDataPreferences;
	}

	public String getNextRegistrationNumber() {
		return nextRegistrationNumber;
	}

	public String getNextAdmissionNumber() {
		return nextAdmissionNumber;
	}

	public String getNextStaffNumber() {
		return nextStaffNumber;
	}

	@Override
	public String toString() {
		return "InstituteMetaData{" +
				"metaDataPreferences=" + metaDataPreferences +
				", nextRegistrationNumber='" + nextRegistrationNumber + '\'' +
				", nextAdmissionNumber='" + nextAdmissionNumber + '\'' +
				", nextStaffNumber='" + nextStaffNumber + '\'' +
				'}';
	}

}
