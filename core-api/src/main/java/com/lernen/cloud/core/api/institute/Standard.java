package com.lernen.cloud.core.api.institute;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.UUID;

public class Standard implements Comparable<Standard> {

	private int instituteId;

	private Integer academicSessionId;

	private UUID standardId;

	private String standardName;

	private Stream stream;

	/**
	 * Used for sorting standards
	 */
	private int level;

	private List<StandardSections> standardSectionList;

	private String displayName;

	private String displayNameWithSection;

	private Integer studentCount;

	public Standard(int instituteId, Integer academicSessionId, UUID standardId,
			String standardName, Stream stream, int level,
			List<StandardSections> standardSectionList) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.standardId = standardId;
		this.standardName = standardName;
		this.stream = stream;
		this.level = level;
		this.standardSectionList = standardSectionList;
		this.displayName = getStandardDisplayName(stream, standardName);
		this.displayNameWithSection = getStandardDisplayName(stream,
				standardName,
				CollectionUtils.isEmpty(standardSectionList)
						? null
						: standardSectionList.get(0));

		studentCount = computeTotalStudent();
	}

	@JsonIgnore
	public static String getStandardDisplayName(Stream stream,
			String standardName) {
		return stream == null || Stream.NA.equals(stream)
				? standardName
				: standardName + " (" + stream.getDisplayName() + ")";
	}

	@JsonIgnore
	public static String getStandardDisplayName(Stream stream,
			String standardName, StandardSections standardSection) {
		String standardDisplayName = getStandardDisplayName(stream,
				standardName);

		return standardSection == null
				? standardDisplayName
				: standardDisplayName + "-" + standardSection.getSectionName();

	}

	private Integer computeTotalStudent() {
		if (CollectionUtils.isEmpty(standardSectionList)) {
			return null;
		}
		int count = 0;
		for (StandardSections standardSections : standardSectionList) {
			if (standardSections.getStudentCount() == null) {
				return null;
			}
			count += standardSections.getStudentCount();
		}
		return count;
	}

	// Default Constructor
	public Standard() {
	}

	public String getDisplayNameWithSection() {
		return displayNameWithSection;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public Integer getAcademicSessionId() {
		return academicSessionId;
	}

	public void setAcademicSessionId(Integer academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	public UUID getStandardId() {
		return standardId;
	}

	public void setStandardId(UUID standardId) {
		this.standardId = standardId;
	}

	public String getStandardName() {
		return standardName;
	}

	public void setStandardName(String standardName) {
		this.standardName = standardName;
	}

	public Stream getStream() {
		return stream;
	}

	public void setStream(Stream stream) {
		this.stream = stream;
	}

	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

	public List<StandardSections> getStandardSectionList() {
		return standardSectionList;
	}

	public void setStandardSectionList(
			List<StandardSections> standardSectionList) {
		this.standardSectionList = standardSectionList;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public Integer getStudentCount() {
		return studentCount;
	}

	public void setStudentCount(Integer studentCount) {
		this.studentCount = studentCount;
	}

	@Override
	public int compareTo(Standard o) {
		if (this.level == o.level) {
			return 0;
		} else if (this.level < o.level) {
			return -1;
		} else {
			return 1;
		}
	}

	@Override
	public String toString() {
		return "Standard [instituteId=" + instituteId + ", academicSessionId="
				+ academicSessionId + ", standardId=" + standardId
				+ ", standardName=" + standardName + ", stream=" + stream
				+ ", level=" + level + ", standardSectionList="
				+ standardSectionList + ", displayName=" + displayName
				+ ", displayNameWithSection=" + displayNameWithSection
				+ ", studentCount=" + studentCount + "]";
	}

}
