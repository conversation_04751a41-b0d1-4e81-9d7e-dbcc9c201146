package com.lernen.cloud.core.api.institute;

import com.lernen.cloud.core.api.user.Gender;

import java.util.UUID;

public class StandardSectionGenderCountRow implements Comparable<StandardSectionGenderCountRow> {

	private int instituteId;

	private int academicSessionId;

	private UUID standardId;

	private String standardName;

	private Stream stream;

	private int level;

	private Integer sectionId;

	private String sectionName;

	private int studentCount;

	private Gender gender;

	private int genderCount;

	public StandardSectionGenderCountRow(int instituteId, int academicSessionId, UUID standardId, Stream stream, String standardName, int level, Integer sectionId, String sectionName, int studentCount, Gender gender, int genderCount) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.standardId = standardId;
		this.stream = stream;
		this.standardName = standardName;
		this.level = level;
		this.sectionId = sectionId;
		this.sectionName = sectionName;
		this.studentCount = studentCount;
		this.gender = gender;
		this.genderCount = genderCount;
	}

	// Default Constructor
	public StandardSectionGenderCountRow() {
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	public UUID getStandardId() {
		return standardId;
	}

	public void setStandardId(UUID standardId) {
		this.standardId = standardId;
	}

	public String getStandardName() {
		return standardName;
	}

	public void setStandardName(String standardName) {
		this.standardName = standardName;
	}

	public Stream getStream() {
		return stream;
	}

	public void setStream(Stream stream) {
		this.stream = stream;
	}

	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

	public Integer getSectionId() {
		return sectionId;
	}

	public void setSectionId(Integer sectionId) {
		this.sectionId = sectionId;
	}

	public int getStudentCount() {
		return studentCount;
	}

	public void setStudentCount(int studentCount) {
		this.studentCount = studentCount;
	}

	public String getSectionName() {
		return sectionName;
	}

	public void setSectionName(String sectionName) {
		this.sectionName = sectionName;
	}

	public Gender getGender() {
		return gender;
	}

	public void setGender(Gender gender) {
		this.gender = gender;
	}

	public int getGenderCount() {
		return genderCount;
	}

	public void setGenderCount(int genderCount) {
		this.genderCount = genderCount;
	}

	@Override
	public int compareTo(StandardSectionGenderCountRow o) {
		if (this.level == o.level) {
			return 0;
		} else if (this.level < o.level) {
			return -1;
		} else {
			return 1;
		}
	}

	@Override
	public String toString() {
		return "StandardSectionwiseCountDetail{" + "instituteId=" + instituteId + ", academicSessionId=" + academicSessionId + ", standardId=" + standardId + ", standardName='" + standardName + '\'' + ", stream=" + stream + ", level=" + level + ", sectionId=" + sectionId + ", sectionName='" + sectionName + '\'' + ", studentCount=" + studentCount + ", gender='" + gender + '\'' + ", genderCount=" + genderCount + '}';
	}

}
