package com.lernen.cloud.core.api.institute;

import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Set;

public class StandardSections {

	private int sectionId;

	private String sectionName;

	private Integer studentCount;

	public StandardSections(int sectionId, String sectionName) {
		this.sectionId = sectionId;
		this.sectionName = sectionName;
	}

	public StandardSections(int sectionId, String sectionName, Integer studentCount) {
		this.sectionId = sectionId;
		this.sectionName = sectionName;
		this.studentCount = studentCount;
	}

	// Dummy Constructor
	public StandardSections() {
	}

	public int getSectionId() {
		return sectionId;
	}

	public void setSectionId(int sectionId) {
		this.sectionId = sectionId;
	}

	public String getSectionName() {
		return sectionName;
	}

	public void setSectionName(String sectionName) {
		this.sectionName = sectionName;
	}

	public Integer getStudentCount() {
		return studentCount;
	}

	public void setStudentCount(Integer studentCount) {
		this.studentCount = studentCount;
	}

	@Override
	public String toString() {
		return "StandardSections [sectionId=" + sectionId + ", sectionName=" + sectionName + ", studentCount="
				+ studentCount + "]";
	}

	public static String getSectionsNameStr(Set<Integer> sectionIds, List<StandardSections> standardSectionsList) {
		StringBuilder sectionNames = new StringBuilder();
		if (sectionIds != null && !CollectionUtils.isEmpty(standardSectionsList)) {
			String delimeter = "";
			for (StandardSections standardSections : standardSectionsList) {
				if (sectionIds.contains(standardSections.getSectionId())) {
					sectionNames.append(delimeter).append(standardSections.getSectionName());
					delimeter = ", ";
				}
			}
		}
		return sectionNames.toString();
	}
}
