package com.lernen.cloud.core.api.institute;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class Time implements Comparable<Time>{

	public static final String TIME_FORMAT_DELIMITER = ":";

	private int hour;

	private int minute;

	private int second;

	public Time(int hour, int minute, int second) {
		this.hour = hour;
		this.minute = minute;
		this.second = second;
	}

	public Time() {

	}

	public int getHour() {
		return hour;
	}

	public void setHour(int hour) {
		this.hour = hour;
	}

	public int getMinute() {
		return minute;
	}

	public void setMinute(int minute) {
		this.minute = minute;
	}

	public int getSecond() {
		return second;
	}

	public void setSecond(int second) {
		this.second = second;
	}

	public String display(boolean showSeconds) {
		StringBuilder sb = new StringBuilder();

		String hour = "0" + this.hour;
		hour = hour.substring(hour.length() - 2);

		String minute = "0" + this.minute;
		minute = minute.substring(minute.length() - 2);

		sb.append(hour).append(TIME_FORMAT_DELIMITER).append(minute);

		if (showSeconds) {
			String second = "0" + this.second;
			second = second.substring(second.length() - 2);
			sb.append(TIME_FORMAT_DELIMITER).append(second);
		}
		return sb.toString();
	}

	@Override
	public int compareTo(Time time) {
		if (this.hour < time.hour) {
			return -1;
		} else if (this.hour > time.hour) {
			return 1;
		} else {
			if (this.minute < time.minute) {
				return -1;
			} else if (this.minute > time.minute) {
				return 1;
			} else {
				if (this.second < time.second) {
					return -1;
				} else if (this.second > time.second) {
					return 1;
				} else {
					return 0;
				}
			}
		}
	}
	public int toMinutes() {
		return (this.hour * 60) + this.minute;
	}

	public static Time fromMinutes(int totalMinutes) {
		int hours = totalMinutes / 60;
		int minutes = totalMinutes % 60;
		return new Time(hours, minutes, 0);
	}

	public boolean isInList(List<Time> timeList) {
		for(Time time : timeList) {
			if(this.compareTo(time) == 0) {
				return true;
			}
		}
		return false;
	}

	@Override
	public String toString() {
		return "Time [hour=" + hour + ", minute=" + minute + ", second=" + second + "]";
	}

	//TODO:check if 'checkOverlapping' methid can be used instead of this
	public boolean thisGreaterOrEqual(Time time) {
		if (this == null || time == null) {
			return true;
		}

		return this.hour > time.hour ? true : this.hour < time.hour ? false :
			this.minute > time.minute ? true : this.minute < time.minute ? false :
			this.second > time.second ? true : this.second < time.second ? false : true;
	}

	// Method to check if this is after time
	public boolean isAfter(Time time) {
		return this.compareTo(time) > 0;
	}

	public String validateTime() {
		if (this.hour < 0 || this.hour > 23) {
			return "Hour out of range.";
		}
		if (this.minute < 0 || this.minute > 59) {
			return "Minute out of range.";
		}
		return null;
	}

	/**
	 * @param time
	 * @return
	 */
	public boolean checkOverlapping(Time time) {
		if (this == null || time == null) {
			return false;
		}
		if(this.getHour() > time.getHour()) {
			return true;
		}

		if(this.getHour() == time.getHour() && this.getMinute() > time.getMinute()) {
			return true;
		}

		if(this.getHour() == time.getHour() && this.getMinute() == time.getMinute() && this.getSecond() > time.getSecond()) {
			return true;
		}

		return false;
	}

}
