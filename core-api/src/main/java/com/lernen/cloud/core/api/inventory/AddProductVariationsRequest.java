package com.lernen.cloud.core.api.inventory;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.user.Gender;

/**
 * 
 * <AUTHOR>
 *
 */
public class AddProductVariationsRequest {

	private int instituteId;

	private UUID productId;

	private Set<String> sizes;

	private Set<Color> colors;

	private Set<UserGroup> userGroups;

	private Set<Gender> genders;

	public AddProductVariationsRequest() {
	}

	public AddProductVariationsRequest(int instituteId, UUID productId, Set<String> sizes, Set<Color> colors,
			Set<UserGroup> userGroups, Set<Gender> genders) {
		this.instituteId = instituteId;
		this.productId = productId;
		this.sizes = sizes;
		this.colors = colors;
		this.userGroups = userGroups;
		this.genders = genders;
		format(this);
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getProductId() {
		return productId;
	}

	public void setProductId(UUID productId) {
		this.productId = productId;
	}

	public Set<String> getSizes() {
		return sizes;
	}

	public void setSizes(Set<String> sizes) {
		this.sizes = sizes;
	}

	public Set<Color> getColors() {
		return colors;
	}

	public void setColors(Set<Color> colors) {
		this.colors = colors;
	}

	public Set<UserGroup> getUserGroups() {
		return userGroups;
	}

	public void setUserGroups(Set<UserGroup> userGroups) {
		this.userGroups = userGroups;
	}

	public Set<Gender> getGenders() {
		return genders;
	}

	public void setGenders(Set<Gender> genders) {
		this.genders = genders;
	}

	public static void format(AddProductVariationsRequest addProductVariationsRequest) {
		if (!CollectionUtils.isEmpty(addProductVariationsRequest.getSizes())) {
			Set<String> sizes = new HashSet<String>();
			for (String size : addProductVariationsRequest.getSizes()) {
				sizes.add(size.toLowerCase().trim());
			}
			addProductVariationsRequest.setSizes(sizes);
		}

	}

}
