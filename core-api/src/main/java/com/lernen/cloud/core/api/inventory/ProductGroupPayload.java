package com.lernen.cloud.core.api.inventory;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class ProductGroupPayload {

	private ProductGroupBasicInfo productGroupBasicInfo;

	private List<ProductGroupItem> productGroupItems;

	// Dummy Constructor
	public ProductGroupPayload() {
	}

	public ProductGroupPayload(ProductGroupBasicInfo productGroupBasicInfo, List<ProductGroupItem> productGroupItems) {
		this.productGroupBasicInfo = productGroupBasicInfo;
		this.productGroupItems = productGroupItems;
	}

	public ProductGroupBasicInfo getProductGroupBasicInfo() {
		return productGroupBasicInfo;
	}

	public void setProductGroupBasicInfo(ProductGroupBasicInfo productGroupBasicInfo) {
		this.productGroupBasicInfo = productGroupBasicInfo;
	}

	public List<ProductGroupItem> getProductGroupItems() {
		return productGroupItems;
	}

	public void setProductGroupItems(List<ProductGroupItem> productGroupItems) {
		this.productGroupItems = productGroupItems;
	}

	@Override
	public String toString() {
		return "ProductGroupPayload [productGroupBasicInfo=" + productGroupBasicInfo + ", productGroupItems="
				+ productGroupItems + "]";
	}

}
