package com.lernen.cloud.core.api.inventory;

/**
 * 
 * <AUTHOR>
 *
 */
public class TimeValue implements Comparable<TimeValue> {

	private final int time;

	private final Object value;

	public TimeValue(int time, Object value) {
		this.time = time;
		this.value = value;
	}

	public int getTime() {
		return time;
	}

	public Object getValue() {
		return value;
	}

	public int compareTo(TimeValue timeValue) {
		if (this.getTime() < timeValue.getTime()) {
			return 1;
		} else if (this.getTime() > timeValue.getTime()) {
			return -1;
		}
		return 0;
	}

}
