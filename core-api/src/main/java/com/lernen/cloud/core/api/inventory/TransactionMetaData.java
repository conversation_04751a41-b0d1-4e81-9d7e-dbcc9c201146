package com.lernen.cloud.core.api.inventory;

import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.common.TransactionMode;

/**
 * 
 * <AUTHOR>
 *
 */
public class TransactionMetaData {

	private final UUID transactionId;

	private final String reference;

	private final String email;

	private final int instituteId;

	private final InventoryUserType inventoryUserType;

	private final InventoryTransactionType transactionType;

	private final String transactionTo;

	private final String transactionBy;

	private final long transactionDate;

	private final long transactionAddedAt;

	private final PaymentStatus paymentStatus;

	private final TransactionMode transactionMode;

	private final InventoryTransactionStatus inventoryTransactionStatus;

	private final String description;

	private final Map<String, Object> metadata;

	public TransactionMetaData(UUID transactionId, String reference, String email, int instituteId,
							   InventoryUserType inventoryUserType, InventoryTransactionType transactionType, String transactionTo, String transactionBy, long transactionDate,
			long transactionAddedAt, PaymentStatus paymentStatus, TransactionMode transactionMode,
			InventoryTransactionStatus inventoryTransactionStatus, String description, Map<String, Object> metadata) {
		this.transactionId = transactionId;
		this.reference = reference;
		this.email = email;
		this.instituteId = instituteId;
		this.inventoryUserType = inventoryUserType;
		this.transactionType = transactionType;
		this.transactionTo = transactionTo;
		this.transactionBy = transactionBy;
		this.transactionDate = transactionDate;
		this.transactionAddedAt = transactionAddedAt;
		this.paymentStatus = paymentStatus;
		this.transactionMode = transactionMode;
		this.inventoryTransactionStatus = inventoryTransactionStatus;
		this.description = description;
		this.metadata = metadata;
	}

	public UUID getTransactionId() {
		return transactionId;
	}

	public String getReference() {
		return reference;
	}

	public String getEmail() {
		return email;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public InventoryUserType getInventoryUserType() {
		return inventoryUserType;
	}

	public InventoryTransactionType getTransactionType() {
		return transactionType;
	}

	public String getTransactionTo() {
		return transactionTo;
	}

	public String getTransactionBy() {
		return transactionBy;
	}

	public long getTransactionDate() {
		return transactionDate;
	}

	public long getTransactionAddedAt() {
		return transactionAddedAt;
	}

	public PaymentStatus getPaymentStatus() {
		return paymentStatus;
	}

	public String getDescription() {
		return description;
	}

	public TransactionMode getTransactionMode() {
		return transactionMode;
	}

	public String getTransactionModeDisplayName() {
		return transactionMode.getDisplayName();
	}

	public InventoryTransactionStatus getInventoryTransactionStatus() {
		return inventoryTransactionStatus;
	}

	public Map<String, Object> getMetadata() {
		return metadata;
	}

	@Override
	public String toString() {
		return "TransactionMetaData{" +
				"transactionId=" + transactionId +
				", reference='" + reference + '\'' +
				", email='" + email + '\'' +
				", instituteId=" + instituteId +
				", inventoryUserType=" + inventoryUserType +
				", transactionType=" + transactionType +
				", transactionTo='" + transactionTo + '\'' +
				", transactionBy='" + transactionBy + '\'' +
				", transactionDate=" + transactionDate +
				", transactionAddedAt=" + transactionAddedAt +
				", paymentStatus=" + paymentStatus +
				", transactionMode=" + transactionMode +
				", inventoryTransactionStatus=" + inventoryTransactionStatus +
				", description='" + description + '\'' +
				", metadata=" + metadata +
				'}';
	}

}
