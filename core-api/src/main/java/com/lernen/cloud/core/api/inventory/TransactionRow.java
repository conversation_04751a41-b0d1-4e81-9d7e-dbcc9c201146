package com.lernen.cloud.core.api.inventory;

import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.common.TransactionMode;

/**
 * 
 * <AUTHOR>
 *
 */
public class TransactionRow {

	private final int instituteId;
	private final UUID transactionId;
	private final String reference;
	private final String email;
	private final UUID skuId;
	private final String productName;
	private final InventoryTransactionType transactionType;
	private final InventoryUserType inventoryUserType;
	private final double quantity;
	private final double totalPrice;
	private final double totalDiscount;
	private final double totalTax;
	private final double initialQuantity;
	private final double finalQuantity;
	private final String transactionTo;
	private final long transactionDate;
	private final long transactionAddedAt;
	private final String transactionBy;
	private final PaymentStatus paymentStatus;
	private final String description;
	private final double additionalCost;
	private final double additionalDiscount;
	private final TransactionMode transactionMode;
	private final InventoryTransactionStatus inventoryTransactionStatus;
	private final Double usedWalletAmount;
	private final Double walletCreditAmount;
	private final Double paidAmount;
	private final Map<String, Object> metadata;

	public TransactionRow(int instituteId, UUID transactionId, String reference,
			String email, UUID skuId, String productName,
			InventoryTransactionType transactionType,
			InventoryUserType inventoryUserType, double quantity,
			double totalPrice, double totalDiscount, double totalTax,
			double initialQuantity, double finalQuantity, String transactionTo,
			long transactionDate, long transactionAddedAt, String transactionBy,
			PaymentStatus paymentStatus, String description,
			double additionalCost, double additionalDiscount,
			TransactionMode transactionMode,
			InventoryTransactionStatus inventoryTransactionStatus, Double usedWalletAmount,
						  Double walletCreditAmount, Double paidAmount, Map<String, Object> metadata) {
		this.instituteId = instituteId;
		this.transactionId = transactionId;
		this.reference = reference;
		this.email = email;
		this.skuId = skuId;
		this.productName = productName;
		this.transactionType = transactionType;
		this.inventoryUserType = inventoryUserType;
		this.quantity = quantity;
		this.totalPrice = totalPrice;
		this.totalDiscount = totalDiscount;
		this.totalTax = totalTax;
		this.initialQuantity = initialQuantity;
		this.finalQuantity = finalQuantity;
		this.transactionTo = transactionTo;
		this.transactionDate = transactionDate;
		this.transactionAddedAt = transactionAddedAt;
		this.transactionBy = transactionBy;
		this.paymentStatus = paymentStatus;
		this.description = description;
		this.additionalCost = additionalCost;
		this.additionalDiscount = additionalDiscount;
		this.transactionMode = transactionMode;
		this.inventoryTransactionStatus = inventoryTransactionStatus;
		this.usedWalletAmount = usedWalletAmount;
		this.walletCreditAmount = walletCreditAmount;
		this.paidAmount = paidAmount;
		this.metadata = metadata;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public UUID getTransactionId() {
		return transactionId;
	}

	public String getReference() {
		return reference;
	}

	public String getEmail() {
		return email;
	}

	public UUID getSkuId() {
		return skuId;
	}

	public String getProductName() {
		return productName;
	}

	public InventoryTransactionType getTransactionType() {
		return transactionType;
	}

	public InventoryUserType getInventoryUserType() {
		return inventoryUserType;
	}

	public double getQuantity() {
		return quantity;
	}

	public double getTotalPrice() {
		return totalPrice;
	}

	public double getTotalDiscount() {
		return totalDiscount;
	}

	public double getTotalTax() {
		return totalTax;
	}

	public double getInitialQuantity() {
		return initialQuantity;
	}

	public double getFinalQuantity() {
		return finalQuantity;
	}

	public String getTransactionTo() {
		return transactionTo;
	}

	public long getTransactionDate() {
		return transactionDate;
	}

	public long getTransactionAddedAt() {
		return transactionAddedAt;
	}

	public String getTransactionBy() {
		return transactionBy;
	}

	public PaymentStatus getPaymentStatus() {
		return paymentStatus;
	}

	public String getDescription() {
		return description;
	}

	public double getAdditionalCost() {
		return additionalCost;
	}

	public double getAdditionalDiscount() {
		return additionalDiscount;
	}

	public TransactionMode getTransactionMode() {
		return transactionMode;
	}

	public InventoryTransactionStatus getInventoryTransactionStatus() {
		return inventoryTransactionStatus;
	}

	public Double getUsedWalletAmount() {
		return usedWalletAmount;
	}

	public Double getWalletCreditAmount() {
		return walletCreditAmount;
	}

	public Double getPaidAmount() {
		return paidAmount;
	}

	public Map<String, Object> getMetadata() {
		return metadata;
	}

	@Override
	public String toString() {
		return "TransactionRow{" +
				"instituteId=" + instituteId +
				", transactionId=" + transactionId +
				", reference='" + reference + '\'' +
				", email='" + email + '\'' +
				", skuId=" + skuId +
				", productName='" + productName + '\'' +
				", transactionType=" + transactionType +
				", inventoryUserType=" + inventoryUserType +
				", quantity=" + quantity +
				", totalPrice=" + totalPrice +
				", totalDiscount=" + totalDiscount +
				", totalTax=" + totalTax +
				", initialQuantity=" + initialQuantity +
				", finalQuantity=" + finalQuantity +
				", transactionTo='" + transactionTo + '\'' +
				", transactionDate=" + transactionDate +
				", transactionAddedAt=" + transactionAddedAt +
				", transactionBy='" + transactionBy + '\'' +
				", paymentStatus=" + paymentStatus +
				", description='" + description + '\'' +
				", additionalCost=" + additionalCost +
				", additionalDiscount=" + additionalDiscount +
				", transactionMode=" + transactionMode +
				", inventoryTransactionStatus=" + inventoryTransactionStatus +
				", usedWalletAmount=" + usedWalletAmount +
				", walletCreditAmount=" + walletCreditAmount +
				", paidAmount=" + paidAmount +
				", metadata=" + metadata +
				'}';
	}

}
