package com.lernen.cloud.core.api.inventory;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.student.StudentLite;

/**
 * 
 * <AUTHOR>
 *
 */
public class TransactionSummary {

	public static final String ORDER_RETURNED = "order_returned";
	public static final String RETURN_ORDER_TRANSACTION_ID = "return_order_transaction_id";

	private final UUID transactionId;

	private final int instituteId;

	private final InventoryTransactionType transactionType;

	private final InventoryUserType inventoryUserType;

	private final UUID transactionUUIDBy;
	
	private final String transactionBy;

	private final String buyerName;

	private final String email;

	private final String reference;

	private final Vendor vendor;

	private final StudentLite studentLite;

	private final long transactionDate;

	private final long transactionAddedAt;

	private final PaymentStatus paymentStatus;

	private final String description;

	private final double additionalCost;

	private final double additionalDiscount;

	private final TransactionMode transactionMode;

	private final InventoryTransactionStatus inventoryTransactionStatus;

	private final Double usedWalletAmount;

	private final Double walletCreditAmount;

	private final Double paidAmount;

	private final Map<String, Object> metadata;

	private final List<PurchasedProductSummary> purchasedProducts;

	public TransactionSummary(UUID transactionId, int instituteId,
			InventoryTransactionType transactionType,
			InventoryUserType inventoryUserType, UUID transactionUUIDBy, String transactionBy,
			String buyerName, String email, String reference, Vendor vendor,
			StudentLite studentLite, long transactionDate,
			long transactionAddedAt, PaymentStatus paymentStatus,
			String description, double additionalCost,
			double additionalDiscount, TransactionMode transactionMode,
			InventoryTransactionStatus inventoryTransactionStatus, Double usedWalletAmount,
							  Double walletCreditAmount, Double paidAmount,   Map<String, Object> metadata,
			List<PurchasedProductSummary> purchasedProducts) {
		this.transactionId = transactionId;
		this.instituteId = instituteId;
		this.transactionType = transactionType;
		this.inventoryUserType = inventoryUserType;
		this.transactionUUIDBy = transactionUUIDBy;
		this.transactionBy = transactionBy;
		this.buyerName = buyerName;
		this.email = email;
		this.reference = reference;
		this.vendor = vendor;
		this.studentLite = studentLite;
		this.transactionDate = transactionDate;
		this.transactionAddedAt = transactionAddedAt;
		this.paymentStatus = paymentStatus;
		this.description = description;
		this.additionalCost = additionalCost;
		this.additionalDiscount = additionalDiscount;
		this.transactionMode = transactionMode;
		this.inventoryTransactionStatus = inventoryTransactionStatus;
		this.usedWalletAmount = usedWalletAmount;
		this.walletCreditAmount = walletCreditAmount;
		this.paidAmount = paidAmount;
		this.metadata = metadata;
		this.purchasedProducts = purchasedProducts;
	}

	public UUID getTransactionId() {
		return transactionId;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public InventoryTransactionType getTransactionType() {
		return transactionType;
	}

	public InventoryUserType getInventoryUserType() {
		return inventoryUserType;
	}

	public String getTransactionBy() {
		return transactionBy;
	}

	/**
	 * @return the transactionUUIDBy
	 */
	public UUID getTransactionUUIDBy() {
		return transactionUUIDBy;
	}

	public String getBuyerName() {
		return buyerName;
	}

	public String getEmail() {
		return email;
	}

	public String getReference() {
		return reference;
	}

	public Vendor getVendor() {
		return vendor;
	}

	public StudentLite getStudentLite() {
		return studentLite;
	}

	public long getTransactionDate() {
		return transactionDate;
	}

	public long getTransactionAddedAt() {
		return transactionAddedAt;
	}

	public PaymentStatus getPaymentStatus() {
		return paymentStatus;
	}

	public String getDescription() {
		return description;
	}

	public double getAdditionalCost() {
		return additionalCost;
	}

	public Map<String, Object> getMetadata() {
		return metadata;
	}

	public List<PurchasedProductSummary> getPurchasedProducts() {
		return purchasedProducts;
	}

	public double getAdditionalDiscount() {
		return additionalDiscount;
	}

	@Override
	public String toString() {
		return "TransactionSummary{" +
				"transactionId=" + transactionId +
				", instituteId=" + instituteId +
				", transactionType=" + transactionType +
				", inventoryUserType=" + inventoryUserType +
				", transactionBy='" + transactionBy + '\'' +
				", buyerName='" + buyerName + '\'' +
				", email='" + email + '\'' +
				", reference='" + reference + '\'' +
				", vendor=" + vendor +
				", studentLite=" + studentLite +
				", transactionDate=" + transactionDate +
				", transactionAddedAt=" + transactionAddedAt +
				", paymentStatus=" + paymentStatus +
				", description='" + description + '\'' +
				", additionalCost=" + additionalCost +
				", additionalDiscount=" + additionalDiscount +
				", transactionMode=" + transactionMode +
				", inventoryTransactionStatus=" + inventoryTransactionStatus +
				", usedWalletAmount=" + usedWalletAmount +
				", walletCreditAmount=" + walletCreditAmount +
				", paidAmount=" + paidAmount +
				", metadata=" + metadata +
				", purchasedProducts=" + purchasedProducts +
				'}';
	}

	public TransactionMode getTransactionMode() {
		return transactionMode;
	}
	public String getTransactionModeDisplayName() {
		return transactionMode.getDisplayName();
	}

	public InventoryTransactionStatus getInventoryTransactionStatus() {
		return inventoryTransactionStatus;
	}

	public Double getUsedWalletAmount() {
		return usedWalletAmount;
	}

	public Double getWalletCreditAmount() {
		return walletCreditAmount;
	}

	public Double getPaidAmount() {
		return paidAmount;
	}

	public void format() {
		if (CollectionUtils.isEmpty(purchasedProducts)) {
			return;
		}
		for (PurchasedProductSummary purchasedProduct : purchasedProducts) {
			purchasedProduct.formatSummary();
		}
	}

}
