package com.lernen.cloud.core.api.inventory;

import java.util.Map;
import java.util.TreeMap;

/**
 * 
 * <AUTHOR>
 *
 */
public class TransactionsStatistics {

	private double currentDaySaleAmount;

	private double currentDayPurchaseAmount;

	private int currentDayQuantitySold;

	private int currentDayQuantityPurchased;

	private Map<Integer, Double> perDaySaleAmount;

	private Map<Integer, Double> perDayPurchaseAmount;

	private Map<Integer, Integer> perDaySaleQuanitity;

	private Map<Integer, Integer> perDayPurchaseQuanitity;

	public TransactionsStatistics() {
		perDaySaleAmount = new TreeMap<Integer, Double>();
		perDayPurchaseAmount = new TreeMap<Integer, Double>();
		perDaySaleQuanitity = new TreeMap<Integer, Integer>();
		perDayPurchaseQuanitity = new TreeMap<Integer, Integer>();
	}

	public double getCurrentDaySaleAmount() {
		return currentDaySaleAmount;
	}

	public void setCurrentDaySaleAmount(double currentDaySaleAmount) {
		this.currentDaySaleAmount = currentDaySaleAmount;
	}

	public double getCurrentDayPurchaseAmount() {
		return currentDayPurchaseAmount;
	}

	public void setCurrentDayPurchaseAmount(double currentDayPurchaseAmount) {
		this.currentDayPurchaseAmount = currentDayPurchaseAmount;
	}

	public int getCurrentDayQuantitySold() {
		return currentDayQuantitySold;
	}

	public void setCurrentDayQuantitySold(int currentDayQuantitySold) {
		this.currentDayQuantitySold = currentDayQuantitySold;
	}

	public Map<Integer, Double> getPerDaySaleAmount() {
		return perDaySaleAmount;
	}

	public void setPerDaySaleAmount(Map<Integer, Double> perDaySaleAmount) {
		this.perDaySaleAmount = perDaySaleAmount;
	}

	public Map<Integer, Double> getPerDayPurchaseAmount() {
		return perDayPurchaseAmount;
	}

	public void setPerDayPurchaseAmount(Map<Integer, Double> perDayPurchaseAmount) {
		this.perDayPurchaseAmount = perDayPurchaseAmount;
	}

	public Map<Integer, Integer> getPerDaySaleQuanitity() {
		return perDaySaleQuanitity;
	}

	public void setPerDaySaleQuanitity(Map<Integer, Integer> perDaySaleQuanitity) {
		this.perDaySaleQuanitity = perDaySaleQuanitity;
	}

	public Map<Integer, Integer> getPerDayPurchaseQuanitity() {
		return perDayPurchaseQuanitity;
	}

	public void setPerDayPurchaseQuanitity(Map<Integer, Integer> perDayPurchaseQuanitity) {
		this.perDayPurchaseQuanitity = perDayPurchaseQuanitity;
	}

	public int getCurrentDayQuantityPurchased() {
		return currentDayQuantityPurchased;
	}

	public void setCurrentDayQuantityPurchased(int currentDayQuantityPurchased) {
		this.currentDayQuantityPurchased = currentDayQuantityPurchased;
	}

}
