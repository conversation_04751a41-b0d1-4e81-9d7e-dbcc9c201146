package com.lernen.cloud.core.api.inventory;

import java.util.UUID;

import org.apache.commons.lang3.StringUtils;

import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;

/**
 * 
 * <AUTHOR>
 *
 */
public class UpdateProductRequest {

	private int instituteId;

	private UUID skuId;

	private String generalName;

	private double sellingPrice;

	private Double discount;

	private Double initialQuantity;

	private Integer initialQuantityAsOf;

	private String description;

	public UpdateProductRequest() {
	}

	public UpdateProductRequest(int instituteId, UUID skuId, String generalName, double sellingPrice, Double discount,
			Double initialQuantity, Integer initialQuantityAsOf, String description) {
		this.instituteId = instituteId;
		this.skuId = skuId;
		this.generalName = generalName;
		this.sellingPrice = sellingPrice;
		this.discount = discount;
		this.initialQuantity = initialQuantity;
		this.initialQuantityAsOf = initialQuantityAsOf;
		this.description = description;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getSkuId() {
		return skuId;
	}

	public void setSkuId(UUID skuId) {
		this.skuId = skuId;
	}

	public String getGeneralName() {
		return generalName;
	}

	public void setGeneralName(String generalName) {
		this.generalName = generalName;
	}

	public double getSellingPrice() {
		return sellingPrice;
	}

	public void setSellingPrice(double sellingPrice) {
		this.sellingPrice = sellingPrice;
	}

	public Double getDiscount() {
		return discount;
	}

	public void setDiscount(Double discount) {
		this.discount = discount;
	}

	public Double getInitialQuantity() {
		return initialQuantity;
	}

	public void setInitialQuantity(Double initialQuantity) {
		this.initialQuantity = initialQuantity;
	}

	public Integer getInitialQuantityAsOf() {
		return initialQuantityAsOf;
	}

	public void setInitialQuantityAsOf(Integer initialQuantityAsOf) {
		this.initialQuantityAsOf = initialQuantityAsOf;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public static boolean validate(UpdateProductRequest updateProductRequest) {
		if (updateProductRequest == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_UPDATION, "No data provided"));
		}
		if (StringUtils.isBlank(updateProductRequest.getGeneralName())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_UPDATION, "Invalid product general name"));
		}
		if (updateProductRequest.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_UPDATION, "Invalid institute"));
		}

		if (updateProductRequest.getSkuId() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_UPDATION, "Invalid sku id"));
		}

		if (updateProductRequest.getSellingPrice() < 0d) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_UPDATION,
					"Selling price cannot be negative"));
		}

		if (updateProductRequest.getDiscount() != null
				&& (updateProductRequest.getDiscount() < 0d || updateProductRequest.getDiscount() > 100d)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_UPDATION, "Discount % should be withing 0-100"));
		}

		if (updateProductRequest.getInitialQuantity() != null && updateProductRequest.getInitialQuantity() < 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_UPDATION,
					"Available quanitity cannot be negative"));
		}

		if (updateProductRequest.getInitialQuantity() != null && updateProductRequest.getInitialQuantity() >= 0
				&& (updateProductRequest.getInitialQuantityAsOf() == null
						|| updateProductRequest.getInitialQuantityAsOf() <= 0)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_UPDATION,
					"Invalid date for initial quantity"));
		}

		if ((updateProductRequest.getInitialQuantity() == null || updateProductRequest.getInitialQuantity() < 0)
				&& (updateProductRequest.getInitialQuantityAsOf() != null
						&& updateProductRequest.getInitialQuantityAsOf() > 0)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_UPDATION,
					"Invalid initial quantity for given date"));
		}

		return true;
	}

	@Override
	public String toString() {
		return "UpdateProductRequest [instituteId=" + instituteId + ", skuId=" + skuId + ", generalName=" + generalName
				+ ", sellingPrice=" + sellingPrice + ", discount=" + discount + ", initialQuantity=" + initialQuantity
				+ ", initialQuantityAsOf=" + initialQuantityAsOf + ", description=" + description + "]";
	}

}
