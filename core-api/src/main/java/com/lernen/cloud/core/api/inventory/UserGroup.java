package com.lernen.cloud.core.api.inventory;

import org.apache.commons.lang3.StringUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public enum UserGroup {
		
	NURSERY("Nursery", "NRSY"),
	LKG("L.K.G", "LKG"),
	UKG("U.K.G", "UKG"),
	CLASS_I ("Class I","CI"),
	CLASS_II ("Class II","CII"),
	CLASS_III("Class III","CIII"),
	CLASS_IV("Class IV","CIV"),
	CLASS_V("Class V","CV"),
	CLASS_VI("Class VI","CVI"),
	CLASS_VII("Class VII","CVII"),
	CLASS_VIII("Class VIII","CVIII"),
	CLASS_IX("Class IX","CIX"),
	CLASS_X("Class X","CX"),
	CLASS_XI("Class XI","CXI"),
	CLASS_XII("Class XII","CXII"),
	KIDS("Kids","KIDS"),
	ADULTS("Adults","ADLT");
	
	
	private String displayName;
	
	/**
	 * Must be unique
	 */
	private String skuId;

	private UserGroup(String displayName,String skuId) {
		this.displayName = displayName;
		this.skuId = skuId;
	}

	public String getDisplayName() {
		return displayName;
	}
	
	
	public String getSkuId() {
		return skuId;
	}

	public static UserGroup getUserGroup(String userGroup){
		if(StringUtils.isBlank(userGroup)){
			return null;
		}
		for(UserGroup userGroupEnum : UserGroup.values()){
			if(userGroupEnum.name().equalsIgnoreCase(userGroup)){
				return userGroupEnum;
			}
		}
		return null;
	}
	
}
