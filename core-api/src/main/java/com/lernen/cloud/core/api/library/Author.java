package com.lernen.cloud.core.api.library;

import java.util.UUID;

public class Author {

    private UUID authorId;
    private String authorName;
    private String nationality;
    private Integer dateOfBirth;
    private String associatedGeners;
    private String shortBiography;
    
    public Author(){}

    public Author(UUID authorId, String authorName, String nationality, Integer dateOfBirth, String associatedGeners,
            String shortBiography) {
        this.authorId = authorId;
        this.authorName = authorName;
        this.nationality = nationality;
        this.dateOfBirth = dateOfBirth;
        this.associatedGeners = associatedGeners;
        this.shortBiography = shortBiography;
    }

    public UUID getAuthorId() {
        return authorId;
    }

    public void setAuthorId(UUID authorId) {
        this.authorId = authorId;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public Integer getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(Integer dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getAssociatedGeners() {
        return associatedGeners;
    }

    public void setAssociatedGeners(String associatedGeners) {
        this.associatedGeners = associatedGeners;
    }

    public String getShortBiography() {
        return shortBiography;
    }

    public void setShortBiography(String shortBiography) {
        this.shortBiography = shortBiography;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    @Override
    public String toString() {
        return "Author [authorId=" + authorId + ", authorName=" + authorName
                + ", nationality=" + nationality + ", dateOfBirth=" + dateOfBirth + ", associatedGeners="
                + associatedGeners + ", shortBiography=" + shortBiography + "]";
    }
    
}
