package com.lernen.cloud.core.api.notification;

import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.user.UserType;

/**
 * 
 * <AUTHOR>
 *
 */
public class BatchNotificationEntry {

	private final UUID batchId;

	private final String batchName;

	private final Integer generatedAt;

	private final Integer deliveredAt;

	private final NotificationType notificationType;

	private final NotificationStatus notificationStatus;

	private final UserType userType;

	private final String notificationContent;

	private final Map<String, Object> metadata;

	private final Integer creditUsed;

	private final Integer refundCredits;

	public BatchNotificationEntry(UUID batchId, String batchName, Integer generatedAt, Integer deliveredAt, NotificationType notificationType, NotificationStatus notificationStatus, UserType userType, String notificationContent, Map<String, Object> metadata, Integer creditUsed, Integer refundCredits) {
		this.batchId = batchId;
		this.batchName = batchName;
		this.generatedAt = generatedAt;
		this.deliveredAt = deliveredAt;
		this.notificationType = notificationType;
		this.notificationStatus = notificationStatus;
		this.userType = userType;
		this.notificationContent = notificationContent;
		this.metadata = metadata;
		this.creditUsed = creditUsed;
		this.refundCredits = refundCredits;
	}

	public UUID getBatchId() {
		return batchId;
	}

	public String getBatchName() {
		return batchName;
	}

	public Integer getGeneratedAt() {
		return generatedAt;
	}

	public Integer getDeliveredAt() {
		return deliveredAt;
	}

	public NotificationType getNotificationType() {
		return notificationType;
	}

	public NotificationStatus getNotificationStatus() {
		return notificationStatus;
	}

	public UserType getUserType() {
		return userType;
	}

	public String getNotificationContent() {
		return notificationContent;
	}

	public Map<String, Object> getMetadata() {
		return metadata;
	}

	public Integer getCreditUsed() {
		return creditUsed;
	}

	public Integer getRefundCredits() {
		return refundCredits;
	}

	@Override
	public String toString() {
		return "BatchNotificationEntry{" +
				"batchId=" + batchId +
				", batchName='" + batchName + '\'' +
				", generatedAt=" + generatedAt +
				", deliveredAt=" + deliveredAt +
				", notificationType=" + notificationType +
				", notificationStatus=" + notificationStatus +
				", userType=" + userType +
				", notificationContent='" + notificationContent + '\'' +
				", metadata=" + metadata +
				", creditUsed=" + creditUsed +
				", refundCredits=" + refundCredits +
				'}';
	}
}
