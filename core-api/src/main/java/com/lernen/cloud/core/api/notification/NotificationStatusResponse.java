package com.lernen.cloud.core.api.notification;

/**
 *
 * <AUTHOR>
 *
 */
public class NotificationStatusResponse {

	private final NotificationStatus notificationStatus;

	private final Long statusUpdateTime;

	public NotificationStatusResponse(NotificationStatus notificationStatus,
			Long statusUpdateTime) {
		this.notificationStatus = notificationStatus;
		this.statusUpdateTime = statusUpdateTime;
	}

	public NotificationStatus getNotificationStatus() {
		return notificationStatus;
	}

	public Long getStatusUpdateTime() {
		return statusUpdateTime;
	}

	@Override
	public String toString() {
		return "NotificationStatusResponse [notificationStatus="
				+ notificationStatus + ", statusUpdateTime=" + statusUpdateTime
				+ "]";
	}

}
