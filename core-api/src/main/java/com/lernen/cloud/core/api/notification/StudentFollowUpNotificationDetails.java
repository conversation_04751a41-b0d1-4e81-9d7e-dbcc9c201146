package com.lernen.cloud.core.api.notification;

import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.student.StudentDetailedRow;
import com.lernen.cloud.core.api.user.UserType;

import java.util.Map;
import java.util.UUID;

public class StudentFollowUpNotificationDetails {

    private static final Integer STANDARD_SMS_INCREMENTAL_CHAR_LIMIT_ONE_CREDIT = 153;
    private static final Integer SMS_UNICODE_INCREMENTAL_CHAR_LIMIT_ONE_CREDIT = 67;

    private UUID notificationId;

    private int instituteId;

    private UUID userId;

    private UserType userType;

    private Integer academicSessionId;

    private CommunicationServiceProvider communicationServiceProvider;

    private NotificationType notificationType;

    private DeliveryMode deliveryMode;

    private String deliveryDestination;

    private UUID batchId;

    private String batchName;

    private String notificationTitle;

    private String notificationContent;

    private NotificationStatus notificationStatus;

    private Integer deliveredTime;

    private Integer generatedTime;

    private Integer lastOpenedTime;

    private Integer lastClickedTime;

    private String externalUniqueId;

    private Map<String, Object> metaData;

    private Integer creditsUsed;

    private UUID creditTransactionId;

    private Integer refundCredits;

    private StudentDetailedRow studentDetailedRow;

    public StudentFollowUpNotificationDetails(){

    }

    public StudentFollowUpNotificationDetails(UUID notificationId, int instituteId, UUID userId, UserType userType, Integer academicSessionId, CommunicationServiceProvider communicationServiceProvider, NotificationType notificationType, DeliveryMode deliveryMode, String deliveryDestination, UUID batchId, String batchName, String notificationTitle, String notificationContent, NotificationStatus notificationStatus, Integer deliveredTime, Integer generatedTime, Integer lastOpenedTime, Integer lastClickedTime, String externalUniqueId, Map<String, Object> metaData, Integer creditsUsed, UUID creditTransactionId, Integer refundCredits, StudentDetailedRow studentDetailedRow) {
        this.notificationId = notificationId;
        this.instituteId = instituteId;
        this.userId = userId;
        this.userType = userType;
        this.academicSessionId = academicSessionId;
        this.communicationServiceProvider = communicationServiceProvider;
        this.notificationType = notificationType;
        this.deliveryMode = deliveryMode;
        this.deliveryDestination = deliveryDestination;
        this.batchId = batchId;
        this.batchName = batchName;
        this.notificationTitle = notificationTitle;
        this.notificationContent = notificationContent;
        this.notificationStatus = notificationStatus;
        this.deliveredTime = deliveredTime;
        this.generatedTime = generatedTime;
        this.lastOpenedTime = lastOpenedTime;
        this.lastClickedTime = lastClickedTime;
        this.externalUniqueId = externalUniqueId;
        this.metaData = metaData;
        this.creditsUsed = creditsUsed;
        this.creditTransactionId = creditTransactionId;
        this.refundCredits = refundCredits;
        this.studentDetailedRow = studentDetailedRow;
    }

    public UUID getNotificationId() {
        return notificationId;
    }

    public void setNotificationId(UUID notificationId) {
        this.notificationId = notificationId;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public UUID getUserId() {
        return userId;
    }

    public void setUserId(UUID userId) {
        this.userId = userId;
    }

    public UserType getUserType() {
        return userType;
    }

    public void setUserType(UserType userType) {
        this.userType = userType;
    }

    public Integer getAcademicSessionId() {
        return academicSessionId;
    }

    public void setAcademicSessionId(Integer academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public CommunicationServiceProvider getCommunicationServiceProvider() {
        return communicationServiceProvider;
    }

    public void setCommunicationServiceProvider(CommunicationServiceProvider communicationServiceProvider) {
        this.communicationServiceProvider = communicationServiceProvider;
    }

    public NotificationType getNotificationType() {
        return notificationType;
    }

    public void setNotificationType(NotificationType notificationType) {
        this.notificationType = notificationType;
    }

    public DeliveryMode getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(DeliveryMode deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    public String getDeliveryDestination() {
        return deliveryDestination;
    }

    public void setDeliveryDestination(String deliveryDestination) {
        this.deliveryDestination = deliveryDestination;
    }

    public UUID getBatchId() {
        return batchId;
    }

    public void setBatchId(UUID batchId) {
        this.batchId = batchId;
    }

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public String getNotificationTitle() {
        return notificationTitle;
    }

    public void setNotificationTitle(String notificationTitle) {
        this.notificationTitle = notificationTitle;
    }

    public String getNotificationContent() {
        return notificationContent;
    }

    public void setNotificationContent(String notificationContent) {
        this.notificationContent = notificationContent;
    }

    public NotificationStatus getNotificationStatus() {
        return notificationStatus;
    }

    public void setNotificationStatus(NotificationStatus notificationStatus) {
        this.notificationStatus = notificationStatus;
    }

    public Integer getDeliveredTime() {
        return deliveredTime;
    }

    public void setDeliveredTime(Integer deliveredTime) {
        this.deliveredTime = deliveredTime;
    }

    public Integer getGeneratedTime() {
        return generatedTime;
    }

    public void setGeneratedTime(Integer generatedTime) {
        this.generatedTime = generatedTime;
    }

    public Integer getLastOpenedTime() {
        return lastOpenedTime;
    }

    public void setLastOpenedTime(Integer lastOpenedTime) {
        this.lastOpenedTime = lastOpenedTime;
    }

    public Integer getLastClickedTime() {
        return lastClickedTime;
    }

    public void setLastClickedTime(Integer lastClickedTime) {
        this.lastClickedTime = lastClickedTime;
    }

    public String getExternalUniqueId() {
        return externalUniqueId;
    }

    public void setExternalUniqueId(String externalUniqueId) {
        this.externalUniqueId = externalUniqueId;
    }

    public Map<String, Object> getMetaData() {
        return metaData;
    }

    public void setMetaData(Map<String, Object> metaData) {
        this.metaData = metaData;
    }

    public Integer getCreditsUsed() {
        return creditsUsed;
    }

    public void setCreditsUsed(Integer creditsUsed) {
        this.creditsUsed = creditsUsed;
    }

    public UUID getCreditTransactionId() {
        return creditTransactionId;
    }

    public void setCreditTransactionId(UUID creditTransactionId) {
        this.creditTransactionId = creditTransactionId;
    }

    public Integer getRefundCredits() {
        return refundCredits;
    }

    public void setRefundCredits(Integer refundCredits) {
        this.refundCredits = refundCredits;
    }

    public StudentDetailedRow getStudentDetailedRow() {
        return studentDetailedRow;
    }

    public void setStudentDetailedRow(StudentDetailedRow studentDetailedRow) {
        this.studentDetailedRow = studentDetailedRow;
    }

    @Override
    public String toString() {
        return "StudentFollowUpNotificationDetails{" +
                "notificationId=" + notificationId +
                ", instituteId=" + instituteId +
                ", userId=" + userId +
                ", userType=" + userType +
                ", academicSessionId=" + academicSessionId +
                ", communicationServiceProvider=" + communicationServiceProvider +
                ", notificationType=" + notificationType +
                ", deliveryMode=" + deliveryMode +
                ", deliveryDestination='" + deliveryDestination + '\'' +
                ", batchId=" + batchId +
                ", batchName='" + batchName + '\'' +
                ", notificationTitle='" + notificationTitle + '\'' +
                ", notificationContent='" + notificationContent + '\'' +
                ", notificationStatus=" + notificationStatus +
                ", deliveredTime=" + deliveredTime +
                ", generatedTime=" + generatedTime +
                ", lastOpenedTime=" + lastOpenedTime +
                ", lastClickedTime=" + lastClickedTime +
                ", externalUniqueId='" + externalUniqueId + '\'' +
                ", metaData=" + metaData +
                ", creditsUsed=" + creditsUsed +
                ", creditTransactionId=" + creditTransactionId +
                ", refundCredits=" + refundCredits +
                ", studentDetailedRow=" + studentDetailedRow +
                '}';
    }
}
