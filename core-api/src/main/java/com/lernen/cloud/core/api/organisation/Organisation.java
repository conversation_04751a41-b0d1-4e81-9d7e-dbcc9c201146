package com.lernen.cloud.core.api.organisation;

import java.util.List;
import java.util.UUID;

import com.lernen.cloud.core.api.institute.Institute;

/**
 * 
 * <AUTHOR>
 *
 */
public class Organisation {

	private final UUID organisationId;

	private final String organisationName;

	private List<Institute> institutes;

	public Organisation(UUID organisationId, String organisationName,
			List<Institute> institutes) {
		this.organisationId = organisationId;
		this.organisationName = organisationName;
		this.institutes = institutes;
	}

	public UUID getOrganisationId() {
		return organisationId;
	}

	public String getOrganisationName() {
		return organisationName;
	}

	public List<Institute> getInstitutes() {
		return institutes;
	}

	public void setInstitute(List<Institute> institutes){
		this.institutes = institutes;
	}

	@Override
	public String toString() {
		return "OrganisationView [organisationId=" + organisationId
				+ ", organisationName=" + organisationName + ", institutes="
				+ institutes + "]";
	}

}
