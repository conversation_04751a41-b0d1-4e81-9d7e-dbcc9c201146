/**
 * 
 */
package com.lernen.cloud.core.api.permissions;

import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import com.lernen.cloud.core.api.user.Module;

/**
 * <AUTHOR>
 *
 */
public class RoleModuleDataPayload {

	private int instituteId;
	private UUID roleId;
	private String roleName;
	private List<Module> module;
	private List<AuthorisationRequiredAction> authorisationRequiredAction;
	private boolean authorised;
	private String description;
	public RoleModuleDataPayload(int instituteId, UUID roleId, String roleName, List<Module> module,
			List<AuthorisationRequiredAction> authorisationRequiredAction, boolean authorised, String description) {
		this.instituteId = instituteId;
		this.roleId = roleId;
		this.roleName = roleName;
		this.module = module;
		this.authorisationRequiredAction = authorisationRequiredAction;
		this.authorised = authorised;
		this.description = description;
	}
	public RoleModuleDataPayload() {
	}
	public int getInstituteId() {
		return instituteId;
	}
	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}
	public UUID getRoleId() {
		return roleId;
	}
	public void setRoleId(UUID roleId) {
		this.roleId = roleId;
	}
	public String getRoleName() {
		return roleName;
	}
	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}
	public List<Module> getModule() {
		return module;
	}
	public void setModule(List<Module> module) {
		this.module = module;
	}
	public List<AuthorisationRequiredAction> getAuthorisationRequiredAction() {
		return authorisationRequiredAction;
	}
	public void setAuthorisationRequiredAction(List<AuthorisationRequiredAction> authorisationRequiredAction) {
		this.authorisationRequiredAction = authorisationRequiredAction;
	}
	public boolean isAuthorised() {
		return authorised;
	}
	public void setAuthorised(boolean authorised) {
		this.authorised = authorised;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	@Override
	public String toString() {
		return "RoleModuleDataPayload [instituteId=" + instituteId + ", roleId=" + roleId + ", roleName=" + roleName
				+ ", module=" + module + ", authorisationRequiredAction=" + authorisationRequiredAction
				+ ", authorised=" + authorised + ", description=" + description + "]";
	}
}
