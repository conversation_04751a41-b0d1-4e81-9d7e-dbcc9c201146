package com.lernen.cloud.core.api.permissions;

import java.util.List;

import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.api.user.User;

/**
 * <AUTHOR>
 *
 */

public class UserLevelPermissionDetails {

	private int instituteId;
	private List<UserPermissionsDetails> userPermissions;
	

	public UserLevelPermissionDetails() {
	}

	
	public UserLevelPermissionDetails(int instituteId, List<UserPermissionsDetails> userPermissions) {
		this.instituteId = instituteId;
		this.userPermissions = userPermissions;
	}


	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}


	public List<UserPermissionsDetails> getUserPermissions() {
		return userPermissions;
	}


	public void setUserPermissions(List<UserPermissionsDetails> userPermissions) {
		this.userPermissions = userPermissions;
	}


	@Override
	public String toString() {
		return "UserLevelPermissionDetails [instituteId=" + instituteId + ", userPermissions=" + userPermissions + "]";
	}
}
