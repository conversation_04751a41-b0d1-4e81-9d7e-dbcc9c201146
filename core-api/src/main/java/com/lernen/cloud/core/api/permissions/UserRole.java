package com.lernen.cloud.core.api.permissions;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class UserRole {

	private int instituteId;

	private UUID roleId;

	private String roleName;

	private String description;

	public UserRole() {

	}

	public UserRole(int instituteId, UUID roleId, String roleName,
			String description) {
		this.instituteId = instituteId;
		this.roleId = roleId;
		this.roleName = roleName;
		this.description = description;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getRoleId() {
		return roleId;
	}

	public void setRoleId(UUID roleId) {
		this.roleId = roleId;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((roleId == null) ? 0 : roleId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UserRole other = (UserRole) obj;
		if (roleId == null) {
			if (other.roleId != null)
				return false;
		} else if (!roleId.equals(other.roleId))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "UserRole [instituteId=" + instituteId + ", roleId=" + roleId
				+ ", roleName=" + roleName + ", description=" + description
				+ "]";
	}

}
