/**
 * 
 */
package com.lernen.cloud.core.api.permissions;

import java.util.Map;

import com.lernen.cloud.core.api.user.UserType;

/**
 * <AUTHOR>
 *
 */
public class UserStats {
	
	private int totalUsers;
	
	private Map<UserType, Integer> userTypeUsersCount;

	public UserStats(int totalUsers, Map<UserType, Integer> userTypeUsersCount) {
		this.totalUsers = totalUsers;
		this.userTypeUsersCount = userTypeUsersCount;
	}

	public UserStats() {
	}

	public int getTotalUsers() {
		return totalUsers;
	}

	public void setTotalUsers(int totalUsers) {
		this.totalUsers = totalUsers;
	}

	public Map<UserType, Integer> getUserTypeUsersCount() {
		return userTypeUsersCount;
	}

	public void setUserTypeUsersCount(Map<UserType, Integer> userTypeUsersCount) {
		this.userTypeUsersCount = userTypeUsersCount;
	}

	@Override
	public String toString() {
		return "UserStats [totalUsers=" + totalUsers + ", userTypeUsersCount=" + userTypeUsersCount + "]";
	}

}
