/**
 * 
 */
package com.lernen.cloud.core.api.report;

/**
 * <AUTHOR>
 *
 */
public class CellIndexes {
	
	private final int indexXX;
	
	private final int indexXY;
	
	private final int indexYX;
	
	private final int indexYY;
	
	/**
	 * @param indexXX
	 * @param indexXY
	 * @param indexYX
	 * @param indexYY
	 */
	public CellIndexes(int indexXX, int indexXY, int indexYX, int indexYY) {
		this.indexXX = indexXX;
		this.indexXY = indexXY;
		this.indexYX = indexYX;
		this.indexYY = indexYY;
	}
	/**
	 * @return the indexXX
	 */
	public int getIndexXX() {
		return indexXX;
	}
	/**
	 * @return the indexXY
	 */
	public int getIndexXY() {
		return indexXY;
	}
	/**
	 * @return the indexYX
	 */
	public int getIndexYX() {
		return indexYX;
	}
	/**
	 * @return the indexYY
	 */
	public int getIndexYY() {
		return indexYY;
	}
	@Override
	public String toString() {
		return "CellIndexes [indexXX=" + indexXX + ", indexXY=" + indexXY + ", indexYX=" + indexYX + ", indexYY="
				+ indexYY + "]";
	}
}
