package com.lernen.cloud.core.api.report;

import org.apache.commons.lang3.StringUtils;

public enum ChartType{
    BAR_GRAPH, PIE_CHART, LINE_GRAPH;


    public static ChartType chartType(String chartType) {
        if (StringUtils.isBlank(chartType)) {
            return null;
        }
        for (ChartType chartTypeEnum : ChartType.values()) {
            if (chartTypeEnum.name().equalsIgnoreCase(chartType)) {
                return chartTypeEnum;
            }
        }
        return null;
    }
}
