/**
 * 
 */
package com.lernen.cloud.core.api.report;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
public enum DownloadFormat {

	EXCEL,PDF;

	public static DownloadFormat getDownloadFormat(String downloadFormat) {
		if (StringUtils.isBlank(downloadFormat)) {
			return null;
		}
		for (DownloadFormat downloadFormatEnum : DownloadFormat.values()) {
			if (downloadFormatEnum.name().equalsIgnoreCase(downloadFormat)) {
				return downloadFormatEnum;
			}
		}
		return null;
	}
}
