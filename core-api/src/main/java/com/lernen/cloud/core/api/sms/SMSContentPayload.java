package com.lernen.cloud.core.api.sms;

import com.embrate.cloud.core.api.service.communication.templates.TemplateVariableDetails;

/**
 * <AUTHOR>
 */
public class SMSContentPayload {

    private final String content;

    private final String dltTemplateId;

    public SMSContentPayload(String content, String dltTemplateId) {
        this.content = content;
        this.dltTemplateId = dltTemplateId;
    }

    public String getContent() {
        return content;
    }

    public String getDltTemplateId() {
        return dltTemplateId;
    }

    @Override
    public String toString() {
        return "SMSContentPayload{" +
                "content='" + content + '\'' +
                ", dltTemplateId='" + dltTemplateId + '\'' +
                '}';
    }
}
