package com.lernen.cloud.core.api.sms.pcexpert;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PCExpertSentMessageData {

	@JsonProperty("Number")
	private String number;

	@JsonProperty("MessageId")
	private String messageId;

	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}

	public String getMessageId() {
		return messageId;
	}

	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}

	@Override
	public String toString() {
		return "PCExpertSentMessageData [number=" + number + ", messageId="
				+ messageId + "]";
	}

}
