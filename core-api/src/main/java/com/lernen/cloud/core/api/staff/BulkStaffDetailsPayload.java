package com.lernen.cloud.core.api.staff;

/**
 * <AUTHOR>
 *
 */

import java.util.List;
import java.util.Set;

public class BulkStaffDetailsPayload {

    private int instituteId;

    private Set<StaffDetailsParameters> staffDetailsParametersList;

    private List<StaffTimingsPayload> staffTimingDetails;

    private StaffVisitingDaysPayload staffVisitingDaysPayload;

    public BulkStaffDetailsPayload(){

    }

    public BulkStaffDetailsPayload(int instituteId, Set<StaffDetailsParameters> staffDetailsParametersList, List<StaffTimingsPayload> staffTimingDetails, StaffVisitingDaysPayload staffVisitingDaysPayload) {
        this.instituteId = instituteId;
        this.staffDetailsParametersList = staffDetailsParametersList;
        this.staffTimingDetails = staffTimingDetails;
        this.staffVisitingDaysPayload = staffVisitingDaysPayload;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public Set<StaffDetailsParameters> getStaffDetailsParametersList() {
        return staffDetailsParametersList;
    }

    public void setStaffDetailsParametersList(Set<StaffDetailsParameters> staffDetailsParametersList) {
        this.staffDetailsParametersList = staffDetailsParametersList;
    }

    public List<StaffTimingsPayload> getStaffTimingDetails() {
        return staffTimingDetails;
    }

    public void setStaffTimingDetails(List<StaffTimingsPayload> staffTimingDetails) {
        this.staffTimingDetails = staffTimingDetails;
    }

    public StaffVisitingDaysPayload getStaffVisitingDaysPayload() {
        return staffVisitingDaysPayload;
    }

    public void setStaffVisitingDaysPayload(StaffVisitingDaysPayload staffVisitingDaysPayload) {
        this.staffVisitingDaysPayload = staffVisitingDaysPayload;
    }

    @Override
    public String toString() {
        return "BulkStaffDetailsPayload{" +
                "instituteId=" + instituteId +
                ", staffDetailsParametersList=" + staffDetailsParametersList +
                ", staffTimingDetails=" + staffTimingDetails +
                ", staffVisitingDaysPayload=" + staffVisitingDaysPayload +
                '}';
    }
}
