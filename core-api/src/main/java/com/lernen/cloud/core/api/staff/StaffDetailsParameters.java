package com.lernen.cloud.core.api.staff;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
public enum StaffDetailsParameters {

    TIMING_DETAILS,VISITING_HOURS;

    public static StaffDetailsParameters getStaffDetailsParameters(String status) {
        if (StringUtils.isBlank(status)) {
            return null;
        }
        for (StaffDetailsParameters staffDetailsParametersEnum : StaffDetailsParameters
                .values()) {
            if (staffDetailsParametersEnum.name().equalsIgnoreCase(status)) {
                return staffDetailsParametersEnum;
            }
        }
        return null;
    }
}
