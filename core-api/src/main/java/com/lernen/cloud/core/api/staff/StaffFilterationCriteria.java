/**
 * 
 */
package com.lernen.cloud.core.api.staff;

import java.util.List;
import java.util.Set;
import java.util.UUID;

import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.UserCategory;

/**
 * <AUTHOR> 
 *
 */
public class StaffFilterationCriteria {
    private List<UserCategory> userCategory;

	private List<StaffStatus> staffStatus;

	private List<Gender> gender;

    private List<String> religion;

    private List<String> state;

	private List<UUID> staffCategory;

	private Set<UUID> staffDepartment;

	private Set<UUID> staffDesignation;

    public StaffFilterationCriteria() {

	}

    public StaffFilterationCriteria( List<UserCategory> userCategory,List<Gender> gender,List<String> religion, List<String> state,List<UUID> staffCategory,Set<UUID> staffDepartment,Set<UUID> staffDesignation) {
        this.userCategory = userCategory;
		this.gender = gender;
        this.religion = religion;
        this.state = state;
		this.staffCategory = staffCategory;
		this.staffDepartment = staffDepartment;
		this.staffDesignation = staffDesignation;
	}

	public List<UserCategory> getUserCategory() {
		return this.userCategory;
	}

	public void setUserCategory(List<UserCategory> userCategory) {
		this.userCategory = userCategory;
	}

	public List<StaffStatus> getStaffStatus() {
		return this.staffStatus;
	}

	public void setStaffStatus(List<StaffStatus> staffStatus) {
		this.staffStatus = staffStatus;
	}

    public List<Gender> getGender() {
		return gender;
	}

    public void setGender(List<Gender> gender) {
		this.gender = gender;
	}

    public List<String> getReligion() {
		return religion;
	}

	public void setReligion(List<String> religion) {
		this.religion = religion;
	}

    public List<String> getState() {
		return state;
	}

	public void setState(List<String> state) {
		this.state = state;
	}
	public List<UUID> getStaffCategory() {
		return this.staffCategory;
	}

	public void setStaffCategory(List<UUID> staffCategory) {
		this.staffCategory = staffCategory;
	}

	public Set<UUID> getStaffDepartment() {
		return this.staffDepartment;
	}

	public void setStaffDepartment(Set<UUID> staffDepartment) {
		this.staffDepartment = staffDepartment;
	}

	public Set<UUID> getStaffDesignation() {
		return this.staffDesignation;
	}

	public void setStaffDesignation(Set<UUID> staffDesignation) {
		this.staffDesignation = staffDesignation;
	}

    @Override
	public String toString() {
		return "StaffFilterationCriteria{" +
				"userCategory=" + userCategory +
				", gender=" + gender +
                ", religion=" + religion + 
                ", state=" + state + 
				", staffCategory=" + staffCategory +
				", staffDepartment=" + staffDepartment +
				", staffDesignation=" + staffDesignation + 
				'}';
	}
}
