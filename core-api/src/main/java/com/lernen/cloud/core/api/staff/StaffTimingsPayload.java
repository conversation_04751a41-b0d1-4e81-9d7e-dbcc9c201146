package com.lernen.cloud.core.api.staff;

import java.util.UUID;

public class StaffTimingsPayload {

    private UUID staffId;

    private StaffTimingDetails staffTimingDetails;

    public StaffTimingsPayload(){

    }

    public StaffTimingsPayload(UUID staffId, StaffTimingDetails staffTimingDetails) {
        this.staffId = staffId;
        this.staffTimingDetails = staffTimingDetails;
    }

    public UUID getStaffId() {
        return staffId;
    }

    public void setStaffId(UUID staffId) {
        this.staffId = staffId;
    }

    public StaffTimingDetails getStaffTimingDetails() {
        return staffTimingDetails;
    }

    public void setStaffTimingDetails(StaffTimingDetails staffTimingDetails) {
        this.staffTimingDetails = staffTimingDetails;
    }

    @Override
    public String toString() {
        return "StaffTimingsPayload{" +
                "staffId=" + staffId +
                ", staffTimingDetails=" + staffTimingDetails +
                '}';
    }
}
