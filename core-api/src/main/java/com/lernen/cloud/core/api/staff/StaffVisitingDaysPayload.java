package com.lernen.cloud.core.api.staff;

import java.util.List;
import java.util.UUID;

public class StaffVisitingDaysPayload {
    private List<UUID> staffIdList;

    private List<StaffVisitingDays> staffVisitingDaysList;

    public StaffVisitingDaysPayload() {

    }

    public StaffVisitingDaysPayload(List<UUID> staffIdList, List<StaffVisitingDays> staffVisitingDays) {
        this.staffIdList = staffIdList;
        this.staffVisitingDaysList = staffVisitingDays;
    }

    public List<UUID> getStaffIdList() {
        return staffIdList;
    }

    public void setStaffIdList(List<UUID> staffIdList) {
        this.staffIdList = staffIdList;
    }

    public List<StaffVisitingDays> getStaffVisitingDaysList() {
        return staffVisitingDaysList;
    }

    public void setStaffVisitingDaysList(List<StaffVisitingDays> staffVisitingDaysList) {
        this.staffVisitingDaysList = staffVisitingDaysList;
    }

    @Override
    public String toString() {
        return "StaffVisitingDaysPayload{" +
                "staffIdList=" + staffIdList +
                ", staffVisitingDaysList=" + staffVisitingDaysList +
                '}';
    }
}
