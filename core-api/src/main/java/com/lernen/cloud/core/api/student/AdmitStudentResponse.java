package com.lernen.cloud.core.api.student;

import java.util.UUID;

/**
 *
 * <AUTHOR>
 *
 */
public class AdmitStudentResponse {

	private final UUID studentId;

	private final Integer academicSessionId;

	private final Boolean houseAssign;

	private final Boolean siblingAssign;
	private final Boolean studentUserCreated;

	private final Boolean feeStructureAssigned;

	private final Boolean optionalCoursesAssigned;

	private final Boolean discountStructureCreated;

	private final Boolean discountStructureAssigned;

	private final Boolean transportAssigned;

	private final Boolean holidayTemplateAssigned;

	private final boolean success;

	public AdmitStudentResponse(UUID studentId, Integer academicSessionId, Boolean houseAssign, Boolean siblingAssign, Boolean studentUserCreated,
								Boolean feeStructureAssigned, Boolean optionalCoursesAssigned,
								Boolean discountStructureCreated,
								Boolean discountStructureAssigned, Boolean transportAssigned, Boolean holidayTemplateAssigned, boolean success) {
		this.studentId = studentId;
		this.academicSessionId = academicSessionId;
		this.houseAssign = houseAssign;
		this.siblingAssign = siblingAssign;
		this.studentUserCreated = studentUserCreated;
		this.feeStructureAssigned = feeStructureAssigned;
		this.optionalCoursesAssigned = optionalCoursesAssigned;
		this.discountStructureCreated = discountStructureCreated;
		this.discountStructureAssigned = discountStructureAssigned;
		this.transportAssigned = transportAssigned;
		this.success = success;
		this.holidayTemplateAssigned = holidayTemplateAssigned;
	}

	//	public AdmitStudentResponse(UUID studentId, Integer academicSessionId,
//								Boolean studentUserCreated, Boolean feeStructureAssigned,
//								boolean sendCredentialSMS, boolean success) {
//		this.studentId = studentId;
//		this.academicSessionId = academicSessionId;
//		this.studentUserCreated = studentUserCreated;
//		this.feeStructureAssigned = feeStructureAssigned;
//		this.sendCredentialSMS = sendCredentialSMS;
//		this.success = success;
//	}

	public UUID getStudentId() {
		return studentId;
	}

	public Integer getAcademicSessionId() {
		return academicSessionId;
	}

	public Boolean getStudentUserCreated() {
		return studentUserCreated;
	}

	public Boolean getFeeStructureAssigned() {
		return feeStructureAssigned;
	}

	public boolean isSuccess() {
		return success;
	}

	public Boolean getOptionalCoursesAssigned() {
		return optionalCoursesAssigned;
	}

	public Boolean getDiscountStructureAssigned() {
		return discountStructureAssigned;
	}

	public Boolean getTransportAssigned() {
		return transportAssigned;
	}

	public Boolean getDiscountStructureCreated() {
		return discountStructureCreated;
	}

	public Boolean getHouseAssign() {
		return houseAssign;
	}

	public Boolean getSiblingAssign() {
		return siblingAssign;
	}

	public Boolean getHolidayTemplateAssigned() {
		return holidayTemplateAssigned;
	}

	@Override
	public String toString() {
		return "AdmitStudentResponse{" +
				"studentId=" + studentId +
				", academicSessionId=" + academicSessionId +
				", studentUserCreated=" + studentUserCreated +
				", feeStructureAssigned=" + feeStructureAssigned +
				", optionalCoursesAssigned=" + optionalCoursesAssigned +
				", discountStructureCreated=" + discountStructureCreated +
				", discountStructureAssigned=" + discountStructureAssigned +
				", transportAssigned=" + transportAssigned +
				", holidayTemplateAssigned=" + holidayTemplateAssigned +
				", success=" + success +
				'}';
	}

	

}
