package com.lernen.cloud.core.api.student;

import java.util.List;
import java.util.Set;

public class BulkStudentPayload {

    private int instituteId;

    private int academicSessionId;

    private Set<StudentParameters> studentParametersSet;

    private List<BulkStudentInfo> bulkStudentInfoList;

    public BulkStudentPayload() {
    }

    public BulkStudentPayload(int instituteId, int academicSessionId, Set<StudentParameters> studentParametersSet, List<BulkStudentInfo> bulkStudentInfoList) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.studentParametersSet = studentParametersSet;
        this.bulkStudentInfoList = bulkStudentInfoList;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public void setAcademicSessionId(int academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public Set<StudentParameters> getStudentParametersSet() {
        return studentParametersSet;
    }

    public void setStudentParametersSet(Set<StudentParameters> studentParametersSet) {
        this.studentParametersSet = studentParametersSet;
    }

    public List<BulkStudentInfo> getBulkStudentInfoList() {
        return bulkStudentInfoList;
    }

    public void setBulkStudentInfoList(List<BulkStudentInfo> bulkStudentInfoList) {
        this.bulkStudentInfoList = bulkStudentInfoList;
    }

    @Override
    public String toString() {
        return "BulkStudentPayload{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", studentParametersSet=" + studentParametersSet +
                ", bulkStudentInfoList=" + bulkStudentInfoList +
                '}';
    }
}
