package com.lernen.cloud.core.api.student;

import java.util.List;
import java.util.UUID;

import com.embrate.cloud.core.api.student.registration.StudentRegistrationPaymentData;
import com.embrate.cloud.core.api.student.registration.StudentRegistrationPaymentStatus;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.user.Document;

/**
 * 
 * <AUTHOR>
 *
 */
public class RegistrationStudent {

	private final UUID studentRegistrationId;

	private final AcademicSession academicSession;

	private final Standard standard;

	private final StudentBasicInfo studentBasicInfo;

	private final StudentFamilyInfo studentFamilyInfo;

	private final List<StudentGuardianInfo> studentGuardianInfoList;

	private final StudentPreviousSchoolInfo studentPreviousSchoolInfo;

	private final StudentMedicalInfo studentMedicalInfo;

	private final StudentRegistrationStatus studentRegistrationStatus;

	private final List<Document<StudentDocumentType>> studentDocuments;

	private final Document<StudentDocumentType> studentImage;

	private final StudentRegistrationPaymentStatus paymentStatus;
	private final StudentRegistrationPaymentData paymentData;

	public RegistrationStudent(UUID studentRegistrationId,
			AcademicSession academicSession, Standard standard,
			StudentBasicInfo studentBasicInfo,
			StudentFamilyInfo studentFamilyInfo,
			List<StudentGuardianInfo> studentGuardianInfoList,
			StudentPreviousSchoolInfo studentPreviousSchoolInfo,
			StudentMedicalInfo studentMedicalInfo,
			StudentRegistrationStatus studentRegistrationStatus,
			List<Document<StudentDocumentType>> studentDocuments,
							   StudentRegistrationPaymentStatus paymentStatus,
							   StudentRegistrationPaymentData paymentData) {
		this.studentRegistrationId = studentRegistrationId;
		this.academicSession = academicSession;
		this.standard = standard;
		this.studentBasicInfo = studentBasicInfo;
		this.studentFamilyInfo = studentFamilyInfo;
		this.studentGuardianInfoList = studentGuardianInfoList;
		this.studentPreviousSchoolInfo = studentPreviousSchoolInfo;
		this.studentMedicalInfo = studentMedicalInfo;
		this.studentRegistrationStatus = studentRegistrationStatus;
		this.studentDocuments = studentDocuments;
		this.studentImage = getImageDocument();
		this.paymentStatus = paymentStatus;
		this.paymentData = paymentData;
	}

	public UUID getStudentRegistrationId() {
		return studentRegistrationId;
	}

	public AcademicSession getAcademicSession() {
		return academicSession;
	}

	public Standard getStandard() {
		return standard;
	}

	public StudentBasicInfo getStudentBasicInfo() {
		return studentBasicInfo;
	}

	public StudentFamilyInfo getStudentFamilyInfo() {
		return studentFamilyInfo;
	}

	public List<StudentGuardianInfo> getStudentGuardianInfoList() {
		return studentGuardianInfoList;
	}

	public StudentPreviousSchoolInfo getStudentPreviousSchoolInfo() {
		return studentPreviousSchoolInfo;
	}

	public StudentMedicalInfo getStudentMedicalInfo() {
		return studentMedicalInfo;
	}

	public StudentRegistrationStatus getStudentRegistrationStatus() {
		return studentRegistrationStatus;
	}

	public List<Document<StudentDocumentType>> getStudentDocuments() {
		return studentDocuments;
	}

	public Document<StudentDocumentType> getStudentImage() {
		return studentImage;
	}

	public StudentRegistrationPaymentStatus getPaymentStatus() {
		return paymentStatus;
	}

	public String getPaymentStatusDisplayName() {
		return paymentStatus == null ? null : paymentStatus.getDisplayName();
	}

	public StudentRegistrationPaymentData getPaymentData() {
		return paymentData;
	}

	private Document<StudentDocumentType> getImageDocument() {
		if (CollectionUtils.isEmpty(studentDocuments)) {
			return null;
		}
		for (Document<StudentDocumentType> studentDocument : studentDocuments) {
			if (studentDocument
					.getDocumentType() == StudentDocumentType.STUDENT_PROFILE_IMAGE) {
				return studentDocument;
			}
		}
		return null;
	}

	@Override
	public String toString() {
		return "RegistrationStudent{" +
				"studentRegistrationId=" + studentRegistrationId +
				", academicSession=" + academicSession +
				", standard=" + standard +
				", studentBasicInfo=" + studentBasicInfo +
				", studentFamilyInfo=" + studentFamilyInfo +
				", studentGuardianInfoList=" + studentGuardianInfoList +
				", studentPreviousSchoolInfo=" + studentPreviousSchoolInfo +
				", studentMedicalInfo=" + studentMedicalInfo +
				", studentRegistrationStatus=" + studentRegistrationStatus +
				", studentDocuments=" + studentDocuments +
				", studentImage=" + studentImage +
				", paymentStatus=" + paymentStatus +
				", paymentData=" + paymentData +
				'}';
	}

}
