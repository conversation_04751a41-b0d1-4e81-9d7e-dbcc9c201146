/**
 * 
 */
package com.lernen.cloud.core.api.student;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
public enum StudentAcademisSessionParameters {
	
	SECTION, ROLL_NUMBER, HEIGHT, WEIGHT, BOARD_REGISTRATION_NUMBER, MEDIUM, IS_NEW_ADMISSION;
	
	public static StudentAcademisSessionParameters getStudentAcademisSessionParameters(String status) {
		if (StringUtils.isBlank(status)) {
			return null;
		}
		for (StudentAcademisSessionParameters studentAcademisSessionParametersEnum : StudentAcademisSessionParameters
				.values()) {
			if (studentAcademisSessionParametersEnum.name().equalsIgnoreCase(status)) {
				return studentAcademisSessionParametersEnum;
			}
		}
		return null;
	}

}
