package com.lernen.cloud.core.api.student;

public class StudentFamilyInfo {

	private String mothersName;

	private String fathersName;

	private String mothersQualification;

	private String fathersQualification;

	private String mothersContactNumber;

	private String fathersContactNumber;

	private String mothersOccupation;

	private String mothersAnnualIncome;

	private String fathersOccupation;

	private String fathersAnnualIncome;
	
	private String mothersAadharNumber;
	
	private String fathersAadharNumber;

	private String mothersPanCardDetails;

	private String fathersPanCardDetails;

	private String approxFamilyIncome;

	public StudentFamilyInfo() {
	}

	public StudentFamilyInfo(String mothersName, String fathersName, String mothersQualification, String fathersQualification,
							 String mothersContactNumber, String fathersContactNumber, String mothersOccupation, String mothersAnnualIncome,
							 String fathersOccupation, String fathersAnnualIncome, String mothersAadharNumber, String fathersAadharNumber,
							 String mothersPanCardDetails,String fathersPanCardDetails,String approxFamilyIncome) {
		this.mothersName = mothersName;
		this.fathersName = fathersName;
		this.mothersQualification = mothersQualification;
		this.fathersQualification = fathersQualification;
		this.mothersContactNumber = mothersContactNumber;
		this.fathersContactNumber = fathersContactNumber;
		this.mothersOccupation = mothersOccupation;
		this.mothersAnnualIncome = mothersAnnualIncome;
		this.fathersOccupation = fathersOccupation;
		this.fathersAnnualIncome = fathersAnnualIncome;
		this.mothersAadharNumber = mothersAadharNumber;
		this.fathersAadharNumber = fathersAadharNumber;
		this.mothersPanCardDetails = mothersPanCardDetails;
		this.fathersPanCardDetails = fathersPanCardDetails;
		this.approxFamilyIncome = approxFamilyIncome;
	}

	public String getMothersName() {
		return mothersName;
	}

	public void setMothersName(String mothersName) {
		this.mothersName = mothersName;
	}

	public String getFathersName() {
		return fathersName;
	}

	public void setFathersName(String fathersName) {
		this.fathersName = fathersName;
	}

	public String getMothersContactNumber() {
		return mothersContactNumber;
	}

	public void setMothersContactNumber(String mothersContactNumber) {
		this.mothersContactNumber = mothersContactNumber;
	}

	public String getFathersContactNumber() {
		return fathersContactNumber;
	}

	public void setFathersContactNumber(String fathersContactNumber) {
		this.fathersContactNumber = fathersContactNumber;
	}

	public String getMothersOccupation() {
		return mothersOccupation;
	}

	public void setMothersOccupation(String mothersOccupation) {
		this.mothersOccupation = mothersOccupation;
	}

	public String getMothersAnnualIncome() {
		return mothersAnnualIncome;
	}

	public void setMothersAnnualIncome(String mothersAnnualIncome) {
		this.mothersAnnualIncome = mothersAnnualIncome;
	}

	public String getFathersOccupation() {
		return fathersOccupation;
	}

	public void setFathersOccupation(String fathersOccupation) {
		this.fathersOccupation = fathersOccupation;
	}

	public String getFathersAnnualIncome() {
		return fathersAnnualIncome;
	}

	public void setFathersAnnualIncome(String fathersAnnualIncome) {
		this.fathersAnnualIncome = fathersAnnualIncome;
	}

	public String getMothersAadharNumber() {
		return mothersAadharNumber;
	}

	public void setMothersAadharNumber(String mothersAadharNumber) {
		this.mothersAadharNumber = mothersAadharNumber;
	}

	public String getFathersAadharNumber() {
		return fathersAadharNumber;
	}

	public void setFathersAadharNumber(String fathersAadharNumber) {
		this.fathersAadharNumber = fathersAadharNumber;
	}

	public String getApproxFamilyIncome() {
		return approxFamilyIncome;
	}

	public void setApproxFamilyIncome(String approxFamilyIncome) {
		this.approxFamilyIncome = approxFamilyIncome;
	}

	public String getMothersQualification() {
		return mothersQualification;
	}

	public void setMothersQualification(String mothersQualification) {
		this.mothersQualification = mothersQualification;
	}

	public String getFathersQualification() {
		return fathersQualification;
	}

	public void setFathersQualification(String fathersQualification) {
		this.fathersQualification = fathersQualification;
	}

	public String getMothersPanCardDetails() { return  mothersPanCardDetails; }

	public void setMothersPanCardDetails(String mothersPanCardDetails) { this.mothersPanCardDetails = mothersPanCardDetails; }

	public String getFathersPanCardDetails() { return fathersPanCardDetails; }

	public void setFathersPanCardDetails(String fathersPanCardDetails) { this.fathersPanCardDetails = fathersPanCardDetails; }

	@Override
	public String toString() {
		return "StudentFamilyInfo{" +
				"mothersName='" + mothersName + '\'' +
				", fathersName='" + fathersName + '\'' +
				", mothersQualification='" + mothersQualification + '\'' +
				", fathersQualification='" + fathersQualification + '\'' +
				", mothersContactNumber='" + mothersContactNumber + '\'' +
				", fathersContactNumber='" + fathersContactNumber + '\'' +
				", mothersOccupation='" + mothersOccupation + '\'' +
				", mothersAnnualIncome='" + mothersAnnualIncome + '\'' +
				", fathersOccupation='" + fathersOccupation + '\'' +
				", fathersAnnualIncome='" + fathersAnnualIncome + '\'' +
				", mothersAadharNumber='" + mothersAadharNumber + '\'' +
				", fathersAadharNumber='" + fathersAadharNumber + '\'' +
				", mothersPanCardDetails='" + mothersPanCardDetails + '\'' +
				", fathersPanCardDetails='" + fathersPanCardDetails + '\'' +
				", approxFamilyIncome='" + approxFamilyIncome + '\'' +
				'}';
	}
}
