package com.lernen.cloud.core.api.student;

import com.lernen.cloud.core.api.user.Gender;

import java.util.LinkedHashMap;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class StudentGenderReport {

	private final UUID standardId;

	private final String standardName;

	private final LinkedHashMap<Gender, Integer> genderCountMap;

	public StudentGenderReport(UUID standardId, String standardName, LinkedHashMap<Gender, Integer> genderCountMap) {
		this.standardId = standardId;
		this.standardName = standardName;
		this.genderCountMap = genderCountMap;
	}

	public UUID getStandardId() {
		return standardId;
	}

	public String getStandardName() {
		return standardName;
	}

	public LinkedHashMap<Gender, Integer> getGenderCountMap() {
		return genderCountMap;
	}

	@Override
	public String toString() {
		return "StudentGenderReport{" +
				"standardId=" + standardId +
				", standardName=" + standardName +
				", genderCountMap=" + genderCountMap +
				'}';
	}
}
