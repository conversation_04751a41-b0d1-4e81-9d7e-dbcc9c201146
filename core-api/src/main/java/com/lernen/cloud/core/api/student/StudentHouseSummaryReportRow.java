package com.lernen.cloud.core.api.student;

import com.lernen.cloud.core.api.user.Gender;

import java.util.UUID;

public class StudentHouseSummaryReportRow {

    private int instituteId;

    private Integer studentCount;

    private Gender gender;

    private UUID standardId;

    private String standardName;

    private String houseName;

    public StudentHouseSummaryReportRow(int instituteId, Integer studentCount, Gender gender, UUID standardId, String standardName, String houseName) {
        this.instituteId = instituteId;
        this.studentCount = studentCount;
        this.gender = gender;
        this.standardId = standardId;
        this.standardName = standardName;
        this.houseName = houseName;
    }

    public StudentHouseSummaryReportRow(){
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public Integer getStudentCount() {
        return studentCount;
    }

    public void setStudentCount(Integer studentCount) {
        this.studentCount = studentCount;
    }

    public Gender getGender() {
        return gender;
    }

    public void setGender(Gender gender) {
        this.gender = gender;
    }

    public UUID getStandardId() {
        return standardId;
    }

    public void setStandardId(UUID standardId) {
        this.standardId = standardId;
    }

    public String getStandardName() {
        return standardName;
    }

    public void setStandardName(String standardName) {
        this.standardName = standardName;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    @Override
    public String toString() {
        return "StudentHouseSummaryReportRow{" +
                "instituteId=" + instituteId +
                ", studentCount=" + studentCount +
                ", gender=" + gender +
                ", standardId=" + standardId +
                ", standardName='" + standardName + '\'' +
                ", houseName='" + houseName + '\'' +
                '}';
    }
}
