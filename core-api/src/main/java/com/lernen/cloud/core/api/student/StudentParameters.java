package com.lernen.cloud.core.api.student;

import org.apache.commons.lang3.StringUtils;

public enum StudentParameters {

    REGISTRATION_NUMBER, ADMISSION_NUMBER, PRIMARY_CONTACT_NUMBER, PRIMARY_EMAIL, <PERSON><PERSON><PERSON>ER_NAME, FATHER_NAME, FATHER_CONTACT_NUMBER, MOTHER_CONTACT_NUMBER, CASTE, CATEGORY, BLOOD_GROUP,
    PEN, AADHAR_NUMBER, ADMISSION_DATE, DATE_OF_BIRTH;

    public static StudentParameters getStudentParameters(String studentParameter) {
        if (StringUtils.isBlank(studentParameter)) {
            return null;
        }
        for (StudentParameters studentParametersEnum : StudentParameters.values()) {
            if (studentParametersEnum.name().equalsIgnoreCase(studentParameter)) {
                return studentParametersEnum;
            }
        }
        return null;
    }
}
