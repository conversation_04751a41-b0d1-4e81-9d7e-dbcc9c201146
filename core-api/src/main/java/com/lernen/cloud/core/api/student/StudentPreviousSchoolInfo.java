package com.lernen.cloud.core.api.student;

import com.lernen.cloud.core.api.common.BooleanDisplay;

public class StudentPreviousSchoolInfo {

	private boolean  isAdmissionTcBased;

	private String  tcNumber;

	private String schoolName;

	private String classPassed;

	private String medium;

	private String percentage;

	private String result;

	private Integer yearOfPassing;

	public StudentPreviousSchoolInfo() {
	}

	public StudentPreviousSchoolInfo(boolean isAdmissionTcBased, String tcNumber, String schoolName, String classPassed, String medium, String percentage, String result, Integer yearOfPassing) {
		this.isAdmissionTcBased = isAdmissionTcBased;
		this.tcNumber = tcNumber;
		this.schoolName = schoolName;
		this.classPassed = classPassed;
		this.medium = medium;
		this.percentage = percentage;
		this.result = result;
		this.yearOfPassing = yearOfPassing;
	}

	public String getSchoolName() {
		return schoolName;
	}

	public void setSchoolName(String schoolName) {
		this.schoolName = schoolName;
	}

	public String getClassPassed() {
		return classPassed;
	}

	public void setClassPassed(String classPassed) {
		this.classPassed = classPassed;
	}

	public String getMedium() {
		return medium;
	}

	public void setMedium(String medium) {
		this.medium = medium;
	}

	public String getPercentage() {
		return percentage;
	}

	public void setPercentage(String percentage) {
		this.percentage = percentage;
	}

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}

	public Integer getYearOfPassing() {
		return yearOfPassing;
	}

	public void setYearOfPassing(Integer yearOfPassing) {
		this.yearOfPassing = yearOfPassing;
	}

	public boolean isAdmissionTcBased() {
		return isAdmissionTcBased;
	}

	public void setAdmissionTcBased(boolean admissionTcBased) {
		isAdmissionTcBased = admissionTcBased;
	}

	public String getAdmissionTcBasedDisplay() {
		return BooleanDisplay.getBooleanDisplay(isAdmissionTcBased).getDisplay();
	}

	public String getTcNumber() {
		return tcNumber;
	}

	public void setTcNumber(String tcNumber) {
		this.tcNumber = tcNumber;
	}

	@Override
	public String toString() {
		return "StudentPreviousSchoolInfo{" +
				"isAdmissionTcBased=" + isAdmissionTcBased +
				", tcNumber='" + tcNumber + '\'' +
				", schoolName='" + schoolName + '\'' +
				", classPassed='" + classPassed + '\'' +
				", medium='" + medium + '\'' +
				", percentage='" + percentage + '\'' +
				", result='" + result + '\'' +
				", yearOfPassing=" + yearOfPassing +
				'}';
	}

}
