package com.lernen.cloud.core.api.student;

import com.embrate.cloud.core.api.student.registration.StudentRegistrationPaymentData;

import java.util.List;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentRegistrationPayload {

	private UUID instituteUniqueCode;

	private UUID standardId;

	private StudentBasicInfo studentBasicInfo;

	private StudentFamilyInfo studentFamilyInfo;

	private List<StudentGuardianInfo> studentGuardianInfoList;

	private StudentPreviousSchoolInfo studentPreviousSchoolInfo;

	private StudentMedicalInfo studentMedicalInfo;

	private StudentRegistrationPaymentData paymentData;

	public UUID getInstituteUniqueCode() {
		return instituteUniqueCode;
	}

	public void setInstituteUniqueCode(UUID instituteUniqueCode) {
		this.instituteUniqueCode = instituteUniqueCode;
	}

	public StudentBasicInfo getStudentBasicInfo() {
		return studentBasicInfo;
	}

	public void setStudentBasicInfo(StudentBasicInfo studentBasicInfo) {
		this.studentBasicInfo = studentBasicInfo;
	}

	public StudentFamilyInfo getStudentFamilyInfo() {
		return studentFamilyInfo;
	}

	public void setStudentFamilyInfo(StudentFamilyInfo studentFamilyInfo) {
		this.studentFamilyInfo = studentFamilyInfo;
	}

	public List<StudentGuardianInfo> getStudentGuardianInfoList() {
		return studentGuardianInfoList;
	}

	public void setStudentGuardianInfoList(
			List<StudentGuardianInfo> studentGuardianInfoList) {
		this.studentGuardianInfoList = studentGuardianInfoList;
	}

	public StudentPreviousSchoolInfo getStudentPreviousSchoolInfo() {
		return studentPreviousSchoolInfo;
	}

	public void setStudentPreviousSchoolInfo(
			StudentPreviousSchoolInfo studentPreviousSchoolInfo) {
		this.studentPreviousSchoolInfo = studentPreviousSchoolInfo;
	}

	public StudentMedicalInfo getStudentMedicalInfo() {
		return studentMedicalInfo;
	}

	public void setStudentMedicalInfo(StudentMedicalInfo studentMedicalInfo) {
		this.studentMedicalInfo = studentMedicalInfo;
	}

	public UUID getStandardId() {
		return standardId;
	}

	public void setStandardId(UUID standardId) {
		this.standardId = standardId;
	}

	public StudentRegistrationPaymentData getPaymentData() {
		return paymentData;
	}

	public void setPaymentData(StudentRegistrationPaymentData paymentData) {
		this.paymentData = paymentData;
	}
}
