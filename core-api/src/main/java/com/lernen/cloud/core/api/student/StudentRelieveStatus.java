package com.lernen.cloud.core.api.student;

import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.fees.payment.FeePaymentDetails;
import com.lernen.cloud.core.api.fees.payment.FeePaymentStatus;
import com.lernen.cloud.core.api.fees.payment.StudentFeesDetails;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentRelieveStatus {

	private final Student student;

	private final StudentFeesDetails studentFeesDetails;

	private final boolean paymentDue;

	private final double totalDueAmount;

	public StudentRelieveStatus(Student student, StudentFeesDetails studentFeesDetails) {
		this.student = student;
		this.studentFeesDetails = studentFeesDetails;
		this.totalDueAmount = computePaymentDue();
		paymentDue = totalDueAmount > 0d;
	}

	private double computePaymentDue() {
		double dueAmount = 0d;
		if (studentFeesDetails == null || CollectionUtils.isEmpty(studentFeesDetails.getFeePaymentDetailsList())) {
			return 0d;
		}

		for (FeePaymentDetails feePaymentDetails : studentFeesDetails.getFeePaymentDetailsList()) {
			dueAmount += feePaymentDetails.getTotalDueAmount();
		}
		return dueAmount;
	}

	public Student getStudent() {
		return student;
	}

	public StudentFeesDetails getStudentFeesDetails() {
		return studentFeesDetails;
	}

	public boolean isPaymentDue() {
		return paymentDue;
	}
	
	

	public double getTotalDueAmount() {
		return totalDueAmount;
	}

	@Override
	public String toString() {
		return "StudentRelieveStatus [student=" + student + ", studentFeesDetails=" + studentFeesDetails
				+ ", paymentDue=" + paymentDue + ", totalDueAmount=" + totalDueAmount + "]";
	}


}
