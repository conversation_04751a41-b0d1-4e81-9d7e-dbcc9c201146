package com.lernen.cloud.core.api.student;

import java.util.UUID;

public class StudentSessionStatusDetails {

    private int instituteId;

    private int academicSessionId;

    private UUID studentId;

    private String admissionNumber;

    private StudentStatus studentStatus;

    public StudentSessionStatusDetails() {
    }

    public StudentSessionStatusDetails(int instituteId, int academicSessionId, UUID studentId, String admissionNumber, StudentStatus studentStatus) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.studentId = studentId;
        this.admissionNumber = admissionNumber;
        this.studentStatus = studentStatus;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public void setAcademicSessionId(int academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public UUID getStudentId() {
        return studentId;
    }

    public void setStudentId(UUID studentId) {
        this.studentId = studentId;
    }

    public String getAdmissionNumber() {
        return admissionNumber;
    }

    public void setAdmissionNumber(String admissionNumber) {
        this.admissionNumber = admissionNumber;
    }

    public StudentStatus getStudentStatus() {
        return studentStatus;
    }

    public void setStudentStatus(StudentStatus studentStatus) {
        this.studentStatus = studentStatus;
    }

    @Override
    public String toString() {
        return "StudentSessionStatusDetails{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", studentId=" + studentId +
                ", admissionNumber='" + admissionNumber + '\'' +
                ", studentStatus=" + studentStatus +
                '}';
    }
}
