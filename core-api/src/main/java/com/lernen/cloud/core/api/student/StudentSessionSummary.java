package com.lernen.cloud.core.api.student;

/**
 * POJO class to hold student session summary data from database queries
 * 
 * <AUTHOR>
 */
public class StudentSessionSummary {

    private final int instituteId;
    private final int academicSessionId;
    private final StudentStatus sessionStatus;
    private final int studentCount;

    public StudentSessionSummary(int instituteId, int academicSessionId, StudentStatus sessionStatus, int studentCount) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.sessionStatus = sessionStatus;
        this.studentCount = studentCount;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public StudentStatus getSessionStatus() {
        return sessionStatus;
    }

    public int getStudentCount() {
        return studentCount;
    }

    @Override
    public String toString() {
        return "StudentSessionSummary{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", sessionStatus=" + sessionStatus +
                ", studentCount=" + studentCount +
                '}';
    }
}
