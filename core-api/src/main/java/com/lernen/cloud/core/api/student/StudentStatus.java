package com.lernen.cloud.core.api.student;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashSet;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
public enum StudentStatus {
	ENROLLED, RELIEVED, DELETED, ENROLMENT_PENDING, NSO;

	public static StudentStatus getStudentStatus(String studentStatus) {
		if (StringUtils.isBlank(studentStatus)) {
			return null;
		}
		for (StudentStatus studentStatusEnum : StudentStatus.values()) {
			if (studentStatusEnum.name().equalsIgnoreCase(studentStatus)) {
				return studentStatusEnum;
			}
		}
		return null;
	}

	public static String getDisplayName(StudentStatus studentStatus) {
		if (studentStatus == null) {
			return null;
		}
		switch(studentStatus) {
			case ENROLLED:
				return "Enrolled";
			case RELIEVED:
				return "Relieved";
			case DELETED:
				return "Deleted";
			case ENROLMENT_PENDING:
				return "Pending";
			case NSO:
				return "NSO";
		}
		return null;
	}

	public static Set<StudentStatus> getStudentStatusSet(String studentStatusCSV) {
		final Set<StudentStatus> studentStatuses = new LinkedHashSet<>();
		if (StringUtils.isBlank(studentStatusCSV)) {
			return studentStatuses;
		}

		final String[] statusTokens = studentStatusCSV.split(",");

		for (final String status : statusTokens) {
			if (StringUtils.isBlank(status))
			{
				continue;
			}
			studentStatuses.add(getStudentStatus(status.trim()));
		}
		return studentStatuses;
	}
	
}
