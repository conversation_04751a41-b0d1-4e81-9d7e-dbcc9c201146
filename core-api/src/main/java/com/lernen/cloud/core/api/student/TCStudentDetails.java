package com.lernen.cloud.core.api.student;

import com.lernen.cloud.core.api.user.UserCategory;

public class TCStudentDetails {

    private String admissionNumber;

    private String studentName;

    private Integer admissionDate;

    private String admissionClass;

    private String fatherGuardianName;

    private String motherName;

    private Integer dob;
    private String proofOfDoBAtTheTimeOfAdmission;

    private UserCategory category;

    private String nationality;

    private String apaarIdNo;

    public TCStudentDetails() {
    }

    public TCStudentDetails(String admissionNumber, String studentName, Integer admissionDate, String admissionClass, String fatherGuardianName, String motherName, Integer dob, String proofOfDoBAtTheTimeOfAdmission, UserCategory category, String nationality, String apaarIdNo) {
        this.admissionNumber = admissionNumber;
        this.studentName = studentName;
        this.admissionDate = admissionDate;
        this.admissionClass = admissionClass;
        this.fatherGuardianName = fatherGuardianName;
        this.motherName = motherName;
        this.dob = dob;
        this.proofOfDoBAtTheTimeOfAdmission = proofOfDoBAtTheTimeOfAdmission;
        this.category = category;
        this.nationality = nationality;
        this.apaarIdNo = apaarIdNo;
    }

    public String getAdmissionNumber() {
        return admissionNumber;
    }

    public void setAdmissionNumber(String admissionNumber) {
        this.admissionNumber = admissionNumber;
    }

    public String getStudentName() {
        return studentName;
    }

    public void setStudentName(String studentName) {
        this.studentName = studentName;
    }

    public Integer getAdmissionDate() {
        return admissionDate;
    }

    public void setAdmissionDate(Integer admissionDate) {
        this.admissionDate = admissionDate;
    }

    public String getAdmissionClass() {
        return admissionClass;
    }

    public void setAdmissionClass(String admissionClass) {
        this.admissionClass = admissionClass;
    }

    public String getFatherGuardianName() {
        return fatherGuardianName;
    }

    public void setFatherGuardianName(String fatherGuardianName) {
        this.fatherGuardianName = fatherGuardianName;
    }

    public String getMotherName() {
        return motherName;
    }

    public void setMotherName(String motherName) {
        this.motherName = motherName;
    }

    public Integer getDob() {
        return dob;
    }

    public void setDob(Integer dob) {
        this.dob = dob;
    }

    public UserCategory getCategory() {
        return category;
    }

    public void setCategory(UserCategory category) {
        this.category = category;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getProofOfDoBAtTheTimeOfAdmission() {
        return proofOfDoBAtTheTimeOfAdmission;
    }

    public void setProofOfDoBAtTheTimeOfAdmission(String proofOfDoBAtTheTimeOfAdmission) {
        this.proofOfDoBAtTheTimeOfAdmission = proofOfDoBAtTheTimeOfAdmission;
    }

    public String getApaarIdNo() {
        return apaarIdNo;
    }

    public void setApaarIdNo(String apaarIdNo) {
        this.apaarIdNo = apaarIdNo;
    }

    @Override
    public String toString() {
        return "TCStudentDetails [admissionNumber=" + admissionNumber + ", studentName=" + studentName
                + ", admissionDate=" + admissionDate + ", admissionClass=" + admissionClass + ", fatherGuardianName="
                + fatherGuardianName + ", motherName=" + motherName + ", dob=" + dob
                + ", proofOfDoBAtTheTimeOfAdmission=" + proofOfDoBAtTheTimeOfAdmission + ", category=" + category
                + ", nationality=" + nationality + ", apaarIdNo=" + apaarIdNo + "]";
    }

    
}
