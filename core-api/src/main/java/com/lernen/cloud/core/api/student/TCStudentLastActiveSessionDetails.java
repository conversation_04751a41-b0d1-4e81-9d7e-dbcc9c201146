package com.lernen.cloud.core.api.student;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

public class TCStudentLastActiveSessionDetails {

    private String lastActiveSessionClass;

    private String lastActiveSessionClassInFigures;

    private String lastActiveSessionClassInWords;

    private String lastExamTakenWithResult;

    private String numberOfTimeExamFailed;

    private List<String> scholasticCoursesLastActiveSession;
    private String promotionToHigherClass;

    private String promotingClassName;

    private String promotingClassNameInFigures;

    private String promotingClassNameInWords;
    private String lastFeesPaid;

    private String discountWithNature;

    private String totalWorkingDays;

    private String totalAttendedDays;

    public TCStudentLastActiveSessionDetails() {
    }

    public TCStudentLastActiveSessionDetails(String lastActiveSessionClass, String lastActiveSessionClassInFigures, String lastActiveSessionClassInWords, String lastExamTakenWithResult, String numberOfTimeExamFailed, List<String> scholasticCoursesLastActiveSession, String promotionToHigherClass, String promotingClassName, String promotingClassNameInFigures, String promotingClassNameInWords, String lastFeesPaid, String discountWithNature, String totalWorkingDays, String totalAttendedDays) {
        this.lastActiveSessionClass = lastActiveSessionClass;
        this.lastActiveSessionClassInFigures = lastActiveSessionClassInFigures;
        this.lastActiveSessionClassInWords = lastActiveSessionClassInWords;
        this.lastExamTakenWithResult = lastExamTakenWithResult;
        this.numberOfTimeExamFailed = numberOfTimeExamFailed;
        this.scholasticCoursesLastActiveSession = scholasticCoursesLastActiveSession;
        this.promotionToHigherClass = promotionToHigherClass;
        this.promotingClassName = promotingClassName;
        this.promotingClassNameInFigures = promotingClassNameInFigures;
        this.promotingClassNameInWords = promotingClassNameInWords;
        this.lastFeesPaid = lastFeesPaid;
        this.discountWithNature = discountWithNature;
        this.totalWorkingDays = totalWorkingDays;
        this.totalAttendedDays = totalAttendedDays;
    }

    public String getLastActiveSessionClass() {
        return lastActiveSessionClass;
    }

    public void setLastActiveSessionClass(String lastActiveSessionClass) {
        this.lastActiveSessionClass = lastActiveSessionClass;
    }

    public String getLastExamTakenWithResult() {
        return lastExamTakenWithResult;
    }

    public void setLastExamTakenWithResult(String lastExamTakenWithResult) {
        this.lastExamTakenWithResult = lastExamTakenWithResult;
    }

    public String getNumberOfTimeExamFailed() {
        return numberOfTimeExamFailed;
    }

    public void setNumberOfTimeExamFailed(String numberOfTimeExamFailed) {
        this.numberOfTimeExamFailed = numberOfTimeExamFailed;
    }

    public List<String> getScholasticCoursesLastActiveSession() {
        return scholasticCoursesLastActiveSession;
    }

    public void setScholasticCoursesLastActiveSession(List<String> scholasticCoursesLastActiveSession) {
        this.scholasticCoursesLastActiveSession = scholasticCoursesLastActiveSession;
    }

    public String getPromotionToHigherClass() {
        return promotionToHigherClass;
    }

    public void setPromotionToHigherClass(String promotionToHigherClass) {
        this.promotionToHigherClass = promotionToHigherClass;
    }

    public String getPromotingClassName() {
        return promotingClassName;
    }

    public void setPromotingClassName(String promotingClassName) {
        this.promotingClassName = promotingClassName;
    }

    public String getLastFeesPaid() {
        return lastFeesPaid;
    }

    public void setLastFeesPaid(String lastFeesPaid) {
        this.lastFeesPaid = lastFeesPaid;
    }

    public String getDiscountWithNature() {
        return discountWithNature;
    }

    public void setDiscountWithNature(String discountWithNature) {
        this.discountWithNature = discountWithNature;
    }

    public String getTotalWorkingDays() {
        return totalWorkingDays;
    }

    public void setTotalWorkingDays(String totalWorkingDays) {
        this.totalWorkingDays = totalWorkingDays;
    }

    public String getTotalAttendedDays() {
        return totalAttendedDays;
    }

    public void setTotalAttendedDays(String totalAttendedDays) {
        this.totalAttendedDays = totalAttendedDays;
    }

    public String getLastActiveSessionClassInFigures() {
        return lastActiveSessionClassInFigures;
    }

    public void setLastActiveSessionClassInFigures(String lastActiveSessionClassInFigures) {
        this.lastActiveSessionClassInFigures = lastActiveSessionClassInFigures;
    }

    public String getLastActiveSessionClassInWords() {
        return lastActiveSessionClassInWords;
    }

    public void setLastActiveSessionClassInWords(String lastActiveSessionClassInWords) {
        this.lastActiveSessionClassInWords = lastActiveSessionClassInWords;
    }

    public String getPromotingClassNameInFigures() {
        return promotingClassNameInFigures;
    }

    public void setPromotingClassNameInFigures(String promotingClassNameInFigures) {
        this.promotingClassNameInFigures = promotingClassNameInFigures;
    }

    public String getPromotingClassNameInWords() {
        return promotingClassNameInWords;
    }

    public void setPromotingClassNameInWords(String promotingClassNameInWords) {
        this.promotingClassNameInWords = promotingClassNameInWords;
    }

    public String formatScholasticCoursesLastActiveSessionString() {
        StringBuilder scholasticCoursesLastActiveSessionString = new StringBuilder();
        if (!CollectionUtils.isEmpty(scholasticCoursesLastActiveSession)) {
            int i = 1;
            for (String subject : scholasticCoursesLastActiveSession) {
                if (StringUtils.isBlank(subject)) {
                    continue;
                }
                scholasticCoursesLastActiveSessionString.append(i).append(". ").append(subject).append("  ");
                i++;
            }
        }
        return scholasticCoursesLastActiveSessionString.toString();
    }


    @Override
    public String toString() {
        return "TCStudentLastActiveSessionDetails{" +
                "lastActiveSessionClass='" + lastActiveSessionClass + '\'' +
                ", lastActiveSessionClassInFigures='" + lastActiveSessionClassInFigures + '\'' +
                ", lastActiveSessionClassInWords='" + lastActiveSessionClassInWords + '\'' +
                ", lastExamTakenWithResult='" + lastExamTakenWithResult + '\'' +
                ", numberOfTimeExamFailed='" + numberOfTimeExamFailed + '\'' +
                ", scholasticCoursesLastActiveSession=" + scholasticCoursesLastActiveSession +
                ", promotionToHigherClass='" + promotionToHigherClass + '\'' +
                ", promotingClassName='" + promotingClassName + '\'' +
                ", promotingClassNameInFigures='" + promotingClassNameInFigures + '\'' +
                ", promotingClassNameInWords='" + promotingClassNameInWords + '\'' +
                ", lastFeesPaid='" + lastFeesPaid + '\'' +
                ", discountWithNature='" + discountWithNature + '\'' +
                ", totalWorkingDays='" + totalWorkingDays + '\'' +
                ", totalAttendedDays='" + totalAttendedDays + '\'' +
                '}';
    }
}
