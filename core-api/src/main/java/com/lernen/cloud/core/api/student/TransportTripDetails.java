package com.lernen.cloud.core.api.student;

import com.embrate.cloud.core.api.transport.tracking.TransportTrackingData;
import com.lernen.cloud.core.api.transport.TransportServiceRoute;
import com.lernen.cloud.core.api.transport.TransportServiceRouteMetadata;

/**
 * <AUTHOR>
 */

public class TransportTripDetails {

    private final TransportServiceRoute transportServiceRoute;

    private final TransportTrackingData transportTrackingData;

    public TransportTripDetails(TransportServiceRoute transportServiceRoute, TransportTrackingData transportTrackingData) {
        this.transportServiceRoute = transportServiceRoute;
        this.transportTrackingData = transportTrackingData;
    }

    public TransportServiceRoute getTransportServiceRoute() {
        return transportServiceRoute;
    }

    public TransportTrackingData getTransportTrackingData() {
        return transportTrackingData;
    }

    @Override
    public String toString() {
        return "TransportTripDetails{" +
                "transportServiceRoute=" + transportServiceRoute +
                ", transportTrackingData=" + transportTrackingData +
                '}';
    }
}
