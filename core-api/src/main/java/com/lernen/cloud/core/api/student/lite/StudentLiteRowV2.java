package com.lernen.cloud.core.api.student.lite;

import com.lernen.cloud.core.api.student.StudentStatus;

import java.io.Serializable;
import java.util.UUID;

/**
 * Minified version of student object where only relevant information is defined
 * 
 * <AUTHOR>
 *
 */
public class StudentLiteRowV2 implements Serializable {

	private final UUID id;
	private final String admNum;
	private final String name;
	private final String fName;
	private final String clsSec;
	private final int level;
	private final String sectionName;

	private final StudentStatus status;

	public StudentLiteRowV2(UUID id, String admNum, String name, String fName, String clsSec, int level, String sectionName, StudentStatus status) {
		this.id = id;
		this.admNum = admNum;
		this.name = name;
		this.fName = fName;
		this.clsSec = clsSec;
		this.level = level;
		this.sectionName = sectionName;
		this.status = status;
	}

	public UUID getId() {
		return id;
	}

	public String getAdmNum() {
		return admNum;
	}

	public String getName() {
		return name;
	}

	public String getfName() {
		return fName;
	}

	public String getClsSec() {
		return clsSec;
	}

	public int getLevel() {
		return level;
	}

	public String getSectionName() {
		return sectionName;
	}

	public StudentStatus getStatus() {
		return status;
	}

	@Override
	public String toString() {
		return "StudentLiteRowV2{" +
				"id=" + id +
				", admNum='" + admNum + '\'' +
				", name='" + name + '\'' +
				", fName='" + fName + '\'' +
				", clsSec='" + clsSec + '\'' +
				", level=" + level +
				", sectionName='" + sectionName + '\'' +
				", status=" + status +
				'}';
	}
}


