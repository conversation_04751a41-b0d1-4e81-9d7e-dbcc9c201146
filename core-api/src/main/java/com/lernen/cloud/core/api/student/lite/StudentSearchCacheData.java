package com.lernen.cloud.core.api.student.lite;

import com.lernen.cloud.core.api.student.StudentStatus;

import java.io.Serializable;
import java.util.UUID;

/**
 * Minified version of student object where only relevant information is defined
 * 
 * <AUTHOR>
 *
 */
public class StudentSearchCacheData implements Serializable {

	private final UUID id;
	private final String text;
	private final StudentStatus status;

	public StudentSearchCacheData(UUID id, String text, StudentStatus status) {
		this.id = id;
		this.text = text;
		this.status = status;
	}

	public UUID getId() {
		return id;
	}

	public String getText() {
		return text;
	}

	public StudentStatus getStatus() {
		return status;
	}

	@Override
	public String toString() {
		return "StudentSearchCacheEntry{" +
				"id=" + id +
				", text='" + text + '\'' +
				", status=" + status +
				'}';
	}
}


