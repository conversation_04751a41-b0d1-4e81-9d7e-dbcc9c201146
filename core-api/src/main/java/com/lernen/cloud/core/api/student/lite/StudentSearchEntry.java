package com.lernen.cloud.core.api.student.lite;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Minified version of student object where only relevant information is defined
 * 
 * <AUTHOR>
 *
 */
public class StudentSearchEntry {

	private final UUID id;
	private final String text;

	public StudentSearchEntry(UUID id, String text) {
		this.id = id;
		this.text = text;
	}

	public UUID getId() {
		return id;
	}

	public String getText() {
		return text;
	}

	@Override
	public String toString() {
		return "StudentSearchEntry{" +
				"id=" + id +
				", text='" + text + '\'' +
				'}';
	}
}


