package com.lernen.cloud.core.api.tracking.events;

/**
 *
 * <AUTHOR>
 *
 */
public enum TrackingEventName {

	USER_LOGIN_SUCCES, USER_LOGIN_FAILED, WEBSITE_HOME_PAGE, WEBSITE_CONTACT_ENQUIRY,
	ADMIN_PORTAL_LOADED, STAFF_PORTAL_LOADED, ADMISSION_MODULE_LOADED, FEE_MODULE_LOADED,
	TRANSPORT_MODULE_LOADED, STORE_MODULE_LOADED, COURSES_MODULE_LOADED, EXAMINATION_MODULE_LOADED,
	ATTENDANCE_MODULE_LOADED, ST<PERSON><PERSON>_<PERSON>NAGEMENT_MODULE_LOADED, INCOME_EXPENSE_MODULE_LOADED,
	USER_MANAGEMENT_MODULE_LOADED, GOOGLE_APP_DOWNLOAD, BROCHURE_VIEW, MO<PERSON><PERSON>_APP_O<PERSON><PERSON>,
	LECTURE_MANAGEMENT_MODULE_LOADED, STUDENT_PORTAL_LOADED, HOMEWORK_MANAGEMENT_MODULE_LOADED,
	NOTICE_BOARD_MANAGEMENT_MODULE_LOADED, SALARY_MANAGEMENT_MODULE_LOADED, TIMETABLE_MANAGEMENT_MODULE_LOADED,
	STUDENT_MANAGEMENT_LOADED, AUDIT_LOGS_LOADED, COMMUNICATION_LOADED, ONLINE_CLASSES_PPT_VIEW,
	STAFF_ATTENDANCE_MODULE_LOADED, HOLIDAY_CALENDAR_LOADED, FRONT_DESK_MODULE_LOADED,
	MOBILE_APPLICATION_MANAGEMENT_MODULE_LOADED, ORGANISATION_PORTAL_LOADED,
	STUDENT_DIARY_MODULE_LOADED, INSTITUTE_MANAGEMENT_MODULE_LOADED, LEAVE_MANAGEMENT_LOADED, COMPLAINT_BOX_LOADED,
	STAFF_DIARY_MODULE_LOADED, PARENTS_APPOINTMENT_MODULE_LOADED, VISITORS_DESK_MODULE_LOADED
	, LIBRARY_MANAGEMENT_MODULE_LOADED, HOSTEL_MANAGEMENT_MODULE_LOADED, ONLINE_ASSESSMENT_MODULE_LOADED, STUDENT_FINANCE_LOADED
}
