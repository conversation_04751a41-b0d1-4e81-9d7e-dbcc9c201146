package com.lernen.cloud.core.api.transport;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeeProportion {

	private UUID feeId;

	private Double proportion;

	public FeeProportion() {
	}

	public FeeProportion(UUID feeId, Double proportion) {
		this.feeId = feeId;
		this.proportion = proportion;
	}

	public UUID getFeeId() {
		return feeId;
	}

	public void setFeeId(UUID feeId) {
		this.feeId = feeId;
	}

	public Double getProportion() {
		return proportion;
	}

	public void setProportion(Double proportion) {
		this.proportion = proportion;
	}

	@Override
	public String toString() {
		return "FeeProportion [feeId=" + feeId + ", proportion=" + proportion + "]";
	}

}
