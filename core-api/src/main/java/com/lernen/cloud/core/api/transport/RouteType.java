/**
 * 
 */
package com.lernen.cloud.core.api.transport;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
public enum RouteType {

	PICK_UP("Pickup"), DROP_OFF("Drop");

	private String displayName;

	RouteType(String displayName) {
		this.displayName = displayName;
	}

	public String getDisplayName() {
		return displayName;
	}

	public static RouteType getRouteType(String routeType) {
		if (StringUtils.isBlank(routeType)) {
			return null;
		}
		for (RouteType routeTypeEnum : RouteType.values()) {
			if (routeTypeEnum.name().equalsIgnoreCase(routeType)) {
				return routeTypeEnum;
			}
		}
		return null;
	}
}
