package com.lernen.cloud.core.api.transport;

import java.util.List;


public class StoppageStudentRouteDetails {
    private final TransportAreaDetail transportAreaDetail;
    private final List<StudentTransportInfo> studentTransportInfoList;
    private final List<TransportServiceRouteMetadata> serviceRouteMetadataList;

    public StoppageStudentRouteDetails( TransportAreaDetail transportAreaDetail, List<StudentTransportInfo> studentTransportInfoList, List<TransportServiceRouteMetadata> serviceRouteMetadataList) {
        
        this.transportAreaDetail = transportAreaDetail;
        this.studentTransportInfoList = studentTransportInfoList;
        this.serviceRouteMetadataList = serviceRouteMetadataList;
    }

    public TransportAreaDetail getTransportAreaDetail() {
        return transportAreaDetail;
    }

    public List<StudentTransportInfo> getStudentTransportInfoList() {
        return studentTransportInfoList;
    }

    public List<TransportServiceRouteMetadata> getServiceRouteMetadataList() {
        return serviceRouteMetadataList;
    }

    @Override
	public String toString() {
		return "StoppageStudentRouteDetails{" +
				"studentTransportInfoList=" + studentTransportInfoList +
				", transportAreaDetail=" + transportAreaDetail +
                ", serviceRouteMetadataList=" + serviceRouteMetadataList +
				'}';
	}
}
