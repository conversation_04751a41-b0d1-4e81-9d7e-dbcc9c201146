package com.lernen.cloud.core.api.transport;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class StudentTransportData {

    private final int instituteId;

    private final UUID transportHistoryId;

    private final int academicSessionId;

    private final UUID studentId;

    private final TransportServiceRouteMetadata pickupTransportServiceRouteMetadata;

    private final TransportServiceRouteMetadata dropTransportServiceRouteMetadata;

    private final TransportArea transportArea;

    private final int currentTimestamp;

    private final int startDate;

    private final int endDate;

    private final TransportStatus transportStatus;

    public StudentTransportData(int instituteId, UUID transportHistoryId, int academicSessionId, UUID studentId, TransportServiceRouteMetadata pickupTransportServiceRouteMetadata,
                                TransportServiceRouteMetadata dropTransportServiceRouteMetadata, TransportArea transportArea, int currentTimestamp,
                                int startDate, int endDate, TransportStatus transportStatus) {
        this.instituteId = instituteId;
        this.transportHistoryId = transportHistoryId;
        this.academicSessionId = academicSessionId;
        this.studentId = studentId;
        this.pickupTransportServiceRouteMetadata = pickupTransportServiceRouteMetadata;
        this.dropTransportServiceRouteMetadata = dropTransportServiceRouteMetadata;
        this.transportArea = transportArea;
        this.currentTimestamp = currentTimestamp;
        this.startDate = startDate;
        this.endDate = endDate;
        this.transportStatus = transportStatus;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public UUID getTransportHistoryId() {
        return transportHistoryId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public UUID getStudentId() {
        return studentId;
    }

    public TransportServiceRouteMetadata getPickupTransportServiceRouteMetadata() {
        return pickupTransportServiceRouteMetadata;
    }

    public TransportServiceRouteMetadata getDropTransportServiceRouteMetadata() {
        return dropTransportServiceRouteMetadata;
    }

    public TransportArea getTransportArea() {
        return transportArea;
    }

    public int getCurrentTimestamp() {
        return currentTimestamp;
    }

    public int getStartDate() {
        return startDate;
    }

    public int getEndDate() {
        return endDate;
    }

    public TransportStatus getTransportStatus() {
        return transportStatus;
    }

    @Override
    public String toString() {
        return "StudentTransportData{" +
                "instituteId=" + instituteId +
                ", transportHistoryId=" + transportHistoryId +
                ", academicSessionId=" + academicSessionId +
                ", studentId=" + studentId +
                ", pickupTransportServiceRouteMetadata=" + pickupTransportServiceRouteMetadata +
                ", dropTransportServiceRouteMetadata=" + dropTransportServiceRouteMetadata +
                ", transportArea=" + transportArea +
                ", currentTimestamp=" + currentTimestamp +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", transportStatus=" + transportStatus +
                '}';
    }
}
