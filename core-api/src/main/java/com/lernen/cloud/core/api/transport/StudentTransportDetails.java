package com.lernen.cloud.core.api.transport;

import java.util.List;
import java.util.UUID;

import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.student.Student;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentTransportDetails {

	private final StudentTransportData studentTransportData;
	private final List<StudentTransportFeeAmount> studentTransportFeeAmountList;

	public StudentTransportDetails(StudentTransportData studentTransportData, List<StudentTransportFeeAmount> studentTransportFeeAmountList) {
		this.studentTransportData = studentTransportData;
		this.studentTransportFeeAmountList = studentTransportFeeAmountList;
	}

	public StudentTransportData getStudentTransportData() {
		return studentTransportData;
	}

	public List<StudentTransportFeeAmount> getStudentTransportFeeAmountList() {
		return studentTransportFeeAmountList;
	}

	public double getTotalAmount() {
		double amount = 0d;
		if (CollectionUtils.isEmpty(studentTransportFeeAmountList)) {
			return amount;
		}
		for (StudentTransportFeeAmount studentTransportFeeAmount : studentTransportFeeAmountList) {
			amount += studentTransportFeeAmount.getAmount();
		}
		return amount;
	}

	@Override
	public String toString() {
		return "StudentTransportDetails{" +
				"studentTransportData=" + studentTransportData +
				", studentTransportFeeAmountList=" + studentTransportFeeAmountList +
				'}';
	}
}
