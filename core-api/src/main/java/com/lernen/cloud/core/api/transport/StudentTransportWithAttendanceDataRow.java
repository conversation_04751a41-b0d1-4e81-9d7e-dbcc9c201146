package com.lernen.cloud.core.api.transport;

import com.lernen.cloud.core.api.attendance.AttendanceRecord;
import com.lernen.cloud.core.api.student.StudentLite;

public class StudentTransportWithAttendanceDataRow {
    private  int instituteId;

    private  int academicSessionId;

    private  StudentLite studentLite;

    private  TransportAreaDetail transportAreaDetail;

    private  TransportServiceRouteMetadata pickupTransportServiceRouteMetadata;

    private  TransportServiceRouteMetadata dropTransportServiceRouteMetadata;

    private  AttendanceRecord attendanceRecord;

    public StudentTransportWithAttendanceDataRow(int instituteId, int academicSessionId, StudentLite studentLite, TransportAreaDetail transportAreaDetail, TransportServiceRouteMetadata pickupTransportServiceRouteMetadata, TransportServiceRouteMetadata dropTransportServiceRouteMetadata, AttendanceRecord attendanceRecord) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.studentLite = studentLite;
        this.transportAreaDetail = transportAreaDetail;
        this.pickupTransportServiceRouteMetadata = pickupTransportServiceRouteMetadata;
        this.dropTransportServiceRouteMetadata = dropTransportServiceRouteMetadata;
        this.attendanceRecord = attendanceRecord;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public void setAcademicSessionId(int academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public StudentLite getStudentLite() {
        return studentLite;
    }

    public void setStudentLite(StudentLite studentLite) {
        this.studentLite = studentLite;
    }

    public TransportAreaDetail getTransportAreaDetail() {
        return transportAreaDetail;
    }

    public void setTransportAreaDetail(TransportAreaDetail transportAreaDetail) {
        this.transportAreaDetail = transportAreaDetail;
    }

    public TransportServiceRouteMetadata getPickupTransportServiceRouteMetadata() {
        return pickupTransportServiceRouteMetadata;
    }

    public void setPickupTransportServiceRouteMetadata(TransportServiceRouteMetadata pickupTransportServiceRouteMetadata) {
        this.pickupTransportServiceRouteMetadata = pickupTransportServiceRouteMetadata;
    }

    public TransportServiceRouteMetadata getDropTransportServiceRouteMetadata() {
        return dropTransportServiceRouteMetadata;
    }

    public void setDropTransportServiceRouteMetadata(TransportServiceRouteMetadata dropTransportServiceRouteMetadata) {
        this.dropTransportServiceRouteMetadata = dropTransportServiceRouteMetadata;
    }

    public AttendanceRecord getAttendanceRecord() {
        return attendanceRecord;
    }

    public void setAttendanceRecord(AttendanceRecord attendanceRecord) {
        this.attendanceRecord = attendanceRecord;
    }

    @Override
    public String toString() {
        return "StudentTransportWithAttendanceDataRow{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", studentLite=" + studentLite +
                ", transportAreaDetail=" + transportAreaDetail +
                ", pickupTransportServiceRouteMetadata=" + pickupTransportServiceRouteMetadata +
                ", dropTransportServiceRouteMetadata=" + dropTransportServiceRouteMetadata +
                ", attendanceRecord=" + attendanceRecord +
                '}';
    }
}
