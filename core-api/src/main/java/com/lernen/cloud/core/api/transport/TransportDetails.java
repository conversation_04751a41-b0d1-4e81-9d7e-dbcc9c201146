package com.lernen.cloud.core.api.transport;

import java.util.List;

public class TransportDetails {

    private List<TransportServiceRouteWithAmount> transportServiceRouteResponseList;

    private List<TransportArea> transportAreaList;

    private TransportFeeConfigData transportFeeConfigData;

    public TransportDetails(List<TransportServiceRouteWithAmount> transportServiceRouteResponseList, List<TransportArea> transportAreaList, TransportFeeConfigData transportFeeConfigData) {
        this.transportServiceRouteResponseList = transportServiceRouteResponseList;
        this.transportAreaList = transportAreaList;
        this.transportFeeConfigData = transportFeeConfigData;
    }

    public List<TransportServiceRouteWithAmount> getTransportServiceRouteResponseList() {
        return transportServiceRouteResponseList;
    }

    public void setTransportServiceRouteResponseList(List<TransportServiceRouteWithAmount> transportServiceRouteResponseList) {
        this.transportServiceRouteResponseList = transportServiceRouteResponseList;
    }

    public List<TransportArea> getTransportAreaList() {
        return transportAreaList;
    }

    public void setTransportAreaList(List<TransportArea> transportAreaList) {
        this.transportAreaList = transportAreaList;
    }

    public TransportFeeConfigData getTransportFeeConfigData() {
        return transportFeeConfigData;
    }

    public void setTransportFeeConfigData(TransportFeeConfigData transportFeeConfigData) {
        this.transportFeeConfigData = transportFeeConfigData;
    }

    @Override
    public String toString() {
        return "TransportDetails{" +
                "transportServiceRouteResponseList=" + transportServiceRouteResponseList +
                ", transportAreaList=" + transportAreaList +
                ", transportFeeConfigData=" + transportFeeConfigData +
                '}';
    }
}
