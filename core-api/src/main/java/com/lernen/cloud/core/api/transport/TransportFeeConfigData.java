package com.lernen.cloud.core.api.transport;

import java.util.List;

import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.fees.FeeConfigurationResponse;

/**
 * 
 * <AUTHOR>
 *
 */
public class TransportFeeConfigData {

	private final List<FeeConfigurationResponse> configurableFees;

	private final List<TransportConfiguredFeeData> transportConfiguredFeeDatas;

	private final boolean configured;

	public TransportFeeConfigData(List<FeeConfigurationResponse> configurableFees,
			List<TransportConfiguredFeeData> transportConfiguredFeeDatas) {
		this.configurableFees = configurableFees;
		this.transportConfiguredFeeDatas = transportConfiguredFeeDatas;
		this.configured = !CollectionUtils.isEmpty(transportConfiguredFeeDatas);
	}

	public List<FeeConfigurationResponse> getConfigurableFees() {
		return configurableFees;
	}

	public List<TransportConfiguredFeeData> getTransportConfiguredFeeDatas() {
		return transportConfiguredFeeDatas;
	}

	public boolean isConfigured() {
		return configured;
	}

	@Override
	public String toString() {
		return "TransportFeeConfigData [configurableFees=" + configurableFees + ", transportConfiguredFeeDatas="
				+ transportConfiguredFeeDatas + ", configured=" + configured + "]";
	}

}
