package com.lernen.cloud.core.api.transport;

import java.util.UUID;

public class TransportParentArea {

    private int instituteId;
	
	private UUID parentId;
	
	private String parentName;

    public TransportParentArea(int instituteId, UUID parentId, String parentName) {
		this.instituteId = instituteId;
		this.parentId = parentId;
		this.parentName = parentName;	
	}
	
    public TransportParentArea() {
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getParentId() {
		return parentId;
	}

	public void setParentId(UUID parentId) {
		this.parentId = parentId;
	}

	public String getParentName() {
		return parentName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}

	@Override
	public String toString() {
		return "TransportArea [instituteId=" + instituteId + ", parentId=" + parentId + ", parentName=" + parentName +"]";
	}
    
}
