package com.lernen.cloud.core.api.transport;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class TransportServiceRouteMetadata {

	private final int instituteId;

	private final int academicSessionId;

	private final UUID serviceRouteId;

	private final String serviceRouteName;

	private final Vehicle vehicle;

	private final RouteType routeType;

	public TransportServiceRouteMetadata(int instituteId, int academicSessionId, UUID serviceRouteId, String serviceRouteName, Vehicle vehicle, RouteType routeType) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.serviceRouteId = serviceRouteId;
		this.serviceRouteName = serviceRouteName;
		this.vehicle = vehicle;
		this.routeType = routeType;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public UUID getServiceRouteId() {
		return serviceRouteId;
	}

	public String getServiceRouteName() {
		return serviceRouteName;
	}

	public Vehicle getVehicle() {
		return vehicle;
	}

	public RouteType getRouteType() {
		return routeType;
	}

	@Override
	public String toString() {
		return "TransportServiceRouteMetadata{" +
				"instituteId=" + instituteId +
				", academicSessionId=" + academicSessionId +
				", serviceRouteId=" + serviceRouteId +
				", serviceRouteName='" + serviceRouteName + '\'' +
				", vehicle=" + vehicle +
				", routeType=" + routeType +
				'}';
	}
}
