package com.lernen.cloud.core.api.transport;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class TransportServiceRouteMetadataRow {

    private final int instituteId;

    private final int academicSessionId;

    private final UUID serviceRouteId;

    private final String serviceRouteName;

    private final Vehicle vehicle;

    private final RouteType routeType;

    private final UUID defaultDriver;

    private final UUID defaultConductor;

    public TransportServiceRouteMetadataRow(int instituteId, int academicSessionId, UUID serviceRouteId, String serviceRouteName, Vehicle vehicle, RouteType routeType, UUID defaultDriver, UUID defaultConductor) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.serviceRouteId = serviceRouteId;
        this.serviceRouteName = serviceRouteName;
        this.vehicle = vehicle;
        this.routeType = routeType;
        this.defaultDriver = defaultDriver;
        this.defaultConductor = defaultConductor;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public UUID getServiceRouteId() {
        return serviceRouteId;
    }

    public String getServiceRouteName() {
        return serviceRouteName;
    }

    public Vehicle getVehicle() {
        return vehicle;
    }

    public RouteType getRouteType() {
        return routeType;
    }

    public UUID getDefaultDriver() {
        return defaultDriver;
    }

    public UUID getDefaultConductor() {
        return defaultConductor;
    }

    @Override
    public String toString() {
        return "TransportServiceRouteMetadataRow{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", serviceRouteId=" + serviceRouteId +
                ", serviceRouteName='" + serviceRouteName + '\'' +
                ", vehicle=" + vehicle +
                ", routeType=" + routeType +
                ", defaultDriver=" + defaultDriver +
                ", defaultConductor=" + defaultConductor +
                '}';
    }
}
