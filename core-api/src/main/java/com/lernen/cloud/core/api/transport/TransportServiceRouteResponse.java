package com.lernen.cloud.core.api.transport;

import java.util.List;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class TransportServiceRouteResponse {

	private int instituteId;
	
	private int academicSessionId;

	private UUID serviceRouteId;

	private String serviceRouteName;

	private VehiclePayload vehicle;

	private List<TransportServiceRouteStoppagesResponse> stoppagesList;

	private int studentCount;

	public TransportServiceRouteResponse(int instituteId, int academicSessionId, UUID serviceRouteId, String serviceRouteName, VehiclePayload vehicle,
			List<TransportServiceRouteStoppagesResponse> stoppagesList) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.serviceRouteId = serviceRouteId;
		this.serviceRouteName = serviceRouteName;
		this.vehicle = vehicle;
		this.stoppagesList = stoppagesList;
	}

	public int getInstituteId() {
		return instituteId;
	}
	
	/**
	 * @return the academicsessionId
	 */
	public int getAcademicsessionId() {
		return academicSessionId;
	}

	public UUID getServiceRouteId() {
		return serviceRouteId;
	}

	public String getServiceRouteName() {
		return serviceRouteName;
	}

	public VehiclePayload getVehicle() {
		return vehicle;
	}

	public List<TransportServiceRouteStoppagesResponse> getStoppagesList() {
		return stoppagesList;
	}

	public int getStudentCount() {
		return studentCount;
	}

	public void setStudentCount(int studentCount) {
		this.studentCount = studentCount;
	}

	@Override
	public String toString() {
		return "TransportServiceRouteResponse [instituteId=" + instituteId + ", serviceRouteId=" + serviceRouteId
				+ ", serviceRouteName=" + serviceRouteName + ", vehicle=" + vehicle + ", stoppagesList=" + stoppagesList
				+ ", studentCount=" + studentCount + "]";
	}

}
