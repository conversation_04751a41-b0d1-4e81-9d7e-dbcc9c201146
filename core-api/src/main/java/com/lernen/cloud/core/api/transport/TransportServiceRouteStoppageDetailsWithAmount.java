package com.lernen.cloud.core.api.transport;

/**
 * <AUTHOR>
 */
public class TransportServiceRouteStoppageDetailsWithAmount {

    private final TransportServiceRouteStoppageDetails transportServiceRouteStoppageDetails;

    private final Double amount;

    public TransportServiceRouteStoppageDetailsWithAmount(TransportServiceRouteStoppageDetails transportServiceRouteStoppageDetails, Double amount) {
        this.transportServiceRouteStoppageDetails = transportServiceRouteStoppageDetails;
        this.amount = amount;
    }

    public TransportServiceRouteStoppageDetails getTransportServiceRouteStoppageDetails() {
        return transportServiceRouteStoppageDetails;
    }

    public Double getAmount() {
        return amount;
    }

    @Override
    public String toString() {
        return "TransportServiceRouteStoppageDetailsWithAmount{" +
                "transportServiceRouteStoppageDetails=" + transportServiceRouteStoppageDetails +
                ", amount=" + amount +
                '}';
    }
}
