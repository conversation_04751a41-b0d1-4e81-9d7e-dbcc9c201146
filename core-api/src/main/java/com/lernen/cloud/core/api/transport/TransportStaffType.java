package com.lernen.cloud.core.api.transport;

import org.apache.commons.lang3.StringUtils;

public enum TransportStaffType {
	DRIVER, CONDUCTOR;
	
	public static TransportStaffType getTransportStaffType(String transportStaffType) {
		if (StringUtils.isBlank(transportStaffType)) {
			return null;
		}
		for (TransportStaffType transportStaffTypeEnum : TransportStaffType.values()) {
			if (transportStaffTypeEnum.name().equalsIgnoreCase(transportStaffType)) {
				return transportStaffTypeEnum;
			}
		}
		return null;
	}
}
