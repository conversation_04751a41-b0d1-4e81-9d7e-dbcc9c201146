package com.lernen.cloud.core.api.transport;

import java.util.List;

import com.lernen.cloud.core.api.student.Student;


public class TransportStats {
    private final int totalEnrolledStudentCount;
    private final int totalTransportAssignedStudentCount;
    private final int totalTransportUnassignedStudentCount;

    public TransportStats(int totalEnrolledStudentCount,  int totalTransportAssignedStudentCount, int totalTransportUnassignedStudentCount){
        this.totalEnrolledStudentCount = totalEnrolledStudentCount;
        this.totalTransportAssignedStudentCount = totalTransportAssignedStudentCount;
        this.totalTransportUnassignedStudentCount = totalTransportUnassignedStudentCount;
    }

    public int getTotalEnrolledStudentCount() {
        return totalEnrolledStudentCount;
    }

    public int getTotalTransportAssignedStudentCount() {
        return totalTransportAssignedStudentCount;
    }

    public int getTotalTransportUnassignedStudentCount() {
        return totalTransportUnassignedStudentCount;
    }

    @Override
	public String toString() {
		return "TransportStats{" +
				"totalEnrolledStudentCount=" + totalEnrolledStudentCount +
				", totalTransportAssignedStudentCount=" + totalTransportAssignedStudentCount +
                ", totalTransportUnassignedStudentCount=" + totalTransportUnassignedStudentCount +
				'}';
	}

}
