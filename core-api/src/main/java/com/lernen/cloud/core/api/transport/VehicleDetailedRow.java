package com.lernen.cloud.core.api.transport;

import com.lernen.cloud.core.api.user.Document;

import java.util.List;

public class VehicleDetailedRow {

	private int instituteId;

	private int vehicleId;

	private String vehicleNumber;

	private String vehicleCode;

	private String registrationNumber;

	private VehicleType vehicleType;

	private int capacity;

	private boolean active;

	private String engineNumber;

	private String batteryNumber;

	private String chassisNumber;

	private Integer purchaseDate;

	private String tyreNumber;

	private String manufacturer;

	private double mileage;

	private double bodyCost;

	private String make;

	private String model;

	private Integer year;

	private String financer;

	private double chassisCost;

	private String pucNumber;

	private List<Document<VehicleDocumentType>> vehicleDocumnetInfo;

	public VehicleDetailedRow(int instituteId, int vehicleId, String vehicleNumber, String vehicleCode, String registrationNumber, VehicleType vehicleType, int capacity, boolean active, String engineNumber, String batteryNumber, String chassisNumber, Integer purchaseDate, String tyreNumber, String manufacturer, double mileage, double bodyCost, String make, String model, Integer year, String financer, double chassisCost, String pucNumber, List<Document<VehicleDocumentType>> vehicleDocumnetInfo) {
		this.instituteId = instituteId;
		this.vehicleId = vehicleId;
		this.vehicleNumber = vehicleNumber;
		this.vehicleCode = vehicleCode;
		this.registrationNumber = registrationNumber;
		this.vehicleType = vehicleType;
		this.capacity = capacity;
		this.active = active;
		this.engineNumber = engineNumber;
		this.batteryNumber = batteryNumber;
		this.chassisNumber = chassisNumber;
		this.purchaseDate = purchaseDate;
		this.tyreNumber = tyreNumber;
		this.manufacturer = manufacturer;
		this.mileage = mileage;
		this.bodyCost = bodyCost;
		this.make = make;
		this.model = model;
		this.year = year;
		this.financer = financer;
		this.chassisCost = chassisCost;
		this.pucNumber = pucNumber;
		this.vehicleDocumnetInfo = vehicleDocumnetInfo;
	}

	public int getInstituteId() { return instituteId;}

	public int getVehicleId() { return vehicleId;}

	public String getVehicleNumber() { return vehicleNumber;}

	public String getVehicleCode() { return vehicleCode;}

	public String getRegistrationNumber() { return registrationNumber;}

	public VehicleType getVehicleType() { return vehicleType;}

	public int getCapacity() { return capacity;}

	public boolean isActive() { return active;}

	public String getEngineNumber() { return engineNumber;}

	public String getBatteryNumber() { return batteryNumber;}

	public String getChassisNumber() { return chassisNumber;}

	public Integer getPurchaseDate() { return purchaseDate;}

	public String getTyreNumber() { return tyreNumber;}

	public String getManufacturer() { return manufacturer;}

	public double getMileage() { return mileage;}

	public double getBodyCost() { return bodyCost;}

	public String getMake() { return make;}

	public String getModel() { return model;}

	public Integer getYear() { return year;}

	public String getFinancer() { return financer;}

	public double getChassisCost() { return chassisCost;}

	public String getPucNumber() { return pucNumber;}

	public List<Document<VehicleDocumentType>> getVehicleDocumnetInfo() { return vehicleDocumnetInfo;}

	@Override
	public String toString() {
		return "VehicleDetailedRow{" +
				"instituteId=" + instituteId +
				", vehicleId=" + vehicleId +
				", vehicleNumber='" + vehicleNumber + '\'' +
				", vehicleCode='" + vehicleCode + '\'' +
				", registrationNumber='" + registrationNumber + '\'' +
				", vehicleType=" + vehicleType +
				", capacity=" + capacity +
				", active=" + active +
				", engineNumber='" + engineNumber + '\'' +
				", batteryNumber='" + batteryNumber + '\'' +
				", chassisNumber='" + chassisNumber + '\'' +
				", purchaseDate=" + purchaseDate +
				", tyreNumber='" + tyreNumber + '\'' +
				", manufacturer='" + manufacturer + '\'' +
				", mileage=" + mileage +
				", bodyCost=" + bodyCost +
				", make='" + make + '\'' +
				", model='" + model + '\'' +
				", year=" + year +
				", financer='" + financer + '\'' +
				", chassisCost=" + chassisCost +
				", pucNumber='" + pucNumber + '\'' +
				", vehicleDocumnetInfo=" + vehicleDocumnetInfo +
				'}';
	}
}
