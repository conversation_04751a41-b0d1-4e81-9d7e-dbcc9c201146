package com.lernen.cloud.core.api.transport;

import com.lernen.cloud.core.api.user.Document;

import java.util.List;

public class VehicleDocumentRenewalNotificationInfo {

    private final int instituteId;

    private final int vehicleId;

    private final String vehicleNumber;

    private final String vehicleCode;

    private final String registrationNumber;

    private final VehicleType vehicleType;

    private final VehicleDocumentInfo vehicleDocumentInfo;

    public VehicleDocumentRenewalNotificationInfo(int instituteId, int vehicleId, String vehicleNumber, String vehicleCode, String registrationNumber, VehicleType vehicleType, VehicleDocumentInfo vehicleDocumentInfo) {
        this.instituteId = instituteId;
        this.vehicleId = vehicleId;
        this.vehicleNumber = vehicleNumber;
        this.vehicleCode = vehicleCode;
        this.registrationNumber = registrationNumber;
        this.vehicleType = vehicleType;
        this.vehicleDocumentInfo = vehicleDocumentInfo;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public int getVehicleId() {
        return vehicleId;
    }

    public String getVehicleNumber() {
        return vehicleNumber;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public String getRegistrationNumber() {
        return registrationNumber;
    }

    public VehicleType getVehicleType() {
        return vehicleType;
    }

    public VehicleDocumentInfo getVehicleDocumentInfo() {
        return vehicleDocumentInfo;
    }

    @Override
    public String toString() {
        return "VehicleDocumentRenewalNotificationInfo{" +
                "instituteId=" + instituteId +
                ", vehicleId=" + vehicleId +
                ", vehicleNumber='" + vehicleNumber + '\'' +
                ", vehicleCode='" + vehicleCode + '\'' +
                ", registrationNumber='" + registrationNumber + '\'' +
                ", vehicleType=" + vehicleType +
                ", vehicleDocumentInfo=" + vehicleDocumentInfo +
                '}';
    }
}
