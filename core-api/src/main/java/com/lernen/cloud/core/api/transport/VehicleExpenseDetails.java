package com.lernen.cloud.core.api.transport;


import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.incomeexpense.IncomeExpenseTransactionType;
import com.lernen.cloud.core.api.user.Document;

import java.util.Map;
import java.util.UUID;

public class VehicleExpenseDetails {
        private int instituteId;
        private int academicSessionId;
        private int vehicleId;
        private String vehicleNumber;
        private String vehicleCode;
        private String registrationNumber;
        private VehicleType vehicleType;
        private UUID transactionId;
        private TransactionMode transactionMode;
        private Integer transactionDate;
        private String transactionTitle;
        private Double quantity;
        private Double rate;
        private Double amount;
        private Double kmReading;
        private String partOfVehicle;
        private VehicleLogCategory categoryType;
        private IncomeExpenseTransactionType incomeExpenseTransactionType;
        private Document<VehicleLogDocumentType> vehicleLogDocument;
        private UUID driverId;
        private String description;

        public VehicleExpenseDetails(){

        }

        public VehicleExpenseDetails(int instituteId, int academicSessionId, int vehicleId, String vehicleNumber, String vehicleCode, String registrationNumber, VehicleType vehicleType, UUID transactionId, TransactionMode transactionMode, Integer transactionDate, String transactionTitle, Double quantity, Double rate, Double amount, Double kmReading, String partOfVehicle, VehicleLogCategory categoryType, IncomeExpenseTransactionType incomeExpenseTransactionType, Document<VehicleLogDocumentType> vehicleLogDocument, UUID driverId, String description) {
                this.instituteId = instituteId;
                this.academicSessionId = academicSessionId;
                this.vehicleId = vehicleId;
                this.vehicleNumber = vehicleNumber;
                this.vehicleCode = vehicleCode;
                this.registrationNumber = registrationNumber;
                this.vehicleType = vehicleType;
                this.transactionId = transactionId;
                this.transactionMode = transactionMode;
                this.transactionDate = transactionDate;
                this.transactionTitle = transactionTitle;
                this.quantity = quantity;
                this.rate = rate;
                this.amount = amount;
                this.kmReading = kmReading;
                this.partOfVehicle = partOfVehicle;
                this.categoryType = categoryType;
                this.incomeExpenseTransactionType = incomeExpenseTransactionType;
                this.vehicleLogDocument = vehicleLogDocument;
                this.driverId = driverId;
                this.description = description;
        }

        public int getInstituteId() {
                return instituteId;
        }

        public void setInstituteId(int instituteId) {
                this.instituteId = instituteId;
        }

        public int getAcademicSessionId() {
                return academicSessionId;
        }

        public void setAcademicSessionId(int academicSessionId) {
                this.academicSessionId = academicSessionId;
        }

        public int getVehicleId() {
                return vehicleId;
        }

        public void setVehicleId(int vehicleId) {
                this.vehicleId = vehicleId;
        }

        public String getVehicleNumber() {
                return vehicleNumber;
        }

        public void setVehicleNumber(String vehicleNumber) {
                this.vehicleNumber = vehicleNumber;
        }

        public String getVehicleCode() {
                return vehicleCode;
        }

        public void setVehicleCode(String vehicleCode) {
                this.vehicleCode = vehicleCode;
        }

        public String getRegistrationNumber() {
                return registrationNumber;
        }

        public void setRegistrationNumber(String registrationNumber) {
                this.registrationNumber = registrationNumber;
        }

        public VehicleType getVehicleType() {
                return vehicleType;
        }

        public void setVehicleType(VehicleType vehicleType) {
                this.vehicleType = vehicleType;
        }


        public UUID getTransactionId() {
                return transactionId;
        }

        public void setTransactionId(UUID transactionId) {
                this.transactionId = transactionId;
        }

        public TransactionMode getTransactionMode() {
                return transactionMode;
        }

        public void setTransactionMode(TransactionMode transactionMode) {
                this.transactionMode = transactionMode;
        }

        public Integer getTransactionDate() {
                return transactionDate;
        }

        public void setTransactionDate(Integer transactionDate) {
                this.transactionDate = transactionDate;
        }

        public String getTransactionTitle() {
                return transactionTitle;
        }

        public void setTransactionTitle(String transactionTitle) {
                this.transactionTitle = transactionTitle;
        }

        public Double getQuantity() {
                return quantity;
        }

        public void setQuantity(Double quantity) {
                this.quantity = quantity;
        }

        public Double getRate() {
                return rate;
        }

        public void setRate(Double rate) {
                this.rate = rate;
        }

        public Double getAmount() {
                return amount;
        }

        public void setAmount(Double amount) {
                this.amount = amount;
        }

        public Double getKmReading() {
                return kmReading;
        }

        public void setKmReading(Double kmReading) {
                this.kmReading = kmReading;
        }

        public String getPartOfVehicle() {
                return partOfVehicle;
        }

        public void setPartOfVehicle(String partOfVehicle) {
                this.partOfVehicle = partOfVehicle;
        }

        public VehicleLogCategory getCategoryType() {
                return categoryType;
        }

        public void setCategoryType(VehicleLogCategory categoryType) {
                this.categoryType = categoryType;
        }

        public Document<VehicleLogDocumentType> getVehicleLogDocument() {
                return vehicleLogDocument;
        }

        public void setVehicleLogDocument(Document<VehicleLogDocumentType> vehicleLogDocument) {
                this.vehicleLogDocument = vehicleLogDocument;
        }

        public IncomeExpenseTransactionType getIncomeExpenseTransactionType() {
                return incomeExpenseTransactionType;
        }

        public void setIncomeExpenseTransactionType(IncomeExpenseTransactionType incomeExpenseTransactionType) {
                this.incomeExpenseTransactionType = incomeExpenseTransactionType;
        }

        public UUID getDriverId() {
                return driverId;
        }

        public void setDriverId(UUID driverId) {
                this.driverId = driverId;
        }

        public String getDescription() {
                return description;
        }

        public void setDescription(String description) {
                this.description = description;
        }

        @Override
        public String toString() {
                return "VehicleExpenseDetails{" +
                        "instituteId=" + instituteId +
                        ", academicSessionId=" + academicSessionId +
                        ", vehicleId=" + vehicleId +
                        ", vehicleNumber='" + vehicleNumber + '\'' +
                        ", vehicleCode='" + vehicleCode + '\'' +
                        ", registrationNumber='" + registrationNumber + '\'' +
                        ", vehicleType=" + vehicleType +
                        ", transactionId=" + transactionId +
                        ", transactionMode=" + transactionMode +
                        ", transactionDate=" + transactionDate +
                        ", transactionTitle='" + transactionTitle + '\'' +
                        ", quantity=" + quantity +
                        ", rate=" + rate +
                        ", amount=" + amount +
                        ", kmReading=" + kmReading +
                        ", partOfVehicle='" + partOfVehicle + '\'' +
                        ", categoryType=" + categoryType +
                        ", incomeExpenseTransactionType=" + incomeExpenseTransactionType +
                        ", vehicleLogDocument=" + vehicleLogDocument +
                        ", driverId=" + driverId +
                        ", description='" + description + '\'' +
                        '}';
        }
}
