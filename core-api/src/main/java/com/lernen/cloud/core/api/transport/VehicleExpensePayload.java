package com.lernen.cloud.core.api.transport;

import com.lernen.cloud.core.api.common.TransactionMode;

import java.util.UUID;

public class VehicleExpensePayload {
    private int academicSessionId;
    private UUID transactionId;
    private int vehicleId;
    private TransactionMode transactionMode;
    private Double quantity;
    private Double amount;
    private Integer transactionDate;
    private String transactionTitle;
    private VehicleLogCategory categoryType;
    private UUID createdUserId;
    private UUID updatedUserId;
    private UUID transactionBy;
    private Double rate;
    private Double kmReading;
    private String partOfVehicle;
    private String description;


    public VehicleExpensePayload(){

    }

    public VehicleExpensePayload(int academicSessionId, UUID transactionId, int vehicleId, TransactionMode transactionMode, Double quantity, Double amount, Integer transactionDate, String transactionTitle, VehicleLogCategory categoryType, UUID createdUserId, UUID updatedUserId, UUID transactionBy, Double rate, Double kmReading, String partOfVehicle, String description) {
        this.academicSessionId = academicSessionId;
        this.transactionId = transactionId;
        this.vehicleId = vehicleId;
        this.transactionMode = transactionMode;
        this.quantity = quantity;
        this.amount = amount;
        this.transactionDate = transactionDate;
        this.transactionTitle = transactionTitle;
        this.categoryType = categoryType;
        this.createdUserId = createdUserId;
        this.updatedUserId = updatedUserId;
        this.transactionBy = transactionBy;
        this.rate = rate;
        this.kmReading = kmReading;
        this.partOfVehicle = partOfVehicle;
        this.description = description;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public void setAcademicSessionId(int academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public UUID getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(UUID transactionId) {
        this.transactionId = transactionId;
    }

    public int getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(int vehicleId) {
        this.vehicleId = vehicleId;
    }

    public TransactionMode getTransactionMode() {
        return transactionMode;
    }

    public void setTransactionMode(TransactionMode transactionMode) {
        this.transactionMode = transactionMode;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Integer getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(Integer transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getTransactionTitle() {
        return transactionTitle;
    }

    public void setTransactionTitle(String transactionTitle) {
        this.transactionTitle = transactionTitle;
    }

    public VehicleLogCategory getCategoryType() {
        return categoryType;
    }

    public void setCategoryType(VehicleLogCategory categoryType) {
        this.categoryType = categoryType;
    }

    public UUID getCreatedUserId() {
        return createdUserId;
    }

    public void setCreatedUserId(UUID createdUserId) {
        this.createdUserId = createdUserId;
    }

    public UUID getUpdatedUserId() {
        return updatedUserId;
    }

    public void setUpdatedUserId(UUID updatedUserId) {
        this.updatedUserId = updatedUserId;
    }

    public UUID getTransactionBy() {
        return transactionBy;
    }

    public void setTransactionBy(UUID transactionBy) {
        this.transactionBy = transactionBy;
    }

    public Double getRate() {
        return rate;
    }

    public void setRate(Double rate) {
        this.rate = rate;
    }

    public Double getKmReading() {
        return kmReading;
    }

    public void setKmReading(Double kmReading) {
        this.kmReading = kmReading;
    }

    public String getPartOfVehicle() {
        return partOfVehicle;
    }

    public void setPartOfVehicle(String partOfVehicle) {
        this.partOfVehicle = partOfVehicle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "VehicleExpensePayload{" +
                "academicSessionId=" + academicSessionId +
                ", transactionId=" + transactionId +
                ", vehicleId=" + vehicleId +
                ", transactionMode=" + transactionMode +
                ", quantity=" + quantity +
                ", amount=" + amount +
                ", transactionDate=" + transactionDate +
                ", transactionTitle='" + transactionTitle + '\'' +
                ", categoryType=" + categoryType +
                ", createdUserId=" + createdUserId +
                ", updatedUserId=" + updatedUserId +
                ", transactionBy=" + transactionBy +
                ", rate=" + rate +
                ", kmReading=" + kmReading +
                ", partOfVehicle='" + partOfVehicle + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
