package com.lernen.cloud.core.api.transport;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class VehiclePayload {
	
	private int instituteId;
	
	private int vehicleId;
	
	private String vehicleNumber;
	
	private String vehicleCode;
	
	private String registrationNumber;
	
	private int vehicleTypeId;
	
	private int capacity;
	
	private boolean active;

	private String engineNumber;

	private String batteryNumber;

	private String chassisNumber;

	private Integer purchaseDate;

	private String tyreNumber;

	private String manufacturer;

	private double mileage;

	private double bodyCost;

	private String make;

	private String model;

	private Integer year;

	private String financer;

	private double chassisCost;

	private String pucNumber;

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public int getVehicleId() {
		return vehicleId;
	}

	public void setVehicleId(int vehicleId) {
		this.vehicleId = vehicleId;
	}

	public String getVehicleNumber() {
		return vehicleNumber;
	}

	public void setVehicleNumber(String vehicleNumber) {
		this.vehicleNumber = vehicleNumber;
	}

	public String getVehicleCode() {
		return vehicleCode;
	}

	public void setVehicleCode(String vehicleCode) {
		this.vehicleCode = vehicleCode;
	}

	public String getRegistrationNumber() {
		return registrationNumber;
	}

	public void setRegistrationNumber(String registrationNumber) {
		this.registrationNumber = registrationNumber;
	}

	public int getVehicleTypeId() {
		return vehicleTypeId;
	}

	public void setVehicleTypeId(int vehicleTypeId) {
		this.vehicleTypeId = vehicleTypeId;
	}

	public int getCapacity() {
		return capacity;
	}

	public void setCapacity(int capacity) {
		this.capacity = capacity;
	}

	public boolean isActive() {
		return active;
	}

	public void setActive(boolean active) {
		this.active = active;
	}

	public String getEngineNumber() {return engineNumber;}

	public void setEngineNumber(String engineNumber) {this.engineNumber = engineNumber;}

	public String getBatteryNumber() {return batteryNumber;}

	public void setBatteryNumber(String batteryNumber) {this.batteryNumber = batteryNumber;}

	public String getChassisNumber() {return chassisNumber;}

	public void setChassisNumber(String chassisNumber) {this.chassisNumber = chassisNumber;}

	public Integer getPurchaseDate() {return purchaseDate;}

	public void setPurchaseDate(Integer purchaseDate) {this.purchaseDate = purchaseDate;}

	public String getTyreNumber() {return tyreNumber;}

	public void setTyreNumber(String tyreNumber) {this.tyreNumber = tyreNumber;}

	public String getManufacturer() {return manufacturer;}

	public void setManufacturer(String manufacturer) {this.manufacturer = manufacturer;}

	public double getMileage() { return mileage;}

	public void setMileage(double mileage) {this.mileage = mileage;}

	public double getBodyCost() {return bodyCost;}

	public void setBodyCost(double bodyCost) { this.bodyCost = bodyCost;}

	public String getMake() { return make;}

	public void setMake(String make) { this.make = make;}

	public String getModel() { return model;}

	public void setModel(String model) { this.model = model;}

	public Integer getYear() { return year;}

	public void setYear(Integer year) { this.year = year;}

	public String getFinancer() { return financer;}

	public void setFinancer(String financer) { this.financer = financer;}

	public double getChassisCost() { return chassisCost;}

	public void setChassisCost(double chassisCost) { this.chassisCost = chassisCost;}

	public String getPucNumber() { return pucNumber;}

	public void setPucNumber(String pucNumber) { this.pucNumber = pucNumber;}
	@Override
	public String toString() {
		return "VehiclePayload{" +
				"instituteId=" + instituteId +
				", vehicleId=" + vehicleId +
				", vehicleNumber='" + vehicleNumber + '\'' +
				", vehicleCode='" + vehicleCode + '\'' +
				", registrationNumber='" + registrationNumber + '\'' +
				", vehicleTypeId=" + vehicleTypeId +
				", capacity=" + capacity +
				", active=" + active +
				", engineNumber='" + engineNumber + '\'' +
				", batteryNumber='" + batteryNumber + '\'' +
				", chassisNumber='" + chassisNumber + '\'' +
				", purchaseDate=" + purchaseDate +
				", tyreNumber='" + tyreNumber + '\'' +
				", manufacturer='" + manufacturer + '\'' +
				", mileage=" + mileage +
				", bodyCost=" + bodyCost +
				", make='" + make + '\'' +
				", model='" + model + '\'' +
				", year=" + year +
				", financer='" + financer + '\'' +
				", chassisCost=" + chassisCost +
				", pucNumber='" + pucNumber + '\'' +
				'}';
	}
}
