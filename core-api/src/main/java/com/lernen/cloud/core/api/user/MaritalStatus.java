/**
 * 
 */
package com.lernen.cloud.core.api.user;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
public enum MaritalStatus {
	
	MARRIED, UNMARRIED, DIVOSED, WIDOWER, WIDOW;
	
	public static MaritalStatus getMaritalStatus(String maritalStatus) {
		if (StringUtils.isBlank(maritalStatus)) {
			return null;
		}
		for (MaritalStatus maritalStatusEnum : MaritalStatus.values()) {
			if (maritalStatusEnum.name().equalsIgnoreCase(maritalStatus)) {
				return maritalStatusEnum;
			}
		}
		return null;
	}

}
