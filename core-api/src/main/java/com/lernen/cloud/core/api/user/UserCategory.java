package com.lernen.cloud.core.api.user;

import org.apache.commons.lang3.StringUtils;

public enum UserCategory {
	GENERAL, OBC, SC, ST, MIN, SBC, BC, OTHER;

	public static UserCategory getCategory(String category) {
		if (StringUtils.isBlank(category)) {
			return null;
		}
		for (UserCategory userCategoryEnum : UserCategory.values()) {
			if (userCategoryEnum.name().equalsIgnoreCase(category)) {
				return userCategoryEnum;
			}
		}
		return null;
	}
}
