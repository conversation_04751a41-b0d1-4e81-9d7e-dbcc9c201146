package com.lernen.cloud.core.api.user;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @created_at 10/10/23 : 23:45
 **/
public class UserLite {

    private UUID uuid;
    private String userInstituteId;
    private int instituteId;
    private String name;
    private UserType userType;
    private UserStatus userStatus;
    private String userName;
    public UserLite(UUID uuid, String userInstituteId, int instituteId, String name, UserType userType, UserStatus userStatus, String userName) {
        this.uuid = uuid;
        this.userInstituteId = userInstituteId;
        this.instituteId = instituteId;
        this.name = name;
        this.userType = userType;
        this.userStatus = userStatus;
        this.userName = userName;
    }

    public UUID getUuid() {
        return uuid;
    }

    public void setUuid(UUID uuid) {
        this.uuid = uuid;
    }

    public String getUserInstituteId() {
        return userInstituteId;
    }

    public void setUserInstituteId(String userInstituteId) {
        this.userInstituteId = userInstituteId;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public UserType getUserType() {
        return userType;
    }

    public void setUserType(UserType userType) {
        this.userType = userType;
    }

    public UserStatus getUserStatus() {
        return userStatus;
    }

    public void setUserStatus(UserStatus userStatus) {
        this.userStatus = userStatus;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }


    public static  UserLite createUserLite(UUID userId, String firstName, String lastName,
                                    String userInstituteId, int instituteId, UserType userType,
                                    UserStatus userStatus, String userName){
        if (userId == null) {
            return null;
        }
        String name = (firstName != null ? firstName : "") + (lastName != null ? " " + lastName : "");

        return new UserLite( userId, userInstituteId, instituteId, name, userType, userStatus, userName);
    }

    @Override
    public String toString() {
        return "UserLite{" +
                "uuid=" + uuid +
                ", userInstituteId='" + userInstituteId + '\'' +
                ", instituteId=" + instituteId +
                ", name='" + name + '\'' +
                ", userType=" + userType +
                ", userStatus=" + userStatus +
                ", userName='" + userName + '\'' +
                '}';
    }
}
