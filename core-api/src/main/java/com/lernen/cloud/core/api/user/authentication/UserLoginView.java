package com.lernen.cloud.core.api.user.authentication;

import com.embrate.cloud.core.api.application.mobile.MobileAppUserMetadata;
import com.embrate.cloud.core.api.application.mobile.UserServiceStatus;
import com.embrate.cloud.core.api.auth.OAuthTokenData;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.InstituteMetaData;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.organisation.Organisation;
import com.lernen.cloud.core.api.permissions.UserPermissions;
import com.lernen.cloud.core.api.user.User;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class UserLoginView {

	private final UserAuthenticationStatus userAuthenticationStatus;

	private final UserServiceStatus userServiceStatus;

	private final User user;

	private final UserPermissions userPermissions;

	private final Organisation organisation;

	private final Map<Integer, InstituteMetaData> instituteMetaDataMap;

	private final MobileAppUserMetadata mobileAppUserMetadata;

	private final OAuthTokenData oauthTokenData;

	private final Map<Integer, List<AcademicSession>> instituteAcademicSessionMap;

	private final Map<Integer, Map<Integer, List<Standard>>> instituteSessionStandardMap;

	private final String moduleRestrictionReason;

	public UserLoginView(UserAuthenticationStatus userAuthenticationStatus, UserServiceStatus userServiceStatus, User user,
						 UserPermissions userPermissions, Organisation organisation, Map<Integer, InstituteMetaData> instituteMetaDataMap,
						 MobileAppUserMetadata mobileAppUserMetadata, OAuthTokenData oauthTokenData, Map<Integer, List<AcademicSession>> instituteAcademicSessionMap,
						 Map<Integer, Map<Integer, List<Standard>>> instituteSessionStandardMap, String moduleRestrictionReason) {
		this.userAuthenticationStatus = userAuthenticationStatus;
		this.userServiceStatus = userServiceStatus;
		this.user = user;
		this.userPermissions = userPermissions;
		this.organisation = organisation;
		this.instituteMetaDataMap = instituteMetaDataMap;
		this.mobileAppUserMetadata = mobileAppUserMetadata;
		this.oauthTokenData = oauthTokenData;
		this.instituteAcademicSessionMap = instituteAcademicSessionMap;
		this.instituteSessionStandardMap = instituteSessionStandardMap;
		this.moduleRestrictionReason = moduleRestrictionReason;
	}

	public static UserLoginView forAuthenticationFailure(UserAuthenticationStatus userAuthenticationStatus){
		return new UserLoginView(userAuthenticationStatus, null, null,null, null, null,null, null, null,  null, null);
	}

	public static UserLoginView forAuthenticationSuccess(UserServiceStatus userServiceStatus, User user, UserPermissions userPermissions, Organisation organisation, Map<Integer, InstituteMetaData> instituteMetaDataMap, MobileAppUserMetadata mobileAppUserMetadata, OAuthTokenData oAuthTokenData, Map<Integer, List<AcademicSession>> instituteAcademicSessionMap, Map<Integer, Map<Integer, List<Standard>>> instituteSessionStandardMap, String moduleRestrictionReason){
		return new UserLoginView(UserAuthenticationStatus.forSuccess(), userServiceStatus, user, userPermissions, organisation, instituteMetaDataMap,mobileAppUserMetadata, oAuthTokenData, instituteAcademicSessionMap, instituteSessionStandardMap, moduleRestrictionReason);
	}

	public UserAuthenticationStatus getUserAuthenticationStatus() {
		return userAuthenticationStatus;
	}

	public UserServiceStatus getUserServiceStatus() {
		return userServiceStatus;
	}

	public User getUser() {
		return user;
	}

	public UserPermissions getUserPermissions() {
		return userPermissions;
	}

	public Organisation getOrganisation() {
		return organisation;
	}

	public Map<Integer, InstituteMetaData> getInstituteMetaDataMap() {
		return instituteMetaDataMap;
	}

	public MobileAppUserMetadata getMobileAppUserMetadata() {
		return mobileAppUserMetadata;
	}

	public OAuthTokenData getOauthTokenData() {
		return oauthTokenData;
	}

	public Map<Integer, List<AcademicSession>> getInstituteAcademicSessionMap() {
		return instituteAcademicSessionMap;
	}

	public Map<Integer, Map<Integer, List<Standard>>> getInstituteSessionStandardMap() {
		return instituteSessionStandardMap;
	}

	public String getModuleRestrictionReason() {
		return moduleRestrictionReason;
	}

	@Override
	public String toString() {
		return "UserLoginView{" +
				"userAuthenticationStatus=" + userAuthenticationStatus +
				", userServiceStatus=" + userServiceStatus +
				", user=" + user +
				", userPermissions=" + userPermissions +
				", organisation=" + organisation +
				", instituteMetaDataMap=" + instituteMetaDataMap +
				", mobileAppUserMetadata=" + mobileAppUserMetadata +
				", oauthTokenData=" + oauthTokenData +
				", instituteAcademicSessionMap=" + instituteAcademicSessionMap +
				", instituteSessionStandardMap=" + instituteSessionStandardMap +
				", moduleRestrictionReason='" + moduleRestrictionReason + '\'' +
				'}';
	}
}
