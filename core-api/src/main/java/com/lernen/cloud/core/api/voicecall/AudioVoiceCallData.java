package com.lernen.cloud.core.api.voicecall;

import com.embrate.cloud.core.api.audio.AudioFileFormat;
import com.embrate.cloud.core.api.audio.AudioFileProperties;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class AudioVoiceCallData {

    private final UUID templateId;

    private final String templateName;

    private final String voiceId;

    private final AudioFileProperties audioFileProperties;

    public AudioVoiceCallData(UUID templateId, String templateName, String voiceId, AudioFileProperties audioFileProperties) {
        this.templateId = templateId;
        this.templateName = templateName;
        this.voiceId = voiceId;
        this.audioFileProperties = audioFileProperties;
    }

    public UUID getTemplateId() {
        return templateId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public String getVoiceId() {
        return voiceId;
    }

    public AudioFileProperties getAudioFileProperties() {
        return audioFileProperties;
    }

    @Override
    public String toString() {
        return "AudioVoiceCallData{" +
                "templateId=" + templateId +
                ", templateName='" + templateName + '\'' +
                ", voiceId='" + voiceId + '\'' +
                ", audioFileProperties=" + audioFileProperties +
                '}';
    }
}
