/**
 * 
 */
package com.lernen.cloud.core.api.voicecall;

import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class VoiceCallAudioTemplateUploadResponse {

	private final boolean success;

	private final String voiceCallTemplateUniqueId;

	private final String errorCode;

	private final String description;

	private final Map<String, Object> metadata;


	public VoiceCallAudioTemplateUploadResponse(boolean success, String voiceCallTemplateUniqueId, String errorCode, String description, Map<String, Object> metadata) {
		this.success = success;
		this.voiceCallTemplateUniqueId = voiceCallTemplateUniqueId;
		this.errorCode = errorCode;
		this.description = description;
		this.metadata = metadata;
	}

	public boolean isSuccess() {
		return success;
	}

	public String getVoiceCallTemplateUniqueId() {
		return voiceCallTemplateUniqueId;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public String getDescription() {
		return description;
	}

	public Map<String, Object> getMetadata() {
		return metadata;
	}

	@Override
	public String toString() {
		return "VoiceCallAudioTemplateUploadResponse{" +
				"success=" + success +
				", voiceCallTemplateUniqueId='" + voiceCallTemplateUniqueId + '\'' +
				", errorCode='" + errorCode + '\'' +
				", description='" + description + '\'' +
				", metadata=" + metadata +
				'}';
	}
}
