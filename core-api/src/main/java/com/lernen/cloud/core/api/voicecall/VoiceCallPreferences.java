/**
 * 
 */
package com.lernen.cloud.core.api.voicecall;

import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import com.lernen.cloud.core.api.common.NameFormat;

/**
 * <AUTHOR>
 *
 */
public class VoiceCallPreferences {

	public static final String VOICE_CALL_SERVICE_PROVIDER = "voice_call_service_provider";
	public static final String BUFFER_VOICE_CALL_COUNT = "buffer_voice_call_count";

	private CommunicationServiceProvider serviceProvider;
	private Integer bufferVoiceCallCount;

	public VoiceCallPreferences() {
	}

	public static String getConfigType() {
		return "voice_call_preferences";
	}

	public CommunicationServiceProvider getServiceProvider() {
		return serviceProvider;
	}

	public void setServiceProvider(CommunicationServiceProvider serviceProvider) {
		this.serviceProvider = serviceProvider;
	}

	public Integer getBufferVoiceCallCount() {
		return bufferVoiceCallCount;
	}

	public void setBufferVoiceCallCount(Integer bufferVoiceCallCount) {
		this.bufferVoiceCallCount = bufferVoiceCallCount;
	}

	@Override
	public String toString() {
		return "VoiceCallPreferences{" +
				"serviceProvider=" + serviceProvider +
				", bufferVoiceCallCount=" + bufferVoiceCallCount +
				'}';
	}
}