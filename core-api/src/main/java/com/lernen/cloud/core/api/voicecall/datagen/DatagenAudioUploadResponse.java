package com.lernen.cloud.core.api.voicecall.datagen;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DatagenAudioUploadResponse {

    @JsonProperty("status")
    private String status;

    @JsonProperty("voiceid")
    private String voiceId;

    @JsonProperty("code")
    private Integer code;

    @JsonProperty("ts")
    private String timestampStr;

    @JsonProperty("desc")
    private String description;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getVoiceId() {
        return voiceId;
    }

    public void setVoiceId(String voiceId) {
        this.voiceId = voiceId;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getTimestampStr() {
        return timestampStr;
    }

    public void setTimestampStr(String timestampStr) {
        this.timestampStr = timestampStr;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "DatagenAudioUploadResponse{" +
                "status='" + status + '\'' +
                ", voiceId='" + voiceId + '\'' +
                ", code='" + code + '\'' +
                ", timestampStr='" + timestampStr + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
