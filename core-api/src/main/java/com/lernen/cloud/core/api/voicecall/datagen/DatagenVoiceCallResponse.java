package com.lernen.cloud.core.api.voicecall.datagen;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DatagenVoiceCallResponse {

    public static final String DATAGEN_VOICE_CALL_RESPONSE = "datagen_voice_call_response";

    @JsonProperty("status")
    private String status;

    @JsonProperty("validcnt")
    private Integer validCount;

    @JsonProperty("campg_id")
    private String campaignId;

    @JsonProperty("reportid")
    private String reportId;

    @JsonProperty("code")
    private Integer code;

    @JsonProperty("ts")
    private String timestampStr;

    @JsonProperty("desc")
    private String description;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getValidCount() {
        return validCount;
    }

    public void setValidCount(Integer validCount) {
        this.validCount = validCount;
    }

    public String getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(String campaignId) {
        this.campaignId = campaignId;
    }

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getTimestampStr() {
        return timestampStr;
    }

    public void setTimestampStr(String timestampStr) {
        this.timestampStr = timestampStr;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "DatagenVoiceCallResponse{" +
                "status='" + status + '\'' +
                ", validCount=" + validCount +
                ", campaignId='" + campaignId + '\'' +
                ", reportId='" + reportId + '\'' +
                ", code='" + code + '\'' +
                ", timestampStr='" + timestampStr + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
