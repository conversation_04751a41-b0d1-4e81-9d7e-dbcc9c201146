package com.lernen.cloud.core.api.voicecall.datagen;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DatagenVoiceReportPayload {

	@JsonProperty("auth")
	private final String authToken;

	@JsonProperty("camp_id")
	private final String campaignId;

	@JsonProperty("reportid")
	private final String reportId;

	@JsonProperty("numbers")
	private final List<String> mobileNumbers;

	public DatagenVoiceReportPayload(String authToken, String campaignId, String reportId, List<String> mobileNumbers) {
		this.authToken = authToken;
		this.campaignId = campaignId;
		this.reportId = reportId;
		this.mobileNumbers = mobileNumbers;
	}

	public String getAuthToken() {
		return authToken;
	}

	public String getCampaignId() {
		return campaignId;
	}

	public String getReportId() {
		return reportId;
	}

	public List<String> getMobileNumbers() {
		return mobileNumbers;
	}

	@Override
	public String toString() {
		return "DatagenVoiceReportPayload{" +
				"authToken='" + authToken + '\'' +
				", campaignId='" + campaignId + '\'' +
				", reportId='" + reportId + '\'' +
				", mobileNumbers=" + mobileNumbers +
				'}';
	}
}
