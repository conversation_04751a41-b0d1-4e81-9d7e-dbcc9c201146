<?xml version="1.0"?>
<project
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
        xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.embrate.cloud</groupId>
        <artifactId>embrate-backend</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <groupId>com.lernen.cloud.core.lib</groupId>
    <artifactId>core-lib</artifactId>
    <packaging>jar</packaging>
    <name>core-lib</name>
    <url>https://www.embrate.com</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.lernen.cloud.core.api</groupId>
            <artifactId>core-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.lernen.cloud.core.utils</groupId>
            <artifactId>core-utils</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.lernen.cloud.dao.tier</groupId>
            <artifactId>dao-tier</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Used to work with the older excel file format - `.xls` -->
        <!-- https://mvnrepository.com/artifact/org.apache.poi/poi -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>

        <!-- Used to work with the newer excel file format - `.xlsx` -->
        <!-- https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-core -->
        <dependency>
            <groupId>com.google.firebase</groupId>
            <artifactId>firebase-admin</artifactId>
        </dependency>

        <dependency>
            <groupId>com.razorpay</groupId>
            <artifactId>razorpay-java</artifactId>
        </dependency>

    </dependencies>
</project>
