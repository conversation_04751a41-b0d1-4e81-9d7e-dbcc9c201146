package com.embrate.cloud.core.lib.attendance.device;

import com.embrate.cloud.core.api.attendance.AttendanceDeviceServiceProviderType;
import com.embrate.cloud.core.api.attendance.device.AttendanceDeviceData;
import com.embrate.cloud.core.api.attendance.device.AttendanceDeviceInstitutes;
import com.embrate.cloud.core.api.attendance.device.AttendanceDeviceStatus;
import com.embrate.cloud.dao.tier.attendance.AttendanceDeviceDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class AttendanceDeviceManager {
    private static final Logger logger = LogManager.getLogger(AttendanceDeviceManager.class);

    private final AttendanceDeviceDao attendanceDeviceDao;

    public AttendanceDeviceManager(AttendanceDeviceDao attendanceDeviceDao) {
        this.attendanceDeviceDao = attendanceDeviceDao;
    }

    public List<AttendanceDeviceData> getDevices(int instituteId, AttendanceDeviceServiceProviderType serviceProviderType, AttendanceDeviceStatus status){
        if(instituteId <= 0 || serviceProviderType == null){
            logger.error("Invalid query for instituteId {}, serviceProviderType {}", instituteId, serviceProviderType);
            return null;
        }
        List<AttendanceDeviceData> attendanceDeviceDataList = attendanceDeviceDao.getDevices(instituteId, serviceProviderType);
        if(CollectionUtils.isEmpty(attendanceDeviceDataList) || status == null){
            return attendanceDeviceDataList;
        }
        List<AttendanceDeviceData> filteredList = new ArrayList<>();
        for(AttendanceDeviceData attendanceDeviceData : attendanceDeviceDataList){
            if(attendanceDeviceData.getStatus() == status){
                filteredList.add(attendanceDeviceData);
            }
        }
        return filteredList;
    }

    public AttendanceDeviceInstitutes getDeviceInstitutes(AttendanceDeviceServiceProviderType serviceProviderType, String authToken ){
        if(serviceProviderType == null || StringUtils.isBlank(authToken)){
            logger.error("Invalid query for authToken or serviceProviderType {}", serviceProviderType);
            return null;
        }
        return attendanceDeviceDao.getDeviceFromToken(serviceProviderType, authToken);
    }

    public UUID addDevice(AttendanceDeviceInstitutes attendanceDeviceInstitutes){
        if(attendanceDeviceInstitutes == null || CollectionUtils.isEmpty(attendanceDeviceInstitutes.getInstitutes()) || attendanceDeviceInstitutes.getDeviceData().getServiceProviderType() == null
                || StringUtils.isBlank( attendanceDeviceInstitutes.getDeviceData().getExtDeviceId()) || StringUtils.isBlank( attendanceDeviceInstitutes.getDeviceData().getName())
                || StringUtils.isBlank( attendanceDeviceInstitutes.getDeviceData().getAuthToken()) || attendanceDeviceInstitutes.getDeviceData().getStatus() == null){
            logger.error("Invalid deviceData add payload for attendanceDeviceInstitutes {}", attendanceDeviceInstitutes);
            return null;
        }
        return attendanceDeviceDao.addDevice(attendanceDeviceInstitutes);
    }

}
