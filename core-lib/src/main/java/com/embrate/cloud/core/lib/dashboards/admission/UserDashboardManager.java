package com.embrate.cloud.core.lib.dashboards.admission;

import com.embrate.cloud.core.api.dashboards.admission.StudentAdmTCCount;
import com.embrate.cloud.core.api.dashboards.admission.StudentAdmissionOrgStats;
import com.embrate.cloud.core.api.dashboards.admission.StudentBirthdayInfo;
import com.embrate.cloud.core.api.dashboards.admission.StudentBirthdayStats;
import com.embrate.cloud.core.api.dashboards.common.InstituteValue;
import com.embrate.cloud.dao.tier.dashboard.admission.UserDashboardDao;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

import static com.embrate.cloud.core.utils.institute.DashboardUtils.*;

/**
 * <AUTHOR>
 **/
public class UserDashboardManager {

	private static final Logger logger = LogManager.getLogger(UserDashboardManager.class);

	private final InstituteManager instituteManager;

	private final UserDashboardDao userDashboardDao;

	public UserDashboardManager(InstituteManager instituteManager, UserDashboardDao userDashboardDao) {
		this.instituteManager = instituteManager;
		this.userDashboardDao = userDashboardDao;
	}

	public StudentAdmissionOrgStats getStudentAdmissionOrgStats(UUID organizationId, String selectedInstituteIds,
																UUID userId, int startDate, int endDate) {

		validatePayload(organizationId, selectedInstituteIds, userId, startDate, endDate);
		List<Integer> institutes = getInstituteIds(selectedInstituteIds);
		List<StudentAdmTCCount> newAdmissionsAndTCCountList = userDashboardDao.getNewAdmissionsAndTCCountByDateRange(institutes, startDate, endDate);
		Map<Integer, String> instituteNameMap = getInstituteBranchNameMap(instituteManager.getInstitutes(institutes));

		List<InstituteValue> newAdmissions = new ArrayList<>();
		List<InstituteValue> tcIssued = new ArrayList<>();
		int totalNewAdmission = 0;
		int totalTCIssued = 0;

		// Create a map to track which institutes have data
		Map<Integer, StudentAdmTCCount> instituteDataMap = new HashMap<>();
		for (StudentAdmTCCount studentAdmTCCount : newAdmissionsAndTCCountList) {
			instituteDataMap.put(studentAdmTCCount.getInstituteId(), studentAdmTCCount);
			totalNewAdmission += studentAdmTCCount.getAdmissionCount();
			totalTCIssued += studentAdmTCCount.getRelieveCount();
		}

		// Create InstituteValue objects for all institutes, with zero values for institutes without data
		for (Integer institute : institutes) {
			StudentAdmTCCount data = instituteDataMap.get(institute);
			if (data != null) {
				newAdmissions.add(new InstituteValue(data.getInstituteId(), instituteNameMap.get(data.getInstituteId()), data.getAdmissionCount()));
				tcIssued.add(new InstituteValue(data.getInstituteId(), instituteNameMap.get(data.getInstituteId()), data.getRelieveCount()));
			} else {
				// Create entries with zero values for institutes without data
				newAdmissions.add(new InstituteValue(institute, instituteNameMap.get(institute), 0));
				tcIssued.add(new InstituteValue(institute, instituteNameMap.get(institute), 0));
			}
		}

		return new StudentAdmissionOrgStats(newAdmissions, totalNewAdmission, tcIssued, totalTCIssued);
	}

	public StudentBirthdayStats getStudentBirthdayStats(UUID organizationId, String selectedInstituteIds, UUID userId, int overrideDate) {
		validateBirthdayPayload(organizationId, selectedInstituteIds, userId);
		List<Integer> institutes = getInstituteIds(selectedInstituteIds);

		// Get current academic sessions for the institutes
		Set<Integer> currentAcademicSessions = new HashSet<>();
		for (Integer instituteId : institutes) {
			AcademicSession currentSession = instituteManager.getCurrentDateSessionDetails(instituteId);
			if (currentSession != null) {
				currentAcademicSessions.add(currentSession.getAcademicSessionId());
			}
		}

		// Get all enrolled students with their current session and class information
		List<StudentBirthdayInfo> allStudents = userDashboardDao.getAllEnrolledStudentsWithCurrentSession(institutes, currentAcademicSessions);

		List<StudentBirthdayInfo> todayBirthdays = new ArrayList<>();
		List<StudentBirthdayInfo> upcomingBirthdays = new ArrayList<>();

		// Get current date information for birthday computation
		int currentDate = overrideDate > 0 ? overrideDate : DateUtils.now();
		int currentYear = DateUtils.getDefaultZoneYear(currentDate);

		// Compute birthday filtering in application code
		for (StudentBirthdayInfo student : allStudents) {
			Integer birthdayDate = student.getBirthdayDate();
			if (birthdayDate == null || birthdayDate <= 0) {
				continue;
			}

			// Calculate this year's birthday
			int birthMonth = DateUtils.getIntMonthOfYear(birthdayDate);
			int birthDay = DateUtils.getIntDayOfMonth(birthdayDate);

			// Create this year's birthday date using the same approach as existing birthday logic
			// EMonthDay constructor takes (day, month)
			int thisYearBirthday = DateUtils.getDefaultZoneTime(new com.lernen.cloud.core.api.common.EMonthDay(birthDay, birthMonth), currentYear);

			// Check if birthday is today
			if (DateUtils.isSameDay(currentDate, thisYearBirthday)) {
				todayBirthdays.add(student);
			}
			// Check if birthday is in the next 7 days
			else {
				int daysBetween = DateUtils.daysBetween(currentDate, thisYearBirthday);
				if (daysBetween > 0 && daysBetween <= 7) {
					upcomingBirthdays.add(student);
				}
				// Handle year-end wrap around (e.g., current date is Dec 28, birthday is Jan 2)
				else if (daysBetween < 0) {
					// Birthday already passed this year, check next year
					int nextYearBirthday = DateUtils.getDefaultZoneTime(new com.lernen.cloud.core.api.common.EMonthDay(birthDay, birthMonth), currentYear + 1);
					int daysToNextYearBirthday = DateUtils.daysBetween(currentDate, nextYearBirthday);
					if (daysToNextYearBirthday > 0 && daysToNextYearBirthday <= 7) {
						upcomingBirthdays.add(student);
					}
				}
			}
		}

		return new StudentBirthdayStats(todayBirthdays, upcomingBirthdays);
	}

	private void validateBirthdayPayload(UUID organizationId, String selectedInstituteIds, UUID userId) {
		if (organizationId == null) {
			throw new RuntimeException("Invalid organization id.");
		}
		if (selectedInstituteIds == null || selectedInstituteIds.trim().isEmpty()) {
			throw new RuntimeException("Invalid institute ids.");
		}
		if (userId == null) {
			throw new RuntimeException("Invalid user id.");
		}
	}
}