package com.embrate.cloud.core.lib.document;

import com.embrate.cloud.core.api.document.*;
import com.google.api.services.drive.model.File;
import com.google.common.base.Joiner;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.document.DocumentManager;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DocumentUtils;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.google.GoogleDriveFileSystem;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 */

public class BulkDocumentManager {
    private static final Logger logger = LogManager.getLogger(BulkDocumentManager.class);

    private final StudentManager studentManager;
    private final StaffManager staffManager;

    public BulkDocumentManager(StudentManager studentManager, StaffManager staffManager) {
        this.studentManager = studentManager;
        this.staffManager = staffManager;
    }

    public BulkDocumentUploadResponse validateStudentProfileImageFile(int instituteId, UserType userType, String imageFolderPath, String profileImageFilePath,
                                                                      String googleDriveAccessToken, boolean header, boolean validationMode,
                                                                      boolean execute, String testAdmissionNumber, double resizeFactor, double rotation) {
        if (instituteId <= 0 || StringUtils.isBlank(imageFolderPath) || StringUtils.isBlank(profileImageFilePath) || StringUtils.isBlank(googleDriveAccessToken)) {
            logger.error("Invalid instituteId {} imageFolderPath {}, profileImageFileName {}, or token", instituteId, imageFolderPath, profileImageFilePath);
            return BulkDocumentUploadResponse.forError("Invalid instituteId, imageFolderPath, profileImageFileName, or token");
        }

        GoogleDriveFileSystem googleDriveFileSystem = new GoogleDriveFileSystem("1040433928877-i2a7lve97t7jdantq78f4kkkjm2csqub.apps.googleusercontent.com",
                "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", "1//04NN9wDBe_di_CgYIARAAGAQSNwF-L9IrMHJv46EuxcQY4VM9eJdVXm-zQkJlsn-0Nuf9qIkNuTDxmCFHN12NaJYOVAWSkaSxQ34", googleDriveAccessToken);

        String[] imageFileTokens = profileImageFilePath.split("/");
        String profileImageFileName = imageFileTokens[imageFileTokens.length - 1];
        String baseFolderPath = Joiner.on("/").join(Arrays.copyOfRange(imageFileTokens, 0, imageFileTokens.length - 1));
        File baseFolder = googleDriveFileSystem.getFolder(baseFolderPath);
        if (baseFolder == null) {
            logger.error("Invalid base folder path {} or credentials", baseFolderPath);
            return BulkDocumentUploadResponse.forError("Invalid base folder path or credentials");
        }
        InputStream fileStream = googleDriveFileSystem.getFile(baseFolder, profileImageFileName);
        if (fileStream == null) {
            logger.error("Invalid file name path {} or credentials", profileImageFileName);
            return BulkDocumentUploadResponse.forError("Invalid file name path or credentials");
        }

        Map<String, String> admissionNumberToImageMap = new HashMap<>();
        List<FileEntryError> failedLines = new ArrayList<>();
        loadProfileImageFile(header, profileImageFileName, fileStream, admissionNumberToImageMap, failedLines);

        if (MapUtils.isEmpty(admissionNumberToImageMap)) {
            logger.error("No entries found in file {}", profileImageFileName);
            return BulkDocumentUploadResponse.forError("No entries found in file");
        }
        List<Student> studentList = new ArrayList<>();
        List<Staff> staffList = new ArrayList<>();
        if (userType == UserType.STUDENT) {
            studentList = studentManager.getAllStudentWithoutSession(instituteId);
            if (CollectionUtils.isEmpty(studentList)) {
                logger.error("No students found for instituteId {}", instituteId);
                return BulkDocumentUploadResponse.forError("No students found for given institute");
            }
        } else if (userType == UserType.STAFF) {
            staffList = staffManager.getStaff(instituteId);
            if (CollectionUtils.isEmpty(staffList)) {
                logger.error("No staff found for instituteId {}", instituteId);
                return BulkDocumentUploadResponse.forError("No staff found for given institute");
            }
        } else {
            logger.error("User type not supported {}", userType);
            return BulkDocumentUploadResponse.forError("User type not supported ");
        }


        File imageFolder = googleDriveFileSystem.getFolder(imageFolderPath);
        if (imageFolder == null) {
            logger.error("Invalid image folder path {} or credentials", imageFolder);
            return BulkDocumentUploadResponse.forError("Invalid image folder path or credentials");
        }

        List<File> fileList = googleDriveFileSystem.getFilesInFolder(imageFolder);
        if (fileList == null) {
            logger.error("Unable to get files in image folder {}", imageFolder);
            return BulkDocumentUploadResponse.forError("Unable to get files in image folder");
        }

        Map<String, Student> admissionNumberStudentMap = new HashMap<>();
        Map<String, Staff> admissionNumberStaffMap = new HashMap<>();
        List<String> notFoundAdmissionNumbers = new ArrayList<>();
        List<String> noImageFoundAdmissionNumbers = new ArrayList<>();
        Map<String, File> fileMap = new HashMap<>();
        List<ImageNotExistEntry> imageNotExistEntryList = new ArrayList<>();
        Map<String, File> studentImageFileMap = new HashMap<>();

        populateValidationResults(userType, admissionNumberToImageMap, studentList, staffList, fileList, admissionNumberStudentMap, admissionNumberStaffMap, notFoundAdmissionNumbers, noImageFoundAdmissionNumbers, fileMap, imageNotExistEntryList, studentImageFileMap);

        if (CollectionUtils.isNotEmpty(notFoundAdmissionNumbers) || CollectionUtils.isNotEmpty(noImageFoundAdmissionNumbers)
                || CollectionUtils.isNotEmpty(failedLines) || CollectionUtils.isNotEmpty(imageNotExistEntryList)) {
            logger.error("Invalid admission number provided in sheet {}, notFoundAdmissionNumbers {}, noImageFoundAdmissionNumbers {}, failedLines {}, imageNotExistEntryList {}",
                    profileImageFilePath, notFoundAdmissionNumbers, noImageFoundAdmissionNumbers, failedLines, imageNotExistEntryList);
            return new BulkDocumentUploadResponse(false, "All entries are not correct. Please fix the file.", new BulkProfileImageUploadValidationResult(instituteId, notFoundAdmissionNumbers, noImageFoundAdmissionNumbers, imageNotExistEntryList, failedLines), null);
        }

        if (validationMode) {
            logger.info("---------In Validation Mode-------");
            return new BulkDocumentUploadResponse(true, "All entries are correct.", new BulkProfileImageUploadValidationResult(instituteId, notFoundAdmissionNumbers, noImageFoundAdmissionNumbers, imageNotExistEntryList, failedLines), null);
        }

        if (execute) {
            logger.info("---------In Execute Mode-------");
            return executeImageUpload(userType, instituteId, testAdmissionNumber, googleDriveFileSystem, fileStream, admissionNumberStudentMap, admissionNumberStaffMap, studentImageFileMap, resizeFactor, rotation);
        }

        return new BulkDocumentUploadResponse(false, "Invalid mode to run", null, null);
    }

    private BulkDocumentUploadResponse executeImageUpload(UserType userType, int instituteId, String testAdmissionNumber,
                                                          GoogleDriveFileSystem googleDriveFileSystem, InputStream fileStream,
                                                          Map<String, Student> admissionNumberStudentMap,
                                                          Map<String, Staff> admissionNumberStaffMap,
                                                          Map<String, File> studentImageFileMap, double resizeFactor, double rotation) {
        int total = 0;
        int success = 0;
        int failure = 0;

        List<String> failureReasonList = new ArrayList<>();
        if (StringUtils.isNotBlank(testAdmissionNumber)) {
            logger.info("Executing in single user mode for {}", testAdmissionNumber);
            total++;
            testAdmissionNumber = testAdmissionNumber.trim().toLowerCase();
            File imageFile = studentImageFileMap.get(testAdmissionNumber.trim().toLowerCase());
            if (imageFile == null) {
                logger.error("No image file found for admission number {}", testAdmissionNumber);
                failure++;
                failureReasonList.add("No image file found for admission number");
            } else {
                Pair<Boolean, String> result = uploadImageForStudent(userType, instituteId, googleDriveFileSystem, fileStream,
                        admissionNumberStudentMap, admissionNumberStaffMap, testAdmissionNumber, imageFile, resizeFactor, rotation);
                if (result.getFirst()) {
                    success++;
                } else {
                    failure++;
                    failureReasonList.add(result.getSecond());
                }
            }
        } else {
            logger.info("Executing for all users {}", studentImageFileMap.size());
            for (Map.Entry<String, File> entry : studentImageFileMap.entrySet()) {
                total++;
                String admissionNumber = entry.getKey();
                File imageFile = entry.getValue();
                Pair<Boolean, String> result = uploadImageForStudent(userType, instituteId, googleDriveFileSystem, fileStream,
                        admissionNumberStudentMap, admissionNumberStaffMap, admissionNumber, imageFile, resizeFactor, rotation);
                if (result.getFirst()) {
                    success++;
                } else {
                    failure++;
                    failureReasonList.add(result.getSecond());
                }
            }
            logger.error("total = {}, success = {}, failure = {}", total, success, failure);
        }

        return new BulkDocumentUploadResponse(success == total, null, null, new BulkDocumentExecutionResult(total, success, failure, failureReasonList));
    }

    private Pair<Boolean, String> uploadImageForStudent(UserType userType, int instituteId, GoogleDriveFileSystem googleDriveFileSystem, InputStream fileStream,
                                                        Map<String, Student> admissionNumberStudentMap, Map<String, Staff> admissionNumberStaffMap, String admissionNumber, File imageFile, double resizeFactor, double rotation) {
        try {
            long start = System.currentTimeMillis();
            logger.info("Uploading image for userType {} admissionNumber {}, imageFile {}", userType, admissionNumber, imageFile.getName());
            Student student = null;
            Staff staff = null;
            if (userType == UserType.STUDENT) {
                student = admissionNumberStudentMap.get(admissionNumber);
                if (student == null) {
                    logger.error("Unable to get student {}", admissionNumber);
                    return new Pair<>(false, "Unable to get student " + admissionNumber);
                }
            } else if (userType == UserType.STAFF) {
                staff = admissionNumberStaffMap.get(admissionNumber);
                if (staff == null) {
                    logger.error("Unable to get staff {}", admissionNumber);
                    return new Pair<>(false, "Unable to get staff " + admissionNumber);
                }
            }

            InputStream imageStream = googleDriveFileSystem.getFile(imageFile);
            String imageName = imageFile.getName();
            if (fileStream == null) {
                logger.error("Unable to get image {}", imageName);
                return new Pair<>(false, "Unable to get image from drive for " + admissionNumber + " , imageFile " + imageFile);
            }

            FileData inputFileData = new FileData(IOUtils.toByteArray(imageStream), imageName);
            final double length = inputFileData.getContent().length / DocumentUtils.ONE_KB;

            FileData compressedFileData = DocumentManager.resizeAndRotateImage(inputFileData, resizeFactor, rotation);

            if (userType == UserType.STUDENT) {
                studentManager.uploadStudentProfileImage(instituteId, "Student Profile Image", compressedFileData, student.getStudentId());
            } else if (userType == UserType.STAFF) {
                staffManager.uploadStaffProfileImage(instituteId, "Staff Profile Image", compressedFileData, staff.getStaffId());
            }

            final double length2 = compressedFileData.getContent().length / DocumentUtils.ONE_KB;

            long now = System.currentTimeMillis();
            double diff = (now - start) / 1000d;
            logger.info("Successfully updated user admissionNumber {}, imageFile {} in {} sec, Original Size {} KB,  Final Size : {} KB", admissionNumber, imageFile.getName(), diff, length, length2);
            return new Pair<>(true, null);
        } catch (Exception e) {
            logger.error("Unable to update user image", e);
            return new Pair<>(false, "Error while uploading image from drive for " + admissionNumber + " , imageFile " + imageFile + " , " + e.getMessage());
        }
    }

    private static void populateValidationResults(UserType userType, Map<String, String> admissionNumberToImageMap, List<Student> studentList, List<Staff> staffList, List<File> fileList,
                                                  Map<String, Student> admissionNumberStudentMap, Map<String, Staff> admissionNumberStaffMap, List<String> notFoundAdmissionNumbers, List<String> noImageFoundAdmissionNumbers, Map<String, File> fileMap, List<ImageNotExistEntry> imageNotExistEntryList, Map<String, File> studentImageFileMap) {
        for (Student student : studentList) {
            admissionNumberStudentMap.put(student.getStudentBasicInfo().getAdmissionNumber().trim().toLowerCase(), student);
        }

        for (Staff staff : staffList) {
            admissionNumberStaffMap.put(staff.getStaffBasicInfo().getStaffInstituteId().trim().toLowerCase(), staff);
        }

        Map<String, String> imageFileNameAdmissionNumberMap = new HashMap<>();
        for (Map.Entry<String, String> entry : admissionNumberToImageMap.entrySet()) {
            if (userType == UserType.STUDENT && !admissionNumberStudentMap.containsKey(entry.getKey())) {
                notFoundAdmissionNumbers.add(entry.getKey());
            }
            if (userType == UserType.STAFF && !admissionNumberStaffMap.containsKey(entry.getKey())) {
                notFoundAdmissionNumbers.add(entry.getKey());
            }

            if (StringUtils.isBlank(entry.getValue())) {
                noImageFoundAdmissionNumbers.add(entry.getKey());
            } else {
                imageFileNameAdmissionNumberMap.put(entry.getValue(), entry.getKey());
            }
        }

        for (File file : fileList) {
            fileMap.put(file.getName().trim().toLowerCase(), file);
        }

        for (Map.Entry<String, String> entry : imageFileNameAdmissionNumberMap.entrySet()) {
            String fileName = entry.getKey().trim().toLowerCase();
            if (!fileMap.containsKey(fileName)) {
                imageNotExistEntryList.add(new ImageNotExistEntry(entry.getValue(), entry.getKey()));
            } else {
                studentImageFileMap.put(entry.getValue(), fileMap.get(fileName));
            }
        }
    }

    private static void loadProfileImageFile(boolean header, String profileImageFileName, InputStream fileStream,
                                             Map<String, String> admissionNumberToImageMap, List<FileEntryError> failedLines) {
        try (BufferedReader br = new BufferedReader(new InputStreamReader(fileStream, StandardCharsets.UTF_8))) {
            // read line by line
            String line;
            int lineNumber = 0;
            while ((line = br.readLine()) != null) {
                lineNumber++;
                if (header) {
                    header = false;
                    continue;
                }
                String[] tokens = line.split(",", -1);
                if (tokens.length < 2) {
                    logger.error("Invalid line {}", line);
                    failedLines.add(new FileEntryError(lineNumber, line, "Less than 2 columns"));
                    continue;
                }
                String admissionNumber = tokens[0].trim().toLowerCase();
                String imageName = tokens[1].trim().toLowerCase();
                if (StringUtils.isBlank(admissionNumber)) {
                    failedLines.add(new FileEntryError(lineNumber, line, "Invalid Admission Number"));
                    continue;
                }
                admissionNumberToImageMap.put(admissionNumber, imageName);
            }
        } catch (Exception e) {
            logger.error("Unable to read file {}", profileImageFileName, e);
            throw new EmbrateRunTimeException("Unable to read file", e);
        }
    }

    public static void main(String[] args) {
        final ApplicationContext context = new ClassPathXmlApplicationContext("core-lib.xml");

        final StudentManager studentManager = context.getBean(StudentManager.class);
        final StaffManager staffManager = context.getBean(StaffManager.class);
        BulkDocumentManager bulkDocumentManager = new BulkDocumentManager(studentManager, staffManager);
        BulkDocumentUploadResponse bulkDocumentUploadResponse =
                bulkDocumentManager.validateStudentProfileImageFile(10180, UserType.STUDENT,"Nosegay ID Cards 2023-24/Images",
                        "Nosegay ID Cards 2023-24/10180_profle_image.csv", "*********************************************************************************************************************************************************************************************************************", false, false, true, null, 0.2, -90);
        System.out.println(SharedConstants.GSON.toJson(bulkDocumentUploadResponse));
    }
}
