/**
 * 
 */
package com.embrate.cloud.core.lib.examination.marks.computation;

import com.lernen.cloud.core.api.examination.MarksComputationOperation;

/**
 * <AUTHOR>
 *
 */
public class MarksComputationFactory {
	
	private SumComputator sumComputator;
	
	private BestOfAllComputator bestOfAllComputator;

	private SumOfBestNComputator sumOfBestNComputator;
	
	public MarksComputationFactory(SumComputator sumComputator,
			BestOfAllComputator bestOfAllComputator, SumOfBestNComputator sumOfBestNComputator) {
		this.sumComputator = sumComputator;
		this.bestOfAllComputator = bestOfAllComputator;
		this.sumOfBestNComputator = sumOfBestNComputator;
	}

	public IMarksComputator getMarksComputator(MarksComputationOperation operation) {
		switch (operation) {
		case BEST_OF_ALL:
			return bestOfAllComputator;
		case SUM_OF_BEST_N:
			return sumOfBestNComputator;
		default:
			return sumComputator;
		}
	}

}
