/**
 * 
 */
package com.embrate.cloud.core.lib.examination.marks.computation;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.*;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.examination.ExamMarksUtils;

/**
 * <AUTHOR>
 *
 */
public class SumComputator implements IMarksComputator {

	@Override
	public Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> compute(List<List<StudentExamMarksDetails>> childrenStudentExamMarksDetails,
			List<StudentExamMarksDetails> parentStudentExamMarksDetails,
			Map<CourseType, List<ExamGrade>> courseTypeExamGrades) {
		
		return combine(childrenStudentExamMarksDetails, parentStudentExamMarksDetails, courseTypeExamGrades);
		
	}

	private Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> combine(List<List<StudentExamMarksDetails>> childrenStudentExamMarksDetails,
			List<StudentExamMarksDetails> parentStudentExamMarksDetails,
			Map<CourseType, List<ExamGrade>> courseTypeExamGrades) {
	
		final Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> studentCourseDimensionObtainedValues = new HashMap<>();
		final Map<UUID, Student> studentsMap = new HashMap<>();
		final Map<UUID, Course> courseMap = new HashMap<>();

		for (final List<StudentExamMarksDetails> childStudentExamMarksDetails : childrenStudentExamMarksDetails) {
			for (final StudentExamMarksDetails studentExamMarksDetails : childStudentExamMarksDetails) {
				final UUID studentId = studentExamMarksDetails.getStudent().getStudentId();
				studentsMap.put(studentId, studentExamMarksDetails.getStudent());
				if (!studentCourseDimensionObtainedValues.containsKey(studentId)) {
					studentCourseDimensionObtainedValues.put(studentId, new HashMap<>());
				}
				for (final ExamCourseMarks examCourseMarks : studentExamMarksDetails
						.getExamCoursesAllDimensionsMarks()) {
					final UUID courseId = examCourseMarks.getCourse().getCourseId();
					courseMap.put(courseId, examCourseMarks.getCourse());
					if (!studentCourseDimensionObtainedValues.get(studentId).containsKey(courseId)) {
						studentCourseDimensionObtainedValues.get(studentId).put(courseId, new HashMap<>());
					}

					for (final ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
							.getExamDimensionObtainedValues()) {
						ExamDimensionObtainedValues examDimensionObtainedValuesNew = new ExamDimensionObtainedValues(
								examDimensionObtainedValues.getExamDimension(),
								examDimensionObtainedValues.getObtainedMarks(), examDimensionObtainedValues.getGraceMarks(),
								examDimensionObtainedValues.getObtainedGrade(), examDimensionObtainedValues.getAttendanceStatus(),
								examDimensionObtainedValues.getOriginalMaxMarks(), examDimensionObtainedValues.getMaxMarks(),
								examDimensionObtainedValues.getMinMarks(), examDimensionObtainedValues.getMaxGrade(),
								examDimensionObtainedValues.getMinGrade(), examDimensionObtainedValues.getExamCoursePublishedStatus());
						final Integer dimensionId = examDimensionObtainedValuesNew.getExamDimension().getDimensionId();
						if (!studentCourseDimensionObtainedValues.get(studentId).get(courseId)
								.containsKey(dimensionId)) {
							/**
							 * Counting number of times grades were summed up
							 * across child exams in dimension to compute
							 * average and get final grade
							 */
							if (examDimensionObtainedValuesNew.getObtainedGrade() != null && examDimensionObtainedValuesNew.getObtainedGrade().getGradeId() != null) {
								final ExamGrade maxGrade = new ExamGrade();
								maxGrade.setGradeValue(1.0);
								examDimensionObtainedValuesNew.setMaxGrade(maxGrade);
							}


							studentCourseDimensionObtainedValues.get(studentId).get(courseId).put(dimensionId,
									examDimensionObtainedValuesNew);
							continue;
						}

						final ExamDimensionObtainedValues sumExamDimensionObtainedValues = studentCourseDimensionObtainedValues
								.get(studentId).get(courseId).get(dimensionId);

						final Double totalObtainedMarks = ExamMarksUtils.addValues(
								sumExamDimensionObtainedValues.getObtainedMarks(),
								examDimensionObtainedValuesNew.getObtainedMarks());
						
						final Double totalOriginalMaxMarks = ExamMarksUtils.addValues(
								sumExamDimensionObtainedValues.getOriginalMaxMarks(),
								examDimensionObtainedValuesNew.getOriginalMaxMarks());

						final Double totalMaxMarks = ExamMarksUtils.addValues(
								sumExamDimensionObtainedValues.getMaxMarks(),
								examDimensionObtainedValuesNew.getMaxMarks());

						final ExamAttendanceStatus netAttendanceStatus = ExamAttendanceStatus.getNetExamAttendanceStatus(
								sumExamDimensionObtainedValues.getAttendanceStatus(), examDimensionObtainedValuesNew.getAttendanceStatus());

						/**
						 * Summing up grade values across child exams in
						 * dimension
						 */
						final Double totalObtainedGradeValueSum = ExamMarksUtils.addValues(
								sumExamDimensionObtainedValues.getObtainedGrade() == null ? null
										: sumExamDimensionObtainedValues.getObtainedGrade().getGradeValue(),
								examDimensionObtainedValuesNew.getObtainedGrade() == null ? null
										: examDimensionObtainedValuesNew.getObtainedGrade().getGradeValue());

						/**
						 * Counting number of times grades were summed up across
						 * child exams in dimension to compute average and get
						 * final grade
						 */
						final Double totalGradeValueCount = ExamMarksUtils.addValues(
								sumExamDimensionObtainedValues.getMaxGrade() == null ? null
										: sumExamDimensionObtainedValues.getMaxGrade().getGradeValue(),
								examDimensionObtainedValuesNew.getObtainedGrade() == null ||
										examDimensionObtainedValuesNew.getObtainedGrade().getGradeId() == null ? null : 1.0);
						

						ExamGrade obtainedGrade = null;
						if (totalObtainedGradeValueSum != null) {
							obtainedGrade = new ExamGrade();
							obtainedGrade.setGradeValue(totalObtainedGradeValueSum);
						}

						ExamGrade maxGrade = null;
						if (totalGradeValueCount != null) {
							maxGrade = new ExamGrade();
							maxGrade.setGradeValue(totalGradeValueCount);
						}

						studentCourseDimensionObtainedValues.get(studentId).get(courseId).put(dimensionId,
								new ExamDimensionObtainedValues(sumExamDimensionObtainedValues.getExamDimension(),
										totalObtainedMarks, null, obtainedGrade, netAttendanceStatus, totalOriginalMaxMarks, totalMaxMarks, null, maxGrade, null,
										sumExamDimensionObtainedValues.getExamCoursePublishedStatus()));
					}
				}
			}
		}

		return studentCourseDimensionObtainedValues;
	}
}
