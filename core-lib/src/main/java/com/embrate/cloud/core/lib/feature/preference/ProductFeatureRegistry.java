package com.embrate.cloud.core.lib.feature.preference;

import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceEntity;
import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceGroup;
import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.lib.feature.preference.handler.GeneralProductFeatureHandler;
import com.embrate.cloud.core.lib.feature.preference.handler.ProductFeatureHandler;

import java.util.*;

/**
 * <AUTHOR>
 */

public class ProductFeatureRegistry {

    private final List<ProductFeatureHandler> productFeatureHandlers;

    private static final Map<String, ProductFeature> PRODUCT_FEATURE_MAP = new LinkedHashMap<>();
    private static final Map<String, Map<String, FeaturePreferenceGroup>> FEATURE_PREFERENCE_GROUP_MAP = new HashMap<>();
    private static final Map<String, Map<String, Map<String, FeaturePreferenceEntity>>> FEATURE_PREFERENCE_ENTITY_MAP = new HashMap<>();

    public ProductFeatureRegistry(List<ProductFeatureHandler> productFeatureHandlers) {
        this.productFeatureHandlers = productFeatureHandlers;
        init();
    }

    private void addFeature(ProductFeature productFeature) {
        if (PRODUCT_FEATURE_MAP.containsKey(productFeature.getId())) {
            throw new IllegalStateException("Duplicate feature registration");
        }
        PRODUCT_FEATURE_MAP.put(productFeature.getId(), productFeature);
        FEATURE_PREFERENCE_GROUP_MAP.put(productFeature.getId(), new HashMap<>());
        FEATURE_PREFERENCE_ENTITY_MAP.put(productFeature.getId(), new HashMap<>());

        for(FeaturePreferenceGroup featurePreferenceGroup : productFeature.getFeaturePreferenceGroups()){
            FEATURE_PREFERENCE_GROUP_MAP.get(productFeature.getId()).put(featurePreferenceGroup.getId(), featurePreferenceGroup);
            FEATURE_PREFERENCE_ENTITY_MAP.get(productFeature.getId()).put(featurePreferenceGroup.getId(), new HashMap<>());
            for(FeaturePreferenceEntity featurePreferenceEntity : featurePreferenceGroup.getFeaturePreferenceEntities()){
                FEATURE_PREFERENCE_ENTITY_MAP.get(productFeature.getId()).get(featurePreferenceGroup.getId()).put(featurePreferenceEntity.getId(), featurePreferenceEntity);
            }
        }
    }

    public List<ProductFeature> getAllProductFeatures() {
        return new ArrayList<>(PRODUCT_FEATURE_MAP.values());
    }

    public ProductFeature getProductFeature(String featureId) {
        return PRODUCT_FEATURE_MAP.get(featureId);
    }

    public FeaturePreferenceGroup getFeaturePreferenceGroup(String featureId, String preferenceGroupId) {
        if(!FEATURE_PREFERENCE_GROUP_MAP.containsKey(featureId)){
            return null;
        }
        return FEATURE_PREFERENCE_GROUP_MAP.get(featureId).get(preferenceGroupId);
    }

    public FeaturePreferenceEntity getFeaturePreferenceEntity(String featureId, String preferenceGroupId, String preferenceId) {
        if(!FEATURE_PREFERENCE_ENTITY_MAP.containsKey(featureId)){
            return null;
        }
        Map<String, Map<String, FeaturePreferenceEntity>> featurePreferenceEntityMap = FEATURE_PREFERENCE_ENTITY_MAP.get(featureId);
        if(!featurePreferenceEntityMap.containsKey(preferenceGroupId)){
            return null;
        }
        return featurePreferenceEntityMap.get(preferenceGroupId).get(preferenceId);
    }


    private void init() {
        for(ProductFeatureHandler productFeatureHandler : productFeatureHandlers){
            addFeature(productFeatureHandler.buildProductFeature());
        }
    }
}
