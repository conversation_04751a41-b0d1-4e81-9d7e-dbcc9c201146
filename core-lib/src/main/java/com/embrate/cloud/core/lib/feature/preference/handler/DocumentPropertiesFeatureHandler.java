package com.embrate.cloud.core.lib.feature.preference.handler;

import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceEntity;
import com.embrate.cloud.core.api.feature.preference.PreferenceDataType;
import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.lib.feature.preference.IFeaturePreferenceGroupBuilder;
import com.lernen.cloud.core.api.configurations.DocumentPropertiesPreferences;
import com.lernen.cloud.core.api.configurations.StudentIdentityCardPreferences;
import com.lernen.cloud.core.lib.institute.InstituteManager;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DocumentPropertiesFeatureHandler extends ProductFeatureHandler {

    private final InstituteManager instituteManager;

    public DocumentPropertiesFeatureHandler(InstituteManager instituteManager) {
        this.instituteManager = instituteManager;
    }

    private ProductFeature productFeature = null;

    @Override
    public ProductFeature buildProductFeature() {
        productFeature = new ProductFeature("com.embrate.feature.documentProperties", "Document Properties Preferences", "Document Properties Preferences");
        addFeaturePreferenceGroup(productFeature, getBasicPreferences());
        return productFeature;
    }

    @Override
    public ProductFeature getProductFeature() {
        return productFeature;
    }

    private IFeaturePreferenceGroupBuilder getBasicPreferences() {
        return new IFeaturePreferenceGroupBuilder() {
            @Override
            public String getGroupId() {
                return "group.preference.basic";
            }

            @Override
            public String getGroupName() {
                return "Basic Preferences";
            }

            @Override
            public String getGroupDescription() {
                return "Basic Preferences";
            }

            @Override
            public List<FeaturePreferenceEntity> getPreferences() {
                List<FeaturePreferenceEntity> featurePreferenceEntities = new LinkedList<>();

                //Header Font Size
                featurePreferenceEntities.add(new FeaturePreferenceEntity("institute_name_font_size", "Institute Name Font Size", "Font Size of institute name in float e.g - 12f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.INSTITUTE_NAME_FONT_SIZE));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("letter_head_1_font_size", "Letter Head 1 Font Size", "Font Size of leter head 1 in float e.g - 12f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.LETTER_HEAD_1_FONT_SIZE));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("letter_head_2_font_size", "Letter Head 2 Font Size", "Font Size of leter head 2 in float e.g - 12f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.LETTER_HEAD_2_FONT_SIZE));

                //Header Font Color
                featurePreferenceEntities.add(new FeaturePreferenceEntity("institute_name_color", "Institute Name Color", "Color of institute name in float e.g - #f9f9f9", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.INSTITUTE_NAME_COLOR));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("letter_head_1_color", "Letter head 1 Color", "Font Size of letter head 1 in float e.g - #f9f9f9", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.LETTER_HEAD_1_COLOR));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("letter_head_2_color", "Letter head 2 Color", "Font Size of letter head 2 in float e.g - #f9f9f9", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.LETTER_HEAD_2_COLOR));

                //Logos & Student Images Size
                featurePreferenceEntities.add(new FeaturePreferenceEntity("primary_logo_width", "Primary Logo Width", "Width of primary logo in float e.g - 75f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.PRIMARY_LOGO_WIDTH));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("primary_logo_height", "Primary Logo Height", "Height of primary logo in float e.g - 75f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.PRIMARY_LOGO_HEIGHT));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("primary_logo_xoffset", "Primary Logo XOffset", "XOffset of primary logo in float e.g - 30f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.PRIMARY_LOGO_XOFFSET));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("primary_logo_yoffset", "Primary Logo YOffset", "YOffset of primary logo in float e.g - 150f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.PRIMARY_LOGO_YOFFSET));

                featurePreferenceEntities.add(new FeaturePreferenceEntity("secondary_logo_width", "Secondary Logo Width", "Width of secondary logo in float e.g - 75f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.SECONDARY_LOGO_WIDTH));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("secondary_logo_height", "Secondary Logo Height", "Height of secondary logo in float e.g - 75f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.SECONDARY_LOGO_HEIGHT));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("secondary_logo_xoffset", "Secondary Logo XOffset", "XOffset of secondary logo in float e.g - 30f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.SECONDARY_LOGO_XOFFSET));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("secondary_logo_yoffset", "Secondary Logo YOffset", "YOffset of secondary logo in float e.g - 150f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.SECONDARY_LOGO_YOFFSET));

                featurePreferenceEntities.add(new FeaturePreferenceEntity("watermark_logo_width", "Watermark Logo Width", "Width of watermark logo in float e.g - 75f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.WATERMARK_LOGO_WIDTH));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("watermark_logo_height", "Watermark Logo Height", "Height of watermark logo in float e.g - 75f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.WATERMARK_LOGO_HEIGHT));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("watermark_logo_xoffset", "Watermark Logo XOffset", "XOffset of watermark logo in float e.g - 30f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.WATERMARK_LOGO_XOFFSET));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("watermark_logo_yoffset", "Watermark Logo YOffset", "YOffset of watermark logo in float e.g - 150f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.WATERMARK_LOGO_YOFFSET));

                featurePreferenceEntities.add(new FeaturePreferenceEntity("student_image_width", "Student Image Width", "Width of student image in float e.g - 75f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.STUDENT_IMAGE_WIDTH));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("student_image_height", "Student Image Height", "Height of student image in float e.g - 75f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.STUDENT_IMAGE_HEIGHT));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("student_image_xoffset", "Student Image XOffset", "XOffset of student image in float e.g - 30f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.STUDENT_IMAGE_XOFFSET));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("student_image_yoffset", "Student Image YOffset", "YOffset of student image in float e.g - 150f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.STUDENT_IMAGE_YOFFSET));

                //Document content & heading
                featurePreferenceEntities.add(new FeaturePreferenceEntity("document_heading_font_size", "Document Heading Font Size", "Font Size of document heading in float e.g - 12f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.DOCUMENT_HEADING_FONT_SIZE));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("document_content_font_size", "Document Content Font Size", "Font Size of document content in float e.g - 12f", PreferenceDataType.STRING, DocumentPropertiesPreferences.getConfigType(), DocumentPropertiesPreferences.DOCUMENT_CONTENT_FONT_SIZE));

                return featurePreferenceEntities;
            }
        };
    }
}
