package com.embrate.cloud.core.lib.feature.preference.handler;

import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceEntity;
import com.embrate.cloud.core.api.feature.preference.PreferenceDataType;
import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.lib.feature.preference.IFeaturePreferenceGroupBuilder;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.common.SessionDisplayFormat;
import com.lernen.cloud.core.api.configurations.ExamAdmitCardPreferences;
import com.lernen.cloud.core.api.inventory.InventoryPreferences;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.utils.feature.preference.ProductFeatureUtils;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class ExamAdmitCardFeatureHandler extends ProductFeatureHandler {

    private final InstituteManager instituteManager;

    public ExamAdmitCardFeatureHandler(InstituteManager instituteManager) {
        this.instituteManager = instituteManager;
    }

    private ProductFeature examAdmitCardProductFeature = null;

    @Override
    public ProductFeature buildProductFeature() {
        examAdmitCardProductFeature = new ProductFeature("com.embrate.feature.exam.admitcard", "Exam Admit Card Preferences", "Exam Admit Card Preferences");
        addFeaturePreferenceGroup(examAdmitCardProductFeature, getBasicPreferences());
        return examAdmitCardProductFeature;
    }

    @Override
    public ProductFeature getProductFeature() {
        return examAdmitCardProductFeature;
    }

    private IFeaturePreferenceGroupBuilder getBasicPreferences() {
        return new IFeaturePreferenceGroupBuilder() {
            @Override
            public String getGroupId() {
                return "group.preference.basic";
            }

            @Override
            public String getGroupName() {
                return "Basic Preferences";
            }

            @Override
            public String getGroupDescription() {
                return "Basic Preferences";
            }

            @Override
            public List<FeaturePreferenceEntity> getPreferences() {
                List<FeaturePreferenceEntity> featurePreferenceEntities = new LinkedList<>();
                featurePreferenceEntities.add(new FeaturePreferenceEntity("items_per_row", "items_per_row", "items_per_row", PreferenceDataType.INT, ExamAdmitCardPreferences.getConfigType(), ExamAdmitCardPreferences.ITEMS_PER_ROW));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("rows_per_page", "rows_per_page", "rows_per_page", PreferenceDataType.INT, ExamAdmitCardPreferences.getConfigType(), ExamAdmitCardPreferences.ROWS_PER_PAGE));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("title_text", "title_text", "title_text", PreferenceDataType.STRING, ExamAdmitCardPreferences.getConfigType(), ExamAdmitCardPreferences.TITLE_TEXT));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("bold_exam_name", "bold_exam_name", "bold_exam_name", PreferenceDataType.BOOLEAN, ExamAdmitCardPreferences.getConfigType(), ExamAdmitCardPreferences.BOLD_EXAM_NAME));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("session_display_format", "session_display_format", "session_display_format", PreferenceDataType.ENUM, ExamAdmitCardPreferences.getConfigType(), ExamAdmitCardPreferences.SESSION_DISPLAY_FORMAT, ProductFeatureUtils.getEnumValues(SessionDisplayFormat.values())));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("admission_number_key_text", "admission_number_key_text", "admission_number_key_text", PreferenceDataType.STRING, ExamAdmitCardPreferences.getConfigType(), ExamAdmitCardPreferences.ADMISSION_NUMBER_KEY_TEXT));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("left_signature_designation_text", "left_signature_designation_text", "left_signature_designation_text", PreferenceDataType.STRING, ExamAdmitCardPreferences.getConfigType(), ExamAdmitCardPreferences.LEFT_SIGNATURE_DESIGNATION_TEXT));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("signature_designation_text", "signature_designation_text", "signature_designation_text", PreferenceDataType.STRING, ExamAdmitCardPreferences.getConfigType(), ExamAdmitCardPreferences.SIGNATURE_DESIGNATION_TEXT));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("include_class_roll_number", "include_class_roll_number", "include_class_roll_number", PreferenceDataType.BOOLEAN, ExamAdmitCardPreferences.getConfigType(), ExamAdmitCardPreferences.INCLUDE_CLASS_ROLL_NUMBER));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("header_section_table_widths", "header_section_table_widths", "header_section_table_widths", PreferenceDataType.STRING, ExamAdmitCardPreferences.getConfigType(), ExamAdmitCardPreferences.HEADER_SECTION_TABLE_WIDTHS));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("include_dimension_in_admit_card", "include_dimension_in_admit_card", "include_dimension_in_admit_card", PreferenceDataType.BOOLEAN, ExamAdmitCardPreferences.getConfigType(), ExamAdmitCardPreferences.INCLUDE_DIMENSION_IN_ADMIT_CARD));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("logo_width", "logo_width", "logo_width", PreferenceDataType.STRING, ExamAdmitCardPreferences.getConfigType(), ExamAdmitCardPreferences.LOGO_WIDTH));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("logo_height", "logo_height", "logo_height", PreferenceDataType.STRING, ExamAdmitCardPreferences.getConfigType(), ExamAdmitCardPreferences.LOGO_HEIGHT));

                return featurePreferenceEntities;
            }
        };
    }
}
