package com.embrate.cloud.core.lib.feature.preference.handler;

import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceEntity;
import com.embrate.cloud.core.api.feature.preference.PreferenceDataType;
import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.lib.feature.preference.IFeaturePreferenceGroupBuilder;
import com.embrate.cloud.core.lib.feature.preference.InstituteFeaturePreferenceExecutor;
import com.lernen.cloud.core.api.common.CounterType;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.institute.CounterData;
import com.lernen.cloud.core.lib.institute.InstituteManager;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class GeneralProductFeatureHandler extends ProductFeatureHandler {

    private final InstituteManager instituteManager;

    public GeneralProductFeatureHandler(InstituteManager instituteManager) {
        this.instituteManager = instituteManager;
    }

    private ProductFeature generalProductFeature = null;

    @Override
    public ProductFeature buildProductFeature() {
        generalProductFeature = new ProductFeature("com.embrate.feature.general", "Basic Preferences", "General Preferences");
        addFeaturePreferenceGroup(generalProductFeature, getCounterPreferences());
        addFeaturePreferenceGroup(generalProductFeature, getPermissionPreferences());
        return generalProductFeature;
    }

    @Override
    public ProductFeature getProductFeature() {
        return generalProductFeature;
    }

    private IFeaturePreferenceGroupBuilder getCounterPreferences() {
        return new IFeaturePreferenceGroupBuilder() {
            @Override
            public String getGroupId() {
                return "group.preference.counter";
            }

            @Override
            public String getGroupName() {
                return "Counters";
            }

            @Override
            public String getGroupDescription() {
                return "Counter preferences";
            }

            @Override
            public List<FeaturePreferenceEntity> getPreferences() {
                List<FeaturePreferenceEntity> featurePreferenceEntities = new LinkedList<>();
                featurePreferenceEntities.add(new FeaturePreferenceEntity("registration_counter", "Registration Counter", "Registration Counter", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.REGISTRATION_COUNTER));
                featurePreferenceEntities.add(new FeaturePreferenceEntity(new InstituteFeaturePreferenceExecutor() {
                    @Override
                    public String read(int instituteId) {
                        CounterData counterData = instituteManager.getCounter(instituteId, CounterType.REGISTRATION_NUMBER, false);
                        return counterData == null ? null : counterData.getCounterPrefix();
                    }

                    @Override
                    public boolean update(int instituteId, String value) {
                        return instituteManager.updateCounterPrefix(instituteId, CounterType.REGISTRATION_NUMBER, value);
                    }
                }, "registration_counter_prefix", "Registration Counter Prefix", "Registration Counter Prefix", PreferenceDataType.STRING));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("admission_counter", "Admission Counter", "Admission Counter", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.ADMISSION_COUNTER));
                featurePreferenceEntities.add(new FeaturePreferenceEntity(new InstituteFeaturePreferenceExecutor() {

                    @Override
                    public String read(int instituteId) {
                        CounterData counterData = instituteManager.getCounter(instituteId, CounterType.ADMISSION_NUMBER, false);
                        return counterData == null ? null : counterData.getCounterPrefix();
                    }

                    @Override
                    public boolean update(int instituteId, String value) {
                        return instituteManager.updateCounterPrefix(instituteId, CounterType.ADMISSION_NUMBER, value);
                    }
                }, "admission_counter_prefix", "Admission Counter Prefix", "Admission Counter Prefix", PreferenceDataType.STRING));

                featurePreferenceEntities.add(new FeaturePreferenceEntity("staff_counter", "staff_counter", "staff_counter", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.STAFF_COUNTER));
                featurePreferenceEntities.add(new FeaturePreferenceEntity(new InstituteFeaturePreferenceExecutor() {

                    @Override
                    public String read(int instituteId) {
                        CounterData counterData =  instituteManager.getCounter(instituteId, CounterType.STAFF_NUMBER, false);
                        return counterData == null ? null : counterData.getCounterPrefix();
                    }

                    @Override
                    public boolean update(int instituteId, String value) {
                        return instituteManager.updateCounterPrefix(instituteId, CounterType.STAFF_NUMBER, value);
                    }
                }, "staff_counter_prefix", "Staff Counter Prefix", "Staff Counter Prefix", PreferenceDataType.STRING));


                featurePreferenceEntities.add(new FeaturePreferenceEntity("enable_permission", "Enable Permission", "Enable Permission", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.ENABLE_PERMISSION));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("audit_log_enabled", "Audit Log Enabled", "Audit Log Enabled", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.AUDIT_LOG_ENABLED));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("create_student_user", "Create Student User", "Create Student User", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.CREATE_STUDENT_USER));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("create_staff_user", "Create Staff User", "Create Staff User", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.CREATE_STAFF_USER));

                featurePreferenceEntities.add(new FeaturePreferenceEntity("send_sms_on_student_user_creation", "send_sms_on_student_user_creation", "send_sms_on_student_user_creation", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.SEND_SMS_ON_STUDENT_USER_CREATION));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("username_length", "username_length", "username_length", PreferenceDataType.INT, MetaDataPreferences.getConfigType(), MetaDataPreferences.USERNAME_LENGTH));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("user_password_length", "user_password_length", "user_password_length", PreferenceDataType.INT, MetaDataPreferences.getConfigType(), MetaDataPreferences.USER_PASSWORD_LENGTH));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("institute_unique_code", "institute_unique_code", "institute_unique_code", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.INSTITUTE_UNIQUE_CODE));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("allowed_working_hours_range", "allowed_working_hours_range", "allowed_working_hours_range", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.ALLOWED_WORKING_HOURS_RANGE));
                return featurePreferenceEntities;
            }
        };
    }

    private IFeaturePreferenceGroupBuilder getPermissionPreferences() {
        return new IFeaturePreferenceGroupBuilder() {
            @Override
            public String getGroupId() {
                return "group.preference.permission";
            }

            @Override
            public String getGroupName() {
                return "Permissions";
            }

            @Override
            public String getGroupDescription() {
                return "Permission Preferences";
            }

            @Override
            public List<FeaturePreferenceEntity> getPreferences() {
                List<FeaturePreferenceEntity> featurePreferenceEntities = new LinkedList<>();

                featurePreferenceEntities.add(new FeaturePreferenceEntity("enable_permission", "Enable Permission", "Enable Permission", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.ENABLE_PERMISSION));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("audit_log_enabled", "Audit Log Enabled", "Audit Log Enabled", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.AUDIT_LOG_ENABLED));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("create_student_user", "Create Student User", "Create Student User", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.CREATE_STUDENT_USER));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("create_staff_user", "Create Staff User", "Create Staff User", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.CREATE_STAFF_USER));

                featurePreferenceEntities.add(new FeaturePreferenceEntity("username_length", "username_length", "username_length", PreferenceDataType.INT, MetaDataPreferences.getConfigType(), MetaDataPreferences.USERNAME_LENGTH));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("user_password_length", "user_password_length", "user_password_length", PreferenceDataType.INT, MetaDataPreferences.getConfigType(), MetaDataPreferences.USER_PASSWORD_LENGTH));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("institute_unique_code", "institute_unique_code", "institute_unique_code", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.INSTITUTE_UNIQUE_CODE));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("allowed_working_hours_range", "allowed_working_hours_range", "allowed_working_hours_range", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.ALLOWED_WORKING_HOURS_RANGE));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("allowed_working_days", "allowed_working_days", "allowed_working_days", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.ALLOWED_WORKING_DAYS));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("work_timings_enabled", "work_timings_enabled", "work_timings_enabled", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.WORK_TIMINGS_ENABLED));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("work_timings_enabled", "module_access", "module_access", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.MODULE_ACCESS));

                featurePreferenceEntities.add(new FeaturePreferenceEntity("restricted_actions", "restricted_actions", "restricted_actions", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.RESTRICTED_ACTIONS));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("restriction_reason", "restriction_reason", "restriction_reason", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.RESTRICTION_REASON));

                featurePreferenceEntities.add(new FeaturePreferenceEntity("transfer_certificate_counter", "transfer_certificate_counter", "transfer_certificate_counter", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.TRANSFER_CERTIFICATE_COUNTER));
                featurePreferenceEntities.add(new FeaturePreferenceEntity(new InstituteFeaturePreferenceExecutor() {
                    @Override
                    public String read(int instituteId) {
                        CounterData counterData = instituteManager.getCounter(instituteId, CounterType.TRANSFER_CERTIFICATE_NUMBER, false);
                        return counterData == null ? null : counterData.getCounterPrefix();
                    }

                    @Override
                    public boolean update(int instituteId, String value) {
                        return instituteManager.updateCounterPrefix(instituteId, CounterType.TRANSFER_CERTIFICATE_NUMBER, value);
                    }
                }, "transfer_certificate_counter_prefix", "Transfer Certificate Counter Prefix", "Transfer Certificate Prefix", PreferenceDataType.STRING));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("detailed_tc_enabled", "detailed_tc_enabled", "detailed_tc_enabled", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.DETAILED_TC_ENABLED));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("student_registration_form_fields", "student_registration_form_fields", "student_registration_form_fields", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.STUDENT_REGISTRATION_FORM_FIELDS));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("student_registration_form_mandatory_fields", "student_registration_form_mandatory_fields", "student_registration_form_mandatory_fields", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.STUDENT_REGISTRATION_FORM_MANDATORY_FIELDS));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("student_registration_from_standard_list", "student_registration_from_standard_list", "student_registration_from_standard_list", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.STUDENT_REGISTRATION_FORM_STANDARD_LIST));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("adm_num_auth_flow", "Admission Number Based Authentication Flow", "User name will be admission number. Be careful to change if credentials are already generated", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.ADM_NUM_AUTH_FLOW));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("auto_compute_payment_discount_and_fine", "Auto compute discount and fine on fee payment", "Auto compute discount and fine on fee payment", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.AUTO_COMPUTE_PAYMENT_DISCOUNT_AND_FINE));

                featurePreferenceEntities.add(new FeaturePreferenceEntity("birthday_sms_enabled", "Birthday SMS Enable", "Enable it to send sms for birthday either through curd or through portal!", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.BIRTHDAY_SMS_ENABLED));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("restrict_enrollment_payment_pending", "Restrict Enrollment Payment Pending", "Enable it to Restrict Enrollment On Payment Pending", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.RESTRICT_ENROLLMENT_PAYMENT_PENDING));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("tutorial_videos_enabled", "Tutorial Videos Enabled", "Enable it to view video tutorials on web and app", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.TUTORIAL_VIDEOS_ENABLED));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("tutorial_video_details", "Tutorial Video Details", "Json format of all the modules video details.", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.TUTORIAL_VIDEO_DETAILS));

                return featurePreferenceEntities;
            }
        };
    }
}
