package com.embrate.cloud.core.lib.feature.preference.handler;

import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceEntity;
import com.embrate.cloud.core.api.feature.preference.PreferenceDataType;
import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.lib.feature.preference.IFeaturePreferenceGroupBuilder;
import com.lernen.cloud.core.api.configurations.StudentIdentityCardPreferences;
import com.lernen.cloud.core.api.inventory.InventoryPreferences;
import com.lernen.cloud.core.lib.institute.InstituteManager;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class InventoryFeatureHandler extends ProductFeatureHandler {

    private final InstituteManager instituteManager;

    public InventoryFeatureHandler(InstituteManager instituteManager) {
        this.instituteManager = instituteManager;
    }

    private ProductFeature inventoryProductFeature = null;

    @Override
    public ProductFeature buildProductFeature() {
        inventoryProductFeature = new ProductFeature("com.embrate.feature.inventory", "Inventory Preferences", "Inventory Preferences");
        addFeaturePreferenceGroup(inventoryProductFeature, getBasicPreferences());
        return inventoryProductFeature;
    }

    @Override
    public ProductFeature getProductFeature() {
        return inventoryProductFeature;
    }

    private IFeaturePreferenceGroupBuilder getBasicPreferences() {
        return new IFeaturePreferenceGroupBuilder() {
            @Override
            public String getGroupId() {
                return "group.preference.basic";
            }

            @Override
            public String getGroupName() {
                return "Basic Preferences";
            }

            @Override
            public String getGroupDescription() {
                return "Basic Preferences";
            }

            @Override
            public List<FeaturePreferenceEntity> getPreferences() {
                List<FeaturePreferenceEntity> featurePreferenceEntities = new LinkedList<>();
                featurePreferenceEntities.add(new FeaturePreferenceEntity("inventory_outlet", "inventory_outlet", "inventory_outlet", PreferenceDataType.UUID, InventoryPreferences.getConfigType(), InventoryPreferences.INVENTORY_OUTLET));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("allow_wallet_usage", "allow_wallet_usage", "allow_wallet_usage", PreferenceDataType.BOOLEAN, InventoryPreferences.getConfigType(), InventoryPreferences.ALLOW_WALLET_USAGE));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("allow_wallet_based_credit", "allow_wallet_based_credit", "allow_wallet_based_credit", PreferenceDataType.BOOLEAN, InventoryPreferences.getConfigType(), InventoryPreferences.ALLOW_WALLET_BASED_CREDIT));

                return featurePreferenceEntities;
            }
        };
    }
}
