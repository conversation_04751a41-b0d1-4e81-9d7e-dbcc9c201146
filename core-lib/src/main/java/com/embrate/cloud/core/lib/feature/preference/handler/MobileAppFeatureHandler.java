package com.embrate.cloud.core.lib.feature.preference.handler;

import com.embrate.cloud.core.api.application.mobile.MobileAppUpdatePriority;
import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceEntity;
import com.embrate.cloud.core.api.feature.preference.PreferenceDataType;
import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.lib.feature.preference.IFeaturePreferenceGroupBuilder;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.fees.fine.FineCalculatorType;
import com.lernen.cloud.core.api.sms.SMSPreferences;
import com.lernen.cloud.core.api.voicecall.VoiceCallPreferences;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.utils.feature.preference.ProductFeatureUtils;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class MobileAppFeatureHandler extends ProductFeatureHandler {

    private final InstituteManager instituteManager;

    public MobileAppFeatureHandler(InstituteManager instituteManager) {
        this.instituteManager = instituteManager;
    }

    private ProductFeature mobileAppProductFeature = null;

    @Override
    public ProductFeature buildProductFeature() {
        mobileAppProductFeature = new ProductFeature("com.embrate.feature.app.mobile", "Mobile App Preferences", "Mobile App Preferences");
        addFeaturePreferenceGroup(mobileAppProductFeature, getAppPreferences());
        return mobileAppProductFeature;
    }

    @Override
    public ProductFeature getProductFeature() {
        return mobileAppProductFeature;
    }

    private IFeaturePreferenceGroupBuilder getAppPreferences() {
        return new IFeaturePreferenceGroupBuilder() {
            @Override
            public String getGroupId() {
                return "group.preference.app";
            }

            @Override
            public String getGroupName() {
                return "Basic Preferences";
            }

            @Override
            public String getGroupDescription() {
                return "Basic Preferences";
            }

            @Override
            public List<FeaturePreferenceEntity> getPreferences() {
                List<FeaturePreferenceEntity> featurePreferenceEntities = new LinkedList<>();
                featurePreferenceEntities.add(new FeaturePreferenceEntity("mobile_app_enabled", "mobile_app_enabled", "mobile_app_enabled", PreferenceDataType.BOOLEAN, MetaDataPreferences.getConfigType(), MetaDataPreferences.MOBILE_APP_ENABLED));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("single_device_max_users", "single_device_max_users", "single_device_max_users", PreferenceDataType.INT, MetaDataPreferences.getConfigType(), MetaDataPreferences.SINGLE_DEVICE_MAX_USERS));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("android_mobile_app_version", "android_mobile_app_version", "android_mobile_app_version", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.ANDROID_MOBILE_APP_VERSION));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("ios_mobile_app_version", "ios_mobile_app_version", "ios_mobile_app_version", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.IOS_MOBILE_APP_VERSION));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("admin_mobile_app_update_priority", "admin_mobile_app_update_priority", "admin_mobile_app_update_priority", PreferenceDataType.ENUM, MetaDataPreferences.getConfigType(), MetaDataPreferences.ADMIN_MOBILE_APP_UPDATE_PRIORITY, ProductFeatureUtils.getEnumValues(MobileAppUpdatePriority.values())));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("student_mobile_app_update_priority", "student_mobile_app_update_priority", "student_mobile_app_update_priority", PreferenceDataType.ENUM, MetaDataPreferences.getConfigType(), MetaDataPreferences.STUDENT_MOBILE_APP_UPDATE_PRIORITY,  ProductFeatureUtils.getEnumValues(MobileAppUpdatePriority.values())));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("android_force_logout_time_threshold", "android_force_logout_time_threshold", "android_force_logout_time_threshold", PreferenceDataType.INT, MetaDataPreferences.getConfigType(), MetaDataPreferences.ANDROID_FORCE_LOGOUT_TIME_THRESHOLD));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("ios_force_logout_time_threshold", "ios_force_logout_time_threshold", "ios_force_logout_time_threshold", PreferenceDataType.INT, MetaDataPreferences.getConfigType(), MetaDataPreferences.IOS_FORCE_LOGOUT_TIME_THRESHOLD));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("student_session_restriction", "student_session_restriction", "It contains the list of comma seperated session ids with are restricted for student e.g [23,24]", PreferenceDataType.STRING, MetaDataPreferences.getConfigType(), MetaDataPreferences.STUDENT_SESSION_RESTRICTION));
                return featurePreferenceEntities;
            }
        };
    }
}
