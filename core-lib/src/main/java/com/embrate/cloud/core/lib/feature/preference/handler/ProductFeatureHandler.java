package com.embrate.cloud.core.lib.feature.preference.handler;

import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceGroup;
import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.lib.feature.preference.IFeaturePreferenceGroupBuilder;

/**
 * <AUTHOR>
 */

public abstract class ProductFeatureHandler {

    public abstract ProductFeature buildProductFeature();

    public abstract ProductFeature getProductFeature();

    public void addFeaturePreferenceGroup(ProductFeature productFeature, IFeaturePreferenceGroupBuilder featurePreferenceGroupBuilder){
        productFeature.addFeaturePreferenceGroup(new FeaturePreferenceGroup(featurePreferenceGroupBuilder.getGroupId(), featurePreferenceGroupBuilder.getGroupName(), featurePreferenceGroupBuilder.getGroupDescription(), featurePreferenceGroupBuilder.getPreferences()));
    }

}
