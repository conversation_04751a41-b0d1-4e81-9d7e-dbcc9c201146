package com.embrate.cloud.core.lib.feature.preference.handler;

import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceEntity;
import com.embrate.cloud.core.api.feature.preference.PreferenceDataType;
import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.api.scratchcard.RewardsPreferences;
import com.embrate.cloud.core.lib.feature.preference.IFeaturePreferenceGroupBuilder;
import com.lernen.cloud.core.api.common.SchoolNameLogoPosition;
import com.lernen.cloud.core.api.configurations.WebUIPreferences;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.utils.feature.preference.ProductFeatureUtils;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class WebAppFeatureHandler extends ProductFeatureHandler {

    private final InstituteManager instituteManager;

    public WebAppFeatureHandler(InstituteManager instituteManager) {
        this.instituteManager = instituteManager;
    }

    private ProductFeature productFeature = null;

    @Override
    public ProductFeature buildProductFeature() {
        productFeature = new ProductFeature("com.embrate.feature.app.web", "Web App Preferences", "Web App Preferences");
        addFeaturePreferenceGroup(productFeature, getBasicPreferences());
        return productFeature;
    }

    @Override
    public ProductFeature getProductFeature() {
        return productFeature;
    }

    private IFeaturePreferenceGroupBuilder getBasicPreferences() {
        return new IFeaturePreferenceGroupBuilder() {
            @Override
            public String getGroupId() {
                return "group.preference.basic";
            }

            @Override
            public String getGroupName() {
                return "Basic Preferences";
            }

            @Override
            public String getGroupDescription() {
                return "Basic Preferences";
            }

            @Override
            public List<FeaturePreferenceEntity> getPreferences() {

                List<FeaturePreferenceEntity> featurePreferenceEntities = new LinkedList<>();
                featurePreferenceEntities.add(new FeaturePreferenceEntity("school_name_logo_position", "school_name_logo_position", "school_name_logo_position", PreferenceDataType.ENUM, WebUIPreferences.getConfigType(), WebUIPreferences.SCHOOL_NAME_LOGO_POSITION, ProductFeatureUtils.getEnumValues(SchoolNameLogoPosition.values())));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("background_image_enable", "background_image_enable", "background_image_enable", PreferenceDataType.BOOLEAN, WebUIPreferences.getConfigType(), WebUIPreferences.BACKGROUND_IMAGE_ENABLE));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("background_image_url", "background_image_url", "background_image_url", PreferenceDataType.STRING, WebUIPreferences.getConfigType(), WebUIPreferences.BACKGROUND_IMAGE_URL));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("hide_admin_portal_fee_content", "hide_admin_portal_fee_content", "hide_admin_portal_fee_content", PreferenceDataType.BOOLEAN, WebUIPreferences.getConfigType(), WebUIPreferences.HIDE_ADMIN_PORTAL_FEE_CONTENT));

                return featurePreferenceEntities;
            }
        };
    }
}
