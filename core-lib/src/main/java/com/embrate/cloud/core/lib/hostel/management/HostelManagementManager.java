package com.embrate.cloud.core.lib.hostel.management;

import com.embrate.cloud.core.api.frontdesk.*;
import com.embrate.cloud.core.api.hostelmanagement.HostelDetails;
import com.embrate.cloud.core.lib.frontdesk.FrontDeskManager;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.dao.tier.hostelmanagement.HostelManagementDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

public class HostelManagementManager {
	private static final Logger logger = LogManager.getLogger(HostelManagementManager.class);

	private final FrontDeskManager frontDeskManager;
	private final UserPermissionManager userPermissionManager;
	private final HostelManagementDao hostelManagementDao;

	public HostelManagementManager(FrontDeskManager frontDeskManager, UserPermissionManager userPermissionManager, HostelManagementDao hostelManagementDao) {
		this.frontDeskManager = frontDeskManager;
		this.userPermissionManager = userPermissionManager;
		this.hostelManagementDao= hostelManagementDao;
	}

	public GatePassResponse addGatePassDetails(int instituteId, UUID userId, GatePassPayload gatePassPayload) {
		if(instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_GATE_PASS_DETAILS, "Invalid institute id."));
		}
		if(userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_GATE_PASS_DETAILS, "Invalid user id."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ADD_HOSTEL_GATE_PASS_DETAILS);

		validateGatePassDetails(gatePassPayload);
		return frontDeskManager.addGatePassDetails(instituteId, gatePassPayload);
	}

	private void validateGatePassDetails(GatePassPayload gatePassPayload) {
		if(gatePassPayload.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_GATE_PASS_DETAILS, "Invalid institute id."));
		}
		if(gatePassPayload.getAcademicSessionId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_GATE_PASS_DETAILS, "Invalid academic session id."));
		}
		if(StringUtils.isBlank(gatePassPayload.getReason())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_GATE_PASS_DETAILS, "Invalid reason."));
		}
		if(gatePassPayload.getGatePassStatus() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_GATE_PASS_DETAILS, "Invalid gate pass status."));
		}
		if(CollectionUtils.isEmpty(gatePassPayload.getGatePassStudentPayloadList())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_GATE_PASS_DETAILS, "Invalid student details."));
		}
		if(gatePassPayload.getDepartureDateTime() <=0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_GATE_PASS_DETAILS, "Invalid departure date."));
		}
		if(gatePassPayload.getExpectedReturnDateTime() <=0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_GATE_PASS_DETAILS, "Invalid expected return date."));
		}
	}

	public boolean updateGatePassStatus(int instituteId, UUID userId, UUID gatePassId, GatePassStatus gatePassStatus) {
		if(instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_GATE_PASS_DETAILS, "Invalid institute id."));
		}
		if(userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_GATE_PASS_DETAILS, "Invalid user id."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_HOSTEL_GATE_PASS_STATUS);

		if(gatePassId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_GATE_PASS_DETAILS, "Invalid gate pass id."));
		}
		if(gatePassStatus == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_GATE_PASS_DETAILS, "Invalid gate pass id."));
		}

		return frontDeskManager.updateGatePassStatus(instituteId, gatePassId, gatePassStatus);
	}

	public GatePassDetails getGatePassDetailsById(int instituteId, UUID gatePassId) {
		return frontDeskManager.getGatePassDetailsById(instituteId, gatePassId);
	}

	public List<GatePassDetails> getGatePassDetailList(int instituteId, GatePassStatus gatePassStatus) {
		return frontDeskManager.getGatePassDetailList(instituteId, gatePassStatus, true);
	}

	public boolean addHostelDetails(int instituteId, UUID userId, HostelDetails hostelDetailsPayload) {
		if(instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if(userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_HOSTEL_DETAILS);
		validateHostelDetailsPayload(hostelDetailsPayload, false);
		return hostelManagementDao.addHostelDetails(instituteId, hostelDetailsPayload);

	}

	public void  validateHostelDetailsPayload(HostelDetails hostelDetailsPayload, boolean update) {
		if(hostelDetailsPayload.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if(StringUtils.isBlank(hostelDetailsPayload.getHostelName())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_HOSTEL_DETAILS, "Invalid Hostel Name."));
		}

		List<HostelDetails> hostelDetailsList = hostelManagementDao.getHostelDetails(hostelDetailsPayload.getInstituteId());
		for(HostelDetails hostelDetails : hostelDetailsList) {
			if(hostelDetails.getHostelName().equalsIgnoreCase(hostelDetailsPayload.getHostelName()) && (hostelDetailsPayload.getHostelId() == null || (!hostelDetails.getHostelId().equals(hostelDetailsPayload.getHostelId())))) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_HOSTEL_DETAILS,  "Hostel already exists with name - " + hostelDetailsPayload.getHostelName()));
			}
		}

		if(update) {
			if(hostelDetailsPayload.getHostelId() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_HOSTEL_DETAILS, "Invalid Hostel Id."));
			}
		}
	}

	public boolean updateHostelDetails(int instituteId, UUID userId, HostelDetails hostelDetailsPayload) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
		}

		if (userId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_HOSTEL_DETAILS);
		validateHostelDetailsPayload(hostelDetailsPayload, true);
		return hostelManagementDao.updateHostelDetails(instituteId, hostelDetailsPayload);
	}

	public boolean deleteHostelDetails(int instituteId, UUID hostelId, UUID userId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
		}

		if (userId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		if(hostelId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_HOSTEL_DETAILS, "Invalid Hostel Id."));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_HOSTEL_DETAILS);

		int assignedStudentCount = hostelManagementDao.getAssignedStudentCount(hostelId);

		if(assignedStudentCount > 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_HOSTEL_DETAILS, "This hostel is still assigned to some students. Please unassign them before deleting."));
		}
		return hostelManagementDao.deleteHostelDetails(instituteId, hostelId);
	}

	public List<HostelDetails> getHostelDetails(int instituteId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
		}

		return hostelManagementDao.getHostelDetails(instituteId);
	}
}
