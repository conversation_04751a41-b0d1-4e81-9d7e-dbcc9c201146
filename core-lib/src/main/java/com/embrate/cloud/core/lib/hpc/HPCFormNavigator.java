package com.embrate.cloud.core.lib.hpc;

import com.embrate.cloud.core.api.hpc.layout.*;
import com.embrate.cloud.core.api.hpc.utils.HPCUserType;
import com.embrate.cloud.core.api.hpc.utils.IHPCCellContext;
import com.embrate.cloud.core.api.hpc.utils.IHPCContainerContext;
import com.embrate.cloud.core.lib.hpc.navigation.IHPCNavigationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class HPCFormNavigator {
	private static final Logger logger = LogManager.getLogger(HPCFormNavigator.class);

	public void traverseHPCForm(HPCForm form, IHPCNavigationHandler elementActionHandler, HPCUserType formUserType) {
		for (HPCSection section : form.getSections()) {
			if(formUserType != null && section.getAssociatedUserType() != formUserType) {
				continue;
			}
			traverseHPCContainer(section.getContainer(), elementAction<PERSON>and<PERSON>, elementActionHandler.getSectionContainerContext(section));
		}
	}

	private void traverseHPCContainer(HPCContainer container, IHPCNavigationHandler elementActionHandler, IHPCContainerContext containerContext) {
		if (container == null) {
			return;
		}

		if (CollectionUtils.isNotEmpty(container.getChildContainers())) {
			for (HPCContainer childContainer : container.getChildContainers()) {
				IHPCContainerContext childContainerContext = elementActionHandler.getChildContainerContext(containerContext, container, childContainer);
				traverseHPCContainer(childContainer, elementActionHandler, childContainerContext);
			}
		}

		if (CollectionUtils.isNotEmpty(container.getTextElements())) {
			for (HPCTextElement textElement : container.getTextElements()) {
				elementActionHandler.textElementAction(textElement, containerContext);
			}
		}

		if (container.getTable() != null) {
			elementActionHandler.tableAction(container, containerContext);
			for (HPCCell cell : container.getTable().getCells()) {
				IHPCCellContext cellContext = elementActionHandler.getCellContext(containerContext, null, cell);
				traverseHPCCell(cell, elementActionHandler, containerContext, cellContext);
			}
		}

		if (container.getImageContainer() != null) {
			elementActionHandler.imageContainerAction(container.getImageContainer(), containerContext);
		}
		/* don't add any other element below it */

	}

	private void traverseHPCCell(HPCCell cell, IHPCNavigationHandler elementActionHandler, IHPCContainerContext containerContext, IHPCCellContext cellContext) {
		if (cell == null) {
			return;
		}

		if (CollectionUtils.isEmpty(cell.getChildCells())) {
			elementActionHandler.leafCellElementAction(cell, containerContext, cellContext);
			return;
		}

		elementActionHandler.nonLeafCellElementAction(cell, containerContext, cellContext);
		
		for (HPCCell childCell : cell.getChildCells()) {
			IHPCCellContext childCellContext = elementActionHandler.getCellContext(containerContext, cell, childCell);
			traverseHPCCell(childCell, elementActionHandler, containerContext, childCellContext);
		}
	}

}
