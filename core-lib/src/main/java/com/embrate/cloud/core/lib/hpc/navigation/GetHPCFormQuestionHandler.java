package com.embrate.cloud.core.lib.hpc.navigation;

import com.embrate.cloud.core.api.hpc.layout.*;
import com.embrate.cloud.core.api.hpc.utils.HPCFieldType;
import com.embrate.cloud.core.api.hpc.utils.HPCFieldValue;
import com.embrate.cloud.core.api.hpc.utils.IHPCCellContext;
import com.embrate.cloud.core.api.hpc.utils.IHPCContainerContext;
import com.lernen.cloud.core.utils.SharedConstants;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class GetHPCFormQuestionHandler implements IHPCNavigationHandler {

	private final Map<String, HPCFieldValue> formFieldMap = new HashMap<>();

	private final Map<String, HPCFieldValue> imageContainerFormFieldMap = new HashMap<>();

	public Map<String, HPCFieldValue> getFormFieldMap() {
		return formFieldMap;
	}

	public Map<String, HPCFieldValue> getImageContainerFormFieldMap() {
		return imageContainerFormFieldMap;
	}

	@Override
	public void textElementAction(HPCTextElement textElement, IHPCContainerContext containerContext) {
		if (textElement == null) {
			return;
		}
		formFieldMap.put(textElement.getId(), new HPCFieldValue(textElement.getId(), HPCFieldType.TEXT, null));
	}

	@Override
	public void imageContainerAction(HPCImageContainer imageContainer, IHPCContainerContext containerContext) {
		if (imageContainer == null) {
			return;
		}
		formFieldMap.put(imageContainer.getId(), new HPCFieldValue(imageContainer.getId(), HPCFieldType.IMAGE, SharedConstants.GSON.toJson(imageContainer.getImage())));
		imageContainerFormFieldMap.put(imageContainer.getId(), new HPCFieldValue(imageContainer.getId(), HPCFieldType.IMAGE, SharedConstants.GSON.toJson(imageContainer.getImage())));
	}

	@Override
	public IHPCContainerContext getChildContainerContext(IHPCContainerContext currentContext, HPCContainer parentContainer, HPCContainer childContainer) {
		return null;
	}

	@Override
	public IHPCContainerContext getSectionContainerContext(HPCSection section) {
		return null;
	}

	@Override
	public void tableAction(HPCContainer container, IHPCContainerContext containerContext) {

	}

	@Override
	public void leafCellElementAction(HPCCell cell, IHPCContainerContext containerContext, IHPCCellContext cellContext) {
		textElementAction(cell.getTextElement(), containerContext);
	}

	@Override
	public void nonLeafCellElementAction(HPCCell cell, IHPCContainerContext containerContext, IHPCCellContext cellContext) {

	}

	@Override
	public IHPCCellContext getCellContext(IHPCContainerContext containerContext, HPCCell parentCell, HPCCell childCell) {
		return null;
	}
}
