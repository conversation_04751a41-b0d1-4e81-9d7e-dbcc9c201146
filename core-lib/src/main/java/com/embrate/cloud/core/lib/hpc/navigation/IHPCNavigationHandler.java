package com.embrate.cloud.core.lib.hpc.navigation;

import com.embrate.cloud.core.api.hpc.layout.*;
import com.embrate.cloud.core.api.hpc.utils.IHPCCellContext;
import com.embrate.cloud.core.api.hpc.utils.IHPCContainerContext;

/**
 * <AUTHOR>
 */
public interface IHPCNavigationHandler<A extends IHPCContainerContext, B extends IHPCCellContext> {

	void textElementAction(HPCTextElement textElement, A containerContext);

	void imageContainerAction(HPCImageContainer container, A containerContext);

	A getChildContainerContext(A currentContext, HPCContainer parentContainer, HPCContainer childContainer);

	A getSectionContainerContext(HPCSection section);

	void tableAction(HPCContainer container, A containerContext);

	void leafCellElementAction(HPCCell cell, A containerContext, B cellContext);

	void nonLeafCellElementAction(HPCCell cell, A containerContext, B cellContext);

	B getCellContext(A containerContext, HPCCell parentCell, HPCCell childCell);
}
