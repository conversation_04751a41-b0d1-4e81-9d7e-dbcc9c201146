package com.embrate.cloud.core.lib.institute.onboarding;

import com.embrate.cloud.core.api.onboarding.institute.*;
import com.embrate.cloud.core.api.onboarding.institute.setup.InstituteSetupResult;
import com.embrate.cloud.core.lib.filesystem.S3FileSystem;
import com.lernen.cloud.core.api.common.AreaType;
import com.lernen.cloud.core.api.common.ChildCategoryCriteria;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.common.Medium;
import com.lernen.cloud.core.api.configurations.Entity;
import com.lernen.cloud.core.api.configurations.InstituteOnBoardingPreferences;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fees.FeeStructureType;
import com.lernen.cloud.core.api.fees.ResolvedDefaultEntityFeeAssignmentStructure;
import com.lernen.cloud.core.api.institute.*;
import com.lernen.cloud.core.api.organisation.InstituteConfigurationPayload;
import com.lernen.cloud.core.api.organisation.Organisation;
import com.lernen.cloud.core.api.student.*;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.lib.configs.ConfigurationManager;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.fees.configuration.FeeConfigurationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.student.StudentAdmissionManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStreamReader;
import java.util.*;


/**
 *
 * <AUTHOR>
 *
 */
public class InstituteOnBoardingManager {

	private static final Logger logger = LogManager.getLogger(InstituteOnBoardingManager.class);

	private static final String TEMP_REGISTRATION_NUMBER_SUFFIX = "-TEMP";

	public static final String INSTITUTE_ONBOARDING_BUCKET = "lernen-institute-onboarding-v5";
	public static final String INSTITUTE_ONBOARDING_STUDENT_DATA_PATH = "student-data";
	public static final String INSTITUTE_ONBOARDING_STUDENT_INGESTION_RESULT_PATH = "student-ingestion-result";
	public static final String INSTITUTE_ONBOARDING_STUDENT_FILE_EXTENSION = ".csv";
	public static final String JSON = ".json";

	private final InstituteManager instituteManager;
	private final StudentManager studentManager;
	private final StudentAdmissionManager studentAdmissionManager;
	private final FeeConfigurationManager feeConfigurationManager;
	private final UserPreferenceSettings userPreferenceSettings;
	private final ConfigurationManager configurationManager;
	private final S3FileSystem s3FileSystem;
	private final String env;
	private final UserManager userManager;

	private final InstituteSetupManager instituteSetupManager;

	public InstituteOnBoardingManager(InstituteManager instituteManager, StudentManager studentManager,
									  StudentAdmissionManager studentAdmissionManager, FeeConfigurationManager feeConfigurationManager,
									  UserPreferenceSettings userPreferenceSettings, ConfigurationManager configurationManager,
									  S3FileSystem s3FileSystem, String env, UserManager userManager, InstituteSetupManager instituteSetupManager) {
		this.instituteManager = instituteManager;
		this.studentManager = studentManager;
		this.studentAdmissionManager = studentAdmissionManager;
		this.feeConfigurationManager = feeConfigurationManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.configurationManager = configurationManager;
		this.s3FileSystem = s3FileSystem;
		this.env = env;
		this.userManager = userManager;
		this.instituteSetupManager = instituteSetupManager;
	}


	public InstituteSetupResult setupInstitute(InstituteConfigurationPayload instituteConfigurationPayload) {
		return instituteSetupManager.setupInstitute(instituteConfigurationPayload);
	}


	public InstituteConfigurationPayload getSampleInstituteConfigurationPayload() {
		return instituteSetupManager.getSampleInstituteConfigurationPayload();
	}

	public String uploadStudentDataFile(int instituteId, FileData document) {
		if (instituteId <= 0) {
			logger.error("Invalid institute {}", instituteId);
			return null;
		}
		if (document == null || document.getContent() == null) {
			logger.error("Invalid document for {}", instituteId);
			return null;
		}

		final String fileId = RandomStringUtils.random(6, CharsetUtils.CAPS_UNAMBIGUOUS_CHARSET) + "-"
				+ System.currentTimeMillis();
		final String studentFilePath = buildStudentDataPath(instituteId, fileId);
		if (s3FileSystem.writeFile(INSTITUTE_ONBOARDING_BUCKET, studentFilePath, document)) {
			logger.info("Successfully written the student data file for instituteId {}, studentFilePath {}",
					instituteId, studentFilePath);
			return fileId;
		}

		logger.error("Error while writing the student data file for instituteId {}, studentFilePath {}", instituteId,
				studentFilePath);
		return null;
	}

	public ByteArrayOutputStream getStudentDataFile(int instituteId, String fileId) {
		if (instituteId <= 0) {
			logger.error("Invalid institute {}", instituteId);
			return null;
		}
		if (StringUtils.isBlank(fileId)) {
			logger.error("Invalid fileId for {}", instituteId);
			return null;
		}

		final String studentFilePath = buildStudentDataPath(instituteId, fileId);
		final ByteArrayOutputStream file = s3FileSystem.readFile(INSTITUTE_ONBOARDING_BUCKET, studentFilePath);
		if (file != null) {
			logger.info("Successfully read the student data file for instituteId {}, studentFilePath {}", instituteId,
					studentFilePath);
			return file;
		}

		logger.error("Error while reading the student data file for instituteId {}, studentFilePath {}", instituteId,
				studentFilePath);
		return null;
	}

	public List<List<String>> getStudentData(int instituteId, String fileId) {

		final InstituteOnBoardingPreferences instituteOnBoardingPreferences = userPreferenceSettings
				.getInstituteOnBoardingPreferences(instituteId);

		final String delimiter = instituteOnBoardingPreferences.getStudentDataDelimiter();
		if (StringUtils.isBlank(delimiter)) {
			logger.error("Invalid delimiter for institute {}", instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid delimiter"));
		}

		final ByteArrayOutputStream fileByteArrayOutputStream = getStudentDataFile(instituteId, fileId);
		if (fileByteArrayOutputStream == null) {
			logger.error("Invalid fileId for institute {}, fileId {}", instituteId, fileId);
			return null;
		}

		final List<List<String>> fileContent = new ArrayList<>();
		try (final BufferedReader bufferedReader = new BufferedReader(
				new InputStreamReader(new ByteArrayInputStream(fileByteArrayOutputStream.toByteArray())))) {
			String line = null;
			while ((line = bufferedReader.readLine()) != null) {
				final String[] tokens = line.split(delimiter, -1);
				fileContent.add(Arrays.asList(tokens));
			}
			return fileContent;
		} catch (final Exception e) {
			logger.error("Error while reading the file for instituteId {}, fileId {}", instituteId, fileId, e);
		}
		return null;
	}

	public InstituteStudentIngestionConfigs getStudentIngestionConfigurations(int instituteId) {
		if (instituteId <= 0) {
			logger.error("Invalid institute {}", instituteId);
			return null;
		}
		final InstituteOnBoardingPreferences instituteOnBoardingPreferences = userPreferenceSettings
				.getInstituteOnBoardingPreferences(instituteId);

		final List<InstituteOnBoardingStudentDataField> studentDataFieldList = instituteOnBoardingPreferences
				.getStudentDataFieldList();
		final Map<InstituteOnBoardingStudentDataFieldName, InstituteOnBoardingStudentDataField> studentDataFieldMap = new EnumMap<>(
				InstituteOnBoardingStudentDataFieldName.class);
		if (CollectionUtils.isNotEmpty(studentDataFieldList)) {
			for (final InstituteOnBoardingStudentDataField instituteOnBoardingStudentDataField : studentDataFieldList) {
				studentDataFieldMap.put(instituteOnBoardingStudentDataField.getStudentDataFieldName(),
						instituteOnBoardingStudentDataField);
			}
		}

		final List<InstituteOnBoardingStudentDataField> allStudentDataFieldList = new ArrayList<>();
		for (final InstituteOnBoardingStudentDataFieldName instituteOnBoardingStudentDataFieldName : InstituteOnBoardingStudentDataFieldName
				.values()) {
			if (studentDataFieldMap.containsKey(instituteOnBoardingStudentDataFieldName)) {
				allStudentDataFieldList.add(studentDataFieldMap.get(instituteOnBoardingStudentDataFieldName));
			} else {
				final InstituteOnBoardingStudentDataField instituteOnBoardingStudentDataField = new InstituteOnBoardingStudentDataField();
				instituteOnBoardingStudentDataField.setStudentDataFieldName(instituteOnBoardingStudentDataFieldName);
				allStudentDataFieldList.add(instituteOnBoardingStudentDataField);
			}
		}

		return new InstituteStudentIngestionConfigs(allStudentDataFieldList,
				instituteOnBoardingPreferences.getStudentDataDelimiter(),
				instituteOnBoardingPreferences.isStudentDataHeader(),
				instituteOnBoardingPreferences.getStudentClassColumnField(),
				instituteOnBoardingPreferences.getDobFormat(), instituteOnBoardingPreferences.getAdmissionDateFormat());

	}

	public boolean updateStudentIngestionConfigurations(int instituteId,
			InstituteStudentIngestionConfigs instituteStudentIngestionConfigs) {

		if (instituteId <= 0) {
			logger.error("Invalid institute {}", instituteId);
			return false;
		}

		if (instituteStudentIngestionConfigs == null) {
			logger.error("Invalid instituteStudentIngestionConfigs for institute {}", instituteId);
			return false;
		}
		if (StringUtils.isBlank(instituteStudentIngestionConfigs.getStudentDataDelimiter())) {
			logger.error("Invalid delimiter for institute {}", instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid delimiter"));
		}

		if (instituteStudentIngestionConfigs.getStudentClassColumnField() == null || instituteStudentIngestionConfigs
				.getStudentClassColumnField().getStudentClassColumnFormat() == null) {
			logger.error("Invalid student class column config {} for institute {}",
					instituteStudentIngestionConfigs.getStudentClassColumnField(), instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid class column format"));
		}

		final Map<String, String> keyValues = new HashMap<>();
		if (CollectionUtils.isNotEmpty(instituteStudentIngestionConfigs.getStudentDataFieldList())) {
			final List<InstituteOnBoardingStudentDataField> instituteOnBoardingStudentDataFields = new ArrayList<>();
			final Set<Integer> indexes = new HashSet<>();
			for (final InstituteOnBoardingStudentDataField instituteOnBoardingStudentDataField : instituteStudentIngestionConfigs
					.getStudentDataFieldList()) {
				final Integer index = instituteOnBoardingStudentDataField.getIndex();
				if (instituteOnBoardingStudentDataField.getStudentDataFieldName() != null
						&& instituteOnBoardingStudentDataField.getIndex() != null && index >= 0) {
					instituteOnBoardingStudentDataFields.add(instituteOnBoardingStudentDataField);

					if (indexes.contains(index)) {
						logger.error("Index {}, is duplicate. Skipping update for institute {}", index, instituteId);
						throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
								"Duplicate column index " + index));
					}
					indexes.add(index);
				}
			}

			if (CollectionUtils.isNotEmpty(instituteOnBoardingStudentDataFields)) {
				keyValues.put(InstituteOnBoardingPreferences.STUDENT_DATA_FIELD_MAPPINGS,
						SharedConstants.GSON.toJson(instituteOnBoardingStudentDataFields));
			}

		}
		keyValues.put(InstituteOnBoardingPreferences.STUDENT_DATA_DELIMITER,
				instituteStudentIngestionConfigs.getStudentDataDelimiter());

		keyValues.put(InstituteOnBoardingPreferences.STUDENT_DATA_HEADER,
				String.valueOf(instituteStudentIngestionConfigs.isStudentDataHeader()));

		keyValues.put(InstituteOnBoardingPreferences.STUDENT_CLASS_COLUMN_CONFIGS,
				SharedConstants.GSON.toJson(instituteStudentIngestionConfigs.getStudentClassColumnField()));

		if (StringUtils.isNotEmpty(instituteStudentIngestionConfigs.getDobFormat())) {
			keyValues.put(InstituteOnBoardingPreferences.DOB_FORMAT, instituteStudentIngestionConfigs.getDobFormat());
		}

		if (StringUtils.isNotEmpty(instituteStudentIngestionConfigs.getAdmissionDateFormat())) {
			keyValues.put(InstituteOnBoardingPreferences.ADMISSION_DATE_FORMAT,
					instituteStudentIngestionConfigs.getAdmissionDateFormat());
		}

		return configurationManager.upsertConfiguration(Entity.INSTITUTE, String.valueOf(instituteId),
				InstituteOnBoardingPreferences.getConfigType(), keyValues);

	}

	public StudentDataIngestionExecutionSummary ingestStudentsDryRun(int instituteId, int academicSessionId,
			String filePath, UUID userId, boolean checkAdmissionNumberOnly) {
		return ingestStudents(instituteId, academicSessionId, filePath, true, false, userId, checkAdmissionNumberOnly, false);
	}

	public String ingestStudentsExecution(int instituteId, int academicSessionId, String filePath,
			boolean ignoreFailure, UUID userId, boolean checkAdmissionNumberOnly, boolean checkCreateUser) {
		List<User> internalUsers = userManager.getDefaultInternalUser(instituteId);
		if (CollectionUtils.isEmpty(internalUsers)) {
			logger.error("No internal user found for institute {}. Skipping execution.", instituteId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
					"No internal user configured for institute"));
		}
		final User user = internalUsers.get(0);
		final UUID internalUserId = user.getUuid();
		final String resultOutput = filePath + "-" + System.currentTimeMillis();
		logger.info("Writing ingestion result at id {}, instituteId {}, academicSessionId {} internal user {}",
				resultOutput, instituteId, academicSessionId, internalUserId);

		final Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				final StudentDataIngestionExecutionSummary studentDataIngestionExecutionSummary = ingestStudents(
						instituteId, academicSessionId, filePath, false, ignoreFailure, internalUserId, checkAdmissionNumberOnly, checkCreateUser);
				final String path = buildStudentDataExecutionResultPath(instituteId, resultOutput);
				final String content = SharedConstants.GSON.toJson(studentDataIngestionExecutionSummary);
				if (s3FileSystem.writeFile(INSTITUTE_ONBOARDING_BUCKET, path, new FileData(content.getBytes(), null))) {
					logger.info(
							"Successfully written the output to s3 bucket {}, path {}, instituteId {}, academicSessionId {}",
							INSTITUTE_ONBOARDING_BUCKET, path, instituteId, academicSessionId);
				} else {
					logger.error(
							"Error while writing the output to s3 bucket {}, path {}, instituteId {}, academicSessionId {}",
							INSTITUTE_ONBOARDING_BUCKET, path, instituteId, academicSessionId);
				}
			}
		});
		t.start();

		return resultOutput;
	}

	public StudentDataIngestionExecutionSummary getStudentDataIngestionExecutionResult(int instituteId,
			String resultId) {

		final String path = buildStudentDataExecutionResultPath(instituteId, resultId);

		try {
			final ByteArrayOutputStream fileByteArrayOutputStream = s3FileSystem.readFile(INSTITUTE_ONBOARDING_BUCKET,
					path);
			if (fileByteArrayOutputStream == null) {
				logger.error("Invalid resultId for institute {}, resultId {}", instituteId, resultId);
				return null;
			}
			return SharedConstants.GSON.fromJson(
					IOUtils.toString(new ByteArrayInputStream(fileByteArrayOutputStream.toByteArray())),
					StudentDataIngestionExecutionSummary.class);

		} catch (final Exception e) {
			logger.error("Error while reading the result for instituteId {}, resultId {}", instituteId, resultId, e);
		}
		return null;
	}

	private StudentDataIngestionExecutionSummary ingestStudents(int instituteId, int academicSessionId, String filePath,
			boolean dryRun, boolean ignoreFailure, UUID userId, boolean checkAdmissionNumberonly, boolean checkCreateUser) {

		final InstituteOnBoardingPreferences instituteOnBoardingPreferences = userPreferenceSettings
				.getInstituteOnBoardingPreferences(instituteId);

		final MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);

		final List<InstituteOnBoardingStudentDataField> studentDataFieldList = instituteOnBoardingPreferences
				.getStudentDataFieldList();

		if (CollectionUtils.isEmpty(studentDataFieldList)) {
			logger.error("No student data field index mapping found for instituteId {}. Skipping student ingestion.",
					instituteId);
			return null;
		}

		final List<Standard> standards = instituteManager.getInstituteStandardList(instituteId, academicSessionId);
		if (CollectionUtils.isEmpty(standards)) {
			logger.error("No standards configured for instituteId {}. Skipping student ingestion.", instituteId);
			return null;
		}

		final List<Student> allSessionExistingStudents = studentManager.getAllStudentWithoutSession(instituteId);
		final Map<String, Student> allSessionRegistrationRequestNumberStudentMap = new HashMap<>();
		final Map<String, Student> allSessionAdmissionNumbersStudentMap = new HashMap<>();
		getStudentAdmissionRegistrationMap(instituteId, allSessionExistingStudents, allSessionAdmissionNumbersStudentMap, allSessionRegistrationRequestNumberStudentMap);

		final List<Student> currentSessionExistingStudents = studentManager.getStudentsByStandardIds(instituteId, academicSessionId, null, null);
		final Map<String, Student> currentSessionRegistrationRequestNumberStudentMap = new HashMap<>();
		final Map<String, Student> currentSessionAdmissionNumberStudentMap = new HashMap<>();
		getStudentAdmissionRegistrationMap(instituteId, currentSessionExistingStudents, currentSessionAdmissionNumberStudentMap, currentSessionRegistrationRequestNumberStudentMap);

		final List<InstituteHouse> instituteHousesList = instituteManager.getInstituteHouseList(instituteId);

		final StudentDataIngestionSummary studentDataIngestionSummary = getStudentDataIngestionSummary(instituteId,
				academicSessionId, filePath, instituteOnBoardingPreferences, standards, metaDataPreferences,
				instituteHousesList, allSessionRegistrationRequestNumberStudentMap, allSessionAdmissionNumbersStudentMap,
				currentSessionRegistrationRequestNumberStudentMap, currentSessionAdmissionNumberStudentMap, checkAdmissionNumberonly);

		if (studentDataIngestionSummary == null) {
			logger.error("Unable to get ingestion summary for instituteId {}. Skipping student ingestion.",
					instituteId);
			return null;
		}

		final StudentDataIngestionExecutionSummary studentDataIngestionExecutionSummary = executeStudentIngestion(
				instituteId, academicSessionId, studentDataIngestionSummary, dryRun, ignoreFailure, userId, allSessionAdmissionNumbersStudentMap, checkCreateUser);

		if (studentDataIngestionExecutionSummary == null) {
			logger.error("Unable to execute student ingestion for instituteId {}.", instituteId);
		}

		return studentDataIngestionExecutionSummary;
	}

	private StudentDataIngestionExecutionSummary executeStudentIngestion(int instituteId, int academicSessionId,
			StudentDataIngestionSummary studentDataIngestionSummary, boolean dryRun, boolean ignoreFailure,
			UUID userId, Map<String, Student> allSessionAdmissionNumbersStudentMap, boolean checkCreateUser) {

		if (!dryRun && !ignoreFailure && studentDataIngestionSummary.getFailureCount() > 0) {
			logger.error("There are {} failure records. Skipping execution.",
					studentDataIngestionSummary.getFailureCount());
			return null;
		}

		final List<StudentDataIngestionRecord> executionSuccessStudentDataIngestionRecords = new ArrayList<>();

		final List<StudentDataIngestionRecord> executionFailureStudentDataIngestionRecords = new ArrayList<>();

		final List<StudentDataIngestionRecord> admitFailureStudentDataIngestionRecords = new ArrayList<>();

		for (final StudentDataIngestionRecord studentDataIngestionRecord : studentDataIngestionSummary
				.getSuccessStudentDataIngestionRecords()) {
			if (dryRun) {
				executionSuccessStudentDataIngestionRecords.add(studentDataIngestionRecord);
			} else {

				String admissionNumber = studentDataIngestionRecord.getRegisterStudentPayload().getStudentPayload()
						.getStudentBasicInfo().getAdmissionNumber().toLowerCase();
				//if student present in any session, following new flow.
				if(allSessionAdmissionNumbersStudentMap.containsKey(admissionNumber)) {
					ingestLatterSessionStudents(instituteId, studentDataIngestionRecord, allSessionAdmissionNumbersStudentMap, userId,
							executionSuccessStudentDataIngestionRecords, executionFailureStudentDataIngestionRecords, admitFailureStudentDataIngestionRecords);
				} else {
					ingestStudent(instituteId, userId, academicSessionId, executionSuccessStudentDataIngestionRecords,
							executionFailureStudentDataIngestionRecords, admitFailureStudentDataIngestionRecords,
							studentDataIngestionRecord, checkCreateUser);
				}
			}
		}

		return new StudentDataIngestionExecutionSummary(dryRun, executionSuccessStudentDataIngestionRecords,
				executionFailureStudentDataIngestionRecords, admitFailureStudentDataIngestionRecords,
				studentDataIngestionSummary.getFailureStudentDataIngestionRecords());

	}

	private void ingestLatterSessionStudents(int instituteId, final StudentDataIngestionRecord studentDataIngestionRecord,
											 Map<String, Student> allSessionAdmissionNumbersStudentMap, UUID userId,
											 final List<StudentDataIngestionRecord> executionSuccessStudentDataIngestionRecords,
											 final List<StudentDataIngestionRecord> executionFailureStudentDataIngestionRecords,
											 final List<StudentDataIngestionRecord> admitFailureStudentDataIngestionRecords) {

		final RegisterStudentPayload registerStudentPayload = studentDataIngestionRecord.getRegisterStudentPayload();
		final Student student = allSessionAdmissionNumbersStudentMap.get(
				registerStudentPayload.getStudentPayload().getStudentBasicInfo().getAdmissionNumber().toLowerCase());

		if (student == null) {
			logger.error(
					"Error while adding student session at record {}, name {}, admission number {}, instituteId {},  feeStructures = {}",
					studentDataIngestionRecord.getLineNumber(),
					registerStudentPayload.getStudentPayload().getStudentBasicInfo().getName(),
					registerStudentPayload.getStudentPayload().getStudentBasicInfo().getAdmissionNumber(),
					instituteId, registerStudentPayload.getFeeStructureIds());
			studentDataIngestionRecord.setErrors(Collections.singletonList("Error adding student session details"));
			executionFailureStudentDataIngestionRecords.add(studentDataIngestionRecord);
			return;
		}

		//if not present in current session
		//verify student update permission
		//insert an entry in student session details table
		//Assign fees
		UUID hostelId = student.getStudentAcademicSessionInfoResponse().getHostelDetails() == null ? null : student.getStudentAcademicSessionInfoResponse().getHostelDetails().getHostelId();
		studentDataIngestionRecord.getRegisterStudentPayload().getStudentPayload().setHostelId(hostelId);
		if (!studentAdmissionManager.addLatterSessionStudent(studentDataIngestionRecord.getRegisterStudentPayload(), student.getStudentId(),
				instituteId, null, null, Arrays.asList(FeeStructureType.REGISTRATION, FeeStructureType.ENROLLMENT),
				false, userId)) {
			logger.error("Error adding student session record {}, name {}, admission number {}, instituteId {}",
					studentDataIngestionRecord.getLineNumber(),
					registerStudentPayload.getStudentPayload().getStudentBasicInfo().getName(),
					registerStudentPayload.getStudentPayload().getStudentBasicInfo().getAdmissionNumber(),
					instituteId);
			studentDataIngestionRecord.setErrors(Collections.singletonList("Error adding student session details"));
			executionFailureStudentDataIngestionRecords.add(studentDataIngestionRecord);
			admitFailureStudentDataIngestionRecords.add(studentDataIngestionRecord);

			return;
		}

		logger.info("Success in adding student session record {}, name {}, admission number {}, instituteId {}",
				studentDataIngestionRecord.getLineNumber(),
				registerStudentPayload.getStudentPayload().getStudentBasicInfo().getName(),
				registerStudentPayload.getStudentPayload().getStudentBasicInfo().getAdmissionNumber(),
				instituteId);

		executionSuccessStudentDataIngestionRecords.add(studentDataIngestionRecord);
	}

	private void ingestStudent(int instituteId, UUID userId, int academicSessionId,
			final List<StudentDataIngestionRecord> executionSuccessStudentDataIngestionRecords,
			final List<StudentDataIngestionRecord> executionFailureStudentDataIngestionRecords,
			final List<StudentDataIngestionRecord> admitFailureStudentDataIngestionRecords,
			final StudentDataIngestionRecord studentDataIngestionRecord, boolean createUser) {

		final RegisterStudentPayload registerStudentPaylaod = studentDataIngestionRecord.getRegisterStudentPayload();
		try {

			logger.info("Ingesting record {}, name {}, admission number {}, instituteId {}, feeStructures = {}",
					studentDataIngestionRecord.getLineNumber(),
					registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getName(),
					registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getAdmissionNumber(), instituteId,
					registerStudentPaylaod.getFeeStructureIds());

			final UUID studentId = studentAdmissionManager.registerStudent(
					studentDataIngestionRecord.getRegisterStudentPayload(), instituteId, null, null,
					Arrays.asList(FeeStructureType.REGISTRATION, FeeStructureType.ENROLLMENT), false, userId);

			if (studentId == null) {
				logger.error(
						"Error while registering student at record {}, name {}, admission number {}, instituteId {},  feeStructures = {}",
						studentDataIngestionRecord.getLineNumber(),
						registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getName(),
						registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getAdmissionNumber(),
						instituteId, registerStudentPaylaod.getFeeStructureIds());
				studentDataIngestionRecord.setErrors(Arrays.asList("Error registering student"));
				executionFailureStudentDataIngestionRecords.add(studentDataIngestionRecord);
			} else {
				logger.info(
						"Success registering student record {}, name {}, admission number {}, instituteId {}, feeStructures = {}",
						studentDataIngestionRecord.getLineNumber(),
						registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getName(),
						registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getAdmissionNumber(),
						instituteId, registerStudentPaylaod.getFeeStructureIds());
				List<String> errorList = new ArrayList<>();
				if (!studentAdmissionManager.admitStudent(instituteId, academicSessionId, userId, studentId,
						registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getAdmissionNumber(),
						registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getAdmissionDate(),
						registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getInstituteHouseId(),
						registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getSiblingGroupId(), createUser, errorList)) {
					logger.error("Error admitting student record {}, name {}, admission number {}, instituteId {}",
							studentDataIngestionRecord.getLineNumber(),
							registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getName(),
							registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getAdmissionNumber(),
							instituteId);
					studentDataIngestionRecord.setErrors(errorList);
					executionFailureStudentDataIngestionRecords.add(studentDataIngestionRecord);
					admitFailureStudentDataIngestionRecords.add(studentDataIngestionRecord);
				} else {
					logger.info("Success admitting student record {}, name {}, admission number {}, instituteId {}",
							studentDataIngestionRecord.getLineNumber(),
							registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getName(),
							registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getAdmissionNumber(),
							instituteId);
					executionSuccessStudentDataIngestionRecords.add(studentDataIngestionRecord);

				}
			}

		} catch (final ApplicationException e) {
			logger.error(
					"Exception ingesting record {}, name {}, admission number {}, instituteId {}, feeStructures = {}",
					studentDataIngestionRecord.getLineNumber(),
					registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getName(),
					registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getAdmissionNumber(), instituteId,
					registerStudentPaylaod.getFeeStructureIds(), e);
			studentDataIngestionRecord.setErrors(
					Arrays.asList("Exception : " + e.getErrorResponse().getMessage() + ", ingesting record"));
			executionFailureStudentDataIngestionRecords.add(studentDataIngestionRecord);
		} catch (final Exception e) {
			logger.error(
					"Exception ingesting record {}, name {}, admission number {}, instituteId {}, feeStructures = {}",
					studentDataIngestionRecord.getLineNumber(),
					registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getName(),
					registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getAdmissionNumber(), instituteId,
					registerStudentPaylaod.getFeeStructureIds(), e);
			studentDataIngestionRecord
					.setErrors(Arrays.asList("Exception : " + e.getStackTrace().toString() + ", ingesting record"));
			executionFailureStudentDataIngestionRecords.add(studentDataIngestionRecord);
		}
	}

	private StudentDataIngestionSummary getStudentDataIngestionSummary(int instituteId, int academicSessionId,
			String fileId, final InstituteOnBoardingPreferences instituteOnBoardingPreferences,
			final List<Standard> standards, MetaDataPreferences metaDataPreferences, List<InstituteHouse> instituteHousesList,
				Map<String, Student> allSessionRegistrationRequestNumberStudentMap, Map<String, Student> allSessionAdmissionNumbersStudentMap,
				Map<String, Student> currentSessionRegistrationRequestNumberStudentMap, Map<String, Student> currentSessionAdmissionNumberStudentMap, boolean checkAdmissionNumberOnly) {
		final Map<UUID, List<ResolvedDefaultEntityFeeAssignmentStructure>> classFeeStructuresMap = new HashMap<>();
		final List<StudentDataIngestionRecord> studentDataIngestionRecords = new ArrayList<>();
		final String delimiter = instituteOnBoardingPreferences.getStudentDataDelimiter();
		boolean header = instituteOnBoardingPreferences.isStudentDataHeader();

		final ByteArrayOutputStream fileByteArrayOutputStream = getStudentDataFile(instituteId, fileId);
		if (fileByteArrayOutputStream == null) {
			logger.error("Invalid fileId {} for institute {}", fileId, instituteId);
			return null;
		}

		try (final BufferedReader bufferedReader = new BufferedReader(
				new InputStreamReader(new ByteArrayInputStream(fileByteArrayOutputStream.toByteArray())))) {
			String line = "";
			int row = 0;
			while ((line = bufferedReader.readLine()) != null) {
				if (header) {
					logger.info("File {} has header, Skipping..", fileId);
					header = false;
					row++;
					continue;
				}

				if (StringUtils.isEmpty(line)) {
					logger.warn("Empty line at row {}, Skipping...", row);
					row++;
					continue;
				}

				final String[] params = line.split(delimiter, -1);
				final StudentDataIngestionRecord studentDataIngestionRecord = getStudentDataIngestionRecord(instituteId,
						academicSessionId, row, line, params, instituteOnBoardingPreferences, standards,
						classFeeStructuresMap, metaDataPreferences, instituteHousesList);

				studentDataIngestionRecords.add(studentDataIngestionRecord);
				row++;
			}

			return getStudentDataIngestionSummary(instituteId, academicSessionId, studentDataIngestionRecords, metaDataPreferences,
					allSessionRegistrationRequestNumberStudentMap, allSessionAdmissionNumbersStudentMap, currentSessionRegistrationRequestNumberStudentMap, currentSessionAdmissionNumberStudentMap, checkAdmissionNumberOnly);

		} catch (final Exception e) {
			logger.error("Error occurred while ingesting students, instituteId {}, academicSessionId {} ", instituteId,
					academicSessionId, e);
		}

		return null;
	}

	private StudentDataIngestionSummary getStudentDataIngestionSummary(int instituteId, int academicSessionId,
			final List<StudentDataIngestionRecord> studentDataIngestionRecords, MetaDataPreferences metaDataPreferences,
		    Map<String, Student> allSessionRegistrationRequestNumberStudentMap, Map<String, Student> allSessionAdmissionNumbersStudentMap,
		    Map<String, Student> currentSessionRegistrationRequestNumberStudentMap, Map<String, Student> currentSessionAdmissionNumberStudentMap, boolean checkAdmissionNumberOnly) {

		final List<StudentDataIngestionRecord> successStudentDataIngestionRecords = new ArrayList<>();

		final List<StudentDataIngestionRecord> failureStudentDataIngestionRecords = new ArrayList<>();

		final Set<String> admissionNumberSet = new HashSet<>();
		final Set<String> newRegistrationRequestNumbers = new HashSet<>();


		for (final StudentDataIngestionRecord studentDataIngestionRecord : studentDataIngestionRecords) {
			if (!studentDataIngestionRecord.isSuccess()) {
				continue;
			}

			if (studentDataIngestionRecord.getErrors() == null) {
				studentDataIngestionRecord.setErrors(new ArrayList<>());
			}

			final String admissionNumber = studentDataIngestionRecord.getRegisterStudentPayload().getStudentPayload()
					.getStudentBasicInfo().getAdmissionNumber();
			if (StringUtils.isNotBlank(admissionNumber)) {
				if (admissionNumberSet.contains(admissionNumber.trim().toLowerCase())) {
					studentDataIngestionRecord.getErrors().add("Duplicate admission number = " + admissionNumber);
				} else {
					admissionNumberSet.add(admissionNumber.trim().toLowerCase());
				}
			}

			final String registrationRequestNumber = studentDataIngestionRecord.getRegisterStudentPayload()
					.getStudentPayload().getStudentBasicInfo().getRegistrationRequestNumber();

			if (StringUtils.isNotBlank(registrationRequestNumber)) {
				if (newRegistrationRequestNumbers.contains(registrationRequestNumber.trim().toLowerCase())) {
					studentDataIngestionRecord.getErrors()
							.add("Duplicate registration request number = " + registrationRequestNumber);
				} else {
					newRegistrationRequestNumbers.add(registrationRequestNumber.trim().toLowerCase());
				}
			}

		}

		for (final StudentDataIngestionRecord studentDataIngestionRecord : studentDataIngestionRecords) {
			if (!studentDataIngestionRecord.isSuccess()) {
				failureStudentDataIngestionRecords.add(studentDataIngestionRecord);
				continue;
			}

			if (studentDataIngestionRecord.getErrors() == null) {
				studentDataIngestionRecord.setErrors(new ArrayList<>());
			}

			String registrationRequestNumber = studentDataIngestionRecord.getRegisterStudentPayload()
					.getStudentPayload().getStudentBasicInfo().getRegistrationRequestNumber();
			String studentName = studentDataIngestionRecord.getRegisterStudentPayload()
					.getStudentPayload().getStudentBasicInfo().getName();
			String fatherName = studentDataIngestionRecord.getRegisterStudentPayload()
					.getStudentPayload().getStudentFamilyInfo() == null ? null : studentDataIngestionRecord.getRegisterStudentPayload()
					.getStudentPayload().getStudentFamilyInfo().getFathersName();


			if (StringUtils.isBlank(registrationRequestNumber)) {
				studentDataIngestionRecord.getErrors().add("Empty registration request number");
				failureStudentDataIngestionRecords.add(studentDataIngestionRecord);
				continue;
			}

			registrationRequestNumber = registrationRequestNumber.trim().toLowerCase();

			if (currentSessionRegistrationRequestNumberStudentMap.containsKey(registrationRequestNumber)) {
				studentDataIngestionRecord.getErrors()
						.add("Existing data already have some student with registration request number = "
								+ registrationRequestNumber + " in selected session.");
			}

			if (allSessionRegistrationRequestNumberStudentMap.containsKey(registrationRequestNumber)
					&& !allSessionRegistrationRequestNumberStudentMap.get(registrationRequestNumber).getStudentBasicInfo().getName().equalsIgnoreCase(studentName) && !checkAdmissionNumberOnly) {
						studentDataIngestionRecord.getErrors()
						.add("Invalid Entry! Existing data in all session already have some student with registration request number = "
								+ registrationRequestNumber + " but not with same student name = "
								+ allSessionRegistrationRequestNumberStudentMap.get(registrationRequestNumber).getStudentBasicInfo().getName()
								+ ". New studentName = " + studentName);
			}

			if (allSessionRegistrationRequestNumberStudentMap.containsKey(registrationRequestNumber)
					&& allSessionRegistrationRequestNumberStudentMap.get(registrationRequestNumber).getStudentFamilyInfo() != null
					&& !StringUtils.isEmpty(allSessionRegistrationRequestNumberStudentMap.get(registrationRequestNumber).getStudentFamilyInfo().getFathersName())
					&& !StringUtils.isEmpty(fatherName) && !allSessionRegistrationRequestNumberStudentMap.get(registrationRequestNumber)
					.getStudentFamilyInfo().getFathersName().equalsIgnoreCase(fatherName) && !checkAdmissionNumberOnly) {
				studentDataIngestionRecord.getErrors()
						.add("Invalid Entry! Existing data in all session already have some student with registration request number = "
								+ registrationRequestNumber + " but not with same father name = " + fatherName);
			}

			if(allSessionRegistrationRequestNumberStudentMap.containsKey(registrationRequestNumber)
				&& allSessionRegistrationRequestNumberStudentMap.get(registrationRequestNumber).getFinalStudentStatus() == StudentStatus.ENROLMENT_PENDING) {
				studentDataIngestionRecord.getErrors()
						.add("Invalid Entry! Existing data in all session already have some student with registration request number = "
								+ registrationRequestNumber + " which is in pending statue");
			}

			String admissionNumber = studentDataIngestionRecord.getRegisterStudentPayload().getStudentPayload()
					.getStudentBasicInfo().getAdmissionNumber();

			if (!metaDataPreferences.isAdmissionCounter()) {
				admissionNumber = admissionNumber.trim().toLowerCase();

				if (currentSessionAdmissionNumberStudentMap.containsKey(admissionNumber)) {
					studentDataIngestionRecord.getErrors()
							.add("Existing data already have some student with admission number = " + admissionNumber + " in selected session.");
				}

				if (allSessionAdmissionNumbersStudentMap.containsKey(admissionNumber)
						&& !allSessionAdmissionNumbersStudentMap.get(admissionNumber).getStudentBasicInfo().getName().equalsIgnoreCase(studentName) && !checkAdmissionNumberOnly) {
					studentDataIngestionRecord.getErrors()
							.add("Invalid Entry! Existing data already have some student with admission number = "
									+ admissionNumber + " but not with same student name = "
									+ allSessionAdmissionNumbersStudentMap.get(admissionNumber).getStudentBasicInfo().getName()
									+ ". New studentName = " + studentName);
				}

				if (allSessionAdmissionNumbersStudentMap.containsKey(admissionNumber)
						&& allSessionAdmissionNumbersStudentMap.get(admissionNumber).getStudentFamilyInfo() != null
						&& !StringUtils.isEmpty(allSessionAdmissionNumbersStudentMap.get(admissionNumber).getStudentFamilyInfo().getFathersName())
						&& !StringUtils.isEmpty(fatherName) && !allSessionAdmissionNumbersStudentMap.get(admissionNumber)
						.getStudentFamilyInfo().getFathersName().equalsIgnoreCase(fatherName) && !checkAdmissionNumberOnly) {
					studentDataIngestionRecord.getErrors()
							.add("Invalid Entry! Existing data already have some student with admission number = "
									+ admissionNumber + " but not with same father name = " + fatherName);
				}

				if(allSessionAdmissionNumbersStudentMap.containsKey(admissionNumber)
						&& allSessionAdmissionNumbersStudentMap.get(admissionNumber).getFinalStudentStatus() == StudentStatus.ENROLMENT_PENDING) {
					studentDataIngestionRecord.getErrors()
							.add("Invalid Entry! Existing data in all session already have some student with registration request number = "
									+ registrationRequestNumber + " which is in pending statue");
				}
			}

			if (CollectionUtils.isNotEmpty(studentDataIngestionRecord.getErrors())) {
				failureStudentDataIngestionRecords.add(studentDataIngestionRecord);
			} else {
				successStudentDataIngestionRecords.add(studentDataIngestionRecord);
			}
		}

		return new StudentDataIngestionSummary(successStudentDataIngestionRecords, failureStudentDataIngestionRecords);

	}

	private void getStudentAdmissionRegistrationMap(int instituteId, List<Student> studentList, Map<String, Student> admissionNumbersStudentMap,
														  Map<String, Student> registrationRequestNumberMap) {
		if (CollectionUtils.isEmpty(studentList)) {
			logger.error("Error while getting existing students of institute {}", instituteId);
			return;
		}

		for (final Student student : studentList) {
			registrationRequestNumberMap
					.put(student.getStudentBasicInfo().getRegistrationRequestNumber().trim().toLowerCase(), student);

			admissionNumbersStudentMap.put(student.getStudentBasicInfo().getAdmissionNumber().trim().toLowerCase(), student);
		}
	}

	private String getColumnString(InstituteOnBoardingStudentDataFieldName instituteOnBoardingStudentDataFieldName,
			String[] params, int index, boolean mandatory, List<String> errors) {
		final String param = params[index];
		if (StringUtils.isNotBlank(param)) {
			return param.trim();
		}

		if (mandatory) {
			errors.add("Missing mandatory field " + instituteOnBoardingStudentDataFieldName.name() + " at index = "
					+ index);
		}

		return null;
	}

	private Integer getColumnInteger(InstituteOnBoardingStudentDataFieldName instituteOnBoardingStudentDataFieldName,
			String[] params, int index, boolean mandatory, List<String> errors) {

		final String integerStr = getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory,
				errors);

		if (StringUtils.isBlank(integerStr)) {
			return null;
		}

		try {
			return Integer.parseInt(integerStr);
		} catch (final NumberFormatException e) {
			errors.add("Invalid integer value " + integerStr + " for " + instituteOnBoardingStudentDataFieldName.name()
					+ " at index = " + index);
			logger.error("Invalid integer value {}", integerStr);
		}

		return null;
	}

	private Boolean getColumnBoolean(InstituteOnBoardingStudentDataFieldName instituteOnBoardingStudentDataFieldName,
			String[] params, int index, boolean mandatory, List<String> errors) {

		String booleanStr = getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory,
				errors);

		if (StringUtils.isBlank(booleanStr)) {
			return null;
		}

		if(booleanStr.equalsIgnoreCase("NO") || booleanStr.equalsIgnoreCase("N")) {
			booleanStr = String.valueOf(false);
		}

		if(booleanStr.equalsIgnoreCase("YES") || booleanStr.equalsIgnoreCase("Y")) {
			booleanStr = String.valueOf(true);
		}

		return Boolean.parseBoolean(booleanStr);
	}

	private StudentDataIngestionRecord getStudentDataIngestionRecord(int instituteId, int academicSessionId, int row,
			String line, String[] params, InstituteOnBoardingPreferences instituteOnBoardingPreferences,
			List<Standard> standards,
			Map<UUID, List<ResolvedDefaultEntityFeeAssignmentStructure>> classFeeStructuresMap,
			MetaDataPreferences metaDataPreferences, List<InstituteHouse> instituteHousesList) {

		final List<String> errors = new ArrayList<>();
		final List<InstituteOnBoardingStudentDataField> studentDataFieldList = instituteOnBoardingPreferences
				.getStudentDataFieldList();

		final RegisterStudentPayload registerStudentPayload = new RegisterStudentPayload();
		final StudentPayload studentPayload = new StudentPayload();
		final StudentBasicInfo studentBasicInfo = new StudentBasicInfo();
		final StudentFamilyInfo studentFamilyInfo = new StudentFamilyInfo();
		final StudentPreviousSchoolInfo studentPreviousSchoolInfo = new StudentPreviousSchoolInfo();
		InstituteOnBoardingStudentDataField feeStructureStudentDataField = null;
		Pair<Standard, Integer> standardSectionPair = null;

		for (final InstituteOnBoardingStudentDataField studentDataField : studentDataFieldList) {
			final InstituteOnBoardingStudentDataFieldName instituteOnBoardingStudentDataFieldName = studentDataField
					.getStudentDataFieldName();
			final int index = studentDataField.getIndex();
			final boolean mandatory = studentDataField.isMandatory();

			logger.info(" Running for row {}, instituteOnBoardingStudentDataFieldName {}, index {}, mandatory {}", row,
					instituteOnBoardingStudentDataFieldName, index, mandatory);

			if (index < 0 || index >= params.length) {
				logger.error("Invalid index configured for studentDataField : {}, instituteId {}", studentDataField,
						instituteId);
				errors.add("Invalid index " + index + " configured for column "
						+ studentDataField.getStudentDataFieldName().name());
				return StudentDataIngestionRecord.forFailure(row, line, errors);
			}

			try {
				if (instituteOnBoardingStudentDataFieldName == InstituteOnBoardingStudentDataFieldName.STANDARD_NAME) {
					standardSectionPair = getStandard(row, instituteOnBoardingStudentDataFieldName, params,
							instituteOnBoardingPreferences, standards, index, errors);
				} else if (instituteOnBoardingStudentDataFieldName == InstituteOnBoardingStudentDataFieldName.FEE_STRUCTURES) {
					feeStructureStudentDataField = studentDataField;
				} else {
					updateStudentFields(instituteId, instituteOnBoardingPreferences,
							instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors, studentPayload, studentBasicInfo,
							studentFamilyInfo, studentPreviousSchoolInfo, metaDataPreferences, instituteHousesList);
				}
			} catch (final Exception e) {
				logger.error("Exception while reading row {}, for instituteOnBoardingStudentDataFieldName {}",
						instituteOnBoardingStudentDataFieldName, e);
				errors.add("Error while processing index " + index + " configured for column "
						+ instituteOnBoardingStudentDataFieldName.name() + ". Message = " + e.getMessage());
			}

		}

		if (standardSectionPair == null) {
			logger.error("No class information found for institute {}, session {}", instituteId, academicSessionId);
			errors.add("Invalid class information");
			return StudentDataIngestionRecord.forFailure(row, line, errors);
		}

		final UUID standardId = standardSectionPair.getFirst().getStandardId();
		final Integer sectionId = standardSectionPair.getSecond();

		final Boolean feeStructuresAssigned = getStudentFeeStructures(instituteId, academicSessionId, row, params,
				classFeeStructuresMap, errors, registerStudentPayload, feeStructureStudentDataField, standardId);

		if (CollectionUtils.isNotEmpty(errors)) {
			return StudentDataIngestionRecord.forFailure(row, line, errors);
		}

		if (!metaDataPreferences.isAdmissionCounter() && StringUtils.isBlank(studentBasicInfo.getAdmissionNumber())) {
			errors.add("Admission number not provided");
			return StudentDataIngestionRecord.forFailure(row, line, errors);
		}

		if (metaDataPreferences.isRegistrationCounter()) {
			if (StringUtils.isNotBlank(studentBasicInfo.getRegistrationNumber())) {
				studentBasicInfo.setRegistrationRequestNumber(studentBasicInfo.getRegistrationNumber());
			} else {
				final String regNumer = UUID.randomUUID().toString();
				studentBasicInfo.setRegistrationNumber(regNumer);
				studentBasicInfo.setRegistrationRequestNumber(regNumer);
			}
		} else if (StringUtils.isBlank(studentBasicInfo.getRegistrationNumber())) {
			if (metaDataPreferences.isAdmissionCounter()) {
				errors.add("Registration number not provided");
				return StudentDataIngestionRecord.forFailure(row, line, errors);
			} else if (StringUtils.isNotBlank(studentBasicInfo.getAdmissionNumber())) {
				studentBasicInfo
						.setRegistrationNumber(studentBasicInfo.getAdmissionNumber() + TEMP_REGISTRATION_NUMBER_SUFFIX);
				studentBasicInfo.setRegistrationRequestNumber(studentBasicInfo.getRegistrationNumber());
			}
		} else if (!StringUtils.isBlank(studentBasicInfo.getRegistrationNumber())) {
			studentBasicInfo.setRegistrationRequestNumber(studentBasicInfo.getRegistrationNumber());
		}

		studentPayload.setStudentBasicInfo(studentBasicInfo);
		studentPayload.setStudentFamilyInfo(studentFamilyInfo);
		studentPayload.setStudentPreviousSchoolInfo(studentPreviousSchoolInfo);
		studentPayload.setStandardId(standardId);
		studentPayload.setSectionId(sectionId);
		studentPayload.setAdmissionAcademicSession(academicSessionId);
		studentPayload.setInstituteId(instituteId);

		registerStudentPayload.setStudentPayload(studentPayload);

		return StudentDataIngestionRecord.forSuccess(row, line, registerStudentPayload, feeStructuresAssigned);
	}

	private void updateStudentFields(int instituteId, InstituteOnBoardingPreferences instituteOnBoardingPreferences,
			InstituteOnBoardingStudentDataFieldName instituteOnBoardingStudentDataFieldName, String[] params, int index,
			boolean mandatory, List<String> errors, StudentPayload studentPayload, StudentBasicInfo studentBasicInfo,
			StudentFamilyInfo studentFamilyInfo, StudentPreviousSchoolInfo studentPreviousSchoolInfo,
			MetaDataPreferences metaDataPreferences, List<InstituteHouse> instituteHousesList) {
		switch (instituteOnBoardingStudentDataFieldName) {
		case STUDENT_NAME:
			final String studentName = getColumnString(instituteOnBoardingStudentDataFieldName, params, index, true,
					errors);

			studentBasicInfo.setName(studentName);
			break;
		case REGISTRATION_NUMBER:
			final boolean registrationMandatory = !metaDataPreferences.isRegistrationCounter() || mandatory;
			final String registrationNumber = getColumnString(instituteOnBoardingStudentDataFieldName, params, index,
					registrationMandatory, errors);
			studentBasicInfo.setRegistrationNumber(registrationNumber);
			break;
		case ADMISSION_NUMBER:
			final boolean admissionNumberMandatory = !metaDataPreferences.isAdmissionCounter() || mandatory;
			final String admissionNumber = getColumnString(instituteOnBoardingStudentDataFieldName, params, index,
					admissionNumberMandatory, errors);
			studentBasicInfo.setAdmissionNumber(admissionNumber);
			break;

		case DOB:
			final String dataFormat = instituteOnBoardingPreferences.getDobFormat();
			if (StringUtils.isEmpty(dataFormat)) {
				logger.error("Invalid dob format configured, instituteId {}", instituteId);
				errors.add("Invalid dob format configured for instituteId" + instituteId);
				break;
			}
			final String dob = getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory,
					errors);
			if (StringUtils.isNotEmpty(dob)) {
				final int dobTimestamp = DateUtils.getTimestampFromDate(dob, User.DFAULT_TIMEZONE, dataFormat);
				studentBasicInfo.setDateOfBirth(dobTimestamp);
			}
			break;

		case ADMISSION_DATE:
			final String admissionDateFormat = instituteOnBoardingPreferences.getAdmissionDateFormat();
			if (StringUtils.isEmpty(admissionDateFormat)) {
				logger.error("Invalid admission date format configured, instituteId {}", instituteId);
				errors.add("Invalid admission date format configured for instituteId" + instituteId);
				break;
			}
			final String admissionDate = getColumnString(instituteOnBoardingStudentDataFieldName, params, index,
					mandatory, errors);
			if (StringUtils.isNotEmpty(admissionDate)) {
				final int admissionTimestamp = DateUtils.getTimestampFromDate(admissionDate, User.DFAULT_TIMEZONE,
						admissionDateFormat);
				studentBasicInfo.setAdmissionDate(admissionTimestamp);
			}
			break;

		case BIRTH_PLACE:
			studentBasicInfo.setBirthPlace(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case GENDER:
			final String genderStr = getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory,
					errors);
			if (StringUtils.isNotBlank(genderStr)) {
				final Gender gender = Gender.getGender(genderStr);
				studentBasicInfo.setGender(gender);
			}
			break;

		case USER_CATEGORY:
			final String categoryStr = getColumnString(instituteOnBoardingStudentDataFieldName, params, index,
					mandatory, errors);
			if (StringUtils.isNotBlank(categoryStr)) {
				final UserCategory category = UserCategory.getCategory(categoryStr);
				studentBasicInfo.setUserCategory(category);
			}
			break;

		case MOTHER_TONGUE:
			studentBasicInfo.setMotherTongue(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case AREA_TYPE:
			final String areaTypeStr = getColumnString(instituteOnBoardingStudentDataFieldName, params, index,
					mandatory, errors);
			if (StringUtils.isNotBlank(areaTypeStr)) {
				final AreaType areaType = AreaType.getAreaType(areaTypeStr);
				studentBasicInfo.setAreaType(areaType);
			}
			break;

		case BPL:
			studentBasicInfo.setBpl(
					getColumnBoolean(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case SPECIALLY_ABLED:
			studentBasicInfo.setSpeciallyAbled(
					getColumnBoolean(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case RELIGION:
			studentBasicInfo.setReligion(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case RTE:
			final Boolean value = getColumnBoolean(instituteOnBoardingStudentDataFieldName, params, index, mandatory,
					errors);
			studentBasicInfo.setRte(value != null && value);
			break;

		case AADHAR_NUMBER:
			studentBasicInfo.setAadharNumber(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case PERMANENT_ADDRESS:
			studentBasicInfo.setPermanentAddress(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case PERMANENT_CITY:
			studentBasicInfo.setPermanentCity(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case PERMANENT_STATE:
			studentBasicInfo.setPermanentState(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case PERMANENT_ZIPCODE:
			studentBasicInfo.setPermanentZipcode(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case PERMANENT_COUNTRY:
			studentBasicInfo.setPermanentCountry(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case PRESENT_ADDRESS:
			studentBasicInfo.setPresentAddress(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case PRESENT_CITY:
			studentBasicInfo.setPresentCity(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case PRESENT_STATE:
			studentBasicInfo.setPresentState(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case PRESENT_ZIPCODE:
			studentBasicInfo.setPresentZipcode(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case PRESENT_COUNTRY:
			studentBasicInfo.setPresentCountry(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case NATIONALITY:
			studentBasicInfo.setNationality(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case PRIMARY_CONTACT_NUMBER:
			studentBasicInfo.setPrimaryContactNumber(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case PRIMARY_EMAIL:
			studentBasicInfo.setPrimaryEmail(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case WHATSAPP_NUMBER:
			studentBasicInfo.setWhatsappNumber(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case CASTE:
			studentBasicInfo.setCast(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case MOTHER_NAME:
			studentFamilyInfo.setMothersName(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case FATHER_NAME:
			studentFamilyInfo.setFathersName(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case MOTHER_CONTACT_NUMBER:
			studentFamilyInfo.setMothersContactNumber(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case FATHER_CONTACT_NUMBER:
			studentFamilyInfo.setFathersContactNumber(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case MOTHER_OCCUPATION:
			studentFamilyInfo.setMothersOccupation(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case MOTHER_ANNUAL_INCOME:
			studentFamilyInfo.setMothersAnnualIncome(
						getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case FATHER_OCCUPATION:
			studentFamilyInfo.setFathersOccupation(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case FATHER_ANNUAL_INCOME:
			studentFamilyInfo.setFathersAnnualIncome(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case MOTHER_AADHAR_NUMBER:
			studentFamilyInfo.setMothersAadharNumber(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case FATHER_AADHAR_NUMBER:
			studentFamilyInfo.setFathersAadharNumber(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case APPROX_FAMILY_INCOME:
			studentFamilyInfo.setApproxFamilyIncome(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case SCHOOL_NAME:
			studentPreviousSchoolInfo.setSchoolName(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case CLASS_PASSED:
			studentPreviousSchoolInfo.setClassPassed(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case MEDIUM:
			studentPreviousSchoolInfo.setMedium(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case PERCENTAGE:
			studentPreviousSchoolInfo.setPercentage(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case RESULT:
			studentPreviousSchoolInfo.setResult(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case YEAR_OF_PASSING:
			studentPreviousSchoolInfo.setYearOfPassing(
					getColumnInteger(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;
		case NEW_ADMISSION:
			Boolean newAdmission = getColumnBoolean(instituteOnBoardingStudentDataFieldName, params, index, true, errors);
			studentPayload.setNewAdmission(newAdmission != null && newAdmission);
			break;

		case ROLL_NUMBER:
			studentPayload.setRollNumber(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case STUDENT_HOUSE_NAME:
			String houseName = getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors);
			UUID houseId = getHouseIdByName(instituteHousesList, houseName);
			studentBasicInfo.setInstituteHouseId(houseId);
			break;

		case ADMISSION_IN_CLASS:
			studentBasicInfo.setAdmissionInClass(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;

		case SESSION_MEDIUM:

			String mediumStr = getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors);
			if (StringUtils.isNotBlank(mediumStr)) {
				Medium medium = Medium.getMedium(mediumStr);
				logger.info("Medium {} found", medium);
				studentPayload.setMedium(medium);
			}
			break;

		case PEN_NUMBER:
			studentBasicInfo.setPenNumber(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;
		case STUDENT_NAME_AS_PER_AADHAR:
			studentBasicInfo.setStudentNameAsPerAadhar(
					getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory, errors));
			break;
		case ONLY_CHILD_CRITERIA:
			final String onlyChildCriteriaStr = getColumnString(instituteOnBoardingStudentDataFieldName, params, index, mandatory,
					errors);
			if (StringUtils.isNotBlank(onlyChildCriteriaStr)) {
				final ChildCategoryCriteria onlyChildCriteria = ChildCategoryCriteria.getChildCategoryCriteria(onlyChildCriteriaStr);
				studentBasicInfo.setChildCategoryCriteria(onlyChildCriteria);
			}
			break;
		default:
			break;
		}
	}

	private UUID getHouseIdByName(List<InstituteHouse> instituteHouseList, String houseName) {
		if(CollectionUtils.isEmpty(instituteHouseList) || StringUtils.isBlank(houseName)) {
			return null;
		}
		for(InstituteHouse instituteHouse : instituteHouseList) {
			if(instituteHouse.getHouseName().equalsIgnoreCase(houseName.trim())) {
				logger.info("House id {} found for house name {}", instituteHouse.getHouseId(), instituteHouse.getHouseName());
				return instituteHouse.getHouseId();
			}
		}
		return null;
	}

	private Boolean getStudentFeeStructures(int instituteId, int academicSessionId, int row, String[] params,
			Map<UUID, List<ResolvedDefaultEntityFeeAssignmentStructure>> classFeeStructuresMap,
			final List<String> errors, final RegisterStudentPayload registerStudentPayload,
			InstituteOnBoardingStudentDataField feeStructureStudentDataField, final UUID standardId) {

		if (feeStructureStudentDataField == null) {
			return null;
		}
		if (!classFeeStructuresMap.containsKey(standardId)) {
			classFeeStructuresMap.put(standardId, getClassFeeStructures(instituteId, academicSessionId, standardId));
		}

		final String feeStructures = getColumnString(feeStructureStudentDataField.getStudentDataFieldName(), params,
				feeStructureStudentDataField.getIndex(), feeStructureStudentDataField.isMandatory(), errors);
		if (StringUtils.isNotBlank(feeStructures)) {
			final String[] tokens = feeStructures.toLowerCase().trim().split(",");

			final List<UUID> requiredFeeAssignmentStructures = getRequiredFeeStructures(
					classFeeStructuresMap.get(standardId), new HashSet<>(Arrays.asList(tokens)));

			logger.info("Fee structure being assinged, row {},  are {}, ids {}", row, feeStructures,
					requiredFeeAssignmentStructures);
			if (CollectionUtils.isNotEmpty(requiredFeeAssignmentStructures)) {
				registerStudentPayload.setFeeStructureIds(requiredFeeAssignmentStructures);
				return true;
			}

		} else {
			logger.info("No fee structure present, row {}", row);
		}
		return false;

	}

	private Pair<Standard, Integer> getStandard(int row,
			InstituteOnBoardingStudentDataFieldName instituteOnBoardingStudentDataFieldName, String[] params,
			InstituteOnBoardingPreferences instituteOnBoardingPreferences, List<Standard> standards, final int index,
			List<String> errors) {
		final InstituteOnBoardingStudentClassColumnField instituteOnBoardingStudentClassColumnField = instituteOnBoardingPreferences
				.getStudentClassColumnField();
		final InstituteOnBoardingStudentClassColumnFormat instituteOnBoardingStudentClassColumnFormat = instituteOnBoardingStudentClassColumnField
				.getStudentClassColumnFormat();

		final String standardNameValue = getColumnString(instituteOnBoardingStudentDataFieldName, params, index, true,
				errors);
		final String[] tokens = standardNameValue.split("#", -1);
		StandardSectionStream standardSectionStream = null;

		if (instituteOnBoardingStudentClassColumnField.isStudentDataMultiColumnClass()) {

			if (instituteOnBoardingStudentClassColumnFormat == null) {
				logger.error("Class column format is must for multi column class");
				throw new EmbrateRunTimeException("Class format is must for multi column class");
			}

			switch (instituteOnBoardingStudentClassColumnFormat) {
			case CLASS_NAME:

				String sectionName = null;
				String streamName = null;

				final String standardName = standardNameValue;
				if (instituteOnBoardingStudentClassColumnField.getSectionColumn() != null) {
					sectionName = getColumnString(instituteOnBoardingStudentDataFieldName, params,
							instituteOnBoardingStudentClassColumnField.getSectionColumn(), false, errors);
				}
				if (instituteOnBoardingStudentClassColumnField.getStreamColumn() != null) {
					streamName = getColumnString(instituteOnBoardingStudentDataFieldName, params,
							instituteOnBoardingStudentClassColumnField.getStreamColumn(), false, errors);
				}
				standardSectionStream = new StandardSectionStream(standardName, sectionName, streamName);
				break;
			case CLASS_SECTION:
				standardSectionStream = getStandardSectionStream(1, null, tokens);
				break;
			case CLASS_STREAM:
				standardSectionStream = getStandardSectionStream(null, 1, tokens);
				break;
			default:
				logger.error("Invalid multi column state {}", instituteOnBoardingStudentClassColumnFormat);
				throw new EmbrateRunTimeException("Invalid multicolumn state");
			}

		} else {
			if (instituteOnBoardingStudentClassColumnFormat == null
					|| instituteOnBoardingStudentClassColumnFormat == InstituteOnBoardingStudentClassColumnFormat.CLASS_NAME) {
				standardSectionStream = new StandardSectionStream(standardNameValue, null, null);
			} else {
				switch (instituteOnBoardingStudentClassColumnFormat) {
				case CLASS_SECTION:
					standardSectionStream = getStandardSectionStream(1, null, tokens);
					break;
				case CLASS_STREAM:
					standardSectionStream = getStandardSectionStream(null, 1, tokens);
					break;
				case CLASS_SECTION_STREAM:
					standardSectionStream = getStandardSectionStream(1, 2, tokens);
					break;
				case CLASS_STREAM_SECTION:
					standardSectionStream = getStandardSectionStream(2, 1, tokens);
					break;
				default:
					logger.error("Invalid instituteOnBoardingStudentClassColumnFormat {}",
							instituteOnBoardingStudentClassColumnFormat);
					throw new EmbrateRunTimeException("Invalid class format");
				}
			}

		}

		if (standardSectionStream != null) {
			final Standard standard = getStandard(row, standards, standardSectionStream.getStandardName(),
					standardSectionStream.getStreamName());
			final Integer sectionId = getSection(standardSectionStream.getSectionName(), standard);
			return new Pair<>(standard, sectionId);
		}

		logger.error("Invalid class columns {}", row);
		throw new EmbrateRunTimeException("Invalid class");
	}

	private Integer getSection(final String sectionName, final Standard standard) {
		if (StringUtils.isBlank(sectionName)) {
			return null;
		}
		for (final StandardSections standardSection : standard.getStandardSectionList()) {
			if (standardSection.getSectionName().equals(sectionName)) {
				return standardSection.getSectionId();
			}
		}

		return null;
	}

	private StandardSectionStream getStandardSectionStream(Integer sectionIndex, Integer streamIndex, String[] tokens) {
		String standardName = null;
		String sectionName = null;
		String streamName = null;

		if (tokens.length == 1) {
			standardName = tokens[0].trim();
		} else if (tokens.length == 2) {
			standardName = tokens[0].trim();

			if (sectionIndex != null && streamIndex != null) {
				throw new EmbrateRunTimeException("Invalid state");
			}

			if (sectionIndex != null) {
				sectionName = tokens[1].trim();
			} else if (streamIndex != null) {
				streamName = tokens[1].trim();
			}
		} else if (tokens.length == 2) {
			standardName = tokens[0].trim();

			if (sectionIndex != null) {
				sectionName = tokens[sectionIndex].trim();
			}
			if (streamIndex != null) {
				streamName = tokens[streamIndex].trim();
			}
		}

		return new StandardSectionStream(standardName, sectionName, streamName);
	}

	private Standard getStandard(int row, List<Standard> standards, String inputStandardName, String streamName) {

		final Map<String, Standard> standardNameMap = new HashMap<>();
		for (final Standard standard : standards) {
			final String standardName = standard.getStandardName();
			final Stream stream = standard.getStream() == null ? Stream.NA : standard.getStream();

			if (stream == Stream.NA && StringUtils.isBlank(streamName)
					&& standardName.equalsIgnoreCase(inputStandardName)) {
				return standard;
			} else if (stream != Stream.NA && StringUtils.isNotBlank(streamName)) {
				if (standardName.equalsIgnoreCase(inputStandardName) && Stream.getStream(streamName) == stream) {
					return standard;
				}
			}
		}

		logger.error("Invalid class {}, stream {} at given row {}", inputStandardName, streamName, row);
		throw new EmbrateRunTimeException("Invalid class at given row " + row);
	}

	private List<ResolvedDefaultEntityFeeAssignmentStructure> getClassFeeStructures(int instituteId,
			int academicSessionId, UUID standardId) {
		return feeConfigurationManager.getDefaultFeeAssignmentStructure(instituteId, academicSessionId, standardId,
				Arrays.asList(FeeStructureType.REGISTRATION, FeeStructureType.ENROLLMENT), false);
	}

	private List<UUID> getRequiredFeeStructures(
			List<ResolvedDefaultEntityFeeAssignmentStructure> allFeeAssignmentStructures, Set<String> structureNames) {

		final List<UUID> requiredResolvedDefaultEntityFeeAssignmentStructures = new ArrayList<>();

		for (final ResolvedDefaultEntityFeeAssignmentStructure resolvedDefaultEntityFeeAssignmentStructure : allFeeAssignmentStructures) {
			if (structureNames
					.contains(resolvedDefaultEntityFeeAssignmentStructure.getStructureName().trim().toLowerCase())) {

				requiredResolvedDefaultEntityFeeAssignmentStructures
						.add(resolvedDefaultEntityFeeAssignmentStructure.getStructureId());
			}
		}
		return requiredResolvedDefaultEntityFeeAssignmentStructures;
	}

	private String buildStudentDataPath(int instituteId, String fileId) {
		return "env=" + env + StringConstants.FOLDER_DELIMITER + "institute=" + instituteId
				+ StringConstants.FOLDER_DELIMITER + INSTITUTE_ONBOARDING_STUDENT_DATA_PATH
				+ StringConstants.FOLDER_DELIMITER + fileId + INSTITUTE_ONBOARDING_STUDENT_FILE_EXTENSION;
	}

	private String buildStudentDataExecutionResultPath(int instituteId, String fileId) {
		return "env=" + env + StringConstants.FOLDER_DELIMITER + "institute=" + instituteId
				+ StringConstants.FOLDER_DELIMITER + INSTITUTE_ONBOARDING_STUDENT_FILE_EXTENSION
				+ StringConstants.FOLDER_DELIMITER + fileId + JSON;
	}

	class StandardSectionStream {

		private final String standardName;
		private final String sectionName;
		private final String streamName;

		public StandardSectionStream(String standardName, String sectionName, String streamName) {
			this.standardName = standardName;
			this.sectionName = sectionName;
			this.streamName = streamName;
		}

		public String getStandardName() {
			return standardName;
		}

		public String getSectionName() {
			return sectionName;
		}

		public String getStreamName() {
			return streamName;
		}

	}
}
