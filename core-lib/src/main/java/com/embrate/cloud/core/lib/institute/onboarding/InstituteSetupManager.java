package com.embrate.cloud.core.lib.institute.onboarding;

import com.embrate.cloud.core.api.onboarding.institute.setup.*;
import com.embrate.cloud.core.lib.institute.onboarding.step.IModuleSetupProcessor;
import com.embrate.cloud.core.lib.institute.onboarding.step.ModuleSetupProcessorRegistry;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.*;
import com.lernen.cloud.core.api.organisation.InstituteConfigurationPayload;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.configs.ConfigurationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.utils.PasswordUtils;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Month;
import java.util.*;

/**
 * <AUTHOR>
 */

public class InstituteSetupManager {
    private static final Logger logger = LogManager.getLogger(InstituteSetupManager.class);

    private static final String INSTITUTE_UNIQUE_CODE = "institute_unique_code";

    private final InstituteManager instituteManager;

    private final ModuleSetupProcessorRegistry moduleSetupProcessorRegistry;

    private final UserManager userManager;

    private final ConfigurationManager configurationManager;


    public InstituteSetupManager(InstituteManager instituteManager, ModuleSetupProcessorRegistry moduleSetupProcessorRegistry, UserManager userManager, ConfigurationManager configurationManager) {
        this.instituteManager = instituteManager;
        this.moduleSetupProcessorRegistry = moduleSetupProcessorRegistry;
        this.userManager = userManager;
        this.configurationManager = configurationManager;
    }

    public InstituteSetupResult setupInstitute(InstituteConfigurationPayload instituteConfigurationPayload) {

        if (instituteConfigurationPayload == null || CollectionUtils.isEmpty(instituteConfigurationPayload.getInstituteSetupInputList())) {
            logger.error("Invalid input for institute setup");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid input"));
        }

        validateInstitutes(instituteConfigurationPayload);

        if (StringUtils.isBlank(instituteConfigurationPayload.getOrganisationName())
                && instituteConfigurationPayload.getInstituteSetupInputList().size() == 1) {
            return setupSingleInstitute(instituteConfigurationPayload);
        } else if (StringUtils.isNotBlank(instituteConfigurationPayload.getOrganisationName())) {
            return setupOrganisation(instituteConfigurationPayload);
        } else {
            logger.error("Invalid request to add multiple institute without organisation {}", instituteConfigurationPayload);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid create organisation request"));
        }
    }

    private InstituteSetupResult setupOrganisation(InstituteConfigurationPayload instituteConfigurationPayload) {
        logger.info("Creating organisation for payload {}", instituteConfigurationPayload);
        UUID organisationId = instituteManager.addOrganisation(instituteConfigurationPayload);
        if (organisationId == null) {
            logger.error("Error creating organisation for payload {}", instituteConfigurationPayload);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to create organisation"));
        }

        Set<Integer> institutes = new HashSet<>();
        Set<Module> modules = new HashSet<>();
        int userInstituteId = 0;
        for (InstituteSetupInput instituteSetupInput : instituteConfigurationPayload.getInstituteSetupInputList()) {
            institutes.add(instituteSetupInput.getInstitute().getInstituteId());
            modules.addAll(instituteSetupInput.getAuthorizedModules());
            if(userInstituteId == 0){
                userInstituteId = instituteSetupInput.getInstitute().getInstituteId();
            }
        }

       
        List<InstituteSetupSummary> instituteSetupSummaryList = new ArrayList<>();
        Pair<String, String> userCreds = createUser(userInstituteId, new ArrayList<>(modules), institutes);
        for (InstituteSetupInput instituteSetupInput : instituteConfigurationPayload.getInstituteSetupInputList()) {
            try {
                logger.info("Configuring institute {}", instituteSetupInput);
                InstituteBasicSetupResult instituteBasicSetupResult = createInstituteAndInitBaseSetup(instituteSetupInput, false);
                InstituteSetupPayload instituteSetupPayload = getInstituteSetupPayload(organisationId, instituteSetupInput, instituteBasicSetupResult, institutes);
                InstituteSetupSummary instituteSetupSummary = configureInstitute(instituteSetupPayload);
                instituteSetupSummaryList.add(instituteSetupSummary);
                logger.info("Done configuring institute {}", instituteSetupInput);
            } catch (Exception e) {
                logger.error("Error while configuring institute {}", instituteSetupInput, e);
            }
        }
       
        return new InstituteSetupResult(true, null, null, new UserCredentials(userCreds.getFirst(), userCreds.getSecond()), instituteSetupSummaryList);
    }

    private InstituteBasicSetupResult createInstituteAndInitBaseSetup(InstituteSetupInput instituteSetupInput, boolean createInstitute){
        int instituteId = instituteSetupInput.getInstitute().getInstituteId();
        if(createInstitute){
            InstitutePayload institutePayload = instituteSetupInput.getInstitute();

            Integer createdInstituteId = instituteManager.addInstitute(InstitutePayload.getInstituteCreationPayload(institutePayload));
            if (createdInstituteId == null) {
                logger.error("Error creating institute for payload {}", instituteSetupInput);
                return null;
            }
        }

        logger.info("Created institute {}",  instituteId);
        createSession(instituteId, instituteSetupInput);
        logger.info("Created session for {}",  instituteId);

        AcademicSession latestSession = instituteManager.getLatestSessionDetails(instituteId);

        createStandards(instituteSetupInput, instituteId, latestSession);
        logger.info("Created standards for {}",  instituteId);
        List<Standard> standards = instituteManager.getInstituteStandardDetails(instituteId, latestSession.getAcademicSessionId());
        return new InstituteBasicSetupResult(instituteId, latestSession, standards);
    }
    private InstituteSetupResult setupSingleInstitute(InstituteConfigurationPayload instituteConfigurationPayload) {
        logger.info("Creating only institute. Skipping organisation for payload {}", instituteConfigurationPayload);
        InstituteSetupInput instituteSetupInput = instituteConfigurationPayload.getInstituteSetupInputList().get(0);
        int instituteId = instituteSetupInput.getInstitute().getInstituteId();
        InstituteBasicSetupResult instituteBasicSetupResult = createInstituteAndInitBaseSetup(instituteSetupInput, true);
        InstituteSetupPayload instituteSetupPayload = getInstituteSetupPayload(null, instituteSetupInput,  instituteBasicSetupResult, new HashSet<>(Arrays.asList(instituteId)));
        Pair<String, String> userCreds = createUser(instituteId, instituteSetupInput.getAuthorizedModules(), null);

        InstituteSetupSummary instituteSetupSummary = configureInstitute(instituteSetupPayload);
    
        return new InstituteSetupResult(true, null, null, new UserCredentials(userCreds.getFirst(), userCreds.getSecond()), Arrays.asList(instituteSetupSummary));
    }

    private InstituteSetupPayload getInstituteSetupPayload(UUID organisationId, InstituteSetupInput instituteSetupInput,
                                                                  InstituteBasicSetupResult instituteBasicSetupResult, Set<Integer> inventoryInstitutes) {
        ConfigInput configInput = instituteSetupInput.getConfigs();
        InstituteSetupPayload instituteSetupPayload = new InstituteSetupPayload(organisationId,
                instituteBasicSetupResult,
                instituteSetupInput.getInstituteSessionPayloadList(),
                configInput, inventoryInstitutes, instituteSetupInput.getAuthorizedModules());
        return instituteSetupPayload;
    }

    private void validateInstitutes(InstituteConfigurationPayload instituteConfigurationPayload) {
        Set<Integer> institutes = new HashSet<>();
        List<String> instituteUinqiueCodeList = new ArrayList<>();
        for (InstituteSetupInput instituteSetupInput : instituteConfigurationPayload.getInstituteSetupInputList()) {
            InstitutePayload institute = instituteSetupInput.getInstitute();
            if (institute == null || institute.getInstituteId() <= 0 || StringUtils.isBlank(institute.getInstituteName())) {
                logger.error("Invalid institute {}", institute);
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute"));
            }
            Institute existingInstitute = instituteManager.getInstitute(institute.getInstituteId());

            if (existingInstitute != null) {
                logger.error("Institute {} already exists", institute.getInstituteId());
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Institute already exists"));
            }
            if (institutes.contains(institute.getInstituteId())) {
                logger.error("Duplicate institutes present {}", instituteSetupInput);
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Institute duplicated"));
            }
            if(instituteUinqiueCodeList.contains(instituteSetupInput.getConfigs().getInstituteUniqueCode())){
                logger.error("Duplicate institute unique code present in the payload itself {}", instituteSetupInput.getConfigs().getInstituteUniqueCode());
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Duplicate Institute Unique Code present in payload itself: " + instituteUinqiueCodeList));
            }
            instituteUinqiueCodeList.add(instituteSetupInput.getConfigs().getInstituteUniqueCode());
        }
        validateInstituteUniqueCode(instituteUinqiueCodeList);
    }

    public void validateInstituteUniqueCode(List<String> instituteUinqiueCodeList){
        if (CollectionUtils.isEmpty(instituteUinqiueCodeList)) return;
        List<String> existingInstituteCodeUniqueList = configurationManager.configValueList(INSTITUTE_UNIQUE_CODE, instituteUinqiueCodeList);
        List<String> duplicateCode = new ArrayList<>();
        if (CollectionUtils.isEmpty(existingInstituteCodeUniqueList)) return;
        for (String instituteUniqueCode : instituteUinqiueCodeList) {
            if(existingInstituteCodeUniqueList.contains(instituteUniqueCode)){
                duplicateCode.add(instituteUniqueCode);
            }
        }
        if (!CollectionUtils.isEmpty(duplicateCode)){
            logger.error("Duplicate institute unique code present {}", instituteUinqiueCodeList);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Duplicate Institute Unique Code present: " + duplicateCode));
        }
    }

    private InstituteSetupSummary configureInstitute(InstituteSetupPayload instituteSetupPayload) {
        int instituteId = instituteSetupPayload.getInstituteBasicSetupResult().getInstituteId();
        List<InstituteModuleSetupSummary> instituteModuleSetupSummaryList = new ArrayList<>();
        int totalModuleCount = 0;
        int successModuleCount = 0;
        int errorModuleCount = 0;
        for (IModuleSetupProcessor moduleSetupProcessor : moduleSetupProcessorRegistry.getProcessors()) {
            Module module = moduleSetupProcessor.getModule();
            logger.info("Running processor for institute {}, module {}", instituteId, module);
            List<IConfigureInstituteStep> instituteStepList = moduleSetupProcessor.getSteps(instituteSetupPayload);
            if (CollectionUtils.isEmpty(instituteStepList)) {
                logger.warn("No status step found for institute {}, module {}", instituteId, module);
                continue;
            }
            totalModuleCount++;
            InstituteModuleSetupSummary instituteModuleSetupSummary = runModuleSteps(instituteId, module, instituteStepList);
            instituteModuleSetupSummaryList.add(instituteModuleSetupSummary);

            if(instituteModuleSetupSummary.getFailedSteps() > 0){
                errorModuleCount++;
            }else{
                successModuleCount++;
            }
        }

        return new InstituteSetupSummary(instituteSetupPayload.getInstituteBasicSetupResult().getInstituteId(), totalModuleCount, successModuleCount, errorModuleCount, instituteModuleSetupSummaryList);
    }

    private void createStandards(InstituteSetupInput instituteSetupInput, Integer instituteId, AcademicSession latestSession) {
        for (InstituteStandardPayload instituteStandardPayload : instituteSetupInput.getStandards()) {
            Standard standard = new Standard();
            standard.setInstituteId(instituteId);
            standard.setAcademicSessionId(latestSession.getAcademicSessionId());
            standard.setStandardName(instituteStandardPayload.getStandardName().trim());
            standard.setStream(instituteStandardPayload.getStream());
            standard.setLevel(instituteStandardPayload.getLevel());

            List<StandardSections> standardSections = new ArrayList<>();
            if (!CollectionUtils.isEmpty(instituteStandardPayload.getSections())) {
                for (String sectionName : instituteStandardPayload.getSections()) {
                    standardSections.add(new StandardSections(0, sectionName));
                }
            }

            standard.setStandardSectionList(standardSections);
            instituteManager.addInstituteStandard(standard);
        }
    }

    private Pair<String, String> createUser(Integer instituteId, List<Module> authorizedModules, Set<Integer> institutes) {
        String userName = "admin"+instituteId+"@embrate";
        String password = PasswordUtils.generatePassword(10);

        User user = new User();
//        user.setUserInstituteId(UUID.randomUUID().toString());
        user.setUserInstituteId("System User");
        user.setInstituteId(instituteId);
        user.setFirstName("Admin");
        user.setLastName("");
        user.setUserType(UserType.INTERNAL);
        user.setUserName(userName);
        user.setPassword(password);
        user.setBirthDate(System.currentTimeMillis() - 3600 * 24 * 30 * 12 * 1000l);
        user.setEnrollmentDate(System.currentTimeMillis());

//        user.setGender(Gender.FEMALE);
        user.setAuthorizedModules(authorizedModules);
        if(institutes != null){
            user.setInstituteScope(new ArrayList<>(institutes));
        }

        final User createdUser = userManager.createUser(user, null);
        if(createdUser == null){
            logger.error("Unable to create user for institute {}",  instituteId);
            return null;
        }
        return new Pair<>(userName, password);
    }

    private InstituteModuleSetupSummary runModuleSteps(int instituteId, Module module, List<IConfigureInstituteStep> instituteStepList) {
        List<InstituteModuleSetupSummary> instituteModuleSetupSummaryList = new ArrayList<>();
        int totalCount = 0;
        int successCount = 0;
        int errorCount = 0;
        List<InstituteSetupStepStatus> setupStepStatusList = new ArrayList<>();
        for (IConfigureInstituteStep instituteSetupStep : instituteStepList) {
            String stepName = instituteSetupStep.getName();
            totalCount++;
            logger.info("Executing step {}, institute {}", stepName, instituteId);
            String errorReason = null;
            try {
                boolean success = instituteSetupStep.execute();
                if (success) {
                    logger.info("Successfully executed step {}, institute {}", stepName, instituteId);
                    successCount++;
                } else {
                    logger.error("Error executing step {}, institute {}", stepName, instituteId);
                    errorReason = "Failed to execute";
                    errorCount++;
                }
            } catch (Exception e) {
                logger.error("Error while executing step {}", stepName);
                errorReason = "Exception during execution";
                errorCount++;
            }
            logger.info("Done executing step {}, institute {}", stepName, instituteId);
            setupStepStatusList.add(new InstituteSetupStepStatus(stepName, errorReason == null, errorReason));
        }
        return new InstituteModuleSetupSummary(module, totalCount, successCount, errorCount, setupStepStatusList);
    }

    private boolean createSession(int instituteId, InstituteSetupInput instituteSetupInput) {
        boolean success = true;
        for (InstituteSessionPayload instituteSessionPayload : instituteSetupInput.getInstituteSessionPayloadList()) {
            success &= instituteManager.addInstituteSession(new AcademicSession(instituteId,
                    0, instituteSessionPayload.getStartYear(), instituteSessionPayload.getEndYear(),
                    Month.of(instituteSessionPayload.getStartMonth()), Month.of(instituteSessionPayload.getEndMonth()), null), 0, true, null);
        }
        return success;
    }


    public InstituteConfigurationPayload getSampleInstituteConfigurationPayload() {
        InstituteConfigurationPayload instituteConfigurationPayload = new InstituteConfigurationPayload();
        InstituteSetupInput instituteSetupInput  = new InstituteSetupInput();

        Map<InstituteMetadataVariables, String> instituteMetadataVariablesMap = new HashMap<>();
        for(InstituteMetadataVariables instituteMetadataVariables : InstituteMetadataVariables.values()) {
            instituteMetadataVariablesMap.put(instituteMetadataVariables, "");
        }

		InstitutePayload institute = new InstitutePayload(10205, "SchoolName",
                null, null, null,"WARD NO 40 RADHAKRISHANPURA",
                "", "SIKAR", "RAJASTHAN", "INDIA", "332001", null,
                null, null,null, null, instituteMetadataVariablesMap);

        instituteSetupInput.setInstitute(institute);
        InstituteSessionPayload instituteSessionPayload = new InstituteSessionPayload();
        instituteSessionPayload.setStartYear(2022);
        instituteSessionPayload.setEndYear(2023);
        instituteSessionPayload.setStartMonth(4);
        instituteSessionPayload.setEndMonth(3);
        instituteSetupInput.setInstituteSessionPayloadList(Arrays.asList(instituteSessionPayload));

        List<InstituteStandardPayload> instituteStandardPayloadList = new ArrayList<>();
        instituteStandardPayloadList.add(new InstituteStandardPayload("LKG", Stream.NA, 1, Arrays.asList("A", "B")));
        instituteStandardPayloadList.add(new InstituteStandardPayload("1st", Stream.NA, 2, Arrays.asList()));
        instituteStandardPayloadList.add(new InstituteStandardPayload("12th", Stream.SCIENCE, 15, Arrays.asList()));
        instituteStandardPayloadList.add(new InstituteStandardPayload("12th", Stream.COMMERCE, 16, Arrays.asList("A", "B")));
        instituteSetupInput.setStandards(instituteStandardPayloadList);

        instituteSetupInput.setAuthorizedModules(Arrays.asList(Module.ADMISSION, Module.FEES, Module.TRANSPORT, Module.EXAMINATION, Module.COURSES, Module.STORE));

        ConfigInput configInput = new ConfigInput();
        configInput.setInstituteNameInSMS("SRICGN");
        configInput.setInstituteUniqueCode("sric205");

        configInput.setAdmissionCounterEnabled(false);
        configInput.setAdmissionCounterPrefix("A-");

        configInput.setRegistrationCounterEnabled(true);
        configInput.setRegistrationCounterPrefix("R-");

        configInput.setStaffCounterEnabled(true);
        configInput.setStaffCounterPrefix("S-");

        configInput.setFeeInvoiceCounterPrefix("I-");

        configInput.setTransferCertificateCounterEnabled(true);
        configInput.setTransferCertificateCounterPrefix("TC-");

        configInput.setSmsServiceEnabled(false);
        
        configInput.setAccessionNumberEnabled(false);
        configInput.setAccessionNumberCounter(false);

        configInput.setAccessionNumberCounterPrefix("");



        instituteSetupInput.setConfigs(configInput);

        instituteConfigurationPayload.setInstituteSetupInputList(Arrays.asList(instituteSetupInput));

        return instituteConfigurationPayload;
    }
}
