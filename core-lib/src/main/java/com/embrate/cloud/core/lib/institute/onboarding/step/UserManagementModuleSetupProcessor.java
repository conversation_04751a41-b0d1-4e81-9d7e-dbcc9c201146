package com.embrate.cloud.core.lib.institute.onboarding.step;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import com.embrate.cloud.core.api.onboarding.institute.setup.InstituteBasicSetupResult;
import com.embrate.cloud.core.api.onboarding.institute.setup.InstituteSetupInput;
import com.embrate.cloud.core.api.onboarding.institute.setup.InstituteSetupPayload;
import com.embrate.cloud.core.lib.institute.onboarding.IConfigureInstituteStep;
import com.embrate.cloud.core.lib.institute.onboarding.InstituteSetupManager;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.examination.ExamDimension;
import com.lernen.cloud.core.api.examination.ExamDimensionType;
import com.lernen.cloud.core.api.examination.ExamEvaluationType;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.permissions.RoleModuleDataPayload;
import com.lernen.cloud.core.api.permissions.UserRoleMapping;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.dao.tier.permissions.UserPermissionDao;

public class UserManagementModuleSetupProcessor implements IModuleSetupProcessor  {

    private static final Logger logger = LogManager.getLogger(UserManagementModuleSetupProcessor.class);
    
    private final UserPermissionManager userPermissionManager;

    private final UserManager userManager;

    private static String ROLE_NANE = "Admin";

    private static String ROLE_DESCRIPTION = "This is Admin Role";
    
    public UserManagementModuleSetupProcessor(UserPermissionManager userPermissionManager, UserManager userManager){
        this.userPermissionManager = userPermissionManager;
        this.userManager = userManager;
    }
    
    @Override
    public Module getModule() {
        return Module.USER_MANAGEMENT;
    }

    @Override
    public List<IConfigureInstituteStep> getSteps(InstituteSetupPayload instituteSetupPayload) {
        int instituteId = instituteSetupPayload.getInstituteBasicSetupResult().getInstituteId();
        List<Module> moduleList = instituteSetupPayload.getAuthorizedModules();

        List<IConfigureInstituteStep> instituteSetupStepsList = new ArrayList<>();
        instituteSetupStepsList.add(createAndAssignRoleStep(instituteId, moduleList));

        return instituteSetupStepsList;
    }

     private IConfigureInstituteStep createAndAssignRoleStep(int instituteId, List<Module> moduleList) {
        return new IConfigureInstituteStep() {
            @Override
            public String getName() {
                return "Create Admin Role and Assign to Internal User";
            }

            @Override
            public boolean execute() {
                List<AuthorisationRequiredAction> authorisationRequiredActionsList = userPermissionManager.getAuthorisationRequiredActionList(instituteId, moduleList);
                if(CollectionUtils.isEmpty(authorisationRequiredActionsList)){
                    logger.error("Authorisation Required Action is Empty for institute {}", instituteId);
                    return false;
                }
                RoleModuleDataPayload roleModuleDataPayload = new RoleModuleDataPayload(instituteId, null, ROLE_NANE, moduleList, authorisationRequiredActionsList, true, ROLE_DESCRIPTION);
                final UUID roleId = userPermissionManager.addRole(instituteId, roleModuleDataPayload);
                if (roleId == null) {
                    logger.error("Error in Creating Role for {}", instituteId);
                    return false;
                }
                List<User> users = userManager.getDefaultInternalUser(instituteId);
                if(CollectionUtils.isEmpty(users)){
                    logger.error("No Internal User Found for {}", instituteId);
                }
                User user = users.get(0);
                /* This check is specific to when multiple institutes are created i.e when an organisation is created, 
                in that case role which is getting assign to an user should belong to user's institute, 
                if it does not we are returning from here */
                if(user.getInstituteId() != instituteId){
                    return true;
                }
                /* Assuming that there is only one user for this institute */
                return userPermissionManager.updateUserRoleMapping(instituteId, user.getUuid(), Arrays.asList(roleId), user.getUuid());

            }
        };
    }
}
