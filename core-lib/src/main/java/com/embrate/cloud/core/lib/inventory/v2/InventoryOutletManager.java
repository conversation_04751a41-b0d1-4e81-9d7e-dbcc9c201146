package com.embrate.cloud.core.lib.inventory.v2;

import com.embrate.cloud.core.api.inventory.v2.*;
import com.embrate.cloud.core.api.inventory.v2.brand.InventoryBrand;
import com.embrate.cloud.core.api.inventory.v2.category.InventoryCategory;
import com.embrate.cloud.core.api.inventory.v2.outlet.InventoryOutlet;
import com.embrate.cloud.core.api.inventory.v2.supplier.InventorySupplier;
import com.embrate.cloud.dao.tier.inventory.v2.InventoryOutletDao;
import com.google.common.base.Joiner;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.inventory.Color;
import com.lernen.cloud.core.api.inventory.ProductProperties;
import com.lernen.cloud.core.api.inventory.ProductVariationsGroup;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.constants.StoreInventoryConstants;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.WordUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 */

public class InventoryOutletManager {
    private static final Logger logger = LogManager.getLogger(InventoryOutletManager.class);

    private final InventoryOutletDao inventoryOutletDao;
    private final StudentManager studentManager;
    private final StaffManager staffManager;
    private final UserPreferenceSettings userPreferenceSettings;

    public InventoryOutletManager(InventoryOutletDao inventoryOutletDao, StudentManager studentManager,
                                  StaffManager staffManager, UserPreferenceSettings userPreferenceSettings) {
        this.inventoryOutletDao = inventoryOutletDao;
        this.studentManager = studentManager;
        this.staffManager = staffManager;
        this.userPreferenceSettings = userPreferenceSettings;
    }

    public UUID addOutlet(InventoryOutlet outlet) {
        return inventoryOutletDao.addOutlet(outlet);
    }

    public InventoryOutlet getOutlet(int instituteId) {
        return InventoryUtils.getOutlet(instituteId, inventoryOutletDao, userPreferenceSettings);
    }

    public UUID getOutletId(int instituteId) {
        return InventoryUtils.getOutletId(instituteId, inventoryOutletDao, userPreferenceSettings);
    }

    public UUID addSupplier(int instituteId, InventorySupplier supplier) {
        if (supplier == null || instituteId <= 0) {
            logger.error("Invalid payload institute {}, supplier {}", instituteId, supplier);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SELLER, " Invalid request"));
        }
        UUID outletId = getOutletId(instituteId);
        validateSupplier(supplier);
        return inventoryOutletDao.addSupplier(outletId, supplier);
    }

    private void validateSupplier(InventorySupplier supplier) {
        if (StringUtils.isBlank(supplier.getSupplierName())) {
            logger.error("Blank supplier name");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SELLER, "Supplier name cannot be blank."));
        }

        if (StringUtils.isBlank(supplier.getContactName())) {
            logger.error("Blank contact name");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SELLER, "Contact name cannot be blank."));
        }

        if (StringUtils.isBlank(supplier.getAddress())) {
            logger.error("Blank seller address");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SELLER, "Address cannot be blank."));
        }
        if (StringUtils.isBlank(supplier.getCity())) {
            logger.error("Blank seller city.");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SELLER, "City cannot be blank."));
        }
        if (StringUtils.isBlank(supplier.getState())) {
            logger.error("Blank state");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SELLER, "State cannot be blank."));
        }

        // if (StringUtils.isBlank(vendor.getZipcode())) {
        // System.out.println("blank zip");
        // throw new ApplicationException(
        // new ErrorResponse(ApplicationErrorCode.INAVLID_SELLER, "Zip Code be
        // blank."));
        // } else if
        // (!vendor.getZipcode().matches(StoreInventoryConstants.VALID_SIX_DIGIT_ZIP))
        // {
        // System.out.println("invalid zip");
        // throw new ApplicationException(new
        // ErrorResponse(ApplicationErrorCode.INAVLID_SELLER,
        // "Zip code is not valid. Kindly enter 6 digit zip code."));
        // }

        if (StringUtils.isNotBlank(supplier.getEmail())
                && !supplier.getEmail().matches(StoreInventoryConstants.VALID_EMAIL_PATTERN)) {
            logger.error("invalid email");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SELLER, "Invalid Email Address"));
        }

        if (StringUtils.isNotBlank(supplier.getPrimaryPhoneNumber())
                && !supplier.getPrimaryPhoneNumber().matches(StoreInventoryConstants.VALID_PHONE_NUMBER_PATTERN)) {
            logger.error("invalid primary phone number");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SELLER,
                    "Invalid Primary Phone Number, Kindly add 10 digit phone number."));
        }
        if (StringUtils.isNotBlank(supplier.getSecondPhoneNumber())
                && !supplier.getSecondPhoneNumber().matches(StoreInventoryConstants.VALID_PHONE_NUMBER_PATTERN)) {
            logger.error("invalid secondary phone number");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SELLER,
                    "Invalid Secondary Phone Number, Kindly add 10 digit phone number."));
        }
    }

    public InventorySupplier getSupplier(int instituteId, UUID supplierId) {
        return inventoryOutletDao.getSupplier(getOutletId(instituteId), supplierId);
    }

    public List<InventorySupplier> getSuppliers(int instituteId) {
        return inventoryOutletDao.getSuppliers(getOutletId(instituteId));
    }

    public List<InventorySupplier> searchSupplier(int instituteId, String searchText) {
        return inventoryOutletDao.searchSupplier(getOutletId(instituteId), searchText);
    }

    public boolean deleteSupplier(int instituteId, UUID supplierId) {
        return inventoryOutletDao.deleteSupplier(getOutletId(instituteId), supplierId);
    }

    public Boolean updateSupplier(int instituteId, InventorySupplier supplier) {
        if (supplier == null || instituteId <= 0 || supplier.getSupplierId() == null) {
            logger.error("Invalid payload institute {}, supplier {}", instituteId, supplier);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SELLER, " Invalid request"));
        }
        UUID outletId = getOutletId(instituteId);
        validateSupplier(supplier);
        return inventoryOutletDao.updateSupplier(outletId, supplier);
    }

    public boolean addCategory(int instituteId, InventoryCategory category) {
        if (category == null || instituteId <= 0 || StringUtils.isBlank(category.getCategoryName())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_CATEGORY, " Invalid Category Data"));
        }
        return inventoryOutletDao.addCategory(getOutletId(instituteId), category);
    }

    public boolean addCategory(UUID outletId, InventoryCategory category) {
        if (category == null || outletId == null || StringUtils.isBlank(category.getCategoryName())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_CATEGORY, " Invalid Category Data"));
        }
        return inventoryOutletDao.addCategory(outletId, category);
    }


    public boolean updateCategory(int instituteId, InventoryCategory category) {
        if (category == null || instituteId <= 0 || category.getCategoryId() <= 0 || StringUtils.isBlank(category.getCategoryName())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_CATEGORY, " Invalid Category Data"));
        }
        return inventoryOutletDao.updateCategory(getOutletId(instituteId), category);
    }

    public List<InventoryCategory> getCategories(int instituteId) {
        return inventoryOutletDao.getCategories(getOutletId(instituteId));
    }

    public boolean deleteCategory(int instituteId, int categoryId) {
        return inventoryOutletDao.deleteCategory(getOutletId(instituteId), categoryId);
    }

    public UUID addBrand(int instituteId, InventoryBrand brand) {
        if (brand == null || instituteId <= 0 || StringUtils.isBlank(brand.getBrandName())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_BRAND, "Invalid Brand Data"));
        }
        return inventoryOutletDao.addBrand(getOutletId(instituteId), brand);
    }

    public boolean updateBrand(int instituteId, InventoryBrand brand) {
        if (brand == null || instituteId <= 0 || StringUtils.isBlank(brand.getBrandName()) || brand.getBrandId() == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_BRAND, "Invalid Brand Data"));
        }
        return inventoryOutletDao.updateBrand(getOutletId(instituteId), brand);
    }

    public List<InventoryBrand> searchBrands(int instituteId, String searchText) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }
        return inventoryOutletDao.searchBrands(getOutletId(instituteId), searchText);
    }

    public boolean deleteBrand(int instituteId, UUID brandId) {
        if (instituteId <= 0 || brandId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }
        return inventoryOutletDao.deleteBrand(getOutletId(instituteId), brandId);
    }


    public boolean insertCounter(UUID outletId, InventoryCounterData inventoryCounterData) {
        return inventoryOutletDao.insertCounter(outletId, inventoryCounterData);
    }

    public SearchResultWithPagination<StudentLite> searchInventoryStudents(int instituteId, String searchText, Set<StudentStatus> studentStatuses) {

        InventoryOutlet outlet = getOutlet(instituteId);
        if (outlet == null) {
            logger.error("No outlet configured for institute {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVENTORY_NOT_ENABLED, "Inventory not enabled for your institute"));
        }
        if (CollectionUtils.isEmpty(outlet.getInstituteScope())) {
            logger.error("Outlet not configured for any institute. {}", outlet);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVENTORY_NOT_ENABLED, "Inventory not enabled for your institute"));
        }

        final List<Student> studentsWithoutAcademicSession = studentManager.searchStudentsWithoutAcademicSesison(
                outlet.getInstituteScope(), searchText, studentStatuses);

        if (studentsWithoutAcademicSession == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(studentsWithoutAcademicSession)) {
            return new SearchResultWithPagination<>(null, new ArrayList<>());
        }

        final List<StudentLite> studentLiteList = new ArrayList<>();
        for (final Student student : studentsWithoutAcademicSession) {
            studentLiteList.add(Student.getStudentLite(student));
        }

        return new SearchResultWithPagination<>(null, studentLiteList);
    }

    public SearchResultWithPagination<Staff> searchInventoryStaff(int instituteId, String searchText) {
        /**
         * Implement based on need similar to
         * com.lernen.cloud.core.lib.inventory.StoreInventoryManager.searchInventoryStudents(int,
         * String, Set<StudentStatus>)
         */
        throw new NotImplementedException("Staff not supported in inventory");
    }

}
