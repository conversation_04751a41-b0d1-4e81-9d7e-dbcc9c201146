/**
 * 
 */
package com.embrate.cloud.core.lib.lecture;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.util.CollectionUtils;

import com.embrate.cloud.core.api.lecture.LectureDetails;
import com.embrate.cloud.core.api.lecture.LectureReportType;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.utils.DateUtils;

/**
 * <AUTHOR>
 *
 */
public class LectureReportGenerator {
	
	private static final Logger logger = LogManager.getLogger(LectureReportGenerator.class);

	private final LectureManager lectureManager;
	
	private static final int COLUMN_WIDTH_PADDING = 4;

	private static final String[] BROADCASTED_LECTURE_REPORT = { "Sr No.", "Broadcasted Date", "Class", "Course", 
			"Chapter", "Title", "Lecturer Name", "Due Date", "Video URL", "Saved Date", "Description" }; 
	
	private static final String NA = "NA";
	
	public LectureReportGenerator(LectureManager lectureManager) {
		this.lectureManager = lectureManager;
	}

	public static void main(String[] args) {

	}

	public ReportOutput generateReport(int instituteId, int academicSessionId, LectureReportType lectureReportType,
			UUID standardId, UUID courseId, int start, int end) {
		switch (lectureReportType) {
		case BROADCASTED_LECTURE_REPORT:
			return generateBroadcastedLectureReport(instituteId, academicSessionId, lectureReportType, standardId, courseId, start, end);
		default:
			break;
		}
		return null;
	}

	private ReportOutput generateBroadcastedLectureReport(int instituteId, int academicSessionId,
			LectureReportType lectureReportType, UUID standardId, UUID courseId, int start, int end) {
		final Workbook workbook = new HSSFWorkbook();
		final List<Integer> columnWidths = new ArrayList<>();

		final Font headerFont = workbook.createFont();
		headerFont.setBold(true);
		headerFont.setFontHeightInPoints((short) 14);
		headerFont.setColor(IndexedColors.BLACK.getIndex());

		// Create a CellStyle with the font
		final CellStyle headerCellStyle = workbook.createCellStyle();
		headerCellStyle.setFont(headerFont);

		// Create a Sheet
		final Sheet sheet = workbook.createSheet("Report");

		final String reportName = lectureReportType.name() + ".xls";
		
		// Create a Row
		final Row headerRow = sheet.createRow(0);
		// Create cells
		int i = 0;
		for (i = 0; i < BROADCASTED_LECTURE_REPORT.length; i++) {
			final Cell cell = headerRow.createCell(i);
			cell.setCellValue(BROADCASTED_LECTURE_REPORT[i]);
			cell.setCellStyle(headerCellStyle);
			columnWidths.add(BROADCASTED_LECTURE_REPORT[i].length());
		}
		try {	
			
			List<LectureDetails> lectureDetailsList= lectureManager.getBroadcastedLectureDetails(instituteId,
					academicSessionId, standardId, start, end, courseId);
						
			if (CollectionUtils.isEmpty(lectureDetailsList)) {
				logger.info("Empty report {}", reportName);
				return getReportOutput(workbook, reportName);
			}

			int rowNum = 1;
			int count = 1;
			for(final LectureDetails lectureDetails : lectureDetailsList) {
				final Row row = sheet.createRow(rowNum++);
				int colNum = 0;
					
				createCell(row, colNum++, String.valueOf(count), columnWidths);
					
				createCell(row, colNum++, lectureDetails.getBroadcastedAt() == null ? NA :
					DateUtils.getFormattedDate(lectureDetails.getBroadcastedAt()), columnWidths);
				
				createCell(row, colNum++, lectureDetails.getStandard().getStandardName(),
						columnWidths);
				
				createCell(row, colNum++, lectureDetails.getCourse().getCourseName(),
						columnWidths);
				
				createCell(row, colNum++, lectureDetails.getChapter(), columnWidths);
					
				createCell(row, colNum++, lectureDetails.getTitle(), columnWidths);
					
				createCell(row, colNum++, lectureDetails.getLecturerName(), columnWidths);
				
				createCell(row, colNum++, lectureDetails.getRecommendedViewDate() == null ? NA :
						DateUtils.getFormattedDate(lectureDetails.getRecommendedViewDate()), columnWidths);
				
				createCell(row, colNum++, lectureDetails.getVideoLink(), columnWidths);
				
				createCell(row, colNum++, lectureDetails.getSavedAt() == null ? NA :
						DateUtils.getFormattedDate(lectureDetails.getSavedAt()), columnWidths);
					
				createCell(row, colNum++, lectureDetails.getDescription(), columnWidths);
				
				count++;
			}
			resizeSheet(sheet, columnWidths);
			return getReportOutput(workbook, reportName);
		} catch (final Exception e) {
			logger.error("Error while generating {} report", reportName, e);
		}

		return null;
	}

	private ReportOutput getReportOutput(Workbook workbook, String reportName) throws IOException {
		final ByteArrayOutputStream bos = new ByteArrayOutputStream();
		workbook.write(bos);
		workbook.close();
		return new ReportOutput(reportName, bos);
	}

	private void createCell(Row row, int colNum, String value, final List<Integer> columnWidths) {
		updateColumnWidth(columnWidths, colNum, value);
		row.createCell(colNum).setCellValue(value);
	}

	private void createCell(Row row, int colNum, Double value, final List<Integer> columnWidths) {
		updateColumnWidth(columnWidths, colNum, value);
		row.createCell(colNum).setCellValue(value);
	}

	private void createCell(Row row, int colNum, Integer value, final List<Integer> columnWidths) {
		updateColumnWidth(columnWidths, colNum, value);
		row.createCell(colNum).setCellValue(value);
	}

	private void updateColumnWidth(final List<Integer> columnWidths, int colNum, Object value) {
		final int charLength = value == null ? 0 : String.valueOf(value).length();
		if (columnWidths.get(colNum) < charLength) {
			columnWidths.set(colNum, charLength);
		}
	}

	private void resizeSheet(final Sheet sheet, final List<Integer> columnWidths) {
		for (int i = 0; i < columnWidths.size(); i++) {
			int min = Math.min(255, columnWidths.get(i) + COLUMN_WIDTH_PADDING);
			sheet.setColumnWidth(i, min * 256);
		}
	}
}
