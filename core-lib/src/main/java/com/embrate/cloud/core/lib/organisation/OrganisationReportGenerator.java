package com.embrate.cloud.core.lib.organisation;

import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fee.discount.DiscountStructureAmountMetadata;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.organisation.Organisation;
import com.lernen.cloud.core.api.organisation.OrganisationReportType;
import com.lernen.cloud.core.api.report.*;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentInsightManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.reports.ReportGenerator;
import com.lernen.cloud.core.utils.NumberUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @created_at 05/04/24 : 16:35
 **/
public class OrganisationReportGenerator extends ReportGenerator {

    private static final Logger logger = LogManager.getLogger(OrganisationReportGenerator.class);

    private static final String[] DISCOUNT_SUMMARY_REPORT = {"S.No", "Structure Name", "Total Amount"};

    private final InstituteManager instituteManager;
    private final UserManager userManager;
    private final UserPermissionManager userPermissionManager;
    private final FeePaymentInsightManager feePaymentInsightManager;


    public OrganisationReportGenerator(InstituteManager instituteManager, UserManager userManager, UserPermissionManager userPermissionManager, FeePaymentInsightManager feePaymentInsightManager) {
        this.instituteManager = instituteManager;
        this.userManager = userManager;
        this.userPermissionManager = userPermissionManager;
        this.feePaymentInsightManager = feePaymentInsightManager;
    }

    public ReportDetails generateReport(UUID organisationId, OrganisationReportType organisationReportType, Integer selectedAcademicSessionId, String studentStatusCSV, UUID userId, DownloadFormat downloadFormat) {

        if (organisationId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute Id"));
        }
        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid User Id"));
        }

        User user = userManager.getUser(userId);
        if (user == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid User"));
        }

        int instituteId = user.getInstituteId();

        if(downloadFormat == DownloadFormat.EXCEL) {
//            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_REPORTS, false)) {
//                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
//                        "You don't have access to download fees reports in excel!"));
//            }
        } else if (downloadFormat == DownloadFormat.PDF) {
//            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_PDF_REPORTS, false)) {
//                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
//                        "You don't have access to download fees reports in pdf!"));
//            }
        }

        final Set<StudentStatus> studentStatuses = getStudentStatus(studentStatusCSV);

        if (selectedAcademicSessionId == null || selectedAcademicSessionId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid session Id"));
        }


        /**
         * fetching organisation, instituteIdSessionIdMap in this method as it must be used in
         * future reports too, if that is not the case move it inside individual methods
         */
        Organisation organisation = instituteManager.getOrganizationById(organisationId);
        if (organisation == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid organisation"));
        }

        AcademicSession selectedAcademicSession = instituteManager.getAcademicSessionByAcademicSessionId(selectedAcademicSessionId);
        //institute id, session id
        Map<Integer, Integer> instituteIdSessionIdMap = instituteManager.getAllInstituteSessionDetailsBySelectedSession(organisation, user, selectedAcademicSession);


        switch (organisationReportType) {
            case DISCOUNT_SUMMARY_REPORT:
//                userPermissionManager.verifyAuthorisation(userId, AuthorisationRequiredAction.DISCOUNT_SUMMARY_REPORT, true);
                return generateOrganisationDiscountSummaryReport(organisation, selectedAcademicSession, instituteIdSessionIdMap, studentStatuses);
            default:
                return null;
        }
    }

    private ReportDetails generateOrganisationDiscountSummaryReport(Organisation organisation, AcademicSession selectedAcademicSession, Map<Integer, Integer> instituteIdSessionIdMap, Set<StudentStatus> studentStatusSet) {

        try {
            final String reportName = "Organisation Discount Summary Report";
            final String sheetName = "OrganisationDiscountSummaryReport";

            Map<Integer, List<DiscountStructureAmountMetadata>> instituteDiscountStructureAmountMetadataMap = new HashMap<>();
            for (Map.Entry<Integer, Integer> instituteIdSessionIdEntry : instituteIdSessionIdMap.entrySet()) {
                Integer instituteId = instituteIdSessionIdEntry.getKey();
                Integer academicSessionId = instituteIdSessionIdEntry.getValue();
                if(instituteId == null || academicSessionId == null || instituteId <= 0 || academicSessionId <= 0) {
                    continue;
                }
                List<DiscountStructureAmountMetadata> discountStructureAmountMetadataList = feePaymentInsightManager.getDiscountStructureAmountMetadata(instituteId, academicSessionId, null, studentStatusSet, true);
                instituteDiscountStructureAmountMetadataMap.put(instituteId, discountStructureAmountMetadataList);
            }

            List<DiscountStructureAmountMetadata> discountStructureAmountMetadataList = mergeOrganisationDiscountStructureBySameName(instituteDiscountStructureAmountMetadataMap);

            Collections.sort(discountStructureAmountMetadataList);

            if (CollectionUtils.isEmpty(instituteDiscountStructureAmountMetadataMap)) {
                logger.error("Empty report {}", reportName);
                List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
                ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null, null);
                reportSheetDetailsList.add(reportSheetDetails);
                return new ReportDetails(reportName, reportSheetDetailsList);
            }

            List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
            List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
            String heading = selectedAcademicSession == null ? reportName : reportName + " (" + selectedAcademicSession.getDisplayName() + ") ";
            reportHeaderRow.add(new ReportCellDetails(heading, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
            headerReportCellDetails.add(reportHeaderRow);
            List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
            for (int i = 0; i < DISCOUNT_SUMMARY_REPORT.length; i++) {
                reportCellDetailsHeaderRow.add(new ReportCellDetails(DISCOUNT_SUMMARY_REPORT[i], STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            }

            List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();
            CellIndexes cellIndexes = new CellIndexes(0, 0, 0, DISCOUNT_SUMMARY_REPORT.length - 1);
            headerMergeCellIndexesList.add(cellIndexes);

            headerReportCellDetails.add(reportCellDetailsHeaderRow);

            int srNo = 1;
            double totalDiscountAmount = 0d;
            List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();

            for (DiscountStructureAmountMetadata discountStructureAmountMetadata : discountStructureAmountMetadataList) {

                String discountStructureName = discountStructureAmountMetadata.getDiscountStructureName();
                Double discountStructureAmount = discountStructureAmountMetadata.getDiscountAmount();
//				/**
//				 * filtering discount which does not have any amount in them
//				 */
//				if(discountStructureAmount == null || discountStructureAmount == 0) {
//					continue;
//				}
                totalDiscountAmount = NumberUtils.addValues(totalDiscountAmount, discountStructureAmount);

                List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
                reportCellDetailsRow.add(new ReportCellDetails(srNo++, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(discountStructureName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

                reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(discountStructureAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetails.add(reportCellDetailsRow);

            }


            List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
            reportCellDetailsRow.add(new ReportCellDetails("Total", INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

            reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

            reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalDiscountAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));


            reportCellDetails.add(reportCellDetailsRow);

            List<CellIndexes> mergeCellIndexesList = new ArrayList<>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, DISCOUNT_SUMMARY_REPORT.length, headerMergeCellIndexesList, mergeCellIndexesList, headerReportCellDetails, reportCellDetails, false, false, null, DISCOUNT_SUMMARY_REPORT.length);

            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);

        } catch (final Exception e) {
            e.printStackTrace();
            logger.error("Error getting organisation id {}, academicSessionId {}", organisation.getOrganisationId(), selectedAcademicSession.getAcademicSessionId(), e);
        }
        return null;


    }

    private List<DiscountStructureAmountMetadata> mergeOrganisationDiscountStructureBySameName(Map<Integer, List<DiscountStructureAmountMetadata>> instituteDiscountStructureAmountMetadataMap) {

        if (CollectionUtils.isEmpty(instituteDiscountStructureAmountMetadataMap)) {
            return new ArrayList<>();
        }

        Map<String, DiscountStructureAmountMetadata> discountStructureAmountMetadataMap = new HashMap<>();
        for (Map.Entry<Integer, List<DiscountStructureAmountMetadata>> instituteDiscountStructureAmountMetadataEntry : instituteDiscountStructureAmountMetadataMap.entrySet()) {
            Integer instituteId = instituteDiscountStructureAmountMetadataEntry.getKey();
            if (instituteId == null || instituteId <= 0) {
                continue;
            }
            List<DiscountStructureAmountMetadata> instituteDiscountStructureAmountMetadataList = instituteDiscountStructureAmountMetadataEntry.getValue();
            if (CollectionUtils.isEmpty(instituteDiscountStructureAmountMetadataList)) {
                continue;
            }
            for (DiscountStructureAmountMetadata discountStructureAmountMetadata : instituteDiscountStructureAmountMetadataList) {
                String discountStructureName = discountStructureAmountMetadata.getDiscountStructureName();
                if (StringUtils.isBlank(discountStructureName)) {
                    continue;
                }
                Double discountAmount = discountStructureAmountMetadata.getDiscountAmount();
                if (!discountStructureAmountMetadataMap.containsKey(discountStructureName)) {
                    discountStructureAmountMetadataMap.put(discountStructureName, discountStructureAmountMetadata);
                } else {
                    Double totalDiscountAmount = NumberUtils.addValues(discountStructureAmountMetadataMap.get(discountStructureName) == null
                            ? 0 : discountStructureAmountMetadataMap.get(discountStructureName).getDiscountAmount() == null ? 0 :
                            discountStructureAmountMetadataMap.get(discountStructureName).getDiscountAmount(), discountAmount);
                    DiscountStructureAmountMetadata newDiscountStructureAmountMetadata = new DiscountStructureAmountMetadata(discountStructureName, totalDiscountAmount);
                    discountStructureAmountMetadataMap.put(discountStructureName, newDiscountStructureAmountMetadata);
                }
            }
        }
        return new ArrayList<>(discountStructureAmountMetadataMap.values());
    }

}
