package com.embrate.cloud.core.lib.payment;

import com.embrate.cloud.core.api.payment.PaymentApplicableDiscount;
import com.embrate.cloud.core.api.payment.PaymentDiscountType;
import com.embrate.cloud.core.api.payment.PaymentRequest;

/**
 * <AUTHOR>
 */

public class DefaultPaymentDiscountCalculator implements IPaymentDiscountCalculator {
    @Override
    public PaymentApplicableDiscount calculateDiscount(int instituteId, PaymentRequest paymentRequest) {
        return new PaymentApplicableDiscount(false, PaymentDiscountType.INSTANT, 0d, null, null);
    }
}
