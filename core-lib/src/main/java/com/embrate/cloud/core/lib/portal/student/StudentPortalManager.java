/**
 * 
 */
package com.embrate.cloud.core.lib.portal.student;

import com.embrate.cloud.core.api.homework.StudentHomeworkStats;
import com.embrate.cloud.core.api.lecture.StudentLectureStats;
import com.embrate.cloud.core.api.portal.student.StudentDashboardDetails;
import com.embrate.cloud.core.api.service.notification.BellNotificationDetails;
import com.embrate.cloud.core.lib.homework.HomeworkManager;
import com.embrate.cloud.core.lib.lecture.LectureManager;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.lernen.cloud.core.api.fees.payment.StudentFeesDetails;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class StudentPortalManager {
	
	private static final Logger logger = LogManager.getLogger(StudentPortalManager.class);
	
	private final StudentManager studentManager;
	private final FeePaymentManager feePaymentManager;
	private final LectureManager lectureManager;
	private final HomeworkManager homeworkManager;
	private final PushNotificationManager pushNotificationManager;
	
	public StudentPortalManager( StudentManager studentManager, FeePaymentManager feePaymentManager,
			LectureManager lectureManager, HomeworkManager homeworkManager, PushNotificationManager pushNotificationManager) {
		this.studentManager = studentManager;
		this.feePaymentManager = feePaymentManager;
		this.lectureManager = lectureManager;
		this.homeworkManager = homeworkManager;
		this.pushNotificationManager = pushNotificationManager;
	}


	public StudentDashboardDetails getStudentDashboardDetails(int instituteId, UUID studentId) {

		final Student student = studentManager.getStudentByLatestAcademicSession(studentId,
				Arrays.asList(StudentStatus.ENROLLED));
		int academicSessionId = student.getStudentAcademicSessionInfoResponse().getAcademicSession().getAcademicSessionId();
		
		final StudentFeesDetails studentFeesDetails = feePaymentManager.getPaymentDetails(instituteId,
				academicSessionId, studentId);
		
		StudentLectureStats studentLectureStats = lectureManager.getStudentLectureStats(instituteId, studentId);
		
		StudentHomeworkStats studentHomeworkStats = homeworkManager.getStudentHomeworkStats(instituteId, studentId);
		
		List<BellNotificationDetails> bellNotificationDetailsList = pushNotificationManager
				.getTodaysBellNotificationList(instituteId, studentId);
		
		//TODO:passing attendance monthly map as null right now
		StudentDashboardDetails studentDashboardDetails = 
				new StudentDashboardDetails(instituteId, studentId, studentFeesDetails.getTotalDueAmount(),
						studentLectureStats == null ? null : studentLectureStats.getStudentLecturesStateMap(),
						studentLectureStats == null ? null : studentHomeworkStats.getStudentHomeworkStateMap(),
						null, bellNotificationDetailsList);
				
		return studentDashboardDetails;
	}
}