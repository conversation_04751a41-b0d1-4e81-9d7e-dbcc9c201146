package com.embrate.cloud.core.lib.salary;

import com.embrate.cloud.core.api.salary.FinalPayslipDetails;
import com.embrate.cloud.core.api.salary.SalaryReportType;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Month;
import java.time.format.TextStyle;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

public class SalaryReportGenerationManager {

	private static final Logger logger = LogManager.getLogger(SalaryReportGenerationManager.class);

	private final SalaryConfigurationManager salaryConfigurationManager;

	private final UserPermissionManager userPermissionManager;

	private static final String[] PAYSLIP_DETAILS_BY_CYCLE_REPORT_COLUMN = { "Amount", "Staff Name", "Account Type", "Bank Name", "Account Holder Name", "Account Number", "IFSC Code", "PAN Number"};

	private static final String DATE = "Date";
	private static final String REPORT_TYPE = "Report Type";
	private static final String CYCLE = "Cycle";
	private static final String NA = "NA";

	public SalaryReportGenerationManager(SalaryConfigurationManager salaryConfigurationManager,
                                         UserPermissionManager userPermissionManager) {
		this.salaryConfigurationManager = salaryConfigurationManager;
		this.userPermissionManager = userPermissionManager;
	}

	private String getDateStr(int timeInSec) {
		final Date date = new Date(timeInSec * 1000L);
		final DateFormat format = new SimpleDateFormat("dd/MMM/yyyy HH:mm:ss");
		format.setTimeZone(User.DFAULT_TIMEZONE);
		final String formattedDate = format.format(date);
		return formattedDate;
	}

	public ReportOutput generateReport(int instituteId, SalaryReportType reportType, Integer cycle, UUID userId) {

		if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.SALARY_REPORTS)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
					"You don't have access to download reports!"));
		}

		switch (reportType) {
		case PAYSLIP_DETAILS_BY_CYCLE_REPORT:
			return generatePayslipDetailsByCycleReport(instituteId, cycle);
		default:
			break;
		}
		return null;
	}
	
	private ReportOutput generatePayslipDetailsByCycleReport(int instituteId, Integer cycle) {

		try {
			List<FinalPayslipDetails> finalPayslipDetailsList = salaryConfigurationManager.getPaySlipDetailsByCycle(instituteId, cycle);
			
			final Workbook workbook = new XSSFWorkbook();
	
			final String reportName = "SalaryDetailsByCycleReport.xlsx";
			
			final Sheet sheet = workbook.createSheet("SalaryDetailsByCycle");
			
			final Font headerFont = workbook.createFont();
			headerFont.setBold(true);
			headerFont.setFontHeightInPoints((short) 16);
			headerFont.setColor(IndexedColors.BLACK.getIndex());

			// Create a CellStyle with the font
			final CellStyle headerCellStyle = workbook.createCellStyle();
			headerCellStyle.setFont(headerFont);
			
			final Row reportTypeRow = sheet.createRow(0);
			reportTypeRow.createCell(0).setCellValue(REPORT_TYPE);
			reportTypeRow.createCell(1).setCellValue("Payslip Details By Cycle Report");
	
			final Row fromDateRow = sheet.createRow(1);
			fromDateRow.createCell(0).setCellValue(DATE);
			fromDateRow.createCell(1).setCellValue(getDateStr(DateUtils.now()));

			Month month = DateUtils.getMonthOfYear(cycle);
			String monthStr = month == null ? NA : month.getDisplayName(TextStyle.FULL, Locale.ENGLISH);
			final Row cycleRow = sheet.createRow(2);
			cycleRow.createCell(0).setCellValue(CYCLE);
			cycleRow.createCell(1).setCellValue(monthStr);
			
			// Create a Row
			final Row headerRow = sheet.createRow(5);
	
			// Create cells
			for (int i = 0; i < PAYSLIP_DETAILS_BY_CYCLE_REPORT_COLUMN.length; i++) {
				final Cell cell = headerRow.createCell(i);
				cell.setCellValue(PAYSLIP_DETAILS_BY_CYCLE_REPORT_COLUMN[i]);
				cell.setCellStyle(headerCellStyle);
			}
			
			if(CollectionUtils.isEmpty(finalPayslipDetailsList)) {
				logger.info("Empty report {}", reportName);
				return getReportOutput(workbook, reportName);
			}

			int rowNum = 6;
			for(FinalPayslipDetails finalPayslipDetails : finalPayslipDetailsList) {
				final Row row = sheet.createRow(rowNum++);
				int colNum = 0;
				String amount = String.valueOf(finalPayslipDetails.getNetAmount());
				row.createCell(colNum++).setCellValue(amount);

				Staff staff = finalPayslipDetails.getBulkSalaryPayslip().getStaff();
				String staffName = staff == null ? NA : staff.getStaffBasicInfo().getName();
				row.createCell(colNum++).setCellValue(staffName);

				String accountType = staff == null ? NA : staff.getStaffBankInfo() == null ? NA : staff.getStaffBankInfo().getAccountType() == null ? NA : staff.getStaffBankInfo().getAccountType().name();
				row.createCell(colNum++).setCellValue(accountType);

				String bankName = staff == null ? NA : staff.getStaffBankInfo() == null ? NA : StringUtils.isBlank(staff.getStaffBankInfo().getBankName()) ? NA : staff.getStaffBankInfo().getBankName();
				row.createCell(colNum++).setCellValue(bankName);

				String accountHolderName = staff == null ? NA : staff.getStaffBankInfo() == null ? NA : StringUtils.isBlank(staff.getStaffBankInfo().getAccountHolderName()) ? NA : staff.getStaffBankInfo().getAccountHolderName();
				row.createCell(colNum++).setCellValue(accountHolderName);

				String accountNumber = staff == null ? NA : staff.getStaffBankInfo() == null ? NA : StringUtils.isBlank(staff.getStaffBankInfo().getAccountNumber()) ? NA : staff.getStaffBankInfo().getAccountNumber();
				row.createCell(colNum++).setCellValue(accountNumber);

				String ifscCode = staff == null ? NA : staff.getStaffBankInfo() == null ? NA : StringUtils.isBlank(staff.getStaffBankInfo().getIfscCode()) ? NA : staff.getStaffBankInfo().getIfscCode();
				row.createCell(colNum++).setCellValue(ifscCode);

				String panNumber = staff == null ? NA : staff.getStaffBankInfo() == null ? NA : StringUtils.isBlank(staff.getStaffBankInfo().getPanNumber()) ? NA : staff.getStaffBankInfo().getPanNumber();
				row.createCell(colNum++).setCellValue(panNumber);
			}
			
			for (int i = 0; i < PAYSLIP_DETAILS_BY_CYCLE_REPORT_COLUMN.length; i++) {
				sheet.autoSizeColumn(i);
			}
			
			return getReportOutput(workbook, reportName);
		} catch (final Exception e) {
			logger.error("Error while generating cycle report", e);
		}
		return null;
	}

	public ReportOutput getReportOutput(Workbook workbook, String reportName) throws IOException {
		final ByteArrayOutputStream bos = new ByteArrayOutputStream();
		workbook.write(bos);
		workbook.close();
		return new ReportOutput(reportName, bos);
	}
}
