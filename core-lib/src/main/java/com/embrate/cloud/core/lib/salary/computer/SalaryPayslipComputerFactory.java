package com.embrate.cloud.core.lib.salary.computer;

/**
 * <AUTHOR>
 */

public class SalaryPayslipComputerFactory {
    private final ISalaryPayslipComputer defaultSalaryPayslipComputer;
    private final ISalaryPayslipComputer salaryPayslipWithESICEPFDeductionComputer;

    public SalaryPayslipComputerFactory(ISalaryPayslipComputer defaultSalaryPayslipComputer, ISalaryPayslipComputer salaryPayslipWithESICEPFDeductionComputer) {
        this.defaultSalaryPayslipComputer = defaultSalaryPayslipComputer;
        this.salaryPayslipWithESICEPFDeductionComputer = salaryPayslipWithESICEPFDeductionComputer;
    }

    public ISalaryPayslipComputer getComputer(int instituteId){
        if(instituteId == 10225 || instituteId == 10226 || instituteId == 10227 || instituteId == 10228) {
            return salaryPayslipWithESICEPFDeductionComputer;
        }
        return defaultSalaryPayslipComputer;
    }
}
