package com.embrate.cloud.core.lib.scratchcard.rewards;

import com.embrate.cloud.core.api.scratchcard.RewardDetails;
import com.embrate.cloud.core.api.scratchcard.RewardType;
import com.lernen.cloud.core.api.fees.payment.FeeIdFeeHeadTransactionDetails;
import com.lernen.cloud.core.api.fees.payment.FeePaymentResponse;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionDetails;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

public class DefaultRewardValidationEngine implements IRewardValidationEngine {

    private static final Logger logger = LogManager.getLogger(DefaultRewardValidationEngine.class);

    @Override
    public List<RewardDetails> isTransactionValidForReward(int instituteId, FeePaymentTransactionDetails feePaymentTransactionDetails,
                                                           FeePaymentResponse feePaymentResponse) {
        if(feePaymentResponse == null || feePaymentResponse.getTransactionId() == null) {
            logger.error("Error in feePaymentResponse");
            return null;
        }
        List<RewardDetails> rewardDetailsList = new ArrayList<RewardDetails>();
        for(FeeIdFeeHeadTransactionDetails feeIdFeeHeadTransactionDetails : feePaymentTransactionDetails.getFeeIdFeeHeadTransactionDetails()) {
            if(feeIdFeeHeadTransactionDetails == null || feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo() == null) {
                continue;
            }
            //TODO: What to do if dueDate for a fees is null, keeping current due date as current time for now.
            //TODO:keeping expiry date of one year from current date
            //TODO: Giving static values to reward details object for now.
            //entityId null for cashback
            Map<String, Object> metaData = new HashMap<String, Object>();
            //String, Double
            metaData.put("amount", 10);
            rewardDetailsList.add(new RewardDetails(UUID.randomUUID(), RewardType.CASHBACK, null, metaData, "Cashback rewards", DateUtils.now(),
                     DateUtils.addDaysCurrentTime(365)));
        }
        return rewardDetailsList;
    }
}
