package com.embrate.cloud.core.lib.service.communication;

import com.embrate.cloud.core.api.service.communication.*;
import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import com.lernen.cloud.core.api.common.CounterType;
import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.institute.CounterData;
import com.lernen.cloud.core.api.notification.NotificationDetails;
import com.lernen.cloud.core.api.notification.NotificationStatus;
import com.lernen.cloud.core.api.notification.NotificationType;
import com.lernen.cloud.core.api.sms.SMSPreferences;
import com.lernen.cloud.core.api.sms.SMSSendDetails;
import com.lernen.cloud.core.api.sms.msg91.UserSMSPayload;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.utils.communication.service.CommunicationServiceUtils;
import com.lernen.cloud.dao.tier.notification.NotificationStatusDao;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
public class CommunicationServiceManager {
    private static final Logger logger = LogManager.getLogger(CommunicationServiceManager.class);

    private static final int BATCH_SEND_SEED_VALUE = 1;
    private static final int BATCH_SEND_SIZE_FACTOR = 2;
    private static final String REFUND_AGAINST_TRANSACTION_KEY = "refund_against_transaction";

    private final NotificationStatusDao notificationStatusDao;
    private final InstituteManager instituteManager;
    private final TransactionTemplate transactionTemplate;

    public CommunicationServiceManager(NotificationStatusDao notificationStatusDao, InstituteManager instituteManager, TransactionTemplate transactionTemplate) {
        this.notificationStatusDao = notificationStatusDao;
        this.instituteManager = instituteManager;
        this.transactionTemplate = transactionTemplate;
    }


    public CommunicationServiceTransactionResponse validateAndTransaction(int instituteId, CommunicationServiceProvider serviceProvider, Integer count, UserType userType,
                                                                            UUID userId, CommunicationServiceTransactionType communicationServiceTransactionType, Integer bufferCount) {
        try {
            final UUID transactionId = transactionTemplate.execute(new TransactionCallback<UUID>() {
                @Override
                public UUID doInTransaction(TransactionStatus status) {

                    CounterType counterType = CommunicationServiceUtils.getCounterForService(serviceProvider);
                    CounterData counterData = instituteManager.getCounter(instituteId, counterType, true);

                    if (counterData == null) {
                        logger.error(counterType + " counter not setup for institute {}", instituteId);
                        throw new EmbrateRunTimeException(counterType + " counter not setup for institute");
                    }

                    int counter = counterData.getCount();

                    Integer totalAvailableCount = NumberUtils.addValues(counter, bufferCount);

                    if (totalAvailableCount < count) {
                        logger.error("You don't have enough credits in your account. Total available " + counterType + " credits : "
                                        + totalAvailableCount + " Requested "+counterType+" credits : " + count + " for institute {}, userId {}",
                                instituteId, userId);
                        throw new EmbrateRunTimeException(
                                "You don't have enough credits in your account. Please recharge as per your requirement. Total available "
                                        + "credits  : " + totalAvailableCount + " Requested credits : " + count);

                    }
                    UUID transactionId = notificationStatusDao.addCommunicationServiceTransactionNonAtomic(instituteId,
                            new CommunicationServiceTransaction(instituteId, serviceProvider, userType, userId, 0d, count * -1, communicationServiceTransactionType,
                                    CommunicationServiceTransactionStatus.DEBIT, null, null),
                            false);

                    if (transactionId == null) {
                        logger.error("Exception occurs while decreasing the count for institute {}, userId {}",
                                instituteId, userId);
                        throw new EmbrateRunTimeException("Unable to process the request right now, please try again in some time.");
                    }
                    return transactionId;
                }
            });

            if (transactionId != null) {
                return new CommunicationServiceTransactionResponse(transactionId, null);
            }
        } catch (EmbrateRunTimeException e) {
            logger.error("Error while executing comm service transaction for instituteId {}", instituteId, e);
            return new CommunicationServiceTransactionResponse(null, e.getMessage());
        } catch (Exception e) {
            logger.error("Exception while executing comm service transaction for instituteId {}", instituteId, e);
        }
        return new CommunicationServiceTransactionResponse(null, "Unable to perform service transaction");
    }

    public boolean refundServiceTransaction(int instituteId, CommunicationServiceProvider communicationServiceProvider, UserType userType, UUID userId, int refundCredits,
                                            UUID transactionId) {

        try{
            Map<String, Object> metaData = new HashMap<>();
            metaData.put(REFUND_AGAINST_TRANSACTION_KEY, transactionId.toString());

            CommunicationServiceTransaction communicationServiceTransaction = new CommunicationServiceTransaction(instituteId, communicationServiceProvider, userType, userId, 0d,
                    refundCredits, CommunicationServiceTransactionType.REFUND, CommunicationServiceTransactionStatus.CREDIT, metaData,
                    "Refund for transaction failure");
            UUID refundTransactionId = notificationStatusDao.addCommunicationServiceTransaction(instituteId, communicationServiceTransaction);
            if (refundTransactionId != null) {
                logger.info("Successfully refunded credits for request {}", communicationServiceTransaction);
                return true;

            }
            // TODO : Need some mechanism to notify this scenario
            logger.error("Failed to refund credits for request {}", communicationServiceTransaction);
        }catch (Exception e){
            logger.error("Exception while refunding the transaction for transactionId {}, instituteId {}, communicationServiceProvider {}, userId {}, refundCredits {}", transactionId, instituteId, communicationServiceProvider, userId, refundCredits, e);
            e.printStackTrace();
        }
        return false;
    }

    public  <T extends UserCommunicationServicePayload> CommunicationServiceUserCreditStatus executeService(int instituteId, CommunicationServiceProvider communicationServiceProvider, Integer academicSessionId, NotificationType notificationType,
                                                                            UUID batchId, String batchName, List<T> userServicePayload,
                                                                           UserType userType, ICommunicationServiceHandler communicationServiceHandler, UUID transactionId,
                                                                                                            boolean updateCredit) {

        List<NotificationDetails> notificationDetailsList = new ArrayList<>();

        int totalContactCount = 0;
        int failedContactCount = 0;

        int totalCreditCount = 0;
        int failedCreditCount = 0;

        int batchSize = BATCH_SEND_SEED_VALUE;
        for (T payload : userServicePayload) {
            int index = 1;
            int perChannelIdCredit = 0;

            if(!CollectionUtils.isEmpty(payload.getDestinationChannelIds())){
                perChannelIdCredit = payload.getCreditsUsed()/payload.getDestinationChannelIds().size();
            }

            for (String channelId : payload.getDestinationChannelIds()) {
                //Check SMS to set credits
//                CommunicationServiceUtils.getServiceCredits(communicationServiceProvider, payload.getMessagePayload(), payload.getMessageDuration());
                totalContactCount++;
                totalCreditCount += perChannelIdCredit;

                String uniqueReferenceId = null;
                NotificationStatus notificationStatus = NotificationStatus.GENERATED;

                Map<String, Object> metadata = new HashMap<>();
                if(MapUtils.isNotEmpty(payload.getMetaData())){
                    metadata.putAll(payload.getMetaData());
                }

                try {
                    logger.info("Sending #{}, channel {}, service provider {},  to user {}", index++, communicationServiceProvider.getDeliveryMode(), communicationServiceProvider, payload.getUserId());

                    CommunicationServiceActionResponse communicationServiceActionResponse = communicationServiceHandler.executeServiceAction(instituteId, channelId, payload);

                    if(!updateCredit) {
                        return new CommunicationServiceUserCreditStatus(totalContactCount, failedContactCount, totalCreditCount,
                                failedCreditCount);
                    }
                    if (communicationServiceActionResponse == null || communicationServiceActionResponse.getUniqueReferenceId() == null) {
                        logger.error("Could not perform action to user {} on channelId {}", payload.getUserId(), channelId);
                        notificationStatus = NotificationStatus.INJECTION_FAILURE;
                    } else {
                        logger.info("Successfully executed action to user {} on channelId {}, actionResponse {}",
                                payload.getUserId(), channelId, communicationServiceActionResponse);
                        uniqueReferenceId = communicationServiceActionResponse.getUniqueReferenceId();
                        if(MapUtils.isNotEmpty(communicationServiceActionResponse.getMetadata())){
                            metadata.putAll(communicationServiceActionResponse.getMetadata());
                        }
                    }

                } catch (Exception e) {
                    logger.error("Error while executing action to user {} on channelId {}", payload.getUserId(), channelId, e);
                    notificationStatus = NotificationStatus.INJECTION_FAILURE;
                }

                Integer refundCredits = null;
                if (notificationStatus == NotificationStatus.INJECTION_FAILURE) {
                    failedContactCount++;
                    failedCreditCount += perChannelIdCredit;
                    refundCredits = perChannelIdCredit;
                }

                notificationDetailsList
                        .add(new NotificationDetails(instituteId, payload.getUserId(), userType, academicSessionId,
                                communicationServiceProvider, notificationType, communicationServiceProvider.getDeliveryMode(), notificationStatus,
                                channelId, batchId, batchName, payload.getTitle(), payload.getDisplayMessage(), uniqueReferenceId, metadata, perChannelIdCredit, transactionId, refundCredits));

                if (notificationDetailsList.size() == batchSize) {
                    addNotifications(instituteId, notificationDetailsList);
                    notificationDetailsList.clear();
                    batchSize = batchSize * BATCH_SEND_SIZE_FACTOR;
                }
            }
        }

        addNotifications(instituteId, notificationDetailsList);

        return new CommunicationServiceUserCreditStatus(totalContactCount, failedContactCount, totalCreditCount,
                failedCreditCount);
    }

    private void addNotifications(int instituteId, List<NotificationDetails> notificationDetailsList) {
        if (CollectionUtils.isEmpty(notificationDetailsList)) {
            return;
        }
        try {
            if (!notificationStatusDao.addNotificationBatch(notificationDetailsList)) {
                logger.error("Unable to store notification details for institute {}, notificationDetailsList {}", instituteId,
                        notificationDetailsList);
                return;
            }
            logger.info("Successfully stored notification details for institute {}, notificationDetailsList size {}", instituteId,
                    notificationDetailsList.size());
        } catch (Exception e) {
            logger.error("Exception to store notification details for institute {}, notificationDetailsList {}", instituteId,
                    notificationDetailsList, e);
        }
    }
}
