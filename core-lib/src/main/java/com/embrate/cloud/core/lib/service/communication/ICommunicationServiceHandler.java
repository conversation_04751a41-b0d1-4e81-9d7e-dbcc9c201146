package com.embrate.cloud.core.lib.service.communication;

import com.embrate.cloud.core.api.service.communication.CommunicationServiceActionResponse;
import com.embrate.cloud.core.api.service.communication.UserCommunicationServicePayload;

/**
 * <AUTHOR>
 */
public interface ICommunicationServiceHandler {

    public  <T extends UserCommunicationServicePayload> CommunicationServiceActionResponse executeServiceAction(int instituteId, String destinationChannelId, T payload);
}
