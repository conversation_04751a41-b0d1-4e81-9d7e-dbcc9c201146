package com.embrate.cloud.core.lib.service.payment.gateway;

import com.embrate.cloud.core.api.service.payment.gateway.*;
import com.embrate.cloud.core.api.wallet.WalletTransactionCategory;
import com.embrate.cloud.core.api.wallet.WalletTransactionPayload;
import com.embrate.cloud.core.lib.wallet.UserWalletManager;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.RetryableCallable;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;

import static com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayAmountTxnResponse.PAYMENT_GATEWAY_AMOUNT_TXN_RESPONSE;
import static com.embrate.cloud.core.lib.service.payment.gateway.PaymentGatewayManager.PAYMENT_SUCCESS_TXN_FAILURE_WALLET_DEPOSIT_FAILURE;
import static com.embrate.cloud.core.lib.service.payment.gateway.PaymentGatewayManager.PAYMENT_SUCCESS_TXN_FAILURE_WALLET_DEPOSIT_SUCCESS;

/**
 * <AUTHOR>
 */

public abstract class AbstractPaymentGatewayTaskHandler {

    private static final Logger logger = LogManager.getLogger(AbstractPaymentGatewayTaskHandler.class);
    protected final TransactionTemplate transactionTemplate;
    protected final UserWalletManager userWalletManager;

    public AbstractPaymentGatewayTaskHandler(TransactionTemplate transactionTemplate, UserWalletManager userWalletManager) {
        this.transactionTemplate = transactionTemplate;
        this.userWalletManager = userWalletManager;
    }

    /**
     * Validate the task specific payload
     *
     * @param instituteId
     * @param initiateTransactionPayload
     * @return
     */
    public abstract boolean validInitiateTransactionPayload(int instituteId, PGInitiateTransactionPayload initiateTransactionPayload);

    /**
     * Update the transaction metadata with task specific data
     *
     * @param metadata
     * @param initiateTransactionPayload
     */
    public abstract void updateInitiateTransactionMetadata(Map<String, Object> metadata, PGInitiateTransactionPayload initiateTransactionPayload);

    /**
     * Handle the success response based on the type of system transaction.
     * System txn is supposed to be within a DB transaction, any failure in
     * system transaction should only revert the intermediate changes if any.
     *
     * @param transactionData
     * @return
     */
    public abstract PaymentGatewayAmountTxnResponse handleSuccessTransaction(PaymentGatewayTransactionData transactionData);

    public abstract PaymentGatewaySystemTransactionDetails getSystemTransactionDetails(int instituteId, PGTransactionType transactionType, PaymentGatewayAmountTxnResponse paymentGatewayAmountTxnResponse);

    public PaymentGatewayAmountTxnResponse handleSuccessResponse(PaymentGatewayTransactionData transactionData){
        PaymentGatewayAmountTxnResponse paymentGatewayAmountTxnResponse = null;
        try{
            paymentGatewayAmountTxnResponse = handleSuccessTransaction(transactionData);
        }catch (Exception e){
            logger.error("Exception while handling success response {}", transactionData, e);
        }

        if(paymentGatewayAmountTxnResponse == null || (!paymentGatewayAmountTxnResponse.isTxnSuccess() && paymentGatewayAmountTxnResponse.getDepositToWalletTransactionId() == null)){
            logger.error("Failed to collect system payment transaction. Depositing amount to wallet for {}", transactionData);
            UUID walletTransactionId = addPaymentAmountToWallet(transactionData, transactionData.getPaymentGatewayAmount());
            if(walletTransactionId == null){
                logger.error("Failed to add amount to wallet after system txn failure {}", transactionData.getTransactionId());
                return new PaymentGatewayAmountTxnResponse(transactionData.getTransactionType(), false, null, null, PAYMENT_SUCCESS_TXN_FAILURE_WALLET_DEPOSIT_FAILURE, "Payment is received but failed to update payment transaction and unable to deposit amount to wallet. Please contact the support team", null);
            }
            logger.info("Successfully added amount to wallet after system txn failure {}", transactionData.getTransactionId());
            return new PaymentGatewayAmountTxnResponse(transactionData.getTransactionType(), false, walletTransactionId, null, PAYMENT_SUCCESS_TXN_FAILURE_WALLET_DEPOSIT_SUCCESS, "Payment is received but failed to update payment transaction. Payment amount is credited to wallet.", null);
        }

        return paymentGatewayAmountTxnResponse;
    }

    public PaymentGatewaySystemTransactionDetails getSystemTransactionDetails(PaymentGatewayTransactionData paymentGatewayTransactionData){
        List<String> transactionList = (List<String>) paymentGatewayTransactionData.getMetadata().get(PAYMENT_GATEWAY_AMOUNT_TXN_RESPONSE);
        if(CollectionUtils.isEmpty(transactionList)){
            return null;
        }
        for(String transaction : transactionList){
            PaymentGatewayAmountTxnResponse paymentGatewayAmountTxnResponse = SharedConstants.GSON.fromJson(transaction, PaymentGatewayAmountTxnResponse.class);
            if(paymentGatewayAmountTxnResponse == null || !paymentGatewayAmountTxnResponse.isTxnSuccess()){
                continue;
            }
            return getSystemTransactionDetails(paymentGatewayTransactionData.getInstituteId(), paymentGatewayTransactionData.getTransactionType(), paymentGatewayAmountTxnResponse);
        }
        return null;
    }

    private UUID addPaymentAmountToWallet(PaymentGatewayTransactionData transactionData, double walletAmount){
        WalletTransactionCategory walletTransactionCategory = WalletTransactionCategory.PAYMENT_GATEWAY_TRANSACTION_FAILURE_ADJUSTMENT;
        Map<String, Object> metaData = new HashMap<>();
        metaData.put("paymentGatewayTransactionId", transactionData.getTransactionId().toString());
        metaData.put("paymentGatewayTransactionData", SharedConstants.GSON.toJson(transactionData));
        WalletTransactionPayload walletTransactionPayload = new WalletTransactionPayload(transactionData.getInstituteId(), transactionData.getUserId(),
                transactionData.getUserType(), walletTransactionCategory, TransactionMode.PAYMENT_GATEWAY, DateUtils.now(), FeePaymentTransactionStatus.ACTIVE,
                walletAmount, transactionData.getUserId(), metaData,
                "Depositing amount in wallet as could not process the payment successfully. It is be either already paid or transaction failed to perform");

        RetryableCallable<UUID> addAmountToWallet = new RetryableCallable<UUID>() {
            @Override
            public UUID retryableCall() throws Exception {
                UUID walletTransaction = userWalletManager.addUserWalletTransaction(walletTransactionPayload);
                if(walletTransaction == null){
                    logger.error("Unable to add amount to wallet for transaction {}", transactionData.getTransactionId());
                    throw new EmbrateRunTimeException("Unable to log transaction update");
                }
                logger.info("Successfully added amount to wallet for transaction {}", transactionData.getTransactionId());
                return walletTransaction;
            }
        };

        try {
            return addAmountToWallet.call();
        } catch (Exception e) {
            logger.error("Unable to add wallet amount after txn failure for transaction {}",transactionData.getTransactionId(), e);
        }
        return null;
    }


}
