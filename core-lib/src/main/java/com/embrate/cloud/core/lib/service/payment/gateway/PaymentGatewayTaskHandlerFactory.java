package com.embrate.cloud.core.lib.service.payment.gateway;

import com.embrate.cloud.core.api.service.payment.gateway.PGTransactionType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */

public class PaymentGatewayTaskHandlerFactory {

    private static final Logger logger = LogManager.getLogger(PaymentGatewayTaskHandlerFactory.class);

    private final StudentFeePaymentTaskHandler studentFeePaymentTaskHandler;

    private final UserWalletRechargeTaskHandler userWalletRechargeTaskHandler;

    private final StudentRegistrationTaskHandler studentRegistrationTaskHandler;

    public PaymentGatewayTaskHandlerFactory(StudentFeePaymentTaskHandler studentFeePaymentTaskHandler,
                                            UserWalletRechargeTaskHandler userWalletRechargeTaskHandler,
                                            StudentRegistrationTaskHandler studentRegistrationTaskHandler) {
        this.studentFeePaymentTaskHandler = studentFeePaymentTaskHandler;
        this.userWalletRechargeTaskHandler = userWalletRechargeTaskHandler;
        this.studentRegistrationTaskHandler = studentRegistrationTaskHandler;
    }

    public AbstractPaymentGatewayTaskHandler getPaymentGatewayTaskHandler(PGTransactionType transactionType){
        if(transactionType == null){
            logger.error("Invalid transaction type");
            return null;
        }
        switch (transactionType){
            case STUDENT_FEE_PAYMENT:
               return studentFeePaymentTaskHandler;
            case WALLET_RECHARGE:
                return userWalletRechargeTaskHandler;
            case STUDENT_REGISTRATION:
                return studentRegistrationTaskHandler;
            default:
                logger.error("Unsupported transactionType {}", transactionType);
                return null;
        }
    }
}
