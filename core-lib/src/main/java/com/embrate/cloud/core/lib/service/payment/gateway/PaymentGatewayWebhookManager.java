package com.embrate.cloud.core.lib.service.payment.gateway;

import com.embrate.cloud.core.api.service.payment.gateway.PGProcessTransactionPayload;
import com.embrate.cloud.core.api.service.payment.gateway.PGProcessTransactionResponse;
import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayTransactionData;
import com.embrate.cloud.core.api.service.payment.gateway.atom.AtomPaymentWebhookResponse;
import com.embrate.cloud.core.api.service.payment.gateway.cashfree.CashFreePaymentWebhookResponse;
import com.embrate.cloud.core.api.service.payment.gateway.jodo.JodoPaymentWebhookResponse;
import com.embrate.cloud.core.api.service.payment.gateway.razorpay.RazorpayPaymentWebhookResponse;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;
import static com.lernen.cloud.core.utils.SharedConstants.OBJECT_MAPPER;

/**
 * <AUTHOR>
 */
public class PaymentGatewayWebhookManager {

	private static final Logger logger = LogManager.getLogger(PaymentGatewayWebhookManager.class);

	private final PaymentGatewayManager paymentGatewayManager;

	public PaymentGatewayWebhookManager(PaymentGatewayManager paymentGatewayManager) {
		this.paymentGatewayManager = paymentGatewayManager;
	}

	public boolean processCashFreeWebhook(int instituteId, String orderId, String orderAmount,
										  String referenceId, String txStatus,
										  String paymentMode, String txMsg, String txTime,
										  String signature) {
		logger.info("webhookData orderId {}, orderAmount {}, signature {}, txStatus {}", orderId, orderAmount, signature, txStatus);

		Map<String, String> transactionDataPayload = new HashMap<>();
		transactionDataPayload.put("orderId", orderId);
		transactionDataPayload.put("orderAmount", orderAmount);
		transactionDataPayload.put("referenceId", referenceId);
		transactionDataPayload.put("txStatus", txStatus);
		transactionDataPayload.put("paymentMode", paymentMode);
		transactionDataPayload.put("txMsg", txMsg);
		transactionDataPayload.put("txTime", txTime);
		transactionDataPayload.put("signature", signature);

		PGProcessTransactionPayload processTransactionPayload = new PGProcessTransactionPayload();
		processTransactionPayload.setTransactionData(transactionDataPayload);

		PGProcessTransactionResponse processTransactionResponse = paymentGatewayManager.processTransaction(instituteId, processTransactionPayload, false);
		return processTransactionResponse.isValidTransaction();
	}

	@Deprecated
	public boolean processCashFreeWebhookV2(int instituteId, String timestamp, String signature, String webhookData) {
		logger.info("webhookData institute = {}, timestamp : {}, signature : {}, webhookData {}", instituteId, timestamp, signature, webhookData);
		if (instituteId <= 0 || StringUtils.isBlank(timestamp) || StringUtils.isBlank(signature) || StringUtils.isBlank(webhookData)) {
			logger.error("Invalid payload for institute {}", instituteId);
			return false;
		}

		PGProcessTransactionPayload processTransactionPayload = new PGProcessTransactionPayload();
		processTransactionPayload.setWebhook(true);
		processTransactionPayload.setWebhookData(webhookData);
		processTransactionPayload.setWebhookHeaderTimestamp(timestamp);
		processTransactionPayload.setWebhookHeaderSignature(signature);

		PGProcessTransactionResponse processTransactionResponse = paymentGatewayManager.processTransaction(instituteId, processTransactionPayload, false);
		return processTransactionResponse.isValidTransaction();
	}

	public boolean processCashFreeWebhookV20230801(String webhookData) {
		logger.info("webhookData webhookData {}", webhookData);
		if (StringUtils.isBlank(webhookData)) {
			logger.error("Invalid webhookData. Skipping request.");
			return false;
		}
		try {
			CashFreePaymentWebhookResponse cashFreePaymentWebhookResponse = OBJECT_MAPPER.readValue(webhookData, CashFreePaymentWebhookResponse.class);
			String orderIdStr = cashFreePaymentWebhookResponse.getData().getOrder().getOrderId();
			UUID orderId = UUID.fromString(orderIdStr);
			PaymentGatewayTransactionData transactionData = paymentGatewayManager.getTransactions(orderId);
			if(transactionData == null){
				logger.error("Transaction not found for order id {} ", orderId);
				return false;
			}
			int instituteId = transactionData.getInstituteId();

			PGProcessTransactionPayload processTransactionPayload = new PGProcessTransactionPayload();
			processTransactionPayload.setWebhook(true);
			processTransactionPayload.setWebhookData(webhookData);

			PGProcessTransactionResponse processTransactionResponse = paymentGatewayManager.processTransaction(instituteId, processTransactionPayload, false);
			return processTransactionResponse.isValidTransaction();

		} catch (Exception e) {
			logger.error("Invalid webhook data {}", webhookData, e);
			return false;
		}


	}

	public boolean processAtomWebhook(AtomPaymentWebhookResponse atomPaymentWebhookResponse) {
		try {
			logger.info("webhookData {}", atomPaymentWebhookResponse);
			if (atomPaymentWebhookResponse == null || atomPaymentWebhookResponse.getMerchantTxnID() == null) {
				logger.error("Invalid payload");
				return false;
			}

			PGProcessTransactionPayload processTransactionPayload = new PGProcessTransactionPayload();
			processTransactionPayload.setWebhook(true);
			processTransactionPayload.setWebhookData(SharedConstants.OBJECT_MAPPER.writeValueAsString(atomPaymentWebhookResponse));

			PaymentGatewayTransactionData paymentGatewayTransactionData = paymentGatewayManager
					.getTransactions(UUID.fromString(atomPaymentWebhookResponse.getMerchantTxnID()));

			PGProcessTransactionResponse processTransactionResponse = paymentGatewayManager.processTransaction(
					paymentGatewayTransactionData.getInstituteId(),
					processTransactionPayload, false);

			return processTransactionResponse.isValidTransaction();
		} catch (Exception ex) {
			logger.error("Error while adding call back flow {}", ex);
			return false;
		}
	}

	public boolean processJodoWebhook(JodoPaymentWebhookResponse jodoPaymentWebhookResponse) {
		try {
			logger.info("webhookData {}", jodoPaymentWebhookResponse);
			if (jodoPaymentWebhookResponse == null || StringUtils.isBlank(jodoPaymentWebhookResponse.getEvent())
					|| StringUtils.isBlank(jodoPaymentWebhookResponse.getEventId())) {
				logger.error("Invalid payload");
				return false;
			}

			System.out.println(jodoPaymentWebhookResponse.getJodoPaymentWebhookResponsePayload().toString());
			String transactionId = jodoPaymentWebhookResponse.getJodoPaymentWebhookResponsePayload() == null ? null :
					jodoPaymentWebhookResponse.getJodoPaymentWebhookResponsePayload().getJodoPaymentWebhookResponsePayloadOrder()
							== null ? null : jodoPaymentWebhookResponse.getJodoPaymentWebhookResponsePayload()
							.getJodoPaymentWebhookResponsePayloadOrder().getIdentifier();

			if (StringUtils.isBlank(transactionId)) {
				System.out.println("Invalid transaction id");
				logger.error("Invalid trnasaction id");
				return false;
			}

			PGProcessTransactionPayload processTransactionPayload = new PGProcessTransactionPayload();
			processTransactionPayload.setWebhook(true);
			processTransactionPayload.setWebhookData(SharedConstants.OBJECT_MAPPER.writeValueAsString(jodoPaymentWebhookResponse));

			System.out.println("processTransactionPayload : " + processTransactionPayload);
			PaymentGatewayTransactionData paymentGatewayTransactionData = paymentGatewayManager
					.getTransactions(UUID.fromString(transactionId));

			System.out.println("paymentGatewayTransactionData : " + paymentGatewayTransactionData);
			PGProcessTransactionResponse processTransactionResponse = paymentGatewayManager.processTransaction(
					paymentGatewayTransactionData.getInstituteId(),
					processTransactionPayload, false);

			System.out.println("processTransactionResponse : " + processTransactionResponse);

			return processTransactionResponse.isValidTransaction();
		} catch (Exception ex) {
			logger.error("Error while adding call back flow", ex);
			return false;
		}

	}

	public boolean processRazorpayWebhook(String razorpayPaymentWebhookResponseString, String signature) {
		try {

			logger.info("webhookDataString {}", razorpayPaymentWebhookResponseString);

			RazorpayPaymentWebhookResponse razorpayPaymentWebhookResponse = GSON.fromJson(razorpayPaymentWebhookResponseString, RazorpayPaymentWebhookResponse.class);

			logger.info("webhookData {}", razorpayPaymentWebhookResponse);

			if (razorpayPaymentWebhookResponse == null || razorpayPaymentWebhookResponse.getPayload() == null
					|| razorpayPaymentWebhookResponse.getPayload().getPayment() == null
					|| razorpayPaymentWebhookResponse.getPayload().getPayment().getEntity() == null
					|| StringUtils.isBlank(razorpayPaymentWebhookResponse.getPayload().getPayment().getEntity().getOrder_id())) {
				logger.error("Invalid payload");
				return false;
			}

			logger.info("Valid payload");
			PGProcessTransactionPayload processTransactionPayload = new PGProcessTransactionPayload();
			processTransactionPayload.setWebhook(true);
			processTransactionPayload.setWebhookHeaderSignature(signature);
			processTransactionPayload.setWebhookData(razorpayPaymentWebhookResponseString);

			PaymentGatewayTransactionData paymentGatewayTransactionData = paymentGatewayManager
					.getTransactionsByToken(razorpayPaymentWebhookResponse.getPayload()
							.getPayment().getEntity()
							.getOrder_id());

			logger.info("paymentGatewayTransactionData by token : {}", paymentGatewayTransactionData);
			if (CollectionUtils.isEmpty(processTransactionPayload.getTransactionData())) {
				processTransactionPayload.setTransactionData(new HashMap<>());
			}

			processTransactionPayload.getTransactionData().put("transactionId", paymentGatewayTransactionData.getTransactionId().toString());

			PGProcessTransactionResponse processTransactionResponse = paymentGatewayManager.processTransaction(
					paymentGatewayTransactionData.getInstituteId(),
					processTransactionPayload, false);

			return processTransactionResponse.isValidTransaction();
		} catch (Exception ex) {
			logger.error("Error while adding call back flow", ex);
			return false;
		}
	}
}
