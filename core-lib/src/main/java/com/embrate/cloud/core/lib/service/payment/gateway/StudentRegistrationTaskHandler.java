package com.embrate.cloud.core.lib.service.payment.gateway;

import com.embrate.cloud.core.api.service.payment.gateway.*;
import com.embrate.cloud.core.lib.wallet.UserWalletManager;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.embrate.cloud.core.api.student.registration.StudentRegistrationPaymentData;
import com.lernen.cloud.dao.tier.student.StudentRegistrationDao;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Map;

import static com.embrate.cloud.core.lib.service.payment.gateway.PaymentGatewayManager.*;

/**
 * <AUTHOR>
 */

public class StudentRegistrationTaskHandler extends AbstractPaymentGatewayTaskHandler {

    private static final Logger logger = LogManager.getLogger(StudentRegistrationTaskHandler.class);

    private final StudentRegistrationDao studentRegistrationDao;

    public StudentRegistrationTaskHandler(TransactionTemplate transactionTemplate, UserWalletManager userWalletManager,
                                          StudentRegistrationDao studentRegistrationDao) {
        super(transactionTemplate, userWalletManager);
        this.studentRegistrationDao = studentRegistrationDao;
    }

    @Override
    public boolean validInitiateTransactionPayload(int instituteId, PGInitiateTransactionPayload initiateTransactionPayload) {
        return Double.compare(initiateTransactionPayload.getPaymentGatewayAmount(), 0d) > 0;
    }

    @Override
    public void updateInitiateTransactionMetadata(Map<String, Object> metadata, PGInitiateTransactionPayload initiateTransactionPayload) {
        //Noting to update in metadata
    }

    @Override
    public PaymentGatewayAmountTxnResponse handleSuccessTransaction(PaymentGatewayTransactionData transactionData) {
        StudentRegistrationPaymentData studentRegistrationPaymentData = new StudentRegistrationPaymentData();
        studentRegistrationPaymentData.setPaidAmount(transactionData.getPaymentGatewayAmount());
        studentRegistrationPaymentData.setDiscountAmount(0d);
        studentRegistrationPaymentData.setTransactionMode(TransactionMode.PAYMENT_GATEWAY);

        boolean success = studentRegistrationDao.updateStudentRegistrationPaymentData(transactionData.getInstituteId(), transactionData.getUserId(), studentRegistrationPaymentData);
        if (!success) {
            //Amount is added in wallet by caller method in case of failure
            logger.error("Unable to execute transaction for registration payment data {}. Skipping any payment update", transactionData);
            return new PaymentGatewayAmountTxnResponse(transactionData.getTransactionType(), false, null, null, INVALID_TRANSACTION_ID, "Unable to execute transaction", null);
        }

        logger.info("Successfully received the registration payment for user {}, {}", transactionData.getUserId(), transactionData);
        return new PaymentGatewayAmountTxnResponse(transactionData.getTransactionType(), true, null, null, SUCCESS_CODE, "Registration payment is successful", null);
    }

    @Override
    public PaymentGatewaySystemTransactionDetails getSystemTransactionDetails(int instituteId, PGTransactionType transactionType, PaymentGatewayAmountTxnResponse paymentGatewayAmountTxnResponse) {
        return new PaymentGatewaySystemTransactionDetails(transactionType, null, null);
    }
}
