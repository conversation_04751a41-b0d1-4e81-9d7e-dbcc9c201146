package com.embrate.cloud.core.lib.service.payment.gateway.merchant;

import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayServiceProvider;
import com.embrate.cloud.core.api.service.payment.gateway.merchants.PGMerchantDetails;
import com.embrate.cloud.dao.tier.service.payment.gateway.PaymentGatewayMerchantDetailsDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class PaymentGatewayMerchantManager {
    private static final Logger logger = LogManager.getLogger(PaymentGatewayMerchantManager.class);

    private final PaymentGatewayMerchantDetailsDao paymentGatewayMerchantDetailsDao;

    public PaymentGatewayMerchantManager(PaymentGatewayMerchantDetailsDao paymentGatewayMerchantDetailsDao) {
        this.paymentGatewayMerchantDetailsDao = paymentGatewayMerchantDetailsDao;
    }

    public PGMerchantDetails getPaymentMerchant(int instituteId, PaymentGatewayServiceProvider paymentGatewayServiceProvider){
        if(instituteId <= 0 || paymentGatewayServiceProvider == null){
            logger.error("Invalid query for instituteId {}, paymentGatewayServiceProvider {}", instituteId, paymentGatewayServiceProvider);
            return null;
        }
        List<PGMerchantDetails> merchantDetailsList = getAllActiveMerchants(instituteId, paymentGatewayServiceProvider);
        if(CollectionUtils.isEmpty(merchantDetailsList)){
            logger.error("No active merchant found for instituteId {}, {}", instituteId, paymentGatewayServiceProvider);
            return null;
        }
//        if(merchantDetailsList.size() == 1){
//            return merchantDetailsList.get(0);
//        }
//        //TODO: any specific logic in case of multiple merchants
//        Collections.sort(merchantDetailsList, new Comparator<PGMerchantDetails>() {
//            @Override
//            public int compare(PGMerchantDetails o1, PGMerchantDetails o2) {
//                return o1.getAddedAt() - o2.getAddedAt();
//            }
//        });
        for(PGMerchantDetails pgMerchantDetails : merchantDetailsList) {
            if(pgMerchantDetails.getServiceProvider() == paymentGatewayServiceProvider) {
                return pgMerchantDetails;
            }
        }
//        return merchantDetailsList.get(0);
        return null;
    }

    public List<PGMerchantDetails> getAllActiveMerchants(int instituteId, PaymentGatewayServiceProvider paymentGatewayServiceProvider){
        if(instituteId <= 0 || paymentGatewayServiceProvider == null){
            logger.error("Invalid query for instituteId {}, paymentGatewayServiceProvider {}", instituteId, paymentGatewayServiceProvider);
            return null;
        }
        return paymentGatewayMerchantDetailsDao.getAllActiveMerchants(instituteId, paymentGatewayServiceProvider);
    }

    public UUID addMerchant(PGMerchantDetails merchantDetails){
        if(merchantDetails == null || merchantDetails.getInstituteId() <= 0 || merchantDetails.getServiceProvider() == null){
            logger.error("Invalid merchant add payload.");
            return null;
        }
        if(merchantDetails.getServiceProvider() == PaymentGatewayServiceProvider.CASH_FREE) {
            if(StringUtils.isBlank( merchantDetails.getMerchantName()) || StringUtils.isBlank( merchantDetails.getMerchantKey())
                    || StringUtils.isBlank( merchantDetails.getMerchantSecret()) || merchantDetails.getStatus() == null) {
                logger.error("Invalid merchant add payload.");
                return null;
            }
        }
        if(merchantDetails.getServiceProvider() == PaymentGatewayServiceProvider.CASH_FREE) {
            if(merchantDetails.getMetadata() == null || CollectionUtils.isEmpty(merchantDetails.getMetadata().entrySet())) {
                logger.error("Invalid merchant add payload.");
                return null;
            }
        }
        return paymentGatewayMerchantDetailsDao.addMerchant(merchantDetails);
    }

}
