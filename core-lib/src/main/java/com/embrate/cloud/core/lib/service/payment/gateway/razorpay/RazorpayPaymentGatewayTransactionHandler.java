package com.embrate.cloud.core.lib.service.payment.gateway.razorpay;

import com.embrate.cloud.core.api.service.payment.gateway.PGProcessTransactionPayload;
import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayTransactionStatus;
import com.embrate.cloud.core.api.service.payment.gateway.merchants.PGMerchantDetails;
import com.embrate.cloud.core.api.service.payment.gateway.razorpay.RazorpayPaymentWebhookResponse;
import com.embrate.cloud.core.lib.service.payment.gateway.IPaymentGatewayTransactionHandler;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.utils.SharedConstants;
import com.razorpay.RazorpayClient;
import com.razorpay.RazorpayException;
import com.razorpay.Utils;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.util.CollectionUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class RazorpayPaymentGatewayTransactionHandler implements IPaymentGatewayTransactionHandler {
	private static final Logger logger = LogManager.getLogger(RazorpayPaymentGatewayTransactionHandler.class);

	private static final String SUCCESS_STATUS = "captured";
	private static final String FAILED_STATUS = "failed";
//	private static final String FLAGGED_STATUS = "FLAGGED";
//	private static final String PENDING_STATUS = "PENDING";
//	private static final String CANCELLED_STATUS = "CANCELLED";
//	private static final String INCOMPLETE_STATUS = "INCOMPLETE";
	public static final String CANCELLED_CODE = "2";
	public static final String CODE = "code";


	private final PGProcessTransactionPayload processTransactionPayload;
	private final boolean webhook;
	private final RazorpayPaymentWebhookResponse webhookResponse;
	private final PGMerchantDetails merchantDetails;
	private final boolean invalidPayload;
	private final String webhookSecret;
	private String transactionId;

	private final RazorpayClient razorpay;

	public RazorpayPaymentGatewayTransactionHandler(PGProcessTransactionPayload processTransactionPayload, PGMerchantDetails merchantDetails, String webhookSecret) {
		this.processTransactionPayload = processTransactionPayload;
		this.merchantDetails = merchantDetails;
		this.invalidPayload = isInvalidPayload(processTransactionPayload);
		this.webhookSecret = webhookSecret;
		RazorpayPaymentWebhookResponse webhookResponseData = null;
		if (invalidPayload) {
			webhook = false;
		} else {
			webhook = processTransactionPayload.isWebhook();
			if (webhook) {
				try {
					webhookResponseData = SharedConstants.OBJECT_MAPPER.readValue(processTransactionPayload.getWebhookData(), RazorpayPaymentWebhookResponse.class);
                    this.transactionId = CollectionUtils.isEmpty(processTransactionPayload.getTransactionData()) ? null :
                            processTransactionPayload.getTransactionData().get("transactionId");
				} catch (Exception e) {
					logger.error("Error while parsing webhook data {}", processTransactionPayload.getWebhookData());
				}
			}
		}
		webhookResponse = webhookResponseData;
		try {
			this.razorpay = new RazorpayClient(merchantDetails.getMerchantKey(), merchantDetails.getMerchantSecret());
		} catch (Exception e) {
			logger.error("Error while init RazorpayClient");
			throw new EmbrateRunTimeException("Error while init RazorpayClient");
		}
	}

	@Override
	public UUID getTransactionId() {
		if (invalidPayload) {
			return null;
		}
		if (webhook) {
			String orderId = transactionId;
			if(orderId == null) {
				return null;
			}
			return UUID.fromString(orderId);
		} else {
			String orderId = processTransactionPayload.getTransactionData().get("transactionId");
			return UUID.fromString(orderId);
		}
	}

	@Override
	public PaymentGatewayTransactionStatus getTransactionStatus() {
		if (invalidPayload) {
			return null;
		}

		String txStatus;
		if (webhook) {
			txStatus = webhookResponse.getPayload().getPayment().getEntity().getStatus();
		} else {
			String paymentId = processTransactionPayload.getTransactionData().get("paymentId");
			if (StringUtils.isNotBlank(paymentId)) {
				return PaymentGatewayTransactionStatus.SUCCESS;
			}
			Map<String, String> transactionData = processTransactionPayload.getTransactionData();
			if (transactionData.containsKey(CODE) && transactionData.get(CODE).equalsIgnoreCase(CANCELLED_CODE)) {
				// Cancelled state
				return PaymentGatewayTransactionStatus.CANCELLED;
			}
			return PaymentGatewayTransactionStatus.FAILED;
		}

		if (StringUtils.isBlank(txStatus)) {
			return null;
		}
		switch (txStatus) {
			case SUCCESS_STATUS:
				return PaymentGatewayTransactionStatus.SUCCESS;
			case FAILED_STATUS:
				return PaymentGatewayTransactionStatus.FAILED;
//			case PENDING_STATUS:
//				return PaymentGatewayTransactionStatus.PENDING;
//			case INCOMPLETE_STATUS:
//				return PaymentGatewayTransactionStatus.INCOMPLETE;
//			case CANCELLED_STATUS:
//				return PaymentGatewayTransactionStatus.CANCELLED;
//			case FLAGGED_STATUS:
//				return PaymentGatewayTransactionStatus.FLAGGED;
			default:
				return PaymentGatewayTransactionStatus.UNKNOWN;
		}
	}

	@Override
	public boolean validTransactionSignature() {
		if (invalidPayload) {
			return false;
		}

		try {
			if (webhook) {
				String signature = processTransactionPayload.getWebhookHeaderSignature();
				String payload = processTransactionPayload.getWebhookData();

				logger.info("=========Signature==========={}", signature);
				logger.info("=========payload==========={}", payload);
				logger.info("=========webhookSecret==========={}", webhookSecret);

				return Utils.verifyWebhookSignature(payload, signature, webhookSecret);

			} else {

				Map<String, String> transactionData = processTransactionPayload.getTransactionData();
				logger.info("transactionData {}", transactionData);
				if (transactionData.containsKey(CODE) && transactionData.get(CODE).equalsIgnoreCase(CANCELLED_CODE)) {
					// Cancelled state
					return true;
				}

				String secret = merchantDetails.getMerchantSecret(); // To be updated

				if(!transactionData.containsKey("orderId") || !transactionData.containsKey("paymentId") || !transactionData.containsKey("signature")){
					logger.error("Mandatory params are missing to verify Razorpay transaction signature {}", transactionData);
					return false;
				}

				JSONObject options = new JSONObject();
				if(transactionData.containsKey("orderId")){
					options.put("razorpay_order_id", processTransactionPayload.getTransactionData().get("orderId"));
				}
				if(transactionData.containsKey("paymentId")){
					options.put("razorpay_payment_id", processTransactionPayload.getTransactionData().get("paymentId"));
				}
				if(transactionData.containsKey("signature")){
					options.put("razorpay_signature", processTransactionPayload.getTransactionData().get("signature"));
				}

				logger.info("Razorpay transaction signature match payload {} \n", options.toString());

				boolean verified = Utils.verifyPaymentSignature(options, secret);
				if (verified) {
					logger.info("Successfully verified the Razorpay transaction signature {} \n", options.toString());
				} else {
					logger.error("Failed to verify Razorpay transaction signature {}", options.toString());
				}
				return verified;
			}
		} catch (Exception e) {
			logger.error("Exception during signature verification for {}", processTransactionPayload, e);
			return false;
		}
	}

	@Override
	public Map<String, String> getTransactionData() {
		if (invalidPayload) {
			return null;
		}
		return processTransactionPayload.getTransactionData();
	}

	@Override
	public boolean isWebhook() {
		return webhook;
	}

	@Override
	public String getWebhookData() {
		if (invalidPayload) {
			return null;
		}
		return processTransactionPayload.getWebhookData();
	}

	private boolean isInvalidPayload(PGProcessTransactionPayload processTransactionPayload) {
		if (processTransactionPayload == null) {
			return true;
		}
		if (!processTransactionPayload.isWebhook()) {
			return MapUtils.isEmpty(processTransactionPayload.getTransactionData());
		}
		return StringUtils.isBlank(processTransactionPayload.getWebhookData());
	}
}
