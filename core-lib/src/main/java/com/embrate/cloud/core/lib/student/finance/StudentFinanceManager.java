package com.embrate.cloud.core.lib.student.finance;

import com.embrate.cloud.core.api.student.finance.StudentExpenseService;
import com.embrate.cloud.core.api.student.finance.ServiceExpensePayload;
import com.embrate.cloud.core.api.student.finance.StudentFinancePreferences;
import com.embrate.cloud.core.api.wallet.WalletPreferences;
import com.embrate.cloud.core.lib.inventory.v2.InventoryTransactionsManager;
import com.embrate.cloud.core.lib.wallet.UserWalletManager;
import com.embrate.cloud.dao.tier.student.finance.StudentFinanceDao;
import com.lernen.cloud.core.api.common.DBLockMode;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.notification.ExcelNotificationPayload;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.TaggedActions;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.RestrictionUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.UUID;

public class StudentFinanceManager {

	private static final Logger logger = LogManager.getLogger(InventoryTransactionsManager.class);
	private final StudentFinanceDao studentFinanceDao;
	private final StudentManager studentManager;
	private final UserPreferenceSettings userPreferenceSettings;
	private final UserPermissionManager userPermissionManager;
	private final UserWalletManager userWalletManager;


	public StudentFinanceManager(StudentFinanceDao studentFinanceDao, StudentManager studentManager, UserPreferenceSettings userPreferenceSettings, UserPermissionManager userPermissionManager, UserWalletManager userWalletManager) {
		this.studentFinanceDao = studentFinanceDao;
		this.studentManager = studentManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.userPermissionManager = userPermissionManager;
		this.userWalletManager = userWalletManager;
	}

	public UUID addServiceExpenseTransaction(int instituteId, UUID userId, ServiceExpensePayload serviceExpensePayload) {

		if (userId == null) {
			logger.error("Invalid user for {}, {}", instituteId, serviceExpensePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user provided"));
		}

		if (!DateUtils.isSameDayAsToday(serviceExpensePayload.getTransactionDate()* 1000L)) {
			userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ADD_SERVICE_BACK_DATE_TRANSACTIONS);
		}

		validateServiceExpensePayload(serviceExpensePayload);

		final Student student = studentManager.getStudentWithoutSession(serviceExpensePayload.getStudentId());
		RestrictionUtils.isActionRestricted(student, TaggedActions.STUDENT_FINANCE_RESTRICTED, true);

		StudentFinancePreferences studentFinancePreferences = userPreferenceSettings.getStudentFinancePreferences(serviceExpensePayload.getInstituteId());
		WalletPreferences walletPreferences = userPreferenceSettings.getStudentWalletPreferences(serviceExpensePayload.getInstituteId());


		Double walletAmount = null;
		if (serviceExpensePayload.getTransactionMode() == TransactionMode.WALLET) {
			walletAmount = userWalletManager.getWalletAmount(serviceExpensePayload.getStudentId(), DBLockMode.FOR_UPDATE);
			if (walletAmount == null) {
				logger.error("Error getting wallet amount for student {}", serviceExpensePayload.getStudentId());
				throw new EmbrateRunTimeException("Error while getting wallet amount for student");
			}
			Double paidAmount = serviceExpensePayload.getPaidAmount();

			if (walletAmount >= paidAmount) {
				serviceExpensePayload.setUsedWalletAmount(paidAmount);
				serviceExpensePayload.setPaidAmount(0d);
			} else {
				double usedWalletAmount = Math.max(walletAmount, 0d);
				serviceExpensePayload.setUsedWalletAmount(usedWalletAmount);
				double remainingAmount = paidAmount - usedWalletAmount;

				if (!studentFinancePreferences.isAllowWalletBasedCredit()) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SERVICE, "Wallet-based credit is not enabled for this institute."));
				}
				Double creditLimit = walletPreferences.getWalletBasedCreditLimit();


				if (creditLimit == null || Double.compare(creditLimit, 0d) < 0) {
					logger.error("Invalid credit limit on wallet, institute {} ",
							serviceExpensePayload.getInstituteId());
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SERVICE,
							"Invalid credit limit on wallet for inventory in institute"));
				}

				if (Double.compare(-creditLimit, walletAmount - usedWalletAmount - remainingAmount) > 0) {
					throw new ApplicationException(
							new ErrorResponse(ApplicationErrorCode.INVALID_SERVICE, "This transaction exceeds the allowed credit limit of " + creditLimit ));
				}

				serviceExpensePayload.setWalletCreditAmount(remainingAmount);
				serviceExpensePayload.setPaidAmount(0d);
			}
		}
		return studentFinanceDao.addServiceExpenseTransaction(instituteId, userId, serviceExpensePayload, studentFinancePreferences);
	}

	private void validateServiceExpensePayload(ServiceExpensePayload serviceExpensePayload) {
		if (serviceExpensePayload.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}
		if (serviceExpensePayload.getAcademicSessionId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_ACADEMIC_SESSION_DETAILS, "Invalid academic session id"));
		}
		if (serviceExpensePayload.getStudentId() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid student id"));
		}
		if (serviceExpensePayload.getTransactionMode() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid transaction mode"));
		}
		if (serviceExpensePayload.getTransactionDate() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid transaction date"));
		}
	}

		public StudentExpenseService getExpenseServiceByServiceId(int instituteId, UUID expenseServiceId) {

		if (instituteId <= 0) {
			logger.error("Invalid institute {}", instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute provided"));
		}

		if (expenseServiceId == null) {
			logger.error("Invalid service for {}", instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid service provided"));
		}

			StudentExpenseService studentExpenseService = studentFinanceDao.getExpenseServiceByServiceId(instituteId, expenseServiceId);
			if (studentExpenseService == null) {
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SERVICE,
						String.format("No service found for serviceId: %s and instituteId: %d", expenseServiceId, instituteId)));
			}
			return studentExpenseService;
	}

	public List<StudentExpenseService> getStudentExpenseServices(int instituteId) {

		if (instituteId <= 0) {
			logger.error("Invalid institute {}", instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute provided"));
		}

		return studentFinanceDao.getStudentExpenseServices(instituteId, null);
	}
}
