/**
 * 
 */
package com.embrate.cloud.core.lib.timetable;

import com.embrate.cloud.core.api.timetable.*;
import com.embrate.cloud.dao.tier.timetable.TimetableDao;
import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.course.ClassCourses;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.CloneStandardWithStaffDetailsPayload;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardRowDetails;
import com.lernen.cloud.core.api.institute.StandardWithStaffDetails;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.User;
import com.embrate.cloud.core.lib.courses.CourseManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.student.StudentManager;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.time.DayOfWeek;
import java.util.*;

/**
 * <AUTHOR>
 *
 */
public class TimetableManager {
	
	private static final Logger logger = LogManager.getLogger(TimetableManager.class);

	private final TimetableDao timetableDao;
	private final UserPermissionManager userPermissionManager;
	private final CourseManager courseManager;
	private final StudentManager studentManager;
	private final TransactionTemplate transactionTemplate;
	private final InstituteManager instituteManager;
	
	public TimetableManager(TimetableDao timetableDao, UserPermissionManager userPermissionManager,
			CourseManager courseManager, StudentManager studentManager, TransactionTemplate transactionTemplate, InstituteManager instituteManager) {
		this.timetableDao = timetableDao;
		this.userPermissionManager = userPermissionManager;
		this.courseManager = courseManager;
		this.studentManager = studentManager;
		this.transactionTemplate = transactionTemplate;
		this.instituteManager = instituteManager;
	}

	public boolean addActivityMetadata(int instituteId, UUID userId, ActivityMetadata activityMetadata) {
		if (userId == null) {
			logger.error("Empty userId whiling adding school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Incorrect userId whiling adding school shifts"));
		}
		if (instituteId <= 0) {
			logger.error("invalid institute id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Invalid institute Id"));
		}

		//Check authentication
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ACTIVITY_CONFIGURATIONS);

		//validate
		validateActivityMetadata(activityMetadata, false);

		//Check if activity exists with same name (fetch activity as per entity id)
		ActivityMetadata existingActivityMetadata = timetableDao.getActivityMetadataByName(instituteId, activityMetadata.getAcademicSessionId(),
						activityMetadata.getEntityName(), activityMetadata.getEntityId(), activityMetadata.getActivityName());
		if(existingActivityMetadata != null) {
			logger.error("Activity with this name already exists for this entity.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Activity with this name already exists for this entity. Please change the name and then save."));
		}

		return timetableDao.addActivityMetadata(activityMetadata);
	}

	public List<String> cloneSubjectTeacher(CloneStandardWithStaffDetailsPayload cloneStandardWithStaffDetailsPayload){
		List<String> errorMessage = new ArrayList<>();
		if(!cloneSubjectTeacherAssignment(cloneStandardWithStaffDetailsPayload.getSrcInstituteId(), cloneStandardWithStaffDetailsPayload.getDestInstituteId(), cloneStandardWithStaffDetailsPayload.getSrcAcademicSessionId(), cloneStandardWithStaffDetailsPayload.getDestAcademicSessionId(), errorMessage)){
			errorMessage.add("Not able to Clone the Subject Teacher");
		}
		return errorMessage;
	}

	private boolean cloneSubjectTeacherAssignment(int srcInstituteId, int destInstituteId, int srcAcademicSessionId, int destAcademicSessionId, List<String> errorMessages){
		if(srcInstituteId <= 0 || destInstituteId <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}
		if(srcAcademicSessionId <= 0 || destAcademicSessionId <= 0) {
			logger.error("Invalid academic session id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id"));
		}
		List<StaffStandardEntityDetailsRow> staffStandardEntityDetailsList = timetableDao.getStaffStandardEntityDetailsRow(srcInstituteId, srcAcademicSessionId);
		if(CollectionUtils.isEmpty(staffStandardEntityDetailsList)){
			errorMessages.add("No data Found for the Standard Subject Teacher");
			return false;
		}
		if(srcInstituteId != destInstituteId && !instituteManager.isSameOrganization(srcInstituteId, destInstituteId)){
			errorMessages.add("Destination Institute Id don't belong to same Organisation So cloning is not possible");
			return false;
		}
		List<ClassCourses> classCourses = courseManager.getClassCoursesByInstitute(destInstituteId, destAcademicSessionId, null);
		List<Standard> destStandard = instituteManager.getInstituteStandardDetails(destInstituteId, destAcademicSessionId);
		Map<String, UUID> standardNameAndIdMap = new HashMap<>();
		Map<String, Map<String, Integer>> sectionNameAndIdMap = new HashMap<>();
		Map<String, Map<String, UUID>> classCoursesNameAndIdMap = new HashMap<>();
		instituteManager.getStandardAndSectionMap(destStandard, standardNameAndIdMap, sectionNameAndIdMap, true);
		getClassCourseMap(classCourses, classCoursesNameAndIdMap);
		if(MapUtils.isEmpty(standardNameAndIdMap)){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,"No data Found for the Destination Institute Standard"));
		}
		if(MapUtils.isEmpty(classCoursesNameAndIdMap)){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,"No data Found for the Destination Institute Courses"));
		}
		List<ClassCoursesActivityDetailsPayload> classCoursesActivityDetailsPayloadList = new ArrayList<>();
		for (StaffStandardEntityDetailsRow staffEntityDetail : staffStandardEntityDetailsList) {
			final StandardRowDetails standardRowDetails = staffEntityDetail.getStandardRowDetails();
		
			if (standardRowDetails == null || staffEntityDetail.getEntity() == Entity.ACTIVITY) {
				continue;
			}
		
			final String displayName = standardRowDetails.getDisplayName();
			final String sectionName = standardRowDetails.getSectionName();
			final Integer sectionId = standardRowDetails.getSectionId();
			final Entity entity = staffEntityDetail.getEntity();
			final String entityIdVal = staffEntityDetail.getEntityIdVal();
			final UUID entityId = staffEntityDetail.getEntityId();
			final Staff staff = staffEntityDetail.getStaff();
		
			if (entity == null || entityId == null) {
				errorMessages.add("Skipping the Standard: " + displayName + " Section: " + sectionName + 
								  " Course: " + entityIdVal + " Due To Course Name Not Found In Source Institute.");
				continue;
			}
		
			if (staff == null) {
				errorMessages.add("Skipping the Standard: " + displayName + " Section: " + sectionName + 
								  " Due To Staff is not 'assigned' in the Previous session");
				continue;
			}
		
			if (staff.getStaffStatus() == StaffStatus.RELIEVED) {
				errorMessages.add("Skipping the Standard: " + displayName + " Section: " + sectionName + 
								  " Due To Staff is in 'Relieved' state.");
				continue;
			}
		
			final UUID destStandardId = standardNameAndIdMap.get(displayName);
			if (destStandardId == null) {
				errorMessages.add("Skipping the Standard: " + displayName + " Section: " + sectionName + 
								  " Due To Standard Name Not Found In Destination Institute.");
				continue;
			}
		
			Integer destMappedSectionId = null;
			if (sectionId != null) {
				final Map<String, Integer> sectionMap = sectionNameAndIdMap.get(displayName);
				if (MapUtils.isEmpty(sectionMap) || !sectionMap.containsKey(sectionName)) {
					errorMessages.add("Skipping the Standard: " + displayName + " Section: " + sectionName + 
									  " Due To Section Name Not Found In Destination Institute.");
					continue;
				}
				destMappedSectionId = sectionMap.get(sectionName);
			}
		
			final Map<String, UUID> courseMap = classCoursesNameAndIdMap.get(displayName);
			if (courseMap == null || MapUtils.isEmpty(courseMap) || courseMap.get(entityIdVal) == null) {
				errorMessages.add("Skipping the Standard: " + displayName + " Section: " + sectionName + 
								  " Course: " + entityIdVal + " Due To Course Name Not Found In Destination Institute.");
				continue;
			}
		
			final Set<UUID> staffIdList = new HashSet<>();
			staffIdList.add(staff.getStaffId());
		
			final List<CoursesActivityDetailsPayload> coursePayloadList = new ArrayList<>();
			coursePayloadList.add(new CoursesActivityDetailsPayload(
				entity,
				courseMap.get(entityIdVal),
				staffIdList
			));
		
			final ClassCoursesActivityDetailsPayload classCoursePayload = new ClassCoursesActivityDetailsPayload(
				destAcademicSessionId,
				destStandardId,
				destMappedSectionId,
				coursePayloadList
			);
		
			classCoursesActivityDetailsPayloadList.add(classCoursePayload);
		}
		
		if(CollectionUtils.isEmpty(classCoursesActivityDetailsPayloadList)){
			errorMessages.add("Error while making classCoursesActivityDetailsPayload");
			return false;
		}
		return timetableDao.saveStaffEntityAssignmentApiCall(classCoursesActivityDetailsPayloadList);
	}

	private void getClassCourseMap(List<ClassCourses> classCoursesList, Map<String, Map<String, UUID>> classCoursesNameAndIdMap){
		if(CollectionUtils.isEmpty(classCoursesList)){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,"No data Found for the Destination Institute Courses"));
		}
		for(ClassCourses classCourse : classCoursesList){
			if(CollectionUtils.isEmpty(classCourse.getCourses())){
				continue;
			}
			Map<String,UUID> coursesNameAndIdMap = new HashMap<>();
			for(Course course : classCourse.getCourses()){
				if(course.getCourseId() == null){
					continue;
				}
				coursesNameAndIdMap.putIfAbsent(course.getCourseName(), course.getCourseId());
			}
			classCoursesNameAndIdMap.putIfAbsent(classCourse.getStandard().getDisplayName(), coursesNameAndIdMap);
		}
	}

	private void validateActivityMetadata(ActivityMetadata activityMetadata, boolean update) {

		if (activityMetadata.getInstituteId() <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Incorrect institute id."));
		}

		if (activityMetadata.getAcademicSessionId() <= 0) {
			logger.error("Incorrect academic session Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Incorrect academic session id."));
		}

		if(update) {
			if (activityMetadata.getActivityId() == null) {
				logger.error("Incorrect activity Id");
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
						"Incorrect activity id."));
			}
		}

		if (StringUtils.isBlank(activityMetadata.getActivityName())) {
			logger.error("Incorrect activity name.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Activity name cannot be empty."));
		}

		if (StringUtils.isBlank(activityMetadata.getEntityId())) {
			logger.error("Incorrect entity id.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Entity Id cannot be empty."));
		}

		if (activityMetadata.getEntityName() == null) {
			logger.error("Incorrect entity name.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Entity name cannot be null."));
		}
	}

	public ActivityMetadata getActivityMetadataById(int instituteId, int academicSessionId, UUID activityId) {
		if (instituteId <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Incorrect institute id."));
		}
		if (academicSessionId <= 0) {
			logger.error("Incorrect academic session Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Incorrect academic session id."));
		}
		if (activityId == null) {
			logger.error("Incorrect activity Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Incorrect activity id."));
		}

		return timetableDao.getActivityMetadataById(instituteId, academicSessionId, activityId);
	}

	public List<ActivityMetadata> getActivityMetadata(int instituteId, int academicSessionId, ActivityEntity entityName,
															  String entityId) {
		if (instituteId <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Incorrect institute id."));
		}
		if (academicSessionId <= 0) {
			logger.error("Incorrect academic session Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Incorrect academic session id."));
		}
		return sortActivityMetadata(timetableDao.getActivityMetadata(instituteId, academicSessionId, entityName, entityId));
	}

	private List<ActivityMetadata> sortActivityMetadata(List<ActivityMetadata> activityMetadataList) {
		Collections.sort(activityMetadataList, new Comparator<ActivityMetadata>() {
			@Override
			public int compare(ActivityMetadata s1, ActivityMetadata s2) {
				return StringHelper.compareIgnoreCase(s1.getActivityName(),
						s2.getActivityName());
			}
		});
		return activityMetadataList;
	}

	public boolean updateActivityMetadata(int instituteId, UUID userId, ActivityMetadata activityMetadata) {
		if (userId == null) {
			logger.error("Empty userId whiling adding school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Incorrect userId whiling adding school shifts"));
		}
		if (instituteId <= 0) {
			logger.error("invalid institute id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Invalid institute Id"));
		}

		//Check authentication
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ACTIVITY_CONFIGURATIONS);

		//validate
		validateActivityMetadata(activityMetadata, true);
		
		//Check if activity exists with same name (fetch activity as per entity id)
		ActivityMetadata existingActivityMetadata = timetableDao.getActivityMetadataByName(instituteId, activityMetadata.getAcademicSessionId(),
				activityMetadata.getEntityName(), activityMetadata.getEntityId(), activityMetadata.getActivityName());
		if(existingActivityMetadata != null) {
			if(existingActivityMetadata.getActivityId() != activityMetadata.getActivityId()) {
				logger.error("Activity with this name already exists for this entity.");
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
						"Activity with this name already exists for this entity. Please change the name and then save."));
			}
		}

		return timetableDao.updateActivityMetadata(activityMetadata);
	}

	public boolean deleteActivityMetadata(int instituteId, int academicSessionId, UUID userId, UUID activityId) {
		if (instituteId <= 0) {
			logger.error("Incorrect instituteId whiling deleting activity");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Incorrect instituteId whiling deleting activity"));
		}
		if (userId == null) {
			logger.error("Empty userId whiling updating activity");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Incorrect userId whiling updating activity"));
		}
		userPermissionManager.verifyAuthorisation(instituteId,
				userId, AuthorisationRequiredAction.ACTIVITY_CONFIGURATIONS);
		if (activityId == null) {
			logger.error("Incorrect activityId whiling updating activity");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Incorrect activityId whiling updating activity"));
		}
		//TODO: check if any faculty is assign to this activity
		// or if this activity is been used
		// in any timetable or substitution cases.
		// If yes, give throw exception.
//		List<TimetableMetaData> timetableMetaDataList = timetableDao
//				.getTimetableMetaDataByShiftId(instituteId, academicSessionId, shiftId);
//		if(!CollectionUtils.isEmpty(timetableMetaDataList)) {
//			logger.error("Shift {} already used in creating timetable.", shiftId);
//			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
//					"Shift is already been used to create a timetable. Please delete that and try again."));
//		}
		return timetableDao.deleteActivityMetadata(instituteId, academicSessionId, activityId);
	}

	public boolean staffEntityAssignment(int instituteId, UUID userId,
										 ClassCoursesActivityDetailsPayload classCoursesActivityDetailsPayload) {
		if (userId == null) {
			logger.error("Empty userId whiling adding school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Incorrect userId whiling adding school shifts"));
		}
		if (instituteId <= 0) {
			logger.error("invalid institute id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_ACTIVITY,
					"Invalid institute Id"));
		}

		//Check authentication
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FACULTY_ASSIGNMENT_CONFIGURATIONS);

		//validate
		validateClassCoursesActivityDetailsPayload(classCoursesActivityDetailsPayload);

		return timetableDao.staffEntityAssignment(classCoursesActivityDetailsPayload);
	}

	private void validateClassCoursesActivityDetailsPayload(ClassCoursesActivityDetailsPayload classCoursesActivityDetailsPayload) {

		if (classCoursesActivityDetailsPayload == null) {
			logger.error("Incorrect payload");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Incorrect payload."));
		}

		if (classCoursesActivityDetailsPayload.getAcademicSessionId() <= 0) {
			logger.error("Incorrect academic session Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Incorrect academic session id."));
		}

		if (classCoursesActivityDetailsPayload.getStandardId() == null) {
			logger.error("Incorrect standard Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Incorrect standard id."));
		}

		if (CollectionUtils.isEmpty(classCoursesActivityDetailsPayload.getCoursesActivityDetailsPayloadList())) {
			logger.error("Empty course-activity payload.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Empty course-activity payload."));
		}

		for(CoursesActivityDetailsPayload coursesActivityDetailsPayload : classCoursesActivityDetailsPayload.getCoursesActivityDetailsPayloadList()) {
			validateCoursesActivityDetailsPayload(coursesActivityDetailsPayload);
		}

	}

	private void validateCoursesActivityDetailsPayload(CoursesActivityDetailsPayload coursesActivityDetailsPayload) {
		if (coursesActivityDetailsPayload == null) {
			logger.error("Incorrect payload");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Incorrect payload."));
		}

		if (coursesActivityDetailsPayload.getEntity() == null) {
			logger.error("Incorrect entity.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Incorrect entity."));
		}

		if (coursesActivityDetailsPayload.getEntityId() == null) {
			logger.error("Incorrect entity Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Incorrect entity id."));
		}
	}

	public ClassCoursesActivityDetails getClassCoursesActivityDetails(int instituteId, int academicSessionId, UUID standardId,
																 Integer sectionId) {
		if (instituteId <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Incorrect institute id."));
		}
		if (academicSessionId <= 0) {
			logger.error("Incorrect academicSession Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Incorrect academicSession id."));
		}
		if (standardId == null) {
			logger.error("Incorrect standard Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Incorrect standard id."));
		}
		List<CoursesActivityDetails> coursesActivityDetailsList = timetableDao.getStaffEntityAssignment(instituteId,
				academicSessionId, standardId, sectionId);

		return new ClassCoursesActivityDetails(academicSessionId, standardId, sectionId,
				CollectionUtils.isEmpty(coursesActivityDetailsList) ? null : coursesActivityDetailsList);
	}

	public List<CoursesActivityDetails> getCourseActivityAssignmentByStaff(int instituteId, int academicSessionId, UUID staffId) {
		if (instituteId <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Incorrect institute id."));
		}
		if (academicSessionId <= 0) {
			logger.error("Incorrect academicSession Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Incorrect academicSession id."));
		}
		if (staffId == null) {
			logger.error("Incorrect standard Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Incorrect standard id."));
		}
		return timetableDao.getCourseActivityAssignmentByStaff(instituteId, academicSessionId, staffId);
	}

	public StaffStandardEntityDetails getStaffStandardEntityDetails(int instituteId, int academicSessionId, UUID staffId) {
		if (instituteId <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Incorrect institute id."));
		}
		if (academicSessionId <= 0) {
			logger.error("Incorrect academicSession Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Incorrect academicSession id."));
		}
		if (staffId == null) {
			logger.error("Incorrect standard Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FACULTY_ASSIGNMENT_DETAILS,
					"Incorrect standard id."));
		}
		return timetableDao.getStaffStandardEntityDetails(instituteId, academicSessionId, staffId);
	}

	public boolean addSchoolShiftConfigurations(int instituteId, UUID userId, SchoolShiftDetails schoolShiftDetails) {
		if (userId == null) {
			logger.error("Empty userId whiling adding school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect userId whiling adding school shifts"));
		}
		if (instituteId <= 0) {
			logger.error("invalid institute id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Invalid institute Id"));
		}
		//Check authentication
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ADD_SHIFT_CONFIGURATION);

		//Check if shift exists with same name
		SchoolShiftDetails existingSchoolShiftDetails = timetableDao
				.getSchoolShiftDetailsByShiftName(instituteId, schoolShiftDetails.getAcademicSessionId(),
						schoolShiftDetails.getShiftName());

		if(existingSchoolShiftDetails != null) {
			logger.error("Shift with this name already exists.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Shift with this name already exists. Please change the name and then save."));
		}

		//validate
		validateSchoolShiftPayload(schoolShiftDetails);

		schoolShiftDetails.setCreatedBy(userId);
		return timetableDao.addSchoolShiftConfigurations(schoolShiftDetails);
	}

	private void validateSchoolShiftPayload(SchoolShiftDetails schoolShiftDetails) {

		if (schoolShiftDetails.getInstituteId() <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect institute id."));
		}
		
		if (schoolShiftDetails.getAcademicSessionId() <= 0) {
			logger.error("Incorrect academic session Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect academic session id."));
		}
		
		if (StringUtils.isBlank(schoolShiftDetails.getShiftName())) {
			logger.error("Incorrect shift name.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Shift name cannot be empty."));
		}
		
		if (schoolShiftDetails.getShiftStartTime() == null) {
			logger.error("Incorrect shift start time.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Shift start time cannot be empty or less than 0."));
		}
		
		if (schoolShiftDetails.getShiftEndTime() == null) {
			logger.error("Incorrect shift end time.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Shift End time cannot be empty or less than 0."));
		}
		
		if (CollectionUtils.isEmpty(schoolShiftDetails.getDays())) {
			logger.error("Empty shift days.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Please select atleast one day to apply the shift."));
		}
		
		if (CollectionUtils.isEmpty(schoolShiftDetails.getShiftPeriodPayloadList())) {
			logger.error("Empty shift period details.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Please add period details for the shift"));
		}

		validateShiftPeriodDetails(schoolShiftDetails);
	}

	private void validateShiftPeriodDetails(SchoolShiftDetails schoolShiftDetails) {
		/**
		 * sorting periods by start time
		 */
		sortSchoolShiftDetailsByStartTime(schoolShiftDetails);
		for(int i = 0; i < schoolShiftDetails.getShiftPeriodPayloadList().size(); i ++) {

			ShiftPeriodDetails shiftPeriodDetails = schoolShiftDetails.getShiftPeriodPayloadList().get(i);
			String error = shiftPeriodDetails.validatePeriodDetails();
			if(!StringUtils.isEmpty(error)) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS, error));
			}

			if (shiftPeriodDetails.getPeriodStartTime().compareTo(schoolShiftDetails.getShiftStartTime()) < 0) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
								"Period start time cannot be less than shift start time."));
			}

			if (schoolShiftDetails.getShiftEndTime().compareTo(shiftPeriodDetails.getPeriodEndTime()) < 0) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
								"Period end time cannot be greater than shift end time."));
			}

			if(i == schoolShiftDetails.getShiftPeriodPayloadList().size() - 1) {
				continue;
			}

			ShiftPeriodDetails secondShiftPeriodPayload = schoolShiftDetails.getShiftPeriodPayloadList().get(i + 1);
			/**
			 * checking shiftPeriodDetails endTime with secondShiftPeriodPayload startTime
			 */
			if(shiftPeriodDetails.getPeriodEndTime().checkOverlapping(secondShiftPeriodPayload.getPeriodStartTime())) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
								"There cannot be any overlapping of time periods in a shift."));
			}
		}
	}

	public SchoolShiftDetails getSchoolShiftDetailsByShiftId(int instituteId, int academicSessionId, UUID shiftId) {
		if (instituteId <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect institute id."));
		}
		if (academicSessionId <= 0) {
			logger.error("Incorrect academic session Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect academic session id."));
		}
		if (shiftId == null) {
			logger.error("Incorrect shift Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect shift id."));
		}
		
		return sortSchoolShiftDetailsByStartTime(timetableDao.getSchoolShiftDetailsByShiftId(
				instituteId, academicSessionId, shiftId));
	}
	
	public SchoolShiftDetails sortSchoolShiftDetailsByStartTime(SchoolShiftDetails schoolShiftDetails) {
		if (schoolShiftDetails != null
				&& !(CollectionUtils.isEmpty(schoolShiftDetails.getShiftPeriodPayloadList()))) {
				Collections.sort(schoolShiftDetails.getShiftPeriodPayloadList(),
						new Comparator<ShiftPeriodDetails>() {
						@Override
						public int compare(ShiftPeriodDetails s1, ShiftPeriodDetails s2) {
							int compare = s1.getPeriodStartTime().compareTo(s2.getPeriodStartTime());
							return compare;
						}
				});
			}
		
		return schoolShiftDetails;
	}
	
	public List<SchoolShiftDetails> getSchoolShiftConfigurationList(int instituteId, int academicSessionId) {
		if (instituteId <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect institute id."));
		}
		
		if (academicSessionId <= 0) {
			logger.error("Incorrect academic session Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect academic session id."));
		}
		
		List<SchoolShiftDetails> schoolShiftDetailsList =  timetableDao.
				getSchoolShiftConfigurationList(instituteId, academicSessionId);
		
		if(CollectionUtils.isEmpty(schoolShiftDetailsList)) {
			return null;
		}
		
		List<SchoolShiftDetails> schoolShiftDetailsFinalList = new ArrayList<SchoolShiftDetails>();
		
		for(SchoolShiftDetails schoolShiftDetails : schoolShiftDetailsList) {
			schoolShiftDetailsFinalList.add(sortSchoolShiftDetailsByStartTime(schoolShiftDetails));
		}

		Collections.sort(schoolShiftDetailsFinalList, new Comparator<SchoolShiftDetails>() {
				@Override
				public int compare(SchoolShiftDetails s1, SchoolShiftDetails s2) {
					int compare = s2.getShiftName().compareTo(s1.getShiftName());
					return compare;
				}
			});

		return schoolShiftDetailsFinalList;
	}
	
	public boolean updateSchoolShiftConfigurations(int instituteId, UUID userId,
			SchoolShiftDetails schoolShiftDetails) {
		if (userId == null) {
			logger.error("Empty userId whiling updating school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect userId whiling updating school shifts"));
		}
		userPermissionManager.verifyAuthorisation(instituteId, 
				userId, AuthorisationRequiredAction.UPDATE_SHIFT_CONFIGURATION);
		if (schoolShiftDetails.getShiftId() == null) {
			logger.error("Incorrect shiftId whiling updating school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect shiftId whiling updating school shifts"));
		}
		//TODO:check name while updating
		validateSchoolShiftPayload(schoolShiftDetails);
		schoolShiftDetails.setUpdatedBy(userId);
		return timetableDao.updateSchoolShiftConfigurations(schoolShiftDetails);
	}

	public boolean deleteSchoolShiftConfigurations(int instituteId, int academicSessionId, UUID userId, UUID shiftId) {
		userPermissionManager.verifyAuthorisation(instituteId, 
				userId, AuthorisationRequiredAction.DELETE_SHIFT_CONFIGURATION);
		if (instituteId <= 0) {
			logger.error("Incorrect instituteId whiling deleting school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect instituteId whiling deleting school shifts"));
		}
		if (shiftId == null) {
			logger.error("Incorrect shiftId whiling updating school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect shiftId whiling updating school shifts"));
		}
		if (userId == null) {
			logger.error("Empty userId whiling updating school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect userId whiling updating school shifts"));
		}
		//check if any timetable is using this shift. If yes, give throw exception.
		List<TimetableMetaData> timetableMetaDataList = timetableDao
				.getTimetableMetaDataByShiftId(instituteId, academicSessionId, shiftId);
		if(!CollectionUtils.isEmpty(timetableMetaDataList)) {
			logger.error("Shift {} already used in creating timetable.", shiftId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Shift is already been used to create a timetable. Please delete that and try again."));
		}
		return timetableDao.deleteSchoolShiftConfigurations(instituteId, shiftId);
	}

	public boolean addTimetableDetails(int instituteId, UUID userId,
			TimetableMetaDataPayload timeTableMetaDataPayload) {
		if (userId == null) {
			logger.error("Empty userId whiling adding school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect userId whiling adding school shifts"));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId,
				AuthorisationRequiredAction.ADD_TIMETABLE_CONFIGURATION);
		validateTimetablePayload(timeTableMetaDataPayload);
		//check if timetable with same name is already here
		TimetableMetaData existingTimetableMetaData = timetableDao.getTimetableDetailsByName(instituteId, timeTableMetaDataPayload.getAcademicSessionId(),
				timeTableMetaDataPayload.getStandardId(), timeTableMetaDataPayload.getSectionId(), timeTableMetaDataPayload.getShiftId(), timeTableMetaDataPayload.getTimetableName());
		if(existingTimetableMetaData != null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Timetable with this name already exists. Please try some other name!"));
		}
		timeTableMetaDataPayload.setCreatedBy(userId);
		SchoolShiftDetails schoolShiftDetails = getSchoolShiftDetailsByShiftId(instituteId,
				timeTableMetaDataPayload.getAcademicSessionId(), timeTableMetaDataPayload.getShiftId());
		return timetableDao.addTimetableDetails(timeTableMetaDataPayload, schoolShiftDetails);
	}

	public boolean addTimetablePeriodDetails(int instituteId, UUID userId,
									   TimetablePeriodPayload timetablePeriodPayload) {
		if (userId == null) {
			logger.error("Empty userId whiling adding school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect userId whiling adding school shifts"));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId,
				AuthorisationRequiredAction.ADD_TIMETABLE_CONFIGURATION);
		validateTimetablePeriodPayload(timetablePeriodPayload);
		return timetableDao.addTimetablePeriodDetails(timetablePeriodPayload);
	}

	private void validateTimetablePayload(TimetableMetaDataPayload timeTableMetaDataPayload) {

		if (timeTableMetaDataPayload.getInstituteId() <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Incorrect institute id."));
		}

		if (timeTableMetaDataPayload.getAcademicSessionId() <= 0) {
			logger.error("Incorrect academic session Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Incorrect academic session id."));
		}

		if (timeTableMetaDataPayload.getShiftId() == null) {
			logger.error("Incorrect shift id.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Incorrect shift id."));
		}

		if (timeTableMetaDataPayload.getStandardId() == null) {
			logger.error("Incorrect standard id.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Incorrect standard id."));
		}

//		validateTimetablePeriodPayloadList(timeTableMetaDataPayload.getTimetablePeriodPayloadList());
	}

//	private void validateTimetablePeriodPayloadList(List<TimetablePeriodPayload> timetablePeriodPayloadList) {
//		for(TimetablePeriodPayload timetablePeriodPayload : timetablePeriodPayloadList) {
//			validateTimetablePeriodPayload(timetablePeriodPayload);
//		}
//	}

	private void validateTimetablePeriodPayload(TimetablePeriodPayload timetablePeriodPayload) {

		if (timetablePeriodPayload.getPeriodId() == null) {
			logger.error("Incorrect period id.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Incorrect period id."));
		}

		if (timetablePeriodPayload.getDay() == null) {
			logger.error("Incorrect day.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Incorrect day."));
		}
		if(!CollectionUtils.isEmpty(timetablePeriodPayload.getPeriodSlotPayloadList())) {
			for(PeriodSlotPayload periodSlotPayload : timetablePeriodPayload.getPeriodSlotPayloadList()) {

//				if (periodSlotPayload.getSlotId() == null) {
//					logger.error("Incorrect course id.");
//					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
//							"Incorrect course id."));
//				}

				if (periodSlotPayload.getPeriodSlotCategory() == null) {
					logger.error("Incorrect period slot category.");
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
							"Incorrect period slot category."));
				}

				if (periodSlotPayload.getStaffId() == null) {
					logger.error("Incorrect staff id.");
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
							"Incorrect staff id."));
				}
			}
		}
	}

//	public boolean updateTimetableDetails(int instituteId, UUID userId,
//			TimetableMetaDataPayload timeTableMetaDataPayload) {
//		if (userId == null) {
//			logger.error("Empty userId whiling updating school shifts");
//			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
//					"Incorrect userId whiling updating school shifts"));
//		}
//		userPermissionManager.verifyAuthorisation(instituteId,
//				userId, AuthorisationRequiredAction.UPDATE_TIMETABLE_CONFIGURATION);
//		if (timeTableMetaDataPayload.getTimetableId() == null) {
//			logger.error("Incorrect timetable whiling updating school shifts");
//			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
//					"Incorrect timetable whiling updating school shifts"));
//		}
//
//		TimetableMetaData existingTimetableMetaData = timetableDao.getTimetableDetailsByName(instituteId,
//						timeTableMetaDataPayload.getAcademicSessionId(),
//				timeTableMetaDataPayload.getStandardId(), timeTableMetaDataPayload.getSectionId(),
//				timeTableMetaDataPayload.getShiftId(), timeTableMetaDataPayload.getTimetableName());
//
//		if(existingTimetableMetaData != null) {
//			if(timeTableMetaDataPayload.getTimetableId() != existingTimetableMetaData.getTimetableId()) {
//				logger.error("Timetable with this name already exists.");
//				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
//						"Timetable with this name already exists. Please change the name and then save."));
//			}
//		}
//
//		validateTimetablePayload(timeTableMetaDataPayload);
//		checkOverlappingTimetablePayload(timeTableMetaDataPayload, true);
//		timeTableMetaDataPayload.setUpdatedBy(userId);
//		return timetableDao.updateTimetableDetails(timeTableMetaDataPayload);
//	}
//

	public boolean deleteTimetablePeriodDetails(int instituteId, UUID userId, UUID timetableId, UUID periodId, DayOfWeek day) {
		userPermissionManager.verifyAuthorisation(instituteId,
				userId, AuthorisationRequiredAction.DELETE_TIMETABLE_CONFIGURATION);
		if (instituteId <= 0) {
			logger.error("Incorrect instituteId whiling deleting school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Incorrect instituteId whiling deleting school shifts"));
		}
		if (timetableId == null) {
			logger.error("Incorrect timetableId whiling deleting school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Incorrect timetableId whiling deleting school shifts"));
		}
		if (periodId == null) {
			logger.error("Incorrect periodId whiling deleting period details");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Incorrect periodId whiling deleting period details"));
		}
		if (day == null) {
			logger.error("Incorrect day whiling deleting period details");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Incorrect day whiling deleting period details"));
		}
		if (userId == null) {
			logger.error("Empty userId whiling updating school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Incorrect userId whiling updating school shifts"));
		}
		return timetableDao.deleteTimetablePeriodDetails(instituteId, timetableId, periodId, day);
	}

	public boolean deleteTimetableDetails(int instituteId, UUID userId, UUID timetableId) {
		userPermissionManager.verifyAuthorisation(instituteId,
				userId, AuthorisationRequiredAction.DELETE_TIMETABLE_CONFIGURATION);
		if (instituteId <= 0) {
			logger.error("Incorrect instituteId whiling deleting school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Incorrect instituteId whiling deleting school shifts"));
		}
		if (timetableId == null) {
			logger.error("Incorrect timetableId whiling deleting school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Incorrect timetableId whiling deleting school shifts"));
		}
		if (userId == null) {
			logger.error("Empty userId whiling updating school shifts");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Incorrect userId whiling updating school shifts"));
		}
		return timetableDao.deleteTimetableDetails(instituteId, timetableId);
	}

	public TimetableDetails getTimetableDetails(int instituteId, int academicSessionId, UUID standardId, Integer sectionId,
			UUID shiftId) {
		if (instituteId <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect institute id."));
		}
		if (academicSessionId <= 0) {
			logger.error("Incorrect academic session Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect academic session id."));
		}
		if (standardId == null) {
			logger.error("Incorrect batch Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect batch Id."));
		}
		if (shiftId == null) {
			logger.error("Incorrect batch Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect batch Id."));
		}

		TimetableDetails timetableDetails = timetableDao
				.getTimetableDetails(instituteId, academicSessionId, standardId, sectionId, shiftId);

		if (timetableDetails != null
			&& !(CollectionUtils.isEmpty(timetableDetails.getSchoolShiftDetails().getShiftPeriodPayloadList()))) {
			Collections.sort(timetableDetails.getSchoolShiftDetails().getShiftPeriodPayloadList(),
					new Comparator<ShiftPeriodDetails>() {
					@Override
					public int compare(ShiftPeriodDetails s1, ShiftPeriodDetails s2) {
						int compare = s2.getPeriodStartTime().compareTo(s1.getPeriodStartTime());
						return compare;
					}
			});
		}

		if (timetableDetails != null
				&& !(CollectionUtils.isEmpty(timetableDetails.getTimetableDayPeriodDetailsMap()))) {
				for(Map.Entry<DayOfWeek, List<TimetableShiftPeriodDetails>> entry1
						: timetableDetails.getTimetableDayPeriodDetailsMap().entrySet()) {
					List<TimetableShiftPeriodDetails> sortedPeriodDetailsOfADayList =
							new ArrayList<TimetableShiftPeriodDetails>();
					List<TimetableShiftPeriodDetails> periodDetailsOfADayList = entry1.getValue();
					for(ShiftPeriodDetails shiftPeriodDetails
							: timetableDetails.getSchoolShiftDetails().getShiftPeriodPayloadList()) {
						for(TimetableShiftPeriodDetails timetableShiftPeriodDetails : periodDetailsOfADayList) {
							if(timetableShiftPeriodDetails.getPeriodID().equals(shiftPeriodDetails.getPeriodId())) {
								sortedPeriodDetailsOfADayList.add(timetableShiftPeriodDetails);
							}
						}
					}
					entry1.setValue(sortedPeriodDetailsOfADayList);
				}
			}

		return timetableDetails;
	}

	public TimetableDetails getTimetableDetailsByTimetableId(int instituteId, int academicSessionId, UUID timetableId) {
		if (instituteId <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect institute id."));
		}
		if (academicSessionId <= 0) {
			logger.error("Incorrect academic session Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect academic session id."));
		}

		TimetableDetails timetableDetails = timetableDao
				.getTimetableDetailsByTimetableId(instituteId, academicSessionId, timetableId);

		if (timetableDetails != null
			&& !(CollectionUtils.isEmpty(timetableDetails.getSchoolShiftDetails().getShiftPeriodPayloadList()))) {
			Collections.sort(timetableDetails.getSchoolShiftDetails().getShiftPeriodPayloadList(),
					new Comparator<ShiftPeriodDetails>() {
					@Override
					public int compare(ShiftPeriodDetails s1, ShiftPeriodDetails s2) {
						int compare = s2.getPeriodStartTime().compareTo(s1.getPeriodStartTime());
						return compare;
					}
			});
		}

		if (timetableDetails != null
				&& !(CollectionUtils.isEmpty(timetableDetails.getTimetableDayPeriodDetailsMap()))) {
				for(Map.Entry<DayOfWeek, List<TimetableShiftPeriodDetails>> entry1
						: timetableDetails.getTimetableDayPeriodDetailsMap().entrySet()) {
					List<TimetableShiftPeriodDetails> sortedPeriodDetailsOfADayList =
							new ArrayList<TimetableShiftPeriodDetails>();
					List<TimetableShiftPeriodDetails> periodDetailsOfADayList = entry1.getValue();
					for(ShiftPeriodDetails shiftPeriodDetails
							: timetableDetails.getSchoolShiftDetails().getShiftPeriodPayloadList()) {
						for(TimetableShiftPeriodDetails timetableShiftPeriodDetails : periodDetailsOfADayList) {
							if(timetableShiftPeriodDetails.getPeriodID().equals(shiftPeriodDetails.getPeriodId())) {
								sortedPeriodDetailsOfADayList.add(timetableShiftPeriodDetails);
							}
						}
					}
					entry1.setValue(sortedPeriodDetailsOfADayList);
				}
			}

		return timetableDetails;
	}


	public List<TimetableMetaData> getTimetableMetaDataList(int instituteId, int academicSessionId, UUID standardId,
			Integer sectionId, UUID shiftId) {
		if (instituteId <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect institute id."));
		}
		if (academicSessionId <= 0) {
			logger.error("Incorrect academic session Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect academic session id."));
		}
		if (standardId == null) {
			logger.error("Incorrect batch Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect batch Id."));
		}
		if (shiftId == null) {
			logger.error("Incorrect batch Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect batch Id."));
		}

		return timetableDao
				.getTimetableMetaDataList(instituteId, academicSessionId, standardId, sectionId, shiftId);
	}

	public List<TimetableMetaData> getTimetableMetaDataList(int instituteId, int academicSessionId, TimetableStatus timetableStatus) {
		if (instituteId <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect institute id."));
		}
		if (academicSessionId <= 0) {
			logger.error("Incorrect academic session Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect academic session id."));
		}

		if (timetableStatus == null) {
			logger.error("Incorrect Timetable Status");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect Timetable Status."));
		}
		return timetableDao
				.getTimetableMetaDataList(instituteId, academicSessionId, timetableStatus);

	}

	public StudentTimetableDetails getStudentTimetableDetails(int instituteId,
			int academicSessionId, UUID studentId) {

		Student student = studentManager.getStudentByAcademicSessionStudentId(instituteId, academicSessionId, studentId);
		if(student == null || student.getStudentAcademicSessionInfoResponse() == null || student.getStudentAcademicSessionInfoResponse().getStandard() == null) {
			logger.error("Student not present in current session.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Student not present in current session."));
		}
		UUID standardId = student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
		Integer sectionId = CollectionUtils.isEmpty(student.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList()) ? null :
				student.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList().get(0).getSectionId();

		List<Course> courses = courseManager.getStudentAssignedCourses(instituteId, studentId, academicSessionId);
		List<UUID> assignedCoursesList = new ArrayList<UUID>();
		for(Course course : courses) {
			assignedCoursesList.add(course.getCourseId());
		}
//		if(CollectionUtils.isEmpty(assignedCoursesList)) {
//			return null;
//		}


		StudentTimetableDetails studentTimetableDetails = timetableDao
				.getStudentTimetableDetails(instituteId, academicSessionId, studentId, standardId, sectionId);

		if(studentTimetableDetails == null) {
			return null;
		}

		Map<DayOfWeek, List<StudentTimetableShiftPeriodDetails>> studentTimetableShiftPeriodDetailsMap
			= studentTimetableDetails.getStudentTimetableShiftPeriodWeekDayMap();

		//Remove all the period details which is not applied to this student
		for(Map.Entry<DayOfWeek, List<StudentTimetableShiftPeriodDetails>> weekDayPeriodMap :
			studentTimetableShiftPeriodDetailsMap.entrySet()) {
			for(int j = 0; j < weekDayPeriodMap.getValue().size(); j++) {
				StudentTimetableShiftPeriodDetails studentTimetableShiftPeriodDetails  = weekDayPeriodMap.getValue().get(j);
				if(studentTimetableShiftPeriodDetails.getPeriodSlotDetailsList() != null) {
					for(int i = 0; i < studentTimetableShiftPeriodDetails.getPeriodSlotDetailsList().size(); i++) {

						PeriodSlotDetails periodSlotDetails = studentTimetableShiftPeriodDetails
								.getPeriodSlotDetailsList().get(i);
						//Remove subject which doesn't apply to student by taking all the assign subjects to this student
						if(periodSlotDetails.getPeriodSlotCategory() == PeriodSlotCategory.COURSE) {
							Course course = (Course) periodSlotDetails.getPeriodSlotCategoryDetails();
							if(!assignedCoursesList.contains(course.getCourseId())) {
								studentTimetableShiftPeriodDetails.getPeriodSlotDetailsList().remove(i);
							}
						}
					}
				}
			}
			weekDayPeriodMap.setValue(sortStudentTimetableShiftPeriodDetailsByStartTime(weekDayPeriodMap.getValue()));
		}

		studentTimetableShiftPeriodDetailsMap = sortbykey(studentTimetableShiftPeriodDetailsMap);
		return new StudentTimetableDetails(
				sortShiftPeriodDetailsByStartTime(studentTimetableDetails.getShiftPeriodDetailsList()),
				studentTimetableShiftPeriodDetailsMap);
	}

	public List<ShiftPeriodDetails> sortShiftPeriodDetailsByStartTime(List<ShiftPeriodDetails> shiftPeriodDetailsList) {
		if (!(CollectionUtils.isEmpty(shiftPeriodDetailsList))) {
				Collections.sort(shiftPeriodDetailsList,
						new Comparator<ShiftPeriodDetails>() {
						@Override
						public int compare(ShiftPeriodDetails s1, ShiftPeriodDetails s2) {
							int compare = s1.getPeriodStartTime().compareTo(s2.getPeriodStartTime());
							return compare;
						}
				});
			}

		return shiftPeriodDetailsList;
	}

	public List<StudentTimetableShiftPeriodDetails> sortStudentTimetableShiftPeriodDetailsByStartTime(
			List<StudentTimetableShiftPeriodDetails> studentTimetableShiftPeriodDetailsList) {
		if (!(CollectionUtils.isEmpty(studentTimetableShiftPeriodDetailsList))) {
				Collections.sort(studentTimetableShiftPeriodDetailsList,
						new Comparator<StudentTimetableShiftPeriodDetails>() {
						@Override
						public int compare(StudentTimetableShiftPeriodDetails s1, StudentTimetableShiftPeriodDetails s2) {
							int compare = s1.getShiftPeriodDetails().getPeriodStartTime().compareTo(
									s2.getShiftPeriodDetails().getPeriodStartTime());
							return compare;
						}
				});
			}
		return studentTimetableShiftPeriodDetailsList;
	}

	public Map<DayOfWeek, List<StudentTimetableShiftPeriodDetails>> sortbykey(Map<DayOfWeek, List<StudentTimetableShiftPeriodDetails>> studentTimetableShiftPeriodDetailsMap) {
        TreeMap<DayOfWeek, List<StudentTimetableShiftPeriodDetails>> sorted = new TreeMap<>();
        sorted.putAll(studentTimetableShiftPeriodDetailsMap);
        return sorted;
	}

	public boolean updateTimetableStatus(int instituteId, int academicSessionId,
										 UUID timetableId, TimetableStatus status, UUID userId) {

		if (instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
					"Invalid institute id"));
		}

		if (status == null) {
			logger.error("Invalid academic session id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
					"Invalid academic session id"));
		}

		if (timetableId == null) {
			logger.error("Invalid staff id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
					"Invalid staff id"));
		}

		if (userId == null) {
			logger.error("Invalid staff id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
					"Invalid staff id"));
		}

		if(status == TimetableStatus.PUBLISHED) {

			TimetableDetails timetableDetails = getTimetableDetailsByTimetableId(instituteId, academicSessionId, timetableId);
			if(timetableDetails == null) {
				logger.error("Invalid staff id");
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
						"No timetable with this id"));
			}

			TimetableMetaData timetableMetaData = getPublishedTimetableMetaData(instituteId,
					academicSessionId, timetableDetails.getStandard().getStandardId(),
					CollectionUtils.isEmpty(timetableDetails.getStandard().getStandardSectionList()) ? null :
							timetableDetails.getStandard().getStandardSectionList().get(0).getSectionId(),
					timetableDetails.getSchoolShiftDetails().getShiftId());
			if(timetableMetaData != null) {
				logger.error("Invalid staff id");
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
						"Already a timetable exists which is published. Please unpublish it then try again!"));
			}
		}

		return timetableDao.updateTimetableStatus(instituteId, timetableId, status, userId);
	}

	public boolean updateTimetableName(int instituteId, int academicSessionId,
										 UUID timetableId, String timetableName, UUID userId) {

		if (instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Invalid institute id"));
		}

		if (StringUtils.isBlank(timetableName)) {
			logger.error("Invalid timetable Name id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Invalid timetable Name id"));
		}

		if (timetableId == null) {
			logger.error("Invalid staff id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Invalid staff id"));
		}

		if (userId == null) {
			logger.error("Invalid staff id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Invalid staff id"));
		}

		TimetableDetails timeTableDetails = timetableDao.getTimetableDetailsByTimetableId(instituteId, academicSessionId, timetableId);
		Integer sectionId = CollectionUtils.isEmpty(timeTableDetails.getStandard().getStandardSectionList()) ?
				null : timeTableDetails.getStandard().getStandardSectionList().get(0).getSectionId();
		TimetableMetaData existingTimetableMetaData = timetableDao.getTimetableDetailsByName(instituteId, academicSessionId,
				timeTableDetails.getStandard().getStandardId(), sectionId, timeTableDetails.getSchoolShiftDetails().getShiftId(), timetableName);

		if(existingTimetableMetaData != null) {
			if(existingTimetableMetaData.getTimetableId() != timetableId) {
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
						"Timetable with this name already exists. Please try some other name!"));
			}
		}

		return timetableDao.updateTimetableName(instituteId, timetableId, timetableName, userId);
	}

	public TimetableMetaData getPublishedTimetableMetaData(int instituteId, int academicSessionId, UUID standardId,
														   Integer sectionId, UUID shiftId) {
		if (instituteId <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect institute id."));
		}
		if (academicSessionId <= 0) {
			logger.error("Incorrect academic session Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect academic session id."));
		}
		if (standardId == null) {
			logger.error("Incorrect standard Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect standard Id."));
		}
		if (shiftId == null) {
			logger.error("Incorrect shift Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect shift Id."));
		}

		return timetableDao
				.getPublishedTimetableMetaData(instituteId, academicSessionId, standardId, sectionId, shiftId);
	}

	public List<User> getTimetableUsers(int instituteId, int academicSessionId, UUID timetableId) {
		if (instituteId <= 0 || academicSessionId <= 0 || timetableId == null) {
			return null;
		}
		return timetableDao.getTimetableUsers(instituteId, academicSessionId, timetableId);
	}

	public Map<String, List<TimetableShiftPeriodDetails>> getTimetableDetailsByShiftId(int instituteId, int academicSessionId, UUID shiftId, DayOfWeek day) {
		if (instituteId <= 0) {
			logger.error("Incorrect institute Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
					"Incorrect institute id."));
		}
		if (academicSessionId <= 0) {
			logger.error("Incorrect academic session Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TIMETABLE_DETAILS,
					"Incorrect academic session id."));
		}
		if (day == null) {
			logger.error("Incorrect day");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect day."));
		}
		if (shiftId == null) {
			logger.error("Incorrect shift Id");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SCHOOL_SHIFT_DETAILS,
					"Incorrect shift Id."));
		}
		return timetableDao.getTimetableDetailsByShiftId(instituteId, academicSessionId, shiftId, day);
	}

	public Map<String, Set<UUID>> getStaffStandardSectionIdEntityDetails(int instituteId, int academicSessionId, UUID staffId) {
		if(instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}

		if(academicSessionId <= 0) {
			logger.error("Invalid academic session id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id"));
		}

		if(staffId == null) {
			logger.error("Invalid staff id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid staff id"));
		}

		StaffStandardEntityDetails staffStandardEntityDetails = getStaffStandardEntityDetails(instituteId, academicSessionId, staffId);

		Map<String, Set<UUID>> staffStandardSectionStrCourseDetailsMap = new HashMap<>();
		if (staffStandardEntityDetails != null && !CollectionUtils.isEmpty(staffStandardEntityDetails.getStandardEntityDetailsList())) {
			for (StandardEntityDetails standardEntityDetails : staffStandardEntityDetails.getStandardEntityDetailsList()) {
				UUID standardId = standardEntityDetails.getStandardRowDetails().getStandardId();
				Integer sectionId = standardEntityDetails.getStandardRowDetails().getSectionId();
				String standardSectionIdStr = standardId + (sectionId == null ? "" : ":" + sectionId);
				if (!staffStandardSectionStrCourseDetailsMap.containsKey(standardSectionIdStr)) {
					staffStandardSectionStrCourseDetailsMap.put(standardSectionIdStr, new HashSet<>());
				}
				for(EntityDetails entityDetails : standardEntityDetails.getEntityDetailsList()) {
					UUID entityId = entityDetails.getEntityId();
					staffStandardSectionStrCourseDetailsMap.get(standardSectionIdStr).add(entityId);
				}
			}
		}

		return staffStandardSectionStrCourseDetailsMap;
	}

	public Map<UUID, List<Staff>> fetchSubjectTeacherDetails(int instituteId, int academicSessionId, UUID standardId, Set<Integer> sectionIdSet) {

		final List<CoursesActivityDetailsRow> coursesActivityDetailsRowList = timetableDao.getCoursesActivityDetailsRowList(instituteId,
				academicSessionId, standardId, null);

		Map<UUID, List<Staff>> subjectTeacherDetailsMap = new HashMap<>();

		if(CollectionUtils.isEmpty(coursesActivityDetailsRowList)) {
			return subjectTeacherDetailsMap;
		}

		for(CoursesActivityDetailsRow coursesActivityDetailsRow : coursesActivityDetailsRowList) {
			if(coursesActivityDetailsRow == null) {
				continue;
			}
			if(coursesActivityDetailsRow.getEntity() != Entity.COURSE) {
				continue;
			}
			if(!CollectionUtils.isEmpty(sectionIdSet) && coursesActivityDetailsRow.getSectionId() != null
					&& !sectionIdSet.contains(coursesActivityDetailsRow.getSectionId())) {
				continue;
			}
			UUID courseId = coursesActivityDetailsRow.getEntityId();
			if(!subjectTeacherDetailsMap.containsKey(courseId)) {
				subjectTeacherDetailsMap.put(courseId, new ArrayList<>());
			}
			subjectTeacherDetailsMap.get(courseId).add(coursesActivityDetailsRow.getStaff());
		}

		return subjectTeacherDetailsMap;
	}

//	public boolean addFacultySubstitution(List<FacultySubstitutionPayload> facultySubstitutionPayloadList,
//			int instituteId, UUID userId) {
//		if (userId == null) {
//			logger.error("Empty userId whiling assigning substitution");
//			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//					"Incorrect userId whiling assigning substitution"));
//		}
//		userPermissionManager.verifyAuthorisation(instituteId,
//				userId, AuthorisationRequiredAction.ASSIGN_SUBSTITUTION);
//		validateFacultySubstitutionPayload(facultySubstitutionPayloadList, false);
//
//		List<FacultySubstitutionPayload> addFacultySubstitutionPayloadList = new ArrayList<FacultySubstitutionPayload>();
//		List<FacultySubstitutionPayload> updateFacultySubstitutionPayloadList = new ArrayList<FacultySubstitutionPayload>();
//
//		for(FacultySubstitutionPayload facultySubstitutionPayload : facultySubstitutionPayloadList) {
//			if(facultySubstitutionPayload.getSubstitutionId() == null) {
//				addFacultySubstitutionPayloadList.add(facultySubstitutionPayload);
//			} else {
//				updateFacultySubstitutionPayloadList.add(facultySubstitutionPayload);
//			}
//		}
//
//		final boolean result = transactionTemplate.execute(new TransactionCallback<Boolean>() {
//			@Override
//			public Boolean doInTransaction(TransactionStatus status) {
//				boolean added = true;
//				if(!CollectionUtils.isEmpty(addFacultySubstitutionPayloadList)) {
//					added &= timetableDao.addFacultySubstitution(addFacultySubstitutionPayloadList, instituteId, userId);
//				}
//				if(!CollectionUtils.isEmpty(updateFacultySubstitutionPayloadList)) {
//					added &= timetableDao.updateFacultySubstitution(updateFacultySubstitutionPayloadList, instituteId, userId);
//				}
//				return added;
//			}
//		});
//		return result;
//	}
//
//	private void validateFacultySubstitutionPayload(List<FacultySubstitutionPayload> facultySubstitutionPayloadList,
//			boolean update) {
//
//		for(FacultySubstitutionPayload facultySubstitutionPayload : facultySubstitutionPayloadList) {
//
//			if (facultySubstitutionPayload.getInstituteId() <= 0) {
//				logger.error("Invalid institute.");
//				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//						"Invalid institute."));
//			}
//
//			if (facultySubstitutionPayload.getAcademicSessionId() <= 0) {
//				logger.error("Invalid academic session id.");
//				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//						"Invalid academic session id."));
//			}
//
//			if(update) {
//				if (facultySubstitutionPayload.getSubstitutionId() == null) {
//					logger.error("Substitution id cannot be null.");
//					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//							"Substitution id cannot be null."));
//				}
//			}
//
//			if (facultySubstitutionPayload.getStaffId() == null) {
//				logger.error("Staff id cannot be null.");
//				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//						"Please select staff."));
//			}
//
//			if (facultySubstitutionPayload.getTimetableId() == null) {
//				logger.error("Timetable id cannot be null.");
//				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//						"Please select proper timetable for substitution."));
//			}
//
//			if (facultySubstitutionPayload.getPeriodId() == null) {
//				logger.error("Period id cannot be null.");
//				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//						"Please select proper period for substitution."));
//			}
//
//			if (facultySubstitutionPayload.getDay() == null) {
//				logger.error("Day cannot be null.");
//				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//						"Please select proper day for substitution."));
//			}
//
//			if (facultySubstitutionPayload.getCourseId() == null) {
//				logger.error("Course id cannot be null.");
//				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//						"Please select proper course for substitution."));
//			}
//
//			if (facultySubstitutionPayload.getStandardId() == null) {
//				logger.error("Standard id cannot be null.");
//				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//						"Please select proper standard for substitution."));
//			}
//
//			if (facultySubstitutionPayload.getSubstitutionDate() == null ||
//					facultySubstitutionPayload.getSubstitutionDate() <= 0) {
//				logger.error("Substitution date cannot be null.");
//				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//						"Please select proper substitution date."));
//			}
//
//		}
//	}
//
//	public boolean updateFacultySubstitution(List<FacultySubstitutionPayload> facultySubstitutionPayloadList,
//			int instituteId, UUID userId) {
//		if (userId == null) {
//			logger.error("Empty userId whiling updating substitution");
//			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//					"Incorrect userId whiling updating substitution"));
//		}
//		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_SUBSTITUTION);
//		validateFacultySubstitutionPayload(facultySubstitutionPayloadList, true);
//		return timetableDao.updateFacultySubstitution(facultySubstitutionPayloadList, instituteId, userId);
//	}
//
//	public List<FacultyPeriodSubstitutionDetails> getFacultyPeriodSubstitutionDetails(
//			int instituteId, int academicSessionId, UUID staffId, DayOfWeek day) {
//
//		if (instituteId <= 0) {
//			logger.error("Invalid institute id");
//			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//					"Invalid institute id"));
//		}
//
//		if (academicSessionId <= 0) {
//			logger.error("Invalid academic session id");
//			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//					"Invalid academic session id"));
//		}
//
//		if (staffId == null) {
//			logger.error("Invalid staff id");
//			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//					"Invalid staff id"));
//		}
//
//		if (day == null) {
//			logger.error("Invalid day");
//			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SUBSTITUTION_DETAILS,
//					"Invalid day"));
//		}
//
//		return timetableDao.getFacultyPeriodSubstitutionDetails(instituteId, academicSessionId, staffId, day);
//	}
//
}