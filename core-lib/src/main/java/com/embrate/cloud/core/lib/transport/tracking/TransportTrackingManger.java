package com.embrate.cloud.core.lib.transport.tracking;

import com.embrate.cloud.core.api.transport.tracking.TransportTrackingData;
import com.embrate.cloud.core.api.transport.tracking.TransportTrackingStatus;
import com.embrate.cloud.core.api.transport.tracking.TransportTripPayload;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.student.TransportTripDetails;
import com.lernen.cloud.core.api.student.TransportTripStudentDetails;
import com.lernen.cloud.core.api.transport.StudentTransportData;
import com.lernen.cloud.core.api.transport.TransportServiceRoute;
import com.lernen.cloud.core.api.transport.TransportServiceRouteDetails;
import com.lernen.cloud.core.api.transport.TransportTripMetadata;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.transport.configuration.TransportAssignmentManager;
import com.lernen.cloud.core.lib.transport.configuration.TransportConfigurationManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.dao.tier.transport.tracking.TransportTrackingDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 */

public class TransportTrackingManger {
    private static final Logger logger = LogManager.getLogger(TransportTrackingManger.class);
    private final TransportTrackingDao transportTrackingDao;
    private final TransportAssignmentManager transportAssignmentManager;
    private final TransportConfigurationManager transportConfigurationManager;
    private final InstituteManager instituteManager;

    public TransportTrackingManger(TransportTrackingDao transportTrackingDao, TransportAssignmentManager transportAssignmentManager, TransportConfigurationManager transportConfigurationManager, InstituteManager instituteManager) {
        this.transportTrackingDao = transportTrackingDao;
        this.transportAssignmentManager = transportAssignmentManager;
        this.transportConfigurationManager = transportConfigurationManager;
        this.instituteManager = instituteManager;
    }

    public UUID startTransportTrip(int instituteId, TransportTripPayload transportTripPayload) {
        if (instituteId <= 0 || transportTripPayload == null || transportTripPayload.getServiceRouteId() == null || transportTripPayload.getTripDate() <= 0
                || transportTripPayload.getDriverId() == null) {
            logger.error("Invalid start trip payload for institute {}", instituteId);
            return null;
        }

        int today = DateUtils.now();
        int dayStart = DateUtils.getDayStart(today, DateUtils.DEFAULT_TIMEZONE);
        int dayEnd = DateUtils.getDayEnd(today, DateUtils.DEFAULT_TIMEZONE);

        List<TransportTrackingData> transportTrackingData = transportTrackingDao.getTransportTrackingDetails(instituteId, new HashSet<>(Arrays.asList(transportTripPayload.getServiceRouteId())), dayStart, dayEnd);
        if (transportTrackingData == null) {
            logger.error("Unable to load existing trips for service route {}, institute {}", transportTripPayload.getServiceRouteId(), instituteId);
            return null;
        }

        List<TransportTrackingData> driverOngoingTransportTrackingData = transportTrackingDao.getTransportStaffOngoingTrip(instituteId, transportTripPayload.getDriverId());
        if (driverOngoingTransportTrackingData == null) {
            logger.error("Unable to load ongoing trips for driver {}, institute {}", transportTripPayload.getDriverId(), instituteId);
            return null;
        }
        /**
         * Only one trip can be created for a particular service route in a day
         * TODO : transaction required?
         */
        if (CollectionUtils.isNotEmpty(transportTrackingData)) {
            logger.error("Trip already created for service route {}, institute {} for today {}, day range {} - {}", transportTripPayload.getServiceRouteId(), instituteId, today, dayStart, dayEnd);
            return null;
        }

        int newTripDayStart = DateUtils.getDayStart(transportTripPayload.getTripDate(), DateUtils.DEFAULT_TIMEZONE);
//         TODO : Handle case if last day trips are not ended by driver. They will also not be displayed in screen. - Done
        if (CollectionUtils.isNotEmpty(driverOngoingTransportTrackingData)) {
            for(TransportTrackingData transportTrackingData1 : driverOngoingTransportTrackingData) {
                if(transportTrackingData1 == null) {
                    continue;
                }
                int existingTripDayStart = DateUtils.getDayStart(transportTrackingData1.getTripDate(), DateUtils.DEFAULT_TIMEZONE);
                TransportTrackingStatus transportTrackingStatus = transportTrackingData1.getTransportTrackingStatus();
                if(transportTrackingStatus != TransportTrackingStatus.ONGOING) {
                    continue;
                }
                if(existingTripDayStart < newTripDayStart) {
                    /**
                     * calling this dao inside for loop as there will always be one ongoing trip at a time,
                     * if in future we are changing this assumption then pull this call out of for loop
                     */
                    UUID existingTripId = transportTrackingData1.getTransportTripId();
                    if(!endTransportTrip(instituteId, existingTripId)) {
                        logger.error("Not able to end Driver {} ongoing trip, institute {}", transportTripPayload.getDriverId(), instituteId);
                        return null;
                    }
                }
            }
        }

        return transportTrackingDao.startTransportTrip(instituteId, transportTripPayload);
    }

    public TransportTrackingData getTransportTrackingDetails(int instituteId, UUID transportTripId) {
        return transportTrackingDao.getTransportTrackingDetails(instituteId, transportTripId);
    }

    public boolean endTransportTrip(int instituteId, UUID transportTripId) {
        return transportTrackingDao.endTransportTrip(instituteId, transportTripId);
    }

    public List<TransportTripMetadata> getTodayStudentTransportTrips(int instituteId, UUID studentId) {
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_SERVICE_ROUTE,
                    "Invalid institute id."));
        }
        if (studentId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_SERVICE_ROUTE,
                    "Invalid student id."));
        }
        int today = DateUtils.now();
        List<AcademicSession> academicSessions = instituteManager.getAcademicSessionList(instituteId);
        int academicSessionId = 0;
        for (AcademicSession academicSession : academicSessions) {
            if (today >= academicSession.getSessionStartTime() && today <= academicSession.getSessionEndTime()) {
                academicSessionId = academicSession.getAcademicSessionId();
                break;
            }
        }

        List<TransportTripMetadata> transportTripData = new ArrayList<>();

        if (academicSessionId == 0) {
            logger.error("No valid session found for transport trip. institute {}, studentId {}", instituteId, studentId);
            return transportTripData;
        }

        StudentTransportData studentTransportData = transportAssignmentManager.getStudentCurrentTransportData(instituteId, academicSessionId, studentId);
        if (studentTransportData == null) {
            logger.error("No valid transport assignment found for studentId {}, institute {}, academicSessionId {}", studentId, instituteId, academicSessionId);
            return transportTripData;
        }

        int dayStart = DateUtils.getDayStart(today, DateUtils.DEFAULT_TIMEZONE);
        int dayEnd = DateUtils.getDayEnd(today, DateUtils.DEFAULT_TIMEZONE);
        Set<UUID> routes = new HashSet<>();
        if (studentTransportData.getPickupTransportServiceRouteMetadata() != null) {
            routes.add(studentTransportData.getPickupTransportServiceRouteMetadata().getServiceRouteId());
        }
        if (studentTransportData.getDropTransportServiceRouteMetadata() != null) {
            routes.add(studentTransportData.getDropTransportServiceRouteMetadata().getServiceRouteId());
        }

        List<TransportTrackingData> transportTrackingDataList = transportTrackingDao.getTransportTrackingDetails(instituteId, routes, dayStart, dayEnd);

        Map<UUID, TransportTrackingData> transportTrackingDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(transportTrackingDataList)) {
            for (TransportTrackingData transportTrackingData : transportTrackingDataList) {
                transportTrackingDataMap.put(transportTrackingData.getServiceRouteId(), transportTrackingData);
            }
        }

        if (studentTransportData.getPickupTransportServiceRouteMetadata() != null) {
            transportTripData.add(new TransportTripMetadata(studentTransportData.getPickupTransportServiceRouteMetadata(), transportTrackingDataMap.get(studentTransportData.getPickupTransportServiceRouteMetadata().getServiceRouteId())));
        }

        if (studentTransportData.getDropTransportServiceRouteMetadata() != null) {
            transportTripData.add(new TransportTripMetadata(studentTransportData.getDropTransportServiceRouteMetadata(), transportTrackingDataMap.get(studentTransportData.getDropTransportServiceRouteMetadata().getServiceRouteId())));
        }

        return transportTripData;
    }

    /**
     * If staff is null, it will fetch for all staffs
     *
     * @param instituteId
     * @param staffId
     * @return
     */
    public List<TransportTripDetails> getTodayTransportStaffTrips(int instituteId, UUID staffId) {
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_SERVICE_ROUTE,
                    "Invalid institute id."));
        }
        int today = DateUtils.now();
        List<AcademicSession> academicSessions = instituteManager.getAcademicSessionList(instituteId);
        int academicSessionId = 0;
        for (AcademicSession academicSession : academicSessions) {
            if (today >= academicSession.getSessionStartTime() && today <= academicSession.getSessionEndTime()) {
                academicSessionId = academicSession.getAcademicSessionId();
                break;
            }
        }

        List<TransportTripDetails> transportTripData = new ArrayList<>();

        if (academicSessionId == 0) {
            logger.error("No valid session found for transport trip. institute {}, staffId {}", instituteId, staffId);
            return transportTripData;
        }
        List<TransportServiceRoute> transportServiceRoutes = null;
        if (staffId != null) {
            transportServiceRoutes = transportConfigurationManager.getTransportServiceRoutes(instituteId, academicSessionId, staffId);
        } else {
            transportServiceRoutes = transportConfigurationManager.getTransportServiceRoutes(instituteId, academicSessionId);
        }

        if (CollectionUtils.isEmpty(transportServiceRoutes)) {
            logger.error("No valid transport assignment found for staff {}, institute {}, academicSessionId {}", staffId, instituteId, academicSessionId);
            return transportTripData;
        }

        int dayStart = DateUtils.getDayStart(today, DateUtils.DEFAULT_TIMEZONE);
        int dayEnd = DateUtils.getDayEnd(today, DateUtils.DEFAULT_TIMEZONE);
        Set<UUID> serviceRouteIds = new HashSet<>();
        for (TransportServiceRoute serviceRoute : transportServiceRoutes) {
            serviceRouteIds.add(serviceRoute.getTransportServiceRouteMetadata().getServiceRouteId());
        }
        List<TransportTrackingData> transportTrackingDataList = transportTrackingDao.getTransportTrackingDetails(instituteId, serviceRouteIds, dayStart, dayEnd);

        Map<UUID, TransportTrackingData> transportTrackingDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(transportTrackingDataList)) {
            for (TransportTrackingData transportTrackingData : transportTrackingDataList) {
                transportTrackingDataMap.put(transportTrackingData.getServiceRouteId(), transportTrackingData);
            }
        }

        for (TransportServiceRoute serviceRoute : transportServiceRoutes) {
            transportTripData.add(new TransportTripDetails(serviceRoute, transportTrackingDataMap.get(serviceRoute.getTransportServiceRouteMetadata().getServiceRouteId())));
        }
        return transportTripData;
    }


    public TransportTripStudentDetails getTodayTransportTripStudentDetails(int instituteId, UUID serviceRouteId) {
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_SERVICE_ROUTE,
                    "Invalid institute id."));
        }
        if (serviceRouteId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_SERVICE_ROUTE,
                    "Invalid serviceRouteId."));
        }
        int today = DateUtils.now();

        TransportServiceRouteDetails transportServiceRouteDetails = transportConfigurationManager.getTransportServiceRouteWithStudents(instituteId, serviceRouteId);
        if (transportServiceRouteDetails == null) {
            logger.error("No valid transport found for serviceRouteId {}, institute {}", serviceRouteId, instituteId);
            return null;
        }

        int dayStart = DateUtils.getDayStart(today, DateUtils.DEFAULT_TIMEZONE);
        int dayEnd = DateUtils.getDayEnd(today, DateUtils.DEFAULT_TIMEZONE);

        List<TransportTrackingData> transportTrackingDataList = transportTrackingDao.getTransportTrackingDetails(instituteId, new HashSet<>(Arrays.asList(serviceRouteId)), dayStart, dayEnd);
        TransportTrackingData transportTrackingData = null;
        if (CollectionUtils.isNotEmpty(transportTrackingDataList)) {
            transportTrackingData = transportTrackingDataList.get(0);
        }

        return new TransportTripStudentDetails(transportServiceRouteDetails, transportTrackingData);
    }

}
