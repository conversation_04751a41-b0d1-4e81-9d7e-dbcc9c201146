package com.lernen.cloud.core.lib.application.mobile;

import com.embrate.cloud.core.api.application.mobile.*;
import com.embrate.cloud.core.api.helpandsupport.HelpAndSupport;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 *
 */
public class MobileApplicationManager {
	
	private final HelpAndSupportManager helpAndSupportManager;

	private final UserPreferenceSettings userPreferenceSettings;
	
	public MobileApplicationManager(HelpAndSupportManager helpAndSupportManager,
									UserPreferenceSettings userPreferenceSettings) {
		this.helpAndSupportManager = helpAndSupportManager;
		this.userPreferenceSettings = userPreferenceSettings;
	}

	private static final Logger logger = LogManager.getLogger(MobileApplicationManager.class);

	public static final String AUTH_FLOW_UPGRADE_VERSION = "1.0.3";
	public static final String FLUTTER_MIGRATION_VERSION = "1.0.7";

	private static final Map<MobileAppPlatform, String> LATEST_APP_PLATFORM_VERSION = new EnumMap<>(
			MobileAppPlatform.class);

	static {
//		LATEST_APP_PLATFORM_VERSION.put(MobileAppPlatform.GOOGLE, "1.0.6");
//		LATEST_APP_PLATFORM_VERSION.put(MobileAppPlatform.IOS, "1.0.1");
	}

	public MobileAppUserMetadata getUserAppMetadata(MobileAppPlatform appPlatform, String appVersion, User user) {

		final Map<MobileAppPlatform, String> LATEST_APP_PLATFORM_VERSION = new EnumMap<>(MobileAppPlatform.class);

		if (user == null || user.getInstituteId() <= 0) {
			logger.error("Invalid user {}.", user);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_MOBILE_APP_DETAILS,
					"Invalid user details"));
		}
		MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(user.getInstituteId());
		LATEST_APP_PLATFORM_VERSION.put(MobileAppPlatform.GOOGLE, metaDataPreferences.getAndroidMobileAppVersion());
		LATEST_APP_PLATFORM_VERSION.put(MobileAppPlatform.IOS, metaDataPreferences.getIosMobileAppVersion());

		if (!LATEST_APP_PLATFORM_VERSION.containsKey(appPlatform)) {
			logger.error("{} not supported.", appPlatform);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_MOBILE_APP_DETAILS,
					appPlatform + " is not supported"));
		}
		final String latestVersion = LATEST_APP_PLATFORM_VERSION.get(appPlatform);
		/**
		 * Validate user and write logic for determining the app update priority
		 * based on user
		 */


		if(StandardAppVersion.compareVersion(new StandardAppVersion(appVersion.trim()), new StandardAppVersion(latestVersion)) >= 0){
			return new MobileAppUserMetadata(MobileAppUpdateStatus.upToDateStatus());
		}
//		if (latestVersion.equalsIgnoreCase(appVersion.trim())) {
//			return new MobileAppUserMetadata(MobileAppUpdateStatus.upToDateStatus());
//		}

		MobileAppUpdatePriority mobileAppUpdatePriority = getMobileAppUpdatePriority(user.getUserType(), metaDataPreferences);
		if(mobileAppUpdatePriority == null) {
			mobileAppUpdatePriority = MobileAppUpdatePriority.LOW;
		}

		String title = "";
		String description = "";
		switch (mobileAppUpdatePriority){
			case LOW:
				description = "There is new version of this app available. Please update this app to enjoy new features and better experience.";
				break;
			case HIGH:
				description = "There is new version of this app available. We recommend to update this app as some features might not work as expected.";
				break;
			case BLOCKER:
				description = "Please update this app to continue using it.";
				break;
		}



		return new MobileAppUserMetadata( MobileAppUpdateStatus.updateRequiredStatus(mobileAppUpdatePriority, title, description));
	}

	public List<HelpAndSupport> getHelpAndSupportDetails() {
		return helpAndSupportManager.getHelpAndSupportDetails();
	}

	public MobileAppUpdatePriority getMobileAppUpdatePriority(UserType userType, MetaDataPreferences metaDataPreferences) {
		if(userType == UserType.STUDENT) {
			return metaDataPreferences.getStudentMobileAppUpdatePriority();
		} else {
			return metaDataPreferences.getAdminMobileAppUpdatePriority();
		}
	}
}
