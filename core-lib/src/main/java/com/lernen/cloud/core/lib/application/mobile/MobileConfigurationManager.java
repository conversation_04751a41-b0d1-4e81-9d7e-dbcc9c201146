package com.lernen.cloud.core.lib.application.mobile;

import com.embrate.cloud.core.api.application.mobile.*;
import com.lernen.cloud.core.api.configurations.Entity;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.lib.configs.ConfigurationManager;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.SharedConstants;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

public class MobileConfigurationManager {

    private final FeePaymentManager feePaymentManager;

    private final UserPermissionManager userPermissionManager;

    private final UserManager userManager;

    private final ConfigurationManager configurationManager;

    private final InstituteManager instituteManager;

    private final UserPreferenceSettings userPreferenceSettings;

    private static final Logger logger = LogManager.getLogger(MobileConfigurationManager.class);

    public MobileConfigurationManager(FeePaymentManager feePaymentManager,
                                      UserPermissionManager userPermissionManager,
                                      UserManager userManager, ConfigurationManager configurationManager, InstituteManager instituteManager,  UserPreferenceSettings userPreferenceSettings) {
        this.feePaymentManager = feePaymentManager;
        this.userPermissionManager = userPermissionManager;
        this.userManager = userManager;
        this.configurationManager = configurationManager;
        this.instituteManager = instituteManager;
        this.userPreferenceSettings = userPreferenceSettings;
    }

    public List<StudentUserDueFeesData> getUserDetails(int instituteId, RestrictionType restrictionType, FilterCriteria filterCriteria) {
        if (instituteId <= 0) {
            logger.error("Invalid Institute Id");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid Institute Id"));
        }

        if (restrictionType == null) {
            logger.error("Invalid Restriction Type");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid Restriction Type"));
        }

        validateFilterCriteria(restrictionType, filterCriteria);

        switch (restrictionType) {
            case DUE_FEES:
                DueFeesFilterCriteria dueFeesFilterCriteria = filterCriteria.getDueFeesFilterCriteria();
                return feePaymentManager.getDueFeesUsers(dueFeesFilterCriteria.getInstituteId(),
                        dueFeesFilterCriteria.getAcademicSessionId(), dueFeesFilterCriteria.getDueDate(),
                        true, computeStandardIdCSV(dueFeesFilterCriteria.getStandardIdList()));
            default:
                return null;
        }
    }

    public String computeStandardIdCSV(List<UUID> standardIdList) {
        if(CollectionUtils.isEmpty(standardIdList)) {
            return null;
        }
        String delimiter = "";
        StringBuilder standardIdCSV = new StringBuilder();
        for(UUID standardId : standardIdList) {
            if(standardId == null){
                continue;
            }
            standardIdCSV.append(delimiter).append(standardId.toString());
            delimiter = ",";
        }
        return standardIdCSV.toString();
    }

    private void validateFilterCriteria(RestrictionType restrictionType, FilterCriteria filterCriteria) {
        if(filterCriteria == null) {
            logger.error("Invalid filter criteria");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid filter criteria"));
        }
        if(restrictionType == RestrictionType.DUE_FEES) {
            validateDueFeesFilterCriteria(filterCriteria.getDueFeesFilterCriteria());
        } else {
            logger.error("Invalid Restriction Type");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Restriction Type " + restrictionType.name() + " not supported. Please contact technical team."));
        }
    }

    private void validateDueFeesFilterCriteria(DueFeesFilterCriteria dueFeesFilterCriteria) {
        if (dueFeesFilterCriteria == null) {
            logger.error("Invalid due fees filter criteria");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid due fees filter criteria"));
        }

        if (dueFeesFilterCriteria.getInstituteId() <= 0 ) {
            logger.error("Invalid institute id");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid institute id."));
        }

        if (dueFeesFilterCriteria.getAcademicSessionId() <= 0 ) {
            logger.error("Invalid academic session id");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid academic session id."));
        }

        if (dueFeesFilterCriteria.getDueDate() <= 0 ) {
            logger.error("Invalid due date");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Due date cannot be empty!"));
        }
    }

    public boolean updateUserModulePermissions(int instituteId, UUID userId, UserModulePermissionPayload userModulePermissionPayload) {
        if (instituteId <= 0 ) {
            logger.error("Invalid institute id");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid institute id"));
        }

        if (userId == null) {
            logger.error("Invalid user id");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid user id"));
        }

        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_USER_MODULE_PERMISSIONS);

        if(userModulePermissionPayload == null) {
            logger.error("Invalid payload");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid payload"));
        }

        if(CollectionUtils.isEmpty(userModulePermissionPayload.getUserIdList())) {
            logger.error("Invalid user list");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Please select at least one user to update module permissions"));
        }

        return userManager.updateUserModulePermissions(instituteId, userModulePermissionPayload);
    }

    public boolean updateUserSessionRestriction(int instituteId, UUID userId, Set<Integer> academicSessionRestrictedIdSet){
        
        if (instituteId <= 0 ) {
            logger.error("Invalid institute id");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid institute id"));
        }

        if (userId == null) {
            logger.error("Invalid user id");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid user id"));
        }
        
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.STUDENT_SESSION_RESTRICTION);
        
        validationForRestrictionSessionIdSet(instituteId, academicSessionRestrictedIdSet);
        
        Map<String, String> configKeyValues = new HashMap<>();
        configKeyValues.put(MetaDataPreferences.STUDENT_SESSION_RESTRICTION, SharedConstants.GSON.toJson(academicSessionRestrictedIdSet));
        
        return configurationManager.upsertConfiguration(Entity.INSTITUTE, String.valueOf(instituteId), MetaDataPreferences.getConfigType(), configKeyValues);
        
    }

    private void validationForRestrictionSessionIdSet( int instituteId, Set<Integer> academicSessionRestrictedIdSet){

        List<AcademicSession> academicSessions = instituteManager.getAcademicSessionList(instituteId);
        if (CollectionUtils.isEmpty(academicSessions)) {
            logger.error("Academic sessions IDs are null");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Academic sessions IDs are null"));
        }
        Set<Integer> academicSessionIds = new HashSet<>();
        for (AcademicSession session : academicSessions) {
            academicSessionIds.add(session.getAcademicSessionId());
        }
        if(!CollectionUtils.isEmpty(academicSessionRestrictedIdSet)) {
            // Check if all restricted IDs are present in academicSessionIds
            boolean allPresent = !Collections.disjoint(academicSessionRestrictedIdSet, academicSessionIds);
            if (!allPresent) {
                logger.error("Some restricted academic session IDs are not valid for the given institute");
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session IDs in restricted list"));
            }
        }
    }

    public MetaDataPreferences getMetaDataPreferences(int instituteId){
        MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);
        return metaDataPreferences;
    }
}
