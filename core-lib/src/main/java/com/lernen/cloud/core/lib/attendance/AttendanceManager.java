package com.lernen.cloud.core.lib.attendance;

import com.embrate.cloud.core.api.attendance.AttendanceInputType;
import com.embrate.cloud.core.api.attendance.AttendanceLog;
import com.embrate.cloud.core.api.calendar.holiday.StaticHoliday;
import com.embrate.cloud.core.lib.calendar.holiday.HolidayCalendarManager;
import com.embrate.cloud.dao.tier.attendance.UserAttendanceDao;
import com.lernen.cloud.core.api.attendance.*;
import com.lernen.cloud.core.api.attendance.calc.StudentAttendanceCalculatorType;
import com.lernen.cloud.core.api.attendance.preference.AttendancePreferences;
import com.lernen.cloud.core.api.configurations.StudentAttendancePreferences;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardWithStaffDetails;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.attendance.student.StudentAttendanceCalculator;
import com.lernen.cloud.core.lib.attendance.student.StudentAttendanceCalculatorFactory;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.institute.InstituteManagementManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.attendance.AttendanceUtils;
import com.lernen.cloud.core.utils.permissions.EPermissionUtils;
import com.lernen.cloud.dao.tier.attendance.AttendanceDao;
import com.lernen.cloud.dao.tier.student.StudentDao;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Month;
import java.util.*;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 */
public class AttendanceManager {
    private static final Logger logger = LogManager.getLogger(AttendanceManager.class);

    private final AttendanceDao attendanceDao;

    private final UserAttendanceDao userAttendanceDao;

    private final InstituteManager instituteManager;

    private final UserPermissionManager userPermissionManager;

    private final UserPreferenceSettings userPreferenceSettings;

    private final StudentAttendanceCalculatorFactory studentAttendanceCalculatorFactory;

    private final HolidayCalendarManager holidayCalendarManager;

    private final StudentDao studentDao;

    public AttendanceManager(AttendanceDao attendanceDao, InstituteManager instituteManager, UserAttendanceDao userAttendanceDao, UserPermissionManager userPermissionManager,
                             UserPreferenceSettings userPreferenceSettings, StudentAttendanceCalculatorFactory studentAttendanceCalculatorFactory, HolidayCalendarManager holidayCalendarManager,
                             StudentDao studentDao) {
        this.attendanceDao = attendanceDao;
        this.instituteManager = instituteManager;
        this.userAttendanceDao = userAttendanceDao;
        this.userPermissionManager = userPermissionManager;
        this.userPreferenceSettings = userPreferenceSettings;
        this.studentAttendanceCalculatorFactory = studentAttendanceCalculatorFactory;
        this.holidayCalendarManager = holidayCalendarManager;
        this.studentDao = studentDao;
    }

    public List<AttendanceType> getInstituteAttendanceTypes(int instituteId, int academicSessionId, List<Integer> attendanceType) {
        return getInstituteAttendanceTypes(instituteId, academicSessionId, attendanceType, new HashSet<>(Arrays.asList(Module.ATTENDANCE)));
    }

    public List<String> getDistinctRegularAttendanceTypes(List<Integer> instituteIdSet, Set<Integer> academicSessionIdSet) {
        return attendanceDao.getDistinctAttendanceTypes(instituteIdSet, academicSessionIdSet);
    }



    public List<AttendanceType> getInstituteAttendanceTypes(int instituteId, int academicSessionId, List<Integer> attendanceType, Set<Module> moduleSet) {
        if (instituteId <= 0 || academicSessionId <= 0) {
            logger.error("Invalid institute {} or session {}", instituteId, academicSessionId);
            return null;
        }
        return attendanceDao.getInstituteAttendanceTypes(instituteId, academicSessionId, attendanceType, moduleSet);
    }

    public List<AttendanceType> getAttendanceTypes(int instituteId, int academicSessionId, UUID standardId) {
        List<AttendanceType> attendanceTypes = getInstituteAttendanceTypes(instituteId, academicSessionId, null);
        return AttendanceUtils.getAttendanceTypes(attendanceTypes, standardId);
    }


    public boolean addHolidays(HolidayCalendar holidayCalendar) {
        return attendanceDao.addHolidays(holidayCalendar);
    }

    public List<HolidaysResponse> getHolidayList(int instituteId, int academicSessionid) {
        return attendanceDao.getHolidayList(instituteId, academicSessionid);
    }

    public SaveStudentAttendanceResult saveAttendance(UUID standardId, Integer sectionId, AttendanceRegister attendanceRegister, AttendanceInputType attendanceInputType, boolean checkAttendanceDataAccessibility) {
        if (attendanceRegister == null || attendanceRegister.getInstituteId() <= 0 || CollectionUtils.isEmpty(attendanceRegister.getStudentAttendances())) {
            logger.error("Invalid attendance register");
            return null;
        }
        UUID createdBy = attendanceRegister.getCreatedBy();
        UUID updatedBy = attendanceRegister.getUpdatedBy();
        /**
         * Assuming students will belong to a single class when attendance is filled from MOBILE or WEB
         */
        if(checkAttendanceDataAccessibility) {
            if (standardId == null) {
                Student student = studentDao.getStudentByAcademicSessionStudentId(attendanceRegister.getInstituteId(),
                        attendanceRegister.getAcademicSessionId(), attendanceRegister.getStudentAttendances().get(0).getStudentId());
                standardId = student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
                sectionId = CollectionUtils.isEmpty(student.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList()) ? null :
                        student.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList().get(0).getSectionId();
            }


            if (createdBy != null) {
                /**
                 * only class teacher or user with special access can add student attendances
                 */
                verifyAttendanceDataAccessibility(attendanceRegister.getInstituteId(), attendanceRegister.getAcademicSessionId(),
                        standardId, sectionId, createdBy, true);
            }

            if (updatedBy != null) {
                /**
                 * only class teacher or user with special access can add student attendances
                 */
                verifyAttendanceDataAccessibility(attendanceRegister.getInstituteId(), attendanceRegister.getAcademicSessionId(), standardId, sectionId, updatedBy, true);
            }
        }

        long currentTime = System.currentTimeMillis();
        long attendanceTime = attendanceRegister.getAttendanceDateTS() * 1000l;
        boolean sameDayAttendance = DateUtils.getDayStart(attendanceTime, DateUtils.DEFAULT_TIMEZONE) == DateUtils.getDayStart(currentTime, DateUtils.DEFAULT_TIMEZONE);
        /**
         * we don't allow future attendance
         * and for past user should have access
         */
        if(!sameDayAttendance) {
            if(attendanceInputType != AttendanceInputType.DEVICE) {
                createdBy = attendanceRegister.getCreatedBy();
                if(createdBy != null) {
                    if(!userPermissionManager.verifyAuthorisation(attendanceRegister.getInstituteId(),
                            createdBy, AuthorisationRequiredAction.UPDATE_BACKDATED_ATTENDANCE, false)) {
                        throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                                "You can only add today's attendance!"));
                    }
                }
                updatedBy = attendanceRegister.getUpdatedBy();
                if(updatedBy != null) {
                    if(!userPermissionManager.verifyAuthorisation(attendanceRegister.getInstituteId(),
                            updatedBy, AuthorisationRequiredAction.UPDATE_BACKDATED_ATTENDANCE, false)) {
                        throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                                "You can only update today's attendance!"));
                    }
                }
            }
        }

        Set<Integer> attendanceTypeSet = new HashSet<>();
        Set<AttendanceStatus> attendanceStatuesSet = new HashSet<>();
        List<AttendanceLog> attendanceLogs = new ArrayList<>();
        for (StudentAttendancePayload studentAttendancePayload : attendanceRegister.getStudentAttendances()) {
            if (studentAttendancePayload.getAttendanceStatus() == null) {
                continue;
            }
            Map<String, String> metadata = new HashMap<>();
            metadata.put(AttendanceLog.ATTENDANCE_STATUS, studentAttendancePayload.getAttendanceStatus().name());
            metadata.put(AttendanceLog.ATTENDANCE_TYPE, String.valueOf(studentAttendancePayload.getAttendanceType()));
            metadata.put(AttendanceLog.ATTENDANCE_REMARK, studentAttendancePayload.getRemarks());
            attendanceTypeSet.add(studentAttendancePayload.getAttendanceType());
            attendanceStatuesSet.add(studentAttendancePayload.getAttendanceStatus());

            attendanceLogs.add(new AttendanceLog(null, null, studentAttendancePayload.getStudentId(), UserType.STUDENT,
                    attendanceInputType == null ? AttendanceInputType.WEB : attendanceInputType, null,
                    currentTime, attendanceTime, currentTime, attendanceRegister.getCreatedBy(), metadata));
        }

        // TODO : handle transaction if required
        userAttendanceDao.addAttendanceLog(attendanceRegister.getInstituteId(), attendanceLogs);
        List<StudentAttendancePayload> updatedAttendanceStudents = attendanceDao.saveAttendance(attendanceRegister);

        AttendancePreferences attendancePreferences = userPreferenceSettings.getAttendancePreferences(attendanceRegister.getInstituteId());
        boolean sendMobileAppPushNotification = sameDayAttendance;
        boolean sendSMS = AttendanceUtils.computeIfAttendanceSMSSend(attendancePreferences, sameDayAttendance, attendanceStatuesSet, attendanceTypeSet);

        return new SaveStudentAttendanceResult(sendMobileAppPushNotification, sendSMS, attendanceRegister.getAcademicSessionId(), updatedAttendanceStudents);
    }

    public List<StudentAttendanceRecord> getAttendance(int instituteId, int academicSessionid, UUID standardId,
                                                       Integer sectionId, int attendanceType, int attendanceDate) {
        final List<StudentAttendanceRecord> classAttendanceRecords = attendanceDao.getAttendance(instituteId,
                academicSessionid, standardId, sectionId, attendanceType, attendanceDate);
        if (classAttendanceRecords == null) {
            return null;
        }

        Collections.sort(classAttendanceRecords, new Comparator<StudentAttendanceRecord>() {
            @Override
            public int compare(StudentAttendanceRecord s1, StudentAttendanceRecord s2) {
                return s1.getStudent().getStudentBasicInfo().getName()
                        .compareToIgnoreCase(s2.getStudent().getStudentBasicInfo().getName());
            }
        });
        return classAttendanceRecords;
    }

    public List<StudentAttendanceRecords> getAttendance(int instituteId, int academicSessionId, UUID standardId,
                                                        Integer sectionId, List<Integer> attendanceType, int attendanceDate, UUID userId) {

        /**
         * for backward compatibility of mobile app
         */
        if(userId != null) {
            if(!verifyAttendanceDataAccessibility(instituteId, academicSessionId, standardId, sectionId, userId, false)) {
                /**
                 * if user don't access to view all classes attendance data and is not class teacher of current class + section, then return empty leave
                 */
                return new ArrayList<>();
            }
        }

        final List<StudentAttendanceRecords> classAttendanceRecords = attendanceDao.getAttendance(instituteId,
                academicSessionId, standardId, sectionId, attendanceType, attendanceDate);
        if (CollectionUtils.isEmpty(classAttendanceRecords)) {
            return null;
        }

        Collections.sort(classAttendanceRecords, new Comparator<StudentAttendanceRecords>() {
            @Override
            public int compare(StudentAttendanceRecords s1, StudentAttendanceRecords s2) {
                return s1.getStudent().getStudentBasicInfo().getName()
                        .compareToIgnoreCase(s2.getStudent().getStudentBasicInfo().getName());
            }
        });
        return classAttendanceRecords;
    }

    private boolean verifyAttendanceDataAccessibility(int instituteId, int academicSessionId, UUID standardId, Integer sectionId, UUID userId, boolean throwError) {
        if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ACCESS_ALL_CLASSES_ATTENDANCE_DATA, false)) {
            Map<UUID, Set<Integer>> classTeacherStandardSectionDetails = instituteManager.getStandardWithStaffDetailsList(
                    instituteId, academicSessionId, userId, null);
            if(!EPermissionUtils.checkUserAccessibility(classTeacherStandardSectionDetails, standardId, sectionId)) {
                if(throwError) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                            "You are not allowed access/update this class attendance data."));
                }
                return false;
            }
        }
        return true;
    }

    public List<StudentAttendanceRecord> getAttendanceWithSessionDetails(int instituteId, int academicSessionid, UUID standardId,
                                                                         Integer sectionId, int attendanceType, int attendanceDate) {
        final List<StudentAttendanceRecord> classAttendanceRecords = attendanceDao.getAttendanceWithSessionDetails(instituteId,
                academicSessionid, standardId, sectionId, attendanceType, attendanceDate);
        if (classAttendanceRecords == null) {
            return null;
        }

        Collections.sort(classAttendanceRecords, new Comparator<StudentAttendanceRecord>() {
            @Override
            public int compare(StudentAttendanceRecord s1, StudentAttendanceRecord s2) {
                return s1.getStudent().getStudentBasicInfo().getName()
                        .compareToIgnoreCase(s2.getStudent().getStudentBasicInfo().getName());
            }
        });
        return classAttendanceRecords;
    }

    public List<StudentAttendanceRegisterPayload> getStudentAttendancePayload(int instituteId,
                int academicSessionId, Set<UUID> studentIdSet, Integer startDate, Integer endDate) {
        return getStudentAttendancePayload(instituteId, academicSessionId, studentIdSet, startDate, endDate, null);
    }

    public List<StudentAttendanceRegisterPayload> getStudentAttendancePayload(int instituteId,
            int academicSessionId, Set<UUID> studentIdSet, Integer startDate, Integer endDate, Set<Integer> attendanceTypeIds) {
        return attendanceDao.getStudentAttendancePayload(instituteId, academicSessionId, studentIdSet, startDate, endDate, attendanceTypeIds, new HashSet<>(Arrays.asList(Module.ATTENDANCE)));
    }

    public Map<Integer, List<StudentAttendanceRecords>> getAttendanceWithSessionDetail(int instituteId, int academicSessionid,
                                                                                       List<UUID> standardIdList, List<Integer> sectionIdList, List<AttendanceStatus> attendanceStatusList,
                                                                                       List<Integer> attendanceType, Integer start, Integer end) {
        final Map<Integer, List<StudentAttendanceRecords>> classAttendanceRecords = attendanceDao.
                getAttendanceWithSessionDetail(instituteId,
                        academicSessionid, standardIdList, sectionIdList, attendanceStatusList, attendanceType, start, end, new HashSet<>(Arrays.asList(Module.ATTENDANCE)));
        if (classAttendanceRecords == null) {
            return null;
        }

        for (Entry<Integer, List<StudentAttendanceRecords>> studentAttendanceRecords : classAttendanceRecords.entrySet()) {
            Collections.sort(studentAttendanceRecords.getValue(), new Comparator<StudentAttendanceRecords>() {
                @Override
                public int compare(StudentAttendanceRecords s1, StudentAttendanceRecords s2) {

                    int compare = s1.getStudent().getStudentAcademicSessionInfoResponse().getStandard()
                            .compareTo(s2.getStudent().getStudentAcademicSessionInfoResponse().getStandard());
                    if (compare != 0) {
                        return compare;
                    }
                    return s1.getStudent().getStudentBasicInfo().getName()
                            .compareToIgnoreCase(s2.getStudent().getStudentBasicInfo().getName());
                }
            });
        }

        return classAttendanceRecords;
    }

    public boolean addAttendanceType(int instituteId, int academicSessionId, AttendanceType attendanceType, UUID userId, Module moduleName) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT_ATTENDANCE_TYPE, "Invalid institute Id"));
        }
        if (userId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT_ATTENDANCE_TYPE, "Invalid User Id"));
        }
        userPermissionManager.verifyAuthorisation(instituteId, userId,
                AuthorisationRequiredAction.ADD_STUDENT_ATTENDANCE_TYPE);
        validateAttendanceType(attendanceType, false);
        AttendanceType existingAttendanceType = attendanceDao.checkExistingNames(instituteId, academicSessionId, attendanceType.getName());
        if (existingAttendanceType != null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT_ATTENDANCE_TYPE, "Student Attendance type already exists with given name. "
                            + "Please change the name and try again."));
        }
        return attendanceDao.addAttendanceType(attendanceType, moduleName);
    }

    private void validateAttendanceType(AttendanceType attendanceType, boolean update) {
        if (attendanceType == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT_ATTENDANCE_TYPE, "Invalid Attendance Type"));
        }
        if (attendanceType.getInstituteId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT_ATTENDANCE_TYPE, "Invalid institute Id"));
        }
        if (attendanceType.getAcademicSessionId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT_ATTENDANCE_TYPE, "Invalid Academic Session"));
        }
        if (StringUtils.isBlank(attendanceType.getName())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT_ATTENDANCE_TYPE, "Invalid Attendance Type Name"));
        }
        if (update) {
            if (attendanceType.getAttendanceTypeId() <= 0) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT_ATTENDANCE_TYPE, "Invalid Attendance Type Id"));
            }
        }
    }

    public boolean updateAttendanceType(AttendanceType attendanceType, UUID userId, int instituteId) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT_ATTENDANCE_TYPE, "Invalid institute Id"));
        }
        if (userId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT_ATTENDANCE_TYPE, "Invalid User Id"));
        }
        userPermissionManager.verifyAuthorisation(attendanceType.getInstituteId(), userId,
                AuthorisationRequiredAction.UPDATE_STUDENT_ATTENDANCE_TYPE);
        validateAttendanceType(attendanceType, true);
        AttendanceType existingAttendanceType = attendanceDao.checkExistingNames(attendanceType.getInstituteId(), attendanceType.getAcademicSessionId(), attendanceType.getName());
        if (existingAttendanceType != null) {
            if (attendanceType.getAttendanceTypeId() != existingAttendanceType.getAttendanceTypeId()) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT_ATTENDANCE_TYPE, "Student Attendance type already exists with given name. "
                                + "Please change the name and try again."));
            }
        }
        return attendanceDao.updateAttendanceType(attendanceType);
    }

    public boolean deleteAttendanceType(int instituteId, int academicSessionId, int attendanceTypeId, UUID userId) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT_ATTENDANCE_TYPE, "Invalid institute Id"));
        }
        if (userId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT_ATTENDANCE_TYPE, "Invalid User Id"));
        }
        userPermissionManager.verifyAuthorisation(instituteId, userId,
                AuthorisationRequiredAction.DELETE_STUDENT_ATTENDANCE_TYPE);

        if (academicSessionId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT_ATTENDANCE_TYPE, "Invalid institute Id"));
        }
        if (attendanceTypeId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT_ATTENDANCE_TYPE, "Invalid Attendance Type Id"));
        }
        return attendanceDao.deleteAttendanceType(instituteId, academicSessionId, attendanceTypeId);
    }

    public StudentAttendanceStats getStudentAttendanceStats(int instituteId, int academicSessionId, UUID studentId) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ATTENDANCE, "Invalid institute id."));
        }

        if (studentId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ATTENDANCE, "Invalid student id."));
        }

        Set<UUID> studentIdSet = new HashSet<>();
        studentIdSet.add(studentId);
        final List<StudentAttendanceRegisterPayload> studentAttendanceRegisterPayloadList = getStudentAttendancePayload(instituteId,
                academicSessionId, studentIdSet, null, null);

        if (CollectionUtils.isEmpty(studentAttendanceRegisterPayloadList)) {
            return new StudentAttendanceStats(instituteId, academicSessionId, null,
                    null, null);
        }

        Map<AttendanceStatus, Double> totalAttendanceStatusMap = defaultAttendanceStatusMap();
        LinkedHashMap<Month, Map<AttendanceStatus, Double>> monthlyAttendanceStatusMap = defaultMonthlyAttendanceStatusMap();

        AttendanceStatus todayAttendance = null;
        int now = DateUtils.now();
        for (StudentAttendanceRegisterPayload studentAttendanceRegisterPayload : studentAttendanceRegisterPayloadList) {
            Month month = DateUtils.getMonthOfYear(studentAttendanceRegisterPayload.getAttendanceDateTS());
            if (month == null) {
                continue;
            }
            Map<AttendanceStatus, Double> attendanceStatusMap = monthlyAttendanceStatusMap.get(month);

            AttendanceStatus attendanceStatus = studentAttendanceRegisterPayload.getAttendanceStatus();
            if (attendanceStatus == null) {
                continue;
            }

            if (DateUtils.isSameDay(studentAttendanceRegisterPayload.getAttendanceDateTS(), now)) {
                todayAttendance = attendanceStatus;
            }

            Double attendance = attendanceStatusMap.get(attendanceStatus);
            attendanceStatusMap.put(attendanceStatus, attendance + 1);

            Double totalAttendance = totalAttendanceStatusMap.get(attendanceStatus);
            totalAttendanceStatusMap.put(attendanceStatus, totalAttendance + 1);

        }

        return new StudentAttendanceStats(instituteId, academicSessionId, todayAttendance,
                totalAttendanceStatusMap, monthlyAttendanceStatusMap);
    }

    public StudentSessionAttendanceDetails getStudentSessionAttendanceDetails(int instituteId, int academicSessionId, UUID studentId) {
        if (instituteId <= 0 || academicSessionId <= 0) {
            logger.error("Invalid institute {}, or session id {}", instituteId, academicSessionId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ATTENDANCE, "Invalid institute id or session id."));
        }

        if (studentId == null) {
            logger.error("Invalid studentId {}", studentId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ATTENDANCE, "Invalid student id."));
        }

        AcademicSession academicSession = instituteManager.getAcademicSession(instituteId, academicSessionId);
        if (academicSession == null) {
            logger.error("Invalid session {} for institute {}", academicSessionId, instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ATTENDANCE, "Invalid session."));
        }


        final List<StudentAttendanceRegisterPayload> studentAttendanceRegisterPayloadList = getStudentAttendancePayload(instituteId,
                academicSessionId, new HashSet<>(Arrays.asList(studentId)), null, null);
        List<StaticHoliday> holidays = holidayCalendarManager.getResolvedStudentHolidayInSession(instituteId, academicSessionId, studentId);
        StudentAttendanceCalculator studentAttendanceCalculator = studentAttendanceCalculatorFactory.getCalculator(StudentAttendanceCalculatorType.BASIC);
        return studentAttendanceCalculator.getSessionAttendanceDetails(instituteId, studentId, academicSession, studentAttendanceRegisterPayloadList, holidays);
    }

    public StudentDateAttendanceDetails getStudentDateAttendanceDetails(int instituteId, int academicSessionId,
                                                                        int date, Integer attendanceType) {
        if (instituteId <= 0 || academicSessionId <= 0) {
            logger.error("Invalid institute {}, or session id {}", instituteId, academicSessionId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ATTENDANCE, "Invalid institute id or session id."));
        }

        int dayStart = DateUtils.getDayStart(date, DateUtils.DEFAULT_TIMEZONE);
        int dayEnd = DateUtils.getDayEnd(date, DateUtils.DEFAULT_TIMEZONE);

        Map<UUID, Student> studentMap = getStudentMap(studentDao.getStudentsInAcademicSession(
                instituteId, academicSessionId, Arrays.asList(StudentStatus.ENROLLED)));
        if (studentMap == null || CollectionUtils.isEmpty(studentMap.entrySet())) {
            return null;
        }

        final List<StudentAttendanceRegisterPayload> studentAttendanceRegisterPayloadList = getStudentAttendancePayload(
                instituteId, academicSessionId, null, dayStart, dayEnd,
                attendanceType == null || attendanceType <= 0 ? null : new HashSet<>(Arrays.asList(attendanceType)));

        Map<UUID, StudentAttendanceDetails> studentAttendanceDetailsMap = getStudentAttendanceRegisterMap(instituteId, studentMap, studentAttendanceRegisterPayloadList);
        Map<UUID, List<StaticHoliday>> studentHolidayMap = getStudentHolidayMap(studentMap, holidayCalendarManager.getResolvedHolidayByUserType(instituteId, academicSessionId, UserType.STUDENT));
        StudentAttendanceCalculator studentAttendanceCalculator = studentAttendanceCalculatorFactory.getCalculator(StudentAttendanceCalculatorType.BASIC);
        List<AttendanceType> attendanceTypeList = getInstituteAttendanceTypes(instituteId, academicSessionId, null);
        int maxAttendanceShift = attendanceType != null || CollectionUtils.isEmpty(attendanceTypeList) ? 1 :
                attendanceTypeList.size();
        return studentAttendanceCalculator.getStudentDateAttendanceDetails(instituteId, academicSessionId,
                date, attendanceType, studentAttendanceDetailsMap, studentHolidayMap, maxAttendanceShift, attendanceTypeList);
    }

    private Map<UUID, List<StaticHoliday>> getStudentHolidayMap(Map<UUID, Student> studentMap, Map<UUID,
            List<StaticHoliday>> resolvedHolidayByUserType) {
        Map<UUID, List<StaticHoliday>> finalHolidayByUserType = new HashMap<>();
        for(Entry<UUID, Student> studentEntry : studentMap.entrySet()) {
            UUID studentId = studentEntry.getKey();
            List<StaticHoliday> staticHolidayList = resolvedHolidayByUserType.get(studentId);
            finalHolidayByUserType.put(studentId, CollectionUtils.isEmpty(staticHolidayList) ?
                            new ArrayList<>() : staticHolidayList);
        }
        return finalHolidayByUserType;
    }

    private Map<UUID, Student> getStudentMap(List<Student> studentsInAcademicSession) {
        if(CollectionUtils.isEmpty(studentsInAcademicSession)) {
            return null;
        }
        Map<UUID, Student> studentMap = new HashMap<>();
        for(Student student : studentsInAcademicSession) {
            if(!studentMap.containsKey(student.getStudentId())) {
                studentMap.put(student.getStudentId(), student);
            }
        }
        return studentMap;
    }

    private Map<UUID, StudentAttendanceDetails> getStudentAttendanceRegisterMap(int instituteId,
            Map<UUID, Student> studentMap, List<StudentAttendanceRegisterPayload> studentAttendanceRegisterPayloadList) {
        if(studentMap == null || CollectionUtils.isEmpty(studentMap.entrySet())) {
            return new HashMap<>();
        }
        Map<UUID, List<StudentAttendanceRegisterPayload>> studentAttendanceRegisterPayloadMap = new HashMap<>();
        for(StudentAttendanceRegisterPayload studentAttendanceRegisterPayload : studentAttendanceRegisterPayloadList) {
            if(studentAttendanceRegisterPayload == null || studentAttendanceRegisterPayload.getStudentId() == null) {
                continue;
            }
            UUID studentId = studentAttendanceRegisterPayload.getStudentId();
            if(!studentAttendanceRegisterPayloadMap.containsKey(studentId)) {
                studentAttendanceRegisterPayloadMap.put(studentId, new ArrayList<>());
            }
            studentAttendanceRegisterPayloadMap.get(studentId).add(studentAttendanceRegisterPayload);
        }
        Map<UUID, StudentAttendanceDetails> finalStudentAttendanceRegisterPayloadMap = new HashMap<>();
        for(Entry<UUID, Student> studentEntry : studentMap.entrySet()) {
            UUID studentId = studentEntry.getKey();
            List<StudentAttendanceRegisterPayload> studentAttendanceRegisterPayloadList1 = studentAttendanceRegisterPayloadMap.get(studentId);
            Standard standard = studentEntry.getValue().getStudentAcademicSessionInfoResponse().getStandard();
            StudentAttendanceDetails studentAttendanceDetails = new StudentAttendanceDetails(instituteId,
                    studentEntry.getValue().getStudentBasicInfo().getAdmissionNumber(),
                    studentEntry.getValue().getStudentBasicInfo().getName(),
                    studentEntry.getValue().getStudentAcademicSessionInfoResponse().getRollNumber(),
                    standard.getStandardId().toString(), CollectionUtils.isEmpty(standard.getStandardSectionList())
                    ? null : standard.getStandardSectionList().get(0).getSectionId(),
                    standard.getDisplayNameWithSection(), standard.getLevel(),
                    studentEntry.getValue().getStudentBasicInfo().getPrimaryContactNumber(),
                    studentAttendanceRegisterPayloadList1);
            finalStudentAttendanceRegisterPayloadMap.put(studentId, studentAttendanceDetails);
        }
        return finalStudentAttendanceRegisterPayloadMap;
    }

    private Map<AttendanceStatus, Double> defaultAttendanceStatusMap() {
        HashMap<AttendanceStatus, Double> attendanceStatusMap = new HashMap<>();
        attendanceStatusMap.put(AttendanceStatus.PRESENT, 0d);
        attendanceStatusMap.put(AttendanceStatus.ABSENT, 0d);
        attendanceStatusMap.put(AttendanceStatus.LEAVE, 0d);
        attendanceStatusMap.put(AttendanceStatus.HALF_DAY, 0d);
        return attendanceStatusMap;
    }

    private LinkedHashMap<Month, Map<AttendanceStatus, Double>> defaultMonthlyAttendanceStatusMap() {
        LinkedHashMap<Month, Map<AttendanceStatus, Double>> monthlyAttendanceStatusMap = new LinkedHashMap<>();
        for (Month month : Month.values()) {
            HashMap<AttendanceStatus, Double> attendanceStatusMap = new HashMap<>();
            attendanceStatusMap.put(AttendanceStatus.PRESENT, 0d);
            attendanceStatusMap.put(AttendanceStatus.ABSENT, 0d);
            attendanceStatusMap.put(AttendanceStatus.LEAVE, 0d);
            attendanceStatusMap.put(AttendanceStatus.HALF_DAY, 0d);
            monthlyAttendanceStatusMap.put(month, attendanceStatusMap);
        }
        return monthlyAttendanceStatusMap;
    }

    public StudentAttendanceMonthlyStats getStudentAttendanceMonthlyStats(int instituteId, int academicSessionId,
                                                                          Month month, UUID studentId) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_LECTURE_DETAILS, "Invalid institute id."));
        }

        if (studentId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_LECTURE_DETAILS, "Invalid student id."));
        }

        if (month == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_LECTURE_DETAILS, "Invalid month details."));
        }

        int startDate = DateUtils.getFirstDayOfMonth(month, DateUtils.getCurrentYear(), DateUtils.DEFAULT_TIMEZONE);
        int endDate = DateUtils.getLastDayOfMonth(month, DateUtils.getCurrentYear(), DateUtils.DEFAULT_TIMEZONE);
        Set<UUID> studentIdSet = new HashSet<>();
        studentIdSet.add(studentId);
        final List<StudentAttendanceRegisterPayload> studentAttendanceRegisterPayloadList = getStudentAttendancePayload(instituteId,
                academicSessionId, studentIdSet, startDate, endDate);

        if (CollectionUtils.isEmpty(studentAttendanceRegisterPayloadList)) {
            return new StudentAttendanceMonthlyStats(instituteId, academicSessionId, month, null);
        }

        Map<AttendanceStatus, Double> totalAttendanceStatusMap = defaultAttendanceStatusMap();

        for (StudentAttendanceRegisterPayload studentAttendanceRegisterPayload : studentAttendanceRegisterPayloadList) {
            AttendanceStatus attendanceStatus = studentAttendanceRegisterPayload.getAttendanceStatus();
            if (attendanceStatus == null) {
                continue;
            }
            Double totalAttendance = totalAttendanceStatusMap.get(attendanceStatus);
            totalAttendanceStatusMap.put(attendanceStatus, totalAttendance + 1);

        }

        return new StudentAttendanceMonthlyStats(instituteId, academicSessionId, month, totalAttendanceStatusMap);
    }

    public Map<UUID, Map<AttendanceStatus, Integer>> getStudentAttendanceDetails(int instituteId,
                                                                                 int academicSessionId, Set<UUID> studentIdSet, Integer startDate, Integer endDate) {

        List<StudentAttendanceRegisterPayload> studentAttendancePayloadList =
                getStudentAttendancePayload(instituteId, academicSessionId, studentIdSet, startDate, endDate);
        Map<UUID, Map<AttendanceStatus, Integer>> attendanceStatusMap = new HashMap<>();

        for (StudentAttendanceRegisterPayload studentAttendanceRegisterPayload : studentAttendancePayloadList) {
            UUID studentId = studentAttendanceRegisterPayload.getStudentId();
            AttendanceStatus attendanceStatus = studentAttendanceRegisterPayload.getAttendanceStatus();
            if (studentId == null || attendanceStatus == null) {
                continue;
            }
            if (attendanceStatusMap.containsKey(studentId)) {
                Map<AttendanceStatus, Integer> attendanceStatusIntegerMap = attendanceStatusMap.get(studentId);
                attendanceStatusIntegerMap.put(attendanceStatus, attendanceStatusIntegerMap.get(attendanceStatus) + 1);
            } else {
                Map<AttendanceStatus, Integer> attendanceStatusIntegerMap = new HashMap<>();
                attendanceStatusIntegerMap.put(AttendanceStatus.PRESENT, 0);
                attendanceStatusIntegerMap.put(AttendanceStatus.LEAVE, 0);
                attendanceStatusIntegerMap.put(AttendanceStatus.HALF_DAY, 0);
                attendanceStatusIntegerMap.put(AttendanceStatus.ABSENT, 0);
                attendanceStatusIntegerMap.put(attendanceStatus, attendanceStatusIntegerMap.get(attendanceStatus) + 1);
                attendanceStatusMap.put(studentId, attendanceStatusIntegerMap);
            }
        }

        return attendanceStatusMap;
    }

    public StudentAttendanceMetadata getStudentAttendanceMetadata(int instituteId, int academicSessionId) {
        List<Standard> standardList = instituteManager.getInstituteStandardDetails(instituteId, academicSessionId);

        List<AttendanceType> attendanceTypeList = getInstituteAttendanceTypes(instituteId, academicSessionId, null);
        //TODO : fetch attendance type class wise eg
        //AttendanceUtils.getAttendanceTypes(attendanceTypeList, standardId)
        AttendancePreferences attendancePreferences = userPreferenceSettings.getAttendancePreferences(instituteId);
        StudentAttendancePreferences studentAttendancePreferences = new StudentAttendancePreferences(attendancePreferences.isDeviceAttendanceEnabled(), attendancePreferences.getDefaultAttendanceStatus());
        return new StudentAttendanceMetadata(instituteId, academicSessionId, standardList, attendanceTypeList,
                studentAttendancePreferences);
    }
}
