/**
 *
 */
package com.lernen.cloud.core.lib.attendance;

import com.embrate.cloud.core.api.calendar.holiday.Holiday;
import com.embrate.cloud.core.api.calendar.holiday.StaticHoliday;
import com.embrate.cloud.core.lib.calendar.holiday.HolidayCalendarManager;
import com.lernen.cloud.core.api.attendance.*;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fees.MonthYear;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.report.*;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.constants.ReportHeaderAttribute;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.NumberUtils;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;
import com.lernen.cloud.core.lib.reports.ReportGenerator;

import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.Map.Entry;

import static com.lernen.cloud.core.api.common.StringHelper.capitalizeFirstOfAllWords;


/**
 * <AUTHOR>
 */
public class AttendanceReportGenerator extends ReportGenerator {

    private static final Logger logger = LogManager.getLogger(AttendanceReportGenerator.class);

    private final AttendanceManager attendanceManager;

    private final StudentManager studentManager;

    private final InstituteManager instituteManager;

    private final UserPermissionManager userPermissionManager;

    private final HolidayCalendarManager holidayCalendarManager;

    private static final String DATE_FORMAT = "dd/MMM/yyyy";

    private static final Map<String, List<ReportHeaderAttribute>> REPORT_HEADERS = new HashMap<>();
    private static final Map<String, List<ReportHeaderAttribute>> REPORT_HEADERS_MAP = new HashMap<>();

    private static final String[] ATTENDANCE_DETAILS_IN_A_DAY_REPORT = {"Sr No.", "Date", "Admission Number", "Name", "Class",
            "Primary Contact Number", "Father's Name", "Father's Contact Number"};

    private static final String[] ATTENDANCE_SUMMARY_REPORT = {"Sr No.", "Admission Number", "Name", "Class",
            "Primary Contact Number", "Father's Name", "Father's Contact Number", "Present", "Absent", "Half Day", "Leave", "Total Days", "Present %"};

    private static final String[] TOTAL_DAYS = {"Total Days in Current Month", "Total Days of Previous Months", "Total Days"};

    private static final String[] PRESENT_COUNT = {"Attendance in Current Month", "Attendance of Previous Months", "Total Attendance"};

    private static final String[] ABSENT_COUNT = {"Absent in Current Month", "Absent of Previous Months", "Total Absent Days"};

    private static final String[] LEAVE_COUNT = {"Leaves in Current Month", "Total Leaves of Previous Months",  "Total Leaves"};

    private static final String[] HALF_DAY_COUNT = {"Half Days in Current Month", "Half Days of Previous Months", "Total Half Days"};

    private static final String[] HOLIDAY_COUNT = {"Holidays in Current Month", "Holidays of Previous Months", "Total Holidays"};

    private static final String[] UNMARKED_COUNT  = {"Unmarked Days in Current Month", "Unmarked Days of Previous Months" , "Total Unmarked Days"};

    private static final String NA = "NA";

    static {
        REPORT_HEADERS.put(AttendanceReportType.ATTENDANCE_DETAILS_IN_A_MONTH.name(), Arrays.asList(
                ReportHeaderAttribute.ATTENDANCE_SR_NO,
                ReportHeaderAttribute.ATTENDANCE_STUDENT_NAME.markMandatory(),
                ReportHeaderAttribute.ATTENDANCE_ADMISSION_NO.markMandatory(),
                ReportHeaderAttribute.ATTENDANCE_STUDENT_CLASS,
                ReportHeaderAttribute.ATTENDANCE_STUDENT_ROLL_NUMBER,
                ReportHeaderAttribute.ATTENDANCE_STUDENT_PRIMARY_CONTACT_NUMBER,
                ReportHeaderAttribute.TOTAL_DAYS,
                ReportHeaderAttribute.PRESENT_COUNT,
                ReportHeaderAttribute.ABSENT_COUNT,
                ReportHeaderAttribute.LEAVE_COUNT,
                ReportHeaderAttribute.HALF_DAY_COUNT,
                ReportHeaderAttribute.HOLIDAY_COUNT,
                ReportHeaderAttribute.UNMARKED_COUNT,
                ReportHeaderAttribute.PRESENT_PERCENT));

        REPORT_HEADERS.put(AttendanceReportType.MONTHLY_ATTENDANCE_SUMMARY_REPORT.name(), Arrays.asList(
                ReportHeaderAttribute.ATTENDANCE_SR_NO,
                ReportHeaderAttribute.ATTENDANCE_STUDENT_NAME.markMandatory(),
                ReportHeaderAttribute.ATTENDANCE_ADMISSION_NO.markMandatory(),
                ReportHeaderAttribute.ATTENDANCE_STUDENT_CLASS,
                ReportHeaderAttribute.ATTENDANCE_STUDENT_ROLL_NUMBER,
                ReportHeaderAttribute.ATTENDANCE_STUDENT_PRIMARY_CONTACT_NUMBER,
                ReportHeaderAttribute.TOTAL_DAYS,
                ReportHeaderAttribute.PRESENT_COUNT,
                ReportHeaderAttribute.ABSENT_COUNT,
                ReportHeaderAttribute.LEAVE_COUNT,
                ReportHeaderAttribute.HALF_DAY_COUNT,
                ReportHeaderAttribute.HOLIDAY_COUNT,
                ReportHeaderAttribute.UNMARKED_COUNT));
    }


    public AttendanceReportGenerator(AttendanceManager attendanceManager,StudentManager studentManager,
                                     InstituteManager instituteManager, UserPermissionManager userPermissionManager, HolidayCalendarManager holidayCalendarManager) {
        this.attendanceManager = attendanceManager;
        this.studentManager = studentManager;
        this.instituteManager = instituteManager;
        this.userPermissionManager = userPermissionManager;
        this.holidayCalendarManager = holidayCalendarManager;
    }

    public List<ReportHeaderAttribute> getReportHeader(AttendanceReportType attendanceReportType) {
        return REPORT_HEADERS.containsKey(attendanceReportType.name()) ? REPORT_HEADERS.get(attendanceReportType.name())
                : new ArrayList<>();
    }

    public Map<String, List<ReportHeaderAttribute>> getReportHeader() {
        return REPORT_HEADERS;
    }

    public ReportDetails generateReport(int instituteId, int academicSessionId, String reportType, String standardIdStr, String attendanceStatusStr,
                                        String attendanceTypeIdStr, String attendanceMonth, Integer start, Integer end, Integer singleDate, UUID userId, DownloadFormat downloadFormat, String requiredHeaderStr) {

        if (academicSessionId <= 0 || instituteId <= 0 || userId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid Request"));
        }

        if (downloadFormat == DownloadFormat.EXCEL) {
            if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ATTENDANCE_EXCEL_REPORTS, false)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                        "You don't have access to download attendance reports in excel!"));
            }
        } else if (downloadFormat == DownloadFormat.PDF) {
            if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ATTENDANCE_PDF_REPORTS, false)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                        "You don't have access to download attendance reports in pdf!"));
            }
        }


        if (!reportType.equalsIgnoreCase("SINGLE_DAY_ATTENDANCE_REPORT") && (start <= 0 || end <= 0) && !reportType.equalsIgnoreCase("MONTHLY_ATTENDANCE_SUMMARY_REPORT")) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid Request"));
        }

        AttendanceReportType attendanceReportType = AttendanceReportType.getAttendanceReportType(reportType);

        if (attendanceReportType == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid Request"));
        }

        Map<UUID, List<Integer>> standardSectionMap = convertToRequiredStandardsWithSection(standardIdStr);
        List<UUID> standardIdList = new ArrayList<>();
        List<Integer> sectionIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(standardSectionMap)) {
            standardIdList = new ArrayList<>(standardSectionMap.keySet());
            for (List<Integer> sectionList : standardSectionMap.values()) {
                if (CollectionUtils.isEmpty(sectionList)) {
                    continue;
                }
                sectionIdList.addAll(sectionList);
            }
        }

        List<AttendanceStatus> attendanceStatusList = convertAttendanceStatusStr(attendanceStatusStr);
        List<Integer> attendanceTypeList = convertAttendanceTypeStr(attendanceTypeIdStr);
        final Set<String> requiredHeaderAttributesSet = convertToRequiredHeaderAttributes(requiredHeaderStr);
        switch (attendanceReportType) {
            case ATTENDANCE_DETAILS_IN_A_DAY:
                return generateAttendanceDetailsInADay(instituteId, academicSessionId, standardIdList, sectionIdList,
                        attendanceStatusList, attendanceTypeList, start, end);
            case ATTENDANCE_SUMMARY:
                return generateAttendanceSummaryDetails(instituteId, academicSessionId, standardIdList, sectionIdList, start, end);
            case ATTENDANCE_DETAILS_IN_A_MONTH:
                return generateAttendanceDetailsInAMonth(instituteId, academicSessionId, standardIdList, sectionIdList,
                        attendanceStatusList, attendanceTypeList, start, end, requiredHeaderAttributesSet, attendanceReportType);
            case SINGLE_DAY_ATTENDANCE_REPORT:
                if (singleDate <= 0) {
                    throw new ApplicationException(
                            new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid Request"));
                }
                return generateSingleDayAttendance(instituteId, academicSessionId,  standardIdList, sectionIdList,
                        attendanceStatusList, attendanceTypeList, singleDate);
            case MONTHLY_ATTENDANCE_SUMMARY_REPORT:
                return generateMonthlyAttendanceSummaryReport(instituteId, academicSessionId, standardIdList, sectionIdList,
                        attendanceStatusList, attendanceTypeList, attendanceMonth, requiredHeaderAttributesSet, attendanceReportType);
            default:
                break;
        }
        return null;
    }

    private ReportDetails generateAttendanceSummaryDetails(int instituteId, int academicSessionId,
                                                           List<UUID> standardIdList, List<Integer> sectionIdList,
                                                           Integer start, Integer end) {

        List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
        final String reportName = "ATTENDANCE SUMMARY";
        AcademicSession academicSession = instituteManager.getAcademicSession(instituteId, academicSessionId);
        String heading = academicSession == null ? reportName : reportName + " (" + academicSession.getDisplayName() + ") ";
        reportHeaderRow.add(new ReportCellDetails(heading, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetails.add(reportHeaderRow);

        List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
        for (int i = 0; i < ATTENDANCE_SUMMARY_REPORT.length; i++) {
            reportCellDetailsHeaderRow.add(new ReportCellDetails(ATTENDANCE_SUMMARY_REPORT[i], STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        }
        reportCellDetails.add(reportCellDetailsHeaderRow);

        try {

            if (CollectionUtils.isEmpty(standardIdList)) {
                List<Standard> standardList = instituteManager.getInstituteStandardDetails(instituteId, academicSessionId);
                standardIdList = getStandardIds(standardList);
            }
            Set<StudentStatus> studentStatusSet = new HashSet<>(Arrays.asList(StudentStatus.ENROLLED));
            LinkedHashMap<UUID, Student> studentMap = getStudentMap(sortStudentList(
                    studentManager.getStudentsWithWalletDetails(instituteId, academicSessionId,
                            new HashSet<>(standardIdList), studentStatusSet)));

            if (CollectionUtils.isEmpty(studentMap)) {
                logger.info("Empty summary report {}", reportName);
                List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
                return new ReportDetails(reportName, reportSheetDetailsList);
            }

            Map<UUID, Map<AttendanceStatus, Integer>> studentAttendanceDetailsMap =
                    attendanceManager.getStudentAttendanceDetails(instituteId, academicSessionId,
                            studentMap.keySet(), start, end);

            if (CollectionUtils.isEmpty(studentAttendanceDetailsMap)) {
                logger.info("Empty summary report {}", reportName);
                List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
                return new ReportDetails(reportName, reportSheetDetailsList);
            }

            int count = 1;
            for (Entry<UUID, Student> studentMapEntry : studentMap.entrySet()) {
                UUID studentId = studentMapEntry.getKey();
                Student student = studentMapEntry.getValue();
                if (student == null) {
                    continue;
                }
                Integer sectionId = CollectionUtils.isEmpty(student.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList()) ? null :
                        student.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList().get(0).getSectionId();
                if (!CollectionUtils.isEmpty(sectionIdList) && !sectionIdList.contains(sectionId)) {
                    continue;
                }
                List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
                reportCellDetailsRow.add(new ReportCellDetails(String.valueOf(count), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(student.getStudentBasicInfo().getAdmissionNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
                reportCellDetailsRow.add(new ReportCellDetails(student.getStudentBasicInfo().getName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
                reportCellDetailsRow.add(new ReportCellDetails(student.getStudentAcademicSessionInfoResponse()
                        .getStandard().getDisplayNameWithSection(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(student.getStudentBasicInfo().getPrimaryContactNumber() == null ?
                        NA : student.getStudentBasicInfo().getPrimaryContactNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(student.getStudentFamilyInfo() == null ? NA :
                        student.getStudentFamilyInfo().getFathersName() == null ? NA :
                                student.getStudentFamilyInfo().getFathersName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
                reportCellDetailsRow.add(new ReportCellDetails(student.getStudentFamilyInfo() == null ? NA :
                        student.getStudentFamilyInfo().getFathersContactNumber() == null ? NA :
                                student.getStudentFamilyInfo().getFathersContactNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

                Map<AttendanceStatus, Integer> attendanceStatusIntegerMap = studentAttendanceDetailsMap.get(studentId);
                Integer presentCount = 0;
                Integer absentCount = 0;
                Integer halfDayCount = 0;
                Integer leaveCount = 0;
                if (!CollectionUtils.isEmpty(attendanceStatusIntegerMap)) {
                    presentCount = attendanceStatusIntegerMap.get(AttendanceStatus.PRESENT);
                    absentCount = attendanceStatusIntegerMap.get(AttendanceStatus.ABSENT);
                    halfDayCount = attendanceStatusIntegerMap.get(AttendanceStatus.HALF_DAY);
                    leaveCount = attendanceStatusIntegerMap.get(AttendanceStatus.LEAVE);
                }
                int totalWorkingDays = (presentCount == null ? 0 : presentCount)
                        + (absentCount == null ? 0 : absentCount)
                        + (halfDayCount == null ? 0 : halfDayCount)
                        + (leaveCount == null ? 0 : leaveCount);
                double presentPercentage = (totalWorkingDays == 0 ? 0 : NumberUtils.formatDoubleNumber((((presentCount * 1d)/totalWorkingDays) * 100d), 2));
                reportCellDetailsRow.add(new ReportCellDetails(presentCount == null ? NA : String.valueOf(presentCount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(absentCount == null ? NA : String.valueOf(absentCount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(halfDayCount == null ? NA : String.valueOf(halfDayCount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(leaveCount == null ? NA : String.valueOf(leaveCount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(String.valueOf(totalWorkingDays), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.removeTrailingZeros(presentPercentage) + "%", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                count++;
                reportCellDetails.add(reportCellDetailsRow);
            }

            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
            CellIndexes cellIndexes = new CellIndexes(0, 0, 0, ATTENDANCE_SUMMARY_REPORT.length - 1);
            mergeCellIndexesList.add(cellIndexes);
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(reportName, true, mergeCellIndexesList, reportCellDetails);
            reportSheetDetailsList.add(reportSheetDetails);

            return new ReportDetails(reportName, reportSheetDetailsList);

        } catch (final Exception e) {
            logger.error("Error while generating {} summary report", reportName, e);
        }

        return null;
    }

    private List<Student> sortStudentList(List<Student> students) {
        if (CollectionUtils.isEmpty(students)) {
            return null;
        }
        /**
         * Make sure sort header course also
         */
        Collections.sort(students, new Comparator<Student>() {
            @Override
            public int compare(Student s1, Student s2) {

                final Standard standard1 = s1.getStudentAcademicSessionInfoResponse().getStandard();
                final Standard standard2 = s2.getStudentAcademicSessionInfoResponse().getStandard();
                final int standardCompare = standard1.compareTo(standard2);
                if (standardCompare != 0) {
                    return standardCompare;
                }

                final List<StandardSections> section1 = s1.getStudentAcademicSessionInfoResponse().getStandard()
                        .getStandardSectionList();
                final List<StandardSections> section2 = s2.getStudentAcademicSessionInfoResponse().getStandard()
                        .getStandardSectionList();
                if (!CollectionUtils.isEmpty(section1) && !CollectionUtils.isEmpty(section2)) {
                    final int sectionCompare = section1.get(0).getSectionName()
                            .compareToIgnoreCase(section2.get(0).getSectionName());
                    if (sectionCompare != 0) {
                        return sectionCompare;
                    }
                }

                return s1.getStudentBasicInfo().getName().compareToIgnoreCase(s2.getStudentBasicInfo().getName());
            }

        });
        return students;
    }

    private List<UUID> getStandardIds(List<Standard> standardList) {
        Map<UUID, Standard> standardMap = new HashMap<>();
        for (Standard standard : standardList) {
            if (!standardMap.containsKey(standard.getStandardId())) {
                standardMap.put(standard.getStandardId(), standard);
            }
        }
        return CollectionUtils.isEmpty(standardMap) ? null : new ArrayList<>(standardMap.keySet());
    }

    private LinkedHashMap<UUID, Student> getStudentMap(List<Student> studentList) {
        if (CollectionUtils.isEmpty(studentList)) {
            return null;
        }
        LinkedHashMap<UUID, Student> studentMap = new LinkedHashMap<>();
        for (Student student : studentList) {
            if (!studentMap.containsKey(student.getStudentId())) {
                studentMap.put(student.getStudentId(), student);
            }
        }
        return CollectionUtils.isEmpty(studentMap) ? null : studentMap;
    }

    private ReportDetails generateAttendanceDetailsInADay(int instituteId, int academicSessionId,
                                                          List<UUID> standardIdList, List<Integer> sectionIdList, List<AttendanceStatus> attendanceStatusList,
                                                          List<Integer> attendanceType, Integer start, Integer end) {

        List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
        final String reportName = "ATTENDANCE DETAILS";
        AcademicSession academicSession = instituteManager.getAcademicSession(instituteId, academicSessionId);
        String heading = academicSession == null ? reportName : reportName + " (" + academicSession.getDisplayName() + ") ";
        reportHeaderRow.add(new ReportCellDetails(heading, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetails.add(reportHeaderRow);

        int totalColumns = ATTENDANCE_DETAILS_IN_A_DAY_REPORT.length;
        List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
        for (int i = 0; i < ATTENDANCE_DETAILS_IN_A_DAY_REPORT.length; i++) {
            reportCellDetailsHeaderRow.add(new ReportCellDetails(ATTENDANCE_DETAILS_IN_A_DAY_REPORT[i], STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        }

        List<AttendanceType> attendanceTypeList = attendanceManager
                .getInstituteAttendanceTypes(instituteId, academicSessionId, attendanceType);

        if (CollectionUtils.isEmpty(attendanceTypeList)) {
            return new ReportDetails(reportName, null);
        }

        LinkedHashMap<Integer, AttendanceType> attendanceMap = new LinkedHashMap<>();

        for (final AttendanceType attendanceTypeVal : attendanceTypeList) {
            final String attendanceTypeNameHeader = attendanceTypeVal.getName();
            reportCellDetailsHeaderRow.add(new ReportCellDetails(attendanceTypeNameHeader, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            int attendanceTypeId = attendanceTypeVal.getAttendanceTypeId();
            attendanceMap.put(attendanceTypeId, attendanceTypeVal);
            totalColumns++;
        }

        reportCellDetails.add(reportCellDetailsHeaderRow);

        try {

            Map<Integer, List<StudentAttendanceRecords>> studentAttendanceRecordList =
                    attendanceManager.getAttendanceWithSessionDetail(instituteId, academicSessionId, standardIdList,
                            sectionIdList, attendanceStatusList, attendanceType, start, end);

            if (CollectionUtils.isEmpty(studentAttendanceRecordList)) {
                logger.info("Empty report {}", reportName);
                List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
                return new ReportDetails(reportName, reportSheetDetailsList);
            }
            int count = 1;
            for (final Entry<Integer, List<StudentAttendanceRecords>> studentAttendanceRecordsMap : studentAttendanceRecordList.entrySet()) {
                for (StudentAttendanceRecords studentAttendanceDetails : studentAttendanceRecordsMap.getValue()) {

                    if (CollectionUtils.isEmpty(studentAttendanceDetails.getAttendanceRecordList())) {
                        continue;
                    }
                    List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
                    reportCellDetailsRow.add(new ReportCellDetails(String.valueOf(count), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(DateUtils.getFormattedDate(studentAttendanceRecordsMap.getKey(), DATE_FORMAT, User.DFAULT_TIMEZONE), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(studentAttendanceDetails.getStudent().getStudentBasicInfo().getAdmissionNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(studentAttendanceDetails.getStudent().getStudentBasicInfo().getName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(studentAttendanceDetails.getStudent().getStudentAcademicSessionInfoResponse()
                            .getStandard().getDisplayNameWithSection(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(studentAttendanceDetails.getStudent().getStudentBasicInfo().getPrimaryContactNumber() == null ?
                            NA : studentAttendanceDetails.getStudent().getStudentBasicInfo().getPrimaryContactNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(studentAttendanceDetails.getStudent().getStudentFamilyInfo() == null ? NA :
                            studentAttendanceDetails.getStudent().getStudentFamilyInfo().getFathersName() == null ? NA :
                                    studentAttendanceDetails.getStudent().getStudentFamilyInfo().getFathersName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(studentAttendanceDetails.getStudent().getStudentFamilyInfo() == null ? NA :
                            studentAttendanceDetails.getStudent().getStudentFamilyInfo().getFathersContactNumber() == null ? NA :
                                    studentAttendanceDetails.getStudent().getStudentFamilyInfo().getFathersContactNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

                    HashMap<Integer, AttendanceStatus> studentAttendanceStatusMap = new HashMap<>();
                    for (AttendanceRecord attendanceRecord : studentAttendanceDetails.getAttendanceRecordList()) {
                        int attendanceTypeId = attendanceRecord.getAttendanceType().getAttendanceTypeId();
                        AttendanceStatus attendanceStatus = attendanceRecord.getAttendanceStatus();
                        studentAttendanceStatusMap.put(attendanceTypeId, attendanceStatus);
                    }

                    for (Entry<Integer, AttendanceType> entry : attendanceMap.entrySet()) {
                        AttendanceStatus attendanceStatus = studentAttendanceStatusMap.get(entry.getKey());
                        String status = attendanceStatus == null ? "" : attendanceStatus.getDisplayName();
                        if (attendanceStatus == AttendanceStatus.LEAVE) {
                            reportCellDetailsRow.add(new ReportCellDetails(status, STRING, CONTENT_SIZE, BLACK_COLOR, YELLOW_COLOR, false));
                        } else if (attendanceStatus == AttendanceStatus.HALF_DAY) {
                            reportCellDetailsRow.add(new ReportCellDetails(status, STRING, CONTENT_SIZE, BLACK_COLOR, ORANGE_COLOR, false));
                        } else if (attendanceStatus == AttendanceStatus.ABSENT) {
                            reportCellDetailsRow.add(new ReportCellDetails(status, STRING, CONTENT_SIZE, BLACK_COLOR, RED_COLOR, false));
                        } else {
                            reportCellDetailsRow.add(new ReportCellDetails(status, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                        }
                    }
                    count++;
                    reportCellDetails.add(reportCellDetailsRow);
                }
            }

            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
            CellIndexes cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
            mergeCellIndexesList.add(cellIndexes);
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(reportName, true, mergeCellIndexesList, reportCellDetails);
            reportSheetDetailsList.add(reportSheetDetails);

            return new ReportDetails(reportName, reportSheetDetailsList);

        } catch (final Exception e) {
            logger.error("Error while generating {} report", reportName, e);
        }
        return null;
    }

    private ReportDetails generateAttendanceDetailsInAMonth(int instituteId, int academicSessionId,
                                                            List<UUID> standardIdList, List<Integer> sectionIdList, List<AttendanceStatus> attendanceStatusList,
                                                            List<Integer> attendanceType, Integer start, Integer end, Set<String> requiredHeaderAttributes, AttendanceReportType attendanceReportType) {

        final String reportName = "MONTH ATTENDANCE DETAILS";
        Institute institute = instituteManager.getInstitute(instituteId);
        List<Student> studentsList = studentManager.getStudentsInAcademicSession(instituteId, academicSessionId);

        List<AttendanceType> attendanceTypeList = attendanceManager.getInstituteAttendanceTypes(instituteId, academicSessionId, attendanceType);
        Map<UUID, List<StaticHoliday>> staticHolidayMap = holidayCalendarManager.getResolvedHolidayByUserType(instituteId, academicSessionId, UserType.STUDENT);
        if (CollectionUtils.isEmpty(studentsList) || CollectionUtils.isEmpty(attendanceTypeList)) {
            return new ReportDetails(reportName, null);
        }

        boolean isSingleAttendanceType = CollectionUtils.isEmpty(attendanceType) ?
                attendanceTypeList.size() == 1 : attendanceType.size() == 1;
      
        Map<Integer, List<StudentAttendanceRecords>> studentAttendanceRecordList =
                attendanceManager.getAttendanceWithSessionDetail(instituteId, academicSessionId, standardIdList,
                        sectionIdList, attendanceStatusList, attendanceType, start, end);

        if (CollectionUtils.isEmpty(studentAttendanceRecordList)) {
            logger.info("Empty report {}", reportName);
            return new ReportDetails(reportName, null);
        }

        Map<UUID, Map<Integer, Map<Integer, AttendanceStatus>>> studentDateAttendanceMap = new HashMap<>();   // map<student_id, map<date, map<Attendancetype, status>>>
        Map<UUID,StudentAttendanceRecords> studentRecords = new HashMap<>();
        List<Integer> dateList = DateUtils.getDateList(DateUtils.getDayStart(start,DateUtils.DEFAULT_TIMEZONE), DateUtils.getDayEnd(end, DateUtils.DEFAULT_TIMEZONE));

        getStudentAttendanceSummaryFormattedData(studentAttendanceRecordList, studentDateAttendanceMap, studentRecords);

        List<StudentAttendanceRecords> studentAttendanceRecordsList = new ArrayList<>(studentRecords.values());
        sortStudentRecord(studentAttendanceRecordsList);
        Collections.sort(dateList);
        AcademicSession academicSession = instituteManager.getAcademicSession(instituteId, academicSessionId);
        String heading = academicSession == null ? reportName : reportName + " (" + academicSession.getDisplayName() + ") ";
        String dateRange = "From " + DateUtils.getFormattedDate(start) + " to " + DateUtils.getFormattedDate(end);
        heading = heading + " " + dateRange;

        List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
        reportHeaderRow.add(new ReportCellDetails(heading, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        headerReportCellDetails.add(reportHeaderRow);

        List<ReportCellDetails> reportCellDetailsHeaderRow1 = new ArrayList<ReportCellDetails>();
        List<ReportCellDetails> reportCellDetailsHeaderRow2 = new ArrayList<ReportCellDetails>();
        List<CellIndexes> headerMergeCellIndexesList = new ArrayList<CellIndexes>();
        List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
        List<ReportHeaderAttribute> reportHeaderAttributeList = REPORT_HEADERS.get(attendanceReportType.name());
        int totalColumns = 0;

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_SR_NO.getKey())) {
            reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.ATTENDANCE_SR_NO.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            CellIndexes cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
            headerMergeCellIndexesList.add(cellIndexes);
            totalColumns++;
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_NAME.getKey())) {
            reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.ATTENDANCE_STUDENT_NAME.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            CellIndexes cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
            headerMergeCellIndexesList.add(cellIndexes);
            totalColumns++;
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_ADMISSION_NO.getKey())) {
            reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.ATTENDANCE_ADMISSION_NO.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            CellIndexes cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
            headerMergeCellIndexesList.add(cellIndexes);
            totalColumns++;
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_CLASS.getKey())) {
            reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.ATTENDANCE_STUDENT_CLASS.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            CellIndexes cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
            headerMergeCellIndexesList.add(cellIndexes);
            totalColumns++;
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_ROLL_NUMBER.getKey())) {
            reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.ATTENDANCE_STUDENT_ROLL_NUMBER.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            CellIndexes cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
            headerMergeCellIndexesList.add(cellIndexes);
            totalColumns++;
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_PRIMARY_CONTACT_NUMBER.getKey())) {
            reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.ATTENDANCE_STUDENT_PRIMARY_CONTACT_NUMBER.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            CellIndexes cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
            headerMergeCellIndexesList.add(cellIndexes);
            totalColumns++;
        }

        int colNum = totalColumns;
        for (final Integer date : dateList) {
            String dateString = DateUtils.getFormattedDate(date, DateUtils.ONLY_DATE_FORMAT, User.DFAULT_TIMEZONE);
            int len = attendanceTypeList.size();
            for (final AttendanceType attendanceTypeVal : attendanceTypeList) {
                final String attendanceTypeNameHeader = isSingleAttendanceType ? EMPTY_TEXT : attendanceTypeVal.getName();
                reportCellDetailsHeaderRow1.add(new ReportCellDetails(dateString, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                reportCellDetailsHeaderRow2.add(new ReportCellDetails(attendanceTypeNameHeader, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                totalColumns++;
                colNum++;
            }

            if (len > 1) {
                CellIndexes cellIndexes = new CellIndexes(1, 1, colNum - len, colNum - 1);
                headerMergeCellIndexesList.add(cellIndexes);
            }
        }


        for (int j = 5; j < reportHeaderAttributeList.size(); j++) {
            ReportHeaderAttribute reportHeaderAttribute = reportHeaderAttributeList.get(j);
            if (!requiredHeaderAttributes.contains(reportHeaderAttribute.getKey())) {
               continue;
            }
            reportCellDetailsHeaderRow1.add(new ReportCellDetails(reportHeaderAttribute.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            CellIndexes cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
            headerMergeCellIndexesList.add(cellIndexes);
            totalColumns += 1;
        }

        headerReportCellDetails.add(reportCellDetailsHeaderRow1);
        headerReportCellDetails.add(reportCellDetailsHeaderRow2);
        try {
            int count = 1;
            for (StudentAttendanceRecords studentAttendanceDetails : studentAttendanceRecordsList) {
                if (CollectionUtils.isEmpty(studentAttendanceDetails.getAttendanceRecordList())) {
                    continue;
                }
                List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
                Student student = studentAttendanceDetails.getStudent();
                UUID studentId = student.getStudentId();
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_SR_NO.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(String.valueOf(count), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_NAME.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(student.getStudentBasicInfo().getName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_ADMISSION_NO.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(student.getStudentBasicInfo().getAdmissionNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_CLASS.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_ROLL_NUMBER.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(student.getStudentAcademicSessionInfoResponse().getRollNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_PRIMARY_CONTACT_NUMBER.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(student.getStudentBasicInfo().getPrimaryContactNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                Map<Integer, Map<Integer, AttendanceStatus>> dateAttendanceMap = studentDateAttendanceMap.get(studentId);
                int totalWorkingDays = 0;
                int presentCount = 0;
                int absentCount = 0;
                int leaveCount = 0;
                int holidayCount = 0;
                int halfDayCount = 0;
                for (Integer date : dateList) {
                    if (!dateAttendanceMap.containsKey(date)) {
                        for (final AttendanceType attendanceTypeVal : attendanceTypeList) {
                            if(DateUtils.isSunday(date)) {
                                reportCellDetailsRow.add(new ReportCellDetails("Sun", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                                holidayCount++;
                                continue;
                            }
                            if(isHoliday(studentId, date, staticHolidayMap)) {
                                reportCellDetailsRow.add(new ReportCellDetails("H", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                                holidayCount++;
                                continue;
                            }
                            totalWorkingDays++;
                            reportCellDetailsRow.add(new ReportCellDetails("", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                        }
                        continue;
                    }
                    Map<Integer, AttendanceStatus> attendanceTypeStatusMap = null;
                    if (!CollectionUtils.isEmpty(dateAttendanceMap)) {
                        attendanceTypeStatusMap = dateAttendanceMap.get(date);
                    }
                    AttendanceStatus studentAttendanceStatus = null;
                    for (final AttendanceType attendanceTypeVal : attendanceTypeList) {
                        int attendanceTypeId = attendanceTypeVal.getAttendanceTypeId();
                        if (attendanceTypeStatusMap != null) {
                            String textColor = BLACK_COLOR;
                            studentAttendanceStatus = attendanceTypeStatusMap.get(attendanceTypeId);
                            if(studentAttendanceStatus == AttendanceStatus.PRESENT) {
                                presentCount++;
                                textColor = GREEN_COLOR;
                            }

                            if(studentAttendanceStatus == AttendanceStatus.ABSENT) {
                                absentCount++;
                                textColor = RED_COLOR;
                            }

                            if(studentAttendanceStatus == AttendanceStatus.HALF_DAY) {
                                halfDayCount++;
                                textColor = ORANGE_COLOR;
                            }

                            if(studentAttendanceStatus == AttendanceStatus.LEAVE) {
                                leaveCount++;
                                textColor = YELLOW_COLOR;
                            }

                            totalWorkingDays++;
                            reportCellDetailsRow.add(new ReportCellDetails(studentAttendanceStatus == null ? EMPTY_TEXT : studentAttendanceStatus.getAbbreviation(), STRING, CONTENT_SIZE, textColor, WHITE_COLOR, false));

                        } 
                    }
                }

                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.TOTAL_DAYS.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(totalWorkingDays, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.PRESENT_COUNT.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(presentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ABSENT_COUNT.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(absentCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.LEAVE_COUNT.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(leaveCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.HALF_DAY_COUNT.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(halfDayCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.HOLIDAY_COUNT.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(holidayCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.UNMARKED_COUNT.getKey())) {
                    int unmarkedCount = totalWorkingDays - (presentCount + absentCount + halfDayCount + leaveCount);
                    reportCellDetailsRow.add(new ReportCellDetails(unmarkedCount, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.PRESENT_PERCENT.getKey())) {
                    double presentPercentage = (totalWorkingDays == 0 ? 0 : NumberUtils.formatDoubleNumber((((presentCount * 1d) / totalWorkingDays) * 100d), 2));
                    reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.removeTrailingZeros(presentPercentage) + "%", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                reportCellDetails.add(reportCellDetailsRow);
                count++;
            }

            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();

            CellIndexes headercellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
            headerMergeCellIndexesList.add(headercellIndexes);

            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(reportName, false,totalColumns, headerMergeCellIndexesList,
                    mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true, true, institute,
                    totalColumns);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        } catch (final Exception e) {
            logger.error("Error while generating {} report", reportName, e);
        }
        return null;

    }

    public boolean isHoliday(UUID studentId, Integer date, Map<UUID, List<StaticHoliday>> staticHolidayMap) {
        if(CollectionUtils.isEmpty(staticHolidayMap) || !staticHolidayMap.containsKey(studentId)) {
            return false;
        }
        List<StaticHoliday> holidays = staticHolidayMap.get(studentId);
        for(Holiday holiday : holidays) {
            int start = holiday.getStart();
            int end  = holiday.getEnd();
            if(date >= start && date < end) {
                return true;
            }
        }
        return false;
    }

    private void getStudentAttendanceSummaryFormattedData( Map<Integer, List<StudentAttendanceRecords>> studentAttendanceRecordList,
                                                           Map<UUID, Map<Integer, Map<Integer, AttendanceStatus>>> studentDateAttendanceMap,
                                                           Map<UUID,StudentAttendanceRecords> studentRecords) {

        for (final Entry<Integer, List<StudentAttendanceRecords>> studentAttendanceRecordsMap : studentAttendanceRecordList.entrySet()) {
            for (StudentAttendanceRecords studentAttendanceDetails : studentAttendanceRecordsMap.getValue()) {

                UUID studentId = studentAttendanceDetails.getStudent().getStudentId();
                studentRecords.put(studentId, studentAttendanceDetails );
                Integer attendanceDate = studentAttendanceRecordsMap.getKey();
                if (!studentDateAttendanceMap.containsKey(studentId)) {
                    studentDateAttendanceMap.put(studentId, new HashMap<>());
                }
                Map<Integer, AttendanceStatus> dateMap = studentDateAttendanceMap.get(studentId).get(attendanceDate);
                if (CollectionUtils.isEmpty(dateMap)) {
                    dateMap = new HashMap<>();
                    studentDateAttendanceMap.get(studentId).put(attendanceDate, dateMap);
                }
                for (AttendanceRecord attendanceRecord : studentAttendanceDetails.getAttendanceRecordList()) {
                    Integer attendanceTypeId = attendanceRecord.getAttendanceType().getAttendanceTypeId();
                    AttendanceStatus attendanceStatus = attendanceRecord.getAttendanceStatus();
                    dateMap.put(attendanceTypeId, attendanceStatus);
                }
            }
        }

    }

    private void sortStudentRecord(List<StudentAttendanceRecords> studentRecords) {

        boolean rollNumberIsNumeric = true;
        for(StudentAttendanceRecords studentAttendanceRecords : studentRecords) {
            if(studentAttendanceRecords == null || studentAttendanceRecords.getStudent().getStudentId() == null) {
                continue;
            }
            String rollNumber = studentAttendanceRecords.getStudent().getStudentAcademicSessionInfoResponse().getRollNumber();
            if(!NumberUtils.isNumeric(rollNumber)) {
                rollNumberIsNumeric = false;
                break;
            }
        }

        boolean finalRollNumberIsNumeric = rollNumberIsNumeric;
        studentRecords.sort(new Comparator<StudentAttendanceRecords>() {
            @Override
            public int compare( StudentAttendanceRecords s1,
                                StudentAttendanceRecords s2) {
                // Compare by standard
                final Standard standard1 = s1.getStudent().getStudentAcademicSessionInfoResponse().getStandard();
                final Standard standard2 = s2.getStudent().getStudentAcademicSessionInfoResponse().getStandard();
                final int standardCompare = standard1.compareTo(standard2);
                if (standardCompare != 0) {
                    return standardCompare;
                }

                final List<StandardSections> section1 = s1.getStudent().getStudentAcademicSessionInfoResponse().getStandard()
                        .getStandardSectionList();
                final List<StandardSections> section2 = s2.getStudent().getStudentAcademicSessionInfoResponse().getStandard()
                        .getStandardSectionList();
                if (!CollectionUtils.isEmpty(section1) && !CollectionUtils.isEmpty(section2)) {
                    final int sectionCompare = section1.get(0).getSectionName()
                            .compareToIgnoreCase(section2.get(0).getSectionName());
                    if (sectionCompare != 0) {
                        return sectionCompare;
                    }
                }
                // Compare by roll number
                final String rollNumber1 = s1.getStudent().getStudentAcademicSessionInfoResponse().getRollNumber();
                final String rollNumber2 = s2.getStudent().getStudentAcademicSessionInfoResponse().getRollNumber();
                if(finalRollNumberIsNumeric) {
                    try {
                        int rollNumberInt1 = StringUtils.isBlank(rollNumber1) ? Integer.MAX_VALUE : Integer.parseInt(rollNumber1);
                        int rollNumberInt2 = StringUtils.isBlank(rollNumber2) ? Integer.MAX_VALUE : Integer.parseInt(rollNumber2);
                        final int rollNumberCompare = rollNumberInt1 - rollNumberInt2;
                        if (rollNumberCompare != 0) {
                            return rollNumberCompare;
                        }
                    } catch (NumberFormatException nfe) {
                        logger.error("Error while parsing", nfe);
                    }
                } else {
                    if (!StringUtils.isBlank(rollNumber1) && !StringUtils.isBlank(rollNumber2)) {
                        final int rollNumberCompare = rollNumber1.compareToIgnoreCase(rollNumber2);
                        if (rollNumberCompare != 0) {
                            return rollNumberCompare;
                        }
                    }
                }

                // Compare by name
                return s1.getStudent().getStudentBasicInfo().getName().compareToIgnoreCase(s2.getStudent().getStudentBasicInfo().getName());
            }
        });
    }


    private ReportDetails generateSingleDayAttendance(int instituteId, int academicSessionId, List<UUID> standardIdList,List<Integer> sectionIdList,
                                                      List<AttendanceStatus> attendanceStatusList, List<Integer> attendanceType, Integer date) {

        final String reportName = "Single Day Attendance Summary";

        Map<Integer, List<StudentAttendanceRecords>> studentAttendanceRecordList =
                attendanceManager.getAttendanceWithSessionDetail(instituteId, academicSessionId, standardIdList,
                        sectionIdList, attendanceStatusList, attendanceType, date, date);

        if (CollectionUtils.isEmpty(studentAttendanceRecordList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        List<StudentAttendanceRecords> studentAttendanceRecordsList = studentAttendanceRecordList.get(date);

        if (CollectionUtils.isEmpty(studentAttendanceRecordsList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        List<Standard> standardList = instituteManager.getInstituteStandardDetails(instituteId, academicSessionId);

        if (CollectionUtils.isEmpty(standardList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        List<AttendanceType> attendanceTypeList = attendanceManager.getInstituteAttendanceTypes(instituteId, academicSessionId, attendanceType);
        if (CollectionUtils.isEmpty(attendanceTypeList)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            return new ReportDetails(reportName, reportSheetDetailsList);
        }

        if (CollectionUtils.isEmpty(attendanceStatusList)) {
            for (AttendanceStatus status : AttendanceStatus.values()) {
                attendanceStatusList.add(status);
            }
        }

        String attendanceTypes = "";

        for (int i = 0; i < attendanceTypeList.size(); i++) {
            AttendanceType attendance = attendanceTypeList.get(i);
            attendanceTypes += attendance.getName();

            if (i < attendanceTypeList.size() - 1) {
                attendanceTypes += ", ";
            }
        }

        List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
        String header = "ATTENDANCE DETAILS AT " + DateUtils.getFormattedDate(date) + " For " + attendanceTypes;

        AcademicSession academicSession = instituteManager.getAcademicSession(instituteId, academicSessionId);
        String heading = academicSession == null ? header : header + " (" + academicSession.getDisplayName() + ") ";
        reportHeaderRow.add(new ReportCellDetails(heading, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetails.add(reportHeaderRow);

        int totalColumns = 0;
        List<ReportCellDetails> reportCellDetailsHeaderRow1 = new ArrayList<ReportCellDetails>();
        List<ReportCellDetails> reportCellDetailsHeaderRow2 = new ArrayList<ReportCellDetails>();
        List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();

        CellIndexes firstCellIndex = new CellIndexes(1, 2, totalColumns, totalColumns);
        mergeCellIndexesList.add(firstCellIndex);
        reportCellDetailsHeaderRow1.add(new ReportCellDetails("CLASS", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        CellIndexes secondCellIndex = new CellIndexes(1, 1, reportCellDetailsHeaderRow1.size(), reportCellDetailsHeaderRow1.size()+2);
        mergeCellIndexesList.add(secondCellIndex);
        reportCellDetailsHeaderRow1.add(new ReportCellDetails("Student", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsHeaderRow1.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsHeaderRow1.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsHeaderRow2.add(new ReportCellDetails("Male", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsHeaderRow2.add(new ReportCellDetails("Female", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsHeaderRow2.add(new ReportCellDetails("Total", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        totalColumns += 4;

        for (int i = 0; i < attendanceStatusList.size(); i++) {
            if(attendanceStatusList.get(i) == null) {
                continue;
            }
            CellIndexes cellIndexes = new CellIndexes(1, 1, reportCellDetailsHeaderRow1.size(), reportCellDetailsHeaderRow1.size()+2);
            mergeCellIndexesList.add(cellIndexes);
            String statusDisplayName  = attendanceStatusList.get(i).getDisplayName();
            generateStaticHeader(statusDisplayName, "", reportCellDetailsHeaderRow1, reportCellDetailsHeaderRow2);
            CellIndexes percentageCellIndexes = new CellIndexes(1, 1, reportCellDetailsHeaderRow1.size(), reportCellDetailsHeaderRow1.size()+2);
            mergeCellIndexesList.add(percentageCellIndexes);
            generateStaticHeader(statusDisplayName, "%", reportCellDetailsHeaderRow1, reportCellDetailsHeaderRow2);
            totalColumns += 6;

        }
        reportCellDetails.add(reportCellDetailsHeaderRow1);
        reportCellDetails.add(reportCellDetailsHeaderRow2);

        try{

            Map<AttendanceStatus, Map<Gender, Double>> statusGenderCountMap = new HashMap<>();

            Map<String, Map<AttendanceStatus, Map<Gender, Double>>> standardStatusGenderCountMap = getStandardStatusGenderCountMap(studentAttendanceRecordsList) ;
            LinkedHashMap<String, Standard> filteredStandard = filterStandards(standardList, standardIdList);

            double grandTotalMale = 0.0, grandTotalFemale = 0.0;

            for (Map.Entry<String, Standard> standards : filteredStandard.entrySet()) {

                String standardSectionId = standards.getKey();
                Map<AttendanceStatus, Map<Gender, Double>>studentStats = standardStatusGenderCountMap.get(standardSectionId);
                if(CollectionUtils.isEmpty(studentStats)) {
                    continue;
                }
                String standardNameWithSection = standards.getValue().getDisplayNameWithSection();
                List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

                double totalMaleCount = 0.0, totalFemaleCount = 0.0;

                for (AttendanceStatus status : AttendanceStatus.values()) {
                    totalMaleCount += getGenderCount(status, Gender.MALE, studentStats);
                    totalFemaleCount += getGenderCount(status, Gender.FEMALE, studentStats);
                }

                grandTotalMale += totalMaleCount ;
                grandTotalFemale += totalFemaleCount;

                reportCellDetailsRow.add(new ReportCellDetails(standardNameWithSection, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.removeTrailingZeros(totalMaleCount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.removeTrailingZeros(totalFemaleCount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.removeTrailingZeros(totalMaleCount + totalFemaleCount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                for (AttendanceStatus status : attendanceStatusList) {
                    double maleCount = 0.0, femaleCount = 0.0, malePercentage = 0.0, femalePercentage = 0.0, totalPercentage = 0.0;
                    maleCount = getGenderCount(status, Gender.MALE, studentStats);
                    femaleCount = getGenderCount(status, Gender.FEMALE, studentStats);
                    if (studentStats.containsKey(status)) {
                        if(totalMaleCount > 0) {
                            malePercentage = maleCount / totalMaleCount * 100.0;
                        }
                        if(totalFemaleCount > 0) {
                            femalePercentage = femaleCount / totalFemaleCount * 100.0;
                        }
                        if(totalMaleCount + totalFemaleCount > 0) {
                            totalPercentage = (maleCount + femaleCount) /(totalMaleCount + totalFemaleCount) * 100;
                        }
                        Map<Gender, Double> genderWiseCountMap = statusGenderCountMap.get(status);
                        if (CollectionUtils.isEmpty(genderWiseCountMap)) {
                            genderWiseCountMap = new HashMap<>();
                            statusGenderCountMap.put(status,genderWiseCountMap);
                        }
                        genderWiseCountMap.put(Gender.MALE,genderWiseCountMap.getOrDefault(Gender.MALE, 0.0) + maleCount);
                        genderWiseCountMap.put(Gender.FEMALE,genderWiseCountMap.getOrDefault(Gender.FEMALE, 0.0) + femaleCount);
                    }

                    reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.removeTrailingZeros(maleCount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.removeTrailingZeros(femaleCount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.removeTrailingZeros(maleCount + femaleCount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(malePercentage), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(femalePercentage), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(totalPercentage), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }

                reportCellDetails.add(reportCellDetailsRow);
            }

            double grandTotal = grandTotalMale + grandTotalFemale;

            List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
            reportCellDetailsRow.add(new ReportCellDetails("Grand Total", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.removeTrailingZeros(grandTotalMale), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.removeTrailingZeros(grandTotalFemale), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.removeTrailingZeros(grandTotal), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            for (AttendanceStatus status : attendanceStatusList) {
                Map<Gender, Double> genderWiseCountMap = statusGenderCountMap.get(status);
                double grandTotalMaleCount = 0.0, grandTotalFemaleCount = 0.0, grandToatalMalePercent = 0.0 , grandToatalFemalePercent = 0.0, grandTotalPercentage = 0.0;
                if (!CollectionUtils.isEmpty(genderWiseCountMap)) {
                    grandTotalMaleCount = genderWiseCountMap.get(Gender.MALE);
                    grandTotalFemaleCount = genderWiseCountMap.get(Gender.FEMALE);
                }
                if(grandTotalMale > 0) {
                    grandToatalMalePercent = grandTotalMaleCount /grandTotalMale * 100;
                }
                if(grandTotalFemale > 0) {
                    grandToatalFemalePercent = grandTotalFemaleCount / grandTotalFemale * 100;
                }
                if(grandTotal > 0){
                    grandTotalPercentage = (grandTotalMaleCount + grandTotalFemaleCount) / grandTotal * 100;
                }
                reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.removeTrailingZeros(grandTotalMaleCount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.removeTrailingZeros(grandTotalFemaleCount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.removeTrailingZeros(grandTotalMaleCount + grandTotalFemaleCount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(grandToatalMalePercent), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(grandToatalFemalePercent), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatDouble(grandTotalPercentage), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            }
            reportCellDetails.add(reportCellDetailsRow);

            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            CellIndexes headercellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
            mergeCellIndexesList.add(headercellIndexes);
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(reportName, true, mergeCellIndexesList, reportCellDetails);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);

        }catch (final Exception e) {
            logger.error("Error while generating {} report", reportName, e);
        }
        return null;
    }

    private  Map<String, Map<AttendanceStatus, Map<Gender, Double>>> getStandardStatusGenderCountMap( List<StudentAttendanceRecords> studentAttendanceRecordsList ) {
        Map<String, Map<AttendanceStatus, Map<Gender, Double>>> classSectionStats = new HashMap<>();
        for (StudentAttendanceRecords record : studentAttendanceRecordsList) {

            Gender gender = record.getStudent().getStudentBasicInfo().getGender();
            UUID standardId = record.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
            String standardSectionId = standardId.toString();
            List<StandardSections> standardSectionList = record.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList();
            if(!standardSectionList.isEmpty()){
                Integer sectionId = standardSectionList.get(0).getSectionId();
                standardSectionId += ":" + sectionId;
            }
            if (!classSectionStats.containsKey(standardSectionId)) {
                classSectionStats.put(standardSectionId, new HashMap<>());
            }

            List<AttendanceRecord>attendanceRecordList = record.getAttendanceRecordList();

            for(AttendanceRecord attendanceRecord : attendanceRecordList) {
                AttendanceStatus status = attendanceRecord.getAttendanceStatus();
                Map<Gender, Double> genderMap = classSectionStats.get(standardSectionId).get(status);
                if (CollectionUtils.isEmpty(genderMap)) {
                    genderMap = new HashMap<>();
                    classSectionStats.get(standardSectionId).put(status, genderMap);
                }
                genderMap.put(gender,genderMap.getOrDefault(gender, 0.0) + 1.0);
            }
        }

        return classSectionStats;

    }

    private LinkedHashMap<String, Standard> filterStandards(List<Standard> standardList, List<UUID> standardIdList) {
        LinkedHashMap<String, Standard> standardMapWithKey = new LinkedHashMap<>();
        for (Standard standard : standardList) {
            UUID standardId = standard.getStandardId();
            if (!CollectionUtils.isEmpty(standardIdList) && !standardIdList.contains(standardId)) {
                continue;
            }
            List<StandardSections> standardSectionList = standard.getStandardSectionList();
            if (standardSectionList.isEmpty()) {
                standardMapWithKey.put(standardId.toString(), standard);
            } else {
                for (StandardSections standardSections : standardSectionList) {
                    String standardSectionId = standardId + ":" + standardSections.getSectionId();
                    standardMapWithKey.put(standardSectionId, standard);
                }
            }

        }

        return standardMapWithKey;
    }

    private void generateStaticHeader(String statusDisplayName, String suffix, List<ReportCellDetails> reportCellDetailsHeaderRow1, List<ReportCellDetails> reportCellDetailsHeaderRow2) {
        for (int i = 0; i < 3; i++) {
            reportCellDetailsHeaderRow1.add(new ReportCellDetails(statusDisplayName + suffix, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        }
        reportCellDetailsHeaderRow2.add(new ReportCellDetails("Male" + " " + suffix, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsHeaderRow2.add(new ReportCellDetails("Female" + " " +suffix, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        reportCellDetailsHeaderRow2.add(new ReportCellDetails("Total" + " " + suffix, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
    }

    private double getGenderCount (AttendanceStatus status, Gender gender, Map<AttendanceStatus, Map<Gender, Double>>studentStats ) {
        double genderCount = 0.0;
        if(studentStats != null) {
            if (studentStats.containsKey(status)) {
                Map<Gender, Double> genderStats = studentStats.get(status);
                if (genderStats.containsKey(gender)) {
                    genderCount = genderStats.get(gender);
                }
            }
        }

        return genderCount;
    }

    private ReportDetails generateMonthlyAttendanceSummaryReport(int instituteId, int academicSessionId,
                                                            List<UUID> standardIdList, List<Integer> sectionIdList, List<AttendanceStatus> attendanceStatusList,
                                                            List<Integer> attendanceType, String attendanceMonth, Set<String> requiredHeaderAttributes, AttendanceReportType attendanceReportType) {
        final String reportName = "Monthly Attendance Summary Report";
        Institute institute = instituteManager.getInstitute(instituteId);
        List<Student> studentsList = studentManager.getStudentsInAcademicSession(instituteId, academicSessionId);
        String startMonth = capitalizeFirstOfAllWords(studentsList.get(0).getStudentAcademicSessionInfoResponse().getAcademicSession().getStartMonth().name());
        int startYear = studentsList.get(0).getStudentAcademicSessionInfoResponse().getAcademicSession().getStartYear();
        String startMonth0fYear = startMonth + "-" +startYear;
        Integer start = getEpochTime(startMonth0fYear, false, false);
        Integer end = getEpochTime(attendanceMonth, true, false);
        Integer previousMonthEnd = getEpochTime(attendanceMonth, false, true);
        Integer currentMonthStart = getEpochTime(attendanceMonth, false, false);

        List<AttendanceType> attendanceTypeList = attendanceManager.getInstituteAttendanceTypes(instituteId, academicSessionId, attendanceType);
        Map<UUID, List<StaticHoliday>> staticHolidayMap = holidayCalendarManager.getResolvedHolidayByUserType(instituteId, academicSessionId, UserType.STUDENT);
        if (CollectionUtils.isEmpty(studentsList) || CollectionUtils.isEmpty(attendanceTypeList)) {
            return new ReportDetails(reportName, null);
        }


        Map<Integer, List<StudentAttendanceRecords>> studentAttendanceRecordList =
                attendanceManager.getAttendanceWithSessionDetail(instituteId, academicSessionId, standardIdList,
                        sectionIdList, attendanceStatusList, attendanceType, start, end);

        if (CollectionUtils.isEmpty(studentAttendanceRecordList)) {
            logger.info("Empty report {}", reportName);
            return new ReportDetails(reportName, null);
        }
        Map<UUID, Map<Integer, Map<Integer, AttendanceStatus>>> studentDateAttendanceMap = new HashMap<>();   // map<student_id, map<date, map<Attendancetype, status>>>
        Map<UUID,StudentAttendanceRecords> studentRecords = new HashMap<>();

        getStudentAttendanceSummaryFormattedData(studentAttendanceRecordList, studentDateAttendanceMap, studentRecords);
        Map<UUID, Integer> currentMonthTotalDaysCountMap = new HashMap<>();
        Map<UUID, Integer> previousMonthsTotalDaysCountMap = new HashMap<>();
        Map<UUID, Integer> currentMonthPresentCountMap = new HashMap<>();
        Map<UUID, Integer> previousMonthsPresentCountMap = new HashMap<>();
        Map<UUID, Integer> currentMonthAbsentCountMap = new HashMap<>();
        Map<UUID, Integer> previousMonthsAbsentCountMap = new HashMap<>();
        Map<UUID, Integer> currentMonthHalfDayCountMap = new HashMap<>();
        Map<UUID, Integer> previousMonthsHalfDayCountMap = new HashMap<>();
        Map<UUID, Integer> currentMonthLeaveCountMap = new HashMap<>();
        Map<UUID, Integer> previousMonthsLeaveCountMap = new HashMap<>();
        Map<UUID, Integer> currentMonthHolidayCountMap = new HashMap<>();
        Map<UUID, Integer> previousMonthsHolidayCountMap = new HashMap<>();
        Map<UUID, Integer> currentMonthUnmarkedCountMap = new HashMap<>();
        Map<UUID, Integer> previousMonthsUnmarkedCountMap = new HashMap<>();
        List<Integer> currentMonthdateList = DateUtils.getDateList(DateUtils.getDayStart(currentMonthStart,DateUtils.DEFAULT_TIMEZONE), DateUtils.getDayEnd(end, DateUtils.DEFAULT_TIMEZONE));
        Collections.sort(currentMonthdateList);
        List<Integer> previousMonthsdateList = DateUtils.getDateList(DateUtils.getDayStart(start,DateUtils.DEFAULT_TIMEZONE), DateUtils.getDayEnd(previousMonthEnd, DateUtils.DEFAULT_TIMEZONE));
        Collections.sort(previousMonthsdateList);
        List<StudentAttendanceRecords> studentAttendanceRecordsList = new ArrayList<>(studentRecords.values());
        sortStudentRecord(studentAttendanceRecordsList);
        boolean isFirstAcademicMonth = start > previousMonthEnd;
        getStudentAttendanceMonthlySummaryFormattedData( currentMonthTotalDaysCountMap, previousMonthsTotalDaysCountMap, currentMonthPresentCountMap, previousMonthsPresentCountMap,
                 currentMonthAbsentCountMap, previousMonthsAbsentCountMap, currentMonthHalfDayCountMap, previousMonthsHalfDayCountMap, currentMonthLeaveCountMap, previousMonthsLeaveCountMap,
                currentMonthHolidayCountMap, previousMonthsHolidayCountMap, currentMonthUnmarkedCountMap, previousMonthsUnmarkedCountMap, currentMonthdateList, previousMonthsdateList, staticHolidayMap, studentDateAttendanceMap, attendanceTypeList, studentAttendanceRecordsList);

        String[] heading = attendanceMonth.split("-");


        List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
        List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
        reportHeaderRow.add(new ReportCellDetails("Month : " + heading[0].toUpperCase(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.BOTTOM));
        headerReportCellDetails.add(reportHeaderRow);

        List<ReportCellDetails> reportCellDetailsHeaderRow1 = new ArrayList<ReportCellDetails>();
        List<ReportCellDetails> reportCellDetailsHeaderRow2 = new ArrayList<ReportCellDetails>();
        List<CellIndexes> headerMergeCellIndexesList = new ArrayList<CellIndexes>();
        List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
        List<ReportHeaderAttribute> reportHeaderAttributeList = REPORT_HEADERS_MAP.get(attendanceReportType.name());
        int totalColumns = 0;

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_SR_NO.getKey())) {
            reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.ATTENDANCE_SR_NO.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            CellIndexes cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
            headerMergeCellIndexesList.add(cellIndexes);
            totalColumns++;
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_NAME.getKey())) {
            reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.ATTENDANCE_STUDENT_NAME.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            CellIndexes cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
            headerMergeCellIndexesList.add(cellIndexes);
            totalColumns++;
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_ADMISSION_NO.getKey())) {
            reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.ATTENDANCE_ADMISSION_NO.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            CellIndexes cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
            headerMergeCellIndexesList.add(cellIndexes);
            totalColumns++;
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_CLASS.getKey())) {
            reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.ATTENDANCE_STUDENT_CLASS.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            CellIndexes cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
            headerMergeCellIndexesList.add(cellIndexes);
            totalColumns++;
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_ROLL_NUMBER.getKey())) {
            reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.ATTENDANCE_STUDENT_ROLL_NUMBER.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            CellIndexes cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
            headerMergeCellIndexesList.add(cellIndexes);
            totalColumns++;
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_PRIMARY_CONTACT_NUMBER.getKey())) {
            reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.ATTENDANCE_STUDENT_PRIMARY_CONTACT_NUMBER.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
            CellIndexes cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
            headerMergeCellIndexesList.add(cellIndexes);
            totalColumns++;
        }

        int colNum = totalColumns;


        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.TOTAL_DAYS.getKey())) {
            for (String s : TOTAL_DAYS) {
//                    final String attendanceTypeNameHeader = isSingleAttendanceType ? EMPTY_TEXT : attendanceTypeVal.getName();
                reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.TOTAL_DAYS.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                reportCellDetailsHeaderRow2.add(new ReportCellDetails(s, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                totalColumns++;
                colNum++;
            }

            CellIndexes cellIndexes = new CellIndexes(1, 1, colNum - 3, colNum - 1);
            headerMergeCellIndexesList.add(cellIndexes);
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.PRESENT_COUNT.getKey())) {
            for (String s : PRESENT_COUNT) {
//                    final String attendanceTypeNameHeader = isSingleAttendanceType ? EMPTY_TEXT : attendanceTypeVal.getName();
                reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.PRESENT_COUNT.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                reportCellDetailsHeaderRow2.add(new ReportCellDetails(s, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                totalColumns++;
                colNum++;
            }

            CellIndexes cellIndexes = new CellIndexes(1, 1, colNum - 3, colNum - 1);
            headerMergeCellIndexesList.add(cellIndexes);
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ABSENT_COUNT.getKey())) {
            for (String s : ABSENT_COUNT) {
//                    final String attendanceTypeNameHeader = isSingleAttendanceType ? EMPTY_TEXT : attendanceTypeVal.getName();
                reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.ABSENT_COUNT.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                reportCellDetailsHeaderRow2.add(new ReportCellDetails(s, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                totalColumns++;
                colNum++;
            }

            CellIndexes cellIndexes = new CellIndexes(1, 1, colNum - 3, colNum - 1);
            headerMergeCellIndexesList.add(cellIndexes);
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.LEAVE_COUNT.getKey())) {
            for (String s : LEAVE_COUNT) {
//                    final String attendanceTypeNameHeader = isSingleAttendanceType ? EMPTY_TEXT : attendanceTypeVal.getName();
                reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.LEAVE_COUNT.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                reportCellDetailsHeaderRow2.add(new ReportCellDetails(s, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                totalColumns++;
                colNum++;
            }

            CellIndexes cellIndexes = new CellIndexes(1, 1, colNum - 3, colNum - 1);
            headerMergeCellIndexesList.add(cellIndexes);
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.HALF_DAY_COUNT.getKey())) {
            for (String s : HALF_DAY_COUNT) {
//                    final String attendanceTypeNameHeader = isSingleAttendanceType ? EMPTY_TEXT : attendanceTypeVal.getName();
                reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.HALF_DAY_COUNT.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                reportCellDetailsHeaderRow2.add(new ReportCellDetails(s, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                totalColumns++;
                colNum++;
            }

            CellIndexes cellIndexes = new CellIndexes(1, 1, colNum - 3, colNum - 1);
            headerMergeCellIndexesList.add(cellIndexes);
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.HOLIDAY_COUNT.getKey())) {
            for (String s : HOLIDAY_COUNT) {
//                    final String attendanceTypeNameHeader = isSingleAttendanceType ? EMPTY_TEXT : attendanceTypeVal.getName();
                reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.HOLIDAY_COUNT.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                reportCellDetailsHeaderRow2.add(new ReportCellDetails(s, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                totalColumns++;
                colNum++;
            }

            CellIndexes cellIndexes = new CellIndexes(1, 1, colNum - 3, colNum - 1);
            headerMergeCellIndexesList.add(cellIndexes);
        }

        if (requiredHeaderAttributes.contains(ReportHeaderAttribute.UNMARKED_COUNT.getKey())) {
            for (String s : UNMARKED_COUNT) {
                reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.UNMARKED_COUNT.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                reportCellDetailsHeaderRow2.add(new ReportCellDetails(s, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                totalColumns++;
                colNum++;
            }

            CellIndexes cellIndexes = new CellIndexes(1, 1, colNum - 3, colNum - 1);
            headerMergeCellIndexesList.add(cellIndexes);
        }

        headerReportCellDetails.add(reportCellDetailsHeaderRow1);
        headerReportCellDetails.add(reportCellDetailsHeaderRow2);
        try {
            int count = 1;
            for (StudentAttendanceRecords studentAttendanceDetails : studentAttendanceRecordsList) {
                if (CollectionUtils.isEmpty(studentAttendanceDetails.getAttendanceRecordList())) {
                    continue;
                }
                List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
                Student student = studentAttendanceDetails.getStudent();
                UUID studentId = student.getStudentId();
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_SR_NO.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(String.valueOf(count), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_NAME.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(student.getStudentBasicInfo().getName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_ADMISSION_NO.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(student.getStudentBasicInfo().getAdmissionNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_CLASS.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_ROLL_NUMBER.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(student.getStudentAcademicSessionInfoResponse().getRollNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }

                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ATTENDANCE_STUDENT_PRIMARY_CONTACT_NUMBER.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(student.getStudentBasicInfo().getPrimaryContactNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }

                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.TOTAL_DAYS.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(currentMonthTotalDaysCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(isFirstAcademicMonth ? "-" : previousMonthsTotalDaysCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(currentMonthTotalDaysCountMap.get(studentId) + previousMonthsTotalDaysCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.PRESENT_COUNT.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(currentMonthPresentCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(isFirstAcademicMonth ? "-" : previousMonthsPresentCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(currentMonthPresentCountMap.get(studentId) + previousMonthsPresentCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }

                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ABSENT_COUNT.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(currentMonthAbsentCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(isFirstAcademicMonth ? "-" : previousMonthsAbsentCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(currentMonthAbsentCountMap.get(studentId) + previousMonthsAbsentCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }

                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.LEAVE_COUNT.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(currentMonthLeaveCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(isFirstAcademicMonth ? "-" : previousMonthsLeaveCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(currentMonthLeaveCountMap.get(studentId) + previousMonthsLeaveCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }

                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.HALF_DAY_COUNT.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(currentMonthHalfDayCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(isFirstAcademicMonth ? "-" : previousMonthsHalfDayCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(currentMonthHalfDayCountMap.get(studentId) + previousMonthsHalfDayCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }

                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.HOLIDAY_COUNT.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(currentMonthHolidayCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(isFirstAcademicMonth ? "-" : previousMonthsHolidayCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(currentMonthHolidayCountMap.get(studentId) + previousMonthsHolidayCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }

                if (requiredHeaderAttributes.contains(ReportHeaderAttribute.UNMARKED_COUNT.getKey())) {
                    reportCellDetailsRow.add(new ReportCellDetails(currentMonthUnmarkedCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(isFirstAcademicMonth ? "-" : previousMonthsUnmarkedCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                    reportCellDetailsRow.add(new ReportCellDetails(currentMonthUnmarkedCountMap.get(studentId) + previousMonthsUnmarkedCountMap.get(studentId), INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
                }
                reportCellDetails.add(reportCellDetailsRow);
                count++;
            }

            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();

            CellIndexes headercellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
            headerMergeCellIndexesList.add(headercellIndexes);

            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(reportName, false,totalColumns, headerMergeCellIndexesList,
                    mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true, true, institute,
                    totalColumns);
            reportSheetDetailsList.add(reportSheetDetails);
            return new ReportDetails(reportName, reportSheetDetailsList);
        } catch (final Exception e) {
            logger.error("Error while generating {} report", reportName, e);
        }
        return null;

    }

    private static Integer getEpochTime(String monthOfYear, boolean isLastDay, boolean isPreviousMonth) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMMM-yyyy", Locale.ENGLISH);
        YearMonth yearMonth = YearMonth.parse(monthOfYear, formatter);
        LocalDate date = isLastDay
                ? yearMonth.atEndOfMonth()
                : yearMonth.atDay(1);

        if (isPreviousMonth) {
            date = yearMonth.minusMonths(1).atEndOfMonth();
        }
        return (int) date.atStartOfDay(ZoneId.systemDefault()).toEpochSecond();
    }

    private void getStudentAttendanceMonthlySummaryFormattedData(Map<UUID, Integer> presentMonthTotalDaysCount, Map<UUID, Integer> previousMonthsTotalDaysCount,
                                                                 Map<UUID, Integer> presentMonthPresentCount, Map<UUID, Integer> previousMonthsPresentCount, Map<UUID, Integer> presentMonthAbsentCount, Map<UUID, Integer> previousMonthsAbsentCount,
                                                                 Map<UUID, Integer> presentMonthHalfDayCount, Map<UUID, Integer> previousMonthsHalfDayCount, Map<UUID, Integer> presentMonthLeaveCount,
                                                                 Map<UUID, Integer> previousMonthsLeaveCount, Map<UUID, Integer> presentMonthHolidayCount, Map<UUID, Integer> previousMonthsHolidayCount,
                                                                 Map<UUID, Integer> presentMonthUnmarkedCount, Map<UUID, Integer> previousMonthsUnmarkedCount, List<Integer> currentMonthdateList, List<Integer> previousMonthsdateList, Map<UUID, List<StaticHoliday>> staticHolidayMap,
                                                                 Map<UUID, Map<Integer, Map<Integer, AttendanceStatus>>> studentDateAttendanceMap, List<AttendanceType> attendanceTypeList, List<StudentAttendanceRecords> studentAttendanceRecordsList) {
        getStudentAttendanceMonthlySummaryFormattedData(presentMonthTotalDaysCount, presentMonthPresentCount,
                presentMonthAbsentCount, presentMonthHalfDayCount, presentMonthLeaveCount,
                presentMonthHolidayCount, presentMonthUnmarkedCount, currentMonthdateList, staticHolidayMap, studentDateAttendanceMap, attendanceTypeList, studentAttendanceRecordsList);
        getStudentAttendanceMonthlySummaryFormattedData(previousMonthsTotalDaysCount, previousMonthsPresentCount,
                previousMonthsAbsentCount, previousMonthsHalfDayCount, previousMonthsLeaveCount,
                previousMonthsHolidayCount, previousMonthsUnmarkedCount, previousMonthsdateList, staticHolidayMap, studentDateAttendanceMap, attendanceTypeList, studentAttendanceRecordsList);


    }
    private void getStudentAttendanceMonthlySummaryFormattedData(Map<UUID, Integer> totalDaysCountMap,
                                                                 Map<UUID, Integer> presentCountMap,  Map<UUID, Integer> absentCountMap,
                                                                 Map<UUID, Integer> halfDayCountMap, Map<UUID, Integer> leaveCountMap,
                                                                  Map<UUID, Integer> holidayCountMap,
                                                                 Map<UUID, Integer> unmarkedCountMap,  List<Integer> dateList,  Map<UUID, List<StaticHoliday>> staticHolidayMap,
                                                                 Map<UUID, Map<Integer, Map<Integer, AttendanceStatus>>> studentDateAttendanceMap,  List<AttendanceType> attendanceTypeList, List<StudentAttendanceRecords> studentAttendanceRecordsList){
        for (StudentAttendanceRecords studentAttendanceDetails : studentAttendanceRecordsList) {
            if (CollectionUtils.isEmpty(studentAttendanceDetails.getAttendanceRecordList())) {
                continue;
            }
            Student student = studentAttendanceDetails.getStudent();
            UUID studentId = student.getStudentId();
            Map<Integer, Map<Integer, AttendanceStatus>> dateAttendanceMap = studentDateAttendanceMap.get(studentId);
            int totalWorkingDays = 0;
            int presentCount = 0;
            int absentCount = 0;
            int leaveCount = 0;
            int holidayCount = 0;
            int halfDayCount = 0;
            for (Integer date : dateList) {
                if (!dateAttendanceMap.containsKey(date)) {
                    for (final AttendanceType attendanceTypeVal : attendanceTypeList) {
                        if(DateUtils.isSunday(date)) {
                            holidayCount++;
                            continue;
                        }
                        if(isHoliday(studentId, date, staticHolidayMap)) {
                            holidayCount++;
                            continue;
                        }
                        totalWorkingDays++;
                    }
                    continue;
                }
                Map<Integer, AttendanceStatus> attendanceTypeStatusMap = null;
                if (!CollectionUtils.isEmpty(dateAttendanceMap)) {
                    attendanceTypeStatusMap = dateAttendanceMap.get(date);
                }
                AttendanceStatus studentAttendanceStatus = null;
                for (final AttendanceType attendanceTypeVal : attendanceTypeList) {
                    int attendanceTypeId = attendanceTypeVal.getAttendanceTypeId();
                    if (attendanceTypeStatusMap != null) {
                        studentAttendanceStatus = attendanceTypeStatusMap.get(attendanceTypeId);
                        if(studentAttendanceStatus == AttendanceStatus.PRESENT) {
                            presentCount++;
                        }

                        if(studentAttendanceStatus == AttendanceStatus.ABSENT) {
                            absentCount++;
                        }

                        if(studentAttendanceStatus == AttendanceStatus.HALF_DAY) {
                            halfDayCount++;
                        }

                        if(studentAttendanceStatus == AttendanceStatus.LEAVE) {
                            leaveCount++;
                        }

                        totalWorkingDays++;


                    }
                }

            }
            Integer unmarkedCount = totalWorkingDays - (presentCount + absentCount + halfDayCount + leaveCount );
            totalDaysCountMap.put(studentId, totalWorkingDays);
            presentCountMap.put(studentId, presentCount);
            absentCountMap.put(studentId, absentCount);
            halfDayCountMap.put(studentId, halfDayCount);
            leaveCountMap.put(studentId, leaveCount);
            holidayCountMap.put(studentId, holidayCount);
            unmarkedCountMap.put(studentId, unmarkedCount);
        }

    }
}