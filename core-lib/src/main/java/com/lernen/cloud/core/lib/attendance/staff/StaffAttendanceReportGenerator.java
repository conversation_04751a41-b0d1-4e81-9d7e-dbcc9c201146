/**
 *
 */
package com.lernen.cloud.core.lib.attendance.staff;

import com.embrate.cloud.core.api.calendar.holiday.Holiday;
import com.embrate.cloud.core.api.calendar.holiday.StaticHoliday;
import com.embrate.cloud.core.lib.calendar.holiday.HolidayCalendarManager;
import com.itextpdf.kernel.geom.PageSize;
import com.lernen.cloud.core.api.attendance.AttendanceReportType;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.attendance.AttendanceType;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceReportType;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceStatus;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceTimeDetails;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceType;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendanceDaySummary;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendanceRegisterData;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffTimeDuration;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Time;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.report.*;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.reports.ReportGenerator;
import com.lernen.cloud.core.lib.reports.utils.*;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.*;

import static com.lernen.cloud.core.api.attendance.staff.StaffAttendanceReportType.*;

/**
 * <AUTHOR>
 *
 */
public class StaffAttendanceReportGenerator extends ReportGenerator {

	private static final Logger logger = LogManager.getLogger(StaffAttendanceReportGenerator.class);

	private static final int COLUMN_WIDTH_PADDING = 4;

	private static final String[] STAFF_ATTENDANCE_DETAILS_IN_A_DAY_REPORT = { "Sr No.", "Date", "Staff Id", "Name",
			"Contact Number", "Total Duration", "Status", "In Time", "Out Time" };

	private static final String[] STAFF_ATTENDANCE_SUMMARY_REPORT = { "Sr No.","Staff Id", "Name"};

	private static final DecimalFormat df = new DecimalFormat("0.00");

	private static final String NA = "NA";

	private final StaffAttendanceManager staffAttendanceManager;

	private final StaffManager staffManager;

	private  final UserPermissionManager userPermissionManager;
	private  final HolidayCalendarManager holidayCalendarManager;
	private  final InstituteManager instituteManager;

	public StaffAttendanceReportGenerator(StaffAttendanceManager staffAttendanceManager,
										  StaffManager staffManager, UserPermissionManager userPermissionManager,
										  HolidayCalendarManager holidayCalendarManager, InstituteManager instituteManager) {
		this.staffAttendanceManager = staffAttendanceManager;
		this.staffManager = staffManager;
		this.userPermissionManager = userPermissionManager;
		this.holidayCalendarManager = holidayCalendarManager;
		this.instituteManager = instituteManager;
	}

	public ReportDetails generateReport(int instituteId, String reportTypeStr, String attendanceStatusStr, String staffCategoriesStr,
										String staffStatusStr, int start, int end, UUID userId, DownloadFormat downloadFormat) {

		if (instituteId <= 0 || userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid Request"));
		}

		if (downloadFormat == DownloadFormat.EXCEL) {
			userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.GENERATE_STAFF_ATTENDANCE_EXCEL_REPORT);

		} else if (downloadFormat == DownloadFormat.PDF) {
			userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.GENERATE_STAFF_ATTENDANCE_PDF_REPORT);
		}


		if ((start <= 0 || end <= 0)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid Request"));
		}

		StaffAttendanceReportType staffAttendanceReportType = StaffAttendanceReportType.getStaffAttendanceReportType(reportTypeStr);

		if (staffAttendanceReportType == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid Request"));
		}

		List<StaffAttendanceStatus> staffAttendanceStatusList = convertStaffAttendanceStatusStr(attendanceStatusStr);
		Set<UUID> staffCategories = convertStrToUUIDSet(staffCategoriesStr);
		Set<StaffStatus> staffStatusSet = getStaffStatuses(staffStatusStr);

		switch (staffAttendanceReportType) {
			case STAFF_ATTENDANCE_DETAILS_IN_A_DAY:
				userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.GENERATE_STAFF_ATTENDANCE_DETAILS_IN_A_DAY_REPORT);
				return generateAttendanceDetailsInADay(instituteId, staffAttendanceStatusList, start, end, staffStatusSet);
			case STAFF_MONTHLY_ATTENDANCE_SUMMARY:
				userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.GENERATE_STAFF_MONTHLY_ATTENDANCE_SUMMARY_REPORT);
				return generateStaffAttendanceSummary(instituteId, start, end, staffCategories, staffStatusSet);
			default:
				break;
		}
		return null;
	}
	private ReportDetails generateAttendanceDetailsInADay(int instituteId, List<StaffAttendanceStatus> attendanceStatusList, Integer start, Integer end,
														  Set<StaffStatus> staffStatusSet) {

		final ReportWorkbook workbook = new ReportWorkbook();
		final List<Integer> columnWidths = new ArrayList<>();

		final ReportFont headerFont = workbook.createFont();
		headerFont.setBold(true);
		headerFont.setFontHeightInPoints((short) 14);
		headerFont.setColor(BLACK_COLOR);

		// Create a CellStyle with the font
		final ReportCellStyle headerCellStyle = workbook.createCellStyle();
		headerCellStyle.setFont(headerFont);


		// Create a Sheet
		final ReportSheet sheet = workbook.createSheet("Report");

		final String reportName = "Datewise Staff Attendance Details";

		final ReportFont titleFont = workbook.createFont();
		titleFont.setBold(true);
		titleFont.setFontHeightInPoints((short) 14);
		titleFont.setColor(BLACK_COLOR);

		final ReportCellStyle titleCellStyle = workbook.createCellStyle();
		titleCellStyle.setFont(titleFont);
		titleCellStyle.setAlignment(HorizontalAlignment.LEFT);

		// Create a Row
		ReportRow headerRow = sheet.createRow(0);

		final ReportCell secondRowCell = headerRow.createCell(0);
		secondRowCell.setCellValue(reportName);
		secondRowCell.setCellStyle(titleCellStyle);
		// Create cells

		headerRow = sheet.createRow(1);
		int i = 0;
		for (i = 0; i < STAFF_ATTENDANCE_DETAILS_IN_A_DAY_REPORT.length; i++) {
			final ReportCell cell = headerRow.createCell(i);
			cell.setCellValue(STAFF_ATTENDANCE_DETAILS_IN_A_DAY_REPORT[i]);
			cell.setCellStyle(headerCellStyle);
			columnWidths.add(STAFF_ATTENDANCE_DETAILS_IN_A_DAY_REPORT[i].length());
		}

		int totalColumns = STAFF_ATTENDANCE_DETAILS_IN_A_DAY_REPORT.length;

		CellIndexes cellRangeAddress = new CellIndexes(0, 0, 0, totalColumns - 1);
		sheet.addMergedRegion(cellRangeAddress);

		ReportCellStyle presentStyle = workbook.createCellStyle();

		ReportCellStyle leaveStyle = workbook.createCellStyle();
		leaveStyle.setCellColor(RED_COLOR);

		ReportCellStyle halfDayStyle = workbook.createCellStyle();
		halfDayStyle.setCellColor(YELLOW_COLOR);

		ReportCellStyle holidayStyle = workbook.createCellStyle();
		holidayStyle.setCellColor(BLUE_COLOR);

		ReportCellStyle lateEntryEarlyExitStyle = workbook.createCellStyle();
		lateEntryEarlyExitStyle.setCellColor(ORANGE_COLOR);

		try {

			List<StaffAttendanceRegisterData> staffAttendanceRegisterDataList = staffAttendanceManager.getStaffAttendanceRegisterForRangeWithStaffStatuses(instituteId, start, end, staffStatusSet);

			if (CollectionUtils.isEmpty(staffAttendanceRegisterDataList)) {
				logger.info("Empty report {}", reportName);
				return getReportOutput(workbook, reportName);
			}

			Map<UUID, Staff> staffMap = getStaffMap(staffAttendanceRegisterDataList);

			Map<Integer, List<StaffAttendanceRegisterData>> staffRegisterDayMap = getStaffRegisterDayMap(staffAttendanceRegisterDataList);

			/**
			 * adding staff details in report whose staff timing
			 * details are not present in DB
			 * because they were absent for that day
			 */
			staffRegisterDayMap = addAllStaffDetailsForEachDay(staffMap, staffRegisterDayMap);

			TreeMap<Integer, List<StaffAttendanceRegisterData>> staffAttendanceRecordsSorted = new TreeMap<>();
			staffAttendanceRecordsSorted.putAll(staffRegisterDayMap);

			for(Map.Entry<Integer, List<StaffAttendanceRegisterData>> staffAttendanceRecords : staffAttendanceRecordsSorted.entrySet()) {
				Collections.sort(staffAttendanceRecords.getValue(), new Comparator<StaffAttendanceRegisterData>() {
					@Override
					public int compare(StaffAttendanceRegisterData s1, StaffAttendanceRegisterData s2) {
						return s1.getStaff().getStaffBasicInfo().getName()
								.compareToIgnoreCase(s2.getStaff().getStaffBasicInfo().getName());
					}
				});
			}

			if (CollectionUtils.isEmpty(staffAttendanceRecordsSorted)) {
				logger.info("Empty report {}", reportName);
				return getReportOutput(workbook, reportName);
			}

			int rowNum = 2;
			int count = 1;
			List<AcademicSession> academicSessionList = instituteManager.getAcademicSessionList(instituteId);
			int startAcademicSessionId = 0;
			int endAcademicSessionId = 0;
			for (AcademicSession academicSession : academicSessionList) {
				/**
				 * assuming a single date will only lie in a single session
				 */
				if (DateUtils.dateLieBetweenTwoDates(academicSession.getSessionStartTime(), academicSession.getSessionEndTime(), start)) {
					startAcademicSessionId = academicSession.getAcademicSessionId();
				}
				if (DateUtils.dateLieBetweenTwoDates(academicSession.getSessionStartTime(), academicSession.getSessionEndTime(), end)) {
					endAcademicSessionId = academicSession.getAcademicSessionId();
				}
			}

			Map<UUID, List<StaticHoliday>> staticHolidayMap = holidayCalendarManager.getResolvedHolidayByUserType(instituteId, startAcademicSessionId, UserType.STAFF);
			if (startAcademicSessionId != endAcademicSessionId) {
				Map<UUID, List<StaticHoliday>> endSessionStaticHolidayMap = holidayCalendarManager.getResolvedHolidayByUserType(instituteId, endAcademicSessionId, UserType.STAFF);
				mergeTwoHolidayMaps(staticHolidayMap, endSessionStaticHolidayMap);
			}

			for (final Map.Entry<Integer, List<StaffAttendanceRegisterData>> staffAttendanceDetailsEntry : staffAttendanceRecordsSorted.entrySet()) {

				for(StaffAttendanceRegisterData staffAttendanceRegisterData : staffAttendanceDetailsEntry.getValue()) {
					if(CollectionUtils.isEmpty(staffAttendanceRegisterData.getStaffAttendanceDaySummaryList())){
						continue;
					}

					StaffStatus staffStatus = staffAttendanceRegisterData.getStaff().getStaffStatus();
					/**
					 * filtering staff on the basics of status
					 */
					if(!CollectionUtils.isEmpty(staffStatusSet) && !staffStatusSet.contains(staffStatus)) {
						continue;
					}

					Time inTime = staffAttendanceRegisterData.getStaff() == null ? null : staffAttendanceRegisterData.getStaff().getStaffTimingDetails() == null ? null : staffAttendanceRegisterData.getStaff().getStaffTimingDetails().getInTime();
					Time outTime = staffAttendanceRegisterData.getStaff() == null ? null : staffAttendanceRegisterData.getStaff().getStaffTimingDetails() == null ? null : staffAttendanceRegisterData.getStaff().getStaffTimingDetails().getOutTime();
					StaffAttendanceDaySummary staffAttendanceDaySummary = staffAttendanceRegisterData.getStaffAttendanceDaySummaryList().get(0);

					/**
					 * removing staff with status whose filter is not there
					 */
					if (!CollectionUtils.isEmpty(attendanceStatusList) &&
							!attendanceStatusList.contains(staffAttendanceDaySummary.getStaffAttendanceStatus())) {
						continue;
					}

					ReportRow row = sheet.createRow(rowNum++);
					int colNum = 0;

					createCell(row, colNum++, String.valueOf(count), columnWidths, presentStyle);

					createCell(row, colNum++, DateUtils.getFormattedDate(staffAttendanceDetailsEntry.getKey(), DateUtils.DEFAULT_DATE_FORMAT, User.DFAULT_TIMEZONE),
							columnWidths, presentStyle);

					createCell(row, colNum++, staffAttendanceRegisterData.getStaff().getStaffBasicInfo().getStaffInstituteId(),
							columnWidths, presentStyle);

					createCell(row, colNum++, staffAttendanceRegisterData.getStaff().getStaffBasicInfo().getName(),
							columnWidths, presentStyle);

					createCell(row, colNum++, staffAttendanceRegisterData.getStaff().getStaffBasicInfo().getPrimaryContactNumber() == null ?
							NA : staffAttendanceRegisterData.getStaff().getStaffBasicInfo().getPrimaryContactNumber(), columnWidths, presentStyle);

					createCell(row, colNum++, staffAttendanceDaySummary.getTotalDurationInSec() == null ? NA :
									DateUtils.getDuration(staffAttendanceDaySummary.getTotalDurationInSec(), false),
							columnWidths, presentStyle);

					if (staffAttendanceDaySummary.getStaffAttendanceStatus() == StaffAttendanceStatus.PRESENT) {
						createCell(row, colNum++, staffAttendanceDaySummary.getStaffAttendanceStatus() == null ?
								NA : "Present", columnWidths, presentStyle);
					} else if (staffAttendanceDaySummary.getStaffAttendanceStatus() == StaffAttendanceStatus.HALF_DAY) {
						createCell(row, colNum++, staffAttendanceDaySummary.getStaffAttendanceStatus() == null ?
								NA : "Half-Day", columnWidths, halfDayStyle);
					} else if (isHoliday(staffAttendanceRegisterData.getStaff().getStaffId(), staffAttendanceDaySummary.getAttendanceDate(), staticHolidayMap)) {
						createCell(row, colNum++, "Holiday", columnWidths, holidayStyle);
						createCell(row, colNum++, EMPTY_TEXT, columnWidths, presentStyle);
						createCell(row, colNum++, EMPTY_TEXT, columnWidths, presentStyle);
						count++;
						continue;
					} else if (staffAttendanceDaySummary.getStaffAttendanceStatus() == StaffAttendanceStatus.LEAVE) {
						createCell(row, colNum++, staffAttendanceDaySummary.getStaffAttendanceStatus() == null ?
								NA : "Leave", columnWidths, leaveStyle);
					} else {
						createCell(row, colNum++, NA, columnWidths, presentStyle);
					}

					List<StaffTimeDuration> staffTimeDurationList = staffAttendanceDaySummary.getStaffAttendanceDayMetadata() == null ? null : staffAttendanceDaySummary.getStaffAttendanceDayMetadata().getStaffTimeDurationList();

					int index = 0;
					int rowStartIndex = row.getRowNum();

					if(!CollectionUtils.isEmpty(staffTimeDurationList)) {
						for (StaffTimeDuration staffTimeDuration : staffTimeDurationList) {
							if (staffTimeDuration == null) {
								continue;
							}
							if (index != 0) {
								row = sheet.createRow(rowNum++);
								for (int cellI = 0; cellI < colNum; cellI++) {
									createCell(row, cellI, "", columnWidths, presentStyle);
								}
							}

							Time attendanceInTime = staffTimeDuration.getInTime();
							Time attendanceOutTime = staffTimeDuration.getOutTime();
							createCell(row, colNum++, attendanceInTime == null ? NA :
									String.valueOf(attendanceInTime.display(false)), columnWidths, attendanceInTime != null && inTime != null && attendanceInTime.isAfter(inTime) ? lateEntryEarlyExitStyle : presentStyle);
							createCell(row, colNum++, attendanceOutTime == null ? NA :
									String.valueOf(attendanceOutTime.display(false)), columnWidths, attendanceOutTime != null && outTime != null && outTime.isAfter(attendanceOutTime) ? lateEntryEarlyExitStyle : presentStyle);
							colNum = colNum - 2;
							index++;
						}
					} else {
						createCell(row, colNum++, NA, columnWidths, presentStyle);
						createCell(row, colNum++, NA, columnWidths, presentStyle);
					}

					int rowEndIndex = row.getRowNum();

					if(rowStartIndex != rowEndIndex) {
						for (int merge = 0; merge <= 6; merge++) {
							cellRangeAddress = new CellIndexes(rowStartIndex, rowEndIndex, merge, merge);
//							sheet.addMergedRegion(cellRangeAddress);
						}
					}
					count++;

				}
			}
			resizeSheet(sheet, columnWidths);
			int headerRowCount = 2;
			Institute institute = instituteManager.getInstitute(instituteId);
//			return getReportOutput(workbook, reportName, null, totalColumns, headerRowCount, institute);
			return getReportOutput(workbook, reportName, null, totalColumns, headerRowCount, institute,true, false,
					false);


		} catch (final Exception e) {
			logger.error("Error while generating {} report", reportName, e);
		}

		return null;
	}

	private ReportDetails generateStaffAttendanceSummary(int instituteId, Integer start, Integer end, Set<UUID> staffCategories,
														 Set<StaffStatus> staffStatusSet) {

		final ReportWorkbook workbook = new ReportWorkbook();
		final List<Integer> columnWidths = new ArrayList<>();

		final ReportFont headerFont = workbook.createFont();
		headerFont.setBold(true);
		headerFont.setFontHeightInPoints((short) 14);
		headerFont.setColor(BLACK_COLOR);

		// Create a CellStyle with the font
		final ReportCellStyle headerCellStyle = workbook.createCellStyle();
		headerCellStyle.setFont(headerFont);


		// Create a Sheet
		final ReportSheet sheet = workbook.createSheet("Report");

		final String reportName = "Monthly Summary Report";


		// Create a Row
		ReportRow headerRow = sheet.createRow(0);
		final ReportCell dateRangeCell = headerRow.createCell(0);
		String dateRange = reportName + " from " + DateUtils.getFormattedDate(start) + " to " + DateUtils.getFormattedDate(end);
		dateRangeCell.setCellValue(dateRange);
		dateRangeCell.setCellStyle(headerCellStyle);
		columnWidths.add(dateRange.length());


		headerRow = sheet.createRow(1);
		// Create cells
		for (int i = 0; i < STAFF_ATTENDANCE_SUMMARY_REPORT.length; i++) {
			final ReportCell cell = headerRow.createCell(i);

			cell.setCellValue(STAFF_ATTENDANCE_SUMMARY_REPORT[i]);
			cell.setCellStyle(headerCellStyle);
			columnWidths.add(STAFF_ATTENDANCE_SUMMARY_REPORT[i].length());
		}

		ReportCellStyle presentStyle = workbook.createCellStyle();

		ReportCellStyle leaveStyle = workbook.createCellStyle();
		leaveStyle.setCellColor(RED_COLOR);

		ReportCellStyle halfDayStyle = workbook.createCellStyle();
		halfDayStyle.setCellColor(YELLOW_COLOR);

		try{

			List<StaffAttendanceRegisterData> staffAttendanceRegisterDataList =
					staffAttendanceManager.getStaffAttendanceRegisterForRange(instituteId, start, end);

			List<Staff> staffList = staffManager.getStaffDetailsByCategories(instituteId,staffCategories);

			if (CollectionUtils.isEmpty(staffAttendanceRegisterDataList) || CollectionUtils.isEmpty(staffList)) {
				logger.info("Empty report {}", reportName);
				return getReportOutput(workbook, reportName);
			}

			Collections.sort(staffList, new Comparator<Staff>() {
				@Override
				public int compare(Staff s1, Staff s2) {
					return s1.getStaffBasicInfo().getName().compareToIgnoreCase(s2.getStaffBasicInfo().getName());
				}
			});

			Map<UUID, Map<Integer, StaffAttendanceStatus>> staffDateAttendanceMap = new HashMap<>();

			getStaffAttendanceSummaryFormattedData(staffAttendanceRegisterDataList, staffDateAttendanceMap);


			//TODO:sort staff map and date set
			List<Integer> dateList = DateUtils.getDateList(DateUtils.getDayStart(start,DateUtils.DEFAULT_TIMEZONE), DateUtils.getDayEnd(end, DateUtils.DEFAULT_TIMEZONE));
			Collections.sort(dateList);

			List<AcademicSession> academicSessionList = instituteManager.getAcademicSessionList(instituteId);
			int startAcademicSessionId = 0;
			int endAcademicSessionId = 0;
			for(AcademicSession academicSession : academicSessionList) {
				/**
				 * assuming a single date will only lie in a single session
				 */
				if(DateUtils.dateLieBetweenTwoDates(academicSession.getPayrollSessionStartTime(), academicSession.getPayrollSessionEndTime(), start)) {
					startAcademicSessionId = academicSession.getAcademicSessionId();
				}
				if(DateUtils.dateLieBetweenTwoDates(academicSession.getPayrollSessionStartTime(), academicSession.getPayrollSessionEndTime(), end)) {
					endAcademicSessionId = academicSession.getAcademicSessionId();
				}
			}

			Map<UUID, List<StaticHoliday>> staticHolidayMap = holidayCalendarManager.getResolvedHolidayByUserType(instituteId, startAcademicSessionId, UserType.STAFF);
			if(startAcademicSessionId != endAcademicSessionId) {
				Map<UUID, List<StaticHoliday>> endSessionStaticHolidayMap = holidayCalendarManager.getResolvedHolidayByUserType(instituteId, endAcademicSessionId, UserType.STAFF);
				mergeTwoHolidayMaps(staticHolidayMap, endSessionStaticHolidayMap);
			}

			if (CollectionUtils.isEmpty(staffDateAttendanceMap) && CollectionUtils.isEmpty(staticHolidayMap)) {
				logger.info("Empty report {}", reportName);
				return getReportOutput(workbook, reportName);
			}

			int rowNum = 2;
			int totalColumns = STAFF_ATTENDANCE_SUMMARY_REPORT.length;
			for (final Integer date : dateList) {
				final ReportCell cell = headerRow.createCell(totalColumns);
				String dateString = DateUtils.getFormattedDate(date, DateUtils.ONLY_DATE_FORMAT, User.DFAULT_TIMEZONE);
				cell.setCellValue(dateString);
				cell.setCellStyle(headerCellStyle);
				columnWidths.add(dateString.length());
				totalColumns++;
			}

			int count = 1;
			for (final Staff staff : staffList) {
				UUID staffId = staff.getStaffId();
				StaffStatus staffStatus = staff.getStaffStatus();

				/**
				 * filtering staff on the basics of status
				 */
				if(!CollectionUtils.isEmpty(staffStatusSet) && !staffStatusSet.contains(staffStatus)) {
					continue;
				}

				ReportRow row = sheet.createRow(rowNum++);
				int colNum = 0;
				createCell(row, colNum++, String.valueOf(count), columnWidths, presentStyle);
				createCell(row, colNum++, staff.getStaffBasicInfo().getStaffInstituteId(), columnWidths, presentStyle);
				createCell(row, colNum++, staff.getStaffBasicInfo().getName(), columnWidths, presentStyle);

				Map<Integer, StaffAttendanceStatus> dateAttendanceMap = staffDateAttendanceMap.get(staffId);

				/**
				 * marking leave for staff if there is no data for them at a particular date
				 * similar to STAFF_ATTENDANCE_DETAILS_IN_A_DAY report
				 */
				for(Integer date : dateList) {
					ReportCellStyle cellStyle = presentStyle;
					if (CollectionUtils.isEmpty(dateAttendanceMap) || !dateAttendanceMap.containsKey(date)) {
						if(DateUtils.isSunday(date)) {
							createCell(row, colNum++, "Sun", columnWidths, cellStyle);
							continue;
						}
						if(isHoliday(staff.getStaffId(), date, staticHolidayMap)) {
							createCell(row, colNum++, "H", columnWidths, cellStyle);
							continue;
						}
						createCell(row, colNum++, StaffAttendanceStatus.LEAVE.getAbbreviation(), columnWidths, leaveStyle);
						continue;
					}
					StaffAttendanceStatus staffAttendanceStatus = dateAttendanceMap.get(date);
					String value = "";
					if(staffAttendanceStatus != null) {
						switch (staffAttendanceStatus) {
							case PRESENT:
								cellStyle = presentStyle;
								break;
							case LEAVE:
								cellStyle = leaveStyle;
								break;
							case HALF_DAY:
								cellStyle = halfDayStyle;
								break;
						}
						value = staffAttendanceStatus.getAbbreviation();
					}
					createCell(row, colNum++, value, columnWidths, cellStyle);
				}
				count++;
			}

			final CellIndexes cellRangeAddress = new CellIndexes(0, 0, 0, totalColumns - 1);
			sheet.addMergedRegion(cellRangeAddress);

			resizeSheet(sheet, columnWidths);
			int headerRowCount = 2;
			Institute institute = instituteManager.getInstitute(instituteId);
			return getReportOutput(workbook, reportName, null, totalColumns, headerRowCount, institute);

		} catch (final Exception e) {
			logger.error("Error while generating {} report", reportName, e);
		}

		return null;
	}

	private void mergeTwoHolidayMaps(Map<UUID, List<StaticHoliday>> staticHolidayMap,
									 Map<UUID, List<StaticHoliday>> endSessionStaticHolidayMap) {
		if(CollectionUtils.isEmpty(staticHolidayMap)) {
			staticHolidayMap = new HashMap<>();
		}
		for(Map.Entry<UUID, List<StaticHoliday>> staticHoliday : endSessionStaticHolidayMap.entrySet()) {
			UUID staffId = staticHoliday.getKey();
			if(!staticHolidayMap.containsKey(staffId)) {
				staticHolidayMap.put(staffId, staticHoliday.getValue());
				continue;
			}
			staticHolidayMap.get(staffId).addAll(staticHoliday.getValue());
		}
	}

	public boolean isHoliday(UUID staffId, Integer date, Map<UUID, List<StaticHoliday>> staticHolidayMap) {
		if(CollectionUtils.isEmpty(staticHolidayMap) || !staticHolidayMap.containsKey(staffId)) {
			return false;
		}
		List<StaticHoliday> holidays = staticHolidayMap.get(staffId);
		if(CollectionUtils.isEmpty(holidays)) {
			return false;
		}
		for(Holiday holiday : holidays) {
			int start = holiday.getStart();
			int end  = holiday.getEnd();
			if(date >= start && date < end) {
				return true;
			}
		}
		return false;
	}
	private void getStaffAttendanceSummaryFormattedData(List<StaffAttendanceRegisterData> staffAttendanceRegisterDataList,
														Map<UUID, Map<Integer, StaffAttendanceStatus>> staffDateAttendanceMap) {
		for(StaffAttendanceRegisterData staffAttendanceRegisterData : staffAttendanceRegisterDataList) {
			UUID staffId = staffAttendanceRegisterData.getStaff().getStaffId();
			List<StaffAttendanceDaySummary> staffAttendanceDaySummaryList = staffAttendanceRegisterData.getStaffAttendanceDaySummaryList();
			for(StaffAttendanceDaySummary staffAttendanceDaySummary : staffAttendanceDaySummaryList) {
				Integer attendanceDate = staffAttendanceDaySummary.getAttendanceDate();
				StaffAttendanceStatus staffAttendanceStatus = staffAttendanceDaySummary.getStaffAttendanceStatus();
				if(!staffDateAttendanceMap.containsKey(staffId)) {
					staffDateAttendanceMap.put(staffId, new HashMap<>());
				}
				staffDateAttendanceMap.get(staffId).put(attendanceDate, staffAttendanceStatus);
			}
		}
	}

	private Map<Integer, List<StaffAttendanceRegisterData>> addAllStaffDetailsForEachDay(Map<UUID, Staff> staffMap,
																						 Map<Integer, List<StaffAttendanceRegisterData>> staffAttendanceDetailsMap) {

		if(CollectionUtils.isEmpty(staffMap) || CollectionUtils.isEmpty(staffAttendanceDetailsMap)) {
			return new HashMap<>();
		}

		Map<Integer, List<StaffAttendanceRegisterData>> finalStaffAttendanceDetailsMap = new HashMap<>();
		for(Map.Entry<Integer, List<StaffAttendanceRegisterData>> entry : staffAttendanceDetailsMap.entrySet()) {
			if(entry == null || entry.getKey() == null || CollectionUtils.isEmpty(entry.getValue())) {
				continue;
			}

			Map<UUID, StaffAttendanceRegisterData> staffAttendanceDetailsUUIDMap = getStaffAttendanceDetailsUUIDMap(entry.getValue());
			for(Map.Entry<UUID, Staff> entry2 : staffMap.entrySet()) {
				if(!staffAttendanceDetailsUUIDMap.containsKey(entry2.getKey())) {
					Staff staff = entry2.getValue();
					StaffAttendanceRegisterData staffAttendanceDetails = new StaffAttendanceRegisterData(staff, Arrays.asList(new StaffAttendanceDaySummary(entry.getKey(), null, StaffAttendanceStatus.LEAVE, null, null)));
					staffAttendanceDetailsUUIDMap.put(entry2.getKey(), staffAttendanceDetails);
				}
			}
			finalStaffAttendanceDetailsMap.put(entry.getKey(), new ArrayList<>(staffAttendanceDetailsUUIDMap.values()));
		}

		return finalStaffAttendanceDetailsMap;
	}

	private Map<UUID, StaffAttendanceRegisterData> getStaffAttendanceDetailsUUIDMap(List<StaffAttendanceRegisterData> staffAttendanceDetailsList) {
		if(CollectionUtils.isEmpty(staffAttendanceDetailsList)) {
			return null;
		}

		Map<UUID, StaffAttendanceRegisterData> staffAttendanceDetailsMap = new HashMap<>();
		for(StaffAttendanceRegisterData staffAttendanceDetails : staffAttendanceDetailsList) {
			if(staffAttendanceDetails == null || staffAttendanceDetails.getStaff() == null) {
				continue;
			}
			staffAttendanceDetailsMap.put(staffAttendanceDetails.getStaff().getStaffId(), staffAttendanceDetails);
		}

		return staffAttendanceDetailsMap;
	}

	private Map<UUID, Staff> getStaffMap(List<StaffAttendanceRegisterData> staffList) {
		if(CollectionUtils.isEmpty(staffList)) {
			return null;
		}

		Map<UUID, Staff> staffMap = new HashMap<UUID, Staff>();
		for(StaffAttendanceRegisterData staffAttendanceRegisterData : staffList) {
			staffMap.put(staffAttendanceRegisterData.getStaff().getStaffId(), staffAttendanceRegisterData.getStaff());
		}

		return staffMap;
	}

	private Map<Integer, List<StaffAttendanceRegisterData>> getStaffRegisterDayMap(List<StaffAttendanceRegisterData> staffAttendanceRegisterDataList) {
		if(CollectionUtils.isEmpty(staffAttendanceRegisterDataList)) {
			return null;
		}

		Map<Integer, List<StaffAttendanceRegisterData>> staffRegisterDayMap = new HashMap<>();
		for(StaffAttendanceRegisterData staffAttendanceRegisterData : staffAttendanceRegisterDataList) {
			for(StaffAttendanceDaySummary staffAttendanceDaySummary : staffAttendanceRegisterData.getStaffAttendanceDaySummaryList()){
				if(!staffRegisterDayMap.containsKey(staffAttendanceDaySummary.getAttendanceDate())){
					staffRegisterDayMap.put(staffAttendanceDaySummary.getAttendanceDate(), new ArrayList<>());
				}
				staffRegisterDayMap.get(staffAttendanceDaySummary.getAttendanceDate()).add(new StaffAttendanceRegisterData(staffAttendanceRegisterData.getStaff(), Arrays.asList(staffAttendanceDaySummary)));
			}
		}

		return staffRegisterDayMap;
	}

	private List<Pair<Time, Time>> getTimingPair(Map<StaffAttendanceType, List<StaffAttendanceTimeDetails>> staffAttendanceTimeTypeMap) {
		if (staffAttendanceTimeTypeMap == null || CollectionUtils.isEmpty(staffAttendanceTimeTypeMap.entrySet())) {
			return null;
		}

		List<Pair<Time, Time>> timingPairList = new ArrayList<Pair<Time, Time>>();
		List<StaffAttendanceTimeDetails> staffAttendanceTimeDetailsInList = staffAttendanceTimeTypeMap.get(StaffAttendanceType.IN);
		List<StaffAttendanceTimeDetails> staffAttendanceTimeDetailsOutList = staffAttendanceTimeTypeMap.get(StaffAttendanceType.OUT);

		int outListSize = CollectionUtils.isEmpty(staffAttendanceTimeDetailsOutList) ? 0 : staffAttendanceTimeDetailsOutList.size();
		/**
		 * we are assuing that the size of in list is either greater than or equal to out list
		 */
		if (!CollectionUtils.isEmpty(staffAttendanceTimeDetailsOutList)) {
			for (int i = outListSize - 1; i >= 0; i--) {
				timingPairList.add(new Pair<Time, Time>(staffAttendanceTimeDetailsInList.get(i).getTimeOfAction(),
						staffAttendanceTimeDetailsOutList.get(i).getTimeOfAction()));
			}
		}

		for (int i = outListSize; i < staffAttendanceTimeDetailsInList.size(); i++) {
			timingPairList.add(new Pair<Time, Time>(staffAttendanceTimeDetailsInList.get(i).getTimeOfAction(), null));
		}

		return timingPairList;
	}
}