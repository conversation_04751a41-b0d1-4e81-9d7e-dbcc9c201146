package com.lernen.cloud.core.lib.constants;

/**
 *
 * <AUTHOR>
 *
 */
public class ReportHeaderAttribute {

	private static final String TRANSPORT_BASIC_INFO = "TransportBasicInfo";
	private static final String STUDENT_MEDICAL_INFO = "StudentMedicalInfo";
	private static final String STUDENT_PREVIOUS_SCHOOL_INFO = "StudentPreviousSchoolInfo";
	private static final String STUDENT_FAMILY_INFO = "StudentFamilyInfo";
	private static final String STUDENT_BASIC_INFO = "StudentBasicInfo";
	private static final String STUDENT_TC_INFO = "StudentTCInfo";
	private static final String STAFF_BASIC_INFO = "StaffBasicInfo";
	private static final String STUDENT_GUARDIAN_INFO = "StudentGuardianInfo";
	private static final String TRANSPORT_FEES_INFO = "TransportFeesInfo";
	private static final String USER_WALLET_INFO = "UserWalletInfo";
	private static final String USER_APP_TRACK_INFO = "UserAppTrackInfo";
	private static final String FEE_INFO = "FeeInfo"; 
	private static final String EXAM_INFO = "ExamInfo";
	private static final String STAFF_ADDRESS_INFO = "StaffAddressInfo";
	private static final String STAFF_QUALIFICATION_EXPERIENCE_INFO = "StaffQualificationExperienceInfo";
	private static final String STAFF_JOINING_DOCUMENT_INFO = "StaffJoiningDocumentInfo";
	private static final String STAFF_BANK_INFO = "StaffBankInfo";
	private static final String STAFF_TIMING_INFO = "StaffTimingInfo";
	private static final String STUDENT_SESSION_INFO = "StudentSessionInfo";
	private static final String STAFF_USER_INFO = "StaffUserInfo";
	private static final String STUDENT_USER_INFO = "StudentUserInfo";
	private static final String ATTENDANCE_INFO = "AttendanceInfo";
	/**
	 * Basic Headers
	 */
	public static final ReportHeaderAttribute SERIAL_NUMBER = new ReportHeaderAttribute("serial_number", "SR#",
			STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_STATUS = new ReportHeaderAttribute("student_status",
			"Student Status", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_COUNT = new ReportHeaderAttribute("student_count",
			"Student Count", STUDENT_BASIC_INFO);

	/**
	 * staff Information Headers 
	*/ 

	// staff basic details
	
	public static final ReportHeaderAttribute STAFF_SERIAL_NUMBER = new ReportHeaderAttribute("serial_number", "S.No",STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_INSTITUTE_ID = new ReportHeaderAttribute(
	"staff_institute_id", "Staff Institute Id", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_NAME = new ReportHeaderAttribute("staff_name", "Name",STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_STATUS = new ReportHeaderAttribute("staff_status",
	"Status", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_INITIALS = new ReportHeaderAttribute("staff_initials","Initials", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_GENDER = new ReportHeaderAttribute("staff_gender", "Gender",STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_DOB = new ReportHeaderAttribute("staff_dob", "DOB",STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_CATEGORY = new ReportHeaderAttribute("staff_category",
	"Category", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_DEPARTMENT_DESIGNATION = new ReportHeaderAttribute("staff_department_designation",
	"Department (Designation)", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_USER_CATEGORY = new ReportHeaderAttribute("staff_user_category",
	"User Category", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_MARTIAL_STATUS = new ReportHeaderAttribute("staff_martial_status","Martial Status", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_RELIGION = new ReportHeaderAttribute("staff_religion",
	"Religion", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_FATHER_NAME = new ReportHeaderAttribute("staff_father_name","Father's/Husband's Name", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_MOTHER_NAME = new ReportHeaderAttribute("staff_mother_name","Mother Name", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_BIRTH_PLACE = new ReportHeaderAttribute("staff_birth_place","Birth Place", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_MOTHER_TOUNGE = new ReportHeaderAttribute("staff_mother_tounge","Mother Tounge", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_SPECIALLY_ABLED = new ReportHeaderAttribute("staff_specially_abled","Specially Abled", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_BPL = new ReportHeaderAttribute("staff_bpl","BPL", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_AADHAR_NUMBER = new ReportHeaderAttribute("staff_aadhar_number","Aadhar Number", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_PRIMARY_CONTACT = new ReportHeaderAttribute(
	"staff_primary_contact_number", "Primary Contact Number", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_PRIMARY_EMAIL = new ReportHeaderAttribute("staff_primary_email","Primary Email", STAFF_BASIC_INFO);

    // staff address info

	public static final ReportHeaderAttribute STAFF_PERMANENT_ADDRESS = new ReportHeaderAttribute("staff_permanent_address","Permanent Address", STAFF_ADDRESS_INFO);

	public static final ReportHeaderAttribute STAFF_PRESENT_ADDRESS = new ReportHeaderAttribute("staff_present_address","Present Address", STAFF_ADDRESS_INFO);

	// staff qualification experience info 

	public static final ReportHeaderAttribute STAFF_HIGHEST_QUALIFICATION = new ReportHeaderAttribute("staff_highest_qualification","Highest Qualification", STAFF_QUALIFICATION_EXPERIENCE_INFO);

	public static final ReportHeaderAttribute STAFF_EXPERIENCE = new ReportHeaderAttribute("staff_experience","Experience", STAFF_QUALIFICATION_EXPERIENCE_INFO);

	public static final ReportHeaderAttribute STAFF_LAST_ORGANIZATION_NAME = new ReportHeaderAttribute("staff_last_organization_name","Last Organization Name", STAFF_QUALIFICATION_EXPERIENCE_INFO);

	public static final ReportHeaderAttribute STAFF_LAST_ORGANIZATION_ADDRESS = new ReportHeaderAttribute("staff_last_organization_address","Last Organization Address", STAFF_QUALIFICATION_EXPERIENCE_INFO);

	public static final ReportHeaderAttribute STAFF_LAST_DESIGNATION = new ReportHeaderAttribute("staff_last_designation","Last Designation", STAFF_QUALIFICATION_EXPERIENCE_INFO);

	public static final ReportHeaderAttribute STAFF_LAST_JOB_DURATION = new ReportHeaderAttribute("staff_last_job_duration","Last Job Duration", STAFF_QUALIFICATION_EXPERIENCE_INFO);

	// staff joining info 

	public static final ReportHeaderAttribute STAFF_OFFER_ACCEPTANCE_DATE = new ReportHeaderAttribute("staff_offer_acceptance_date","Offer Acceptance Date", STAFF_JOINING_DOCUMENT_INFO);

	public static final ReportHeaderAttribute STAFF_TENTATIVE_DATE_OF_JOINING = new ReportHeaderAttribute("staff_tentative_date_of_joining","Tentative Date Of Joining",  STAFF_JOINING_DOCUMENT_INFO);

	public static final ReportHeaderAttribute STAFF_REHIRE = new ReportHeaderAttribute("staff_rehire","Rehire",  STAFF_JOINING_DOCUMENT_INFO);

	public static final ReportHeaderAttribute STAFF_ABSCONDIG = new ReportHeaderAttribute("staff_absconding","Absconding",  STAFF_JOINING_DOCUMENT_INFO);

	public static final ReportHeaderAttribute STAFF_BACKGROUND_VERIFICATION = new ReportHeaderAttribute("staff_background_verification","Background Verification",  STAFF_JOINING_DOCUMENT_INFO);

	// staff bank info

	public static final ReportHeaderAttribute STAFF_ACCOUNT_TYPE = new ReportHeaderAttribute("staff_account_type","Account Type", STAFF_BANK_INFO);

	public static final ReportHeaderAttribute STAFF_BANK_NAME = new ReportHeaderAttribute("staff_bank_name","Bank Name",  STAFF_BANK_INFO);

	public static final ReportHeaderAttribute STAFF_ACCOUNT_HOLDER_NAME = new ReportHeaderAttribute("staff_account_holder_name","Account Holder Name",  STAFF_BANK_INFO);

	public static final ReportHeaderAttribute STAFF_ACCOUNT_NUMBER = new ReportHeaderAttribute("staff_account_number","Account Number",  STAFF_BANK_INFO);

	public static final ReportHeaderAttribute STAFF_IFSC_CODE = new ReportHeaderAttribute("staff_ifsc_code","IFSC code",  STAFF_BANK_INFO);

	public static final ReportHeaderAttribute STAFF_PAN_NUMBER = new ReportHeaderAttribute("staff_pan_number","Pan Number",  STAFF_BANK_INFO);

	// staff timing details

	public static final ReportHeaderAttribute STAFF_IN_TIME = new ReportHeaderAttribute("staff_in_time","In time", STAFF_TIMING_INFO);

	public static final ReportHeaderAttribute STAFF_OUT_TIME = new ReportHeaderAttribute("staff_out_time","Out time",  STAFF_TIMING_INFO);

	public static final ReportHeaderAttribute STAFF_HALF_DAY_DURATION = new ReportHeaderAttribute("staff_half_day_duration","Half Day Duration",  STAFF_TIMING_INFO);

	public static final ReportHeaderAttribute STAFF_FULL_DAY_DURATION = new ReportHeaderAttribute("staff_full_day_duration","Full Day Duration",  STAFF_TIMING_INFO);
	public static final ReportHeaderAttribute STAFF_BIOMETRIC_DEVICE_USER_ID = new ReportHeaderAttribute("staff_biometric_device_user_id","Biometric Id", STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_USER_NAME = new ReportHeaderAttribute("staff_user_name","Staff Username",  STAFF_USER_INFO);
	public static final ReportHeaderAttribute STAFF_USER_STATUS = new ReportHeaderAttribute("staff_user_status","Staff Status",  STAFF_USER_INFO);

	public static final ReportHeaderAttribute STAFF_OASIS_ID = new ReportHeaderAttribute("staff_oasis_id","Oasis Id",  STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_NATIONAL_CODE = new ReportHeaderAttribute("staff_national_code","National Code (U-Dise)",  STAFF_BASIC_INFO);

	public static final ReportHeaderAttribute STAFF_NATURE_OF_APPOINTMENT = new ReportHeaderAttribute("staff_nature_of_appointment","Nature of Appointment",  STAFF_BASIC_INFO);

	
	/**
	 * Student Information Headers
	 */
	public static final ReportHeaderAttribute STUDENT_NAME = new ReportHeaderAttribute("student_name", "Student Name",
			STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_SESSION_STATUS = new ReportHeaderAttribute("student_session_status",
			"Session Status", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute FINAL_STUDENT_STATUS = new ReportHeaderAttribute("final_student_status",
			"Final Student Status", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_MOTHER_NAME = new ReportHeaderAttribute("student_mother_name",
			"Mother Name", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_FATHER_NAME = new ReportHeaderAttribute("student_father_name",
			"Father Name", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_CLASS_NAME = new ReportHeaderAttribute("student_class_name",
			"Class", STUDENT_BASIC_INFO);
//	public static final ReportHeaderAttribute STUDENT_SECTION_NAME = new ReportHeaderAttribute("student_section_name",
//			"Section", STUDENT_BASIC_INFO);
	
	
	public static final ReportHeaderAttribute STUDENT_ADMISSION_NUMBER = new ReportHeaderAttribute(
			"student_admission_number", "Admission Number", STUDENT_BASIC_INFO);

	public static final ReportHeaderAttribute STUDENT_ADMISSION_DATE = new ReportHeaderAttribute(
			"student_admission_date", "Admission Date", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_DOB = new ReportHeaderAttribute("student_dob", "DOB",
			STUDENT_BASIC_INFO);

	public static final ReportHeaderAttribute STUDENT_GENDER = new ReportHeaderAttribute("student_gender", "Gender",
			STUDENT_BASIC_INFO);

	public static final ReportHeaderAttribute STUDENT_CATEGORY = new ReportHeaderAttribute("student_category",
			"Category", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_RELIGION = new ReportHeaderAttribute("student_religion",
			"Religion", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_CASTE = new ReportHeaderAttribute("student_caste",
			"Caste", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_RTE = new ReportHeaderAttribute("student_rte", "RTE Student",
			STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_SPECIALLY_ABLED = new ReportHeaderAttribute(
			"student_specially_abled", "Specially Abled", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_SPECIALLY_ABLED_TYPE = new ReportHeaderAttribute(
			"student_specially_abled_type", "Specially Abled Type", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_BPL = new ReportHeaderAttribute("student_bpl", "BPL",
			STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_PRIMARY_CONTACT = new ReportHeaderAttribute(
			"student_primary_contact_number", "Primary Contact Number", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute FATHER_CONTACT = new ReportHeaderAttribute("father_contact_number",
			"Father Contact Number", STUDENT_FAMILY_INFO);
	public static final ReportHeaderAttribute MOTHER_CONTACT = new ReportHeaderAttribute("mother_contact_number",
			"Mother Contact Number", STUDENT_FAMILY_INFO);
	public static final ReportHeaderAttribute STUDENT_BLOOD_GROUP = new ReportHeaderAttribute("student_blood_group",
			"Blood Group", STUDENT_MEDICAL_INFO);
	public static final ReportHeaderAttribute STUDENT_REGISTRATION_NUMBER = new ReportHeaderAttribute(
			"student_registration_number", "Registration Number", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_RELIEVE_DATE = new ReportHeaderAttribute("student_relieve_date",
			"Relieve Date", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_BIRTH_PLACE = new ReportHeaderAttribute("student_birth_place",
			"Birth Place", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_AADHAR_NUMBER = new ReportHeaderAttribute("student_aadhar_number",
			"Aadhar Number", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_NATIONALITY = new ReportHeaderAttribute("student_nationality",
			"Nationality", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_MOTHER_TONGUE = new ReportHeaderAttribute("student_mother_tongue",
			"Mother Tongue", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_AREA_TYPE = new ReportHeaderAttribute("student_area_type",
			"Area Type", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_PERMANENT_ADDRESS = new ReportHeaderAttribute(
			"student_permanent_address", "Permanent Address", STUDENT_BASIC_INFO);
			
	public static final ReportHeaderAttribute STUDENT_PRESENT_ADDRESS = new ReportHeaderAttribute(
				"student_present_address", "Present Address", STUDENT_BASIC_INFO);		
	public static final ReportHeaderAttribute STUDENT_CITY = new ReportHeaderAttribute("student_city", "Permanent City",
			STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_STATE = new ReportHeaderAttribute("student_state", "Permanent State",
			STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_ZIPCODE = new ReportHeaderAttribute("student_zipcode", "Permanent Zipcode",
			STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_PRESENT_CITY = new ReportHeaderAttribute("student_present_city", "Present City",
			STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_PRESENT_STATE = new ReportHeaderAttribute("student_present_state", "Present State",
			STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_PRESENT_ZIPCODE = new ReportHeaderAttribute("student_present_zipcode", "Present Zipcode",
			STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_PRIMARY_EMAIL = new ReportHeaderAttribute("student_primary_email",
			"Primary Email", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_SPONSORED = new ReportHeaderAttribute("student_sponsored",
	"Sponsored", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_HOSTELLER = new ReportHeaderAttribute("student_hosteller",
	"Hosteller", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_WHATSAPP_NUMBER = new ReportHeaderAttribute("student_whatsapp_number",
	"Whatsapp Number", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_ADMISSION_IN_CLASS  = new ReportHeaderAttribute("student_admission_in_class",
	"Admission in Class", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_PRESENT_POST_OFFICE  = new ReportHeaderAttribute("student_present_post_office",
	"Present Post Office", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_PERMANENT_POST_OFFICE  = new ReportHeaderAttribute("student_permanent_post_office",
	"Permanent Post Office", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_PRESENT_POLICE_STATION  = new ReportHeaderAttribute("student_present_police_station",
	"Present Police Station", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_PERMANENT_POLICE_STATION  = new ReportHeaderAttribute("student_permanent_police_station",
	"Permanent Police Station", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_HOUSE_NAME  = new ReportHeaderAttribute("student_house_name",
	"House Name", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_BIOMETRIC_DEVICE_USED_ID = new ReportHeaderAttribute(
			"student_biometric_id", "Biometric Id", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_PEN_NUMBER = new ReportHeaderAttribute(
			"student_pen_number", "PEN Number", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_NAME_AS_PER_AADHAR  = new ReportHeaderAttribute("student_name_as_per_aadhar",
			"Student Name As Per Aadhar", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute CHILD_CRITERIA_CATEGORY = new ReportHeaderAttribute("child_category_criteria",
			"Only Child Category", STUDENT_BASIC_INFO);
	public static final ReportHeaderAttribute STUDENT_HOSTEL_NAME = new ReportHeaderAttribute("student_hostel_name", "Hostel Name", STUDENT_BASIC_INFO);


	public static final ReportHeaderAttribute STUDENT_FATHER_OCCUPATION = new ReportHeaderAttribute(
			"student_father_occupation", "Father Occupation", STUDENT_FAMILY_INFO);
	public static final ReportHeaderAttribute STUDENT_FATHER_ANNUAL_INCOME = new ReportHeaderAttribute(
			"student_father_annual_income", "Father Annual Income", STUDENT_FAMILY_INFO);
	public static final ReportHeaderAttribute STUDENT_MOTHER_OCCUPATION = new ReportHeaderAttribute(
			"student_mother_occupation", "Mother Occupation", STUDENT_FAMILY_INFO);
	public static final ReportHeaderAttribute STUDENT_MOTHER_ANNUAL_INCOME = new ReportHeaderAttribute(
			"student_mother_annual_income", "Mother Annual Income", STUDENT_FAMILY_INFO);
	public static final ReportHeaderAttribute FATHER_AADHAR_NUMBER = new ReportHeaderAttribute("father_aadhar_number",
			"Father Aadhar Number", STUDENT_FAMILY_INFO);
	public static final ReportHeaderAttribute FATHER_PAN_CARD_DETAILS = new ReportHeaderAttribute("father_pan_card_details",
			"Father Pan Card Details", STUDENT_FAMILY_INFO);
	public static final ReportHeaderAttribute STUDENT_FATHER_QUALIFICATION  = new ReportHeaderAttribute("student_father_qualification",
	"Father Qualification", STUDENT_FAMILY_INFO);
	public static final ReportHeaderAttribute STUDENT_MOTHER_QUALIFICATION  = new ReportHeaderAttribute("student_mother_qualification",
	"Mother Qualification", STUDENT_FAMILY_INFO);
	public static final ReportHeaderAttribute MOTHER_AADHAR_NUMBER = new ReportHeaderAttribute("mother_aadhar_number",
			"Mother Aadhar Number", STUDENT_FAMILY_INFO);
	public static final ReportHeaderAttribute MOTHER_PAN_CARD_DETAILS = new ReportHeaderAttribute("mother_pan_card_details",
			"Mother Pan Card Details", STUDENT_FAMILY_INFO);
	public static final ReportHeaderAttribute STUDENT_FAMILY_APPROX_INCOME = new ReportHeaderAttribute(
			"student_family_approx_income", "Family Approx Income", STUDENT_FAMILY_INFO);
	public static final ReportHeaderAttribute STUDENT_GUARDIAN_NAME = new ReportHeaderAttribute(
			"guardian_name", "Guardian Name", STUDENT_GUARDIAN_INFO);
	public static final ReportHeaderAttribute STUDENT_GUARDIAN_AGE = new ReportHeaderAttribute(
			"guardian_age", "Guardian Age", STUDENT_GUARDIAN_INFO);
	public static final ReportHeaderAttribute STUDENT_GUARDIAN_GENDER = new ReportHeaderAttribute(
			"guardian_gender", "Guardian Gender", STUDENT_GUARDIAN_INFO);
	public static final ReportHeaderAttribute STUDENT_GUARDIAN_OCCUPATION = new ReportHeaderAttribute(
			"guardian_occupation", "Guardian Occupation", STUDENT_GUARDIAN_INFO);
	public static final ReportHeaderAttribute STUDENT_GUARDIAN_EMAIL = new ReportHeaderAttribute(
			"guardian_email", "Guardian Email", STUDENT_GUARDIAN_INFO);
	public static final ReportHeaderAttribute STUDENT_GUARDIAN_CONTACT_NUMBER = new ReportHeaderAttribute(
			"guardian_contact_number", "Guardian Contact Number", STUDENT_GUARDIAN_INFO);
	public static final ReportHeaderAttribute STUDENT_GUARDIAN_ADDRESS = new ReportHeaderAttribute(
			"guardian_address", "Guardian Address", STUDENT_GUARDIAN_INFO);
	public static final ReportHeaderAttribute STUDENT_GUARDIAN_CITY = new ReportHeaderAttribute(
			"guardian_city", "Guardian City", STUDENT_GUARDIAN_INFO);
	public static final ReportHeaderAttribute STUDENT_GUARDIAN_STATE = new ReportHeaderAttribute(
			"guardian_state", "Guardian State", STUDENT_GUARDIAN_INFO);
	public static final ReportHeaderAttribute STUDENT_GUARDIAN_ZIPCODE = new ReportHeaderAttribute(
			"guardian_zipcode", "Guardian Zipcode", STUDENT_GUARDIAN_INFO);
	public static final ReportHeaderAttribute STUDENT_GUARDIAN_RELATION = new ReportHeaderAttribute(
				"guardian_relation", "Guardian Relation", STUDENT_GUARDIAN_INFO);	
	public static final ReportHeaderAttribute STUDENT_PREVIOUS_SCHOOL_NAME = new ReportHeaderAttribute(
			"student_previous_school_name", "Previous School Name", STUDENT_PREVIOUS_SCHOOL_INFO);
	public static final ReportHeaderAttribute STUDENT_CLASS_PASSED = new ReportHeaderAttribute("student_class_passed",
			"Class Passed", STUDENT_PREVIOUS_SCHOOL_INFO);
	public static final ReportHeaderAttribute STUDENT_PREVIOUS_TC_NUMBER  =  new ReportHeaderAttribute("student_previous_tc_number",
			"Previous TC Number", STUDENT_PREVIOUS_SCHOOL_INFO);
	public static final ReportHeaderAttribute STUDENT_TC_BASED_ADMISSION_DISPLAY =  new ReportHeaderAttribute("student_tc_based_admission_display",
	"Tc Based Admission", STUDENT_PREVIOUS_SCHOOL_INFO);
	public static final ReportHeaderAttribute STUDENT_PREVIOUS_SCHOOL_MEDIUM = new ReportHeaderAttribute(
			"student_previous_school_medium", "Previous School Medium", STUDENT_PREVIOUS_SCHOOL_INFO);
	public static final ReportHeaderAttribute STUDENT_PREVIOUS_RESULT = new ReportHeaderAttribute(
			"student_previous_result", "Previous Result", STUDENT_PREVIOUS_SCHOOL_INFO);
	public static final ReportHeaderAttribute STUDENT_PREVIOUS_PERCENTAGE = new ReportHeaderAttribute(
			"student_previous_percentage", "Previous Percentage", STUDENT_PREVIOUS_SCHOOL_INFO);
	public static final ReportHeaderAttribute STUDENT_YEAR_OF_PASSING = new ReportHeaderAttribute(
			"student_year_of_passing", "Year Of Passing", STUDENT_PREVIOUS_SCHOOL_INFO);
	public static final ReportHeaderAttribute STUDENT_BLOOD_PRESSURE = new ReportHeaderAttribute(
			"student_blood_pressure", "Blood Pressure", STUDENT_MEDICAL_INFO);
	public static final ReportHeaderAttribute STUDENT_PULSE = new ReportHeaderAttribute("student_pulse", "Pulse",
			STUDENT_MEDICAL_INFO);
	public static final ReportHeaderAttribute STUDENT_HEIGHT = new ReportHeaderAttribute("student_height", "Height",
			STUDENT_MEDICAL_INFO);
	public static final ReportHeaderAttribute STUDENT_WEIGHT = new ReportHeaderAttribute("student_weight", "weight",
			STUDENT_MEDICAL_INFO);
	public static final ReportHeaderAttribute STUDENT_DATE_OF_PHYSICAL_EXAMINATION = new ReportHeaderAttribute(
			"student_date_of_physical_examination", "Date Of Physical Examination", STUDENT_MEDICAL_INFO);
	public static final ReportHeaderAttribute STUDENT_ACADEMIC_SESSION = new ReportHeaderAttribute(
			"student_academic_session", "Academic Session", STUDENT_BASIC_INFO);
	
	public static final ReportHeaderAttribute STUDENT_SESSION_ROLL_NUMBER = new ReportHeaderAttribute(
		"student_session_roll_number", "Session Roll Number", STUDENT_SESSION_INFO);
	public static final ReportHeaderAttribute STUDENT_SESSION_HEIGHT = new ReportHeaderAttribute(
			"student_session_height", "Session Height", STUDENT_SESSION_INFO);
	public static final ReportHeaderAttribute STUDENT_SESSION_WEIGHT = new ReportHeaderAttribute(
		"student_session_weight", "Session Weight", STUDENT_SESSION_INFO);
	public static final ReportHeaderAttribute STUDENT_BOARD_REGISTRATION_NUMBER = new ReportHeaderAttribute(
					"student_board_registration_number", "Board Registration Number", STUDENT_SESSION_INFO);
	public static final ReportHeaderAttribute STUDENT_MEDIUM = new ReportHeaderAttribute(
		"student_medium", "Medium", STUDENT_SESSION_INFO);

	public static final ReportHeaderAttribute STUDENT_USER_NAME = new ReportHeaderAttribute(
			"student_user_name","Student Username",  STUDENT_USER_INFO);
	public static final ReportHeaderAttribute STUDENT_USER_STATUS = new ReportHeaderAttribute(
			"student_user_status","Student Status",  STUDENT_USER_INFO);

	public static final ReportHeaderAttribute STUDENT_TC_NUMBER = new ReportHeaderAttribute(
			"student_tc_number", "TC Number", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute STUDENT_AFFILIATION_NUMBER = new ReportHeaderAttribute(
			"student_affiliation_number", "Affiliation Number", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute STUDENT_DISE_CODE = new ReportHeaderAttribute(
			"student_dise_code", "DISE Code", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute STUDENT_BOOK_NUMBER = new ReportHeaderAttribute(
			"student_book_number", "Book Number", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute STUDENT_SCHOOL_CODE = new ReportHeaderAttribute(
			"student_school_code", "School Code", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute STUDENT_RTE_AFFILIATION_NUMBER = new ReportHeaderAttribute(
			"student_rte_affiliation_number", "RTE Affiliation Number", STUDENT_TC_INFO);

	public static final ReportHeaderAttribute TC_STUDENT_ADMISSION_NUMBER = new ReportHeaderAttribute(
			"tc_student_admission_number", "Admission Number(TC)", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute TC_STUDENT_NAME = new ReportHeaderAttribute(
			"tc_student_name", "Student Name(TC)", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute TC_STUDENT_ADMISSION_DATE = new ReportHeaderAttribute(
			"tc_student_admission_date", "Admission Date(TC)", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute STUDENT_ADMISSION_CLASS = new ReportHeaderAttribute(
			"tc_student_admission_class", "Admission Class(TC)", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute STUDENT_FATHER_OR_GUARDIAN_NAME = new ReportHeaderAttribute(
			"tc_student_father_or_guardian_name", "Father Name/Guardian Name(TC)", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute TC_STUDENT_MOTHER_NAME = new ReportHeaderAttribute(
			"tc_student_mother_name", "Mother Name(TC)", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute TC_STUDENT_DOB = new ReportHeaderAttribute(
			"tc_student_dob", "DOB(TC)", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute STUDENT_DOB_PROOF = new ReportHeaderAttribute(
			"student_dob_proof", "Proof Of DOB", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute TC_STUDENT_CATEGORY = new ReportHeaderAttribute(
			"tc_student_category", "Category(TC)", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute TC_STUDENT_NATIONALITY = new ReportHeaderAttribute(
			"tc_student_nationality", "Nationality(TC)", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute STUDENT_PEN = new ReportHeaderAttribute(
			"student_pen", "Primary Education Affiliation Number (P.E.N)", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute STUDENT_APAAR_ID = new ReportHeaderAttribute(
			"student_apaar_id", "APAAR ID Number", STUDENT_TC_INFO);

	public static final ReportHeaderAttribute LAST_ACTIVE_SESSION_CLASS = new ReportHeaderAttribute(
			"last_active_session_class", "Last Active Session Class", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute LAST_EXAM_RESULT = new ReportHeaderAttribute(
			"last_exam_taken_result", "Last Exam with Result", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute FAILED_IN_SAME_CLASS = new ReportHeaderAttribute(
			"failed_status", "No. of Times Failed in same class", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute LAST_SESSION_SUBJECTS = new ReportHeaderAttribute(
			"subjects_last_session", "Last Active Session Subjects", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute QUALIFIED_FOR_PROMOTION = new ReportHeaderAttribute(
			"qualified_for_promotion", "Qualified for Promoting in Class", STUDENT_TC_INFO);

	public static final ReportHeaderAttribute LAST_FEES_PAID = new ReportHeaderAttribute(
			"last_fees_paid", "Last fees paid", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute DISCOUNT_DETAILS = new ReportHeaderAttribute(
			"discount_details", "Discount given", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute TOTAL_WORKING_DAYS = new ReportHeaderAttribute(
			"total_working_days", "Total working days in the session", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute TOTAL_ATTENDED_DAYS = new ReportHeaderAttribute(
			"total_attended_days", "Total attended days in the session", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute NCC_SCOUT_GUIDE_DETAILS = new ReportHeaderAttribute(
			"ncc_scout_guide_details", "Whether NCC Cadet/Boy Scout/Girl Guide", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute CO_CURRICULAR_ACTIVITIES = new ReportHeaderAttribute(
			"co_curricular_activities", "Co-curricular Activities", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute CODE_OF_CONDUCT = new ReportHeaderAttribute(
			"code_of_conduct", "Code of Conduct", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute TC_GENERATION_DATE = new ReportHeaderAttribute(
			"tc_generation_date", "TC Generation Date", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute RELIEVE_DATE = new ReportHeaderAttribute(
			"relieve_date", "Relieve Date", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute RELIEVE_REASON = new ReportHeaderAttribute(
			"relieve_reason", "Relieve Reason", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute CERTIFICATE_APPLICATION_DATE = new ReportHeaderAttribute(
			"certificate_application_date", "Certificate Application Date", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute NAME_STRUCK_OFF_DATE = new ReportHeaderAttribute(
			"name_struck_off_date", "Name Struck-off Date", STUDENT_TC_INFO);
	public static final ReportHeaderAttribute TC_REMARKS = new ReportHeaderAttribute(
			"remarks", "Remarks", STUDENT_TC_INFO);

	/**
	 * Transport
	 */
	public static final ReportHeaderAttribute TRANSPORT_PARENT_AREA = new ReportHeaderAttribute("transport_parent_area_name",
			"Area Name", TRANSPORT_BASIC_INFO);
	public static final ReportHeaderAttribute TRANSPORT_AREA_NAME = new ReportHeaderAttribute("transport_area_name",
			"Stoppage Name", TRANSPORT_BASIC_INFO);
	public static final ReportHeaderAttribute TRANSPORT_AREA_ID = new ReportHeaderAttribute("transport_area_id",
			"Stoppage Short Code", TRANSPORT_BASIC_INFO);

	public static final ReportHeaderAttribute TRANSPORT_PICKUP_ROUTE_NAME = new ReportHeaderAttribute("transport_pickup_route_name",
			"Pickup Route Name", TRANSPORT_BASIC_INFO);
	public static final ReportHeaderAttribute TRANSPORT_PICKUP_VEHICLE_NAME = new ReportHeaderAttribute("transport_pickup_vehicle_name",
			"Pickup Vehicle Name (Vehicle Code)", TRANSPORT_BASIC_INFO);

	public static final ReportHeaderAttribute TRANSPORT_DROP_ROUTE_NAME = new ReportHeaderAttribute("transport_drop_route_name",
			"Drop Route Name", TRANSPORT_BASIC_INFO);
	public static final ReportHeaderAttribute TRANSPORT_DROP_VEHICLE_NAME = new ReportHeaderAttribute("transport_drop_vehicle_name",
			"Drop Vehicle Name (Vehicle Code)", TRANSPORT_BASIC_INFO);

	public static final ReportHeaderAttribute TRANSPORT_ROUTE_NAME = new ReportHeaderAttribute("transport_route_name",
			"Route Name", TRANSPORT_BASIC_INFO);

	public static final ReportHeaderAttribute TRANSPORT_ROUTE_TYPE = new ReportHeaderAttribute("transport_route_type",
			"Route Type", TRANSPORT_BASIC_INFO);

	public static final ReportHeaderAttribute ROUTE_AREA_TIME = new ReportHeaderAttribute(
			"transport_route_area_time", "Time", TRANSPORT_BASIC_INFO);

//	public static final ReportHeaderAttribute ROUTE_AREA_PICK_TIME = new ReportHeaderAttribute(
//			"transport_route_area_pickup_time", "Pick Up Time", TRANSPORT_BASIC_INFO);
//
//	public static final ReportHeaderAttribute ROUTE_AREA_DROP_TIME = new ReportHeaderAttribute(
//			"transport_route_area_drop_time", "Drop Up Time", TRANSPORT_BASIC_INFO);

	public static final ReportHeaderAttribute TRANSPORT_FEES_NAME = new ReportHeaderAttribute(
			"transport_fees_name", "Fees Name", TRANSPORT_BASIC_INFO);

	public static final ReportHeaderAttribute ROUTE_AREA_PAYBLE_AMOUNT = new ReportHeaderAttribute(
			"transport_route_area_payble_amount", "Payble Amount", TRANSPORT_BASIC_INFO);
	
	public static final ReportHeaderAttribute ROUTE_AREA_DUE_AMOUNT = new ReportHeaderAttribute(
			"transport_route_area_due_amount", "Due Amount", TRANSPORT_FEES_INFO);
	
	public static final ReportHeaderAttribute ASSIGNED_AMOUNT = new ReportHeaderAttribute(
			"transport_route_area_assigned_amount", "Assigned Amount", TRANSPORT_FEES_INFO);
	
	public static final ReportHeaderAttribute AMOUNT_COLLECTED = new ReportHeaderAttribute(
			"transport_route_area_amount_collected", "Amount Collected", TRANSPORT_FEES_INFO);
	
	public static final ReportHeaderAttribute GIVEN_DISCOUNT = new ReportHeaderAttribute(
			"transport_route_area_given_discount", "Given Discount", TRANSPORT_FEES_INFO);
	
	public static final ReportHeaderAttribute DISCOUNT_TO_BE_GIVEN = new ReportHeaderAttribute(
			"transport_route_area_discount_to_be_given", "Discount To Be Given", TRANSPORT_FEES_INFO);

//	public static final ReportHeaderAttribute TRANSPORT_PAID_FINE_AMOUNT = new ReportHeaderAttribute(
//			"transport_paid_fine_amount", "Paid Fine Amount", TRANSPORT_FEES_INFO);
//	public static final ReportHeaderAttribute TRANSPORT_DUE_FINE_AMOUNT = new ReportHeaderAttribute(
//			"transport_due_fine_amount", "Due Fine Amount", TRANSPORT_FEES_INFO);


	/**
	 * FEE reports
	 */

	public static final ReportHeaderAttribute FEE_DISCOUNT_STRUCTURE_NAMES = new ReportHeaderAttribute(
			"fee.discount.structure.names", "Discounts", FEE_INFO);

	public static final ReportHeaderAttribute FEES_SR_NO = new ReportHeaderAttribute(
			"sr_no", "Sr No.", FEE_INFO);
	public static final ReportHeaderAttribute REGISTRATION_NUMBER = new ReportHeaderAttribute(
			"registration_no", "Registration No.", FEE_INFO);
	public static final ReportHeaderAttribute ADMISSION_NUMBER = new ReportHeaderAttribute(
			"admission_no", "Admission No.", FEE_INFO);
	public static final ReportHeaderAttribute FEES_STUDENT_NAME = new ReportHeaderAttribute(
			"name", "Student Name", FEE_INFO);
	public static final ReportHeaderAttribute STATUS = new ReportHeaderAttribute(
			"status", "Status", FEE_INFO);
	public static final ReportHeaderAttribute STUDENT_CLASS = new ReportHeaderAttribute(
			"class", "Class", FEE_INFO);
	public static final ReportHeaderAttribute FEES_FATHER_NAME = new ReportHeaderAttribute(
			"father_name", "Father Name", FEE_INFO);
	public static final ReportHeaderAttribute FEES_HEAD_NAME = new ReportHeaderAttribute(
			"fees_head_name", "Fees Head Name", FEE_INFO);
	public static final ReportHeaderAttribute PRIMARY_CONTACT = new ReportHeaderAttribute(
			"primary_contact", "Primary Contact", FEE_INFO);
	public static final ReportHeaderAttribute WHATSAPP_NUMBER = new ReportHeaderAttribute(
			"whatsapp_number", "Whatsapp Number", FEE_INFO);
	public static final ReportHeaderAttribute FEES_FATHER_CONTACT = new ReportHeaderAttribute(
			"father_contact", "Father Contact", FEE_INFO);
	public static final ReportHeaderAttribute FEES_MOTHER_CONTACT = new ReportHeaderAttribute(
			"mother_contact", "Mother Contact", FEE_INFO);
	public static final ReportHeaderAttribute FEES_ASSIGNED_AMOUNT = new ReportHeaderAttribute(
			"assigned_amount", "Assigned Amount", FEE_INFO);
	public static final ReportHeaderAttribute FEES_AMOUNT_COLLECTED = new ReportHeaderAttribute(
			"amount_collected", "Amount Collected", FEE_INFO);
	public static final ReportHeaderAttribute FEES_GIVEN_DISCOUNT = new ReportHeaderAttribute(
			"given_discount", "Given Discount", FEE_INFO);
	public static final ReportHeaderAttribute FEES_DISCOUNT_TO_BE_GIVEN = new ReportHeaderAttribute(
			"discount_to_be_given", "Discount To Be Given", FEE_INFO);
	public static final ReportHeaderAttribute FEES_DUE_AMOUNT = new ReportHeaderAttribute(
			"due_amount", "Due Amount", FEE_INFO);
	public static final ReportHeaderAttribute AMOUNT_PAID = new ReportHeaderAttribute(
			"amount_paid", "Amount Paid", FEE_INFO);
	public static final ReportHeaderAttribute FEES_PAID_FINE_AMOUNT = new ReportHeaderAttribute(
			"paid_fine_amount", "Paid Fine Amount", FEE_INFO);
	public static final ReportHeaderAttribute FEES_DUE_FINE_AMOUNT = new ReportHeaderAttribute(
			"due_fine_amount", "Due Fine Amount", FEE_INFO);
	public static final ReportHeaderAttribute TRANSACTION_DATE = new ReportHeaderAttribute(
			"transaction_date", "Transaction Date", FEE_INFO);
	public static final ReportHeaderAttribute TRANSACTION_ADDED_AT = new ReportHeaderAttribute(
			"transaction_added_at", "Transaction Added At", FEE_INFO);
	public static final ReportHeaderAttribute TRANSACTION_MODE = new ReportHeaderAttribute(
			"transaction_mode", "Transaction Mode", FEE_INFO);
	public static final ReportHeaderAttribute INVOICE_NUMBER = new ReportHeaderAttribute(
			"invoice_number", "Invoice Number", FEE_INFO);
	public static final ReportHeaderAttribute TRANSACTION_BY = new ReportHeaderAttribute(
			"transaction_by", "Transaction By", FEE_INFO);
	public static final ReportHeaderAttribute REMARKS = new ReportHeaderAttribute(
			"remarks", "Remarks", FEE_INFO);
	public static final ReportHeaderAttribute BANK_ACCOUNT = new ReportHeaderAttribute(
			"bank_account", "Bank Account", FEE_INFO);

	/**
	 * EXAM reports
	 */
	public static final ReportHeaderAttribute EXAM_SR_NO = new ReportHeaderAttribute(
			"sr_no", "Sr No.", EXAM_INFO);
	public static final ReportHeaderAttribute ADMISSION_No = new ReportHeaderAttribute(
			"admission_no", "Admission No.", EXAM_INFO);
	public static final ReportHeaderAttribute ROLL_NO = new ReportHeaderAttribute(
			"roll_no", "Roll No.", EXAM_INFO);
	public static final ReportHeaderAttribute NAME = new ReportHeaderAttribute(
			"name", "Name", EXAM_INFO);
	public static final ReportHeaderAttribute FATHER_NAME = new ReportHeaderAttribute(
			"father_name", "Father Name", EXAM_INFO);
	public static final ReportHeaderAttribute DOB = new ReportHeaderAttribute(
			"dob", "DOB", EXAM_INFO);
	public static final ReportHeaderAttribute CLASS = new ReportHeaderAttribute(
			"class", "Class", EXAM_INFO);
	public static final ReportHeaderAttribute PERCENTAGE = new ReportHeaderAttribute(
			"percentage", "%", EXAM_INFO);
	public static final ReportHeaderAttribute GRADE = new ReportHeaderAttribute(
			"grade", "Grade", EXAM_INFO);
	public static final ReportHeaderAttribute DIVISION = new ReportHeaderAttribute(
			"division", "Division", EXAM_INFO);
	public static final ReportHeaderAttribute RESULT = new ReportHeaderAttribute(
			"result", "Result", EXAM_INFO);
	public static final ReportHeaderAttribute RANK = new ReportHeaderAttribute(
			"rank", "Rank", EXAM_INFO);
	public static final ReportHeaderAttribute ATTENDANCE = new ReportHeaderAttribute(
			"attendance", "Attendance", EXAM_INFO);
	public static final ReportHeaderAttribute PARENT_REMARKS = new ReportHeaderAttribute(
			"parent_remarks", "Parent Remarks", EXAM_INFO);

	/**
	 * User wallet report
	 */

	public static final ReportHeaderAttribute USER_WALLET_TRANSACTION_DATE = new ReportHeaderAttribute(
			"user.wallet.transaction.date", "Transaction Date", USER_WALLET_INFO);

	public static final ReportHeaderAttribute USER_WALLET_TRANSACTION_TYPE = new ReportHeaderAttribute(
			"user.wallet.transaction.type", "Type", USER_WALLET_INFO);

	public static final ReportHeaderAttribute USER_WALLET_TRANSACTION_MODE = new ReportHeaderAttribute(
			"user.wallet.transaction.mode", "Mode", USER_WALLET_INFO);

	public static final ReportHeaderAttribute USER_WALLET_TRANSACTION_ADDED_AMOUNT = new ReportHeaderAttribute(
			"user.wallet.transaction.added_amount", "Added Amount", USER_WALLET_INFO);

	public static final ReportHeaderAttribute USER_WALLET_TRANSACTION_USED_AMOUNT = new ReportHeaderAttribute(
			"user.wallet.transaction.used_amount", "Used Amount", USER_WALLET_INFO);

	public static final ReportHeaderAttribute USER_WALLET_TRANSACTION_DESCRIPTION = new ReportHeaderAttribute(
			"user.wallet.transaction.description", "Description", USER_WALLET_INFO);

	public static final ReportHeaderAttribute USER_WALLET_BALANCE = new ReportHeaderAttribute(
			"user.wallet.balance", "Balance (In INR)", USER_WALLET_INFO);

	/**
	 * User app track
	 */

	public static final ReportHeaderAttribute USER_MOBILE_APP_INSTALLED = new ReportHeaderAttribute(
			"user.app.mobile.installed", "Mobile App Installed", USER_APP_TRACK_INFO);

	public static final ReportHeaderAttribute USER_MOBILE_APP_LAST_LOGIN = new ReportHeaderAttribute(
			"user.app.mobile.last.login", "Last Login", USER_APP_TRACK_INFO);

	public static final ReportHeaderAttribute USER_MOBILE_APP_LAST_ACCESS = new ReportHeaderAttribute(
			"user.app.mobile.last.access", "Last Access", USER_APP_TRACK_INFO);

	/**
	 * Attendance
	 */
	public static final ReportHeaderAttribute ATTENDANCE_SR_NO = new ReportHeaderAttribute(
			"attendance_sr_no", "Sr No.", ATTENDANCE_INFO);

	public static final ReportHeaderAttribute ATTENDANCE_STUDENT_NAME = new ReportHeaderAttribute(
			"attendance_student_name", "Name", ATTENDANCE_INFO);

	public static final ReportHeaderAttribute ATTENDANCE_ADMISSION_NO = new ReportHeaderAttribute(
			"attendance_admission_no", "Admission No.", ATTENDANCE_INFO);

	public static final ReportHeaderAttribute ATTENDANCE_STUDENT_CLASS = new ReportHeaderAttribute(
			"attendance_student_class", "Class", ATTENDANCE_INFO);

	public static final ReportHeaderAttribute ATTENDANCE_STUDENT_ROLL_NUMBER = new ReportHeaderAttribute(
			"attendance_student_roll_number", "Roll Number", ATTENDANCE_INFO);

public static final ReportHeaderAttribute ATTENDANCE_STUDENT_PRIMARY_CONTACT_NUMBER = new ReportHeaderAttribute(
			"attendance_student_primary_contact_number", "Primary Contact Number", ATTENDANCE_INFO);

	public static final ReportHeaderAttribute TOTAL_DAYS = new ReportHeaderAttribute(
			"total_days", "Total Days", ATTENDANCE_INFO);

	public static final ReportHeaderAttribute PRESENT_COUNT = new ReportHeaderAttribute(
			"present_count", "Present Count", ATTENDANCE_INFO);

	public static final ReportHeaderAttribute ABSENT_COUNT = new ReportHeaderAttribute(
			"absent_count", "Absent Count", ATTENDANCE_INFO);

	public static final ReportHeaderAttribute PRESENT_PERCENT = new ReportHeaderAttribute(
			"present_percent", "Present %", ATTENDANCE_INFO);

	public static final ReportHeaderAttribute UNMARKED_COUNT = new ReportHeaderAttribute(
			"unmarked_count", "Unmarked Count", ATTENDANCE_INFO);

	public static final ReportHeaderAttribute LEAVE_COUNT = new ReportHeaderAttribute(
			"leave_count", "Leave Count", ATTENDANCE_INFO);

	public static final ReportHeaderAttribute HOLIDAY_COUNT = new ReportHeaderAttribute(
			"holiday_count", "Holiday Count", ATTENDANCE_INFO);

	public static final ReportHeaderAttribute HALF_DAY_COUNT = new ReportHeaderAttribute(
			"half_day_count", "Half Day Count", ATTENDANCE_INFO);



	private final String key;

	private final String displayName;

	private final String parentCategory;

	private final boolean mandatory;

	public ReportHeaderAttribute(String key, String displayName, String parentCategory, boolean mandatory) {
		this.key = key;
		this.displayName = displayName;
		this.parentCategory = parentCategory;
		this.mandatory = mandatory;
	}

	public ReportHeaderAttribute(String key, String displayName, String parentCategory) {
		this.key = key;
		this.displayName = displayName;
		this.parentCategory = parentCategory;
		this.mandatory = false;
	}

	public ReportHeaderAttribute markMandatory() {
		return new ReportHeaderAttribute(this.getKey(), this.getDisplayName(), this.parentCategory, true);
	}

	public ReportHeaderAttribute withDisplayName(String displayName) {
		return new ReportHeaderAttribute(this.getKey(), displayName, this.parentCategory);
	}

	public String getKey() {
		return key;
	}

	public String getDisplayName() {
		return displayName;
	}

	public boolean isMandatory() {
		return mandatory;
	}

	public String getParentCategory() {
		return parentCategory;
	}

	@Override
	public String toString() {
		return "ReportHeaderAttribute [key=" + key + ", displayName=" + displayName + ", mandatory=" + mandatory + "]";
	}
}
