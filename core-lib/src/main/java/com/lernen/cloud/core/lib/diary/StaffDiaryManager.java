package com.lernen.cloud.core.lib.diary;

import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.diary.*;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.student.DownloadDocumentWrapper;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;

import java.util.List;
import java.util.Set;
import java.util.UUID;

public class StaffDiaryManager {
    private final UserDiaryManager userDiaryManager;

    public StaffDiaryManager(UserDiaryManager userDiaryManager) {
        this.userDiaryManager = userDiaryManager;
    }

    public boolean addDiaryRemarkCategory(int instituteId, int academicSessionId, UUID userId, DiaryRemarkCategory diaryRemarkCategory) {
        return userDiaryManager.addDiaryRemarkCategory(instituteId, academicSessionId, userId, RemarkUserType.STAFF, AuthorisationRequiredAction.EDIT_STAFF_DIARY_REMARK_CATEGORY, diaryRemarkCategory);
    }

    public boolean updateDiaryRemarkCategory(int instituteId, int academicSessionId, UUID userId, DiaryRemarkCategory diaryRemarkCategory) {
        return userDiaryManager.updateDiaryRemarkCategory(instituteId, academicSessionId, userId, RemarkUserType.STAFF, AuthorisationRequiredAction.EDIT_STAFF_DIARY_REMARK_CATEGORY, diaryRemarkCategory);
    }

    public List<DiaryRemarkCategory> getDiaryRemarkCategory(int instituteId) {
        return userDiaryManager.getDiaryRemarkCategory(instituteId, RemarkUserType.STAFF);
    }

    public boolean deleteDiaryRemarkCategory(int instituteId, int academicSessionId, int categoryId, UUID userId) {
        return userDiaryManager.deleteDiaryRemarkCategory(instituteId, academicSessionId, categoryId, RemarkUserType.STAFF, userId, AuthorisationRequiredAction.EDIT_STAFF_DIARY_REMARK_CATEGORY);
    }

    public boolean addStandardRemarks(int instituteId, UUID userId, StandardRemarks standardRemarks) {
        return userDiaryManager.addStandardRemarks(instituteId, userId, RemarkUserType.STAFF, AuthorisationRequiredAction.EDIT_STAFF_DIARY_STANDARD_REMARKS, standardRemarks);
    }

    public boolean updateStandardRemarks(int instituteId, UUID userId, StandardRemarks standardRemarks) {
        return userDiaryManager.updateStandardRemarks(instituteId, userId, RemarkUserType.STAFF, AuthorisationRequiredAction.EDIT_STAFF_DIARY_STANDARD_REMARKS, standardRemarks);
    }

    public boolean deleteStandardRemarks(int instituteId, UUID userId, UUID remarkId) {
        return userDiaryManager.deleteStandardRemarks(instituteId, userId, remarkId, RemarkUserType.STAFF, AuthorisationRequiredAction.EDIT_STAFF_DIARY_STANDARD_REMARKS);
    }

    public List<StandardRemarkDetails> getStandardRemarkDetails(int instituteId, int categoryId, UUID userId) {
        return userDiaryManager.getStandardRemarkDetails(instituteId, categoryId, RemarkUserType.STAFF, userId);
    }

    public UUID addRemarks(int instituteId, int academicSessionId, UUID userId, UserDiaryRemarkDetailsPayload userDiaryRemarkDetailsPayload, List<FileData> attachments) {
        return userDiaryManager.addRemarks(instituteId, academicSessionId, userId, RemarkUserType.STAFF, UserType.STAFF, AuthorisationRequiredAction.EDIT_STAFF_DIARY_REMARKS, userDiaryRemarkDetailsPayload, attachments);
    }

    public boolean updateRemarks(int instituteId, int academicSessionId, UUID userId, UserDiaryRemarkDetailsPayload userDiaryRemarkDetailsPayload, boolean editAttachments, List<FileData> attachments) {
        return userDiaryManager.updateRemarks(instituteId, academicSessionId, userId, RemarkUserType.STAFF, UserType.STAFF, AuthorisationRequiredAction.EDIT_STAFF_DIARY_REMARKS, userDiaryRemarkDetailsPayload, editAttachments, attachments);
    }

    public boolean deleteRemarks(int instituteId, int academicSessionId, UUID userId, UUID remarkId) {
        return userDiaryManager.deleteRemarks(instituteId, academicSessionId, userId, remarkId, RemarkUserType.STAFF, AuthorisationRequiredAction.EDIT_STAFF_DIARY_REMARKS);
    }

    public List<StaffDiaryRemarkDetails> getRemarksByCreatedUserId(int instituteId, int academicSessionId, String searchText, Set<Integer> categoryIdSet, Set<UUID> createdUserIdSet) {
        return userDiaryManager.getRemarksByCreatedUserId(instituteId, academicSessionId, RemarkUserType.STAFF, searchText, categoryIdSet, createdUserIdSet);
    }

    public SearchResultWithPagination<StaffDiaryRemarkDetails> getRemarksByCreatedUserIdWithPagination(int instituteId, int academicSessionId, String searchText, Set<Integer> categoryIdInt, Set<UUID> userIdList, Integer offset, Integer limit) {
        return userDiaryManager.getRemarksByCreatedUserIdWithPagination(instituteId, academicSessionId, RemarkUserType.STAFF, searchText, categoryIdInt, userIdList, offset == null ? 0 : offset, limit == null ? 0 : limit);
    }

    public StaffDiaryRemarkDetails getDiaryRemarkDetailByRemarkId(int instituteId, UUID remarkId) {
        return userDiaryManager.getStaffDiaryRemarkDetailByRemarkId(instituteId, remarkId, RemarkUserType.STAFF);
    }

    public List<StaffDiaryRemarkDetails> getRemarksBySendToId(int instituteId, int academicSessionId, UUID sendToUUID, String searchText, Set<Integer> categoryIdSet, Set<UUID> userIdSet) {
        return userDiaryManager.getRemarksBySendToId(instituteId, academicSessionId, sendToUUID, RemarkUserType.STAFF, searchText, categoryIdSet, userIdSet);
    }

    public List<DiaryRemarkMetadata> getRemarkMetadata(int instituteId, int academicSessionId, Integer offset, Integer limit) {
        return userDiaryManager.getRemarkMetadata(instituteId, academicSessionId, RemarkUserType.STAFF, offset == null ? 0 : offset, limit == null ? 0 : limit);
    }

    public List<DiaryRemarkMetadata> getRemarkMetadata(int instituteId, int academicSessionId, UUID staffId, Integer offset, Integer limit) {
        return userDiaryManager.getRemarkMetadata(instituteId, academicSessionId, staffId, RemarkUserType.STAFF, offset == null ? 0 : offset, limit == null ? 0 : limit);
    }

    public DownloadDocumentWrapper<Document<DiaryRemarkDocumentType>> downloadDocument(int instituteId, UUID documentId, UUID remarkId) {
        return userDiaryManager.downloadDocument(instituteId, documentId, remarkId, RemarkUserType.STAFF);
    }

    public List<User> getDiaryRemarkUsers(int instituteId, int academicSessionId, UUID remarkId) {
        return userDiaryManager.getDiaryRemarkUsers(instituteId, academicSessionId, remarkId, RemarkUserType.STAFF);
    }

    public List<DatewiseDiaryMetadataDetails> getInstituteDiaryDetails(int instituteId, int academicSessionId, int offset, int limit, UUID staffId) {
        return userDiaryManager.getInstituteDiaryDetails(instituteId, academicSessionId, offset, limit, staffId, RemarkUserType.STAFF);
    }

    public List<CategorywiseDiaryMetadataDetails> getInstituteDiaryDetailsCategoryWise(int instituteId, int academicSessionId, int offset, int limit, UUID staffId) {
        return userDiaryManager.getInstituteDiaryDetailsCategoryWise(instituteId, academicSessionId, RemarkUserType.STAFF, offset, limit, staffId);
    }

    public List<DatewiseDiaryMetadataDetails> getSendDatewiseDiaryMetadataDetailsList(int instituteId, int academicSessionId, String searchText, Set<Integer> categoryIdInt, Set<UUID> createdUserIdSet, int offset, int limit) {
        return userDiaryManager.getSendDatewiseDiaryMetadataDetailsList(instituteId, academicSessionId, RemarkUserType.STAFF, searchText, categoryIdInt, createdUserIdSet, offset, limit);
    }

    public List<DatewiseDiaryMetadataDetails> getReceivedDatewiseDiaryMetadataDetailsList(int instituteId, int academicSessionId, UUID staffId, Integer offset, Integer limit) {
        return userDiaryManager.getReceivedDatewiseDiaryMetadataDetailsList(instituteId, academicSessionId, staffId, RemarkUserType.STAFF, offset == null ? 0 : offset, limit == null ? 0 : limit);
    }
}
