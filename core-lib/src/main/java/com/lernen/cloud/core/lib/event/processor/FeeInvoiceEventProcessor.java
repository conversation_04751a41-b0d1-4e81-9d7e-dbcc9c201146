package com.lernen.cloud.core.lib.event.processor;

import java.util.UUID;

import com.lernen.cloud.core.api.events.FeeInvoiceEvent;
import com.lernen.cloud.core.api.fees.payment.FeePaymentInvoiceSummary;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentManager;

/**
 *
 * <AUTHOR>
 *
 */
public class FeeInvoiceEventProcessor {

	private final FeePaymentManager feePaymentManager;

	public FeeInvoiceEventProcessor(FeePaymentManager feePaymentManager) {
		this.feePaymentManager = feePaymentManager;
	}

	public FeeInvoiceEvent generateFeeInvoiceEvent(int instituteId, UUID transactionId) {
		final FeePaymentInvoiceSummary feePaymentInvoiceSummary = feePaymentManager
				.getTransactionInvoiceSummary(instituteId, transactionId, false);

		return new FeeInvoiceEvent(feePaymentInvoiceSummary);
	}
}
