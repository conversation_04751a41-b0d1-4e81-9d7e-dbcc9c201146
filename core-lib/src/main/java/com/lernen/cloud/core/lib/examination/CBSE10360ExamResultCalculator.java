package com.lernen.cloud.core.lib.examination;

import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.ExamCourseMarks;
import com.lernen.cloud.core.api.examination.ExamDimensionObtainedValues;
import com.lernen.cloud.core.api.examination.StudentExamMarksDetailsLite;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.utils.NumberUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

public class CBSE10360ExamResultCalculator implements IExamResultCalculator {
    private static final Logger logger = LogManager.getLogger(CBSE10360ExamResultCalculator.class);

    private static final double PASSING_THRESHOLD = 0.325d;

    @Override
    public void updateExamResult(ExamReportStructure examReportStructure, ExamReportData examReportMarksData, Map<UUID, Double> studentExamWisePercentage,
                                 Map<UUID, ExamResultStatus> studentExamResultStatusMap) {

        ExamReportResultConfigs examReportResultConfigs = examReportStructure.getExamReportResultConfigs();

        if (examReportResultConfigs == null || CollectionUtils.isEmpty(examReportResultConfigs.getResultComputationGroupList())) {
            logger.error("Invalid configurations for examReportResultConfigs = {}", examReportResultConfigs);
            return;
        }

        List<StudentExamMarksDetailsLite> studentExamMarksDetailsLiteList = examReportMarksData.getStudentExamMarksDetailsLiteList();

        if (CollectionUtils.isEmpty(studentExamMarksDetailsLiteList)) {
            logger.error("No student marks list present for {}. Skipping result computation", examReportMarksData.getStudentLite().getStudentId());
            return;
        }

        ExamReportMarksGrid examReportMarksGrid = examReportMarksData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC);
        if (examReportMarksGrid == null) {
            logger.error("No scholastic grid present for {}. Skipping result computation", examReportMarksData.getStudentLite().getStudentId());
            return;
        }
        Set<UUID> additionalCourseIds = new HashSet<>();
        if(examReportStructure.getExamReportCourseStructure() != null){
            if(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) != null && !CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses())){
                additionalCourseIds.addAll(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses());
            }
            if(examReportStructure.getExamReportCourseStructure().get(CourseType.COSCHOLASTIC) != null && !CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.COSCHOLASTIC).getAdditionalCourses())){
                additionalCourseIds.addAll(examReportStructure.getExamReportCourseStructure().get(CourseType.COSCHOLASTIC).getAdditionalCourses());
            }
        }

        Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> courseExamDimensionValueMap = getCourseExamDimensionValueMap(studentExamMarksDetailsLiteList, additionalCourseIds);

        Map<UUID, Map<String, CBSE10360ExamResultCalculator.CourseGroupTotal>> courseGroupTotalMap = getCourseGroupTotalMap(examReportResultConfigs, examReportMarksGrid, courseExamDimensionValueMap, additionalCourseIds);

        computeAndUpdateResult(examReportMarksData, courseGroupTotalMap);
    }

    @Override
    public ExamResultStatus getExamResult(UUID examId, List<ExamCourseMarks> examCourseMarksList, Double percentage, Set<UUID> additionalCourseIds) {
//        throw new NotImplementedException("CBSE get exam result not implemented");
        return getExamResultStatus(percentage);
    }

    public static ExamResultStatus getExamResultStatus(Double percentage) {
        return percentage != null
                && percentage >= PASSING_THRESHOLD * 100 ? ExamResultStatus.PASS : ExamResultStatus.FAIL;
    }

    private Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> getCourseExamDimensionValueMap(List<StudentExamMarksDetailsLite> studentExamMarksDetailsLiteList, Set<UUID> additionalCourseIds) {
        Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> courseExamDimensionValueMap = new HashMap<>();
        for (StudentExamMarksDetailsLite studentExamMarksDetailsLite : studentExamMarksDetailsLiteList) {
            if (!studentExamMarksDetailsLite.getCourseMarksMatrix().containsKey(CourseType.SCHOLASTIC)) {
                continue;
            }
            UUID examId = studentExamMarksDetailsLite.getExamMetaData().getExamId();

            List<ExamCourseMarks> examCourseMarksList = studentExamMarksDetailsLite.getCourseMarksMatrix().get(CourseType.SCHOLASTIC);
            for (ExamCourseMarks examCourseMarks : examCourseMarksList) {
                UUID courseId = examCourseMarks.getCourse().getCourseId();
                if(additionalCourseIds.contains(courseId)){
                    continue;
                }
                for (ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks.getExamDimensionObtainedValues()) {
                    if (!courseExamDimensionValueMap.containsKey(courseId)) {
                        courseExamDimensionValueMap.put(courseId, new HashMap<>());
                    }
                    if (!courseExamDimensionValueMap.get(courseId).containsKey(examId)) {
                        courseExamDimensionValueMap.get(courseId).put(examId, new HashMap<>());
                    }
                    courseExamDimensionValueMap.get(courseId).get(examId).put(examDimensionObtainedValues.getExamDimension().getDimensionId(),
                            examDimensionObtainedValues);
                }
            }
        }
        return courseExamDimensionValueMap;
    }

    private Map<UUID, Map<String, CBSE10360ExamResultCalculator.CourseGroupTotal>> getCourseGroupTotalMap(ExamReportResultConfigs examReportResultConfigs, ExamReportMarksGrid examReportMarksGrid, Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> courseExamDimensionValueMap, Set<UUID> additionalCourseIds) {
        Map<UUID, Map<String, CBSE10360ExamResultCalculator.CourseGroupTotal>> courseGroupTotalMap = new HashMap<>();
        for (ExamResultComputationGroup examResultComputationGroup : examReportResultConfigs.getResultComputationGroupList()) {
            String groupId = examResultComputationGroup.getGroupId();
            for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportMarksGrid.getExamReportCourseMarksRows()) {
                UUID courseId = examReportCourseMarksRow.getCourse().getCourseId();
                if (!courseExamDimensionValueMap.containsKey(courseId) || additionalCourseIds.contains(courseId)) {
                    continue;
                }

                if (!courseGroupTotalMap.containsKey(courseId)) {
                    courseGroupTotalMap.put(courseId, new HashMap<>());
                }
                if (!courseGroupTotalMap.get(courseId).containsKey(groupId)) {
                    courseGroupTotalMap.get(courseId).put(groupId, new CBSE10360ExamResultCalculator.CourseGroupTotal(courseId, groupId));
                }

                computeCourseGroupTotal(courseExamDimensionValueMap, courseGroupTotalMap, examResultComputationGroup, groupId, courseId);
            }
        }
        return courseGroupTotalMap;
    }

    private void computeCourseGroupTotal(Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> courseExamDimensionValueMap, Map<UUID, Map<String, CBSE10360ExamResultCalculator.CourseGroupTotal>> courseGroupTotalMap, ExamResultComputationGroup examResultComputationGroup, String groupId, UUID courseId) {
        Double totalExamObtainedMarks = null;
        Double totalExamMaxMarks = null;
        Double totalExamMinMarks = null;
        for (ExamResultComputationEntry examResultComputationEntry : examResultComputationGroup.getResultComputationEntries()) {
            UUID examId = examResultComputationEntry.getExamId();
            if (!courseExamDimensionValueMap.get(courseId).containsKey(examId)) {
                continue;
            }
            Double totalDimensionObtainedMarks = null;
            Double totalDimensionMaxMarks = null;
            Double totalDimensionMinMarks = null;
            for (ExamDimensionObtainedValues examDimensionObtainedValues : courseExamDimensionValueMap.get(courseId).get(examId).values()) {
                if (examDimensionObtainedValues.getExamDimension().isTotal()) {
                    if (examResultComputationEntry.isTotalDimension()) {
                        totalDimensionObtainedMarks = examDimensionObtainedValues.getObtainedMarks();
                        totalDimensionMaxMarks = examDimensionObtainedValues.getMaxMarks();
                        totalDimensionMinMarks = examDimensionObtainedValues.getMinMarks();
                        break;
                    }
                } else {
                    totalDimensionObtainedMarks = NumberUtils.addValues(totalDimensionObtainedMarks, examDimensionObtainedValues.getObtainedMarks());
                    totalDimensionMaxMarks = NumberUtils.addValues(totalDimensionMaxMarks, examDimensionObtainedValues.getMaxMarks());
                    totalDimensionMinMarks = NumberUtils.addValues(totalDimensionMinMarks, examDimensionObtainedValues.getMinMarks());
                }
            }
            totalExamObtainedMarks = NumberUtils.addValues(totalExamObtainedMarks, totalDimensionObtainedMarks);
            totalExamMaxMarks = NumberUtils.addValues(totalExamMaxMarks, totalDimensionMaxMarks);
            totalExamMinMarks = NumberUtils.addValues(totalExamMinMarks, totalDimensionMinMarks);
        }
        CourseGroupTotal courseGroupTotal = courseGroupTotalMap.get(courseId).get(groupId);
        courseGroupTotal.setTotalObtained(NumberUtils.addValues(courseGroupTotal.getTotalObtained(), totalExamObtainedMarks));
        courseGroupTotal.setTotalMaxMarks(NumberUtils.addValues(courseGroupTotal.getTotalMaxMarks(), totalExamMaxMarks));
        courseGroupTotal.setTotalMinMarks(NumberUtils.addValues(courseGroupTotal.getTotalMinMarks(), totalExamMinMarks));
    }

    private void computeAndUpdateResult(ExamReportData examReportMarksData, Map<UUID, Map<String, CBSE10360ExamResultCalculator.CourseGroupTotal>> courseGroupTotalMap) {
        double totalGraceMarks = 5d;
        int failedCourseCount = 0;
        double totalMinMarks = 0d;
        double totalBelowPassMarks = 0d;
        for (Map.Entry<UUID, Map<String, CBSE10360ExamResultCalculator.CourseGroupTotal>> entry : courseGroupTotalMap.entrySet()) {
            for (CBSE10360ExamResultCalculator.CourseGroupTotal courseGroupTotal : entry.getValue().values()) {
                if (courseGroupTotal.getTotalObtained() != null && courseGroupTotal.getTotalMaxMarks() != null && courseGroupTotal.getTotalMaxMarks() > 0d) {
                    Double totalMinPassMarks = courseGroupTotal.getTotalMinMarks();
                    double minPassMarks = totalMinPassMarks == null ? Math.round(PASSING_THRESHOLD * courseGroupTotal.getTotalMaxMarks()) * 1d : totalMinPassMarks;
                    if (courseGroupTotal.getTotalObtained() >= minPassMarks) {
                        continue;
                    }
                    failedCourseCount++;

                    totalMinMarks += minPassMarks;
                    totalBelowPassMarks += courseGroupTotal.getTotalObtained();

                }
            }
        }

        if (failedCourseCount == 0) {
            examReportMarksData.setExamResultStatus(ExamResultStatus.PASS);
        }  else if(failedCourseCount <= 2) {
            examReportMarksData.setExamResultStatus(ExamResultStatus.COMPARTMENT);
        } else{
            examReportMarksData.setExamResultStatus(ExamResultStatus.FAIL);
        }
    }

    private class CourseGroupTotal {
        private UUID courseId;

        private String groupId;

        private Double totalObtained;

        private Double totalMaxMarks;

        private Double totalMinMarks;

        private CourseResultStatus courseResultStatus;

        public CourseGroupTotal() {
        }

        public CourseGroupTotal(UUID courseId, String groupId) {
            this.courseId = courseId;
            this.groupId = groupId;
        }

        public UUID getCourseId() {
            return courseId;
        }

        public void setCourseId(UUID courseId) {
            this.courseId = courseId;
        }

        public String getGroupId() {
            return groupId;
        }

        public void setGroupId(String groupId) {
            this.groupId = groupId;
        }

        public Double getTotalObtained() {
            return totalObtained;
        }

        public void setTotalObtained(Double totalObtained) {
            this.totalObtained = totalObtained;
        }

        public Double getTotalMaxMarks() {
            return totalMaxMarks;
        }

        public void setTotalMaxMarks(Double totalMaxMarks) {
            this.totalMaxMarks = totalMaxMarks;
        }

        public Double getTotalMinMarks() {
            return totalMinMarks;
        }

        public void setTotalMinMarks(Double totalMinMarks) {
            this.totalMinMarks = totalMinMarks;
        }

        public CourseResultStatus getCourseResultStatus() {
            return courseResultStatus;
        }

        public void setCourseResultStatus(CourseResultStatus courseResultStatus) {
            this.courseResultStatus = courseResultStatus;
        }

        @Override
        public String toString() {
            return "CourseGroupTotal{" +
                    "courseId=" + courseId +
                    ", groupId='" + groupId + '\'' +
                    ", totalObtained=" + totalObtained +
                    ", totalMaxMarks=" + totalMaxMarks +
                    ", totalMinMarks=" + totalMinMarks +
                    ", courseResultStatus=" + courseResultStatus +
                    '}';
        }
    }
}
