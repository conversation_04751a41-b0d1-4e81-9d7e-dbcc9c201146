/**
 *
 */
package com.lernen.cloud.core.lib.examination;

import com.lernen.cloud.core.api.configurations.ExaminationPreferences;
import com.lernen.cloud.core.api.examination.report.ReportCardVariableDetails;
import com.lernen.cloud.core.api.examination.report.greensheet.GreenSheetClassData;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class ExamGreenSheetHandler {

    private static final Logger logger = LogManager.getLogger(ExamGreenSheetHandler.class);

    private final ExamGreenSheetManager examGreenSheetManager;
    private final UserPreferenceSettings userPreferenceSettings;
    private final ExamReportCardManager examReportCardManager;
    private final InstituteManager instituteManager;
    private final ExamGreenSheetFactory examGreenSheetFactory;

    public ExamGreenSheetHandler(ExamGreenSheetManager examGreenSheetManager, UserPreferenceSettings userPreferenceSettings,
                                 ExamReportCardManager examReportCardManager, InstituteManager instituteManager) {
        this.examGreenSheetManager = examGreenSheetManager;
        this.userPreferenceSettings = userPreferenceSettings;
        this.examReportCardManager = examReportCardManager;
        this.instituteManager = instituteManager;
        this.examGreenSheetFactory = new ExamGreenSheetFactory();

    }

    public ReportOutput generateGreenSheet(int instituteId, int academicSessionId, UUID standardId) throws IOException {

        if (instituteId <= 0) {
            logger.error("Invalid institute.");
            throw new EmbrateRunTimeException("Invalid institute.");
        }
        ExaminationPreferences examinationPreferences = userPreferenceSettings.getExaminationPreferences(instituteId);
        if (!examinationPreferences.isGreenSheetEnabled()) {
            logger.error("Green sheet is not enabled for institute {}", instituteId);
            return null;
        }

        Institute institute = instituteManager.getInstitute(instituteId);
        if (institute == null) {
            logger.error("Invalid institute for details {}", instituteId);
            return null;
        }
        AcademicSession academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
        if (academicSession == null) {
            logger.error("Invalid academic session details for institute {}", instituteId);
            return null;
        }

        String greenSheetHeader = institute.getInstituteName() + " (" + academicSession.getShortYearDisplayName() + ")";

        final GreenSheetClassData greenSheetClassData = examGreenSheetManager.getGreenSheetClassData(instituteId,
                academicSessionId, standardId);

        /**
         * Report card type - annual
         * getting attendance details for report card type annual
         */
        final ReportCardVariableDetails reportCardVariableDetails = examReportCardManager.getStudentsReportCardVariables(instituteId,
                academicSessionId, standardId, "Annual");

        ExamGreenSheetGenerator examGreenSheetGenerator = examGreenSheetFactory.getExamGreenSheetGenerator(
                greenSheetClassData.getGreenSheetClass());

        return examGreenSheetGenerator.generateGreenSheet(greenSheetClassData, reportCardVariableDetails,
                greenSheetHeader);
    }
}

