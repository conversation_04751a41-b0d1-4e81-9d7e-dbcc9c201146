package com.lernen.cloud.core.lib.examination;

import com.lernen.cloud.core.api.examination.ExamCourseMarks;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.ExamResultStatus;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.examination.report.ExamReportStructure;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public interface IExamResultCalculator {

    void updateExamResult(ExamReportStructure examReportStructure, ExamReportData examReportMarksData, Map<UUID, Double> studentExamWisePercentage,
                          Map<UUID, ExamResultStatus> studentExamResultStatusMap);
    ExamResultStatus getExamResult(UUID examId, List<ExamCourseMarks> examCourseMarksList, Double percentage, Set<UUID> additionalCourseIds);
}
