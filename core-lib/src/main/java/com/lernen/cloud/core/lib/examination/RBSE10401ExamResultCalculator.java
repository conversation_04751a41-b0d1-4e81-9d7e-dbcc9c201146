package com.lernen.cloud.core.lib.examination;

import com.embrate.cloud.core.api.examination.utility.ExamGridRowAttribute;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.ExamCourseMarks;
import com.lernen.cloud.core.api.examination.ExamDimensionObtainedValues;
import com.lernen.cloud.core.api.examination.StudentExamMarksDetailsLite;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.utils.NumberUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

public class RBSE10401ExamResultCalculator implements IExamResultCalculator {
	private static final Logger logger = LogManager.getLogger(RBSE10401ExamResultCalculator.class);

	private static final double PASSING_THRESHOLD = 0.33d;

	private static final String DISTINCTION = "DISTINCTION";
	private static final String DIVISION_I = "I";
	private static final String DIVISION_II = "II";
	private static final String DIVISION_III = "III";
	private static final String DIVISION_IV = "IV";
	@Override
	public void updateExamResult(ExamReportStructure examReportStructure, ExamReportData examReportMarksData, Map<UUID, Double> studentExamWisePercentage,
								 Map<UUID, ExamResultStatus> studentExamResultStatusMap) {

		ExamReportResultConfigs examReportResultConfigs = examReportStructure.getExamReportResultConfigs();

		if (examReportResultConfigs == null || CollectionUtils.isEmpty(examReportResultConfigs.getResultComputationGroupList())) {
			logger.error("Invalid configurations for examReportResultConfigs = {}", examReportResultConfigs);
			return;
		}

		List<StudentExamMarksDetailsLite> studentExamMarksDetailsLiteList = examReportMarksData.getStudentExamMarksDetailsLiteList();

		if (CollectionUtils.isEmpty(studentExamMarksDetailsLiteList)) {
			logger.error("No student marks list present for {}. Skipping result computation", examReportMarksData.getStudentLite().getStudentId());
			return;
		}

		ExamReportMarksGrid examReportMarksGrid = examReportMarksData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC);
		if (examReportMarksGrid == null) {
			logger.error("No scholastic grid present for {}. Skipping result computation", examReportMarksData.getStudentLite().getStudentId());
			return;
		}

		Set<UUID> additionalCourseIds = new HashSet<>();
        if(examReportStructure.getExamReportCourseStructure() != null){
            if(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) != null && !CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses())){
                additionalCourseIds.addAll(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses());
            }
            if(examReportStructure.getExamReportCourseStructure().get(CourseType.COSCHOLASTIC) != null && !CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.COSCHOLASTIC).getAdditionalCourses())){
                additionalCourseIds.addAll(examReportStructure.getExamReportCourseStructure().get(CourseType.COSCHOLASTIC).getAdditionalCourses());
            }
        }

		Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> courseExamDimensionValueMap = getCourseExamDimensionValueMap(studentExamMarksDetailsLiteList, additionalCourseIds);

		Map<UUID, Map<String, RBSE10401ExamResultCalculator.CourseGroupTotal>> courseGroupTotalMap = getCourseGroupTotalMap(examReportResultConfigs, examReportMarksGrid, courseExamDimensionValueMap, additionalCourseIds);

		computeAndUpdateResult(examReportMarksData, courseGroupTotalMap, examReportResultConfigs);

//       examReportMarksData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC).getExamReportTotalMarksColumns();

		updateIndividualExamResult(examReportMarksData, examReportResultConfigs, courseExamDimensionValueMap, examReportMarksGrid, studentExamWisePercentage,
				studentExamResultStatusMap, additionalCourseIds);
	}

	private void updateIndividualExamResult(ExamReportData examReportMarksData, ExamReportResultConfigs examReportResultConfigs, Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> courseExamDimensionValueMap,
											ExamReportMarksGrid examReportMarksGrid, Map<UUID, Double> studentExamWisePercentage, Map<UUID, ExamResultStatus> studentExamResultStatusMap, Set<UUID> additionalCourseIds) {
		for(ExamReportHeaderStructureColumn examReportHeaderStructureColumn : examReportMarksData.getExamReportStructure().getExamReportHeaderStructureColumns().get(CourseType.SCHOLASTIC)) {

			if (examReportHeaderStructureColumn.getExamReportGridColumnType() != ExamReportGridColumnType.EXAM) {
				continue;
			}
			UUID examId = examReportHeaderStructureColumn.getExamId();
			if(examId == null) {
				continue;
			}
			String groupId = "g1";
			List<ExamResultComputationEntry> resultComputationEntries = new ArrayList<>();
			boolean totalDimension = true;
			Set<Integer> dimensions = null;
			resultComputationEntries.add(new ExamResultComputationEntry(examId, totalDimension, dimensions));

			List<ExamResultComputationGroup> resultComputationGroupList = new ArrayList<>();
			resultComputationGroupList.add(new ExamResultComputationGroup(groupId, resultComputationEntries));
			String resultCalculatorId = "";

			ExamReportResultConfigs eachExamReportResultConfigs = new ExamReportResultConfigs(resultCalculatorId, resultComputationGroupList);
			Map<UUID, Map<String, RBSE10401ExamResultCalculator.CourseGroupTotal>> totalCourseGroupTotalMap = getCourseGroupTotalMap(eachExamReportResultConfigs, examReportMarksGrid, courseExamDimensionValueMap, additionalCourseIds);

			computeAndUpdateIndividualExamResult(totalCourseGroupTotalMap, examReportResultConfigs, examReportMarksData.getCourseTypeExamReportMarksGrid(), examId, studentExamWisePercentage, studentExamResultStatusMap);

		}
	}

	@Override
	public ExamResultStatus getExamResult(UUID examId, List<ExamCourseMarks> examCourseMarksList, Double percentage, Set<UUID> additionalCourseIds) {
		Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> courseExamDimensionValueMap = new HashMap<>();

		List<ExamResultComputationGroup> resultComputationGroupList = new ArrayList<>();
		resultComputationGroupList.add(new ExamResultComputationGroup("g1", Arrays.asList(new ExamResultComputationEntry(examId, true, new HashSet<>()))));

		ExamReportResultConfigs examReportResultConfigs = new ExamReportResultConfigs(null, resultComputationGroupList);

		populateCourseExamDimensionValueMap(courseExamDimensionValueMap, examId, examCourseMarksList, additionalCourseIds);
		Map<UUID, Map<String, RBSE10401ExamResultCalculator.CourseGroupTotal>> courseGroupTotalMap = getCourseGroupTotalMap(examReportResultConfigs, examCourseMarksList, courseExamDimensionValueMap, additionalCourseIds);

		return getResult(courseGroupTotalMap, examReportResultConfigs);
	}

	private Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> getCourseExamDimensionValueMap(List<StudentExamMarksDetailsLite> studentExamMarksDetailsLiteList, Set<UUID> additionalCourseIds) {
		Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> courseExamDimensionValueMap = new HashMap<>();
		for (StudentExamMarksDetailsLite studentExamMarksDetailsLite : studentExamMarksDetailsLiteList) {
			if (!studentExamMarksDetailsLite.getCourseMarksMatrix().containsKey(CourseType.SCHOLASTIC)) {
				continue;
			}
			UUID examId = studentExamMarksDetailsLite.getExamMetaData().getExamId();

			List<ExamCourseMarks> examCourseMarksList = studentExamMarksDetailsLite.getCourseMarksMatrix().get(CourseType.SCHOLASTIC);
			populateCourseExamDimensionValueMap(courseExamDimensionValueMap, examId, examCourseMarksList, additionalCourseIds);
		}
		return courseExamDimensionValueMap;
	}

	private static void populateCourseExamDimensionValueMap(Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> courseExamDimensionValueMap, UUID examId, List<ExamCourseMarks> examCourseMarksList, Set<UUID> additionalCourseIds) {
		for (ExamCourseMarks examCourseMarks : examCourseMarksList) {
			UUID courseId = examCourseMarks.getCourse().getCourseId();
			if(additionalCourseIds.contains(courseId)){
				continue;
			}
			for (ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks.getExamDimensionObtainedValues()) {
				if (!courseExamDimensionValueMap.containsKey(courseId)) {
					courseExamDimensionValueMap.put(courseId, new HashMap<>());
				}
				if (!courseExamDimensionValueMap.get(courseId).containsKey(examId)) {
					courseExamDimensionValueMap.get(courseId).put(examId, new HashMap<>());
				}
				courseExamDimensionValueMap.get(courseId).get(examId).put(examDimensionObtainedValues.getExamDimension().getDimensionId(),
						examDimensionObtainedValues);
			}
		}
	}

	private Map<UUID, Map<String, RBSE10401ExamResultCalculator.CourseGroupTotal>> getCourseGroupTotalMap(ExamReportResultConfigs examReportResultConfigs, ExamReportMarksGrid examReportMarksGrid, Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> courseExamDimensionValueMap, Set<UUID> additionalCourseIds) {
		Map<UUID, Map<String, RBSE10401ExamResultCalculator.CourseGroupTotal>> courseGroupTotalMap = new HashMap<>();
		for (ExamResultComputationGroup examResultComputationGroup : examReportResultConfigs.getResultComputationGroupList()) {
			String groupId = examResultComputationGroup.getGroupId();
			for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportMarksGrid.getExamReportCourseMarksRows()) {
				UUID courseId = examReportCourseMarksRow.getCourse().getCourseId();
				if (!courseExamDimensionValueMap.containsKey(courseId) || additionalCourseIds.contains(courseId)) {
					continue;
				}

				if (!courseGroupTotalMap.containsKey(courseId)) {
					courseGroupTotalMap.put(courseId, new HashMap<>());
				}
				if (!courseGroupTotalMap.get(courseId).containsKey(groupId)) {
					courseGroupTotalMap.get(courseId).put(groupId, new RBSE10401ExamResultCalculator.CourseGroupTotal(courseId, groupId));
				}

				computeCourseGroupTotal(courseExamDimensionValueMap, courseGroupTotalMap, examResultComputationGroup, groupId, courseId);
			}
		}
		return courseGroupTotalMap;
	}

	private Map<UUID, Map<String, RBSE10401ExamResultCalculator.CourseGroupTotal>> getCourseGroupTotalMap(ExamReportResultConfigs examReportResultConfigs, List<ExamCourseMarks> examCourseMarksList, Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> courseExamDimensionValueMap, Set<UUID> additionalCourseIds) {
		Map<UUID, Map<String, RBSE10401ExamResultCalculator.CourseGroupTotal>> courseGroupTotalMap = new HashMap<>();
		for (ExamResultComputationGroup examResultComputationGroup : examReportResultConfigs.getResultComputationGroupList()) {
			String groupId = examResultComputationGroup.getGroupId();
			for (ExamCourseMarks examCourseMarks : examCourseMarksList) {
				UUID courseId = examCourseMarks.getCourse().getCourseId();
				if (!courseExamDimensionValueMap.containsKey(courseId) || additionalCourseIds.contains(courseId)) {
					continue;
				}

				if (!courseGroupTotalMap.containsKey(courseId)) {
					courseGroupTotalMap.put(courseId, new HashMap<>());
				}
				if (!courseGroupTotalMap.get(courseId).containsKey(groupId)) {
					courseGroupTotalMap.get(courseId).put(groupId, new RBSE10401ExamResultCalculator.CourseGroupTotal(courseId, groupId));
				}

				computeCourseGroupTotal(courseExamDimensionValueMap, courseGroupTotalMap, examResultComputationGroup, groupId, courseId);
			}
		}
		return courseGroupTotalMap;
	}

	private void computeCourseGroupTotal(Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> courseExamDimensionValueMap, Map<UUID, Map<String, RBSE10401ExamResultCalculator.CourseGroupTotal>> courseGroupTotalMap, ExamResultComputationGroup examResultComputationGroup, String groupId, UUID courseId) {
		Double totalExamObtainedMarks = null;
		Double totalExamMaxMarks = null;
		Double totalExamMinMarks = null;
		for (ExamResultComputationEntry examResultComputationEntry : examResultComputationGroup.getResultComputationEntries()) {
			UUID examId = examResultComputationEntry.getExamId();
			if (!courseExamDimensionValueMap.get(courseId).containsKey(examId)) {
				continue;
			}
			Double totalDimensionObtainedMarks = null;
			Double totalDimensionMaxMarks = null;
			Double totalDimensionMinMarks = null;
			for (ExamDimensionObtainedValues examDimensionObtainedValues : courseExamDimensionValueMap.get(courseId).get(examId).values()) {
				if (examDimensionObtainedValues.getExamDimension().isTotal()) {
					if (examResultComputationEntry.isTotalDimension()) {
						totalDimensionObtainedMarks = examDimensionObtainedValues.getObtainedMarks();
						totalDimensionMaxMarks = examDimensionObtainedValues.getMaxMarks();
						totalDimensionMinMarks = examDimensionObtainedValues.getMinMarks();
						break;
					}
				} else {
					totalDimensionObtainedMarks = NumberUtils.addValues(totalDimensionObtainedMarks, examDimensionObtainedValues.getObtainedMarks());
					totalDimensionMaxMarks = NumberUtils.addValues(totalDimensionMaxMarks, examDimensionObtainedValues.getMaxMarks());
					totalDimensionMinMarks = NumberUtils.addValues(totalDimensionMinMarks, examDimensionObtainedValues.getMinMarks());
				}
			}
			totalExamObtainedMarks = NumberUtils.addValues(totalExamObtainedMarks, totalDimensionObtainedMarks);
			totalExamMaxMarks = NumberUtils.addValues(totalExamMaxMarks, totalDimensionMaxMarks);
			totalExamMinMarks = NumberUtils.addValues(totalExamMinMarks, totalDimensionMinMarks);
		}
		RBSE10401ExamResultCalculator.CourseGroupTotal courseGroupTotal = courseGroupTotalMap.get(courseId).get(groupId);
		courseGroupTotal.setTotalObtained(NumberUtils.addValues(courseGroupTotal.getTotalObtained(), totalExamObtainedMarks));
		courseGroupTotal.setTotalMaxMarks(NumberUtils.addValues(courseGroupTotal.getTotalMaxMarks(), totalExamMaxMarks));
		courseGroupTotal.setTotalMinMarks(NumberUtils.addValues(courseGroupTotal.getTotalMinMarks(), totalExamMinMarks));
	}

	private void computeAndUpdateIndividualExamResult(Map<UUID, Map<String, RBSE10401ExamResultCalculator.CourseGroupTotal>> courseGroupTotalMap, ExamReportResultConfigs examReportResultConfigs,
													  Map<CourseType, ExamReportMarksGrid> examReportMarksGridMap, UUID examId, Map<UUID, Double> studentExamWisePercentage,
													  Map<UUID, ExamResultStatus> studentExamResultStatusMap) {

		if(examReportMarksGridMap.get(CourseType.SCHOLASTIC) == null){
			return;
		}

		ExamResultStatus examResultStatus = getResult(courseGroupTotalMap, examReportResultConfigs);
		Double percentage = getPercentage(courseGroupTotalMap);

		if(!studentExamWisePercentage.containsKey(examId)) {
			studentExamWisePercentage.put(examId, percentage);
		}

		if(!studentExamResultStatusMap.containsKey(examId)) {
			studentExamResultStatusMap.put(examId, examResultStatus);
		}

		for (ExamReportAdditionalAttributesRow examReportAdditionalAttributesRow : examReportMarksGridMap.get(CourseType.SCHOLASTIC).getExamReportAdditionalAttributesRows()) {
			if (examReportAdditionalAttributesRow.getAttribute() == ExamGridRowAttribute.RESULT) {
				for (ExamReportMarksColumn examReportMarksColumn : examReportAdditionalAttributesRow.getExamReportMarksColumns()) {
					if(examReportMarksColumn.isHide()) {
						continue;
					}
					if (examReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM) {
						if(!examReportMarksColumn.getExamMetaData().getExamId().equals(examId)) {
							continue;
						}
						examReportMarksColumn.setColumnValue(percentage == null || examResultStatus == null ? null : examResultStatus.getDisplayName());
					}
				}
			}

			if (examReportAdditionalAttributesRow.getAttribute() == ExamGridRowAttribute.DIVISION) {
				for (ExamReportMarksColumn examReportMarksColumn : examReportAdditionalAttributesRow.getExamReportMarksColumns()) {
					if(examReportMarksColumn.isHide()) {
						continue;
					}
					if (examReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM) {
						if(!examReportMarksColumn.getExamMetaData().getExamId().equals(examId)) {
							continue;
						}
						examReportMarksColumn.setColumnValue(examResultStatus == null || examResultStatus == ExamResultStatus.FAIL ? "NA" : getDivision(percentage));
					}
				}
			}

		}
	}

	private void computeAndUpdateResult(ExamReportData examReportMarksData, Map<UUID, Map<String, RBSE10401ExamResultCalculator.CourseGroupTotal>> courseGroupTotalMap, ExamReportResultConfigs examReportResultConfigs) {
		ExamResultStatus examResultStatus = getResult(courseGroupTotalMap, examReportResultConfigs);
		examReportMarksData.setExamResultStatus(examResultStatus);
	}

	private ExamResultStatus getResult(Map<UUID, Map<String, RBSE10401ExamResultCalculator.CourseGroupTotal>> courseGroupTotalMap, ExamReportResultConfigs examReportResultConfigs) {
		double totalGraceMarks = examReportResultConfigs.getGraceMarks() == null ? 0d : examReportResultConfigs.getGraceMarks();
		int failedCourseCountThreshold = examReportResultConfigs.getFailedCourseThreshold() == null ? 0 : examReportResultConfigs.getFailedCourseThreshold();
		int failedCourseCount = 0;
		double totalMinMarks = 0d;
		double totalBelowPassMarks = 0d;
		for (Map.Entry<UUID, Map<String, RBSE10401ExamResultCalculator.CourseGroupTotal>> entry : courseGroupTotalMap.entrySet()) {
			for (RBSE10401ExamResultCalculator.CourseGroupTotal courseGroupTotal : entry.getValue().values()) {
				if (courseGroupTotal.getTotalObtained() == null && courseGroupTotal.getTotalMaxMarks() != null) {
					failedCourseCount++;
				} else if (courseGroupTotal.getTotalObtained() != null && courseGroupTotal.getTotalMaxMarks() != null && courseGroupTotal.getTotalMaxMarks() > 0d) {
					Double totalMinPassMarks = courseGroupTotal.getTotalMinMarks();
					double minPassMarks = totalMinPassMarks == null ? Math.round(PASSING_THRESHOLD * courseGroupTotal.getTotalMaxMarks()) * 1d : totalMinPassMarks;
					if (courseGroupTotal.getTotalObtained() >= minPassMarks) {
						continue;
					}
					failedCourseCount++;

					totalMinMarks += minPassMarks;
					totalBelowPassMarks += courseGroupTotal.getTotalObtained();

				}
			}
		}

		if (failedCourseCount == 0) {
			return ExamResultStatus.PASS;
		} else if (failedCourseCount <= 2) {
			return ExamResultStatus.COMPARTMENT;
		} else {
			return ExamResultStatus.FAIL;
		}
	}

	private Double getPercentage(Map<UUID, Map<String, RBSE10401ExamResultCalculator.CourseGroupTotal>> courseGroupTotalMap) {
		Double totalMarks = 0d;
		Double obtainedMarks = 0d;
		for (Map.Entry<UUID, Map<String, RBSE10401ExamResultCalculator.CourseGroupTotal>> entry : courseGroupTotalMap.entrySet()) {
			for (RBSE10401ExamResultCalculator.CourseGroupTotal courseGroupTotal : entry.getValue().values()) {
				totalMarks = NumberUtils.addValues(totalMarks, courseGroupTotal.getTotalMaxMarks());
				obtainedMarks = NumberUtils.addValues(obtainedMarks, courseGroupTotal.getTotalObtained());
			}
		}

		if (obtainedMarks == null ||totalMarks == null
				|| Double.compare(totalMarks, 0) <= 0) {
			return null;
		}
		Double fraction = obtainedMarks / totalMarks;

		return fraction == null ? null : Math.round(fraction * 10000) / 100d;
	}

	protected String getDivision(Double percentage) {
		if (percentage == null) {
			return "";
		}
		if (percentage >= 60.0 && percentage <= 100) {
			return DIVISION_I;
		} else if (percentage >= 45.0 && percentage < 60.0) {
			return DIVISION_II;
		} else if (percentage >= 36.0 && percentage < 45.0) {
			return DIVISION_III;
		}
		return DIVISION_IV;
	}

	private class CourseGroupTotal {
		private UUID courseId;

		private String groupId;

		private Double totalObtained;

		private Double totalMaxMarks;

		private Double totalMinMarks;

		private CourseResultStatus courseResultStatus;

		public CourseGroupTotal() {
		}

		public CourseGroupTotal(UUID courseId, String groupId) {
			this.courseId = courseId;
			this.groupId = groupId;
		}

		public UUID getCourseId() {
			return courseId;
		}

		public void setCourseId(UUID courseId) {
			this.courseId = courseId;
		}

		public String getGroupId() {
			return groupId;
		}

		public void setGroupId(String groupId) {
			this.groupId = groupId;
		}

		public Double getTotalObtained() {
			return totalObtained;
		}

		public void setTotalObtained(Double totalObtained) {
			this.totalObtained = totalObtained;
		}

		public Double getTotalMaxMarks() {
			return totalMaxMarks;
		}

		public void setTotalMaxMarks(Double totalMaxMarks) {
			this.totalMaxMarks = totalMaxMarks;
		}

		public CourseResultStatus getCourseResultStatus() {
			return courseResultStatus;
		}

		public void setCourseResultStatus(CourseResultStatus courseResultStatus) {
			this.courseResultStatus = courseResultStatus;
		}

		public Double getTotalMinMarks() {
			return totalMinMarks;
		}

		public void setTotalMinMarks(Double totalMinMarks) {
			this.totalMinMarks = totalMinMarks;
		}

		@Override
		public String toString() {
			return "CourseGroupTotal{" +
					"courseId=" + courseId +
					", groupId='" + groupId + '\'' +
					", totalObtained=" + totalObtained +
					", totalMaxMarks=" + totalMaxMarks +
					", totalMinMarks=" + totalMinMarks +
					", courseResultStatus=" + courseResultStatus +
					'}';
		}
	}
}
