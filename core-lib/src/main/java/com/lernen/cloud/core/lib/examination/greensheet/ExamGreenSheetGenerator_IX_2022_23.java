package com.lernen.cloud.core.lib.examination.greensheet;

import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.report.ExamResultStatus;
import com.lernen.cloud.core.api.examination.report.ReportCardVariableDetails;
import com.lernen.cloud.core.api.examination.report.StudentReportCardVariableDetails;
import com.lernen.cloud.core.api.examination.report.greensheet.*;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.lib.examination.ExamGreenSheetGenerator;
import com.lernen.cloud.core.lib.examination.ExamMarksUtils;
import com.lernen.cloud.core.utils.examination.GreenSheetProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.util.*;

/**
 * <AUTHOR>
 */

public class ExamGreenSheetGenerator_IX_2022_23 extends ExamGreenSheetGenerator {

    private static final Double TOTAL_MARKS_IX = 1200d;
    private static final Integer TOTAL_LINE_SPACING_IX = 25;
    private static final Integer START_ROW_IX = 9;
    private static final Integer START_COLUMN_2nd_ROW_IX = 5;
    private static final Integer START_ROW_COURSE_WISE_MAP_IX = 57;
    private static final Integer START_COLUMN_SCHOLASTIC_DIVISION_MAP_IX = 7;
    private static final Integer START_COLUMN_COSCHOLASTIC_DIVISION_MAP_IX = 43;
    private static final Integer START_COLUMN_COSCHOLASTIC_GRADE_MAP_IX = 60;

    @Override
    public ReportOutput generateGreenSheet(GreenSheetClassData greenSheetClassData, ReportCardVariableDetails reportCardVariableDetails,
                                           String greenSheetHeader) throws IOException {

        GreenSheetListClassName greenSheetListClassName = getGreenSheetData(greenSheetClassData, reportCardVariableDetails);

        if(greenSheetListClassName == null) {
            logger.error("Invalid Green sheet data");
            return null;
        }

        List<List<GreenSheetColumn>> greenSheetData = greenSheetListClassName.getGreenSheetColumnList();

        GreenSheetClass greenSheetClass = greenSheetListClassName.getGreenSheetClass();

        String standardName = greenSheetClassData.getStandard().getDisplayName();

        InputStream fsIP = null;
        try {
            fsIP = GreenSheetProvider.INSTANCE.getGreenSheet(GreenSheetProvider.IX_GREEN_SHEET);
        } catch (URISyntaxException | IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        Workbook wb = new XSSFWorkbook(fsIP);

        wb.cloneSheet(0);

        int row = START_ROW_IX;
        int currentRow = row - 1;
        Cell cell = null;
        int sheetNumber = 1;
        int studentCount = 0;
        for (List<GreenSheetColumn> studentDataGreenSheet : greenSheetData) {
            studentCount++;
            Sheet worksheet = wb.getSheetAt(sheetNumber);
            row = currentRow + 1;
            int col = 0;
            for (GreenSheetColumn value : studentDataGreenSheet) {
                String cellValue = value.getResolvedValue();
                Map<Integer, Integer> mergedCells = new HashMap<>();
                for(int i = 0; i < worksheet.getNumMergedRegions(); ++i) {
                    CellRangeAddress range = worksheet.getMergedRegion(i);
                    if (range.getFirstRow() <= row && range.getLastRow() >= row) {
                        mergedCells.put(range.getFirstColumn(), range.getLastColumn());
                    }
                }
                cell = worksheet.getRow(row).getCell(col);
                if(cell == null) {
                    String snNo = worksheet.getRow(row).getCell(0).getStringCellValue();
                    String name = worksheet.getRow(row).getCell(4).getStringCellValue();
                    currentRow = row;
                    row = currentRow + TOTAL_LINE_SPACING_IX;
                    worksheet.getRow(row).getCell(0).setCellValue(snNo);
                    worksheet.getRow(row).getCell(1).setCellValue(name);
                    col = START_COLUMN_2nd_ROW_IX;
                    cell = worksheet.getRow(row).getCell(col);
                }
                cell.setCellValue(cellValue);
                if(mergedCells.containsKey(col)) {
                    col = col + (mergedCells.get(col) - col);
                }
                col++;
            }
            row++;
            if (studentCount % PER_SHEET_STUDENT_COUNT == 0) {
                sheetNumber++;
                row = START_ROW_IX;
                currentRow = row - 1;
                cell = null;
                wb.cloneSheet(0);
            }
        }
        wb.removeSheetAt(0);

        for(int i = 0; i < wb.getNumberOfSheets(); i++) {
            addInstituteNameOnSheet(wb.getSheetAt(i), greenSheetHeader);
            addClassNameOnSheet(wb.getSheetAt(i), standardName.contains("Class") ? standardName : "Class " + standardName);
//            addTotalDetailsBottom(wb.getSheetAt(i), studentCount, rowStart, columnStart);

            cell = wb.getSheetAt(i).getRow(56).getCell(2);
            cell.setCellValue(studentCount);
            cell = wb.getSheetAt(i).getRow(57).getCell(2);
            cell.setCellValue(studentCount);
            cell = wb.getSheetAt(i).getRow(58).getCell(2);
            cell.setCellValue(studentCount);
            cell = wb.getSheetAt(i).getRow(59).getCell(2);
            cell.setCellValue(studentCount);
            cell = wb.getSheetAt(i).getRow(60).getCell(2);
            cell.setCellValue("100%");

            addScholasticSubjectDivisionDetails(wb.getSheetAt(i),
                    greenSheetListClassName.getCourseDivisionMap().get(CourseType.SCHOLASTIC),
                    greenSheetClass, CourseType.SCHOLASTIC, START_ROW_COURSE_WISE_MAP_IX, START_COLUMN_SCHOLASTIC_DIVISION_MAP_IX);
            addScholasticSubjectDivisionDetails(wb.getSheetAt(i),
                    greenSheetListClassName.getCourseDivisionMap().get(CourseType.COSCHOLASTIC),
                    greenSheetClass, CourseType.COSCHOLASTIC, START_ROW_COURSE_WISE_MAP_IX, START_COLUMN_COSCHOLASTIC_DIVISION_MAP_IX);
            addCoScholasticSubjectGradeDetails(wb.getSheetAt(i),
                    greenSheetListClassName.getCourseGradeMap().get(CourseType.COSCHOLASTIC), greenSheetClass);
            addGreenSheetDate(wb.getSheetAt(i), 61, 73, greenSheetListClassName.getResultDeclarationDate());//BU62
            addTotalAttendedDays(wb.getSheetAt(i), 33, 69, greenSheetListClassName.getTotalAttendanceDays());// BS34
        }

        String reportName = "greenSheetClass_" + greenSheetClass + ".xlsx";
        return getReportOutput(wb, reportName);

    }

    protected void addScholasticSubjectDivisionDetails(Sheet worksheet, LinkedHashMap<String, LinkedHashMap<String, Integer>> courseDivisionMap,
                                                       GreenSheetClass greenSheetClass, CourseType courseType,
                                                       int startRow, int startColumn) {

        int subjectCount = 0;
        for(Map.Entry<String, LinkedHashMap<String, Integer>> courseGradeMapEntry : courseDivisionMap.entrySet()) {
            subjectCount++;
            if(courseType == CourseType.COSCHOLASTIC && subjectCount > 3) {
                break;
            }
            int row = startRow;
            int col = startColumn;
            int totalStudents = 0;

            int index = 0;

            for (Map.Entry<String, Integer> gradeMapEntry : courseGradeMapEntry.getValue().entrySet()) {
                index++;
                Cell cell = worksheet.getRow(row).getCell(col);
                totalStudents += gradeMapEntry.getValue();
                cell.setCellValue(gradeMapEntry.getValue());
                if(index == 3) {
                    break;
                }
                col += 2;
            }

            Cell cell = worksheet.getRow(row + 2).getCell(col - 5);
            cell.setCellValue(totalStudents);

            cell = worksheet.getRow(row - 1).getCell(col - 5);
            cell.setCellValue(totalStudents);

            col += 2;

            startRow = row;
            startColumn = col;

        }
    }

    protected void addCoScholasticSubjectGradeDetails(Sheet worksheet, LinkedHashMap<String, LinkedHashMap<String, Integer>> courseGradeMap, GreenSheetClass greenSheetClass) {
        int startRow = START_ROW_COURSE_WISE_MAP_IX + 1;
        int startColumn = START_COLUMN_COSCHOLASTIC_GRADE_MAP_IX;

        int subjectCount = 0;
        for(Map.Entry<String, LinkedHashMap<String, Integer>> courseGradeMapEntry : courseGradeMap.entrySet()) {
            subjectCount++;
            if(subjectCount <= 3) {
                continue;
            }
            int row = startRow;
            int col = startColumn;
            int index = 0;
            int totalStudents = 0;

            for (Map.Entry<String, Integer> gradeMapEntry : courseGradeMapEntry.getValue().entrySet()) {

                Cell cell = worksheet.getRow(row).getCell(col);
                totalStudents += gradeMapEntry.getValue();
                cell.setCellValue(gradeMapEntry.getValue());
                col++;
                index++;

                if(index >= 5) {
                    break;
                }
            }

            Cell cell = worksheet.getRow(row).getCell(col);
            cell.setCellValue(totalStudents);

            cell = worksheet.getRow(row + 1).getCell(col - 5);
            cell.setCellValue(totalStudents);

            cell = worksheet.getRow(row - 2).getCell(col - 5);
            cell.setCellValue(totalStudents);

            col++;
            index++;

            startRow = row;
            startColumn = col;

        }
    }

    public ReportOutput getReportOutput(Workbook workbook, String reportName) throws IOException {
        final ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        workbook.close();
        return new ReportOutput(reportName, bos);
    }

    protected GreenSheetListClassName getGreenSheetData(GreenSheetClassData greenSheetClassData,
                                                        ReportCardVariableDetails reportCardVariableDetails) {

        Map<UUID, StudentReportCardVariableDetails> studentReportCardVariableDetailsMap =
                getStudentReportCardVariableDetails(reportCardVariableDetails);

        if(greenSheetClassData == null || CollectionUtils.isEmpty(greenSheetClassData.getStudentGreenSheetDataList())) {
            return null;
        }

        LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> courseTypeGradeMap = defineStaticCourseGradeMap(
                greenSheetClassData.getStudentGreenSheetDataList().get(0).getGreenSheetCourseExamMarksList());

        LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> courseTypeDivisionMap = defineStaticDivisionGradeMap(
                greenSheetClassData.getStudentGreenSheetDataList().get(0).getGreenSheetCourseExamMarksList());

        GreenSheetClass greenSheetClassName = greenSheetClassData.getGreenSheetClass();
        List<List<GreenSheetColumn>> greenSheetData = new ArrayList<>();
        Integer index = 1;
        Map<Integer, Double> studentPercentMap = new HashMap<>();
        String totalDays = "";
        Integer resultDeclareDate = null;
        for (StudentGreenSheetData studentGreenSheetData : greenSheetClassData.getStudentGreenSheetDataList()) {

            List<GreenSheetColumn> studentData = new ArrayList<>();
            studentData.add(new GreenSheetColumn(null, index.toString(), ColumnType.STATIC));

            //basic info
            studentData.addAll(getStudentBasicDetails(studentGreenSheetData));

            //scholastic portion
            GreenSheetSubjectWiseData greenSheetSubjectWiseData = getStudentScholasticDetails(greenSheetClassName,
                    studentGreenSheetData, courseTypeGradeMap.get(CourseType.SCHOLASTIC),
                    courseTypeDivisionMap.get(CourseType.SCHOLASTIC));
            if(greenSheetSubjectWiseData != null && !CollectionUtils.isEmpty(greenSheetSubjectWiseData.getGreenSheetDataStudentLevel())) {
                studentData.addAll(greenSheetSubjectWiseData.getGreenSheetDataStudentLevel());
            }

            String distinctionSubjects = greenSheetSubjectWiseData.getDistinctionCourses();

            //total portion of scholastic part
            studentData.add(new GreenSheetColumn(greenSheetSubjectWiseData.getTotalMarks(), null, ColumnType.RAW));
            Double totalMarks = TOTAL_MARKS_IX;
            Double percentage = getPercentage(greenSheetSubjectWiseData.getTotalMarks(), totalMarks);
            studentPercentMap.put(index  - 1, percentage);

            //Vivaran portion
            studentData.add(new GreenSheetColumn(null, "", ColumnType.STATIC));
//            studentData.add(new GreenSheetColumn(percentage, null, ColumnType.PERCENTAGE));
//            studentData.add(new GreenSheetColumn(null, getGrade(percentage), ColumnType.GRADE));

            //co-scholastic portion
            greenSheetSubjectWiseData = getStudentCoScholasticDetails(greenSheetClassName, studentGreenSheetData,
                    courseTypeGradeMap.get(CourseType.COSCHOLASTIC), courseTypeDivisionMap.get(CourseType.COSCHOLASTIC));
            if(greenSheetSubjectWiseData != null && !CollectionUtils.isEmpty(greenSheetSubjectWiseData.getGreenSheetDataStudentLevel())) {
                studentData.addAll(greenSheetSubjectWiseData.getGreenSheetDataStudentLevel());
            }

            //add result details
            final ExamResultStatus examResultStatus = percentage != null
                    && percentage >= PASSING_THRESHOLD * 100 ? ExamResultStatus.PASS
                    : ExamResultStatus.FAIL;
            studentData.add(new GreenSheetColumn(null, examResultStatus.getDisplayName(), ColumnType.STATIC));

            //add division details
            studentData.add(new GreenSheetColumn(null, getDivision(percentage), ColumnType.STATIC));

            //Rank
            studentData.add(new GreenSheetColumn(null, "", ColumnType.RANK));

            //Distinction Courses
            studentData.add(new GreenSheetColumn(null, distinctionSubjects, ColumnType.STATIC));

            //add attended days details
            UUID studentId = studentGreenSheetData.getStudent().getStudentId();
            String attendedDays = studentReportCardVariableDetailsMap == null ? "" :
                    studentReportCardVariableDetailsMap.get(studentId) == null ? "" :
                            studentReportCardVariableDetailsMap.get(studentId).getAttendedDays() == null ?
                                    "" : String.valueOf(Math.round(studentReportCardVariableDetailsMap.get(studentId).getAttendedDays()));
            studentData.add(new GreenSheetColumn(null, attendedDays, ColumnType.STATIC));

            if(StringUtils.isBlank(totalDays)){
                totalDays =  studentReportCardVariableDetailsMap == null ? "" :
                        studentReportCardVariableDetailsMap.get(studentId) == null ? "" :
                                studentReportCardVariableDetailsMap.get(studentId).getTotalDays() == null ?
                                        "" : String.valueOf(Math.round(studentReportCardVariableDetailsMap.get(studentId).getTotalDays()));
            }

            if(resultDeclareDate == null){
                resultDeclareDate =  studentReportCardVariableDetailsMap == null ? null :
                        studentReportCardVariableDetailsMap.get(studentId) == null ? null :
                                studentReportCardVariableDetailsMap.get(studentId).getDateOfResultDeclaration() == null ?
                                        null : studentReportCardVariableDetailsMap.get(studentId).getDateOfResultDeclaration();
            }

            greenSheetData.add(studentData);
            index++;
        }

        populateStudentRank(greenSheetData, studentPercentMap);

        return new GreenSheetListClassName(greenSheetClassName, greenSheetData, courseTypeGradeMap,
                courseTypeDivisionMap, totalDays, resultDeclareDate);
    }

    protected GreenSheetSubjectWiseData getStudentScholasticDetails(GreenSheetClass greenSheetClassName,
                                                                    StudentGreenSheetData studentGreenSheetData, LinkedHashMap<String, LinkedHashMap<String, Integer>> courseGradeMap,
                                                                    LinkedHashMap<String, LinkedHashMap<String, Integer>> courseDivisionMap) {
        List<GreenSheetColumn> subjectWiseData = new ArrayList<>();
        Double obtainedTotalMarks = null;
        String distinctionSubjects = "";
        boolean first = true;
        for(GreenSheetCourseExamMarks greenSheetCourseExamMarks : studentGreenSheetData.getGreenSheetCourseExamMarksList()) {
            if(greenSheetCourseExamMarks.getCourseType() == CourseType.SCHOLASTIC) {
                List<GreenSheetTotalObtainedMarks> rawMarks = getSubjectWiseRawMarks(greenSheetCourseExamMarks.getGreenSheetExamMarksList());
                List<GreenSheetNode> structure = getGreenSheetNodeScholasticIX();
                GreenSheetSubjectWiseData greenSheetSubjectWiseData = getGreenSheetSubjectData(structure, rawMarks,
                        greenSheetClassName);
                subjectWiseData.addAll(greenSheetSubjectWiseData.getGreenSheetDataStudentLevel());
                obtainedTotalMarks = ExamMarksUtils.addValues(greenSheetSubjectWiseData.getTotalMarks(), obtainedTotalMarks);

                String courseName = greenSheetCourseExamMarks.getCourseName();
                if(StringUtils.isBlank(courseName)) {
                    continue;
                }

                //code for populating courseDivisionMap for scholastic courses
                GreenSheetColumn greenSheetColumn = greenSheetSubjectWiseData.getGreenSheetDataStudentLevel().get(
                        greenSheetSubjectWiseData.getGreenSheetDataStudentLevel().size() - 1);
                Double totalMarks = greenSheetColumn.getMarks();
                if(totalMarks == null || totalMarks <= 0) {
                    continue;
                }
                Double percentage = getPercentage(totalMarks, 200d);
                String division = getDivision(percentage);
                if(percentage >= 86.0) {
                    if(first) {
                        distinctionSubjects += getShortCourseName(greenSheetCourseExamMarks.getCourseName());
                        first = false;
                    } else {
                        distinctionSubjects +=  ", " + getShortCourseName(greenSheetCourseExamMarks.getCourseName());
                    }
                }

                if(!StringUtils.isBlank(division)) {
                    int currentCount = courseDivisionMap.get(courseName).get(division);
                    courseDivisionMap.get(courseName).put(division, currentCount + 1);
                }

            }
        }
        return new GreenSheetSubjectWiseData(subjectWiseData, obtainedTotalMarks, distinctionSubjects);
    }

    protected GreenSheetSubjectWiseData getStudentCoScholasticDetails(GreenSheetClass greenSheetClassName,
                                                                      StudentGreenSheetData studentGreenSheetData, LinkedHashMap<String, LinkedHashMap<String, Integer>> courseGradeMap,
                                                                      LinkedHashMap<String, LinkedHashMap<String, Integer>> courseDivisionMap) {
        List<GreenSheetColumn> subjectWiseData = new ArrayList<>();
        Double obtainedTotalMarks = null;
        int subjectCount = 0;
        for(GreenSheetCourseExamMarks greenSheetCourseExamMarks : studentGreenSheetData.getGreenSheetCourseExamMarksList()) {
            if(greenSheetCourseExamMarks.getCourseType() == CourseType.COSCHOLASTIC) {
                subjectCount++;
                List<GreenSheetTotalObtainedMarks> rawMarks = getSubjectWiseRawMarks(greenSheetCourseExamMarks.getGreenSheetExamMarksList());
                List<GreenSheetNode> structure = null;
                if(subjectCount == 1) {
                    structure = getGreenSheetNodeScholasticIX();
                } else if(subjectCount == 2) {
                    structure = getGreenSheetNodeCoScholasticSubjectII();
                } else if(subjectCount == 3) {
                    structure = getGreenSheetNodeCoScholasticSubjectIII();
                } else if(subjectCount == 4) {
                    structure = getGreenSheetNodeCoScholasticSubjectIV();
                } else if(subjectCount == 5) {
                    structure = getGreenSheetNodeCoScholasticSubjectV();
                }
                if(structure == null) {
                    new GreenSheetSubjectWiseData(subjectWiseData, obtainedTotalMarks, null);
                }
                GreenSheetSubjectWiseData greenSheetSubjectWiseData = getGreenSheetSubjectData(structure, rawMarks,
                        greenSheetClassName);
                subjectWiseData.addAll(greenSheetSubjectWiseData.getGreenSheetDataStudentLevel());
                obtainedTotalMarks = ExamMarksUtils.addValues(greenSheetSubjectWiseData.getTotalMarks(), obtainedTotalMarks);

                String courseName = greenSheetCourseExamMarks.getCourseName();
                if(StringUtils.isBlank(courseName)) {
                    continue;
                }

                GreenSheetColumn greenSheetColumn = greenSheetSubjectWiseData.getGreenSheetDataStudentLevel().get(
                        greenSheetSubjectWiseData.getGreenSheetDataStudentLevel().size() - 1);
                if(subjectCount > 3) {
                    //code for populating courseGradeMap for scholastic courses
                    String grade = greenSheetColumn.getValue();
                    if(StringUtils.isBlank(grade)) {
                        continue;
                    }
                    int currentCount = courseGradeMap.get(courseName).get(grade);
                    courseGradeMap.get(courseName).put(grade, currentCount + 1);
                } else {
                    //code for populating courseDivisionMap for scholastic courses
                    Double totalMarks = greenSheetColumn.getMarks();
                    if(totalMarks == null || totalMarks <= 0) {
                        continue;
                    }
                    Double percentage = getPercentage(totalMarks, 200d);
                    String division = getDivision(percentage);

                    if(!StringUtils.isBlank(division)) {
                        int currentCount = courseDivisionMap.get(courseName).get(division);
                        courseDivisionMap.get(courseName).put(division, currentCount + 1);
                    }
                }


            }
        }
        return new GreenSheetSubjectWiseData(subjectWiseData, obtainedTotalMarks, null);
    }

    protected List<GreenSheetNode> getGreenSheetNodeScholasticIX() {
        List<GreenSheetNode> greenSheetNode = new ArrayList<>();

        //Terms
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 0
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 1
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 2
        List<Integer> indexes = new ArrayList<>();
        indexes.add(0);
        indexes.add(1);
        indexes.add(2);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 3

        //Half Yearly
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 4

        //Total till now
        indexes = new ArrayList<>();
        indexes.add(3);
        indexes.add(4);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 5

        //Yearly
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 6

        //total tillnow
        indexes = new ArrayList<>();
        indexes.add(5);
        indexes.add(6);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 7

        return greenSheetNode;
    }

    protected GreenSheetSubjectWiseData getGreenSheetSubjectData(List<GreenSheetNode> structure,
                                                                 List<GreenSheetTotalObtainedMarks> rawMarks,
                                                                 GreenSheetClass greenSheetClass) {
        List<GreenSheetColumn> greenSheetDataStudentLevel = new ArrayList<>();
        List<GreenSheetColumn> greenSheetDataStudentLevelTotalMarks = new ArrayList<>();

        List<Double> obtainedMarksList = new ArrayList<>();
        List<Double> totalMarksList = new ArrayList<>();

        for (GreenSheetTotalObtainedMarks greenSheetTotalObtainedMarks : rawMarks) {
            obtainedMarksList.add(greenSheetTotalObtainedMarks.getObtainedMarks());
            totalMarksList.add(greenSheetTotalObtainedMarks.getTotalMarks());
        }

        int index = 0;
        int count = 0;
        Double obtainedTotalMarks = null;
        for (GreenSheetNode greenSheetNode : structure) {
            if(greenSheetNode.getRawValue()) {
                greenSheetDataStudentLevel.add(new GreenSheetColumn(obtainedMarksList.get(index), null, ColumnType.RAW));
                greenSheetDataStudentLevelTotalMarks.add(new GreenSheetColumn(totalMarksList.get(index), null, ColumnType.RAW));
                index++;
            }
            else {
                if(greenSheetNode.getTypeOfColumn() == ColumnType.GRADE) {
                    int indexes = greenSheetNode.getIndexes().get(0);
                    Double percentage = getPercentage(greenSheetDataStudentLevel.get(indexes).getMarks(),
                            greenSheetDataStudentLevelTotalMarks.get(indexes).getMarks());
                    String grade = getGrade(percentage);
                    greenSheetDataStudentLevel.add(new GreenSheetColumn(null, grade, ColumnType.GRADE));
                    greenSheetDataStudentLevelTotalMarks.add(new GreenSheetColumn(null, grade, ColumnType.GRADE));
                }
                else if(greenSheetNode.getTypeOfColumn() == ColumnType.SUM) {
                    Double sum = null;
                    for(Integer indexes : greenSheetNode.getIndexes()) {
                        sum = ExamMarksUtils.addValues(greenSheetDataStudentLevel.get(indexes).getMarks(), sum);
                    }
                    if (count == structure.size() - 1) {
                        obtainedTotalMarks = ExamMarksUtils.addValues(obtainedTotalMarks, sum);
                    }
                    greenSheetDataStudentLevel.add(new GreenSheetColumn(sum, null, ColumnType.RAW));

                    Double maxMarks = null;
                    for(Integer indexes : greenSheetNode.getIndexes()) {
                        maxMarks = ExamMarksUtils.addValues(greenSheetDataStudentLevelTotalMarks.get(indexes).getMarks(), maxMarks);
                    }
                    greenSheetDataStudentLevelTotalMarks.add(new GreenSheetColumn(maxMarks, null, ColumnType.RAW));
                }
            }
            count++;
        }
        return new GreenSheetSubjectWiseData(greenSheetDataStudentLevel, obtainedTotalMarks, null);
    }

    protected Double getPercentage(Double obtainedMarks, Double totalMarks) {
        if(obtainedMarks == null || totalMarks == null || totalMarks <= 0d) {
            return null;
        }
        return obtainedMarks / totalMarks * 100d;
    }

    protected List<GreenSheetTotalObtainedMarks> getSubjectWiseRawMarks(List<GreenSheetExamMarks> greenSheetExamMarksList) {

        if(greenSheetExamMarksList == null) {
            return null;
        }

        List<GreenSheetTotalObtainedMarks> marks = new ArrayList<>();
        for (GreenSheetExamMarks greenSheetExamMarks : greenSheetExamMarksList) {
            for (GreenSheetExamDimensionMarks greenSheetExamDimensionMarks : greenSheetExamMarks.getGreenSheetExamDimensionMarksList()) {
                marks.add(new GreenSheetTotalObtainedMarks(greenSheetExamDimensionMarks.getObtainedMarks(), greenSheetExamDimensionMarks.getTotalMarks()));
            }
        }
        return marks;
    }

    protected List<GreenSheetNode> getGreenSheetNodeCoScholasticSubjectII() {
        List<GreenSheetNode> greenSheetNode = new ArrayList<>();

        //Term I
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 0
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 1

        //Term II
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 2
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 3

        //Term III
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 4
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 5

        //total till now
        List<Integer> indexes = new ArrayList<>();
        indexes.add(0);
        indexes.add(2);
        indexes.add(4);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 6
        indexes = new ArrayList<>();
        indexes.add(1);
        indexes.add(3);
        indexes.add(5);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 7

        //Total
        indexes = new ArrayList<>();
        indexes.add(6);
        indexes.add(7);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 8

        //Half-yearly
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 9
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 10

        //Half-yearly Total
        indexes = new ArrayList<>();
        indexes.add(9);
        indexes.add(10);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 11


        //total till now
        indexes = new ArrayList<>();
        indexes.add(6);
        indexes.add(9);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 12
        indexes = new ArrayList<>();
        indexes.add(7);
        indexes.add(10);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 13

        //Total
        indexes = new ArrayList<>();
        indexes.add(12);
        indexes.add(13);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 14

        //Yearly
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 15
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 16

        //Yearly Total
        indexes = new ArrayList<>();
        indexes.add(15);
        indexes.add(16);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 17


        //course dimension total
        indexes = new ArrayList<>();
        indexes.add(12);
        indexes.add(15);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 18
        indexes = new ArrayList<>();
        indexes.add(13);
        indexes.add(16);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 19

        //course total
        indexes = new ArrayList<>();
        indexes.add(18);
        indexes.add(19);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 20

        return greenSheetNode;
    }

    protected List<GreenSheetNode> getGreenSheetNodeCoScholasticSubjectIII() {
        List<GreenSheetNode> greenSheetNode = new ArrayList<>();

        //Terms
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 0
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 1
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 2

        //total till now
        List<Integer> indexes = new ArrayList<>();
        indexes.add(0);
        indexes.add(1);
        indexes.add(2);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 3

        //Half-yearly
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 4
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 5

        //Half-yearly Total
        indexes = new ArrayList<>();
        indexes.add(4);
        indexes.add(5);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 6

        //total till now
        indexes = new ArrayList<>();
        indexes.add(3);
        indexes.add(6);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 7

        //Yearly
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 8
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 9

        //Yearly Total
        indexes = new ArrayList<>();
        indexes.add(8);
        indexes.add(9);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 10

        //course total
        indexes = new ArrayList<>();
        indexes.add(7);
        indexes.add(10);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 11

        return greenSheetNode;
    }

    protected List<GreenSheetNode> getGreenSheetNodeCoScholasticSubjectIV() {
        List<GreenSheetNode> greenSheetNode = new ArrayList<>();

        //Yearly
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 0
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 1
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 2

        //total till now
        List<Integer> indexes = new ArrayList<>();
        indexes.add(0);
        indexes.add(1);
        indexes.add(2);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 3

        indexes = new ArrayList<>();
        indexes.add(3);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.GRADE, indexes));//index 4

        return greenSheetNode;
    }

    protected List<GreenSheetNode> getGreenSheetNodeCoScholasticSubjectV() {
        List<GreenSheetNode> greenSheetNode = new ArrayList<>();

        //Yearly
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 0
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 1
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 2
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 3

        //total till now
        List<Integer> indexes = new ArrayList<>();
        indexes.add(0);
        indexes.add(1);
        indexes.add(2);
        indexes.add(3);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 4

        indexes = new ArrayList<>();
        indexes.add(4);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.GRADE, indexes));//index 5

        return greenSheetNode;
    }

}
