package com.lernen.cloud.core.lib.examination.greensheet;

import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.report.ExamResultStatus;
import com.lernen.cloud.core.api.examination.report.ReportCardVariableDetails;
import com.lernen.cloud.core.api.examination.report.StudentReportCardVariableDetails;
import com.lernen.cloud.core.api.examination.report.greensheet.*;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.lib.examination.ExamGreenSheetGenerator;
import com.lernen.cloud.core.lib.examination.ExamMarksUtils;
import com.lernen.cloud.core.utils.examination.GreenSheetProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.util.*;

/**
 * <AUTHOR>
 */

public class ExamGreenSheetGenerator_VI_VII_2022_23  extends ExamGreenSheetGenerator {

    private static final Double TOTAL_MARKS_VI_VII = 1200d;
    private static final Integer TOTAL_LINE_SPACING_VI_VII = 25;
    private static final Integer START_ROW_VI_VII = 9;
    private static final Integer START_COLUMN_VI_VII = 5;
    private static final Integer START_ROW_COURSE_WISE_MAP_VI_VII = 57;
    private static final Integer START_COLUMN_COURSE_WISE_MAP_VI_VII = 21;

    @Override
    public ReportOutput generateGreenSheet(GreenSheetClassData greenSheetClassData, ReportCardVariableDetails reportCardVariableDetails,
                                           String greenSheetHeader) throws IOException {

        GreenSheetListClassName greenSheetListClassName = getGreenSheetData(greenSheetClassData, reportCardVariableDetails);

        if(greenSheetListClassName == null) {
            logger.error("Invalid Green sheet data");
            return null;
        }

        List<List<GreenSheetColumn>> greenSheetData = greenSheetListClassName.getGreenSheetColumnList();

        GreenSheetClass greenSheetClass = greenSheetListClassName.getGreenSheetClass();

        String standardName = greenSheetClassData.getStandard().getDisplayName();

        InputStream fsIP = null;
        try {
            fsIP = GreenSheetProvider.INSTANCE.getGreenSheet(GreenSheetProvider.VI_VII_GREEN_SHEET);
        } catch (URISyntaxException | IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        Workbook wb = new XSSFWorkbook(fsIP);

        wb.cloneSheet(0);

        int row = START_ROW_VI_VII;
        int currentRow = row - 1;
        Cell cell = null;
        int sheetNumber = 1;
        int studentCount = 0;
        for (List<GreenSheetColumn> studentDataGreenSheet : greenSheetData) {
            studentCount++;
            Sheet worksheet = wb.getSheetAt(sheetNumber);
            row = currentRow + 1;
            int col = 0;
            for (GreenSheetColumn value : studentDataGreenSheet) {
                String cellValue = value.getResolvedValue();
                Map<Integer, Integer> mergedCells = new HashMap<>();
                for(int i = 0; i < worksheet.getNumMergedRegions(); ++i) {
                    CellRangeAddress range = worksheet.getMergedRegion(i);
                    if (range.getFirstRow() <= row && range.getLastRow() >= row) {
                        mergedCells.put(range.getFirstColumn(), range.getLastColumn());
                    }
                }
                cell = worksheet.getRow(row).getCell(col);
                if(cell == null) {
                    String snNo = worksheet.getRow(row).getCell(0).getStringCellValue();
                    String name = worksheet.getRow(row).getCell(4).getStringCellValue();
                    currentRow = row;
                    row = currentRow + TOTAL_LINE_SPACING_VI_VII;
                    worksheet.getRow(row).getCell(0).setCellValue(snNo);
                    worksheet.getRow(row).getCell(1).setCellValue(name);
                    col = START_COLUMN_VI_VII;
                    cell = worksheet.getRow(row).getCell(col);
                }
                cell.setCellValue(cellValue);
                if(mergedCells.containsKey(col)) {
                    col = col + (mergedCells.get(col) - col);
                }
                col++;
            }
            row++;
            if (studentCount % PER_SHEET_STUDENT_COUNT == 0) {
                sheetNumber++;
                row = START_ROW_VI_VII;
                currentRow = row - 1;
                cell = null;
                wb.cloneSheet(0);
            }
        }
        wb.removeSheetAt(0);

        int rowStart = START_ROW_COURSE_WISE_MAP_VI_VII - 1;
        int columnStart = START_COLUMN_COURSE_WISE_MAP_VI_VII - 16


                ;
        for(int i = 0; i < wb.getNumberOfSheets(); i++) {

            addInstituteNameOnSheet(wb.getSheetAt(i), greenSheetHeader);

            addClassNameOnSheet(wb.getSheetAt(i), standardName.contains("Class") ? standardName : "Class " + standardName);

            addTotalDetailsBottom(wb.getSheetAt(i), studentCount, rowStart, columnStart);

            LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> courseTypeGradeMap =
                    greenSheetListClassName.getCourseGradeMap();
            LinkedHashMap<String, Map<String, Integer>> courseGradeMap = new LinkedHashMap<>();
            courseGradeMap.putAll(courseTypeGradeMap.get(CourseType.SCHOLASTIC));
            courseGradeMap.putAll(courseTypeGradeMap.get(CourseType.COSCHOLASTIC));
            addCourseWiseGradeData(wb.getSheetAt(i), courseGradeMap, greenSheetClass);
            addGreenSheetDate(wb.getSheetAt(i), 60, 101, greenSheetListClassName.getResultDeclarationDate());// BY62
            addTotalAttendedDays(wb.getSheetAt(i), 33, 102, greenSheetListClassName.getTotalAttendanceDays());// CG34
        }

        String reportName = "greenSheetClass_" + greenSheetClass + ".xlsx";
        return getReportOutput(wb, reportName);

    }

    protected void addCourseWiseGradeData(Sheet worksheet, LinkedHashMap<String, Map<String, Integer>> courseGradeMap,
                                          GreenSheetClass greenSheetClass) {
        int startRow = START_ROW_COURSE_WISE_MAP_VI_VII;
        int startColumn = START_COLUMN_COURSE_WISE_MAP_VI_VII;

        for(Map.Entry<String, Map<String, Integer>> courseGradeMapEntry : courseGradeMap.entrySet()) {
            int row = startRow;
            int col = startColumn;
            int index = 0;
            int totalStudents = 0;

            for (Map.Entry<String, Integer> gradeMapEntry : courseGradeMapEntry.getValue().entrySet()) {


                if(index == 3) {
                    row++;
                    col = startColumn;
                }

                Cell cell = worksheet.getRow(row).getCell(col);
                totalStudents += gradeMapEntry.getValue();
                cell.setCellValue(gradeMapEntry.getValue());
                if(index == 1 || index == 4) {
                    col += 3;
                } else {
                    col += 2;
                }
                index++;
            }

            Cell cell = worksheet.getRow(row).getCell(col);
            cell.setCellValue(totalStudents);

            cell = worksheet.getRow(row - 2).getCell(col - 8);
            cell.setCellValue(totalStudents);

            col += 4;
            index++;

            row--;
            startRow = row;
            startColumn = col;

        }
    }

    public ReportOutput getReportOutput(Workbook workbook, String reportName) throws IOException {
        final ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        workbook.close();
        return new ReportOutput(reportName, bos);
    }

    protected GreenSheetListClassName getGreenSheetData(GreenSheetClassData greenSheetClassData,
                                                        ReportCardVariableDetails reportCardVariableDetails) {

        Map<UUID, StudentReportCardVariableDetails> studentReportCardVariableDetailsMap =
                getStudentReportCardVariableDetails(reportCardVariableDetails);

        if(greenSheetClassData == null || CollectionUtils.isEmpty(greenSheetClassData.getStudentGreenSheetDataList())) {
            return null;
        }

        LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> courseTypeGradeMap = defineStaticCourseGradeMap(
                greenSheetClassData.getStudentGreenSheetDataList().get(0).getGreenSheetCourseExamMarksList());

        LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> courseTypeDivisionMap = defineStaticDivisionGradeMap(
                greenSheetClassData.getStudentGreenSheetDataList().get(0).getGreenSheetCourseExamMarksList());

        GreenSheetClass greenSheetClassName = greenSheetClassData.getGreenSheetClass();
        List<List<GreenSheetColumn>> greenSheetData = new ArrayList<>();
        Integer index = 1;
        Map<Integer, Double> studentPercentMap = new HashMap<>();
        String totalDays = "";
        Integer resultDeclareDate = null;
        for (StudentGreenSheetData studentGreenSheetData : greenSheetClassData.getStudentGreenSheetDataList()) {

            List<GreenSheetColumn> studentData = new ArrayList<>();
            studentData.add(new GreenSheetColumn(null, index.toString(), ColumnType.STATIC));

            //basic info
            studentData.addAll(getStudentBasicDetails(studentGreenSheetData));

            //scholastic portion
            GreenSheetSubjectWiseData greenSheetSubjectWiseData = getStudentScholasticDetails(greenSheetClassName,
                    studentGreenSheetData, courseTypeGradeMap.get(CourseType.SCHOLASTIC),
                    courseTypeDivisionMap.get(CourseType.SCHOLASTIC));
            if(greenSheetSubjectWiseData != null && !CollectionUtils.isEmpty(greenSheetSubjectWiseData.getGreenSheetDataStudentLevel())) {
                studentData.addAll(greenSheetSubjectWiseData.getGreenSheetDataStudentLevel());
            }

            String distinctionSubjects = greenSheetSubjectWiseData.getDistinctionCourses();

            //total portion of scholastic part
            studentData.add(new GreenSheetColumn(greenSheetSubjectWiseData.getTotalMarks(), null, ColumnType.RAW));
            Double totalMarks = TOTAL_MARKS_VI_VII;
            Double percentage = getPercentage(greenSheetSubjectWiseData.getTotalMarks(), totalMarks);
            studentPercentMap.put(index  - 1, percentage);
            studentData.add(new GreenSheetColumn(percentage, null, ColumnType.PERCENTAGE));
            studentData.add(new GreenSheetColumn(null, getGrade(percentage), ColumnType.GRADE));

            //co-scholastic portion
            greenSheetSubjectWiseData = getStudentCoScholasticDetails(greenSheetClassName, studentGreenSheetData,
                    courseTypeGradeMap.get(CourseType.COSCHOLASTIC), courseTypeDivisionMap.get(CourseType.COSCHOLASTIC));
            if(greenSheetSubjectWiseData != null && !CollectionUtils.isEmpty(greenSheetSubjectWiseData.getGreenSheetDataStudentLevel())) {
                studentData.addAll(greenSheetSubjectWiseData.getGreenSheetDataStudentLevel());
            }

            //add result details
            final ExamResultStatus examResultStatus = percentage != null
                    && percentage >= PASSING_THRESHOLD * 100 ? ExamResultStatus.PASS
                    : ExamResultStatus.FAIL;
            studentData.add(new GreenSheetColumn(null, examResultStatus.getDisplayName(), ColumnType.STATIC));

            //Rank
//            studentData.add(new GreenSheetColumn(null, "", ColumnType.RANK));

            //Grade
            studentData.add(new GreenSheetColumn(null, getGrade(percentage), ColumnType.GRADE));

            //Distinction Courses
            studentData.add(new GreenSheetColumn(null, distinctionSubjects,
                    ColumnType.STATIC));

            //add attended days details
            UUID studentId = studentGreenSheetData.getStudent().getStudentId();
            String attendedDays = studentReportCardVariableDetailsMap == null ? "" :
                    studentReportCardVariableDetailsMap.get(studentId) == null ? "" :
                            studentReportCardVariableDetailsMap.get(studentId).getAttendedDays() == null ?
                                    "" : String.valueOf(Math.round(studentReportCardVariableDetailsMap.get(studentId).getAttendedDays()));

            studentData.add(new GreenSheetColumn(null, attendedDays, ColumnType.STATIC));

            if(StringUtils.isBlank(totalDays)){
                totalDays =  studentReportCardVariableDetailsMap == null ? "" :
                        studentReportCardVariableDetailsMap.get(studentId) == null ? "" :
                                studentReportCardVariableDetailsMap.get(studentId).getTotalDays() == null ?
                                        "" : String.valueOf(Math.round(studentReportCardVariableDetailsMap.get(studentId).getTotalDays()));
            }

            if(resultDeclareDate == null){
                resultDeclareDate =  studentReportCardVariableDetailsMap == null ? null :
                        studentReportCardVariableDetailsMap.get(studentId) == null ? null :
                                studentReportCardVariableDetailsMap.get(studentId).getDateOfResultDeclaration() == null ?
                                        null : studentReportCardVariableDetailsMap.get(studentId).getDateOfResultDeclaration();
            }



            greenSheetData.add(studentData);
            index++;
        }

        populateStudentRank(greenSheetData, studentPercentMap);

        return new GreenSheetListClassName(greenSheetClassName, greenSheetData, courseTypeGradeMap,
                courseTypeDivisionMap, totalDays, resultDeclareDate);
    }

    protected GreenSheetSubjectWiseData getStudentScholasticDetails(GreenSheetClass greenSheetClassName,
                                                                    StudentGreenSheetData studentGreenSheetData, LinkedHashMap<String, LinkedHashMap<String, Integer>> courseGradeMap,
                                                                    LinkedHashMap<String, LinkedHashMap<String, Integer>> courseDivisionMap) {
        List<GreenSheetColumn> subjectWiseData = new ArrayList<>();
        Double obtainedTotalMarks = null;
        int subjectCount = 0;
        String distinctionSubjects = "";
        boolean first = true;
        for(GreenSheetCourseExamMarks greenSheetCourseExamMarks : studentGreenSheetData.getGreenSheetCourseExamMarksList()) {
            if(greenSheetCourseExamMarks.getCourseType() == CourseType.SCHOLASTIC) {
                subjectCount++;
                List<GreenSheetTotalObtainedMarks> rawMarks = getSubjectWiseRawMarks(greenSheetCourseExamMarks.getGreenSheetExamMarksList());
                List<GreenSheetNode> structure = getGreenSheetNodeScholastic();
                GreenSheetSubjectWiseData greenSheetSubjectWiseData = getGreenSheetSubjectData(structure, rawMarks,
                        greenSheetClassName);
                subjectWiseData.addAll(greenSheetSubjectWiseData.getGreenSheetDataStudentLevel());
                obtainedTotalMarks = ExamMarksUtils.addValues(greenSheetSubjectWiseData.getTotalMarks(), obtainedTotalMarks);

                String courseName = greenSheetCourseExamMarks.getCourseName();
                if(StringUtils.isBlank(courseName)) {
                    continue;
                }

                //code for populating courseGradeMap for scholastic courses
                GreenSheetColumn greenSheetColumn = greenSheetSubjectWiseData.getGreenSheetDataStudentLevel().get(
                        greenSheetSubjectWiseData.getGreenSheetDataStudentLevel().size() - 1);
                String grade = greenSheetColumn.getValue();
                if(StringUtils.isBlank(grade)) {
                    continue;
                }
                if(grade.equals(GRADE_A)) {
                    if(first) {
                        distinctionSubjects += getShortCourseName(greenSheetCourseExamMarks.getCourseName());
                        first = false;
                    } else {
                        distinctionSubjects +=  ", " + getShortCourseName(greenSheetCourseExamMarks.getCourseName());
                    }
                }
                int currentCount = courseGradeMap.get(courseName).get(grade);
                courseGradeMap.get(courseName).put(grade, currentCount + 1);
            }
        }
        return new GreenSheetSubjectWiseData(subjectWiseData, obtainedTotalMarks, distinctionSubjects);
    }

    protected GreenSheetSubjectWiseData getStudentCoScholasticDetails(GreenSheetClass greenSheetClassName,
                                                                      StudentGreenSheetData studentGreenSheetData, LinkedHashMap<String, LinkedHashMap<String, Integer>> courseGradeMap,
                                                                      LinkedHashMap<String, LinkedHashMap<String, Integer>> courseDivisionMap) {
        List<GreenSheetColumn> subjectWiseData = new ArrayList<>();
        Double obtainedTotalMarks = null;
        int subjectCount = 0;
        for(GreenSheetCourseExamMarks greenSheetCourseExamMarks : studentGreenSheetData.getGreenSheetCourseExamMarksList()) {
            if(greenSheetCourseExamMarks.getCourseType() == CourseType.COSCHOLASTIC) {
                subjectCount++;
                List<GreenSheetTotalObtainedMarks> rawMarks = getSubjectWiseRawMarks(greenSheetCourseExamMarks.getGreenSheetExamMarksList());
                List<GreenSheetNode> structure = getGreenSheetNodeCoScholastic();
                GreenSheetSubjectWiseData greenSheetSubjectWiseData = getGreenSheetSubjectData(structure, rawMarks,
                        greenSheetClassName);
                subjectWiseData.addAll(greenSheetSubjectWiseData.getGreenSheetDataStudentLevel());
                obtainedTotalMarks = ExamMarksUtils.addValues(greenSheetSubjectWiseData.getTotalMarks(), obtainedTotalMarks);

                String courseName = greenSheetCourseExamMarks.getCourseName();
                if(StringUtils.isBlank(courseName)) {
                    continue;
                }

                //code for populating courseGradeMap for scholastic courses
                GreenSheetColumn greenSheetColumn = greenSheetSubjectWiseData.getGreenSheetDataStudentLevel().get(
                        greenSheetSubjectWiseData.getGreenSheetDataStudentLevel().size() - 1);
                String grade = greenSheetColumn.getValue();
                if(StringUtils.isBlank(grade)) {
                    continue;
                }

                int currentCount = courseGradeMap.get(courseName).get(grade);
                courseGradeMap.get(courseName).put(grade, currentCount + 1);

            }
        }
        return new GreenSheetSubjectWiseData(subjectWiseData, obtainedTotalMarks, null);
    }




    protected GreenSheetSubjectWiseData getGreenSheetSubjectData(List<GreenSheetNode> structure,
                                                                 List<GreenSheetTotalObtainedMarks> rawMarks,
                                                                 GreenSheetClass greenSheetClass) {
        List<GreenSheetColumn> greenSheetDataStudentLevel = new ArrayList<>();
        List<GreenSheetColumn> greenSheetDataStudentLevelTotalMarks = new ArrayList<>();

        List<Double> obtainedMarksList = new ArrayList<>();
        List<Double> totalMarksList = new ArrayList<>();

        for (GreenSheetTotalObtainedMarks greenSheetTotalObtainedMarks : rawMarks) {
            obtainedMarksList.add(greenSheetTotalObtainedMarks.getObtainedMarks());
            totalMarksList.add(greenSheetTotalObtainedMarks.getTotalMarks());
        }

        int index = 0;
        int count = 0;
        Double obtainedTotalMarks = null;
        for (GreenSheetNode greenSheetNode : structure) {
            if(greenSheetNode.getRawValue()) {
                greenSheetDataStudentLevel.add(new GreenSheetColumn(obtainedMarksList.get(index), null, ColumnType.RAW));
                greenSheetDataStudentLevelTotalMarks.add(new GreenSheetColumn(totalMarksList.get(index), null, ColumnType.RAW));
                index++;
            }
            else {
                if(greenSheetNode.getTypeOfColumn() == ColumnType.GRADE) {
                    int indexes = greenSheetNode.getIndexes().get(0);
                    Double percentage = getPercentage(greenSheetDataStudentLevel.get(indexes).getMarks(),
                            greenSheetDataStudentLevelTotalMarks.get(indexes).getMarks());
                    String grade = getGrade(percentage);
                    greenSheetDataStudentLevel.add(new GreenSheetColumn(null, grade, ColumnType.GRADE));
                    greenSheetDataStudentLevelTotalMarks.add(new GreenSheetColumn(null, grade, ColumnType.GRADE));
                }
                else if(greenSheetNode.getTypeOfColumn() == ColumnType.SUM) {
                    Double sum = null;
                    for(Integer indexes : greenSheetNode.getIndexes()) {
                        sum = ExamMarksUtils.addValues(greenSheetDataStudentLevel.get(indexes).getMarks(), sum);
                    }
                    if (count == structure.size() - 2) {
                        obtainedTotalMarks = ExamMarksUtils.addValues(obtainedTotalMarks, sum);
                    }
                    greenSheetDataStudentLevel.add(new GreenSheetColumn(sum, null, ColumnType.RAW));

                    Double maxMarks = null;
                    for(Integer indexes : greenSheetNode.getIndexes()) {
                        maxMarks = ExamMarksUtils.addValues(greenSheetDataStudentLevelTotalMarks.get(indexes).getMarks(), maxMarks);
                    }
                    greenSheetDataStudentLevelTotalMarks.add(new GreenSheetColumn(maxMarks, null, ColumnType.RAW));
                }
            }
            count++;
        }
        return new GreenSheetSubjectWiseData(greenSheetDataStudentLevel, obtainedTotalMarks, null);
    }

    protected Double getPercentage(Double obtainedMarks, Double totalMarks) {
        if(obtainedMarks == null || totalMarks == null || totalMarks <= 0d) {
            return null;
        }
        return obtainedMarks / totalMarks * 100d;
    }

    protected List<GreenSheetTotalObtainedMarks> getSubjectWiseRawMarks(List<GreenSheetExamMarks> greenSheetExamMarksList) {

        if(greenSheetExamMarksList == null) {
            return null;
        }

        List<GreenSheetTotalObtainedMarks> marks = new ArrayList<>();
        for (GreenSheetExamMarks greenSheetExamMarks : greenSheetExamMarksList) {
            for (GreenSheetExamDimensionMarks greenSheetExamDimensionMarks : greenSheetExamMarks.getGreenSheetExamDimensionMarksList()) {
                marks.add(new GreenSheetTotalObtainedMarks(greenSheetExamDimensionMarks.getObtainedMarks(), greenSheetExamDimensionMarks.getTotalMarks()));
            }
        }
        return marks;
    }

    protected List<GreenSheetNode> getGreenSheetNodeCoScholastic() {
        List<GreenSheetNode> greenSheetNode = new ArrayList<>();

        List<Integer> indexes = new ArrayList<>();
        //Valuation 1
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 0
        indexes.add(0);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.GRADE, indexes));//index 1


        indexes = new ArrayList<>();
        //Valuation 2
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 2
        indexes.add(2);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.GRADE, indexes));//index 3


        indexes = new ArrayList<>();
        //Valuation 3
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 4
        indexes.add(4);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.GRADE, indexes));//index 5


        indexes = new ArrayList<>();
        //Valuation 4
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 6
        indexes.add(6);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.GRADE, indexes));//index 7

        indexes = new ArrayList<>();
        //Valuation 5
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 8
        indexes.add(8);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.GRADE, indexes));//index 9

        //total
        indexes = new ArrayList<>();
        indexes.add(0);
        indexes.add(2);
        indexes.add(4);
        indexes.add(6);
        indexes.add(8);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 10
        indexes = new ArrayList<>();
        indexes.add(10);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.GRADE, indexes));//index 11

        return greenSheetNode;

    }

    protected List<GreenSheetNode> getGreenSheetNodeScholastic() {
        List<GreenSheetNode> greenSheetNode = new ArrayList<>();

        //Term 1
        List<Integer> indexes = new ArrayList<>();
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 0
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 1
        indexes = new ArrayList<>();
        indexes.add(0);
        indexes.add(1);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 2
        indexes = new ArrayList<>();
        indexes.add(2);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.GRADE, indexes));//index 3


        //Term 2
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 4
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 5
        indexes = new ArrayList<>();
        indexes.add(4);
        indexes.add(5);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 6
        indexes = new ArrayList<>();
        indexes.add(6);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.GRADE, indexes));//index 7

        //Term 3
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 8
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 9
        indexes = new ArrayList<>();
        indexes.add(8);
        indexes.add(9);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 10
        indexes = new ArrayList<>();
        indexes.add(10);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.GRADE, indexes));//index 11

        //total till now
        indexes = new ArrayList<>();
        indexes.add(2);
        indexes.add(6);
        indexes.add(10);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 12

        //Half-yearly
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 13
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 14
        indexes = new ArrayList<>();
        indexes.add(13);
        indexes.add(14);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 15
        indexes = new ArrayList<>();
        indexes.add(15);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.GRADE, indexes));//index 16

        //half-yealy total till now
        indexes = new ArrayList<>();
        indexes.add(12);
        indexes.add(15);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 17

        //Yearly
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 18
        greenSheetNode.add(new GreenSheetNode(Boolean.TRUE, null, null));//index 19
        indexes = new ArrayList<>();
        indexes.add(18);
        indexes.add(19);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 20
        indexes = new ArrayList<>();
        indexes.add(20);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.GRADE, indexes));//index 21

        //Subject Sum & Grade
        indexes = new ArrayList<>();
        indexes.add(17);
        indexes.add(20);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.SUM, indexes));//index 22
        indexes = new ArrayList<>();
        indexes.add(22);
        greenSheetNode.add(new GreenSheetNode(Boolean.FALSE, ColumnType.GRADE, indexes));//index 23

        return greenSheetNode;
    }

}