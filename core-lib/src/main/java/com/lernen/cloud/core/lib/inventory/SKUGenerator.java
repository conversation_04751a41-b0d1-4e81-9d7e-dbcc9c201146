package com.lernen.cloud.core.lib.inventory;

import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.inventory.BrandDetails;
import com.lernen.cloud.core.api.inventory.BrandInfo;
import com.lernen.cloud.core.api.inventory.Category;
import com.lernen.cloud.core.api.inventory.Color;
import com.lernen.cloud.core.api.inventory.ProductProperties;
import com.lernen.cloud.core.api.inventory.ProductVariationsGroup;
import com.lernen.cloud.core.api.inventory.UserGroup;
import com.lernen.cloud.core.api.user.Gender;

/**
 * 
 * <AUTHOR>
 *
 */
public class SKUGenerator {

	public static final String COMPONENT_DELIMITER = "_";
	public static final String SKU_DELIMITER = "|";

	public List<ProductProperties> generateProductVariation(ProductVariationsGroup productVariationsGroup, UUID productId) {
		ProductVariationsGroup.format(productVariationsGroup);
		SKUNode root = createSKUTree(productVariationsGroup, productId);
		ProductProperties propertiesHolder = new ProductProperties();
		root.setProductProperties(propertiesHolder);

		List<ProductProperties> productVariations = new LinkedList<ProductProperties>();
		populateProductVariations(root, productVariations);
		return productVariations;
	}

	private void populateProductVariations(SKUNode currentNode, List<ProductProperties> productVariations) {
		if (currentNode == null) {
			return;
		}
		ProductProperties productProperties = currentNode.getProductProperties();
		if (!currentNode.skipNode()) {
			if (StringUtils.isBlank(productProperties.getSku())) {
				productProperties.setSku(currentNode.getComponentName());
			} else {
				productProperties.setSku(productProperties.getSku() + SKU_DELIMITER + currentNode.getComponentName());
			}
			updateProperty(productProperties, currentNode.getSkuComponent(), currentNode.getValue());
		}

		if (CollectionUtils.isEmpty(currentNode.getChildren())) {
			productVariations.add(new ProductProperties(productProperties.getSku(), productProperties.getSize(),
					productProperties.getColor(), productProperties.getUserGroup(), productProperties.getGender()));
			return;
		}

		for (SKUNode child : currentNode.getChildren()) {
			child.setProductProperties(new ProductProperties(productProperties.getSku(), productProperties.getSize(),
					productProperties.getColor(), productProperties.getUserGroup(), productProperties.getGender()));
			populateProductVariations(child, productVariations);
		}

	}

	private void updateProperty(ProductProperties productProperties, SKUComponent skuComponent, String value) {
		switch (skuComponent) {
		case USER_GROUP:
			productProperties.setUserGroup(UserGroup.valueOf(value));
			break;
		case GENDER:
			productProperties.setGender(Gender.valueOf(value));
			break;
		case SIZE:
			productProperties.setSize(value);
			break;
		case COLOR:
			productProperties.setColor(new Color(Integer.valueOf(value)));
			break;
		default:
			break;
		}
	}

	private SKUNode createSKUTree(ProductVariationsGroup productVariationsGroup, UUID productId) {

		String part1 = String.valueOf(productVariationsGroup.getCategoryId());
		String part2 = productId.toString();
		String part3 = productVariationsGroup.getBrandId().toString();

		SKUNode root = new SKUNode(SKUComponent.CATEGORY, part1, part1);
		SKUNode nameNode = new SKUNode(SKUComponent.NAME, part2, part2);
		SKUNode brandNode = new SKUNode(SKUComponent.BRAND, part3, part3);

		Set<String> genderValues = new HashSet<>();
		if (!CollectionUtils.isEmpty(productVariationsGroup.getGenders())) {
			for (Gender gender : productVariationsGroup.getGenders()) {
				genderValues.add(gender.name());
			}
		}

		Set<String> userGroupValues = new HashSet<>();
		if (!CollectionUtils.isEmpty(productVariationsGroup.getUserGroups())) {
			for (UserGroup userGroup : productVariationsGroup.getUserGroups()) {
				userGroupValues.add(userGroup.name());
			}
		}

		Set<String> colorValues = new HashSet<>();
		if (!CollectionUtils.isEmpty(productVariationsGroup.getColors())) {
			for (Color color : productVariationsGroup.getColors()) {
				colorValues.add(String.valueOf(color.getColorId()));
			}
		}

		Set<SKUNode> colorChildren = init(colorValues, SKUComponent.COLOR, null);
		Set<SKUNode> sizeChildren = init(productVariationsGroup.getSizes(), SKUComponent.SIZE, colorChildren);
		Set<SKUNode> genderChildren = init(genderValues, SKUComponent.GENDER, sizeChildren);
		Set<SKUNode> userGroupChildren = init(userGroupValues, SKUComponent.USER_GROUP, genderChildren);

		brandNode.setChildren(userGroupChildren);
		nameNode.addChild(brandNode);
		root.addChild(nameNode);

		return root;

	}

	private Set<SKUNode> init(Set<String> attributes, SKUComponent skuComponent, Set<SKUNode> children) {
		Set<SKUNode> attributeNodes = new HashSet<SKUNode>();
		if (!CollectionUtils.isEmpty(attributes)) {
			for (String attribute : attributes) {
				String componentName = attribute.trim().toUpperCase().replaceAll(" +", COMPONENT_DELIMITER);
				if (SKUComponent.USER_GROUP.equals(skuComponent)) {
					componentName = UserGroup.getUserGroup(componentName).getSkuId();
				} else if (SKUComponent.GENDER.equals(skuComponent)) {
					componentName = Gender.getGender(componentName).getSkuId();
				}
				attributeNodes.add(new SKUNode(skuComponent, attribute.trim(), componentName, children));
			}
		} else {
			attributeNodes.add(new SKUNode(skuComponent, true, children));
		}

		return attributeNodes;
	}

	// public static void main(String[] args) {
	// SKUGenerator skuGenerator = new SKUGenerator();
	//// Set<String> sizes = new HashSet<String>(Arrays.asList("28", "30", "32",
	// "34", "36"));
	//// Set<UserGroup> userGroups = new
	// HashSet<UserGroup>(Arrays.asList(UserGroup.KIDS,UserGroup.ADULTS));
	//// Set<Gender> genders = new HashSet<Gender>(Arrays.asList(Gender.MALE,
	// Gender.FEMALE));
	// Set<String> colors = new HashSet<String>(Arrays.asList("Red", "Brown"));
	//
	// NewProductRequest newProductRequest = new NewProductRequest(1, "Blazer",
	// "Clothing", "SESOMU", null, colors,
	// null, null, "");
	// skuGenerator.generateSKU(newProductRequest);
	// }
}
