package com.lernen.cloud.core.lib.reports;

import com.itextpdf.kernel.geom.PageSize;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceStatus;
import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.fees.payment.StudentFeePaymentData;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Stream;
import com.lernen.cloud.core.api.report.CellIndexes;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.report.ReportSheetDetails;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.constants.ReportHeaderAttribute;
import com.lernen.cloud.core.lib.reports.utils.ReportCell;
import com.lernen.cloud.core.lib.reports.utils.ReportCellStyle;
import com.lernen.cloud.core.lib.reports.utils.ReportFont;
import com.lernen.cloud.core.lib.reports.utils.ReportRow;
import com.lernen.cloud.core.lib.reports.utils.ReportSheet;
import com.lernen.cloud.core.lib.reports.utils.ReportWorkbook;
import com.lernen.cloud.core.utils.EColorUtils;
import com.embrate.cloud.core.utils.institute.StandardUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
public class ReportGenerator {

    public static final String NOT_AVAILABLE = "NA";
    public static final String BOYS = "Boys";
    public static final String GIRLS = "Girls";
    public static final String COMMA_DELIMITER = ",";
    private static final int COLUMN_WIDTH_PADDING = 4;
    protected static final String STRING = "String";
    protected static final String INTEGER = "Integer";
    protected static final String DOUBLE = "Double";
    protected static final int CONTENT_SIZE = 11;
    protected static final DecimalFormat df = new DecimalFormat("0.00");
    protected static final String DATE_FORMAT = "dd/MMM/yyyy";
    protected static final String EMPTY_TEXT = "";
    protected static final String NA = "NA";
    protected static final String TOTAL = "Total";

    protected static final String DISTINCTION = "DISTINCTION";
    protected static final String DIVISION_I = "I";
    protected static final String DIVISION_II = "II";
    protected static final String DIVISION_III = "III";
    protected static final String DIVISION_IV = "IV";
    protected static final String WHITE_COLOR = EColorUtils.WHITE_COLOR_HEX_CODE;

    protected static final String BLACK_COLOR = EColorUtils.BLACK_COLOR_HEX_CODE;
    protected static final String RED_COLOR = EColorUtils.LIGHT_RED_COLOR_HEX_CODE;
    protected static final String YELLOW_COLOR = EColorUtils.LIGHT_YELLOW_COLOR_HEX_CODE;
    protected static final String ORANGE_COLOR = EColorUtils.LIGHT_ORANGE_COLOR_HEX_CODE;
    protected static final String GREEN_COLOR = EColorUtils.LIGHT_GREEN_COLOR_HEX_CODE;
    protected static final String BLUE_COLOR = EColorUtils.LIGHT_BLUE_COLOR2_HEX_CODE;

    public Set<String> convertToRequiredHeaderAttributes(String requiredHeadersCSV) {
        final Set<String> requiredHeaders = new HashSet<>();
        if (StringUtils.isBlank(requiredHeadersCSV)) {
            return requiredHeaders;
        }
        final String[] requiredHeadersArray = requiredHeadersCSV.split(COMMA_DELIMITER);
        for (final String reportHeaderAttribute : requiredHeadersArray) {
            requiredHeaders.add(reportHeaderAttribute.trim());
        }
        return requiredHeaders;
    }

    public Set<UUID> convertToRequiredStandards(String requiredStandardsCSV) {
        return StringHelper.convertStringToSetUUID(requiredStandardsCSV);
    }

    public Set<Integer> convertStrToIntegerSet(String feeHeadIdsStr) {
        return StringHelper.convertStringToSetInteger(feeHeadIdsStr);
    }

    public Set<Integer> convertToRequiredMultipleSessions(String requiredMultipleSessionCSV) {
        return StringHelper.convertStringToSetInteger(requiredMultipleSessionCSV);
    }

    public Map<UUID, List<Integer>> convertToRequiredStandardsWithSection(String requiredStandardsCSV) {
        return StandardUtils.convertToRequiredStandardsWithSection(requiredStandardsCSV);
    }

    public List<Integer> convertAttendanceTypeStr(String attendanceTypeIdStr) {
        List<Integer> attendanceTypeList = new ArrayList<>();
        if(!StringUtils.isBlank(attendanceTypeIdStr)) {
            attendanceTypeList = new ArrayList<Integer>();
            String [] attendanceTypeArr = attendanceTypeIdStr.split(",");
            for(String attendanceType : attendanceTypeArr) {
                attendanceTypeList.add(Integer.parseInt(attendanceType));
            }
        }
        return attendanceTypeList;
    }

    public List<AttendanceStatus> convertAttendanceStatusStr(String attendanceStatusStr) {
        List<AttendanceStatus> attendanceStatusList = new ArrayList<>();
        if(!StringUtils.isBlank(attendanceStatusStr)) {
            attendanceStatusList = new ArrayList<AttendanceStatus>();
            String [] attendanceStatusArr = attendanceStatusStr.split(",");
            for(String attendanceStatus : attendanceStatusArr) {
                attendanceStatusList.add(AttendanceStatus.getAttendanceStatus((attendanceStatus)));
            }
        }
        return attendanceStatusList;
    }

    public List<StaffAttendanceStatus> convertStaffAttendanceStatusStr(String staffAttendanceStatusStr) {
        List<StaffAttendanceStatus> attendanceStatusList = new ArrayList<>();
        if(!StringUtils.isBlank(staffAttendanceStatusStr)) {
            attendanceStatusList = new ArrayList<StaffAttendanceStatus>();
            String [] attendanceStatusArr = staffAttendanceStatusStr.split(",");
            for(String attendanceStatus : attendanceStatusArr) {
                attendanceStatusList.add(StaffAttendanceStatus.getStaffAttendanceStatus((attendanceStatus)));
            }
        }
        return attendanceStatusList;
    }

    public Map<String, ReportHeaderAttribute> getHeaderKeyAttributeMap(
            List<ReportHeaderAttribute> requiredHeaderAttributes) {
        final Map<String, ReportHeaderAttribute> headerKeyAttributeMap = new HashMap<>();
        if (CollectionUtils.isEmpty(requiredHeaderAttributes)) {
            return headerKeyAttributeMap;
        }

        for (final ReportHeaderAttribute reportHeaderAttribute : requiredHeaderAttributes) {
            headerKeyAttributeMap.put(reportHeaderAttribute.getKey(), reportHeaderAttribute);
        }

        return headerKeyAttributeMap;
    }

    public List<Boolean> getRequiredHeaderAttributes(String reportType, Set<String> requiredHeaderAttributes,
                                                     Map<String, List<ReportHeaderAttribute>> reportHeaders) {
        final List<ReportHeaderAttribute> configuredReportHeader = reportHeaders.get(reportType);
        if (CollectionUtils.isEmpty(configuredReportHeader)) {
            throw new EmbrateRunTimeException("Report headers are not configured for " + reportType);
        }
        final List<Boolean> finalHeaderAttributes = new ArrayList<>();
        for (final ReportHeaderAttribute reportHeaderAttribute : configuredReportHeader) {
            if (reportHeaderAttribute.isMandatory() || CollectionUtils.isEmpty(requiredHeaderAttributes)) {
                finalHeaderAttributes.add(true);
                continue;
            }
            if (requiredHeaderAttributes.contains(reportHeaderAttribute.getKey())) {
                finalHeaderAttributes.add(true);
            } else {
                finalHeaderAttributes.add(false);
            }
        }
        return finalHeaderAttributes;
    }

    public Set<StaffStatus> getStaffStatuses(String staffStatusStr){
        final Set<StaffStatus> staffStatus = new HashSet<>();
        if (StringUtils.isBlank(staffStatusStr)) {
            return staffStatus;
        }

        final String[] staffStatuses = staffStatusStr.split(COMMA_DELIMITER);
        for(final String staffCategoryList : staffStatuses){
            staffStatus.add(StaffStatus.getStaffStatus(staffCategoryList));
        }

        return staffStatus;
    }

    public boolean addHeader(final ReportWorkbook workbook, final ReportSheet sheet, int headerRowNumber,
                             final List<ReportHeaderAttribute> configuredReportHeader, final List<Boolean> finalHeaderAttributes) {
        final ReportFont headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 14);
        headerFont.setColor(BLACK_COLOR);

        // Create a CellStyle with the font
        final ReportCellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFont(headerFont);

        final ReportRow headerRow = sheet.createRow(headerRowNumber);

        // Create cells
        int i = 0;
        int cellCount = 0;
        for (final ReportHeaderAttribute reportHeaderAttribute : configuredReportHeader) {
            if (!finalHeaderAttributes.get(i)) {
                i++;
                continue;
            }
            final ReportCell cell = headerRow.createCell(cellCount++);
            cell.setCellValue(reportHeaderAttribute.getDisplayName());
            cell.setCellStyle(headerCellStyle);
            i++;
        }
        return cellCount != 0;
    }

    public boolean addHeader(final ReportWorkbook workbook, final ReportSheet sheet, int headerRowNumber, int[] totalColumns,
                             final List<ReportHeaderAttribute> configuredReportHeader, final List<Boolean> finalHeaderAttributes) {
        final ReportFont headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 14);
        headerFont.setColor(BLACK_COLOR);

        // Create a CellStyle with the font
        final ReportCellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFont(headerFont);

        final ReportRow headerRow = sheet.createRow(headerRowNumber);

        // Create cells
        int i = 0;
        int cellCount = 0;
        for (final ReportHeaderAttribute reportHeaderAttribute : configuredReportHeader) {
            if (!finalHeaderAttributes.get(i)) {
                i++;
                continue;
            }
            final ReportCell cell = headerRow.createCell(cellCount++);
            cell.setCellValue(reportHeaderAttribute.getDisplayName());
            cell.setCellStyle(headerCellStyle);
            i++;
            totalColumns[0] = cellCount;
        }
        return cellCount != 0;
    }


    public boolean addHeader(final Workbook workbook, final Sheet sheet, int headerRowNumber,
                             final List<ReportHeaderAttribute> configuredReportHeader, final List<Boolean> finalHeaderAttributes) {
        final Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 14);
        headerFont.setColor(IndexedColors.BLACK.getIndex());

        // Create a CellStyle with the font
        final CellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFont(headerFont);

        final Row headerRow = sheet.createRow(headerRowNumber);

        // Create cells
        int i = 0;
        int cellCount = 0;
        for (final ReportHeaderAttribute reportHeaderAttribute : configuredReportHeader) {
            if (!finalHeaderAttributes.get(i)) {
                i++;
                continue;
            }
            final Cell cell = headerRow.createCell(cellCount++);
            cell.setCellValue(reportHeaderAttribute.getDisplayName());
            cell.setCellStyle(headerCellStyle);
            i++;
        }
        return cellCount != 0;
    }


    public void writeSheetContent(final ReportWorkbook workbook, final ReportSheet sheet, int headerRowNumber,
                                  final List<ReportHeaderAttribute> configuredReportHeader, final List<Boolean> finalHeaderAttributes,
                                  List<List<String>> reportRows) {

        addHeader(workbook, sheet, headerRowNumber, configuredReportHeader, finalHeaderAttributes);
        int rowNum = headerRowNumber + 1;
        for (final List<String> contentRow : reportRows) {
            final ReportRow row = sheet.createRow(rowNum++);
            int colNum = 0;
            int colCount = 0;
            for (final String columnValue : contentRow) {
                if (!finalHeaderAttributes.get(colCount)) {
                    colCount++;
                    continue;
                }
                row.createCell(colNum++).setCellValue(columnValue);
                colCount++;
            }
        }

        // Resize all columns to fit the content size
        int i = 0;
        for (final Boolean includeAttribute : finalHeaderAttributes) {
            if (!includeAttribute) {
                continue;
            }
            // sheet.autoSizeColumn(i++);
            i++;
        }
    }

    public void writeSheetContent(final ReportWorkbook workbook, final ReportSheet sheet, final int headerRowNumber,int[] totalColumns,
                                  final List<ReportHeaderAttribute> configuredReportHeader, final List<Boolean> finalHeaderAttributes,
                                  List<List<String>> reportRows) {

        addHeader(workbook, sheet, headerRowNumber, totalColumns, configuredReportHeader, finalHeaderAttributes);
        int rowNum = headerRowNumber + 1;
        for (final List<String> contentRow : reportRows) {
            final ReportRow row = sheet.createRow(rowNum++);
            int colNum = 0;
            int colCount = 0;
            for (final String columnValue : contentRow) {
                if (!finalHeaderAttributes.get(colCount)) {
                    colCount++;
                    continue;
                }
                row.createCell(colNum++).setCellValue(columnValue);
                colCount++;
                totalColumns[0] = colNum;
            }
        }

        // Resize all columns to fit the content size
        int i = 0;
        for (final Boolean includeAttribute : finalHeaderAttributes) {
            if (!includeAttribute) {
                continue;
            }
            // sheet.autoSizeColumn(i++);
            i++;
        }
    }


    public void writeSheetContent(final Workbook workbook, final Sheet sheet, int headerRowNumber,
                                  final List<ReportHeaderAttribute> configuredReportHeader, final List<Boolean> finalHeaderAttributes,
                                  List<List<String>> reportRows) {
        addHeader(workbook, sheet, headerRowNumber, configuredReportHeader, finalHeaderAttributes);
        int rowNum = headerRowNumber + 1;
        for (final List<String> contentRow : reportRows) {
            final Row row = sheet.createRow(rowNum++);
            int colNum = 0;
            int colCount = 0;
            for (final String columnValue : contentRow) {
                if (!finalHeaderAttributes.get(colCount)) {
                    colCount++;
                    continue;
                }
                row.createCell(colNum++).setCellValue(columnValue);
                colCount++;
            }
        }

        // Resize all columns to fit the content size
        int i = 0;
        for (final Boolean includeAttribute : finalHeaderAttributes) {
            if (!includeAttribute) {
                continue;
            }
            sheet.autoSizeColumn(i++);
        }
    }

    public void addValueInList(List<String> list, Object value) {
        if (list == null) {
            return;
        }
        list.add(getFieldValue(value));
    }

    public String getFieldValue(Object obj) {
        return obj == null ? NOT_AVAILABLE : String.valueOf(obj);
    }

    public List<Integer> addHeader(final Workbook workbook, final Sheet sheet, int headerRowNumber, String[] headers) {
        final Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 14);
        headerFont.setColor(IndexedColors.BLACK.getIndex());

        // Create a CellStyle with the font
        final CellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFont(headerFont);

        return addHeader(workbook, sheet, headerCellStyle, headerRowNumber, headers);
    }

    public List<Integer> addHeader(final ReportWorkbook workbook, final ReportSheet sheet, ReportCellStyle headerCellStyle,
                                   int headerRowNumber, String[] headers) {
        final ReportRow headerRow = sheet.createRow(headerRowNumber);

        // Create cells
        final List<Integer> columnWidths = new ArrayList<>();
        // Create cells
        for (int i = 0; i < headers.length; i++) {
            final ReportCell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            if (headerCellStyle != null) {
                cell.setCellStyle(headerCellStyle);
            }
            columnWidths.add(headers[i].length());
        }

        return columnWidths;
    }

    public List<Integer> addHeader(final Workbook workbook, final Sheet sheet, CellStyle headerCellStyle,
                                   int headerRowNumber, String[] headers) {
        final Row headerRow = sheet.createRow(headerRowNumber);

        // Create cells
        final List<Integer> columnWidths = new ArrayList<>();
        // Create cells
        for (int i = 0; i < headers.length; i++) {
            final Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            if (headerCellStyle != null) {
                cell.setCellStyle(headerCellStyle);
            }
            columnWidths.add(headers[i].length());
        }

        return columnWidths;
    }

    public ReportOutput getReportOutput(Workbook workbook, String reportName) throws IOException {
        final ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        workbook.close();
        return new ReportOutput(reportName, bos);
    }

    public ReportDetails getReportOutput(ReportWorkbook workbook, String reportName) throws IOException {
        return getReportOutput(workbook, reportName, null);
    }

    public ReportDetails getReportOutput(ReportWorkbook workbook, String reportName, PageSize pageSize) throws IOException {
        List<ReportSheetDetails> reportSheetDetailsList = workbook.getReportSheetDetails();
        if(pageSize != null) {
            for(ReportSheetDetails reportSheetDetails : reportSheetDetailsList) {
                reportSheetDetails.setPageSize(pageSize);
            }
        }
        return new ReportDetails(reportName, reportSheetDetailsList);
    }

    public ReportDetails getReportOutput(ReportWorkbook workbook, String reportName, PageSize pageSize, int totalColumns, int headerRowCount, Institute institute) throws IOException {
        return getReportOutput(workbook, reportName, pageSize, totalColumns, headerRowCount, institute,true, true, false);
    }

    public ReportDetails getReportOutput(ReportWorkbook workbook, String reportName, PageSize pageSize, int totalColumns, int headerRowCount, Institute institute, boolean showCenterHeading, boolean showDateAndTime, boolean wrapText) throws IOException {
        List<ReportSheetDetails> reportSheetDetailsList = workbook.getReportSheetDetails(totalColumns, headerRowCount, institute, showCenterHeading, showDateAndTime,
                wrapText);
        if(pageSize != null) {
            for(ReportSheetDetails reportSheetDetails : reportSheetDetailsList) {
                reportSheetDetails.setPageSize(pageSize);
            }
        }
        return new ReportDetails(reportName, reportSheetDetailsList);
    }

    public void createCell(Row row, int colNum, String value, final List<Integer> columnWidths) {
        createCell(row, colNum, value, columnWidths, null);
    }

    public void createCell(Row row, int colNum, String value, final List<Integer> columnWidths, CellStyle cellStyle) {
        updateColumnWidth(columnWidths, colNum, value);
        final Cell cell = row.createCell(colNum);
        cell.setCellValue(value);
        if (cellStyle != null) {
            cell.setCellStyle(cellStyle);
        }

    }

    public void createCell(Row row, int colNum, Double value, final List<Integer> columnWidths) {
        updateColumnWidth(columnWidths, colNum, value);
        row.createCell(colNum).setCellValue(value);
    }

    public void createCell(Row row, int colNum, Integer value, final List<Integer> columnWidths) {
        createCell(row, colNum, value, columnWidths, null);
    }

    public void createCell(Row row, int colNum, Integer value, final List<Integer> columnWidths, CellStyle cellStyle) {
        updateColumnWidth(columnWidths, colNum, value);
        final Cell cell = row.createCell(colNum);
        cell.setCellValue(value);
        if (cellStyle != null) {
            cell.setCellStyle(cellStyle);
        }

    }

    public void createCell(Row row, int colNum, Double value, final List<Integer> columnWidths, CellStyle cellStyle) {
        updateColumnWidth(columnWidths, colNum, value);
        final Cell cell = row.createCell(colNum);
        cell.setCellValue(value);
        if (cellStyle != null) {
            cell.setCellStyle(cellStyle);
        }

    }

    public void updateColumnWidth(final List<Integer> columnWidths, int colNum, Object value) {
        final int charLength = value == null ? 0 : String.valueOf(value).length();
        if (columnWidths.get(colNum) < charLength) {
            columnWidths.set(colNum, charLength);
        }
    }

    public void resizeSheet(final Sheet sheet, final List<Integer> columnWidths) {
        for (int i = 0; i < columnWidths.size(); i++) {
            sheet.setColumnWidth(i, (columnWidths.get(i) + COLUMN_WIDTH_PADDING) * 256);
        }
    }

    public void resizeSheet(final ReportSheet sheet, final List<Integer> columnWidths) {
        for (int i = 0; i < columnWidths.size(); i++) {
            sheet.setColumnWidth(i, (columnWidths.get(i) + COLUMN_WIDTH_PADDING) * 256);
        }
    }

    public String getRoundValueString(Double value) {
        if (value == null) {
            return "";
        }
        final long tempValue = Math.round(value * 100);
        if (tempValue % 100l == 0) {
            return String.valueOf((tempValue / 100l));
        }
        return String.valueOf((tempValue / 100d));
    }

    public void resizeCell(Sheet sheet, int length) {
        for (int i = 0; i < length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    public void mergeCells(Sheet sheet, List<CellIndexes> cellIndexesList) {
        if(!CollectionUtils.isEmpty(cellIndexesList)) {
            for(CellIndexes cellIndexes : cellIndexesList) {
                final CellRangeAddress cellRangeAddress = new CellRangeAddress(cellIndexes.getIndexXX(),
                        cellIndexes.getIndexXY(), cellIndexes.getIndexYX(), cellIndexes.getIndexYY());
                sheet.addMergedRegion(cellRangeAddress);
            }
        }
    }

    public String getDateStr(int timeInSec, String formatStr) {
        final Date date = new Date(timeInSec * 1000L);
        final DateFormat format = new SimpleDateFormat(formatStr);
        format.setTimeZone(User.DFAULT_TIMEZONE);
        final String formattedDate = format.format(date);
        return formattedDate;
    }

    public void addTotalRow(final Workbook workbook, final Sheet sheet, int headerRowNumber, List<String> rowValues) {
        final Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 14);
        headerFont.setColor(IndexedColors.BLACK.getIndex());

        // Create a CellStyle with the font
        final CellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFont(headerFont);

        generateTotalRow(sheet, headerCellStyle, headerRowNumber, rowValues);
    }

    public void generateTotalRow(Sheet sheet, CellStyle headerCellStyle, int rowNum, List<String> rowValues) {
        if(CollectionUtils.isEmpty(rowValues)) {
            return;
        }
        final Row headerRow = sheet.createRow(rowNum);
        for (int i = 0; i < rowValues.size(); i++) {
            final Cell cell = headerRow.createCell(i);
            cell.setCellValue(rowValues.get(i));
            cell.setCellStyle(headerCellStyle);
        }
    }

    public Set<UUID> convertStrToUUIDSet(String feeIdsStr) {
        final Set<UUID> feeIds = new HashSet<>();
        if (StringUtils.isBlank(feeIdsStr)) {
            return feeIds;
        }

        final String[] feeIdTokens = feeIdsStr.split(",");

        for (final String feeId : feeIdTokens) {
            if(StringUtils.isBlank(feeId)) {
                continue;
            }
            feeIds.add(UUID.fromString(feeId));
        }
        return feeIds;
    }

    public Set<StudentStatus> getStudentStatus(String studentStatusCSV) {
        final Set<StudentStatus> studentStatuses = new LinkedHashSet<>();
        if (StringUtils.isBlank(studentStatusCSV)) {
            return studentStatuses;
        }

        final String[] statusTokens = studentStatusCSV.split(",");

        for (final String status : statusTokens) {
            if (StringUtils.isBlank(status))
            {
                continue;
            }
            studentStatuses.add(StudentStatus.getStudentStatus(status.trim()));
        }
        return studentStatuses;
    }

    public void createCell(ReportRow row, int colNum, String value, final List<Integer> columnWidths) {
        createCell(row, colNum, value, columnWidths, null);
    }

    public void createCell(ReportRow row, int colNum, String value, final List<Integer> columnWidths, ReportCellStyle cellStyle) {
        updateColumnWidth(columnWidths, colNum, value);
        final ReportCell cell = row.createCell(colNum);
        cell.setCellValue(value);
        if (cellStyle != null) {
            cell.setCellStyle(cellStyle);
        }

    }

    public void createCell(ReportRow row, int colNum, Double value, final List<Integer> columnWidths) {
        updateColumnWidth(columnWidths, colNum, value);
        row.createCell(colNum).setCellValue(value);
    }

    public void createCell(ReportRow row, int colNum, Integer value, final List<Integer> columnWidths) {
        createCell(row, colNum, value, columnWidths, null);
    }

    public void createCell(ReportRow row, int colNum, Integer value, final List<Integer> columnWidths, ReportCellStyle cellStyle) {
        updateColumnWidth(columnWidths, colNum, value);
        final ReportCell cell = row.createCell(colNum);
        cell.setCellValue(value);
        if (cellStyle != null) {
            cell.setCellStyle(cellStyle);
        }

    }

    public void createCell(ReportRow row, int colNum, Double value, final List<Integer> columnWidths, ReportCellStyle cellStyle) {
        updateColumnWidth(columnWidths, colNum, value);
        final ReportCell cell = row.createCell(colNum);
        cell.setCellValue(value);
        if (cellStyle != null) {
            cell.setCellStyle(cellStyle);
        }

    }

    public String getStandardDisplay(StudentFeePaymentData studentFeePaymentData) {
        return getStandardDisplay(studentFeePaymentData.getStandardName(),studentFeePaymentData.getStream(), studentFeePaymentData.getSectionName());
    }

    public String getStandardDisplay(String standardDisplayName, Stream stream, String section) {
        if (stream != null && stream != Stream.NA) {
            standardDisplayName += " (" + stream + ")";
        }

        if (!StringUtils.isBlank(section)) {
            standardDisplayName += " - " + section;
        }
        return standardDisplayName;
    }
}