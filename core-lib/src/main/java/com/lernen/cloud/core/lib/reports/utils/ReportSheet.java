package com.lernen.cloud.core.lib.reports.utils;

import com.lernen.cloud.core.api.report.CellIndexes;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class ReportSheet {
    private final String sheetName;

    private final List<ReportRow> reportRowList;

    private final List<CellIndexes> mergeCellIndexesList;
    public ReportSheet(String sheetName) {
        this.sheetName = sheetName;
        this.reportRowList = new ArrayList<>();
        this.mergeCellIndexesList = new ArrayList<>();
    }

    public List<ReportRow> getReportRowList() {
        return reportRowList;
    }

    public ReportRow createRow(int rowNum){
        if (rowNum < reportRowList.size()) {
            return reportRowList.get(rowNum);
        }

        for (int i = reportRowList.size(); i <= rowNum; i++) {
            reportRowList.add(new ReportRow(i));
        }

        return reportRowList.get(rowNum);
    }

    public ReportRow getRow(int rowNum){
        if (rowNum < reportRowList.size()) {
            return reportRowList.get(rowNum);
        }
        return null;
    }

    public int getLastRowNum(){
        return reportRowList.size() - 1;
    }
    public void addMergedRegion(CellIndexes cellIndexes){
        mergeCellIndexesList.add(cellIndexes);
    }

    public List<CellIndexes> getMergeCellIndexesList() {
        return mergeCellIndexesList;
    }

    public void setColumnWidth(int columnIndex, int width){
        //TODO : implement
    }
}
