package com.lernen.cloud.core.lib.reports.utils;

import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.report.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class ReportWorkbook {

    private final Map<String, ReportSheet> reportSheetList;
    private static final int CONTENT_SIZE = 9;

    public ReportWorkbook() {
        this.reportSheetList = new HashMap<>();
    }

    public ReportSheet createSheet(String sheetName) {
        if (reportSheetList.containsKey(sheetName)) {
            return reportSheetList.get(sheetName);
        }
        ReportSheet reportSheet = new ReportSheet(sheetName);
        reportSheetList.put(sheetName, reportSheet);
        return reportSheet;
    }

    public ReportCellStyle createCellStyle() {
        return new ReportCellStyle();
    }

    public ReportFont createFont() {
        return new ReportFont();
    }


    public List<ReportSheetDetails> getReportSheetDetails() {
        List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
        if (reportSheetList.isEmpty()) {
            return reportSheetDetailsList;
        }
        String sheetName = reportSheetList.keySet().iterator().next();
        ReportSheet reportSheet = reportSheetList.get(sheetName);

        List<CellIndexes> mergeCellIndexesList = reportSheet.getMergeCellIndexesList();

        List<List<ReportCellDetails>> reportRowDetailsList = new ArrayList<>();
        for (ReportRow reportRow : reportSheet.getReportRowList()) {
            List<ReportCellDetails> reportCellDetailsList = new ArrayList<>();
           
            for (ReportCell reportCell : reportRow.getReportCellList()) {
                ReportHorizontalTextAlignment reportHorizontalTextAlignment = ReportHorizontalTextAlignment.CENTER;
                if(reportCell.getCellStyle().getAlignment() == HorizontalAlignment.LEFT) {
                    reportHorizontalTextAlignment = ReportHorizontalTextAlignment.LEFT;
                } else if(reportCell.getCellStyle().getAlignment() == HorizontalAlignment.RIGHT) {
                    reportHorizontalTextAlignment = ReportHorizontalTextAlignment.RIGHT;
                }
                reportCellDetailsList.add(new ReportCellDetails(reportCell.getCellValue(), reportCell.getDataType(),
                        reportCell.getCellStyle().getFont().getFontHeightInPoints(), reportCell.getCellStyle().getFont().getColor(),
                        reportCell.getCellStyle().getCellColor(), reportCell.getCellStyle().getFont().isBold(),
                        reportHorizontalTextAlignment, ReportVerticalTextAlignment.MIDDLE));
            }
            reportRowDetailsList.add(reportCellDetailsList);
        }
      
        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, mergeCellIndexesList, reportRowDetailsList);
        reportSheetDetailsList.add(reportSheetDetails);
        return reportSheetDetailsList;
    }

//     public List<ReportSheetDetails> getReportSheetDetails(int totalColumns, int headerRowCount, Institute institute) {
//        return getReportSheetDetails(totalColumns, headerRowCount, institute, true, true, false);
//     }

    public List<ReportSheetDetails> getReportSheetDetails(int totalColumns, int headerRowCount, Institute institute, boolean showCenterHeading, boolean showDateAndTime, boolean wrapText) {
        List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
        if (reportSheetList.isEmpty()) {
            return reportSheetDetailsList;
        }
        String sheetName = reportSheetList.keySet().iterator().next();
        ReportSheet reportSheet = reportSheetList.get(sheetName);

        List<CellIndexes> mergeCellIndexes = reportSheet.getMergeCellIndexesList();

        List<CellIndexes> mergeCellIndexesList = new ArrayList<>();
        List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();
        for(CellIndexes cellIndexes : mergeCellIndexes) {
            if(cellIndexes.getIndexXX() <= headerRowCount || cellIndexes.getIndexXY() <= headerRowCount) {
                headerMergeCellIndexesList.add(cellIndexes);
                continue;
            }
            mergeCellIndexesList.add(cellIndexes);
        }
        List<List<ReportCellDetails>> reportRowDetailsList = new ArrayList<>();
        List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<>();
        int rowNumber = 0;
        for (ReportRow reportRow : reportSheet.getReportRowList()) {
            rowNumber++;
            List<ReportCellDetails> reportCellDetailsList = new ArrayList<>();
            for (ReportCell reportCell : reportRow.getReportCellList()) {
                ReportHorizontalTextAlignment reportHorizontalTextAlignment = ReportHorizontalTextAlignment.CENTER;
                if(reportCell.getCellStyle().getAlignment() == HorizontalAlignment.LEFT) {
                    reportHorizontalTextAlignment = ReportHorizontalTextAlignment.LEFT;
                } else if(reportCell.getCellStyle().getAlignment() == HorizontalAlignment.RIGHT) {
                    reportHorizontalTextAlignment = ReportHorizontalTextAlignment.RIGHT;
                }
                reportCellDetailsList.add(new ReportCellDetails(reportCell.getCellValue(), reportCell.getDataType(),
                        CONTENT_SIZE, reportCell.getCellStyle().getFont().getColor(),
                        reportCell.getCellStyle().getCellColor(), reportCell.getCellStyle().getFont().isBold(),
                        reportHorizontalTextAlignment, ReportVerticalTextAlignment.MIDDLE));
            }

            if(rowNumber <= headerRowCount) {
                headerReportCellDetails.add(reportCellDetailsList);
                continue;
            }

            reportRowDetailsList.add(reportCellDetailsList);
        }

        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, showDateAndTime,
                totalColumns, headerMergeCellIndexesList,
                mergeCellIndexesList, headerReportCellDetails, reportRowDetailsList, true,
                true, institute, totalColumns, showCenterHeading, wrapText);

        reportSheetDetailsList.add(reportSheetDetails);
        return reportSheetDetailsList;
    }

}
