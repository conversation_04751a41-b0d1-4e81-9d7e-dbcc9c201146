package com.lernen.cloud.core.lib.sms;

import java.util.List;

import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.notification.NotificationStatus;
import com.lernen.cloud.core.api.sms.msg91.MSG91SMSReport;
import com.lernen.cloud.core.api.sms.msg91.MSG91SMSStatus;
import com.lernen.cloud.core.api.sms.msg91.MSG91SMSWebhookPayload;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.notifications.NotificationManager;
import com.lernen.cloud.core.utils.DateUtils;

/**
 *
 * <AUTHOR>
 *
 */
public class MSG91SMSWebhookManager {

	private static final Logger logger = LogManager.getLogger(MSG91SMSWebhookManager.class);

	private static final String MSG91_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
	private final NotificationManager notificationManager;

	public MSG91SMSWebhookManager(NotificationManager notificationManager) {
		this.notificationManager = notificationManager;
	}

	public boolean handleWebhookRequest(List<MSG91SMSWebhookPayload> msg91smsWebhookPayloads) {
		try {
			boolean status = true;
			for (final MSG91SMSWebhookPayload msg91smsWebhookPayload : msg91smsWebhookPayloads) {
				logger.info("MSG91 SMS webhook payload {}", msg91smsWebhookPayload);
				if (!valid(msg91smsWebhookPayload)) {
					logger.error("Invalid webhook payload {}. Skipping", msg91smsWebhookPayload);
					status = false;
					continue;
				}
				NotificationStatus notificationStatus = null;
				Long deliveryTime = null;

				for (final MSG91SMSReport msg91smsReport : msg91smsWebhookPayload.getMsg91SMSReports()) {
					final NotificationStatus internalNotificationStatus = MSG91SMSStatus
							.getNotificationStatus(msg91smsReport.getStatus());
					switch (internalNotificationStatus) {
					case DELIVERED:
						notificationStatus = NotificationStatus.DELIVERED;
						deliveryTime = parseEventTime(msg91smsReport.getEventDateTime());
						break;
					case FAILED:
						if (notificationStatus != NotificationStatus.DELIVERED) {
							notificationStatus = NotificationStatus.FAILED;
						}
						break;
					case REJECTED:
						if (notificationStatus != NotificationStatus.DELIVERED
								&& notificationStatus != NotificationStatus.FAILED) {
							notificationStatus = NotificationStatus.REJECTED;
						}
						break;
					default:
						break;
					}
				}
				final boolean updateStatus = notificationManager
						.updateNotification(msg91smsWebhookPayload.getSmsUniqueId(), CommunicationServiceProvider.MSG91_SMS, notificationStatus, deliveryTime);
				if (updateStatus) {
					logger.info("Updated webhook data for request id {}, status {}, successfully",
							msg91smsWebhookPayload.getSmsUniqueId(), notificationStatus);
				} else {
					logger.error("Unable to update webhook data for request id {}, status {}",
							msg91smsWebhookPayload.getSmsUniqueId(), notificationStatus);
				}
				status &= updateStatus;
			}
			return status;
		} catch (final Exception e) {
			logger.error("Unknow error occurred while processing webhook {}", msg91smsWebhookPayloads, e);
			return false;
		}

	}

	private Long parseEventTime(String dateTime) {
		return DateUtils.getTimestampFromDate(dateTime, User.DFAULT_TIMEZONE, MSG91_DATE_FORMAT) * 1000l;
	}

	private boolean valid(MSG91SMSWebhookPayload msg91smsWebhookPayload) {
		return !(msg91smsWebhookPayload == null || StringUtils.isBlank(msg91smsWebhookPayload.getSmsUniqueId())
				|| StringUtils.isBlank(msg91smsWebhookPayload.getSenderId())
				|| CollectionUtils.isEmpty(msg91smsWebhookPayload.getMsg91SMSReports()));
	}
}
