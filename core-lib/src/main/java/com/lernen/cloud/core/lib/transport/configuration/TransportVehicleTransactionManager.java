package com.lernen.cloud.core.lib.transport.configuration;

import com.lernen.cloud.core.api.appointment.AppointmentDetailsPayload;
import com.lernen.cloud.core.api.appointment.AppointmentStatus;
import com.lernen.cloud.core.api.appointment.StudentAppointmentDetails;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.transport.*;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.dao.tier.transport.configuration.TransportVehicleTransactionDao;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

public class TransportVehicleTransactionManager {
    private final TransportVehicleTransactionDao transportVehicleTransactionDao;

    public TransportVehicleTransactionManager(TransportVehicleTransactionDao transportVehicleTransactionDao) {
        this.transportVehicleTransactionDao = transportVehicleTransactionDao;
    }

    public List<VehicleExpenseDetails> getTransportVehicleExpenseByDate(int instituteId, int academicSessionId, Integer date){
        if (instituteId <= 0 || academicSessionId <= 0 || date <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid details."));
        }

        return transportVehicleTransactionDao.getTransportVehicleExpenseByDate(instituteId, academicSessionId, date);

    }

    public boolean addVehicleExpense(int instituteId, List<VehicleExpensePayload> vehicleExpensePayloadList){
        if (instituteId <= 0 ) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid details."));
        }

        validateVehicleExpenseDetails(vehicleExpensePayloadList);
        return transportVehicleTransactionDao.addVehicleExpense(instituteId, vehicleExpensePayloadList);
    }

    private void validateVehicleExpenseDetails(List<VehicleExpensePayload> vehicleExpensePayloadList) {
        if (CollectionUtils.isEmpty(vehicleExpensePayloadList)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid details."));
        }

        for (VehicleExpensePayload vehicleExpensePayload : vehicleExpensePayloadList) {
            if (vehicleExpensePayload == null) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid details."));
            }

            if (vehicleExpensePayload.getVehicleId() <= 0) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid vehicle ID."));
            }

            if (vehicleExpensePayload.getAmount() == null || vehicleExpensePayload.getAmount() < 0) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid amount."));
            }

            if (vehicleExpensePayload.getTransactionDate() == null || vehicleExpensePayload.getTransactionDate() <= 0) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid transaction date."));
            }

            if (vehicleExpensePayload.getTransactionMode() == null) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Transaction mode is required."));
            }

            if (vehicleExpensePayload.getCategoryType() == null) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Category type is required."));
            }

            if (vehicleExpensePayload.getCreatedUserId() == null) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Created user ID is required."));
            }

            if (vehicleExpensePayload.getUpdatedUserId() == null) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Updated user ID is required."));
            }

            if (vehicleExpensePayload.getRate() == null && vehicleExpensePayload.getRate() < 0) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid rate."));
            }

            if(vehicleExpensePayload.getQuantity() == null && vehicleExpensePayload.getQuantity() < 0) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid quantity."));
            }
        }
    }

    public boolean addVehicleLogs(int instituteId, int academicSessionId, List<VehicleLogPayload> vehicleLogPayloadList){
        if (instituteId <= 0 || academicSessionId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid details."));
        }
        validateVehicleLogDetails(vehicleLogPayloadList);
        return transportVehicleTransactionDao.addVehicleLogs(instituteId, academicSessionId, vehicleLogPayloadList);
    }

    private void validateVehicleLogDetails(List<VehicleLogPayload> vehicleLogPayloadList) {
        if (CollectionUtils.isEmpty(vehicleLogPayloadList)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid details."));
        }

        for (VehicleLogPayload vehicleLogPayload : vehicleLogPayloadList) {
            if (vehicleLogPayload == null) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid details."));
            }

            if (vehicleLogPayload.getVehicleId() <= 0) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid vehicle ID."));
            }

            if ((vehicleLogPayload.getMorningKmReading() == null || vehicleLogPayload.getMorningKmReading() < 0) && (vehicleLogPayload.getEveningKmReading() == null || vehicleLogPayload.getEveningKmReading() < 0)) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid reading."));
            }


            if (vehicleLogPayload.getLogDate() == null || vehicleLogPayload.getLogDate() <= 0) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid transaction date."));
            }


            if (vehicleLogPayload.getCreatedUserId() == null) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Created user ID is required."));
            }

            if (vehicleLogPayload.getUpdatedUserId() == null) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Updated user ID is required."));
            }

        }
    }

    public List<VehicleLogDetails> getTransportVehicleLogByDate(int instituteId, int academicSessionId, Integer date){
        if (instituteId <= 0 || academicSessionId <= 0 || date <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid details."));
        }

        return transportVehicleTransactionDao.getTransportVehicleLogByDate(instituteId, academicSessionId, date);

    }

    public UUID addOtherExpense(int instituteId, UUID userId, VehicleExpensePayload vehicleExpensePayload){
        if (instituteId <= 0 && vehicleExpensePayload.getAcademicSessionId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute details."));
        }
        validateOtherExpenseDetails(vehicleExpensePayload, false);
        return transportVehicleTransactionDao.addOtherExpense(instituteId, vehicleExpensePayload);
    }

    private void validateOtherExpenseDetails(VehicleExpensePayload vehicleExpensePayload, boolean update) {
        if (vehicleExpensePayload == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid details."));
        }

        if (vehicleExpensePayload.getVehicleId() <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid vehicle ID."));
        }

        if (vehicleExpensePayload.getAmount() == null || vehicleExpensePayload.getAmount() < 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid amount."));
        }

        if (vehicleExpensePayload.getTransactionDate() == null || vehicleExpensePayload.getTransactionDate() <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid transaction date."));
        }

        if (vehicleExpensePayload.getTransactionMode() == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Transaction mode is required."));
        }

        if (vehicleExpensePayload.getCategoryType() == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Category type is required."));
        }

        if (vehicleExpensePayload.getCreatedUserId() == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Created user ID is required."));
        }

        if (vehicleExpensePayload.getUpdatedUserId() == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Updated user ID is required."));
        }

        if(update) {
            if (vehicleExpensePayload.getTransactionId() == null) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Transaction ID is required."));
            }
        }

    }

    public boolean updateOtherExpense(int instituteId, UUID userId, VehicleExpensePayload vehicleExpensePayload) {
        if (instituteId <= 0 && vehicleExpensePayload.getAcademicSessionId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
        }

        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
        }

        validateOtherExpenseDetails(vehicleExpensePayload, true);
        return transportVehicleTransactionDao.updateOtherExpense(instituteId, vehicleExpensePayload);
    }

    public SearchResultWithPagination<VehicleExpenseDetails> getVehicleExpenseWithPagination(int instituteId, UUID userId, int academicSessionId, Integer limit, Integer offset) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
        }

        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
        }

        if (academicSessionId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid academic session id"));
        }

        if (offset == null || offset < 0 || limit == null || limit <= 0) {
            offset = 0;
            limit = Integer.MAX_VALUE;
        }

        Set<VehicleLogCategory> vehicleLogCategories = new HashSet<>(Arrays.asList(
                VehicleLogCategory.MAINTENANCE,
                VehicleLogCategory.REPAIR,
                VehicleLogCategory.SERVICES,
                VehicleLogCategory.OTHER
        ));


        return transportVehicleTransactionDao.getVehicleOtherExpense(
                instituteId, userId, academicSessionId, vehicleLogCategories, limit, offset);

    }

    public boolean deleteVehicleExpense(int instituteId, int academicSessionId, UUID userId, UUID transactionId) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
        }

        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
        }

        if (academicSessionId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid academic session id"));
        }

        if(transactionId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_EXPENSE_TRANSACTION, "Invalid transaction id"));
        }

        return transportVehicleTransactionDao.deleteVehicleExpense(instituteId, academicSessionId, transactionId);
    }

}
