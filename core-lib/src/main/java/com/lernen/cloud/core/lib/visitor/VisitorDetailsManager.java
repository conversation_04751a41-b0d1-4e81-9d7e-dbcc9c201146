package com.lernen.cloud.core.lib.visitor;

import com.embrate.cloud.core.api.homework.HomeworkDocumentType;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.staff.StaffDocumentType;
import com.lernen.cloud.core.api.student.DownloadDocumentWrapper;
import com.lernen.cloud.core.api.student.StudentDocumentType;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.api.visitor.VisitorDetails;
import com.lernen.cloud.core.api.visitor.VisitorDetailsPayload;
import com.lernen.cloud.core.api.visitor.VisitorDocumentType;
import com.lernen.cloud.core.api.visitor.VisitorStatus;
import com.lernen.cloud.core.lib.document.DocumentManager;
import com.lernen.cloud.core.lib.google.auth.GoogleRecaptchaManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.DocumentUtils;
import com.lernen.cloud.core.utils.ImageUtils;
import com.lernen.cloud.dao.tier.visitor.VisitorDao;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.util.*;

public class VisitorDetailsManager {
	private static final Logger logger = LogManager.getLogger(VisitorDetailsManager.class);

	private final VisitorDao visitorDao;
	private final UserPermissionManager userPermissionManager;
	private final DocumentManager documentManager;
	private final GoogleRecaptchaManager googleRecaptchaManager;
	private final TransactionTemplate transactionTemplate;

	public VisitorDetailsManager(VisitorDao visitorDao, UserPermissionManager userPermissionManager, DocumentManager documentManager, GoogleRecaptchaManager googleRecaptchaManager, TransactionTemplate transactionTemplate) {
		this.visitorDao = visitorDao;
		this.userPermissionManager = userPermissionManager;
		this.documentManager = documentManager;
		this.googleRecaptchaManager = googleRecaptchaManager;
		this.transactionTemplate = transactionTemplate;
	}

	public UUID addVisitorDetails(int instituteId, int academicSessionId, VisitorDetailsPayload visitorDetailsPayload, String imageName,
								  FileData visitorImage, String visitorIdDocumentName, FileData visitorIdDocument, String recaptchaAuthenticationKey) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
		}
		final boolean verified = googleRecaptchaManager.validateRecaptchaUserToken(recaptchaAuthenticationKey);
		if (!verified) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_CAPTCHA, "Invalid captcha code"));
		}

		final UUID visitorId = transactionTemplate.execute(new TransactionCallback<UUID>() {
			@Override
			public UUID doInTransaction(TransactionStatus status) {
				validateVisitorDetails(visitorDetailsPayload, false);

				UUID visitorId = visitorDao.addVisitorDetails(visitorDetailsPayload);

				if (visitorId == null) {
					logger.error("Unable to create visitor {}", visitorDetailsPayload);
					return null;
				}
				if (visitorImage != null) {
					uploadVisitorImage(instituteId, academicSessionId, visitorId, imageName, visitorImage, VisitorDocumentType.VISITOR_PROFILE_IMAGE);
				}
				if (visitorIdDocument != null) {
					uploadVisitorImage(instituteId, academicSessionId, visitorId, visitorIdDocumentName, visitorIdDocument, VisitorDocumentType.VISITOR_AADHAR_CARD);
				}
				return visitorId;
			}
		});

		return visitorId;
	}

	private void uploadVisitorImage(int instituteId, int academicSessionId, UUID visitorId, String documentName, FileData uploadPhoto, VisitorDocumentType visitorDocumentType) {
		try {
			final List<Document<VisitorDocumentType>> studentDocumentsUploaded = uploadDocument(
					instituteId, visitorId, visitorDocumentType, academicSessionId, documentName, uploadPhoto);
			if (CollectionUtils.isEmpty(studentDocumentsUploaded)) {
				logger.error("Unable to upload photo for visitor {}", visitorId);
			}

		} catch (final Exception e) {
			logger.error("Error while uploading photo for visitor {}", visitorId, e);
		}
	}

	private void validateVisitorDetails(VisitorDetailsPayload visitorDetailsPayload, boolean update) {
		if (visitorDetailsPayload == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_VISITOR_DETAILS, "Invalid visitor details"));
		}


		if (visitorDetailsPayload.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}


		if (visitorDetailsPayload.getAcademicSessionId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_ACADEMIC_SESSION_DETAILS, "Invalid academic session id"));
		}

		if (StringUtils.isBlank(visitorDetailsPayload.getVisitorName())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_VISITOR_DETAILS, "Invalid visitor name"));
		}

		if (StringUtils.isBlank(visitorDetailsPayload.getContactNumber())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_VISITOR_DETAILS, "Invalid contact number"));
		}

		if (StringUtils.isBlank(visitorDetailsPayload.getPurposeOfVisit())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_VISITOR_DETAILS, "Invalid purpose of visit"));
		}
		if (!update) {
			if (visitorDetailsPayload.getUserId() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_VISITOR_DETAILS, "Invalid staff "));
			}
		}
		if (update) {
			if (visitorDetailsPayload.getVisitorId() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_VISITOR_DETAILS, "Invalid visitor id"));
			}
		}
	}

	public boolean updateVisitorDetails(int instituteId, UUID userId, VisitorDetailsPayload visitorDetailsPayload, String imageName,
										FileData visitorImage, String visitorIdDocumentName, FileData visitorIdDocument) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
		}

		visitorDetailsPayload.setInstituteId(instituteId);

		if (userId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}
		validateVisitorDetails(visitorDetailsPayload, true);
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.VISITORS_DESK_ADMIN_ACCESS);

		final boolean result = transactionTemplate.execute(new TransactionCallback<Boolean>() {
			@Override
			public Boolean doInTransaction(TransactionStatus status) {

				boolean result = visitorDao.updateVisitorDetails(instituteId, visitorDetailsPayload);

				if (visitorImage != null) {
					uploadVisitorImage(instituteId, visitorDetailsPayload.getAcademicSessionId(), visitorDetailsPayload.getVisitorId(), imageName, visitorImage, VisitorDocumentType.VISITOR_PROFILE_IMAGE);
				}
				if (visitorIdDocument != null) {
					uploadVisitorImage(instituteId, visitorDetailsPayload.getAcademicSessionId(), visitorDetailsPayload.getVisitorId(), visitorIdDocumentName, visitorIdDocument, VisitorDocumentType.VISITOR_AADHAR_CARD);
				}
				return result;
			}
		});

		return result;
	}

	public List<VisitorDetails> getVisitorDetails(int instituteId, UUID userId, int academicSessionId, Set<VisitorStatus> visitorStatusSet, Integer limit, Integer offset) {

		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
		}

		if (userId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid academic session id"));
		}

		final SearchResultWithPagination<VisitorDetails> visitorDetailsResultWithPagination = getVisitorDetailsWithPagination(instituteId, userId, academicSessionId, visitorStatusSet, limit, offset);

		return visitorDetailsResultWithPagination == null ? null : visitorDetailsResultWithPagination.getResult();
	}

	public SearchResultWithPagination<VisitorDetails> getVisitorDetailsWithPagination(int instituteId, UUID userId, int academicSessionId, Set<VisitorStatus> visitorStatusSet, Integer limit, Integer offset) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
		}

		if (userId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid academic session id"));
		}

		userId = userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.VISITORS_DESK_ADMIN_ACCESS, false) ? null : userId;

//		When user don't have access of Visitor's Details then we give only those visitor's details who come to visit that user
		if(userId != null && visitorStatusSet.contains(VisitorStatus.PENDING)) {
			visitorStatusSet.remove(VisitorStatus.PENDING);
			visitorStatusSet.add(VisitorStatus.STAFF_APPROVAL_PENDING);
		}

		if (offset == null || offset < 0 || limit == null || limit <= 0) {
			offset = 0;
			limit = Integer.MAX_VALUE;
		}

		return visitorDao.getVisitorDetails(instituteId, userId, academicSessionId, visitorStatusSet, limit, offset);
	}


	public VisitorDetails getVisitorDetailsByVisitorId(int instituteId, int academicSessionId, UUID visitorId) {

		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid academic session id"));
		}

		if (visitorId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid visitor id."));
		}

		return visitorDao.getVisitorDetailsByVisitorId(instituteId, academicSessionId, visitorId);
	}

	public boolean updateVisitorStatus(int instituteId, UUID userId, int academicSessionId, UUID visitorId, VisitorStatus visitorStatus, String reason, String comment) {

		if (userId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid institute id."));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid academic session id"));
		}

		if (visitorId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_VISITOR_DETAILS, "Invalid visitor id."));
		}

		if (visitorStatus == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid visitor Status."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.VISITORS_DESK_ADMIN_ACCESS);

		UUID visitorStatusUpdatedByUserId = (visitorStatus.equals(VisitorStatus.STAFF_APPROVAL_PENDING)) ? userId : null ;

		return visitorDao.updateVisitorStatus(instituteId, visitorStatusUpdatedByUserId, academicSessionId, visitorId, visitorStatus, reason, comment);

	}

	public boolean updateVisitorAssignedStaff(int instituteId, UUID userId, int academicSessionId, UUID visitorId, UUID staffId) {

		if (userId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid institute id."));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid academic session id"));
		}

		if (visitorId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_VISITOR_DETAILS, "Invalid visitor id."));
		}

		if (staffId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid staff id."));
		}

		UUID receptionistId = (userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.VISITORS_DESK_ADMIN_ACCESS, false)) ? userId : null;

		if (receptionistId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "The user doesn't have permission to update the visitor."));
		}

		final VisitorDetails visitorDetails = visitorDao.getVisitorDetailsByVisitorId(instituteId, academicSessionId, visitorId);
		UUID previousStaffId = visitorDetails.getStaff().getStaffId();

		return visitorDao.updateVisitorAssignedStaff(instituteId, academicSessionId, visitorId, staffId, receptionistId, previousStaffId);
	}

	public List<Document<VisitorDocumentType>> uploadDocument(int instituteId, UUID visitorId,
															  VisitorDocumentType visitorDocumentType, int academicSessionId, String documentName, FileData document) {
		final VisitorDetails visitorDetails = visitorDao.getVisitorDetailsByVisitorId(instituteId, academicSessionId, visitorId);
		if (visitorDetails == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_VISITOR_DETAILS, "Visitor does not exists"));
		}
		return uploadDocument(instituteId, visitorDetails, visitorDocumentType, documentName, document);
	}

	public List<Document<VisitorDocumentType>> uploadDocument(int instituteId, VisitorDetails visitorDetails,
															  VisitorDocumentType visitorDocumentType, String documentName, FileData document) {
		final UUID visitorId = visitorDetails.getVisitorId();

		List<Document<VisitorDocumentType>> visitorDocuments = visitorDetails.getVisitorDocuments();
		if (CollectionUtils.isEmpty(visitorDocuments)) {
			visitorDocuments = new ArrayList<>();
		}

		if (visitorDocuments.size() == DocumentUtils.FILE_COUNT_LIMIT) {
			logger.error("Visitor already have " + DocumentUtils.FILE_COUNT_LIMIT
					+ " documents uploaded. Please delete some to upload more.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COUNT_OF_DOCUMENT,
					"Visitor already have " + DocumentUtils.FILE_COUNT_LIMIT
							+ " documents uploaded. Please delete some to upload more."));
		}

		final double length = document.getContent().length / DocumentUtils.ONE_KB;
		if (length > DocumentUtils.FILE_SIZE_LIMIT) {
			logger.error("Size {} Greater than " + DocumentUtils.FILE_SIZE_LIMIT + " kb", length);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SIZE_OF_DOCUMENT,
					"Size Of document cannot be greater than " + DocumentUtils.FILE_SIZE_LIMIT + " kb"));
		}

		if (!DocumentUtils.validVisitorDocument(visitorDetails, visitorDocumentType, documentName, document)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT,
					"Document already exists with give type and name"));
		}

		/**
		 * upload document
		 */
		Document<VisitorDocumentType> uploadedDocument = uploadDocument(instituteId, document, visitorDocumentType, documentName, visitorId, false);

		List<Document<VisitorDocumentType>> finalVisitorDocuments = getFinalVisitorDocuments(visitorDocuments, uploadedDocument);

		return visitorDao.updateDocuments(visitorId, finalVisitorDocuments) ? finalVisitorDocuments : null;
	}

	private Document<VisitorDocumentType> uploadDocument(int instituteId, FileData fileData,
														 VisitorDocumentType visitorDocumentType, String documentName, UUID visitorId,
														 boolean isThumbnail) {
		final UUID documentId = UUID.randomUUID();
		String fileExtension = FilenameUtils.getExtension(fileData.getFileName());
		Document<VisitorDocumentType> newDocument = new Document<>(visitorDocumentType, documentName, documentId,
				fileExtension, (int) (System.currentTimeMillis() / 1000l));
		String s3Path = buildDocumentPath(instituteId, visitorId, newDocument);
		boolean documentUploaded = documentManager.uploadDocument(s3Path, instituteId, fileData);

		if (!documentUploaded) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT,
					"Unable to upload document. Please try again"));
		}
		return newDocument;
	}

	private String buildDocumentPath(int instituteId, UUID visitorId, Document<VisitorDocumentType> visitorDocument) {
		final StringBuilder s3Path = new StringBuilder();
		s3Path.append("institute_id=").append(instituteId)
				.append(DocumentUtils.S3_FILE_PATH_DELIMITER).append("visitors_images")
				.append(DocumentUtils.S3_FILE_PATH_DELIMITER).append("visitor_id=").append(visitorId)
				.append(DocumentUtils.S3_FILE_PATH_DELIMITER).append("document_id=").append(visitorDocument.getDocumentId())
				.append(".").append(visitorDocument.getFileExtension());
		return s3Path.toString();
	}

	private List<Document<VisitorDocumentType>> getFinalVisitorDocuments(List<Document<VisitorDocumentType>> existingVisitorDocuments,
																		 Document<VisitorDocumentType> uploadedDocument) {
		List<Document<VisitorDocumentType>> finalVisitorDocuments = new ArrayList<>();
		//Not adding the document with new document type, adding rest all.
		for (Document<VisitorDocumentType> visitorDocument : existingVisitorDocuments) {
			if (visitorDocument.getDocumentType() != VisitorDocumentType.OTHER
					&& visitorDocument.getDocumentType() == uploadedDocument.getDocumentType()) {
				continue;
			}
			finalVisitorDocuments.add(visitorDocument);
		}
		//Adding new uploaded document
		finalVisitorDocuments.add(uploadedDocument);
		return finalVisitorDocuments;
	}

	public DownloadDocumentWrapper<Document<VisitorDocumentType>> downloadDocument(int instituteId, int academicSessionId, UUID visitorId,
																				   UUID documentId) {
		if (documentId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Invalid document Information"));
		}
		final VisitorDetails visitorDetails = visitorDao.getVisitorDetailsByVisitorId(instituteId, academicSessionId, visitorId);
		if (visitorDetails == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_VISITOR_DETAILS, "Visitor does not exists"));
		}

		final List<Document<VisitorDocumentType>> visitorDocuments = visitorDetails.getVisitorDocuments();
		if (CollectionUtils.isEmpty(visitorDocuments)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}
		for (final Document<VisitorDocumentType> visitorDocument : visitorDocuments) {
			if (documentId.equals(visitorDocument.getDocumentId())) {
				final String s3Path = buildDocumentPath(instituteId, visitorId, visitorDocument);
				return new DownloadDocumentWrapper<>(visitorDocument,
						documentManager.downloadDocument(instituteId, s3Path));
			}
		}
		return null;
	}

	public List<Document<VisitorDocumentType>> deleteDocument(int instituteId, int academicSessionId, UUID visitorId, UUID documentId) {
		final VisitorDetails visitorDetails = visitorDao.getVisitorDetailsByVisitorId(instituteId, academicSessionId, visitorId);
		return deleteDocument(instituteId, visitorDetails, documentId);
	}

	public List<Document<VisitorDocumentType>> deleteDocument(int instituteId, VisitorDetails visitorDetails, UUID documentId) {
		if (visitorDetails == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_VISITOR_DETAILS, "Visitor does not exists"));
		}

		if (documentId == null || instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Invalid document Information"));
		}
		final UUID visitorId = visitorDetails.getVisitorId();
		final List<Document<VisitorDocumentType>> visitorDocuments = visitorDetails.getVisitorDocuments();
		if (CollectionUtils.isEmpty(visitorDocuments)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}
		final Iterator<Document<VisitorDocumentType>> iterator = visitorDocuments.iterator();
		boolean deleted = false;
		while (iterator.hasNext()) {
			final Document<VisitorDocumentType> visitorDocument = iterator.next();
			if (documentId.equals(visitorDocument.getDocumentId())) {
				final String s3Path = buildDocumentPath(instituteId, visitorId, visitorDocument);
				if (documentManager.deleteDocument(instituteId, s3Path)) {
					iterator.remove();
					deleted = true;
					if (visitorDocument.getDocumentType() == VisitorDocumentType.VISITOR_PROFILE_IMAGE) {
					}
				}
				break;
			}
		}
		if (!deleted) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}

		return visitorDao.updateDocuments(visitorId, visitorDocuments) ? visitorDocuments : null;
	}

	public DownloadDocumentWrapper<Document<VisitorDocumentType>> downloadVisitorImage(int instituteId, VisitorDetails visitorDetails) {
		final List<Document<VisitorDocumentType>> visitorDocuments = visitorDetails.getVisitorDocuments();
		if (CollectionUtils.isEmpty(visitorDocuments)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}
		for (final Document<VisitorDocumentType> visitorDocument : visitorDocuments) {
			if (visitorDocument.getDocumentType() == VisitorDocumentType.VISITOR_PROFILE_IMAGE) {
				final String s3Path = buildDocumentPath(instituteId, visitorDetails.getVisitorId(), visitorDocument);
				return new DownloadDocumentWrapper<>(visitorDocument,
						documentManager.downloadDocument(instituteId, s3Path));
			}
		}
		return null;
	}
}