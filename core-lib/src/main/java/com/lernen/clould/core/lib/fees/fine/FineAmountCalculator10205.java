package com.lernen.clould.core.lib.fees.fine;

import com.embrate.cloud.core.api.payment.PaymentApplicableFine;
import com.embrate.cloud.core.api.payment.PaymentRequest;
import com.lernen.cloud.core.api.fees.FeeConfigurationResponse;
import com.lernen.cloud.core.api.fees.fine.FeesFinePreferences;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.dao.tier.fees.configuration.FeeConfigurationDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * This fine calculator computes the fixed fine applicable for due fees
 *
 * <AUTHOR>
 */
public class FineAmountCalculator10205 implements IFineAmountCalculator {

    private static final Logger logger = LogManager.getLogger(FineAmountCalculator10205.class);
    private final UserPreferenceSettings userPreferenceSettings;

    private final FeeConfigurationDao feeConfigurationDao;

    public FineAmountCalculator10205(UserPreferenceSettings userPreferenceSettings, FeeConfigurationDao feeConfigurationDao) {
        this.userPreferenceSettings = userPreferenceSettings;
        this.feeConfigurationDao = feeConfigurationDao;
    }

    @Override
    public Double computeFineAmount(int instituteId, UUID feeId, Integer dueDate, Integer fineDate) {
        FeeConfigurationResponse feeConfigurationResponse = feeConfigurationDao.getFeeConfiguration(instituteId, feeId);
        final FeesFinePreferences feesFinePreferences = userPreferenceSettings.getFeesFinePreferences(instituteId);
        return computeFineAmount(instituteId, feeConfigurationResponse, dueDate, fineDate, feesFinePreferences);
    }

    private Double computeFineAmount(int instituteId, FeeConfigurationResponse feeConfigurationResponse, Integer dueDate, Integer fineDate, FeesFinePreferences feesFinePreferences) {
        if (dueDate == null || dueDate <= 0) {
            logger.warn("Due date is not set for computing fixed fine. Skipping");
            return 0d;
        }
        if (fineDate == null || fineDate <= 0) {
            logger.warn("Fine date is not set for computing fixed fine. Skipping");
            return 0d;
        }
        if (feesFinePreferences.getFineExemptDays() < 0) {
            logger.warn("Fine exempt days are negative. Not computing fixed fine.");
            return 0d;
        }

        if(feeConfigurationResponse == null || !feeConfigurationResponse.getFeeConfigurationBasicInfo().isFineApplicable()){
            logger.warn("Fees not present or applicable for fine. Not computing fixed fine.");
            return 0d;
        }

//        String feeName = formatToLower(feeConfigurationResponse.getFeeConfigurationBasicInfo().getFeeName());
//        if (!ALLOWED_FEES_NAME.contains(feeName)) {
//            return 0d;
//        }


        int dueDateNextDayStart = DateUtils.getNextDayStart(dueDate, DateUtils.DEFAULT_TIMEZONE);
        DateTime dueDateNextDayStartDateTime = DateUtils.getDefaultZoneDateTime(dueDateNextDayStart);
        DateTime adjustedDueDateNextDayStartDateTime = dueDateNextDayStartDateTime.plusDays(feesFinePreferences.getFineExemptDays());
        int adjustedDueDateNextDayStart = (int) (adjustedDueDateNextDayStartDateTime.getMillis() / 1000L);

        int fineDateDayStart = DateUtils.getDayStartDefaultTimezone(fineDate);

        // Both are day start ts so equal condition holds
        if (adjustedDueDateNextDayStart <= fineDateDayStart) {
            return feesFinePreferences.getFixedFineAmount();
        }

        return 0d;
    }

    @Override
    public PaymentApplicableFine computeTotalFineAmount(int instituteId, PaymentRequest paymentRequest, Integer fineDate) {
        if (paymentRequest == null || CollectionUtils.isEmpty(paymentRequest.getFeeIds())) {
            return new PaymentApplicableFine(false, null, null, null);
        }
        List<FeeConfigurationResponse> feeConfigurationResponseList = feeConfigurationDao.getFeeConfigurations(instituteId, paymentRequest.getFeeIds());
        if (CollectionUtils.isEmpty(feeConfigurationResponseList)) {
            return new PaymentApplicableFine(false, null, null, null);
        }

        final FeesFinePreferences feesFinePreferences = userPreferenceSettings.getFeesFinePreferences(instituteId);

        double totalAmount = 0d;
        Map<UUID, Double> feesFineMap = new HashMap<>();
        for (FeeConfigurationResponse feeConfigurationResponse : feeConfigurationResponseList) {
//            String feeName = formatToLower(feeConfigurationResponse.getFeeConfigurationBasicInfo().getFeeName());
//            if (!ALLOWED_FEES_NAME.contains(feeName)) {
//                continue;
//            }
            Double fineAmount = computeFineAmount(instituteId, feeConfigurationResponse, feeConfigurationResponse.getFeeConfigurationBasicInfo().getDueDate(), fineDate, feesFinePreferences);
            feesFineMap.put(feeConfigurationResponse.getFeeConfigurationBasicInfo().getFeeId(), fineAmount);
            totalAmount = NumberUtils.addValues(totalAmount, fineAmount);
        }

        if (Double.compare(totalAmount, 0d) == 0) {
            return new PaymentApplicableFine(false, null, null, null);
        }
        return new PaymentApplicableFine(true, "Late Fees Fine", "This amount is added extra as selected fees for payment are overdue.", feesFineMap);
    }

}
