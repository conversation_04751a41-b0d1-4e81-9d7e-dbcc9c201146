package com.embrate.cloud.core.utils;

import java.util.*;

/**
 * <AUTHOR>
 * @created_at 29/08/23 : 14:31
 **/
public class EMapUtils {

    public static interface MapFunction<T, K, V> {
        K getKey(T entry);

        V getValue(T entry);
    }

    public static <T, K, V> Map<K, V> getMap(List<T> list, MapFunction<T, K, V> func) {
        Map<K, V> map = new HashMap<>();
        for (T entry : list) {
            map.put(func.getKey(entry), func.getValue(entry));
        }
        return map;
    }

    public static <K, V extends Comparable<? super V>> LinkedHashMap<K, V> sortMapByValue(
            Map<K, V> map, boolean isDescending) {
        if (map == null || map.isEmpty()) {
            return new LinkedHashMap<>();
        }

        // Create a list of map entries
        List<Map.Entry<K, V>> entryList = new ArrayList<>(map.entrySet());

        // Sort the list based on values
        entryList.sort((entry1, entry2) -> {
            if (entry1 == null || entry1.getValue() == null) {
                return 1; // Place null entries at the end
            }
            if (entry2 == null || entry2.getValue() == null) {
                return -1; // Place null entries at the end
            }
            return isDescending
                    ? entry2.getValue().compareTo(entry1.getValue())
                    : entry1.getValue().compareTo(entry2.getValue());
        });

        // Populate a LinkedHashMap to maintain the sorted order
        LinkedHashMap<K, V> sortedMap = new LinkedHashMap<>();
        for (Map.Entry<K, V> entry : entryList) {
            sortedMap.put(entry.getKey(), entry.getValue());
        }

        return sortedMap;
    }

    public static <K, V> void mergeTwoMaps(Map<K, List<V>> mainMap, Map<K, List<V>> secondaryMap) {
        if (mainMap == null || mainMap.isEmpty()) {
            mainMap = new HashMap<>();
        }
        if (secondaryMap == null || secondaryMap.isEmpty()) {
            return; // Nothing to merge
        }
        for (Map.Entry<K, List<V>> entry : secondaryMap.entrySet()) {
            K key = entry.getKey();
            if (!mainMap.containsKey(key)) {
                mainMap.put(key, entry.getValue());
            } else {
                mainMap.get(key).addAll(entry.getValue());
            }
        }
    }
}
