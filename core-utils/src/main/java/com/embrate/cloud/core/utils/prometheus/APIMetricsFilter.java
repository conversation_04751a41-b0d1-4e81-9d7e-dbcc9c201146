package com.embrate.cloud.core.utils.prometheus;

import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import io.prometheus.client.Summary;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class APIMetricsFilter implements Filter {

	private static final Logger logger = LoggerFactory.getLogger(APIMetricsFilter.class);

	private boolean metricsEnabled;
	// Exclusion list for specific routes
	private static final Set<Pattern> exclusionList = new HashSet<>();

	// Constants
	private static final String ROUTE_UNKNOWN_PREFIX = "UNKNOWN";
	private static final String ROUTE_SYSTIME = "/systime";
	private static final String METRIC_ENABLED = "metric.enabled";
	private static final String METRIC_TYPE_LATENCY = "latency";
	private static final String METRIC_TYPE_REQUEST_COUNT = "request_count";
	private static final String METRIC_TYPE_ERROR_COUNT = "error_count";
	public static final String STATUS = "status";
	public static final String ROUTE = "route";
	public static final String METHOD = "method";
	private static final String SOURCE = "source";
	private static final String INSTITUTE = "institute";
	private static final String SOURCE_HEADER = "X-Client-Type";
	private static final String INSTITUTE_HEADER = "X-Institute-Id";
	private static final String DELIMITER = ",";

	private static final String DEFAULT_INSTITUTE = "INTERNAL(0)";
	private static final String DEFAULT_SOURCE= "INTERNAL";
	private static final String DEFAULT_CLIENT_TYPE = "WEB";
	private static final String ROUTE_REASON = "Route mismatch";
	private static final String METHOD_REASON = "HTTP method mismatch";

	private static final String PROD_ENV="prod-na";
	private static final String NONPROD_ENV="nonprodqa";
	private final ArrayList<String> UNKNOWN_ROUTE_ENVIRONMENTS = new ArrayList<>(Arrays.asList(PROD_ENV, NONPROD_ENV));

	// Prometheus metrics
	private static final Histogram requestLatency = Histogram.build()
			.name("api_request_latency_seconds")
			.help("Request latency in seconds.")
			.labelNames(METHOD, ROUTE, SOURCE, INSTITUTE)
			.buckets(0.01, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10, 30, 60)
			.register();

	private static final Counter requestCounter = Counter.build()
			.name("api_requests_total")
			.help("Total API requests.")
			.labelNames(METHOD, ROUTE, STATUS, SOURCE, INSTITUTE)
			.register();

	private static final Counter errorCounter = Counter.build()
			.name("api_errors_total")
			.help("Total API error responses.")
			.labelNames(METHOD, ROUTE, STATUS, SOURCE, INSTITUTE)
			.register();

	// Prometheus Metrics for /systime endpoint
	private static final Summary apiHealthSummary = Summary.build()
			.name("api_health")
			.help("Metrics for /systime.")
			.labelNames(METHOD, ROUTE, STATUS)
			.register();

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {

		// Retrieve the Spring ApplicationContext
		ServletContext context = filterConfig.getServletContext();

       /* Fetch the value of the 'metric.enabled' property from the system properties.
          The system property "metrics.enabled" can be set to the same value as is defined for the
          prometheus configuration "scrapeEnabled". This allows us to reflect the same push and pull configuration
          for a given environment
        */

		final String metricEnabledString = System.getProperty(METRIC_ENABLED);
//		metricsEnabled = Boolean.parseBoolean(metricEnabledString);
		metricsEnabled = true;
		logger.info("Metrics Expose Enabled: {}", metricsEnabled);
		if (!metricsEnabled) {
			logger.info("Metrics collection is disabled based on configuration.");
			return;  // Skip further filter setup
		}
		exclusionList.add(Pattern.compile("/metrics"));
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
		// Only proceed if metrics are enabled
//		System.out.println("metricsEnabled "+ metricsEnabled+ ", "+PathRegistry.isRegistered());
		if (!metricsEnabled || !PathRegistry.isRegistered()) {
			chain.doFilter(request, response);
			return;
		}

//		System.out.println("after metricsEnabled "+ metricsEnabled+ ", "+PathRegistry.isRegistered());
		if (!(request instanceof HttpServletRequest) || !(response instanceof HttpServletResponse)) {
			chain.doFilter(request, response);
			return;
		}

		HttpServletRequest httpRequest = (HttpServletRequest) request;
		HttpServletResponse httpResponse = (HttpServletResponse) response;

		String method = httpRequest.getMethod();
		String requestUri = httpRequest.getRequestURI();
		if (isExcluded(requestUri)) {
			chain.doFilter(request, response);
			return;
		}
		long startTime = System.currentTimeMillis();
		String route = getRouteForRequest(requestUri,method);
		long endTime = System.currentTimeMillis();
		long duration = endTime - startTime;
		logger.debug("Pattern matching processing time for {}: {} ms", httpRequest.getRequestURI(), duration);
		if (isExcluded(route)) {
			chain.doFilter(request, response);
			return;
		}

		String source = Optional.ofNullable(httpRequest.getHeader(SOURCE_HEADER)).filter(sourceHeader -> !sourceHeader.isEmpty()).orElse(DEFAULT_SOURCE);
		String pilot = Optional.ofNullable(httpRequest.getHeader(INSTITUTE_HEADER)).filter(pilotHeader -> !pilotHeader.isEmpty()).orElse(DEFAULT_INSTITUTE);

		if (ROUTE_SYSTIME.equals(route)) {
			handleSystimeMetrics(chain, httpRequest, httpResponse, method);
		} else {
			handleStandardMetrics(chain, httpRequest, httpResponse, method, route, source, pilot);
		}
	}

	private String getRouteForRequest(String requestUri, String httpMethod) {
//		System.out.println("requestUri " + requestUri + ", httpMethod" + httpMethod);
		logger.info("requestUri " + requestUri + ", httpMethod" + httpMethod);
		AbstractMap.SimpleEntry<String,Boolean> result = PathRegistry.getBestMatchingPattern(requestUri, httpMethod);
//		System.out.println("result " + result);
		String route = result.getKey();
		Boolean isRequestMethodMatch = result.getValue();
		if (route == null || !isRequestMethodMatch) {
			// No matching route found
			logger.warn("No matching route found for URI: {}", requestUri);
			return ROUTE_UNKNOWN_PREFIX + "-" + httpMethod + "-"+requestUri;
		}

		return route;
	}


	private boolean isExcluded(String route) {
		return exclusionList.stream().anyMatch(pattern -> pattern.matcher(route).matches());
	}

	private void handleSystimeMetrics(FilterChain chain, HttpServletRequest request, HttpServletResponse response, String method) throws IOException, ServletException {
		Summary.Timer requestTimer = apiHealthSummary.labels(METRIC_TYPE_LATENCY, method, "N/A").startTimer();
		try {
			chain.doFilter(request, response);
		} finally {
			int status = response.getStatus();
			apiHealthSummary.labels(METRIC_TYPE_REQUEST_COUNT, method, Integer.toString(status)).observe(1);
			if (status >= 400) {
				apiHealthSummary.labels(METRIC_TYPE_ERROR_COUNT, method, Integer.toString(status)).observe(1);
			}
			requestTimer.observeDuration();
		}
	}

	private void handleStandardMetrics(FilterChain chain, HttpServletRequest request, HttpServletResponse response, String method, String route, String source, String pilot) throws IOException, ServletException {
		Histogram.Timer requestTimer = requestLatency.labels(method, route, source, pilot).startTimer();;
//		if (!ROUTE_UNKNOWN.equals(route)) {
//			// Start timer only if it's a known route
//			requestTimer = requestLatency.labels(method, route, source, pilot).startTimer();
//		}
		try {
			chain.doFilter(request, response);
		} finally {
			int status = response.getStatus();
			requestCounter.labels(method, route, Integer.toString(status), source, pilot).inc();

			if (status >= 400) {
				errorCounter.labels(method, route, Integer.toString(status), source, pilot).inc();
			}

			// Only observe latency if it's not an unknown route
			if (requestTimer != null) {
				requestTimer.observeDuration();
			}
		}
	}

	@Override
	public void destroy() {
		// Cleanup code if needed
	}

}



