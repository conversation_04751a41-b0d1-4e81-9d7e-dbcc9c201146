package com.embrate.cloud.core.utils.prometheus;

/**
 * <AUTHOR>
 */

import org.reflections.Reflections;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.Path;
import java.lang.reflect.Method;
import java.util.Set;

public class PathRegistryInit{

	private static final Logger logger = LoggerFactory.getLogger(PathRegistryInit.class);
	private static final String BASE_PACKAGE_1 = "com.lernen.cloud.data.server"; // Hardcoded package to scan
	private static final String BASE_PACKAGE_2 = "com.lernen.cloud"; // Hardcoded package to scan
	private static final String UANNOTATED_METHOD = "non-annotated";
	private static final String ROOT_PATH = "/";
	private static final String DEFAULT_METHOD_PATH= "";

	public PathRegistryInit() {
		contextInitialized();
	}

	public void contextInitialized() {
		long startTime = System.currentTimeMillis();
		int totalRegisteredPaths = 0;
		System.out.println("PathRegistryListener initialized! Scanning package");
		logger.info("Scanning package for @Path annotations...");

		// Scan the package for @Path annotations
		logger.info("Starting scan for package: {}, {}", BASE_PACKAGE_1, BASE_PACKAGE_2);

		Reflections reflections1 = new Reflections(BASE_PACKAGE_1);
		Reflections reflections2 = new Reflections(BASE_PACKAGE_2);
		Set<Class<?>> pathAnnotatedClasses1 = reflections1.getTypesAnnotatedWith(Path.class);
		System.out.println(pathAnnotatedClasses1);
		Set<Class<?>> pathAnnotatedClasses2 = reflections2.getTypesAnnotatedWith(Path.class);
		pathAnnotatedClasses1.addAll(pathAnnotatedClasses2);

		Set<Class<?>> pathAnnotatedClasses = pathAnnotatedClasses1;
		System.out.println(pathAnnotatedClasses.size());
		for (Class<?> clazz : pathAnnotatedClasses) {
			Path classPathAnnotation = clazz.getAnnotation(Path.class);
			String classPath = normalizePath(classPathAnnotation.value());

			// Scan methods for @Path annotations
			for (Method method : clazz.getDeclaredMethods()) {
				String httpMethod = getHttpMethod(method);
				if(httpMethod==null) {
					continue; // Skip methods without HTTP method annotations
				}

				String methodPath = getMethodPath(method);
                /*We register unannotated methods with a http method (GET, POST, PUT, DELETE) with a default path(= "")
                  We also register method path annotated with "/" with a default path(= "")
                 */
				if (UANNOTATED_METHOD.equals(methodPath) || ROOT_PATH.equals(methodPath)) {
					methodPath = DEFAULT_METHOD_PATH;
				}
				System.out.println(classPath +"," + methodPath+"," +httpMethod);
				PathRegistry.registerPath(classPath, methodPath, httpMethod); // Register with HTTP method
				totalRegisteredPaths++;
			}
			logger.debug("Registered class: {} with path: {}", clazz.getName(), classPath);
		}

		long endTime = System.currentTimeMillis();
		System.out.println("Total paths registered "+totalRegisteredPaths);
		logger.info("Time taken to scan and register paths: {} ms", endTime - startTime);
		logger.debug("Total paths registered: {}", totalRegisteredPaths);
	}

	private String getMethodPath(Method method) {
		if (method.isAnnotationPresent(Path.class)) {
			Path methodPathAnnotation = method.getAnnotation(Path.class);
			return normalizePath(methodPathAnnotation.value());
		}
		return UANNOTATED_METHOD;
	}

	private String normalizePath(String path) {
		String normalizedPath;
		if (path.startsWith(ROOT_PATH)) {
			normalizedPath = path;
		} else {
			normalizedPath = ROOT_PATH + path;
		}
		return normalizedPath;
	}

	private String getHttpMethod(Method method) {
		if (method.isAnnotationPresent(javax.ws.rs.GET.class)) {
			return "GET";
		} else if (method.isAnnotationPresent(javax.ws.rs.POST.class)) {
			return "POST";
		} else if (method.isAnnotationPresent(javax.ws.rs.PUT.class)) {
			return "PUT";
		} else if (method.isAnnotationPresent(javax.ws.rs.DELETE.class)) {
			return "DELETE";
		}
		// Add other HTTP methods as needed
		return null;
	}
}
