package com.lernen.cloud.core.utils;

import com.lernen.cloud.core.api.staff.StaffAddressContactInfo;
import com.lernen.cloud.core.api.student.Student;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

public class AddressUtils {

    public static String getStudentAddress(Student student) {

        String presentAddress = getStudentAddress(
                student.getStudentBasicInfo().getPresentAddress(),
                student.getStudentBasicInfo().getPresentCity(),
                student.getStudentBasicInfo().getPresentState(),
                student.getStudentBasicInfo().getPresentZipcode());

        String permanentAddress = getStudentAddress(
                student.getStudentBasicInfo().getPermanentAddress(),
                student.getStudentBasicInfo().getPermanentCity(),
                student.getStudentBasicInfo().getPermanentState(),
                student.getStudentBasicInfo().getPermanentZipcode());

        return StringUtils.isBlank(presentAddress) ? permanentAddress : presentAddress;
    }

    public static String getStudentAddress(String addressDB, String city, String state, String zipcode) {
        StringBuilder address = new StringBuilder();
        boolean textAdded = false;
        if (StringUtils.isNotBlank(addressDB)) {
            address.append(addressDB.trim());
            textAdded = true;
        }
        if (StringUtils.isNotBlank(city)) {
            if (textAdded) {
                address.append(StringConstants.COMMA).append(" ");
            }
            address.append(city.trim());
            textAdded = true;
        }
        if (StringUtils.isNotBlank(state)) {
            if (textAdded) {
                address.append(StringConstants.COMMA).append(" ");
            }
            address.append(state.trim());
            textAdded = true;
        }
        if (StringUtils.isNotBlank(zipcode)) {
            if (textAdded) {
                address.append(StringConstants.COMMA).append(" ");
            }
            address.append(zipcode.trim());
            textAdded = true;
        }
        return address.toString();
    }

    public static String getStaffAddress(StaffAddressContactInfo staffAddressContactInfo) {
        if(staffAddressContactInfo == null) {
            return "";
        }

        String presentAddress = getStaffAddress(
                staffAddressContactInfo.getPresentAddress1(),
                staffAddressContactInfo.getPresentAddress2(),
                staffAddressContactInfo.getPresentCity(),
                staffAddressContactInfo.getPresentState(),
                staffAddressContactInfo.getPresentZipcode());

        String permanentAddress = getStaffAddress(
                staffAddressContactInfo.getPermanentAddress1(),
                staffAddressContactInfo.getPermanentAddress2(),
                staffAddressContactInfo.getPermanentCity(),
                staffAddressContactInfo.getPermanentState(),
                staffAddressContactInfo.getPermanentZipcode());

        return StringUtils.isBlank(presentAddress) ? permanentAddress : presentAddress;
    }

    public static String getStaffAddress(String addressDB1, String addressDB2, String city, String state, String zipcode) {
        StringBuilder address = new StringBuilder();
        boolean textAdded = false;
        if (StringUtils.isNotBlank(addressDB1)) {
            address.append(addressDB1.trim());
            textAdded = true;
        }
        if (StringUtils.isNotBlank(addressDB2)) {
            if (textAdded) {
                address.append(StringConstants.COMMA).append(" ");
            }
            address.append(addressDB2.trim());
            textAdded = true;
        }
        if (StringUtils.isNotBlank(city)) {
            if (textAdded) {
                address.append(StringConstants.COMMA).append(" ");
            }
            address.append(city.trim());
            textAdded = true;
        }
        if (StringUtils.isNotBlank(state)) {
            if (textAdded) {
                address.append(StringConstants.COMMA).append(" ");
            }
            address.append(state.trim());
            textAdded = true;
        }
        if (StringUtils.isNotBlank(zipcode)) {
            if (textAdded) {
                address.append(StringConstants.COMMA).append(" ");
            }
            address.append(zipcode.trim());
            textAdded = true;
        }
        return address.toString();
    }
}
