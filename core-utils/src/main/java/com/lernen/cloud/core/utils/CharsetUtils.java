package com.lernen.cloud.core.utils;

/**
 * 
 * <AUTHOR>
 *
 */
public class CharsetUtils {

	/**
	 * Does not contains chars l, i, o and the number 0
	 */
	public static final char[] PASSWORD_CHARSET = new char[] { 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'j', 'k', 'm',
			'n', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '1', '2', '3', '4', '5', '6', '7', '8', '9' };

	public static final char[] NUMERIC_PASSWORD_CHARSET = new char[] { '1', '2', '3', '4', '5', '6', '7', '8', '9' };

	public static final char[] USERNAME_NUMERIC_CHARSET = new char[] { '1', '2', '3', '4', '5', '6', '7', '8', '9' };

	/**
	 * Does not contains chars l, i, o and the number 0
	 */
	public static final char[] CAPS_UNAMBIGUOUS_CHARSET = new char[]{ 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'M',
		'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '1', '2', '3', '4', '5', '6', '7', '8', '9' };
}
