package com.lernen.cloud.core.utils;

import com.lernen.cloud.core.api.common.FileData;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;

public class ImageUtils {

    protected static int THUMBNAIL_WIDTH = 150;
    protected static int THUMBNAIL_HEIGHT = 150;

    public static FileData createThumbnailInBytes(FileData fileData) {

        try {
            byte[] fileDataByteArr = fileData.getContent();
            ByteArrayInputStream bis = new ByteArrayInputStream(fileDataByteArr);
            BufferedImage fileDataImage = ImageIO.read(bis);

            BufferedImage img = new BufferedImage(THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT, BufferedImage.TYPE_INT_RGB);
            img.createGraphics().drawImage(fileDataImage.getScaledInstance(THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT, Image.SCALE_SMOOTH),0,0,null);

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            String fileName = fileData.getFileName();
            String[] strArr = fileName.split("\\.");
            ImageIO.write(img, strArr.length > 1 ? fileName.split("\\.")[1] : "", baos);

            return new FileData(baos.toByteArray(), fileData.getFileName());
        } catch(Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static File convertFileDataToFile(FileData fileData) throws IOException {
        File tempFile = File.createTempFile("file_", "_" + fileData.getFileName());
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(fileData.getContent());
        }
        return tempFile;
    }
}