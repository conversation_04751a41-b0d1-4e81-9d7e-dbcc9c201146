package com.lernen.cloud.core.utils;

import org.apache.commons.lang.NotImplementedException;
import org.apache.commons.lang3.StringUtils;

import com.lernen.cloud.core.api.common.NameFormat;

/**
 * 
 * <AUTHOR>
 *
 */
public class NameFormatUtils {
	private static String NAME_DELIMITER = " ";
	
	public static String getFormattedName(String name, NameFormat nameFormat) {
		
		if(StringUtils.isBlank(name)) {
			return null;
		}
		
		if(nameFormat == null) {
			return null;
		}
		
		switch (nameFormat) {
		case FIRST_NAME_ONLY:
			return name.split(NAME_DELIMITER)[0].trim();
		case FULL_NAME:
			return name;
		default:
			throw new NotImplementedException();
		}
		
	}

	public static String getCamelCaseFormattedName(String name) {
		String[] words = name.split(NAME_DELIMITER);
		StringBuilder formattedName = new StringBuilder();

		for (String word : words) {
			if (StringUtils.isNotBlank(word)) {
				formattedName.append(Character.toUpperCase(word.charAt(0)))
						.append(word.substring(1).toLowerCase())
						.append(NAME_DELIMITER);
			}
		}

		return formattedName.toString().trim();
	}
}
