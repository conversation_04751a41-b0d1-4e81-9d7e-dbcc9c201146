package com.lernen.cloud.core.utils;

import com.lernen.cloud.core.api.student.Student;
import org.apache.commons.lang.RandomStringUtils;

/**
 * <AUTHOR>
 */
public class PasswordUtils {

    public static String generatePassword(int passwordLength) {
        return RandomStringUtils.random(passwordLength, CharsetUtils.PASSWORD_CHARSET);
    }

    public static String generateNumericPassword(int passwordLength) {
        return RandomStringUtils.random(passwordLength, CharsetUtils.NUMERIC_PASSWORD_CHARSET);
    }

    private static String getDefaultDOBPassword(Student student) {
        String trimmedAdmissionNumber = student.getStudentBasicInfo().getAdmissionNumber().trim();
        if (student.getStudentBasicInfo().getDateOfBirth() == null || student.getStudentBasicInfo().getDateOfBirth() <= 0) {
            return trimmedAdmissionNumber;
        }
        return DateUtils.getFormattedDate(student.getStudentBasicInfo().getDateOfBirth(), DateUtils.CONTINUOUS_DATE_FORMAT, DateUtils.DEFAULT_TIMEZONE);
    }

    public static String getStudentPassword(boolean admissionNumberAuthFlow, int passwordLength, Student student) {
        if (admissionNumberAuthFlow) {
            return getDefaultDOBPassword(student);
        }
        return generateNumericPassword(passwordLength);
    }
}
