package com.lernen.cloud.core.utils;

import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentTaggedDetails;
import com.lernen.cloud.core.api.student.TaggedActions;
import org.springframework.util.CollectionUtils;

public class RestrictionUtils {


    public static boolean isActionRestricted(Student student, TaggedActions taggedAction, boolean throwException) {
        if (student == null || CollectionUtils.isEmpty(student.getStudentTaggedDetailsList())) {
            return false;
        }

        for (StudentTaggedDetails studentTaggedDetails : student.getStudentTaggedDetailsList()) {
            if (studentTaggedDetails.getTaggedActions() == taggedAction) {
                if(throwException) {
                    throw new ApplicationException(new ErrorResponse(
                            ApplicationErrorCode.ACTION_NOT_AUTHORIZED, taggedAction.getErrorMessage()
                    ));
                }
                return true;
            }
        }
        return false;
    }
}
