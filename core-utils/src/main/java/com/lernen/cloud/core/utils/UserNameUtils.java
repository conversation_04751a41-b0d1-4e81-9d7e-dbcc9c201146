package com.lernen.cloud.core.utils;

import org.apache.commons.lang.RandomStringUtils;
import com.lernen.cloud.core.api.user.UserType;

/**
 * <AUTHOR>
 */
public class UserNameUtils {

    public static String createRandomNumericUserName(int length, String instituteUsernameSuffix, UserType userType) {

        char[] characterSet = CharsetUtils.CAPS_UNAMBIGUOUS_CHARSET;
        if(userType == UserType.STUDENT) {
            characterSet = CharsetUtils.USERNAME_NUMERIC_CHARSET;
        }

        String userName = RandomStringUtils.random(length, characterSet);
        return userName + getSuffix(instituteUsernameSuffix);
    }

    public static String getFullUserName(String admissionNumber, String instituteUsernameSuffix) {
        return admissionNumber.trim() + getSuffix(instituteUsernameSuffix);
    }

    private static String getSuffix(String instituteUsernameSuffix) {
        return new StringBuilder().append(StringConstants.AT_THE_RATE)
                .append(instituteUsernameSuffix == null ? "embrate" : instituteUsernameSuffix.trim()).toString();
    }

    public static void main(String[] args) {
        System.out.println(getFullUserName("ES-483", "eis110"));
    }
}
