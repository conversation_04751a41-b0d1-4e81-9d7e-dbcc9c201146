package com.lernen.cloud.core.utils;

import org.apache.velocity.app.Velocity;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;

/**
 * This class initializes the Velocity runtime engine, using the system
 * properties
 * 
 * We may need to create different velocity engine in different modules, and
 * corresponding bean initialization needs to be added in respective module's
 * root spring configuration file
 *
 * <AUTHOR>
 */
public class VelocityLoader {
	private boolean strictReferencesCheck = false;

	public void init() {
		Velocity.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
		Velocity.setProperty("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
		Velocity.setProperty("runtime.log.logsystem.log4j.logger", "org.apache.velocity.runtime.log.Log4JLogChute");
		Velocity.setProperty("runtime.references.strict", Boolean.toString(strictReferencesCheck));
		Velocity.init();
	}

	public void setStrictReferencesCheck(boolean strictReferencesCheck) {
		this.strictReferencesCheck = strictReferencesCheck;
	}
}