package com.lernen.cloud.core.utils.attendance.staff;

import com.embrate.cloud.core.api.calendar.holiday.StaticHoliday;
import com.embrate.cloud.core.utils.EMapUtils;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceStatus;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceType;
import com.lernen.cloud.core.api.attendance.staff.v2.*;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffLite;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.DateUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @created_at 28/06/24 : 16:31
 **/
public class StaffAttendanceUtils {

    public static Map<Integer, Map<StaffAttendanceType, List<User>>> getDateStaffAttendanceTypeUserListMap(
            List<StaffAttendanceRegisterData> staffAttendanceRegisterDataList, Map<UUID, User> userMap,
            Map<UUID, Staff> staffMap) {
        Map<Integer, Map<StaffAttendanceType, List<User>>> dateStaffAttendanceTypeUserListMap = new HashMap<>();
        for(StaffAttendanceRegisterData staffAttendanceRegisterData : staffAttendanceRegisterDataList) {
            for(StaffAttendanceDaySummary staffAttendanceDaySummary : staffAttendanceRegisterData.getStaffAttendanceDaySummaryList()) {
                int attendanceDateStart = DateUtils.getDayStartWithDefaultZone(staffAttendanceDaySummary.getAttendanceDate());
                int currentDate = DateUtils.getDayStartWithDefaultZone(DateUtils.now());
                if(currentDate != attendanceDateStart) {
                    continue;
                }
                if(staffAttendanceRegisterData.getStaff() == null) {
                    continue;
                }
                UUID staffId = staffAttendanceRegisterData.getStaff().getStaffId();
                User user = userMap.get(staffId);
                if(user == null) {
                    continue;
                }
                StaffAttendanceType staffAttendanceType = staffAttendanceDaySummary.getStaffAttendanceDayMetadata().getLastAttendanceType();
                if(staffAttendanceType == null) {
                    continue;
                }
                if(!dateStaffAttendanceTypeUserListMap.containsKey(attendanceDateStart)) {
                    dateStaffAttendanceTypeUserListMap.put(attendanceDateStart, new HashMap<>());
                }
                if(!dateStaffAttendanceTypeUserListMap.get(attendanceDateStart).containsKey(staffAttendanceType)) {
                    dateStaffAttendanceTypeUserListMap.get(attendanceDateStart).put(staffAttendanceType, new ArrayList<>());
                }
                dateStaffAttendanceTypeUserListMap.get(attendanceDateStart).get(staffAttendanceType).add(user);
            }

            if(!staffMap.containsKey(staffAttendanceRegisterData.getStaff().getStaffId())) {
                staffMap.put(staffAttendanceRegisterData.getStaff().getStaffId(), staffAttendanceRegisterData.getStaff());
            }

        }
        return dateStaffAttendanceTypeUserListMap;
    }

    public static Map<Integer, Map<StaffAttendanceType, List<StaffLite>>> getDateStaffAttendanceTypeStaffListMap(
            List<StaffAttendanceRegisterData> staffAttendanceRegisterDataList, Map<UUID, Staff> staffLiteMap) {
        Map<Integer, Map<StaffAttendanceType, List<StaffLite>>> dateStaffAttendanceTypeStaffLiteListMap = new HashMap<>();
        for(StaffAttendanceRegisterData staffAttendanceRegisterData : staffAttendanceRegisterDataList) {
            for(StaffAttendanceDaySummary staffAttendanceDaySummary : staffAttendanceRegisterData.getStaffAttendanceDaySummaryList()) {
                int attendanceDateStart = DateUtils.getDayStartWithDefaultZone(staffAttendanceDaySummary.getAttendanceDate());
                int currentDate = DateUtils.getDayStartWithDefaultZone(DateUtils.now());
                if(currentDate != attendanceDateStart) {
                    continue;
                }
                if(staffAttendanceRegisterData.getStaff() == null) {
                    continue;
                }
                UUID staffId = staffAttendanceRegisterData.getStaff().getStaffId();
                StaffLite staffLite = Staff.getStaffLite(staffLiteMap.get(staffId));
                if(staffLite == null) {
                    continue;
                }
                StaffAttendanceType staffAttendanceType = staffAttendanceDaySummary.getStaffAttendanceDayMetadata().getLastAttendanceType();
                if(staffAttendanceType == null) {
                    continue;
                }
                if(!dateStaffAttendanceTypeStaffLiteListMap.containsKey(attendanceDateStart)) {
                    dateStaffAttendanceTypeStaffLiteListMap.put(attendanceDateStart, new HashMap<>());
                }
                if(!dateStaffAttendanceTypeStaffLiteListMap.get(attendanceDateStart).containsKey(staffAttendanceType)) {
                    dateStaffAttendanceTypeStaffLiteListMap.get(attendanceDateStart).put(staffAttendanceType, new ArrayList<>());
                }
                dateStaffAttendanceTypeStaffLiteListMap.get(attendanceDateStart).get(staffAttendanceType).add(staffLite);
            }
        }
        return dateStaffAttendanceTypeStaffLiteListMap;
    }

    public static Map<Integer, List<StaffAttendanceDateData>> getStaffAttendanceDateDataMap(int startDate, int endDate, Set<UUID> staffCategoryIdSet, Set<StaffStatus> staffStatusSet,
                 Map<UUID, Staff> staffMap, Map<UUID, StaffAttendanceRegisterData> staffAttendanceRegisterDataMap, Map<UUID, List<StaticHoliday>> staticHolidayMap) {

        Map<Integer, List<StaffAttendanceDateData>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(staffAttendanceRegisterDataMap) || CollectionUtils.isEmpty(staffMap)) {
            return result;
        }

        //TODO:sort staff map and date set
        List<Integer> dateList = DateUtils.getDateList(DateUtils.getDayStart(startDate, DateUtils.DEFAULT_TIMEZONE), DateUtils.getDayEnd(endDate, DateUtils.DEFAULT_TIMEZONE));
        Collections.sort(dateList);

        HashMap<UUID, Map<Integer, StaffAttendanceDateSummary>> staffAttendanceDateDataMap = new HashMap<>();

        for (final Map.Entry<UUID, Staff> staffEntry : staffMap.entrySet()) {
            Staff staff = staffEntry.getValue();
            StaffAttendanceRegisterData staffAttendanceRegisterData = staffAttendanceRegisterDataMap.get(staff.getStaffId());
            UUID staffId = staff.getStaffId();

            StaffStatus staffStatus = staff.getStaffStatus();
            UUID categoryId = staff.getStaffBasicInfo().getStaffCategoryId();
            /**
             * filtering staff on the basics of status
             */
            if (!CollectionUtils.isEmpty(staffStatusSet) && !staffStatusSet.contains(staffStatus)) {
                continue;
            }
            /**
             * filtering staff on the basics of category
             */
            if (!CollectionUtils.isEmpty(staffCategoryIdSet) && !staffCategoryIdSet.contains(categoryId)) {
                continue;
            }

            if(!staffAttendanceDateDataMap.containsKey(staffId)) {
                staffAttendanceDateDataMap.put(staffId, new HashMap<>());
            }

            if(CollectionUtils.isEmpty(staffAttendanceRegisterData.getStaffAttendanceDaySummaryList())) {
                for(Integer date : dateList) {

                FinalStaffAttendanceStatus finalStaffAttendanceStatus = StaffFinalAttendanceStatusComputer.getFinalStaffAttendanceStatus(
                        staffId, date, staticHolidayMap, null,
                        DateUtils.getDayStart(DateUtils.now(), DateUtils.DEFAULT_TIMEZONE), false);

                    staffAttendanceDateDataMap.get(staffId).put(date,
                            new StaffAttendanceDateSummary(date, null, finalStaffAttendanceStatus, null));
                }
                continue;
            }
            for (final StaffAttendanceDaySummary staffAttendanceDaySummary : staffAttendanceRegisterData.getStaffAttendanceDaySummaryList()) {
                int date = DateUtils.getDayStart(staffAttendanceDaySummary.getAttendanceDate(), DateUtils.DEFAULT_TIMEZONE);
                if(!dateList.contains(date)) {
                    continue;
                }

                if(!staffAttendanceDateDataMap.get(staffId).containsKey(date)) {

                    FinalStaffAttendanceStatus finalStaffAttendanceStatus = StaffFinalAttendanceStatusComputer.getFinalStaffAttendanceStatus(
                            staffId, date, staticHolidayMap, staffAttendanceDaySummary.getStaffAttendanceStatus(),
                            DateUtils.getDayStart(DateUtils.now(), DateUtils.DEFAULT_TIMEZONE),
                        /**
                         * for current day if in time is added that means staff is present for the day
                         * as status and duration is only calculated when out time is added.
                         */
                        staffAttendanceDaySummary.getStaffAttendanceDayMetadata() != null
                                && (!CollectionUtils.isEmpty(staffAttendanceDaySummary.getStaffAttendanceDayMetadata().getStaffTimeDurationList())));

                    staffAttendanceDateDataMap.get(staffId).put(date,
                            new StaffAttendanceDateSummary(date,
                                    staffAttendanceDaySummary.getTotalDurationInSec(), finalStaffAttendanceStatus,
                                    staffAttendanceDaySummary.getStaffAttendanceDayMetadata()));
                }
            }
        }

        return transformStaffAttendanceData(staffAttendanceDateDataMap, staffMap, dateList);
    }

    private static Map<Integer, List<StaffAttendanceDateData>> transformStaffAttendanceData(
            HashMap<UUID, Map<Integer, StaffAttendanceDateSummary>> staffAttendanceDateDataMap,
            Map<UUID, Staff> staffMap, List<Integer> dateList) {

        Map<Integer, List<StaffAttendanceDateData>> dateWiseStaffAttendanceDateDataMap = new HashMap<>();

        for(Integer attendanceDate : dateList) {

            for (Map.Entry<UUID, Staff> staffEntry : staffMap.entrySet()) {

                Staff staff = staffEntry.getValue();
                UUID staffId = staff.getStaffId();
                StaffLite staffLite = Staff.getStaffLite(staff);

                Map<Integer, StaffAttendanceDateSummary> staffAttendanceDateSummaryMap = staffAttendanceDateDataMap.get(staffId);

                StaffAttendanceDateData staffAttendanceDateData = new StaffAttendanceDateData(
                        staffLite, CollectionUtils.isEmpty(staffAttendanceDateSummaryMap) ? new ArrayList<>() :
                        new ArrayList<>(staffAttendanceDateSummaryMap.values()));

                if (!dateWiseStaffAttendanceDateDataMap.containsKey(attendanceDate)) {
                    dateWiseStaffAttendanceDateDataMap.put(attendanceDate, new ArrayList<>());
                }

                dateWiseStaffAttendanceDateDataMap.get(attendanceDate).add(staffAttendanceDateData);
            }
        }

        return sortStaffAttendanceDataMap(dateWiseStaffAttendanceDateDataMap);
    }

    private static Map<Integer, List<StaffAttendanceDateData>> sortStaffAttendanceDataMap(
            Map<Integer, List<StaffAttendanceDateData>> dateWiseStaffAttendanceDateDataMap) {

        Map<Integer, List<StaffAttendanceDateData>> sortedDateWiseMap = new TreeMap<>(dateWiseStaffAttendanceDateDataMap);

        for (Map.Entry<Integer, List<StaffAttendanceDateData>> entry : sortedDateWiseMap.entrySet()) {
            List<StaffAttendanceDateData> staffAttendanceDateDataList = entry.getValue();

            Collections.sort(staffAttendanceDateDataList);

            for (StaffAttendanceDateData staffAttendanceDateData : staffAttendanceDateDataList) {
                List<StaffAttendanceDateSummary> summaryList = staffAttendanceDateData.getStaffAttendanceDateSummaryList();

                Collections.sort(summaryList, new Comparator<StaffAttendanceDateSummary>() {
                    @Override
                    public int compare(StaffAttendanceDateSummary o1, StaffAttendanceDateSummary o2) {
                        return Integer.compare(o1.getAttendanceDate(), o2.getAttendanceDate());
                    }
                });
            }
        }

        return sortedDateWiseMap;
    }

    public static Map<Integer, Map<StaffAttendanceStatus, List<User>>> getDateStaffAttendanceStatusUserListMap(
            List<StaffAttendanceRegisterData> staffAttendanceRegisterDataList, Map<UUID, User> userMap,
            Map<UUID, Staff> staffMap) {
        Map<Integer, Map<StaffAttendanceStatus, List<User>>> dateStaffAttendanceStatusUserListMap = new HashMap<>();
        for(StaffAttendanceRegisterData staffAttendanceRegisterData : staffAttendanceRegisterDataList) {
            for(StaffAttendanceDaySummary staffAttendanceDaySummary : staffAttendanceRegisterData.getStaffAttendanceDaySummaryList()) {
                int attendanceDateStart = DateUtils.getDayStartWithDefaultZone(staffAttendanceDaySummary.getAttendanceDate());
                int currentDate = DateUtils.getDayStartWithDefaultZone(DateUtils.now());
                if(currentDate != attendanceDateStart) {
                    continue;
                }
                if(staffAttendanceRegisterData.getStaff() == null) {
                    continue;
                }
                UUID staffId = staffAttendanceRegisterData.getStaff().getStaffId();
                User user = userMap.get(staffId);
                if(user == null) {
                    continue;
                }

                StaffAttendanceStatus staffAttendanceStatus = staffAttendanceDaySummary.getStaffAttendanceStatus();
                if(staffAttendanceStatus == null) {
                    continue;
                }
                if(!dateStaffAttendanceStatusUserListMap.containsKey(attendanceDateStart)) {
                    dateStaffAttendanceStatusUserListMap.put(attendanceDateStart, new HashMap<>());
                }
                if(!dateStaffAttendanceStatusUserListMap.get(attendanceDateStart).containsKey(staffAttendanceStatus)) {
                    dateStaffAttendanceStatusUserListMap.get(attendanceDateStart).put(staffAttendanceStatus, new ArrayList<>());
                }
                dateStaffAttendanceStatusUserListMap.get(attendanceDateStart).get(staffAttendanceStatus).add(user);
            }

            if(!staffMap.containsKey(staffAttendanceRegisterData.getStaff().getStaffId())) {
                staffMap.put(staffAttendanceRegisterData.getStaff().getStaffId(), staffAttendanceRegisterData.getStaff());
            }

        }
        return dateStaffAttendanceStatusUserListMap;
    }

    public static Map<Integer, Map<StaffAttendanceStatus, List<StaffLite>>> getDateStaffAttendanceStatusStaffListMap(
            List<StaffAttendanceRegisterData> staffAttendanceRegisterDataList,
            Map<UUID, Staff> staffMap) {
        Map<Integer, Map<StaffAttendanceStatus, List<StaffLite>>> dateStaffAttendanceStatusStaffListMap = new HashMap<>();
        for(StaffAttendanceRegisterData staffAttendanceRegisterData : staffAttendanceRegisterDataList) {
            for(StaffAttendanceDaySummary staffAttendanceDaySummary : staffAttendanceRegisterData.getStaffAttendanceDaySummaryList()) {
                int attendanceDateStart = DateUtils.getDayStartWithDefaultZone(staffAttendanceDaySummary.getAttendanceDate());
                int currentDate = DateUtils.getDayStartWithDefaultZone(DateUtils.now());
                if(currentDate != attendanceDateStart) {
                    continue;
                }
                if(staffAttendanceRegisterData.getStaff() == null) {
                    continue;
                }
                UUID staffId = staffAttendanceRegisterData.getStaff().getStaffId();
                StaffLite staffLite = Staff.getStaffLite(staffMap.get(staffId));
                if(staffLite == null) {
                    continue;
                }

                StaffAttendanceStatus staffAttendanceStatus = staffAttendanceDaySummary.getStaffAttendanceStatus();
                if(staffAttendanceStatus == null) {
                    continue;
                }
                if(!dateStaffAttendanceStatusStaffListMap.containsKey(attendanceDateStart)) {
                    dateStaffAttendanceStatusStaffListMap.put(attendanceDateStart, new HashMap<>());
                }
                if(!dateStaffAttendanceStatusStaffListMap.get(attendanceDateStart).containsKey(staffAttendanceStatus)) {
                    dateStaffAttendanceStatusStaffListMap.get(attendanceDateStart).put(staffAttendanceStatus, new ArrayList<>());
                }
                dateStaffAttendanceStatusStaffListMap.get(attendanceDateStart).get(staffAttendanceStatus).add(staffLite);
            }

            if(!staffMap.containsKey(staffAttendanceRegisterData.getStaff().getStaffId())) {
                staffMap.put(staffAttendanceRegisterData.getStaff().getStaffId(), staffAttendanceRegisterData.getStaff());
            }

        }
        return dateStaffAttendanceStatusStaffListMap;
    }
}
