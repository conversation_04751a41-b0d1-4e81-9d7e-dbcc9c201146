package com.lernen.cloud.core.utils.attendance.staff;

import com.embrate.cloud.core.api.calendar.holiday.Holiday;
import com.embrate.cloud.core.api.calendar.holiday.StaticHoliday;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceStatus;
import com.lernen.cloud.core.api.attendance.staff.v2.FinalStaffAttendanceStatus;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public class StaffFinalAttendanceStatusComputer {

    public static FinalStaffAttendanceStatus getFinalStaffAttendanceStatus(UUID staffId, Integer date, Map<UUID, List<StaticHoliday>> staticHolidayMap,
                                                                           StaffAttendanceStatus staffAttendanceStatus, int todayDate, boolean isInTimeAdded) {

        if(isHoliday(staffId, date, staticHolidayMap)) {
            return FinalStaffAttendanceStatus.HOLIDAY;
        }
        if(staffAttendanceStatus != null) {
            switch (staffAttendanceStatus) {
                case PRESENT:
                    return FinalStaffAttendanceStatus.PRESENT;
                case LEAVE:
                    return FinalStaffAttendanceStatus.LEAVE;
                case HALF_DAY:
                    return FinalStaffAttendanceStatus.HALF_DAY;
            }
        }
        /**
         * If data is getting fetched for today, we will mark it as unmarked, or if data is getting fetched for past days, we will mark it as leave.
         */
        return isInTimeAdded ? FinalStaffAttendanceStatus.PRESENT :
                date >= todayDate ? FinalStaffAttendanceStatus.UNMARKED : FinalStaffAttendanceStatus.LEAVE;
    }

    public static boolean isHoliday(UUID staffId, Integer date, Map<UUID, List<StaticHoliday>> staticHolidayMap) {
        if(CollectionUtils.isEmpty(staticHolidayMap) || !staticHolidayMap.containsKey(staffId)) {
            return false;
        }
        List<StaticHoliday> holidays = staticHolidayMap.get(staffId);
        if(CollectionUtils.isEmpty(holidays)) {
            return false;
        }
        for(Holiday holiday : holidays) {
            int start = holiday.getStart();
            int end  = holiday.getEnd();
            if(date >= start && date < end) {
                return true;
            }
        }
        return false;
    }
}
