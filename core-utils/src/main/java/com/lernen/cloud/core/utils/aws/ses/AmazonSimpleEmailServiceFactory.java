package com.lernen.cloud.core.utils.aws.ses;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailService;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClientBuilder;
import com.lernen.cloud.core.utils.credentials.IAWSCredentialProviderChain;

/**
 * 
 * <AUTHOR>
 *
 */
public class AmazonSimpleEmailServiceFactory {

	private final AmazonSimpleEmailService amazonSimpleEmailService;

	public AmazonSimpleEmailServiceFactory(IAWSCredentialProviderChain awsCredentialProviderChain) {
		this.amazonSimpleEmailService = AmazonSimpleEmailServiceClientBuilder.standard().withRegion(awsCredentialProviderChain.getRegion())
				.withCredentials(awsCredentialProviderChain.getCredentials()).build();
	}

	public AmazonSimpleEmailService getAmazonSimpleEmailService() {
		return amazonSimpleEmailService;
	}

}
