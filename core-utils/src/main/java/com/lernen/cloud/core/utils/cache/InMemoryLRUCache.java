package com.lernen.cloud.core.utils.cache;

import com.embrate.cloud.core.api.cache.ICacheKey;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class InMemoryLRUCache<K extends ICacheKey, V> implements ICache<K, V> {

	private final LoadingCache<K, V> cache;


	public InMemoryLRUCache(int capacity, ICacheLoader<K, V> cacheLoader) {
		CacheLoader<K, V> loader = new CacheLoader<K, V>() {
			@Override
			public V load(K key) throws Exception {
				return cacheLoader.load(key);
			}
		};
		this.cache = CacheBuilder.newBuilder()
				.maximumSize(capacity)
				.expireAfterAccess(24, TimeUnit.HOURS)
				.build(loader);
	}

	@Override
	public V get(K key) {
		try {
			return cache.get(key);
		} catch (Exception e) {
			return null;
		}

	}

	@Override
	public void put(K key, V val) {
		cache.put(key, val);
	}

	@Override
	public void delete(K key) {
		cache.invalidate(key);
	}
}