package com.lernen.cloud.core.utils.credentials;

import com.amazonaws.auth.AWSCredentialsProviderChain;
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;

/**
 * <AUTHOR>
 */

public class AWSDefaultCredentialProviderChain implements IAWSCredentialProviderChain {

    private final DefaultAWSCredentialsProviderChain defaultAWSCredentialsProviderChain;
    private final String region;

    public AWSDefaultCredentialProviderChain(DefaultAWSCredentialsProviderChain defaultAWSCredentialsProviderChain, String region) {
        this.defaultAWSCredentialsProviderChain = defaultAWSCredentialsProviderChain;
        this.region = region;
    }

    @Override
    public DefaultAWSCredentialsProviderChain getCredentials() {
        return defaultAWSCredentialsProviderChain;
    }

    @Override
    public String getRegion() {
        return region;
    }
}
