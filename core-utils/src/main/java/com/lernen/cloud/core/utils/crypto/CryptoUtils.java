package com.lernen.cloud.core.utils.crypto;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class CryptoUtils {

	private static final Logger logger = LogManager.getLogger(CryptoUtils.class);
	private static final String KEY = "Xp2s5v8y/B?E(H+M";
	private static final String IV = "n2r5u7x!A%D*G-Ka";

	private static final String ALGO = "AES";
	private static final String CHAR_SET = "UTF-8";

	private static final String CIPHER_KEY = "AES/CBC/PKCS5PADDING";

	public static String encrypt(String text) {
		if (StringUtils.isBlank(text)) {
			return null;
		}
		try {
			IvParameterSpec iv = new IvParameterSpec(IV.getBytes(CHAR_SET));
			SecretKeySpec skeySpec = new SecretKeySpec(KEY.getBytes(CHAR_SET), ALGO);

			Cipher cipher = Cipher.getInstance(CIPHER_KEY);
			cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);

			byte[] encrypted = cipher.doFinal(text.getBytes());

			return Base64.encodeBase64String(encrypted);
		} catch (Exception e) {
			logger.error("Exception during encrypting the text", e);
		}

		return null;
	}

	public static String decrypt(String encryptedText) {
		if (StringUtils.isBlank(encryptedText)) {
			return null;
		}
		try {
			IvParameterSpec iv = new IvParameterSpec(IV.getBytes(CHAR_SET));
			SecretKeySpec skeySpec = new SecretKeySpec(KEY.getBytes(CHAR_SET), ALGO);

			Cipher cipher = Cipher.getInstance(CIPHER_KEY);
			cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);

			byte[] original = cipher.doFinal(Base64.decodeBase64(encryptedText));

			return new String(original);
		} catch (Exception e) {
			logger.error("Exception during decrypting the text", e);
		}

		return null;
	}

	public static void main(String[] args) {
		System.out.println(decrypt(encrypt("Hello World")));
	}
}
