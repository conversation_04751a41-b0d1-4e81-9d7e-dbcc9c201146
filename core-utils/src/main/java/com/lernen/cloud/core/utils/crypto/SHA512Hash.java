package com.lernen.cloud.core.utils.crypto;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class SHA512Hash {

	private static final String SALT = "F874ED12200F360B";
	
	public static String createHash(String text, String salt) {
		try {
			MessageDigest md = MessageDigest.getInstance("SHA-512");
			md.update(salt.getBytes(StandardCharsets.UTF_8));
			byte[] bytes = md.digest(text.getBytes(StandardCharsets.UTF_8));
			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < bytes.length; i++) {
				sb.append(Integer.toString((bytes[i] & 0xff) + 0x100, 16).substring(1));
			}
			return sb.toString();
		} catch (NoSuchAlgorithmException e) {

		}
		return null;
	}
	
	public static String createHash(String text) {
		try {
			MessageDigest md = MessageDigest.getInstance("SHA-512");
			md.update(SALT.getBytes(StandardCharsets.UTF_8));
			byte[] bytes = md.digest(text.getBytes(StandardCharsets.UTF_8));
			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < bytes.length; i++) {
				sb.append(Integer.toString((bytes[i] & 0xff) + 0x100, 16).substring(1));
			}
			return sb.toString();
		} catch (NoSuchAlgorithmException e) {

		}
		return null;
	}
	
	 public static void main(String[] args) {
		   
	        System.out.println(SHA512Hash.createHash("Hello World"));
	    }
}
