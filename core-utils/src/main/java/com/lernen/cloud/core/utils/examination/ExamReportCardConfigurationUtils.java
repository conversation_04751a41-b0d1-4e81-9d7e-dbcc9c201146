package com.lernen.cloud.core.utils.examination;

import com.itextpdf.styledxmlparser.jsoup.internal.StringUtil;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.ExamReportCardMetadata;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.utils.UUIDUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @created_at 12/10/23 : 18:41
 **/
public class ExamReportCardConfigurationUtils {

    /**
     * @param instituteId
     * @param academicSessionId
     * @param standardId
     * @param examIdSet
     * @param reportCardType
     * @param containsScholasticSubjects
     * @param containsCoScholasticSubjects
     * @param examScholasticContainMap
     * @param examCoScholasticContainMap
     * @param hideExamIdSet
     * @param hideScholasticTotal
     * @param hideCoScholasticTotal
     * @return
     */
    //TODO:improve the calculation, currently we are calculation course gain, course avg, course high, percentage, rank
    public static ExamReportCardConfiguration getReportExamReportCardConfiguration(int instituteId, int academicSessionId,
                    UUID standardId, Set<UUID> examIdSet, String reportCardType, boolean containsScholasticSubjects,
                    boolean containsCoScholasticSubjects, Map<UUID, Boolean> examScholasticContainMap,
                    Map<UUID, Boolean> examCoScholasticContainMap, List<UUID> hideExamIdSet, boolean hideScholasticTotal, boolean hideCoScholasticTotal,
                    String examResultCalculatorId, Integer attendanceStartDate, Integer attendanceEndDate,
                    boolean isCoScholasticGrade, boolean isScholasticGrade, Set<UUID> additionalCoursesSet) {

        ExamReportStructureMetaData examReportStructureMetaData = new ExamReportStructureMetaData();
        examReportStructureMetaData.setShowScholasticCourseClassMetrics(true);
        examReportStructureMetaData.setShowCoScholasticCourseClassMetrics(true);
        examReportStructureMetaData.setShowScholasticCourseGainMetrics(true);
        examReportStructureMetaData.setShowCoScholasticCourseGainMetrics(true);
        examReportStructureMetaData.setAttendanceStartDate(attendanceStartDate);
        examReportStructureMetaData.setAttendanceStartDate(attendanceEndDate);

        ExamReportCourseStructure examReportCourseStructure = new ExamReportCourseStructure();
        if(!CollectionUtils.isEmpty(additionalCoursesSet)) {
            examReportCourseStructure.setAdditionalCourses(additionalCoursesSet);
        }
        Map<CourseType, ExamReportCourseStructure> examReportCourseStructureMap = new HashMap<>();
        examReportCourseStructureMap.put(CourseType.SCHOLASTIC, examReportCourseStructure);
        examReportCourseStructureMap.put(CourseType.COSCHOLASTIC, new ExamReportCourseStructure());

        Map<CourseType, List<ExamReportHeaderStructureColumn>> examReportHeaderStructureColumns =
                getExamReportHeaderStructureColumnListMap(examIdSet, containsScholasticSubjects, containsCoScholasticSubjects,
                        examScholasticContainMap, examCoScholasticContainMap, hideExamIdSet, hideScholasticTotal,
                        hideCoScholasticTotal, isCoScholasticGrade, isScholasticGrade);

        ExamReportResultConfigs examReportResultConfigs = StringUtils.isBlank(examResultCalculatorId)
                ? new ExamReportResultConfigs() : getExamReportResultConfigs(examResultCalculatorId, examIdSet);

        ExamReportStructure examReportStructure = new ExamReportStructure();
        examReportStructure.setExamReportStructureMetaData(examReportStructureMetaData);
        examReportStructure.setExamReportCourseStructure(examReportCourseStructureMap);
        examReportStructure.setExamReportHeaderStructureColumns(examReportHeaderStructureColumns);
        examReportStructure.setExamReportResultConfigs(examReportResultConfigs);

        ExamReportCardMetadata examReportCardMetadata = new ExamReportCardMetadata(instituteId, academicSessionId, UUIDUtils.generateUUID(),
                standardId, reportCardType, reportCardType);

        ExamReportCardConfiguration examReportCardConfiguration = new ExamReportCardConfiguration();
        examReportCardConfiguration.setExamReportCardMetadata(examReportCardMetadata);
        examReportCardConfiguration.setExamReportStructure(examReportStructure);


        return examReportCardConfiguration;
    }

    private static ExamReportResultConfigs getExamReportResultConfigs(String examResultCalculatorId, Set<UUID> examIdSet) {


        List<ExamResultComputationEntry> resultComputationEntries = new ArrayList<>();
        for(UUID examId : examIdSet) {
            ExamResultComputationEntry examResultComputationEntry = new ExamResultComputationEntry();
            examResultComputationEntry.setExamId(examId);
            examResultComputationEntry.setTotalDimension(true);
            resultComputationEntries.add(examResultComputationEntry);
        }

        ExamResultComputationGroup examResultComputationGroup = new ExamResultComputationGroup();
        examResultComputationGroup.setGroupId("g1");
        examResultComputationGroup.setResultComputationEntries(resultComputationEntries);

        List<ExamResultComputationGroup> resultComputationGroupList = new ArrayList<>();
        resultComputationGroupList.add(examResultComputationGroup);

        ExamReportResultConfigs examReportResultConfigs = new ExamReportResultConfigs();
        examReportResultConfigs.setResultCalculatorId(examResultCalculatorId);
        examReportResultConfigs.setResultComputationGroupList(resultComputationGroupList);

        return examReportResultConfigs;
    }

    private static Map<CourseType, List<ExamReportHeaderStructureColumn>> getExamReportHeaderStructureColumnListMap(
            Set<UUID> examIdSet, boolean containsScholasticSubjects, boolean containsCoScholasticSubjects,
            Map<UUID, Boolean> examScholasticContainMap, Map<UUID, Boolean> examCoScholasticContainMap, List<UUID> hideExamIdSet,
            boolean hideScholasticTotal, boolean hideCoScholasticTotal, boolean isCoScholasticGrade, boolean isScholasticGrade) {

        HashMap<CourseType, List<ExamReportHeaderStructureColumn>> courseTypeListHashMap = new HashMap<>();
        List<ExamReportHeaderStructureColumn> scholasticExamReportHeaderStructureColumnList = new ArrayList<>();
        List<ExamReportHeaderStructureColumn> coScholasticExamReportHeaderStructureColumnList = new ArrayList<>();
        Set<String> scholasticComputationColumnsSet = new HashSet<>();
        Set<String> coScholasticComputationColumnsSet = new HashSet<>();

        for(UUID examId : examIdSet) {
            boolean hideScholasticExam = false;
            boolean hideCoScholasticExam = false;
            if(!CollectionUtils.isEmpty(hideExamIdSet)) {
                if(hideExamIdSet.contains(examId)) {
                    hideScholasticExam = true;
                    hideCoScholasticExam = true;
                }
            }

            if(!CollectionUtils.isEmpty(examScholasticContainMap.entrySet()) && examScholasticContainMap.get(examId) != null
                    && examScholasticContainMap.get(examId)) {

                String scholasticId = examId.toString() + "-scholastic";
                scholasticComputationColumnsSet.add(scholasticId);

                ExamReportHeaderStructureColumn examReportHeaderStructureColumn = getExamReportHeaderStructureColumn(scholasticId,
                        ExamReportGridColumnType.EXAM, examId, null, hideScholasticExam,
                        null, null);

                if(isScholasticGrade) {
                    examReportHeaderStructureColumn = getExamReportHeaderStructureColumn(scholasticId,
                            ExamReportGridColumnType.EXAM_GRADE, examId, null, hideScholasticExam,
                            null, null);
                }

                scholasticExamReportHeaderStructureColumnList.add(examReportHeaderStructureColumn);
            }

            if(!CollectionUtils.isEmpty(examCoScholasticContainMap.entrySet()) && examCoScholasticContainMap.get(examId) != null
                    && examCoScholasticContainMap.get(examId)) {

                String coScholasticId = examId.toString() + "-coscholastic";
                coScholasticComputationColumnsSet.add(coScholasticId);

                ExamReportHeaderStructureColumn examReportHeaderStructureColumn = getExamReportHeaderStructureColumn(coScholasticId,
                        ExamReportGridColumnType.EXAM, examId, null, hideCoScholasticExam,
                        null, null);

                if(isCoScholasticGrade) {
                    examReportHeaderStructureColumn = getExamReportHeaderStructureColumn(coScholasticId,
                            ExamReportGridColumnType.EXAM_GRADE, examId, null, hideCoScholasticExam,
                            null, null);
                }
                coScholasticExamReportHeaderStructureColumnList.add(examReportHeaderStructureColumn);
            }

        }

        if(containsScholasticSubjects && !CollectionUtils.isEmpty(scholasticExamReportHeaderStructureColumnList)
                && !CollectionUtils.isEmpty(scholasticComputationColumnsSet)) {
            String scholasticSumId = "scholastic-sum-id";
            ExamReportHeaderStructureColumn examReportHeaderStructureColumn = getExamReportHeaderStructureColumn(scholasticSumId,
                    ExamReportGridColumnType.SUM, null, scholasticComputationColumnsSet, hideScholasticTotal,
                    null, null);
            scholasticExamReportHeaderStructureColumnList.add(examReportHeaderStructureColumn);

            scholasticComputationColumnsSet = new HashSet<>();
            scholasticComputationColumnsSet.add(scholasticSumId);

            String scholasticPercentageId = "scholastic-percentage-id";
            examReportHeaderStructureColumn = getExamReportHeaderStructureColumn(scholasticPercentageId,
                    ExamReportGridColumnType.PERCENT, null, scholasticComputationColumnsSet, hideScholasticTotal,
                    null, null);
            scholasticExamReportHeaderStructureColumnList.add(examReportHeaderStructureColumn);

            String scholasticRankId = "scholastic-rank-id";
            examReportHeaderStructureColumn = getExamReportHeaderStructureColumn(scholasticRankId,
                    ExamReportGridColumnType.COURSE_RANK, null, null, hideScholasticTotal,
                    scholasticSumId, null);
            scholasticExamReportHeaderStructureColumnList.add(examReportHeaderStructureColumn);

            String scholasticCourseAvgId = "scholastic-course-avg-id";
            examReportHeaderStructureColumn = getExamReportHeaderStructureColumn(scholasticCourseAvgId,
                    ExamReportGridColumnType.CLASS_COURSE_AVG, null, null, hideScholasticTotal,
                    scholasticSumId, null);
            scholasticExamReportHeaderStructureColumnList.add(examReportHeaderStructureColumn);

            String scholasticCourseHighId = "scholastic-course-high-id";
            examReportHeaderStructureColumn = getExamReportHeaderStructureColumn(scholasticCourseHighId,
                    ExamReportGridColumnType.CLASS_COURSE_HIGH, null, null, hideScholasticTotal,
                    scholasticSumId, null);
            scholasticExamReportHeaderStructureColumnList.add(examReportHeaderStructureColumn);
        }

        if(containsCoScholasticSubjects && !CollectionUtils.isEmpty(coScholasticExamReportHeaderStructureColumnList)
                && !CollectionUtils.isEmpty(coScholasticComputationColumnsSet)) {

            String coScholasticSumId = "coscholastic-sum-id";
            ExamReportHeaderStructureColumn examReportHeaderStructureColumn = getExamReportHeaderStructureColumn(coScholasticSumId,
                    ExamReportGridColumnType.SUM, null, coScholasticComputationColumnsSet, hideCoScholasticTotal,
                    null, null);
            coScholasticExamReportHeaderStructureColumnList.add(examReportHeaderStructureColumn);

            coScholasticComputationColumnsSet = new HashSet<>();
            coScholasticComputationColumnsSet.add(coScholasticSumId);

            String coScholasticPercentageId = "coscholastic-percentage-id";
            examReportHeaderStructureColumn = getExamReportHeaderStructureColumn(coScholasticPercentageId,
                    ExamReportGridColumnType.PERCENT, null, coScholasticComputationColumnsSet, hideCoScholasticTotal,
                    null, null);
            coScholasticExamReportHeaderStructureColumnList.add(examReportHeaderStructureColumn);

            String coScholasticRankId = "coscholastic-rank-id";
            examReportHeaderStructureColumn = getExamReportHeaderStructureColumn(coScholasticRankId,
                    ExamReportGridColumnType.COURSE_RANK, null, null, hideCoScholasticTotal,
                    coScholasticSumId, null);
            coScholasticExamReportHeaderStructureColumnList.add(examReportHeaderStructureColumn);

            String coScholasticCourseAvgId = "coscholastic-course-avg-id";
            examReportHeaderStructureColumn = getExamReportHeaderStructureColumn(coScholasticCourseAvgId,
                    ExamReportGridColumnType.CLASS_COURSE_AVG, null, null, hideCoScholasticTotal,
                    coScholasticSumId, null);
            coScholasticExamReportHeaderStructureColumnList.add(examReportHeaderStructureColumn);

            String coScholasticCourseHighId = "coscholastic-course-high-id";
            examReportHeaderStructureColumn = getExamReportHeaderStructureColumn(coScholasticCourseHighId,
                    ExamReportGridColumnType.CLASS_COURSE_HIGH, null, null, hideCoScholasticTotal,
                    coScholasticSumId, null);
            coScholasticExamReportHeaderStructureColumnList.add(examReportHeaderStructureColumn);

        }

        courseTypeListHashMap.put(CourseType.SCHOLASTIC, scholasticExamReportHeaderStructureColumnList);
        courseTypeListHashMap.put(CourseType.COSCHOLASTIC, coScholasticExamReportHeaderStructureColumnList);

        return courseTypeListHashMap;
    }

    private static ExamReportHeaderStructureColumn getExamReportHeaderStructureColumn(String id, ExamReportGridColumnType examReportGridColumnType,
                UUID examId, Set<String> scholasticComputationColumnsSet, boolean hide,
                String courseWiseClassMetricsComputeColumn, ExamReportCourseGainColumns courseWiseGainMetricsComputeColumns) {

        return new ExamReportHeaderStructureColumn(id, null, null, null, false,
                examReportGridColumnType, false, examId, null,
                null, false, false, scholasticComputationColumnsSet,
                courseWiseClassMetricsComputeColumn, courseWiseGainMetricsComputeColumns, null, hide);
    }
}
