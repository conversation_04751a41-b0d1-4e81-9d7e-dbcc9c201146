package com.lernen.cloud.core.utils.exceptions;

/**
 * 
 * <AUTHOR>
 *
 */
public class ServiceResponse<T> {

	private String requestId;

	private T payload;

	private ServiceErrorResponse error;

	public ServiceResponse(ServiceErrorResponse error) {
		this.error = error;
	}

	public ServiceResponse(T payload) {
		this.payload = payload;
	}

	public ServiceResponse() {

	}

	public String getRequestId() {
		return requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	public T getPayload() {
		return payload;
	}

	public void setPayload(T payload) {
		this.payload = payload;
	}

	public ServiceErrorResponse getError() {
		return error;
	}

	public void setError(ServiceErrorResponse error) {
		this.error = error;
	}

	@Override
	public String toString() {
		return "ServiceResponse [requestId=" + requestId + ", payload=" + payload + ", error=" + error + "]";
	}

}