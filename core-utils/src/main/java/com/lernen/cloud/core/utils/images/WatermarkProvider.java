
package com.lernen.cloud.core.utils.images;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class WatermarkProvider {

	private static final Map<Integer, String> WATER_MARK_FILE_NAMES = new HashMap<>();
	public static final WatermarkProvider INSTANCE = new WatermarkProvider();

	static {
		//Embrate school
		WATER_MARK_FILE_NAMES.put(101, "702_bg_logo.png");
		WATER_MARK_FILE_NAMES.put(702, "702_bg_logo.png");
		//Sesomu school
		WATER_MARK_FILE_NAMES.put(10001, "10001_bg_logo.png");
		//Vidhylasthali
		WATER_MARK_FILE_NAMES.put(10005, "10005_bg_logo.png");
		WATER_MARK_FILE_NAMES.put(10065, "10065_bg_logo.png");
		//Sunshine
		WATER_MARK_FILE_NAMES.put(10006, "10006_bg_logo.png");
		//Srs inter college
//		WATER_MARK_FILE_NAMES.put(10160, "10160_bg_logo.png");
		//Balaji convent
		WATER_MARK_FILE_NAMES.put(10010, "10010_bg_logo.png");
		//70-71 mordern rajasthan branches
//		WATER_MARK_FILE_NAMES.put(10070, "src/main/res2/10070_71_bg_logo.png");
//		WATER_MARK_FILE_NAMES.put(10071, "src/main/res2/10070_71_bg_logo.png");
		//Sumit Shikshan Sansthan
		WATER_MARK_FILE_NAMES.put(10075, "10075_bg_logo.png");
		//Sir Chhoturam
		WATER_MARK_FILE_NAMES.put(10085, "src/main/res2/10085_bg_logo.png");
		//Mother's Pride
		WATER_MARK_FILE_NAMES.put(10105, "10105_bg_logo.png");
		//Swami vivek aanand
		WATER_MARK_FILE_NAMES.put(10110, "10110_bg_logo.png");
		//Holy blend
		WATER_MARK_FILE_NAMES.put(10115, "10115_bg_logo.png");
		//CH. RAGHUBIR SINGH MEMORIAL CONVENT SCHOOL
		WATER_MARK_FILE_NAMES.put(10130, "10130_bg_logo.png");
		//Lala central
		WATER_MARK_FILE_NAMES.put(10135, "10135_bg_logo.png");
		//10145 & 10146 brahamved school
		WATER_MARK_FILE_NAMES.put(10145, "10145_10146_bg_logo.png");
		//10145 & 10146 brahamved school
		WATER_MARK_FILE_NAMES.put(10146, "10145_10146_bg_logo.png");
		//10170 & 10171 (NAVJYOTI CONVENT SR. SEC. SCHOOL)
		WATER_MARK_FILE_NAMES.put(10170, "src/main/res2/10170_10171_bg_logo.png");
		//10170 & 10171 (NAVJYOTI CONVENT SR. SEC. SCHOOL)
		WATER_MARK_FILE_NAMES.put(10171, "src/main/res2/10170_10171_bg_logo.png");
		//10180 (NOSEGAY CONVENT SCHOOL)
		WATER_MARK_FILE_NAMES.put(10180, "10180_bg_logo.png");
		//10185 (M.A.SENIOR SECONDARY SCHOOL)
		WATER_MARK_FILE_NAMES.put(10185, "10185_bg_logo.png");
		//10190 (SANSKAR INTERNATIONAL SCHOOL)
		WATER_MARK_FILE_NAMES.put(10190, "10190_bg_logo.png");
		//10200 (SHRI RAM INTER COLLEGE)
		WATER_MARK_FILE_NAMES.put(10200, "10200_bg_logo.png");
		WATER_MARK_FILE_NAMES.put(10220, "10220_bg_logo.png");
		WATER_MARK_FILE_NAMES.put(10230, "10230_bg_logo.png");
		WATER_MARK_FILE_NAMES.put(10231, "10231_bg_logo.png");
		WATER_MARK_FILE_NAMES.put(10295, "10295_bg_logo.png");
		WATER_MARK_FILE_NAMES.put(10300, "10300_bg_logo.png");
		WATER_MARK_FILE_NAMES.put(10345, "10345_bg_logo.png");
		WATER_MARK_FILE_NAMES.put(10360, "10360_bg_logo.png");
	}

	private WatermarkProvider() {

	}

	public byte[] getWatermark(int instituteId) throws IOException {
		String fileName = "empty_image.png";
		if (WATER_MARK_FILE_NAMES.containsKey(instituteId)) {
			fileName = WATER_MARK_FILE_NAMES.get(instituteId);
		}

		BufferedImage bImage = ImageIO.read(getClass().getResource("/" + fileName));

		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		ImageIO.write(bImage, fileName.split("\\.")[1], bos);
		return bos.toByteArray();
	}
}
