package com.embrate.cloud.dao.tier.calendar.holiday;

import com.embrate.cloud.core.api.calendar.holiday.Holiday;
import com.embrate.cloud.core.api.calendar.holiday.HolidayPayload;
import com.embrate.cloud.core.api.calendar.holiday.template.HolidayTemplate;
import com.embrate.cloud.core.api.calendar.holiday.template.HolidayTemplateDetails;
import com.embrate.cloud.core.api.calendar.holiday.template.HolidayTemplatePayload;
import com.embrate.cloud.core.api.calendar.holiday.template.assignment.UserHolidayTemplateData;
import com.embrate.cloud.core.api.calendar.holiday.template.assignment.UserHolidayTemplateMapping;
import com.embrate.cloud.dao.tier.calendar.holiday.mappers.HolidayRowMapper;
import com.embrate.cloud.dao.tier.calendar.holiday.mappers.HolidayTemplateAssignmentRowMapper;
import com.embrate.cloud.dao.tier.calendar.holiday.mappers.HolidayTemplateDetailsRowMapper;
import com.embrate.cloud.dao.tier.calendar.holiday.mappers.HolidayTemplateRowMapper;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.configurations.Entity;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class HolidayCalendarDao {

    private static final Logger logger = LogManager.getLogger(HolidayCalendarDao.class);
    private static final Gson GSON = SharedConstants.GSON;
    private static final HolidayRowMapper HOLIDAY_ROW_MAPPER = new HolidayRowMapper();
    private static final HolidayTemplateRowMapper HOLIDAY_TEMPLATE_ROW_MAPPER = new HolidayTemplateRowMapper();

    private static final HolidayTemplateDetailsRowMapper HOLIDAY_TEMPLATE_DETAILS_ROW_MAPPER = new HolidayTemplateDetailsRowMapper();

    private static final HolidayTemplateAssignmentRowMapper HOLIDAY_TEMPLATE_ASSIGNMENT_ROW_MAPPER = new HolidayTemplateAssignmentRowMapper();

    private static final String INSERT_HOLIDAY = " insert into holiday "
            + " (entity_name, entity_id, type, start_time, end_time, name, description, metadata) "
            + " values(?, ?, ?, ?, ?, ?, ?, ?) ";
    private static final String GET_HOLIDAYS = " select * from holiday " +
            " where entity_id = ? and entity_name = ? and ((start_time >= ? and start_time < ?)  " +
            " or  (end_time > ? and end_time <= ?) or (start_time <= ? and end_time > ?)) order by start_time";
    private static final String UPDATE_HOLIDAY = " update holiday set name = ?, description = ?, start_time = ?, end_time = ?, metadata = ? " +
            " where entity_id = ? and entity_name = ? and holiday_id = ?";
    private static final String DELETE_HOLIDAY = " delete from holiday where entity_id = ? and entity_name = ? and holiday_id = ?";

    // Holiday template //
    private static final String INSERT_HOLIDAY_TEMPLATE = " insert into holiday_template "
            + " (institute_id, academic_session_id, template_id, name, type, standard_ids, tags, description, metadata) "
            + " values(?, ?, ?, ?, ?, ?, ?, ?, ?) ";

    private static final String UPDATE_HOLIDAY_TEMPLATE = "update holiday_template "
            + " set name = ?, type = ?, standard_ids = ?, tags = ?, description = ?, metadata = ? where institute_id = ? "
            + " and template_id = ?";

    private static final String INSERT_HOLIDAY_TEMPLATE_MAPPING = "insert into holiday_template_mapping " +
            "(institute_id, template_id, holiday_id) values (?, ?, ?)";

    private static final String DELETE_HOLIDAY_TEMPLATE_MAPPING = "delete from holiday_template_mapping where institute_id = ? " +
            "and template_id = ?";
    private static final String DELETE_HOLIDAY_TEMPLATE = " delete from holiday_template where institute_id = ? and template_id = ?";

    private static final String GET_HOLIDAY_TEMPLATES = " select * from holiday_template where institute_id = ? and academic_session_id = ? order by name";

    private static final String GET_HOLIDAY_TEMPLATE_DETAILS = " select holiday_template.*, holiday.* from holiday_template " +
            "left join holiday_template_mapping on holiday_template.template_id = holiday_template_mapping.template_id left join " +
            "holiday on holiday_template_mapping.holiday_id = holiday.holiday_id " +
            "where holiday_template.institute_id = ? and holiday_template.template_id = ? order by holiday.start_time";

    private static final String GET_ALL_HOLIDAY_TEMPLATE_DETAILS = " select holiday_template.*, holiday.* from holiday_template " +
            "left join holiday_template_mapping on holiday_template.template_id = holiday_template_mapping.template_id left join " +
            "holiday on holiday_template_mapping.holiday_id = holiday.holiday_id " +
            "where holiday_template.institute_id = ? and holiday_template.template_id in %s";

//    // Holiday assignment //


    private static final String GET_HOLIDAY_TEMPLATE_ASSIGNMENT_FOR_USER_TYPE = "select holiday_template.*, user_holiday_template_assignment.* from " +
            "user_holiday_template_assignment join holiday_template on " +
            "user_holiday_template_assignment.template_id = holiday_template.template_id where " +
            "user_holiday_template_assignment.institute_id = ? and user_holiday_template_assignment.academic_session_id = ? " +
            "and user_holiday_template_assignment.user_type = ?";


    private static final String GET_USER_HOLIDAY_TEMPLATE_ASSIGNMENT = "select holiday_template.*, user_holiday_template_assignment.* from " +
            "user_holiday_template_assignment join holiday_template on " +
            "user_holiday_template_assignment.template_id = holiday_template.template_id where " +
            "user_holiday_template_assignment.institute_id = ? and user_holiday_template_assignment.academic_session_id = ? " +
            "and user_holiday_template_assignment.user_id = ?";


    private static final String INSERT_USER_HOLIDAY_TEMPLATE_ASSIGNMENT = "insert into user_holiday_template_assignment " +
            "(institute_id, user_id, user_type, academic_session_id, template_id, start_time, end_time) values (?, ?, ?, ?, ?, ?, ?)";

    private static final String DELETE_USER_HOLIDAY_TEMPLATE_ASSIGNMENT = " delete from user_holiday_template_assignment where institute_id = ?" +
            " and academic_session_id = ? and user_type = ? and user_id = ?";
//
//    private static final String INSERT_HOLIDAY_ASSIGNMENT = "insert into holiday_assignment(institute_id, entity_name, entity_id, holiday_id) values(?, ?, ?, ?)";
//
//    /**
//     * No duration filter as templates are bounded by session.
//     */
//    private static final String GET_HOLIDAY_ASSIGNMENTS = " select holiday.* from holiday_assignment join holiday on " +
//            "holiday_assignment.institute_id = holiday.entity_id and holiday_assignment.holiday_id = holiday.holiday_id " +
//            "where institute_id = ? and entity_id = ? and entity_name = ? %s order by start_time";
//
//    private static final String HOLIDAY_TIME_FILTER = " and ((start_time >= ? and start_time < ?)  or  (end_time > ? and end_time <= ?))";
//
//    private static final String DELETE_HOLIDAY_ASSIGNMENT = " delete from holiday_assignment where institute_id = ? and entity_id = ? and " +
//            "entity_name = ? and holiday_id = ?";

    private final JdbcTemplate jdbcTemplate;

    private final TransactionTemplate transactionTemplate;


    public HolidayCalendarDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
    }


    public boolean addHoliday(int instituteId, HolidayPayload holidayPayload) {
        return addHoliday(Entity.INSTITUTE, String.valueOf(instituteId), holidayPayload);
    }

    public boolean addGlobalHoliday(HolidayPayload holidayPayload) {
        return addHoliday(Entity.GLOBAL, Entity.GLOBAL.name(), holidayPayload);
    }

    private Timestamp getDayStartTimestamp(int time) {
        return new Timestamp(DateUtils.getDayStart(time, DateUtils.DEFAULT_TIMEZONE) * 1000l);
    }

    private Timestamp getDayEndTimestamp(int time) {
        return new Timestamp(DateUtils.getDayEnd(time, DateUtils.DEFAULT_TIMEZONE) * 1000l);
    }

    private boolean addHoliday(Entity entity, String entityId, HolidayPayload holidayPayload) {

        try {
            final int row = jdbcTemplate.update(INSERT_HOLIDAY, entity.name(), entityId, holidayPayload.getHolidayType().name(),
                    getDayStartTimestamp(holidayPayload.getStart()), getDayEndTimestamp(holidayPayload.getEnd()),
                    holidayPayload.getName(), holidayPayload.getDescription(), getMetadata(holidayPayload));
            return row == 1;
        } catch (final Exception e) {
            logger.error("Error while adding holiday for entity {} : {}, holidayPayload = {} ", entity, entityId, holidayPayload, e);
        }
        return false;
    }

    public List<Holiday> getHolidays(int instituteId, int start, int end) {
        return getHolidays(Entity.INSTITUTE, String.valueOf(instituteId), start, end);
    }

    public List<Holiday> getGlobalHolidays(int start, int end) {
        return getHolidays(Entity.GLOBAL, Entity.GLOBAL.name(), start, end);
    }

    private List<Holiday> getHolidays(Entity entity, String entityId, int start, int end) {
        try {
            Timestamp startTimestamp = new Timestamp(start * 1000l);
            Timestamp endTimestamp = new Timestamp(end * 1000l);
            return jdbcTemplate.query(GET_HOLIDAYS, new Object[]{entityId, entity.name(), startTimestamp, endTimestamp, startTimestamp, endTimestamp, startTimestamp, startTimestamp},
                    HOLIDAY_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error while getting holidays for entity {} : {}, start {}, end {}", entity, entityId, start, end, e);
        }
        return null;
    }

    public boolean updateHoliday(int instituteId, int holidayId, HolidayPayload holidayPayload) {
        return updateHoliday(Entity.INSTITUTE, String.valueOf(instituteId), holidayId, holidayPayload);
    }

    public boolean updateGlobalHoliday(int holidayId, HolidayPayload holidayPayload) {
        return updateHoliday(Entity.GLOBAL, Entity.GLOBAL.name(), holidayId, holidayPayload);
    }

    private boolean updateHoliday(Entity entity, String entityId, int holidayId, HolidayPayload holidayPayload) {
        try {
            final int row = jdbcTemplate.update(UPDATE_HOLIDAY, holidayPayload.getName(), holidayPayload.getDescription(),
                    getDayStartTimestamp(holidayPayload.getStart()), getDayEndTimestamp(holidayPayload.getEnd()), getMetadata(holidayPayload),
                    entityId, entity.name(), holidayId);
            return row == 1;
        } catch (final Exception e) {
            logger.error("Error while updating holiday for entity {} : {}, holidayId = {}, holidayPayload = {} ", entity, entityId, holidayId, holidayPayload, e);
        }
        return false;
    }

    public boolean deleteHoliday(int instituteId, int holidayId) {
        return deleteHoliday(Entity.INSTITUTE, String.valueOf(instituteId), holidayId);
    }

    public boolean deleteGlobalHoliday(int holidayId) {
        return deleteHoliday(Entity.GLOBAL, Entity.GLOBAL.name(), holidayId);
    }

    private boolean deleteHoliday(Entity entity, String entityId, int holidayId) {
        try {
            final int row = jdbcTemplate.update(DELETE_HOLIDAY, entityId, entity.name(), holidayId);
            return row == 1;
        } catch (final Exception e) {
            logger.error("Error while deleting holiday for entity {} : {}, holidayId = {}.", entity, entityId, holidayId, e);
        }
        return false;
    }

    private String getMetadata(HolidayPayload holidayPayload) {
        switch (holidayPayload.getHolidayType()) {
            case RECURRING:
                return GSON.toJson(holidayPayload.getRecurringPattern());
            default:
                return null;
        }
    }


    public UUID addHolidayTemplate(int instituteId, HolidayTemplatePayload holidayTemplatePayload) {
        try {
            return transactionTemplate.execute(new TransactionCallback<UUID>() {
                @Override
                public UUID doInTransaction(TransactionStatus status) {
                    UUID templateId = addHolidayTemplateMetadata(instituteId, holidayTemplatePayload);
                    if (templateId == null) {
                        throw new EmbrateRunTimeException("Unable to add holiday template metadata");
                    }

                    if (!addHolidayTemplateMapping(instituteId, templateId, holidayTemplatePayload)) {
                        throw new EmbrateRunTimeException("Error while adding holiday template mapping");
                    }
                    return templateId;
                }
            });
        } catch (final Exception e) {
            logger.error("Exception while adding holiday template for institute {}, holidayTemplatePayload {}", instituteId, holidayTemplatePayload, e);
        }
        return null;
    }
    // Holiday template//

    private UUID addHolidayTemplateMetadata(int instituteId, HolidayTemplatePayload holidayTemplate) {
        try {
            UUID templateId = UUID.randomUUID();
            final int row = jdbcTemplate.update(INSERT_HOLIDAY_TEMPLATE, instituteId, holidayTemplate.getAcademicSessionId(),
                    templateId.toString(), holidayTemplate.getTemplateName().trim(), holidayTemplate.getTemplateType().name(),
                    CollectionUtils.isEmpty(holidayTemplate.getStandardIds()) ? null : SharedConstants.GSON.toJson(holidayTemplate.getStandardIds()),
                    CollectionUtils.isEmpty(holidayTemplate.getTags()) ? null : SharedConstants.GSON.toJson(holidayTemplate.getTags()),
                    StringUtils.isBlank(holidayTemplate.getDescription()) ? null : holidayTemplate.getDescription().trim(),
                    holidayTemplate.getMetadata() == null ? null : SharedConstants.GSON.toJson(holidayTemplate.getMetadata()));
            return row == 1 ? templateId : null;
        } catch (final Exception e) {
            logger.error("Error while adding holiday template for instituteId {}, holidayTemplate {} ", instituteId, holidayTemplate, e);
        }
        return null;
    }

    private boolean updateHolidayTemplateMetadata(int instituteId, UUID templateId, HolidayTemplatePayload holidayTemplate) {
        try {
            return jdbcTemplate.update(UPDATE_HOLIDAY_TEMPLATE, holidayTemplate.getTemplateName().trim(), holidayTemplate.getTemplateType().name(),
                    CollectionUtils.isEmpty(holidayTemplate.getStandardIds()) ? null : SharedConstants.GSON.toJson(holidayTemplate.getStandardIds()),
                    CollectionUtils.isEmpty(holidayTemplate.getTags()) ? null : SharedConstants.GSON.toJson(holidayTemplate.getTags()),
                    StringUtils.isBlank(holidayTemplate.getDescription()) ? null : holidayTemplate.getDescription().trim(),
                    holidayTemplate.getMetadata() == null ? null : SharedConstants.GSON.toJson(holidayTemplate.getMetadata()),
                    instituteId, templateId.toString()) == 1;
        } catch (final Exception e) {
            logger.error("Error while updating holiday template for instituteId {}, holidayTemplate {} ", instituteId, holidayTemplate, e);
        }
        return false;
    }

    public List<HolidayTemplate> getHolidayTemplates(int instituteId, int academicSessionId) {
        try {
            return jdbcTemplate.query(GET_HOLIDAY_TEMPLATES, new Object[]{instituteId, academicSessionId}, HOLIDAY_TEMPLATE_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error while adding holiday template for instituteId {}, academicSessionId = {} ", instituteId, academicSessionId, e);
        }
        return null;
    }

    public HolidayTemplateDetails getHolidayTemplateDetails(int instituteId, UUID templateId) {
        try {
            return HolidayTemplateDetailsRowMapper.getHolidayTemplateDetails(
                    jdbcTemplate.query(GET_HOLIDAY_TEMPLATE_DETAILS, new Object[]{instituteId, templateId.toString()}, HOLIDAY_TEMPLATE_DETAILS_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while fetching holiday template for instituteId {}, templateId = {} ", instituteId, templateId, e);
        }
        return null;
    }

    public List<HolidayTemplateDetails> getHolidayTemplateDetails(int instituteId, Set<UUID> templateIds) {
        try {
            List<Object> args = new ArrayList<>();
            args.add(instituteId);
            StringBuilder sb = new StringBuilder();
            sb.append("(");
            String delimiter = "";
            for(UUID templateId : templateIds){
                args.add(templateId.toString());
                sb.append(delimiter).append(" ?");
                delimiter = ",";
            }
            sb.append(")");
            return HolidayTemplateDetailsRowMapper.getAllHolidayTemplateDetails(
                    jdbcTemplate.query(String.format(GET_ALL_HOLIDAY_TEMPLATE_DETAILS, sb), args.toArray(), HOLIDAY_TEMPLATE_DETAILS_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while fetching holiday template for instituteId {}, templateId = {} ", instituteId, templateIds, e);
        }
        return null;
    }


    public boolean updateHolidayTemplate(int instituteId, UUID templateId, HolidayTemplatePayload holidayTemplatePayload) {
        try {
            return transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    if (!updateHolidayTemplateMetadata(instituteId, templateId, holidayTemplatePayload)) {
                        throw new EmbrateRunTimeException("Unable to update holiday template metadata");
                    }
                    if (!deleteHolidayTemplateMapping(instituteId, templateId)) {
                        throw new EmbrateRunTimeException("Error while deleting holiday template mapping");
                    }

                    if (!addHolidayTemplateMapping(instituteId, templateId, holidayTemplatePayload)) {
                        throw new EmbrateRunTimeException("Error while adding holiday template mapping");
                    }
                    return true;
                }
            });
        } catch (final Exception e) {
            logger.error("Exception while updating holiday template for institute {}, templateId {}, holidayTemplatePayload {}",
                    instituteId, templateId, holidayTemplatePayload, e);
        }
        return false;
    }

    public boolean deleteHolidayTemplate(int instituteId, UUID templateId) {
        try {
            return transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    if (!deleteHolidayTemplateMapping(instituteId, templateId)) {
                        throw new EmbrateRunTimeException("Error while deleting holiday template mapping");
                    }

                    if (!deleteHolidayTemplateMetadata(instituteId, templateId)) {
                        throw new EmbrateRunTimeException("Unable to update holiday template metadata");
                    }

                    return true;
                }
            });
        } catch (final Exception e) {
            logger.error("Exception while updating holiday template for institute {}, templateId {}", instituteId, e);
        }
        return false;
    }

    private boolean deleteHolidayTemplateMetadata(int instituteId, UUID templateId) {
        try {
            return jdbcTemplate.update(DELETE_HOLIDAY_TEMPLATE, instituteId, templateId.toString()) == 1;
        } catch (final Exception e) {
            logger.error("Error while deleting holiday template metadata for instituteId {}, templateId {}", instituteId, templateId, e);
        }
        return false;
    }

    // Holiday assignment //
//    public boolean addTemplateHolidayAssignments(int instituteId, UUID templateId, Set<Integer> holidays) {
//        return addHolidayAssignments(instituteId, HolidayAssignmentEntity.TEMPLATE, templateId.toString(), holidays);
//    }
//
//    public boolean addUserHolidayAssignments(int instituteId, UUID userId, Set<Integer> holidays) {
//        return addHolidayAssignments(instituteId, HolidayAssignmentEntity.USER, userId.toString(), holidays);
//    }

    private boolean addHolidayTemplateMapping(int instituteId, UUID templateId, HolidayTemplatePayload holidayTemplatePayload) {
        try {
            List<Object[]> args = new ArrayList<>();
            for (Integer holiday : holidayTemplatePayload.getHolidays()) {
                args.add(new Object[]{instituteId, templateId.toString(), holiday});
            }
            final int[] rows = jdbcTemplate.batchUpdate(INSERT_HOLIDAY_TEMPLATE_MAPPING, args);
            return rows.length == holidayTemplatePayload.getHolidays().size();
        } catch (final Exception e) {
            logger.error("Error while adding holiday template mapping for instituteId {}, templateId {}, holidayTemplatePayload {} ",
                    instituteId, templateId, holidayTemplatePayload, e);
        }
        return false;
    }

    private boolean deleteHolidayTemplateMapping(int instituteId, UUID templateId) {
        try {
            return jdbcTemplate.update(DELETE_HOLIDAY_TEMPLATE_MAPPING, instituteId, templateId.toString()) >= 0;
        } catch (final Exception e) {
            logger.error("Error while deleting holiday template mapping for instituteId {}, templateId {}",
                    instituteId, templateId, e);
        }
        return false;
    }

    public UserHolidayTemplateData getUserTemplateAssignment(int instituteId, int academicSessionId, UUID userId) {
        try {
            return HolidayTemplateAssignmentRowMapper.getUserHolidayTemplateData(jdbcTemplate.query(GET_USER_HOLIDAY_TEMPLATE_ASSIGNMENT, new Object[]{instituteId, academicSessionId, userId.toString()},
                    HOLIDAY_TEMPLATE_ASSIGNMENT_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while getting holiday template assignment for instituteId {}, academicSessionId {}, userId {}", instituteId, academicSessionId, userId, e);
        }
        return null;
    }

    public List<UserHolidayTemplateData> getUserTemplateAssignment(int instituteId, int academicSessionId, UserType userType) {
        try {
            return HolidayTemplateAssignmentRowMapper.getUserHolidayTemplateDataList(jdbcTemplate.query(GET_HOLIDAY_TEMPLATE_ASSIGNMENT_FOR_USER_TYPE, new Object[]{instituteId, academicSessionId, userType.name()},
                    HOLIDAY_TEMPLATE_ASSIGNMENT_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while getting holiday template assignment for instituteId {}, academicSessionId {}, userType {}", instituteId, academicSessionId, userType, e);
        }
        return null;
    }

    // Must be used within transaction
    public boolean addUserHolidayTemplateAssignment(int instituteId, int academicSessionId, UserType userType, List<UserHolidayTemplateMapping> userHolidayTemplateMappingList) {
        try {
            List<Object[]> args = new ArrayList<>();
            for (UserHolidayTemplateMapping userHolidayTemplateMapping : userHolidayTemplateMappingList) {
                args.add(new Object[]{instituteId, userHolidayTemplateMapping.getUserId().toString(), userType.name(), academicSessionId, userHolidayTemplateMapping.getTemplateId().toString(), userHolidayTemplateMapping.getStartTime(), userHolidayTemplateMapping.getEndTime()});
            }
            final int[] rows = jdbcTemplate.batchUpdate(INSERT_USER_HOLIDAY_TEMPLATE_ASSIGNMENT, args);
            return rows.length == userHolidayTemplateMappingList.size();
        } catch (final Exception e) {
            logger.error("Error while adding holiday template assignment for instituteId {}, academicSessionId {}, userType {} userHolidayTemplateMappingList {}",
                    instituteId, academicSessionId, userType, userHolidayTemplateMappingList, e);
        }
        return false;
    }

    // Must be used within transaction
    public boolean deleteUserHolidayTemplateAssignment(int instituteId, int academicSessionId, UserType userType, Set<UUID> userIds) {
        try {
            List<Object []> args = new ArrayList<>();
            for(UUID userId : userIds){
                args.add(new Object[]{instituteId, academicSessionId, userType.name(), userId.toString()});
            }
            return jdbcTemplate.batchUpdate(DELETE_USER_HOLIDAY_TEMPLATE_ASSIGNMENT, args).length == userIds.size();
        } catch (final Exception e) {
            logger.error("Error while deleting holiday template assignment for instituteId {}, academicSessionId {}, userType {}, userIds {}",
                    instituteId, academicSessionId, userType, userIds, e);
        }

        return false;
    }

//    private boolean deleteHolidayAssignments(int instituteId, HolidayAssignmentEntity entity, String entityId, Set<Integer> holidays) {
//        try {
//            List<Object[]> args = new ArrayList<>();
//            for (Integer holiday : holidays) {
//                args.add(new Object[]{instituteId, entityId, entity.name(), holiday});
//            }
//            final int[] rows = jdbcTemplate.batchUpdate(DELETE_HOLIDAY_ASSIGNMENT, args);
//            return rows.length == holidays.size();
//        } catch (final Exception e) {
//            logger.error("Error while adding holiday assignments for instituteId {}, entity {}, entityId {}, holidays {} ",
//                    instituteId, entity, entityId, holidays, e);
//        }
//        return false;
//    }
//
//    public List<Holiday> getTemplateHolidayAssignments(int instituteId, UUID templateId) {
//        try {
//            return jdbcTemplate.query(String.format(GET_HOLIDAY_ASSIGNMENTS, ""), new Object[]{instituteId, templateId.toString(), HolidayAssignmentEntity.TEMPLATE.name()}, HOLIDAY_ROW_MAPPER);
//        } catch (final Exception e) {
//            logger.error("Error while getting holiday assignments for instituteId {}, templateId = {} ", instituteId, templateId, e);
//        }
//        return null;
//    }
//
//    public List<Holiday> getUserHolidayAssignments(int instituteId, UUID userId, int start, int end) {
//        try {
//            Timestamp startTimestamp = new Timestamp(start * 1000l);
//            Timestamp endTimestamp = new Timestamp(end * 1000l);
//            return jdbcTemplate.query(String.format(GET_HOLIDAY_ASSIGNMENTS, HOLIDAY_TIME_FILTER), new Object[]{instituteId, userId.toString(),
//                    HolidayAssignmentEntity.USER.name(), startTimestamp, endTimestamp, startTimestamp, endTimestamp}, HOLIDAY_ROW_MAPPER);
//        } catch (final Exception e) {
//            logger.error("Error while getting holiday assignments for instituteId {}, userId = {}, start {}, end {} ", instituteId, userId, start, end, e);
//        }
//        return null;
//    }

}