package com.embrate.cloud.dao.tier.calendar.holiday.mappers;

import com.embrate.cloud.core.api.calendar.holiday.Holiday;
import com.embrate.cloud.core.api.calendar.holiday.template.HolidayTemplate;
import com.embrate.cloud.core.api.calendar.holiday.template.HolidayTemplateDetails;
import com.embrate.cloud.core.api.calendar.holiday.template.HolidayTemplateDetailsRow;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 */
public class HolidayTemplateDetailsRowMapper implements RowMapper<HolidayTemplateDetailsRow> {

    private static final HolidayRowMapper HOLIDAY_ROW_MAPPER = new HolidayRowMapper();
    private static final HolidayTemplateRowMapper HOLIDAY_TEMPLATE_ROW_MAPPER = new HolidayTemplateRowMapper();


    @Override
    public HolidayTemplateDetailsRow mapRow(ResultSet rs, int rowNum) throws SQLException {
        HolidayTemplate holidayTemplate = HOLIDAY_TEMPLATE_ROW_MAPPER.mapRow(rs, rowNum);
        Holiday holiday = HOLIDAY_ROW_MAPPER.mapRow(rs, rowNum);
        return new HolidayTemplateDetailsRow(holidayTemplate, holiday);
    }

    // Assumes that all data belongs to same template id
    public static HolidayTemplateDetails getHolidayTemplateDetails(List<HolidayTemplateDetailsRow> holidayTemplateDetailsRowList) {
        if (CollectionUtils.isEmpty(holidayTemplateDetailsRowList)) {
            return null;
        }
        HolidayTemplateDetailsRow firstEntry = holidayTemplateDetailsRowList.get(0);
        List<Holiday> holidays = new ArrayList<>();
        for (HolidayTemplateDetailsRow holidayTemplateDetailsRow : holidayTemplateDetailsRowList) {
            holidays.add(holidayTemplateDetailsRow.getHoliday());
        }
        Collections.sort(holidays);
        return new HolidayTemplateDetails(firstEntry.getHolidayTemplate(), holidays);
    }

    // Assumes that all data belongs to same institute
    public static List<HolidayTemplateDetails> getAllHolidayTemplateDetails(List<HolidayTemplateDetailsRow> holidayTemplateDetailsRowList) {
        if (CollectionUtils.isEmpty(holidayTemplateDetailsRowList)) {
            return null;
        }
        Map<UUID, List<HolidayTemplateDetailsRow>> templateMap = new HashMap<>();
        for (HolidayTemplateDetailsRow holidayTemplateDetailsRow : holidayTemplateDetailsRowList) {
            UUID templateId = holidayTemplateDetailsRow.getHolidayTemplate().getTemplateId();
            if(!templateMap.containsKey(templateId)){
                templateMap.put(templateId, new ArrayList<>());
            }
            templateMap.get(templateId).add(holidayTemplateDetailsRow);
        }
        List<HolidayTemplateDetails> holidayTemplateDetailsList = new ArrayList<>();
        for(Map.Entry<UUID, List<HolidayTemplateDetailsRow>> entry : templateMap.entrySet()){
            HolidayTemplateDetails holidayTemplateDetails =  getHolidayTemplateDetails(entry.getValue());
            if(holidayTemplateDetails == null){
                continue;
            }
            holidayTemplateDetailsList.add(holidayTemplateDetails);
        }

        return holidayTemplateDetailsList;
    }
}