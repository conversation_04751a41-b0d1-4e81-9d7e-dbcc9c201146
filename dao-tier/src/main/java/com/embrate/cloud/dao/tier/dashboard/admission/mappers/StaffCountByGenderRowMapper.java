package com.embrate.cloud.dao.tier.dashboard.admission.mappers;

import com.embrate.cloud.core.api.dashboards.admission.StaffCountByGender;
import com.lernen.cloud.core.api.user.Gender;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for StaffCountByGender
 * Maps database results from staff count queries to StaffCountByGender POJO
 * 
 * <AUTHOR>
 */
public class StaffCountByGenderRowMapper implements RowMapper<StaffCountByGender> {

    protected static final String INSTITUTE_ID = "institute_id";
    protected static final String GENDER = "gender";
    protected static final String COUNT = "count";

    @Override
    public StaffCountByGender mapRow(ResultSet rs, int rowNum) throws SQLException {
        int instituteId = rs.getInt(INSTITUTE_ID);
        Gender gender = Gender.getGender(rs.getString(GENDER));
        int count = rs.getInt(COUNT);

        return new StaffCountByGender(instituteId, gender, count);
    }
}
