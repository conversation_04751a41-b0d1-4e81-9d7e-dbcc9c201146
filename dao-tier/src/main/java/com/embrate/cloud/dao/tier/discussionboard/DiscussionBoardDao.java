/**
 * 
 */
package com.embrate.cloud.dao.tier.discussionboard;

import java.util.List;
import java.util.UUID;

import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import com.embrate.cloud.core.api.discussionboard.ChannelDetails;
import com.embrate.cloud.core.api.discussionboard.ChannelDetailsPayload;
import com.embrate.cloud.core.api.discussionboard.ConversationPayload;
import com.embrate.cloud.core.api.discussionboard.DiscussionBoardEntity;
import com.embrate.cloud.core.api.discussionboard.ThreadConversationDetails;
import com.embrate.cloud.core.api.discussionboard.ThreadDetailsPayload;
import com.embrate.cloud.dao.tier.discussionboard.mappers.ChannelDetailsPayloadRowMapper;
import com.embrate.cloud.dao.tier.discussionboard.mappers.ChannelThreadRowMapper;
import com.embrate.cloud.dao.tier.discussionboard.mappers.ConversationDetailsPayloadRowMapper;
import com.embrate.cloud.dao.tier.discussionboard.mappers.ThreadConversationRowMapper;
import com.embrate.cloud.dao.tier.discussionboard.mappers.ThreadDetailsPayloadRowMapper;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.DatabaseException;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;

/**
 * <AUTHOR>
 *
 */
public class DiscussionBoardDao {

	private static final Logger logger = LogManager.getLogger(DiscussionBoardDao.class);

	private final JdbcTemplate jdbcTemplate;

	private final TransactionTemplate transactionTemplate;

	private static final ThreadDetailsPayloadRowMapper THREAD_DETAILS_PAYLOAD_ROW_MAPPER = new ThreadDetailsPayloadRowMapper();
	private static final ChannelThreadRowMapper CHANNEL_THREAD_ROW_MAPPER = new ChannelThreadRowMapper();
	private static final ThreadConversationRowMapper THREAD_CONVERSATION_ROW_MAPPER = new ThreadConversationRowMapper();
	private static final ChannelDetailsPayloadRowMapper CHANNEL_DETAILS_PAYLOAD_ROW_MAPPER = new ChannelDetailsPayloadRowMapper();
	private static final ConversationDetailsPayloadRowMapper CONVERSATION_DETAILS_PAYLOAD_ROW_MAPPER = new ConversationDetailsPayloadRowMapper();

	private static final String CREATE_CHANNEL = " insert into channel_details(institute_id, channel_id, entity, entity_id, "
			+ " channel_name, description, created_by, user_type) values(?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String UPDATE_CHANNEL_INFO = " update channel_details set  channel_name = ?, description = ? "
			+ " where  institute_id = ? and channel_id = ? ";

	private static final String DELETE_CHANNEL = " delete from channel_details where  institute_id = ? and channel_id = ? ";

	private static final String DELETE_CHANNEL_BY_ENTITY = " delete from channel_details where institute_id = ? and "
			+ "channel_details.entity_id = ? and channel_details.entity = ? ";

	private static final String CREATE_THREAD = " insert into channel_threads(thread_id, channel_id, name, description, created_by, attachments, user_type) "
			+ " values(?, ?, ?, ?, ?, ?, ?) ";

	private static final String UPDATE_THREAD_DETAILS = " update channel_threads set name = ?, description = ?, attachments = ? "
			+ " where thread_id = ? ";

	private static final String DELETE_THREAD = " delete from channel_threads where thread_id = ? and created_by = ? ";

	private static final String DELETE_THREAD_BY_CHANNEL_ID = " delete from channel_threads where channel_id = ? ";

	private static final String DELETE_THREAD_BY_ENTITY = " delete from channel_threads where channel_id in "
			+ "(select channel_id from channel_details where channel_details.entity_id = ? and channel_details.entity = ?)";

	private static final String GET_THREAD_IDS_BY_CHANNEL_ID = " select * from channel_threads where channel_id = ? ";

	private static final String CREATE_CONVERSATION = " insert into conversation_details(conversation_id, thread_id, created_by, message) "
			+ " values(?, ?, ?, ?) ";

	private static final String UPDATE_CONVERSATION_DETAILS = " update conversation_details set message = ? "
			+ " where  conversation_id = ? and created_by = ? ";

	private static final String DELETE_CONVERSATION = " delete from conversation_details where conversation_id = ? and created_by = ? ";

	private static final String DELETE_CONVERSATION_BY_CHANNEL_ID = " delete from conversation_details where thread_id in "
			+ "(select thread_id from channel_threads where channel_id = ?) ";

	private static final String DELETE_CONVERSATION_BY_ENTITY = " delete from conversation_details where thread_id in "
			+ "(select thread_id from channel_threads where channel_id in "
			+ "(select channel_id from channel_details where channel_details.entity_id = ? and channel_details.entity = ? ))";

	private static final String DELETE_CONVERSATION_BY_THREAD_ID = " delete from conversation_details where thread_id = ? ";

	private static final String GET_THREADS_IN_CHANNEL = " select * from channel_details "
			+ " left join channel_threads on channel_threads.channel_id = channel_details.channel_id "
			+ " left join users on users.user_id = channel_threads.created_by "
			+ " where channel_details.institute_id = ? and channel_details.channel_id = ? "
			+ " order by channel_threads.created_timestamp desc ";
	
	private static final String GET_CHANNEL_DETAILS_BY_THREAD_ID = " select * from channel_details "
			+ " inner join channel_threads on channel_threads.channel_id = channel_details.channel_id "
			+ " left join users on users.user_id = channel_threads.created_by "
			+ " where channel_details.institute_id = ? and channel_threads.thread_id = ? "
			+ " order by channel_threads.created_timestamp desc ";
	
	private static final String GET_CHANNEL_DETAILS_BY_CONVERSATION_ID = " select * from channel_details "
			+ " inner join channel_threads on channel_threads.channel_id = channel_details.channel_id "
			+ " left join users on users.user_id = channel_threads.created_by "
			+ " inner join conversation_details on conversation_details.thread_id =  channel_threads.thread_id "
			+ " where channel_details.institute_id = ? and conversation_details.conversation_id = ? "
			+ " order by channel_threads.created_timestamp desc ";
	
	private static final String GET_THREADS_IN_CHANNEL_BY_ENTITY_ID = " select * from channel_details "
			+ " left join channel_threads on channel_threads.channel_id = channel_details.channel_id "
			+ " left join users on users.user_id = channel_threads.created_by "
			+ " where channel_details.institute_id = ? and channel_details.entity_id = ? and channel_details.entity = ? "
			+ " order by channel_threads.created_timestamp desc ";

	private static final String GET_THREADS_IN_CHANNEL_BY_ENTITY_ID_WITH_HOMEWORK_DETAILS = " select * from channel_details "
			+ " left join channel_threads on channel_threads.channel_id = channel_details.channel_id "
			+ " left join users on users.user_id = channel_threads.created_by "
			+ " inner join homework_details on homework_details.homework_id = channel_details.entity_id "
			+ " where channel_details.institute_id = ? and channel_details.entity_id = ? and channel_details.entity = ? "
			+ " order by channel_threads.created_timestamp desc ";
	
	private static final String GET_THREADS_IN_CHANNEL_BY_ENTITY_ID_WITH_LECTURE_DETAILS =  " select * from channel_details "
			+ " left join channel_threads on channel_threads.channel_id = channel_details.channel_id "
			+ " left join users on users.user_id = channel_threads.created_by "
			+ " inner join lecture_details on lecture_details.lecture_id = channel_details.entity_id "
			+ " where channel_details.institute_id = ? and channel_details.entity_id = ? and channel_details.entity = ? "
			+ " order by channel_threads.created_timestamp desc ";

	private static final String GET_CONVERSATIONS_IN_THREAD = " select * from channel_threads "
			+ " left join conversation_details on conversation_details.thread_id = channel_threads.thread_id "
			+ " left join users on users.user_id = conversation_details.created_by "
			+ " where channel_threads.thread_id = ? " + " order by conversation_details.created_timestamp desc ";

	private static final String GET_CHANNEL_DETAILS_BY_CHANNEL_ID = " select * from channel_details where institute_id = ? and channel_id = ? ";

	private static final String GET_THREAD_DETAILS_BY_THREAD_ID = " select * from channel_threads where thread_id = ? ";

	private static final String GET_CONVERSATION_DETAILS_BY_CONVERSATION_ID = " select * from conversation_details where conversation_id = ? ";
	
	private static final String GET_THREAD_DETAILS_BY_CONVERSATION_ID = " select * from channel_threads "
			+ " inner join conversation_details on conversation_details.thread_id = channel_threads.thread_id "
			+ " where conversation_details.conversation_id = ? ";

	public DiscussionBoardDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
		this.jdbcTemplate = jdbcTemplate;
		this.transactionTemplate = transactionTemplate;
	}

	public Boolean createChannel(ChannelDetailsPayload channelDetailsPayload) {
		try {
			channelDetailsPayload.setChannelId(UUID.randomUUID());
			return jdbcTemplate.update(CREATE_CHANNEL, channelDetailsPayload.getInstituteId(),
					channelDetailsPayload.getChannelId().toString(),
					channelDetailsPayload.getDiscussionBoardEntity().name(), channelDetailsPayload.getEntityId(),
					channelDetailsPayload.getChannelName(), channelDetailsPayload.getDescription(),
					channelDetailsPayload.getCreatedBy() == null ? null
							: channelDetailsPayload.getCreatedBy().toString(),
					channelDetailsPayload.getUserType().name()) == 1;
		} catch (final Exception e) {
			logger.error("Exception while creating hannel for entity {}, instituteId {}",
					channelDetailsPayload.getDiscussionBoardEntity().name(), channelDetailsPayload.getInstituteId(), e);
		}
		return false;
	}

	public boolean updateChannelInfo(ChannelDetailsPayload channelDetailsPayload) {
		try {
			return jdbcTemplate.update(UPDATE_CHANNEL_INFO, channelDetailsPayload.getChannelName(),
					channelDetailsPayload.getDescription(), channelDetailsPayload.getInstituteId(),
					channelDetailsPayload.getChannelId().toString()) == 1;
		} catch (final Exception e) {
			logger.error("Exception while updating channel info for institute {}, channel {}",
					channelDetailsPayload.getInstituteId(), channelDetailsPayload, e);
		}
		return false;
	}

	public boolean deleteChannel(int instituteId, UUID userId, UUID channelId) {
		try {
			final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					return deleteChannelNonAtomic(instituteId, userId, channelId);
				}

			});
			return success.booleanValue();
		} catch (final Exception e) {
			logger.error("Error while deleting channel {} for institute {}", channelId, instituteId, e);
		}
		return false;
	}

	/**
	 * Always use within transaction
	 * 
	 * @param instituteId
	 * @param userId
	 * @param channelId
	 * @return
	 */
	public boolean deleteChannelNonAtomic(int instituteId, UUID userId, UUID channelId) {
		boolean result = deleteChannelThreadsNonAtomic(channelId);
		if (!result) {
			throw new EmbrateRunTimeException("Error while deleting thread of conversation. Please try again.");
		}
		result = jdbcTemplate.update(DELETE_CHANNEL, instituteId, channelId.toString()) == 1;
		if (!result) {
			throw new EmbrateRunTimeException("Unable to delete channel");
		}
		return true;
	}

	public boolean deleteChannelByEntityNonAtomic(int instituteId, UUID userId,
			DiscussionBoardEntity discussionBoardEntity, String entityId) {
		boolean result = deleteChannelThreadsByEntityNonAtomic(discussionBoardEntity, entityId);
		if (!result) {
			throw new EmbrateRunTimeException("Error while deleting thread of conversation. Please try again.");
		}
		result = jdbcTemplate.update(DELETE_CHANNEL_BY_ENTITY, instituteId, entityId, discussionBoardEntity.name()) >= 1;
		if (!result) {
			throw new EmbrateRunTimeException("Unable to delete channel");
		}
		return true;
	}

	/**
	 * Always use this within transaction
	 * 
	 * @param channelId
	 * @return
	 */
	private boolean deleteChannelThreadsNonAtomic(UUID channelId) {
		boolean success = jdbcTemplate.update(DELETE_CONVERSATION_BY_CHANNEL_ID, channelId.toString()) >= 0;
		if (!success) {
			throw new EmbrateRunTimeException("Unable to delete channel conversations");
		}

		success = jdbcTemplate.update(DELETE_THREAD_BY_CHANNEL_ID, channelId.toString()) >= 0;

		if (!success) {
			throw new EmbrateRunTimeException("Unable to delete channel threads");
		}

		return true;
	}

	/**
	 * Always use this within transaction
	 * 
	 * @param channelId
	 * @return
	 */
	private boolean deleteChannelThreadsByEntityNonAtomic(DiscussionBoardEntity discussionBoardEntity,
			String entityId) {
		boolean success = jdbcTemplate.update(DELETE_CONVERSATION_BY_ENTITY, entityId,
				discussionBoardEntity.name()) >= 0;
		if (!success) {
			throw new EmbrateRunTimeException("Unable to delete channel conversations");
		}

		success = jdbcTemplate.update(DELETE_THREAD_BY_ENTITY, entityId, discussionBoardEntity.name()) >= 0;

		if (!success) {
			throw new EmbrateRunTimeException("Unable to delete channel threads");
		}

		return true;
	}

	private List<ThreadDetailsPayload> getThreadIdsByChannelId(UUID channelId) {
		try {
			final Object[] args = { channelId.toString() };
			return jdbcTemplate.query(GET_THREAD_IDS_BY_CHANNEL_ID, args, THREAD_DETAILS_PAYLOAD_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting thread details by channel id {}", channelId, e);
		}
		return null;
	}

	public UUID createThread(ThreadDetailsPayload threadDetailsPayload) {
		try {
			threadDetailsPayload.setThreadId(UUID.randomUUID());
			boolean result = jdbcTemplate.update(CREATE_THREAD, threadDetailsPayload.getThreadId().toString(),
					threadDetailsPayload.getChannelId().toString(), threadDetailsPayload.getName(),
					threadDetailsPayload.getDescription(), threadDetailsPayload.getCreatedBy().toString(),
					threadDetailsPayload.getAttachments(), threadDetailsPayload.getUserType().name()) == 1;
			if(result) {
				return threadDetailsPayload.getThreadId();
			}
			return null;
		} catch (final Exception e) {
			logger.error("Exception while creating thread for channel {}", threadDetailsPayload.getChannelId(), e);
		}
		return null;
	}

	public boolean updateThreadDetails(ThreadDetailsPayload threadDetailsPayload) {
		try {
			return jdbcTemplate.update(UPDATE_THREAD_DETAILS, threadDetailsPayload.getName(),
					threadDetailsPayload.getDescription(), threadDetailsPayload.getAttachments(),
					threadDetailsPayload.getThreadId().toString()) == 1;
		} catch (final Exception e) {
			logger.error("Exception while updating thread details for thread {}", threadDetailsPayload.getThreadId(),
					e);
		}
		return false;
	}

	public boolean deleteThread(int instituteId, UUID userId, UUID threadId) {
		try {
			final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {

					ThreadConversationDetails threadConversationDetails = getConversationsInThread(instituteId,
							threadId);
					Boolean result = true;
					if (threadConversationDetails != null
							&& !CollectionUtils.isEmpty(threadConversationDetails.getConversationDetailsList())) {
						result = deleteConversationByThreadId(threadId);
					}
					if (!result) {
						throw new RuntimeException("Error while deleting conversation of thread. Please try again.");
					}
					result = jdbcTemplate.update(DELETE_THREAD, threadId.toString(), userId.toString()) == 1;
					if (!result) {
						throw new RuntimeException("Error while deleting thread. Please try again.");
					}
					return true;
				}
			});
			return success;
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (final Exception e) {
			logger.error("Error while adding lectures for institute {}", instituteId, e);
		}
		return false;
	}

	private Boolean deleteConversationByThreadId(UUID threadId) {
		try {
			return jdbcTemplate.update(DELETE_CONVERSATION_BY_THREAD_ID, threadId.toString()) >= 1;
		} catch (final Exception e) {
			logger.error("Exception while deleting conversation for thread {}", threadId, e);
		}
		return false;
	}

	public UUID createConversation(ConversationPayload conversationPayload) {
		try {
			conversationPayload.setConversationId(UUID.randomUUID());
			boolean result = jdbcTemplate.update(CREATE_CONVERSATION, conversationPayload.getConversationId().toString(),
					conversationPayload.getThreadId().toString(), conversationPayload.getCreatedBy().toString(),
					conversationPayload.getMessage()) == 1;
			if(result) {
				return conversationPayload.getConversationId();
			}
		} catch (final Exception e) {
			logger.error("Exception while creating thread for thread {}", conversationPayload.getThreadId(), e);
		}
		return null;
	}

	public boolean updateThreadDetails(ConversationPayload conversationPayload) {
		try {
			return jdbcTemplate.update(UPDATE_CONVERSATION_DETAILS, conversationPayload.getMessage(),
					conversationPayload.getConversationId().toString(),
					conversationPayload.getCreatedBy().toString()) == 1;
		} catch (final Exception e) {
			logger.error("Exception while updating conversation details for thread {}",
					conversationPayload.getConversationId(), e);
		}
		return false;
	}

	public boolean deleteConversation(int instituteId, UUID userId, UUID conversationId) {
		try {
			return jdbcTemplate.update(DELETE_CONVERSATION, conversationId.toString(), userId.toString()) == 1;
		} catch (final Exception e) {
			logger.error("Exception while deleting conversation for institute {}, conversation {}", instituteId,
					conversationId, e);
		}
		return false;
	}

	public ChannelDetails getThreadsInChannel(int instituteId, UUID channelId) {
		try {
			final Object[] args = { instituteId, channelId.toString() };
			return ChannelThreadRowMapper
					.getChannelDetails(jdbcTemplate.query(GET_THREADS_IN_CHANNEL, args, CHANNEL_THREAD_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while getting threads details for instituteId {},channelId {}", instituteId,
					channelId, e);
		}
		return null;
	}
	
	public ChannelDetailsPayload getChannelDetailsByThreadId(int instituteId, UUID threadId) {
		try {
			final Object[] args = { instituteId, threadId.toString() };
			return jdbcTemplate.queryForObject(GET_CHANNEL_DETAILS_BY_THREAD_ID, args, CHANNEL_DETAILS_PAYLOAD_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting threads details for instituteId {},channelId {}", instituteId,
					threadId, e);
		}
		return null;
	}

	/**
	 * TODO : Handle multiple channels for single entity id case
	 * 
	 * @param instituteId
	 * @param discussionBoardEntity
	 * @param entityId
	 * @return
	 */
	public ChannelDetails getEntityChannelDetails(int instituteId, DiscussionBoardEntity discussionBoardEntity,
			String entityId) {
		try {
			final Object[] args = { instituteId, entityId, discussionBoardEntity.name() };
			
			if(discussionBoardEntity == DiscussionBoardEntity.ONLINE_LECTURE) {
				return ChannelThreadRowMapper.getChannelDetails(
						jdbcTemplate.query(GET_THREADS_IN_CHANNEL_BY_ENTITY_ID_WITH_LECTURE_DETAILS, 
								args, CHANNEL_THREAD_ROW_MAPPER));
			}
			else if (discussionBoardEntity == DiscussionBoardEntity.HOMEWORK) {
				return ChannelThreadRowMapper.getChannelDetails(
						jdbcTemplate.query(GET_THREADS_IN_CHANNEL_BY_ENTITY_ID_WITH_HOMEWORK_DETAILS, 
								args, CHANNEL_THREAD_ROW_MAPPER));
			}
			else {
				return ChannelThreadRowMapper.getChannelDetails(
						jdbcTemplate.query(GET_THREADS_IN_CHANNEL_BY_ENTITY_ID, args, CHANNEL_THREAD_ROW_MAPPER));
			}
		} catch (final Exception e) {
			logger.error(
					"Exception while getting threads details for instituteId {}, discussionBoardEntity {}, entityId {}",
					instituteId, discussionBoardEntity, entityId, e);
		}
		return null;
	}

	public ThreadConversationDetails getConversationsInThread(int instituteId, UUID threadId) {
		try {
			final Object[] args = { threadId.toString() };
			return ThreadConversationRowMapper.getThreadDetails(
					jdbcTemplate.query(GET_CONVERSATIONS_IN_THREAD, args, THREAD_CONVERSATION_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while getting conversation details for instituteId {}, threadId {}", instituteId,
					threadId, e);
		}
		return null;
	}

	public ChannelDetailsPayload getChannelDetailsByChannelId(int instituteId, UUID channelId) {
		try {
			final Object[] args = { instituteId, channelId.toString() };
			return jdbcTemplate.queryForObject(GET_CHANNEL_DETAILS_BY_CHANNEL_ID, args,
					CHANNEL_DETAILS_PAYLOAD_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting channel details instituteId {}, channelId {}", instituteId, channelId,
					e);
		}
		return null;
	}

	public ThreadDetailsPayload getThreadDetailsByThreadId(int instituteId, UUID threadId) {
		try {
			final Object[] args = { threadId.toString() };
			return jdbcTemplate.queryForObject(GET_THREAD_DETAILS_BY_THREAD_ID, args,
					THREAD_DETAILS_PAYLOAD_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting thread details instituteId {}, thread {}", instituteId, threadId, e);
		}
		return null;
	}

	public ConversationPayload getConversationDetailsByConversationId(int instituteId, UUID conversationId) {
		try {
			final Object[] args = { conversationId.toString() };
			return jdbcTemplate.queryForObject(GET_CONVERSATION_DETAILS_BY_CONVERSATION_ID, args,
					CONVERSATION_DETAILS_PAYLOAD_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting conversation details instituteId {}, conversationId {}", instituteId,
					conversationId, e);
		}
		return null;
	}

	public ChannelDetailsPayload getChannelDetailsByConversationId(int instituteId, UUID conversationId) {
		try {
			final Object[] args = { instituteId, conversationId.toString() };
			return jdbcTemplate.queryForObject(GET_CHANNEL_DETAILS_BY_CONVERSATION_ID, args, CHANNEL_DETAILS_PAYLOAD_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting threads details for instituteId {}, conversationId {}", instituteId,
					conversationId, e);
		}
		return null;
	}
	
	public ThreadDetailsPayload getThreadDetailsByConversationId(int instituteId, UUID conversationId) {
		try {
			final Object[] args = { conversationId.toString() };
			return jdbcTemplate.queryForObject(GET_THREAD_DETAILS_BY_CONVERSATION_ID, args, THREAD_DETAILS_PAYLOAD_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting threads details for instituteId {}, conversationId {}", instituteId,
					conversationId, e);
		}
		return null;
	}
}
