/**
 * 
 */
package com.embrate.cloud.dao.tier.discussionboard.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.embrate.cloud.core.api.discussionboard.ChannelDetailsPayload;
import com.embrate.cloud.core.api.discussionboard.DiscussionBoardEntity;
import com.lernen.cloud.core.api.user.UserType;

/**
 * <AUTHOR>
 *
 */
public class ChannelDetailsPayloadRowMapper implements RowMapper<ChannelDetailsPayload> {

	protected static final String INSTITUTE_ID = "channel_details.institute_id";
	protected static final String CHANNEL_ID = "channel_details.channel_id";
	protected static final String ENTITY = "channel_details.entity";
	protected static final String ENTITY_ID = "channel_details.entity_id";
	protected static final String CHANNEL_NAME = "channel_details.channel_name";
	protected static final String DESCRIPTION = "channel_details.description";
	protected static final String CREATED_BY = "channel_details.created_by";
	protected static final String USER_TYPE = "channel_details.user_type";

	@Override
	public ChannelDetailsPayload mapRow(ResultSet rs, int rowNum) throws SQLException {
		if (rs.getString(CHANNEL_ID) == null) {
			return null;
		}
		return new ChannelDetailsPayload(rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(CHANNEL_ID)),
				DiscussionBoardEntity.getDiscussionBoardEntity(rs.getString(ENTITY)), rs.getString(ENTITY_ID),
				rs.getString(CHANNEL_NAME), rs.getString(DESCRIPTION),
				rs.getString(CREATED_BY) == null ? null : UUID.fromString(rs.getString(CREATED_BY)),
				UserType.getUserType(rs.getString(USER_TYPE)));
	}
}
