package com.embrate.cloud.dao.tier.homework.mappers;

import com.embrate.cloud.core.api.homework.HomeworkType;
import com.embrate.cloud.core.api.homework.HomeworkPayload;
import com.embrate.cloud.core.api.homework.HomeworkStatus;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.student.StudentDocumentType;
import com.lernen.cloud.core.api.user.Document;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;

public class HomeworkDetailsPayloadRowMapper implements RowMapper<HomeworkPayload> {

	protected static final String INSTITUTE_ID = "homework_details.institute_id";
	protected static final String ACADEMIC_SESSION_ID = "homework_details.academic_session_id";
	protected static final String HOMEWORK_ID = "homework_details.homework_id";
	protected static final String COURSE_ID = "homework_details.course_id";
	protected static final String STANDARD_ID = "homework_details.standard_id";
	protected static final String SECTION_IDS = "homework_details.section_ids";
	protected static final String CHAPTER = "homework_details.chapter";
	protected static final String TITLE = "homework_details.title";
	protected static final String HOMEWORK_TYPE = "homework_details.homework_type";
	protected static final String CREATED_USER_ID = "homework_details.created_user_id";
	protected static final String CREATED_TIMESTAMP = "homework_details.created_timestamp";
	protected static final String UPDATED_USER_ID = "homework_details.updated_user_id";
	protected static final String UPDATED_TIMESTAMP = "homework_details.updated_timestamp";
	protected static final String DESCRIPTION = "homework_details.description";
	protected static final String DUE_DATE = "homework_details.due_date";
	protected static final String STATUS = "homework_details.status";
	protected static final String BROADCASTED_TIMESTAMP = "homework_details.broadcasted_timestamp";
	protected static final String BROADCASTED_USER_ID = "homework_details.broadcasted_user_id";
	protected static final String FACULTY_USER_ID = "homework_details.faculty_user_id";
	protected static final String FACULTY_NAME = "homework_details.faculty_name";
	protected static final String ALLOW_EDIT_AFTER_SUBMIT = "homework_details.allow_edit_after_submit";
	protected static final String IS_GRADE = "homework_details.is_grade";
	protected static final String MAX_MARKS = "homework_details.max_marks";
	protected static final String SCHEDULE_TIMESTAMP = "homework_details.scheduled_timestamp";
	protected static final String ATTACHMENTS = "homework_details.attachments";
	protected static final String ALLOW_MOBILE_APP_SUBMISSIONS = "homework_details.allow_mobile_app_submissions";
	protected static final String ATTACH_DISCUSSION_FORUM = "homework_details.attach_discussion_forum";

	private static final Gson GSON = new Gson();

	@Override
	public HomeworkPayload mapRow(ResultSet rs, int rowNum) throws SQLException {
		if (rs.getString(HOMEWORK_ID) == null) {
			return null;
		}
		
		final Timestamp dueDate = rs.getTimestamp(DUE_DATE);
		final Integer dueDateTime = dueDate == null ? null
				: (int) (dueDate.getTime() / 1000l);
		
		final Timestamp scheduleTimestamp = rs.getTimestamp(SCHEDULE_TIMESTAMP);
		final Integer scheduleTimestampTime = scheduleTimestamp == null ? null
				: (int) (scheduleTimestamp.getTime() / 1000l);

		final String sectionIdStr = rs.getString(SECTION_IDS);
		List<Integer> sectionIdList = null;
		if (!StringUtils.isBlank(sectionIdStr)) {
			final Type collectionType = new TypeToken<List<Integer>>() {
			}.getType();
			sectionIdList = GSON.fromJson(sectionIdStr, collectionType);
		}

		return new HomeworkPayload(rs.getInt(INSTITUTE_ID),rs.getInt(ACADEMIC_SESSION_ID), UUID.fromString(rs.getString(HOMEWORK_ID)),
				UUID.fromString(rs.getString(STANDARD_ID)), sectionIdList, rs.getString(COURSE_ID) == null ? null :UUID.fromString(rs.getString(COURSE_ID)),
				rs.getString(CHAPTER), rs.getString(TITLE), HomeworkType.getHomeworkType(rs.getString(HOMEWORK_TYPE)),
				rs.getBoolean(ALLOW_MOBILE_APP_SUBMISSIONS), rs.getBoolean(ATTACH_DISCUSSION_FORUM), UUID.fromString(rs.getString(CREATED_USER_ID)),
				rs.getString(UPDATED_USER_ID) == null ? null : UUID.fromString(rs.getString(UPDATED_USER_ID)),
				rs.getString(DESCRIPTION), dueDateTime, HomeworkStatus.getHomeworkStatus(rs.getString(STATUS)),
				rs.getString(BROADCASTED_USER_ID) == null ? null : UUID.fromString(rs.getString(BROADCASTED_USER_ID)),
				rs.getString(FACULTY_USER_ID) == null ? null : UUID.fromString(rs.getString(FACULTY_USER_ID)),
				rs.getString(FACULTY_NAME), rs.getBoolean(ALLOW_EDIT_AFTER_SUBMIT), rs.getBoolean(IS_GRADE),
				rs.getDouble(MAX_MARKS), scheduleTimestampTime);		
	}
}
