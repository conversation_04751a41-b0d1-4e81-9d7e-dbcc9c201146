package com.embrate.cloud.dao.tier.homework.mappers;

import com.embrate.cloud.core.api.homework.HomeworkSubmissionDetails;
import com.embrate.cloud.core.api.homework.HomeworkViewDetails;
import com.embrate.cloud.core.api.homework.StudentHomeworkViewSubmissionDetails;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.dao.tier.student.mappers.StudentRowMapper;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class StudentHomeworkViewSubmissionDetailsRowMapper implements RowMapper<StudentHomeworkViewSubmissionDetails> {

    private static final StudentRowMapper STUDENT_ROW_MAPPER = new StudentRowMapper();
    private static final HomeworkSubmissionDetailsRowMapper HOMEWORK_SUBMISSION_DETAILS_ROW_MAPPER = new HomeworkSubmissionDetailsRowMapper();
    private static final HomeworkViewDetailsRowMapper HOMEWORK_VIEW_DETAILS_ROW_MAPPER = new HomeworkViewDetailsRowMapper();

    @Override
    public StudentHomeworkViewSubmissionDetails mapRow(ResultSet rs, int rowNum) throws SQLException {


        final StudentLite studentLite = Student.getStudentLite(STUDENT_ROW_MAPPER.mapRow(rs, rowNum));
        if(studentLite == null){
            return null;
        }
        final HomeworkSubmissionDetails homeworkSubmissionDetails = HOMEWORK_SUBMISSION_DETAILS_ROW_MAPPER.mapRow(rs, rowNum);
        final HomeworkViewDetails homeworkViewDetails = HOMEWORK_VIEW_DETAILS_ROW_MAPPER.mapRow(rs, rowNum);
        return new StudentHomeworkViewSubmissionDetails(studentLite, homeworkViewDetails, homeworkSubmissionDetails);
    }
}
