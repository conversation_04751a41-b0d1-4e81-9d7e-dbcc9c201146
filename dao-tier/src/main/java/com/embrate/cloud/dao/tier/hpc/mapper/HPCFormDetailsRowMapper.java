/**
 *
 */
package com.embrate.cloud.dao.tier.hpc.mapper;

import com.embrate.cloud.core.api.hpc.layout.HPCForm;
import com.embrate.cloud.core.api.hpc.payload.HPCFormDetails;
import com.google.gson.Gson;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class HPCFormDetailsRowMapper implements RowMapper<HPCFormDetails> {

	private static final String FORM_ID = "hpc_form.form_id";
	private static final String INSTITUTE_ID = "hpc_form.institute_id";
	private static final String ACADEMIC_SESSION_ID = "hpc_form.academic_session_id";
	private static final String STANDARD_ID = "hpc_form.standard_id";
	private static final String FORM_NAME = "hpc_form.name";

	private static final HPCFormRowMapper HPC_FORM_ROW_MAPPER = new HPCFormRowMapper();

	@Override
	public HPCFormDetails mapRow(ResultSet rs, int rowNum) throws SQLException {

		HPCForm hpcForm = HPC_FORM_ROW_MAPPER.mapRow(rs, rowNum);
		return new HPCFormDetails(UUID.fromString(rs.getString(FORM_ID)), rs.getInt(INSTITUTE_ID),
				rs.getInt(ACADEMIC_SESSION_ID), UUID.fromString(rs.getString(STANDARD_ID)),
				rs.getString(FORM_NAME), hpcForm);
	}

}