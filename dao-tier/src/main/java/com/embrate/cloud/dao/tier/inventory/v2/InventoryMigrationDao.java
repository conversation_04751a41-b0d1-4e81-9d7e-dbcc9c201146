package com.embrate.cloud.dao.tier.inventory.v2;

import com.embrate.cloud.core.api.inventory.v2.TradeProductBatchPayload;
import com.embrate.cloud.core.api.inventory.v2.TradeProductPayload;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.inventory.TransactionSummary;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class InventoryMigrationDao {

    private static final Logger logger = LogManager.getLogger(InventoryMigrationDao.class);

    private static final String SET_PRODUCT_BATCH_TOTAL_QUANTITY_FOR_MIGRATION = "update product_batch_details set total_quantity = ?, " +
            "in_use = ? where outlet_id = ? and sku_id = ? and batch_id = ?";


    private static final String ADD_TRANSACTION_METADATA = "insert into inventory_transaction_metadata (outlet_id, transaction_id, invoice_id, " +
            "institute_id, inventory_user_type, transaction_type, reference, email, supplier_id, transaction_to, transaction_by, transaction_date, " +
            "transaction_added_at, payment_status, payment_mode, transaction_status, additional_cost, additional_discount, wallet_debit_amount, "
            + "wallet_based_credit_amount, paid_amount, documents, meta_data, description) "
            + "values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String ADD_TRANSACTION_DETAILS = "insert into inventory_transaction_details (transaction_id, sku_id, batch_id, "
            + "quantity, total_price, total_discount, total_tax)"
            + "values (?, ?, ?, ?, ?, ?, ?)";


    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;


    public InventoryMigrationDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
    }


    public boolean setProductBatchesQuantityForMigration(UUID outletId, Map<UUID, Map<UUID, Pair<Double, Boolean>>> productBatchQuantitySetPayloadMap) {
        List<Object[]> batchArgs = new ArrayList<>();
        for (Map.Entry<UUID, Map<UUID,  Pair<Double, Boolean>>> productEntry : productBatchQuantitySetPayloadMap.entrySet()) {
            UUID skuId = productEntry.getKey();
            for (Map.Entry<UUID,  Pair<Double, Boolean>> batchEntry : productEntry.getValue().entrySet()) {
                batchArgs.add(new Object[]{batchEntry.getValue().getFirst() == null ? 0d : batchEntry.getValue().getFirst(),
                        batchEntry.getValue().getSecond() != null && batchEntry.getValue().getSecond(),
                        outletId.toString(), skuId.toString(), batchEntry.getKey().toString()});
            }
        }
        try {
            int[] rows = jdbcTemplate.batchUpdate(SET_PRODUCT_BATCH_TOTAL_QUANTITY_FOR_MIGRATION, batchArgs);
            return rows.length == batchArgs.size();
        } catch (final Exception e) {
            logger.error("Error while setting product batch total quantity for migration outlet {}, productBatchQuantityUpdatePayloadMap {}", outletId, productBatchQuantitySetPayloadMap, e);
        }
        return false;
    }



    public boolean migrateTransaction(UUID outletId, String invoiceId, Integer inventoryInstituteId, UUID supplierId, String transactionTo, UUID userId,
                                      TransactionSummary transactionSummary, List<TradeProductPayload> tradePayloadPayloadList) {
        int rows = jdbcTemplate.update(ADD_TRANSACTION_METADATA, outletId.toString(),
                transactionSummary.getTransactionId().toString(), invoiceId, inventoryInstituteId,
                transactionSummary.getInventoryUserType().name(), transactionSummary.getTransactionType().name(),
                transactionSummary.getReference() == null ? null : transactionSummary.getReference().trim(),
                transactionSummary.getEmail() == null ? null : CryptoUtils.encrypt(transactionSummary.getEmail().trim()),
                supplierId == null ? null : supplierId.toString(),
                transactionTo, userId.toString(),
                new Timestamp(transactionSummary.getTransactionDate()), new Timestamp(transactionSummary.getTransactionAddedAt()),
                transactionSummary.getPaymentStatus().name(), transactionSummary.getTransactionMode().name(),
                transactionSummary.getInventoryTransactionStatus().name(),
                transactionSummary.getAdditionalCost(),
                transactionSummary.getAdditionalDiscount(),
                transactionSummary.getUsedWalletAmount(),
                transactionSummary.getWalletCreditAmount(), transactionSummary.getPaidAmount(), null,
                transactionSummary.getMetadata() == null ? null : SharedConstants.GSON.toJson(transactionSummary.getMetadata()),
                transactionSummary.getDescription() == null ? null : transactionSummary.getDescription().trim());

        if (rows != 1) {
            logger.error("Unable to add transaction meta data for payload {}", outletId, transactionSummary);
            throw new EmbrateRunTimeException("Unable to add transaction meta data");
        }

        List<Object[]> transactionDetailsArgList = new ArrayList<>();
        for (TradeProductPayload tradeProductPayload : tradePayloadPayloadList) {
            for (TradeProductBatchPayload tradeProductBatchPayload : tradeProductPayload.getTradeProductBatchPayloadList()) {
                transactionDetailsArgList.add(new Object[]{transactionSummary.getTransactionId().toString(), tradeProductPayload.getSkuId().toString(), tradeProductBatchPayload.getBatchId().toString(),
                        tradeProductBatchPayload.getQuantity(), tradeProductBatchPayload.getTotalPrice(),
                        tradeProductBatchPayload.getTotalDiscount(), tradeProductBatchPayload.getTotalTax()});
            }
        }


        int[] transactionDetailsRows = jdbcTemplate.batchUpdate(ADD_TRANSACTION_DETAILS, transactionDetailsArgList);
        if (transactionDetailsRows.length != transactionDetailsArgList.size()) {
            logger.error("Unable to add transaction details for payload outlet {}, {}", outletId, transactionDetailsArgList);
            throw new EmbrateRunTimeException("Unable to add transaction details");
        }
        return true;
    }
}

