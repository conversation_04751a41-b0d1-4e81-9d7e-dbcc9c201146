package com.embrate.cloud.dao.tier.inventory.v2;

import com.embrate.cloud.core.api.inventory.v2.*;
import com.embrate.cloud.core.api.inventory.v2.issue.ProductIssuePayload;
import com.embrate.cloud.core.api.inventory.v2.product.group.InventoryProductDataQuantity;
import com.embrate.cloud.core.api.inventory.v2.product.group.InventoryProductGroupData;
import com.embrate.cloud.core.api.inventory.v2.product.group.ProductGroupBatchDataQuantity;
import com.embrate.cloud.core.api.inventory.v2.product.group.TradeProductGroupPayload;
import com.embrate.cloud.core.api.inventory.v2.sale.NewTradePayload;
import com.embrate.cloud.core.api.inventory.v2.transaction.InventoryTransactionMetadata;
import com.embrate.cloud.core.api.inventory.v2.transaction.InventoryTransactionSummary;
import com.embrate.cloud.core.api.inventory.v2.transaction.ProductTransactionPayload;
import com.embrate.cloud.core.api.wallet.WalletPreferences;
import com.embrate.cloud.core.api.wallet.WalletTransactionCategory;
import com.embrate.cloud.core.api.wallet.WalletTransactionMetaDataKeys;
import com.embrate.cloud.core.api.wallet.WalletTransactionPayload;
import com.embrate.cloud.dao.tier.inventory.v2.mappers.InventoryTransactionMetadataRowMapper;
import com.embrate.cloud.dao.tier.inventory.v2.mappers.InventoryTransactionSummaryRowMapper;
import com.embrate.cloud.dao.tier.wallet.UserWalletDao;
import com.lernen.cloud.core.api.common.DBLockMode;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.inventory.*;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 */
public class InventoryTransactionsDao {
    private static final Logger logger = LogManager.getLogger(InventoryTransactionsDao.class);

    private static final String ADD_TRANSACTION_METADATA = "insert into inventory_transaction_metadata (outlet_id, transaction_id, invoice_id, " +
            "institute_id, inventory_user_type, transaction_type, reference, email, supplier_id, transaction_to, transaction_by, transaction_date, " +
            "transaction_added_at, payment_status, payment_mode, transaction_status, additional_cost, additional_discount, wallet_debit_amount, "
            + "wallet_based_credit_amount, paid_amount, documents, meta_data, description) "
            + "values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String ADD_TRANSACTION_DETAILS = "insert into inventory_transaction_details (transaction_id, sku_id, batch_id, "
            + "quantity, total_price, total_discount, total_tax)"
            + "values (?, ?, ?, ?, ?, ?, ?)";


    private static final String GET_TRANSACTIONS_META_DATA = "select inventory_transaction_metadata.*, inventory_supplier.*, "
            + "students.*, users.first_name as transaction_by_name from inventory_transaction_metadata join "
            + "users on inventory_transaction_metadata.transaction_by = users.user_id left join inventory_supplier on "
            + "inventory_user_type='SELLER' and inventory_transaction_metadata.supplier_id = inventory_supplier.supplier_id "
            + "left join students on inventory_user_type='STUDENT' and transaction_to = students.student_id "
            + "where inventory_transaction_metadata.outlet_id = ? order by transaction_added_at desc";

    private static final String GET_TRANSACTION_DETAILS = "select inventory_transaction_metadata.*, inventory_transaction_details.*, "
            + "product_metadata.*, product_batch_details.*, inventory_supplier.* , students.*, users.first_name as transaction_by_name from inventory_transaction_metadata "
            + "join inventory_transaction_details on inventory_transaction_metadata.transaction_id = inventory_transaction_details.transaction_id "
            + "join product_metadata on inventory_transaction_details.sku_id = product_metadata.sku_id join product_batch_details on "
            + "inventory_transaction_details.batch_id = product_batch_details.batch_id "
            + "join users on transaction_by = users.user_id "
            + "left join inventory_supplier on inventory_user_type='SELLER' and inventory_transaction_metadata.supplier_id = inventory_supplier.supplier_id "
            + "left join students on inventory_user_type='STUDENT' and transaction_to = students.student_id "
            + "where inventory_transaction_metadata.outlet_id = ? ";

    private static final String TRANSACTION_ID_CLAUSE = " and inventory_transaction_metadata.transaction_id = ? ";
    private static final String INVOICE_ID_CLAUSE = " and inventory_transaction_metadata.invoice_id = ? ";

    private static final String START_DATE_CLAUSE = " and inventory_transaction_metadata.transaction_date >= ? ";

    private static final String END_DATE_CLAUSE = " and inventory_transaction_metadata.transaction_date <= ? ";

    private static final String TRANSACTION_TYPE_CLAUSE = " and inventory_transaction_metadata.transaction_type = ? ";

    private static final String TRANSACTION_STATUS_CLAUSE = " and inventory_transaction_metadata.transaction_status = ? ";

//    private static final String DELETE_TRANSACTION_METADATA = "delete from inventory_transaction_metadata where outlet_id = ? and transaction_id = ?";
//    private static final String DELETE_TRANSACTION_DETAILS = "delete from inventory_transaction_details where transaction_id = ?";

    private static final String UPDATE_TRANSACTION_ACTIVE_STATUS = "update inventory_transaction_metadata set transaction_status = ? where outlet_id = ? and transaction_id = ?";

    private static final String UPDATE_TRANSACTION_METADATA_COL = "update inventory_transaction_metadata set meta_data = ? where outlet_id = ? and transaction_id = ?";

    private static final InventoryTransactionMetadataRowMapper INVENTORY_TRANSACTION_METADATA_ROW_MAPPER = new InventoryTransactionMetadataRowMapper();
    private static final InventoryTransactionSummaryRowMapper INVENTORY_TRANSACTION_SUMMARY_ROW_MAPPER = new InventoryTransactionSummaryRowMapper();

    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;
    private final UserWalletDao userWalletDao;
    private final InventoryProductDao inventoryProductDao;
    private final InventoryOutletDao inventoryOutletDao;

    private final InventoryProductGroupDao inventoryProductGroupDao;

    public InventoryTransactionsDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate,
                                    UserWalletDao userWalletDao, InventoryProductDao inventoryProductDao, InventoryOutletDao inventoryOutletDao,
                                    InventoryProductGroupDao inventoryProductGroupDao) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
        this.userWalletDao = userWalletDao;
        this.inventoryProductDao = inventoryProductDao;
        this.inventoryOutletDao = inventoryOutletDao;
        this.inventoryProductGroupDao = inventoryProductGroupDao;
    }


    public UUID addNewPurchaseTransaction(UUID outletId, UUID userId, NewPurchasePayload newPurchasePayload) {

        try {
            final UUID purchaseTransactionId = transactionTemplate.execute(new TransactionCallback<UUID>() {

                @Override
                public UUID doInTransaction(TransactionStatus status) {
                    Map<UUID, List<TradeProductBatchPayload>> productNewBatchListMap = new HashMap<>();
                    Map<UUID, Map<UUID, ProductBatchPayload>> productBatchUpdateMap = new HashMap<>();
                    List<TradeProductPayload> newProductPayloadList = new ArrayList<>();
                    Set<UUID> inputSkuIds = new HashSet<>();
                    for (TradeProductPayload tradeProductPayload : newPurchasePayload.getPurchasedProductPayloadList()) {
                        UUID skuId = tradeProductPayload.getSkuId();
                        if (skuId == null) {
                            newProductPayloadList.add(tradeProductPayload);
                            continue;
                        }

                        inputSkuIds.add(skuId);
                        List<TradeProductBatchPayload> newBatchList = new ArrayList<>();
                        Map<UUID, ProductBatchPayload> updateBatchMap = new HashMap<>();
                        for (TradeProductBatchPayload purchasedProductBatchPayload : tradeProductPayload.getTradeProductBatchPayloadList()) {
                            if (purchasedProductBatchPayload.getBatchId() == null) {
                                newBatchList.add(purchasedProductBatchPayload);
                            } else {
                                // Currently, only purchase price update is supported
                                double totalPrice = purchasedProductBatchPayload.getTotalPrice();
                                double quantity = purchasedProductBatchPayload.getQuantity();
                                double purchasePrice = 0d;
                                if (quantity > 0) {
                                    purchasePrice = totalPrice / quantity;
                                }
                                updateBatchMap.put(purchasedProductBatchPayload.getBatchId(), new ProductBatchPayload(null, null, null, null, null, null, null, purchasePrice, null, false, true));
                            }
                        }
                        if (CollectionUtils.isNotEmpty(newBatchList)) {
                            productNewBatchListMap.put(skuId, newBatchList);
                        }
                        if (MapUtils.isNotEmpty(updateBatchMap)) {
                            productBatchUpdateMap.put(skuId, updateBatchMap);
                        }
                    }

                    Set<UUID> skuIds = new HashSet<>(inputSkuIds);
                    if (CollectionUtils.isNotEmpty(newProductPayloadList)) {
                        List<UUID> newSkuIds = addNewProducts(outletId, userId, newProductPayloadList);
                        if (CollectionUtils.isEmpty(newSkuIds) || newSkuIds.size() != newProductPayloadList.size()) {
                            logger.error("Failed to add new products {} for outlet {}, user {}, newPurchasePayload {}", productNewBatchListMap, outletId, userId, newPurchasePayload);
                            throw new EmbrateRunTimeException("Unable to add new products in purchase transaction");
                        }
                        skuIds.addAll(newSkuIds);
                    }

                    if (MapUtils.isNotEmpty(productNewBatchListMap) && !addNewBatches(outletId, userId, productNewBatchListMap)) {
                        logger.error("Failed to add new batches {} for outlet {}, user {}, newPurchasePayload {}", productNewBatchListMap, outletId, userId, newPurchasePayload);
                        throw new EmbrateRunTimeException("Unable to add new batches in purchase transaction");
                    }

                    if (MapUtils.isNotEmpty(productBatchUpdateMap) && !updateProductBatchPurchasePrice(outletId, userId, productBatchUpdateMap)) {
                        logger.error("Failed to update existing batches purchase price {} for outlet {}, user {}, newPurchasePayload {}", productNewBatchListMap, outletId, userId, newPurchasePayload);
                        throw new EmbrateRunTimeException("Unable to update purchase price in purchase transaction");
                    }

                    Map<UUID, Map<UUID, ProductBatchData>> skuBatchIdMap = new HashMap<>();
                    Map<UUID, Map<String, ProductBatchData>> skuBatchNameMap = new HashMap<>();
                    Map<String, ProductMetadata> productMetadataMap = new HashMap<>();
                    populateProductDataMap(outletId, skuIds, skuBatchIdMap, skuBatchNameMap, productMetadataMap);


                    List<Object[]> transactionDetailsArgList = new ArrayList<>();
                    Map<UUID, Map<UUID, Double>> productBatchQuantityUpdateMap = new HashMap<>();
                    final UUID purchaseTransactionId = UUID.randomUUID();
                    preparePurchaseTransaction(skuBatchIdMap, skuBatchNameMap, productMetadataMap, transactionDetailsArgList, productBatchQuantityUpdateMap, purchaseTransactionId, newPurchasePayload);

                    Double netPaidAmount = newPurchasePayload.getPaymentAmount();

                    ProductTransactionPayload productTransactionPayload = new ProductTransactionPayload(userId, outletId, purchaseTransactionId,
                            newPurchasePayload.getInventoryUserInstituteId(), InventoryUserType.SELLER, InventoryTransactionType.PURCHASE, newPurchasePayload.getReference(),
                            null, newPurchasePayload.getSupplierId(), null, newPurchasePayload.getTransactionDate(),
                            newPurchasePayload.getPaymentStatus(), newPurchasePayload.getTransactionMode(), newPurchasePayload.getAdditionalCost(),
                            newPurchasePayload.getAdditionalDiscount(), 0d, 0d,
                            netPaidAmount, null, null, newPurchasePayload.getDescription(), transactionDetailsArgList, productBatchQuantityUpdateMap, true);


                    addProductTransaction(productTransactionPayload);

                    return purchaseTransactionId;
                }
            });
            return purchaseTransactionId;
        } catch (final Exception e) {
            logger.error("Unable to execute new purchase transaction for outlet {}, user {}, payload {}",
                    outletId, userId, newPurchasePayload, e);
        }
        return null;
    }

    private void preparePurchaseTransaction(Map<UUID, Map<UUID, ProductBatchData>> skuBatchIdMap,
                                            Map<UUID, Map<String, ProductBatchData>> skuBatchNameMap,
                                            Map<String, ProductMetadata> productMetadataMap,
                                            List<Object[]> transactionDetailsArgList,
                                            Map<UUID, Map<UUID, Double>> productBatchQuantityUpdateMap,
                                            UUID purchaseTransactionId, NewPurchasePayload newPurchasePayload) {
        for (TradeProductPayload tradeProductPayload : newPurchasePayload.getPurchasedProductPayloadList()) {
            UUID skuId = tradeProductPayload.getSkuId();
            if (skuId == null) {
                String productName = tradeProductPayload.getProductMetadataPayload().getName().trim().toLowerCase();
                if (!productMetadataMap.containsKey(productName)) {
                    logger.error("Product {} in purchase transaction does not exists", productName);
                    throw new EmbrateRunTimeException("Product in purchase transaction does not exists");
                }
                skuId = productMetadataMap.get(productName).getSkuId();
            } else {
                if (!skuBatchIdMap.containsKey(skuId)) {
                    logger.error("Product {} in purchase transaction does not exists", skuId);
                    throw new EmbrateRunTimeException("Product in purchase transaction does not exists");
                }
            }

            Map<UUID, ProductBatchData> batchIdMap = skuBatchIdMap.get(skuId);
            Map<String, ProductBatchData> batchNameMap = skuBatchNameMap.get(skuId);

            if (!productBatchQuantityUpdateMap.containsKey(skuId)) {
                productBatchQuantityUpdateMap.put(skuId, new HashMap<>());
            }
            for (TradeProductBatchPayload purchasedProductBatchPayload : tradeProductPayload.getTradeProductBatchPayloadList()) {
                UUID purchasedBatchId = null;
                if (purchasedProductBatchPayload.getBatchId() == null) {
                    String batchName = purchasedProductBatchPayload.getProductBatchPayload().getBatchName().trim().toLowerCase();
                    if (!batchNameMap.containsKey(batchName)) {
                        logger.error("Product {} batch {} in purchase transaction does not exists", skuId, batchName);
                        throw new EmbrateRunTimeException("Product batch in purchase transaction does not exists");
                    }
                    purchasedBatchId = batchNameMap.get(batchName).getBatchId();
                } else {
                    UUID batchId = purchasedProductBatchPayload.getBatchId();
                    if (!batchIdMap.containsKey(batchId)) {
                        logger.error("Product {} batch {} in purchase transaction does not exists", skuId, batchId);
                        throw new EmbrateRunTimeException("Product batch in purchase transaction does not exists");
                    }
                    purchasedBatchId = batchId;
                }

                if (Double.compare(purchasedProductBatchPayload.getQuantity(), 0d) <= 0) {
                    logger.error("Invalid quantity for product {} in purchase transaction", skuId, purchasedProductBatchPayload);
                    throw new EmbrateRunTimeException("Invalid quantity for product in purchase transaction");
                }

                productBatchQuantityUpdateMap.get(skuId).put(purchasedBatchId, purchasedProductBatchPayload.getQuantity());
                transactionDetailsArgList.add(new Object[]{purchaseTransactionId.toString(), skuId.toString(), purchasedBatchId.toString(),
                        purchasedProductBatchPayload.getQuantity(), purchasedProductBatchPayload.getTotalPrice(),
                        purchasedProductBatchPayload.getTotalDiscount(), purchasedProductBatchPayload.getTotalTax()});
            }
        }
    }


    private List<TradeProductPayload> mergePurchasedProducts(List<TradeProductPayload> purchasedProductsInGroup,
                                                             List<TradeProductPayload> individualPurchasedProducts) {
        final Map<UUID, Map<UUID, TradeProductBatchPayload>> mergedPurchasedProducts = new HashMap<>();
        final List<TradeProductPayload> allPurchasedProducts = new ArrayList<>();
        allPurchasedProducts.addAll(individualPurchasedProducts);
        allPurchasedProducts.addAll(purchasedProductsInGroup);
        for (final TradeProductPayload purchasedProduct : allPurchasedProducts) {
            UUID skuId = purchasedProduct.getSkuId();
            if (!mergedPurchasedProducts.containsKey(skuId)) {
                mergedPurchasedProducts.put(skuId, new HashMap<>());
            }
            Map<UUID, TradeProductBatchPayload> batchMap = mergedPurchasedProducts.get(skuId);
            for (TradeProductBatchPayload tradeProductBatchPayload : purchasedProduct.getTradeProductBatchPayloadList()) {
                UUID batchId = tradeProductBatchPayload.getBatchId();
                if (!batchMap.containsKey(batchId)) {
                    batchMap.put(batchId, tradeProductBatchPayload);
                } else {
                    TradeProductBatchPayload existingBatchPayload = batchMap.get(batchId);
                    existingBatchPayload.setQuantity(existingBatchPayload.getQuantity() + tradeProductBatchPayload.getQuantity());
                    existingBatchPayload.setTotalDiscount(existingBatchPayload.getTotalDiscount() + tradeProductBatchPayload.getTotalDiscount());
                    existingBatchPayload.setTotalTax(existingBatchPayload.getTotalTax() + tradeProductBatchPayload.getTotalTax());
                    existingBatchPayload.setTotalPrice(existingBatchPayload.getTotalPrice() + tradeProductBatchPayload.getTotalPrice());
                }
            }
        }

        List<TradeProductPayload> finalMergePayload = new ArrayList<>();
        for (Map.Entry<UUID, Map<UUID, TradeProductBatchPayload>> skuEntry : mergedPurchasedProducts.entrySet()) {
            finalMergePayload.add(new TradeProductPayload(skuEntry.getKey(), new ArrayList<>(skuEntry.getValue().values())));
        }

        return finalMergePayload;
    }

    private List<TradeProductPayload> getIndividualProductsFromGroup(UUID outletId, NewTradePayload newTradePayload) {
        final List<TradeProductPayload> purchasedProductsInGroup = new ArrayList<>();
        if (CollectionUtils.isEmpty(newTradePayload.getSaleProductGroupPayloadList())) {
            return purchasedProductsInGroup;
        }
        final Set<UUID> groupIds = new HashSet<>(newTradePayload.getSaleProductGroupPayloadList().size());
        final Map<UUID, TradeProductGroupPayload> purchasedProductGroupMap = new HashMap<>();
        for (final TradeProductGroupPayload purchasedProductGroup : newTradePayload.getSaleProductGroupPayloadList()) {
            groupIds.add(purchasedProductGroup.getGroupId());
            purchasedProductGroupMap.put(purchasedProductGroup.getGroupId(), purchasedProductGroup);
        }

        final List<InventoryProductGroupData> productGroupDataList = inventoryProductGroupDao
                .getProductGroupData(outletId, groupIds);
        for (final InventoryProductGroupData productGroupData : productGroupDataList) {
            if (!purchasedProductGroupMap.containsKey(productGroupData.getProductGroupBasicInfo().getGroupId())) {
                continue;
            }

            final TradeProductGroupPayload purchasedProductGroup = purchasedProductGroupMap
                    .get(productGroupData.getProductGroupBasicInfo().getGroupId());

            final double groupQuantity = purchasedProductGroup.getQuantity();
            final double groupDiscountInPercent = purchasedProductGroup.getTotalDiscount() / purchasedProductGroup.getTotalPrice();
//            final double totalGroupSellingPrice = groupQuantity * productGroupData.getSellingPrice();
//            final double totalGroupMRP = groupQuantity * productGroupData.getMrp();

            for (final InventoryProductDataQuantity productDataQuantity : productGroupData.getProductDataQuantityList()) {
                List<TradeProductBatchPayload> tradeProductBatchPayloadList = new ArrayList<>();

                for (ProductGroupBatchDataQuantity productGroupBatchDataQuantity : productDataQuantity.getProductGroupBatchDataQuantities()) {

                    final double totalProductQuantity = groupQuantity * productGroupBatchDataQuantity.getQuantity();
                    final double totalProductPrice = groupQuantity * productGroupBatchDataQuantity.getTotalSellingPrice();
                    // Total group level discount can be changed from UI so taking value from there
                    final double totalDiscountAmount = totalProductPrice * productGroupBatchDataQuantity.getDiscount() / 100d;
                    final double additionalDiscount = (totalProductPrice - totalDiscountAmount) * groupDiscountInPercent;
                    final double totalDiscount = totalDiscountAmount + additionalDiscount;

                    tradeProductBatchPayloadList.add(new TradeProductBatchPayload(productGroupBatchDataQuantity.getProductBatchData().getBatchId(), null, totalProductQuantity, totalProductPrice, totalDiscount, 0));

//                    final double totalMRP = groupQuantity * productDataQuantity.getMRPTotal();
//                    final double itemLevelDiscount = totalMRP - totalProductPrice;
//                    final double discountedPriceRatio = totalGroupSellingPrice <= 0 ? 0
//                            : totalProductPrice / totalGroupSellingPrice;
//                    final double mrpRatio = totalGroupMRP <= 0 ? 0 : totalMRP / totalGroupMRP;
                }

                purchasedProductsInGroup.add(new TradeProductPayload(productDataQuantity.getProductMetadata().getSkuId(), tradeProductBatchPayloadList));

                // Discount is applied on top of discounted amount of group.
                // Tax is applied on MRP
//                purchasedProductsInGroup.add(new PurchasedProduct(productDetails.getSkuId(),
//                        productDetails.getProductName(), totalProductQuantity, totalMRP,
//                        itemLevelDiscount + purchasedProductGroup.getTotalDiscount() * discountedPriceRatio,
//                        purchasedProductGroup.getTotalTax() * mrpRatio));
            }

        }
        return purchasedProductsInGroup;
    }

    public UUID addNewTradeTransaction(UUID outletId, UUID userId, NewTradePayload newTradePayload, InventoryPreferences inventoryPreferences, WalletPreferences walletPreferences) {

        try {
            final UUID tradeTransactionId = transactionTemplate.execute(new TransactionCallback<UUID>() {

                @Override
                public UUID doInTransaction(TransactionStatus status) {

                    if(CollectionUtils.isNotEmpty(newTradePayload.getSaleProductGroupPayloadList())){
                        final List<TradeProductPayload> purchasedProductsInGroup = getIndividualProductsFromGroup(outletId, newTradePayload);
                        final List<TradeProductPayload> allPurchasedProducts = mergePurchasedProducts(purchasedProductsInGroup,
                                newTradePayload.getSaleProductPayloadList());
                        // Merging all the group items into individual entries
                        newTradePayload.setSaleProductGroupPayloadList(new ArrayList<>());
                        newTradePayload.setSaleProductPayloadList(allPurchasedProducts);
                    }

                    List<Object[]> transactionDetailsArgList = new ArrayList<>();
                    Map<UUID, Map<UUID, Double>> productBatchQuantityUpdateMap = new HashMap<>();
                    final UUID tradeTransactionId = UUID.randomUUID();
//                    Integer transactionInstituteId = newTradePayload.getInventoryUserType() == InventoryUserType.STUDENT ?
//                            newTradePayload.getInventoryUserInstituteId() : null;
                    Integer transactionInstituteId = newTradePayload.getInventoryUserInstituteId();

                    TransactionMode transactionMode = newTradePayload.getTransactionMode();
                    UUID supplierId = null;
                    String transactionTo = null;
                    Boolean quantityIncrement = null;
                    switch (newTradePayload.getInventoryTransactionType()) {
                        case SALE:
                            transactionTo = newTradePayload.getPurchasedBy();
                            quantityIncrement = false;
                            validatePaymentAmount(newTradePayload, inventoryPreferences, outletId, walletPreferences);
                            prepareTransactionPayload(outletId, transactionDetailsArgList, productBatchQuantityUpdateMap, tradeTransactionId, newTradePayload.getSaleProductPayloadList(), true);
                            addWalletTransactionsForUser(tradeTransactionId, newTradePayload, userId);
                            // If wallet is used and no payment is made, then force updating the mode
                            if (newTradePayload.isUseWallet() &&
                                    (newTradePayload.getPaidAmount() == null || Double.compare(newTradePayload.getPaidAmount(), 0d) == 0)) {
                                transactionMode = TransactionMode.WALLET;
                            }
                            break;
                        case RETURN:
                            supplierId = UUID.fromString(newTradePayload.getPurchasedBy());
                            quantityIncrement = false;
                            prepareTransactionPayload(outletId, transactionDetailsArgList, productBatchQuantityUpdateMap, tradeTransactionId, newTradePayload.getSaleProductPayloadList(), false);
                            break;
                        case SALES_RETURN:
                            transactionTo = newTradePayload.getPurchasedBy();
                            quantityIncrement = true;
                            prepareTransactionPayload(outletId, transactionDetailsArgList, productBatchQuantityUpdateMap, tradeTransactionId, newTradePayload.getSaleProductPayloadList(), false);
                            addWalletRefundTransactionForUser(tradeTransactionId, newTradePayload, userId);
                            break;
                    }

                    if (quantityIncrement == null) {
                        logger.error("Transaction not handled properly {}", newTradePayload);
                        throw new EmbrateRunTimeException("Unable to add transaction");
                    }
                    ProductTransactionPayload productTransactionPayload = new ProductTransactionPayload(userId, outletId, tradeTransactionId,
                            transactionInstituteId, newTradePayload.getInventoryUserType(), newTradePayload.getInventoryTransactionType(), newTradePayload.getReference(),
                            newTradePayload.getEmail(), supplierId, transactionTo, newTradePayload.getTransactionDate(),
                            newTradePayload.getPaymentStatus(), transactionMode, newTradePayload.getAdditionalCost(),
                            newTradePayload.getAdditionalDiscount(), newTradePayload.getUsedWalletAmount(), newTradePayload.getWalletCreditAmount(),
                            newTradePayload.getPaidAmount(), null, null, newTradePayload.getDescription(), transactionDetailsArgList, productBatchQuantityUpdateMap, quantityIncrement);


                    addProductTransaction(productTransactionPayload);

                    return tradeTransactionId;
                }
            });
            return tradeTransactionId;
        } catch (final Exception e) {
            logger.error("Unable to execute new trade transaction for outlet {}, user {}, payload {}",
                    outletId, userId, newTradePayload, e);
        }
        return null;
    }

    public UUID addProductIssueTransaction(UUID outletId, UUID userId, ProductIssuePayload productIssuePayload) {

        try {
            final UUID issueTransactionId = transactionTemplate.execute(new TransactionCallback<UUID>() {

                @Override
                public UUID doInTransaction(TransactionStatus status) {

                    List<Object[]> transactionDetailsArgList = new ArrayList<>();
                    Map<UUID, Map<UUID, Double>> productBatchQuantityUpdateMap = new HashMap<>();
                    final UUID issueTransactionId = UUID.randomUUID();
                    prepareTransactionPayload(outletId, transactionDetailsArgList, productBatchQuantityUpdateMap, issueTransactionId, productIssuePayload.getIssueProductPayloadList(), true);

                    ProductTransactionPayload productTransactionPayload = new ProductTransactionPayload(userId, outletId, issueTransactionId, productIssuePayload.getInventoryUserInstituteId(),
                            productIssuePayload.getInventoryUserType(), InventoryTransactionType.ISSUE, productIssuePayload.getReference(),
                            productIssuePayload.getEmail(), null, productIssuePayload.getIssuedTo(), productIssuePayload.getTransactionDate(),
                            PaymentStatus.UNPAID, TransactionMode.NONE, 0d,
                            0d, 0d, 0d, 0d, null, null, productIssuePayload.getDescription(),
                            transactionDetailsArgList, productBatchQuantityUpdateMap, false);


                    addProductTransaction(productTransactionPayload);

                    return issueTransactionId;
                }
            });
            return issueTransactionId;
        } catch (final Exception e) {
            logger.error("Unable to execute new issue transaction for outlet {}, user {}, payload {}",
                    outletId, userId, productIssuePayload, e);
        }
        return null;
    }

    private void addProductTransaction(ProductTransactionPayload productTransactionPayload) {
        UUID outletId = productTransactionPayload.getOutletId();
        InventoryCounterType counterType = productTransactionPayload.getTransactionType().getCounterType();
        final InventoryCounterData invoiceCounterData = inventoryOutletDao.getCounter(productTransactionPayload.getOutletId(),
                counterType, true);
        if (invoiceCounterData == null) {
            logger.error("{} counter is not present for outlet {}", counterType.name(), outletId);
            throw new EmbrateRunTimeException("Counter not setup for outlet");
        }

        int rows = jdbcTemplate.update(ADD_TRANSACTION_METADATA, productTransactionPayload.getOutletId().toString(),
                productTransactionPayload.getTransactionId().toString(), invoiceCounterData.getFullCounterValue(), productTransactionPayload.getInventoryUserInstituteId(),
                productTransactionPayload.getInventoryUserType().name(), productTransactionPayload.getTransactionType().name(),
                productTransactionPayload.getReference() == null ? null : productTransactionPayload.getReference().trim(),
                productTransactionPayload.getEmail() == null ? null : CryptoUtils.encrypt(productTransactionPayload.getEmail().trim()),
                productTransactionPayload.getSupplierId() == null ? null : productTransactionPayload.getSupplierId().toString(),
                productTransactionPayload.getTransactionTo(), productTransactionPayload.getUserId().toString(),
                new Timestamp(productTransactionPayload.getTransactionDate()), new Timestamp(System.currentTimeMillis()),
                productTransactionPayload.getPaymentStatus().name(), productTransactionPayload.getTransactionMode().name(),
                InventoryTransactionStatus.ACTIVE.name(),
                productTransactionPayload.getAdditionalCost() == null ? 0d : productTransactionPayload.getAdditionalCost(),
                productTransactionPayload.getAdditionalDiscount() == null ? 0d : productTransactionPayload.getAdditionalDiscount(),
                productTransactionPayload.getUsedWalletAmount(),
                productTransactionPayload.getWalletCreditAmount(), productTransactionPayload.getPaidAmount(), productTransactionPayload.getDocuments(),
                productTransactionPayload.getMetaData(),
                productTransactionPayload.getDescription() == null ? null : productTransactionPayload.getDescription().trim());

        if (rows != 1) {
            logger.error("Unable to add transaction meta data for payload {}", productTransactionPayload.getOutletId(), productTransactionPayload);
            throw new EmbrateRunTimeException("Unable to add transaction meta data");
        }

        if (!inventoryOutletDao.incrementCounter(outletId, counterType)) {
            logger.error("Unable to increment counter for outlet {}, counter {}", outletId, counterType);
            throw new EmbrateRunTimeException("Unable to increment invoice counter");
        }

        int[] transactionDetailsRows = jdbcTemplate.batchUpdate(ADD_TRANSACTION_DETAILS, productTransactionPayload.getTransactionDetailsArgList());
        if (transactionDetailsRows.length != productTransactionPayload.getTransactionDetailsArgList().size()) {
            logger.error("Unable to add transaction details for payload outlet {}, {}", productTransactionPayload.getOutletId(), productTransactionPayload.getTransactionDetailsArgList());
            throw new EmbrateRunTimeException("Unable to add transaction details");
        }

        if (!inventoryProductDao.updateProductBatchesQuantityWithTransaction(productTransactionPayload.getOutletId(), productTransactionPayload.getProductBatchQuantityUpdateMap(), productTransactionPayload.isQuantityIncrement())) {
            logger.error("Unable to update transaction quantities for outlet {} payload {}", productTransactionPayload.getOutletId(), productTransactionPayload.getProductBatchQuantityUpdateMap());
            throw new EmbrateRunTimeException("Unable to update transaction quantities");
        }
    }

    private void prepareTransactionPayload(UUID outletId, List<Object[]> transactionDetailsArgList, Map<UUID, Map<UUID, Double>> productBatchQuantityUpdateMap, UUID transactionId, List<TradeProductPayload> tradeProductPayloadList, boolean validateProductDecrement) {
        Set<UUID> skuIds = new HashSet<>();
        for (TradeProductPayload tradeProductPayload : tradeProductPayloadList) {
            UUID skuId = tradeProductPayload.getSkuId();
            skuIds.add(skuId);
        }

        Map<UUID, Map<UUID, ProductBatchData>> skuBatchIdMap = new HashMap<>();
        Map<UUID, Map<String, ProductBatchData>> skuBatchNameMap = new HashMap<>();
        Map<String, ProductMetadata> productMetadataMap = new HashMap<>();
        populateProductDataMap(outletId, skuIds, skuBatchIdMap, skuBatchNameMap, productMetadataMap);

        for (TradeProductPayload tradeProductPayload : tradeProductPayloadList) {
            UUID skuId = tradeProductPayload.getSkuId();
            if (!skuBatchIdMap.containsKey(skuId)) {
                logger.error("Product {} in sale transaction does not exists", skuId);
                throw new EmbrateRunTimeException("Product in sale transaction does not exists");
            }
            Map<UUID, ProductBatchData> batchIdMap = skuBatchIdMap.get(skuId);

            if (!productBatchQuantityUpdateMap.containsKey(skuId)) {
                productBatchQuantityUpdateMap.put(skuId, new HashMap<>());
            }
            for (TradeProductBatchPayload tradeProductBatchPayload : tradeProductPayload.getTradeProductBatchPayloadList()) {
                UUID batchId = tradeProductBatchPayload.getBatchId();
                if (!batchIdMap.containsKey(batchId)) {
                    logger.error("Product {} batch {} in sale transaction does not exists", skuId, batchId);
                    throw new EmbrateRunTimeException("Product batch in sale transaction does not exists");
                }

                ProductBatchData productBatchData = batchIdMap.get(batchId);

                if (Double.compare(tradeProductBatchPayload.getQuantity(), 0d) <= 0) {
                    logger.error("Invalid quantity for product {} in sale transaction", skuId, tradeProductBatchPayload);
                    throw new EmbrateRunTimeException("Invalid quantity for product in sale transaction");
                }

                if (validateProductDecrement && Double.compare(productBatchData.getTotalQuantity() - tradeProductBatchPayload.getQuantity(), 0d) < 0) {
                    logger.error("More than available quantity {} sale for product {}", productBatchData.getTotalQuantity(), skuId, tradeProductBatchPayload);
                    throw new EmbrateRunTimeException("More than available quantity for product in sale");
                }


                productBatchQuantityUpdateMap.get(skuId).put(batchId, tradeProductBatchPayload.getQuantity());
                transactionDetailsArgList.add(new Object[]{transactionId.toString(), skuId.toString(), batchId.toString(),
                        tradeProductBatchPayload.getQuantity(), tradeProductBatchPayload.getTotalPrice(),
                        tradeProductBatchPayload.getTotalDiscount(), tradeProductBatchPayload.getTotalTax()});
            }
        }
    }

    private void addWalletTransactionsForUser(UUID saleTransactionId, NewTradePayload newTradePayload, UUID userId) {
        Map<String, Object> metaData = new HashMap<String, Object>();
        metaData.put(WalletTransactionMetaDataKeys.INVENTORY_TRANSACTION_ID, saleTransactionId);
        int transactionDate = (int) (newTradePayload.getTransactionDate()/1000L);
        if (newTradePayload.isUseWallet() && newTradePayload.getInventoryUserType() == InventoryUserType.STUDENT) {
            if (newTradePayload.getUsedWalletAmount() != null && Double.compare(newTradePayload.getUsedWalletAmount(), 0d) > 0) {
                userWalletDao.addUserWalletTransactionNonAtomic(new WalletTransactionPayload(
                        newTradePayload.getInventoryUserInstituteId(), UUID.fromString(newTradePayload.getPurchasedBy()), UserType.STUDENT,
                        WalletTransactionCategory.INVENTORY_PAYMENT, TransactionMode.WALLET, transactionDate, FeePaymentTransactionStatus.ACTIVE, -newTradePayload.getUsedWalletAmount(),
                        userId, metaData, "Paid for product purchase"));
            }

            if (newTradePayload.getWalletCreditAmount() != null
                    && Double.compare(newTradePayload.getWalletCreditAmount(), 0d) > 0) {
                userWalletDao.addUserWalletTransactionNonAtomic(new WalletTransactionPayload(
                        newTradePayload.getInventoryUserInstituteId(), UUID.fromString(newTradePayload.getPurchasedBy()), UserType.STUDENT,
                        WalletTransactionCategory.INVENTORY_CREDIT, TransactionMode.WALLET, transactionDate, FeePaymentTransactionStatus.ACTIVE, -newTradePayload.getWalletCreditAmount(),
                        userId, metaData, "Credit taken for product purchase"));
            }
        }
    }

    private void addWalletRefundTransactionForUser(UUID saleTransactionId, NewTradePayload newTradePayload, UUID userId) {
        if (newTradePayload.getTransactionMode() != TransactionMode.WALLET) {
            return;
        }
        if (newTradePayload.getInventoryUserType() != InventoryUserType.STUDENT) {
            logger.error("Invalid transaction mode = wallet for user {}, payload {}", newTradePayload.getInventoryUserType(), newTradePayload);
            throw new EmbrateRunTimeException("Invalid transaction mode");
        }

        int transactionDate = (int) (newTradePayload.getTransactionDate()/1000L);
        Map<String, Object> metaData = new HashMap<String, Object>();
        metaData.put(WalletTransactionMetaDataKeys.INVENTORY_TRANSACTION_ID, saleTransactionId);
        userWalletDao.addUserWalletTransactionNonAtomic(new WalletTransactionPayload(
                newTradePayload.getInventoryUserInstituteId(), UUID.fromString(newTradePayload.getPurchasedBy()), UserType.STUDENT,
                WalletTransactionCategory.INVENTORY_SALE_RETURN_REFUND, null, transactionDate, FeePaymentTransactionStatus.ACTIVE, newTradePayload.getNetPayableAmount(),
                userId, metaData, "Refund for product return"));
    }

    private void validatePaymentAmount(NewTradePayload newTradePayload, InventoryPreferences inventoryPreferences, UUID outletId, WalletPreferences walletPreferences) {
        if (newTradePayload.getInventoryUserType() == InventoryUserType.STUDENT && newTradePayload.isUseWallet()) {
            final Double walletAmount = userWalletDao
                    .getUserWalletAmount(UUID.fromString(newTradePayload.getPurchasedBy()), DBLockMode.FOR_UPDATE);
            if (walletAmount == null) {
                logger.error("Error getting wallet amount for student {}", newTradePayload.getPurchasedBy());
                throw new EmbrateRunTimeException("Error while getting wallet amount for student");
            }

            validateWalletUsage(newTradePayload, inventoryPreferences, walletAmount.doubleValue(), walletPreferences);

        } else if (!newTradePayload.isUseWallet()) {

            final double netPayableAmount = newTradePayload.getNetPayableAmount();

            final double paidAmount = newTradePayload.getPaidAmount() == null ? 0d
                    : newTradePayload.getPaidAmount().doubleValue();

            validateAmountPaid(netPayableAmount, paidAmount, 0d, 0d, newTradePayload.getPurchasedBy());
        } else {
            logger.error("Wallet usage not supported for user, outletId {}, newSalePayload {}", outletId, newTradePayload);
            throw new EmbrateRunTimeException("Wallet usage not supported for user");
        }
    }

    private void validateWalletUsage(NewTradePayload newTradePayload, final InventoryPreferences inventoryPreferences,
                                     double walletAmount, WalletPreferences walletPreferences) {

        if (Double.compare(newTradePayload.getUsedWalletAmount(), 0d) > 0
                && Double.compare(walletAmount, newTradePayload.getUsedWalletAmount()) < 0) {
            logger.error("Insufficient wallet amount {}, transaction wallet amount {}, for user {}", walletAmount,
                    newTradePayload.getUsedWalletAmount(), newTradePayload.getPurchasedBy());
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Insufficient wallet amount for user"));
        }

        if (newTradePayload.getWalletCreditAmount() != null && Double.compare(newTradePayload.getWalletCreditAmount(), 0d) > 0) {
            if (!inventoryPreferences.isAllowWalletBasedCredit()) {
                logger.error("Credit on wallet for inventory is not enabled for institute {} ",
                        newTradePayload.getInventoryUserInstituteId());
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
                        "Credit on wallet for inventory is not enabled for institute"));
            }

            final Double creditLimit = walletPreferences.getWalletBasedCreditLimit();

            if (creditLimit == null || Double.compare(creditLimit, 0d) < 0) {
                logger.error("Invalid credit limit on wallet for inventory, institute {} ",
                        newTradePayload.getInventoryUserInstituteId());
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
                        "Wallet credit limit not set for inventory in institute"));
            }

            if (Double.compare(walletAmount - newTradePayload.getUsedWalletAmount().doubleValue(), 0d) > 0) {
                logger.error("Wallet will not be empty. Invalid credit requirement, user {} ",
                        newTradePayload.getPurchasedBy());
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
                        "Invalid credit requirement as amount is available in wallet."));
            }

            if (Double.compare(-creditLimit, walletAmount - newTradePayload.getUsedWalletAmount().doubleValue()
                    - newTradePayload.getWalletCreditAmount()) > 0) {
                logger.error("Max credit value {}. Cannot give credit of {} in this transaction, user {} ", creditLimit,
                        newTradePayload.getWalletCreditAmount(), newTradePayload.getPurchasedBy());
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
                        "Exceeding max credit value " + creditLimit + " in this transaction."));
            }
        }

        final double netPayableAmount = newTradePayload.getNetPayableAmount();

        final double paidAmount = newTradePayload.getPaidAmount() == null ? 0d : newTradePayload.getPaidAmount().doubleValue();

        final double usedWalletAmount = newTradePayload.getUsedWalletAmount().doubleValue();

        final double creditAmount = newTradePayload.getWalletCreditAmount() == null ? 0d
                : newTradePayload.getWalletCreditAmount().doubleValue();

        validateAmountPaid(netPayableAmount, paidAmount, usedWalletAmount, creditAmount, newTradePayload.getPurchasedBy());

    }

    private void validateAmountPaid(double netPayableAmount, double paidAmount, double usedWalletAmount,
                                    double creditAmount, String purchasedBy) {

        final double netPaidAmount = paidAmount + usedWalletAmount + creditAmount;
        final double roundOfNetPayableAmount = Math.round(netPayableAmount * 100.0) / 100.0;
        if (Double.compare(netPaidAmount, roundOfNetPayableAmount) != 0) {
            logger.error("Invalid total paid amount {}, netPayableAmount {} , user {} ", netPaidAmount, netPayableAmount,
                    purchasedBy);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
                    "Invalid total paid amount " + netPaidAmount + ". Expected amount is " + netPayableAmount));
        }
    }

    private void populateProductDataMap(UUID outletId, Set<UUID> skuIds, Map<UUID, Map<UUID, ProductBatchData>> skuBatchIdMap,
                                        Map<UUID, Map<String, ProductBatchData>> skuBatchNameMap,
                                        Map<String, ProductMetadata> productMetadataMap) {
        List<ProductDetailsV2> productDetailsList = inventoryProductDao.getProductDetails(outletId, skuIds);
        if (CollectionUtils.isEmpty(productDetailsList)) {
            logger.error("No valid product found for outlet {}, skuIds {}", outletId, skuIds);
            throw new EmbrateRunTimeException("No valid product found in purchase transaction");
        }

        for (ProductDetailsV2 productDetailsV2 : productDetailsList) {
            UUID skuId = productDetailsV2.getProductMetadata().getSkuId();
            String productName = productDetailsV2.getProductMetadata().getName().trim().toLowerCase();
            Map<UUID, ProductBatchData> batchIdMap = new HashMap<>();
            Map<String, ProductBatchData> batchNameMap = new HashMap<>();
            for (ProductBatchData productBatchData : productDetailsV2.getProductBatchDataList()) {
                UUID batchId = productBatchData.getBatchId();
                String batchName = productBatchData.getBatchName().trim().toLowerCase();

                batchIdMap.put(batchId, productBatchData);
                batchNameMap.put(batchName, productBatchData);
            }
            skuBatchIdMap.put(skuId, batchIdMap);
            skuBatchNameMap.put(skuId, batchNameMap);
            productMetadataMap.put(productName, productDetailsV2.getProductMetadata());
        }
    }

    private List<UUID> addNewProducts(UUID outletId, UUID userId, List<TradeProductPayload> newProductList) {
        List<ProductPayload> productPayloadList = new ArrayList<>();
        for (TradeProductPayload tradeProductPayload : newProductList) {
            List<ProductBatchPayload> productBatchPayloadList = new ArrayList<>();
            for (TradeProductBatchPayload tradeProductBatchPayload : tradeProductPayload.getTradeProductBatchPayloadList()) {
                productBatchPayloadList.add(tradeProductBatchPayload.getProductBatchPayload());
            }
            productPayloadList.add(new ProductPayload(tradeProductPayload.getProductMetadataPayload(), productBatchPayloadList));
        }

        return inventoryProductDao.addProductsWithoutTransaction(new NewProductRequestV2(productPayloadList), outletId, userId);
    }

    private boolean addNewBatches(UUID outletId, UUID userId, Map<UUID, List<TradeProductBatchPayload>> productNewBatchListMap) {
        Map<UUID, List<ProductBatchPayload>> productBatchPayloadMap = new HashMap<>();
        for (Map.Entry<UUID, List<TradeProductBatchPayload>> entry : productNewBatchListMap.entrySet()) {
            UUID skuId = entry.getKey();
            List<TradeProductBatchPayload> newBatchList = entry.getValue();
            List<ProductBatchPayload> productBatchPayloadList = new ArrayList<>();
            for (TradeProductBatchPayload tradeProductBatchPayload : newBatchList) {
                productBatchPayloadList.add(tradeProductBatchPayload.getProductBatchPayload());
            }
            productBatchPayloadMap.put(skuId, productBatchPayloadList);
        }

        return inventoryProductDao.addProductBatches(outletId, userId, productBatchPayloadMap);
    }

    private boolean updateProductBatchPurchasePrice(UUID outletId, UUID userId, Map<UUID, Map<UUID, ProductBatchPayload>> productBatchUpdateMap) {
        return inventoryProductDao.updateProductBatchPurchasePrice(outletId, userId, productBatchUpdateMap);
    }

    public List<InventoryTransactionMetadata> getTransactionsMetaData(UUID outletId) {
        try {
            final Object[] args = {outletId.toString()};
            return jdbcTemplate.query(GET_TRANSACTIONS_META_DATA, args, INVENTORY_TRANSACTION_METADATA_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error while getting transaction meta data {}", outletId, e);
        }
        return null;
    }

    public InventoryTransactionSummary getTransactionDetails(UUID outletId, UUID transactionId) {
        try {
            final Object[] args = {outletId.toString(), transactionId.toString()};
            return InventoryTransactionSummaryRowMapper.getTransactionSummary(
                    jdbcTemplate.query(GET_TRANSACTION_DETAILS + TRANSACTION_ID_CLAUSE,
                            args, INVENTORY_TRANSACTION_SUMMARY_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while getting transaction details outletId {}, {}", outletId, transactionId, e);
        }
        return null;
    }

    public InventoryTransactionSummary getTransactionDetailsByInvoiceId(UUID outletId, String invoiceId) {
        try {
            final Object[] args = {outletId.toString(), invoiceId.trim()};
            return InventoryTransactionSummaryRowMapper.getTransactionSummary(
                    jdbcTemplate.query(GET_TRANSACTION_DETAILS + INVOICE_ID_CLAUSE,
                            args, INVENTORY_TRANSACTION_SUMMARY_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while getting transaction details outletId {}, {}", outletId, invoiceId, e);
        }
        return null;
    }

    public List<InventoryTransactionSummary> getTransactionDetailsList(UUID outletId, Integer startDate, Integer endDate,
                                                                       InventoryTransactionType inventoryTransactionType) {
        try {
            List<Object> args = new ArrayList<>();
            args.add(outletId.toString());
            String query = GET_TRANSACTION_DETAILS;
            if (startDate != null && startDate > 0) {
                args.add(new Timestamp(startDate * 1000l));
                query += START_DATE_CLAUSE;
            }
            if (endDate != null && endDate > 0) {
                args.add(new Timestamp(endDate * 1000l));
                query += END_DATE_CLAUSE;
            }
            if (inventoryTransactionType != null) {
                args.add(inventoryTransactionType.name());
                query += TRANSACTION_TYPE_CLAUSE;
            }
            query += TRANSACTION_STATUS_CLAUSE;
            args.add(InventoryTransactionStatus.ACTIVE.name());
            return InventoryTransactionSummaryRowMapper.getTransactionSummaryList(
                    jdbcTemplate.query(query, args.toArray(), INVENTORY_TRANSACTION_SUMMARY_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while getting transaction details for outlet {}", outletId, e);
        }
        return null;
    }


    public boolean cancelTransaction(UUID outletId, UUID userId, UUID transactionId) {
        try {
            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {

                @Override
                public Boolean doInTransaction(TransactionStatus status) {

                    InventoryTransactionSummary transactionSummary = getTransactionDetails(outletId, transactionId);
                    if (transactionSummary == null) {
                        logger.error("Invalid transaction id {} for outlet {}", transactionId, outletId);
                        return false;
                    }

                    InventoryTransactionType transactionType = transactionSummary.getTransactionType();
                    boolean quantityIncrement = true;
                    switch (transactionType) {
                        case PURCHASE:
                            quantityIncrement = false;
                            break;
                        case SALE:
                            quantityIncrement = true;
                            addWalletRefundForCancelSaleTransaction(transactionSummary, userId);
                            break;
                        case ISSUE:
                            quantityIncrement = true;
                            break;
                        case RETURN:
                            quantityIncrement = true;
                            break;
                        case SALES_RETURN:
                            quantityIncrement = false;
                            addWalletRefundForCancelSaleReturnTransaction(transactionSummary, userId);
                            break;
                    }


                    Map<UUID, Map<UUID, Double>> productBatchQuantityUpdateMap = getProductQuantityUpdateMapForCancelTransaction(transactionSummary, quantityIncrement, outletId);

                    if (!inventoryProductDao.updateProductBatchesQuantityWithTransaction(outletId, productBatchQuantityUpdateMap, quantityIncrement)) {
                        logger.error("Unable to update transaction quantities for outlet {} payload {}", outletId, productBatchQuantityUpdateMap);
                        throw new EmbrateRunTimeException("Unable to update transaction quantities");
                    }


//                    int transactionDetailsDeleteRows = jdbcTemplate.update(DELETE_TRANSACTION_DETAILS, transactionId.toString());
//                    if (transactionDetailsDeleteRows < 1) {
//                        logger.error("Unable to delete transaction details data for outlet {}, transactionId {}", outletId, transactionId);
//                        throw new EmbrateRunTimeException("Unable to delete transaction details");
//                    }

                    int transactionMetadataCancelRows = jdbcTemplate.update(UPDATE_TRANSACTION_ACTIVE_STATUS, InventoryTransactionStatus.CANCELLED.name(), outletId.toString(), transactionId.toString());
                    if (transactionMetadataCancelRows != 1) {
                        logger.error("Unable to update status of transaction meta data for outlet {}, transactionId {}", outletId, transactionId);
                        throw new EmbrateRunTimeException("Unable to update status of transaction metadata");
                    }

                    return true;
                }
            });
            return success;
        } catch (final Exception e) {
            logger.error("Unable to cancel transaction for outlet {}, transactionId {}",
                    outletId, transactionId, e);
        }
        return false;
    }

    //Always perform read before write. As it contains multiple data, it should not override the existing data
    public boolean updateTransactionMetadata(UUID outletId, UUID transactionId, Map<String, String> metadata) {
        try {
            return jdbcTemplate.update(UPDATE_TRANSACTION_METADATA_COL, metadata == null ? null : SharedConstants.GSON.toJson(metadata), outletId.toString(), transactionId.toString()) == 1;
        } catch (Exception e) {
            logger.error("Unable to update the transaction metadata for outletId {}, transactionId {}, metadata {}", outletId, transactionId, metadata, e);
        }
        return false;

    }

    private Map<UUID, Map<UUID, Double>> getProductQuantityUpdateMapForCancelTransaction(InventoryTransactionSummary transactionSummary, boolean quantityIncrement, UUID outletId) {
        Map<UUID, Map<UUID, Double>> productBatchQuantityUpdateMap = new HashMap<>();
        Set<UUID> skuIds = new HashSet<>();
        for (TradeProductSummary tradeProductSummary : transactionSummary.getTradeProductSummaryList()) {
            UUID skuId = tradeProductSummary.getSkuId();
            skuIds.add(skuId);
        }

        Map<UUID, Map<UUID, ProductBatchData>> skuBatchIdMap = new HashMap<>();
        Map<UUID, Map<String, ProductBatchData>> skuBatchNameMap = new HashMap<>();
        Map<String, ProductMetadata> productMetadataMap = new HashMap<>();
        populateProductDataMap(outletId, skuIds, skuBatchIdMap, skuBatchNameMap, productMetadataMap);

        for (TradeProductSummary tradeProductSummary : transactionSummary.getTradeProductSummaryList()) {
            UUID skuId = tradeProductSummary.getSkuId();
            if (!skuBatchIdMap.containsKey(skuId)) {
                logger.error("Product {} in transaction does not exists", skuId);
                throw new EmbrateRunTimeException("Product in transaction does not exists");

            }
            Map<UUID, ProductBatchData> batchIdMap = skuBatchIdMap.get(skuId);

            if (!productBatchQuantityUpdateMap.containsKey(skuId)) {
                productBatchQuantityUpdateMap.put(skuId, new HashMap<>());
            }
            for (TradeProductBatchSummary productBatchSummary : tradeProductSummary.getTradeProductBatchSummaryList()) {
                UUID batchId = productBatchSummary.getBatchId();
                if (!batchIdMap.containsKey(batchId)) {
                    logger.error("Product {} batch {} in transaction does not exists", skuId, batchId);
                    throw new EmbrateRunTimeException("Product batch in transaction does not exists");

                }

                ProductBatchData productBatchData = batchIdMap.get(batchId);

                if (Double.compare(productBatchSummary.getQuantity(), 0d) <= 0) {
                    logger.error("Invalid quantity for product {} in transaction", skuId, productBatchSummary);
                    throw new EmbrateRunTimeException("No stock is currently available for this product to cancel the transaction.");

                }

                if (!quantityIncrement && Double.compare(productBatchData.getTotalQuantity() - productBatchSummary.getQuantity(), 0d) < 0) {
                    logger.error("More than available quantity {} for product {}", productBatchData.getTotalQuantity(), skuId, productBatchSummary);
                    throw new EmbrateRunTimeException("The quantity available for this product is less than the quantity you are trying to cancel.");

                }
                productBatchQuantityUpdateMap.get(skuId).put(batchId, productBatchSummary.getQuantity());
            }
        }
        return productBatchQuantityUpdateMap;
    }


    private void addWalletRefundForCancelSaleTransaction(InventoryTransactionSummary transactionSummary, UUID userId) {
        if (transactionSummary.getInventoryUserType() != InventoryUserType.STUDENT) {
            return;
        }
        Map<String, Object> metaData = new HashMap<String, Object>();
        int now = DateUtils.now();
        metaData.put(WalletTransactionMetaDataKeys.INVENTORY_TRANSACTION_ID, transactionSummary.getTransactionId());
        if (transactionSummary.getUsedWalletAmount() != null && Double.compare(transactionSummary.getUsedWalletAmount(), 0d) > 0) {
            userWalletDao.addUserWalletTransactionNonAtomic(new WalletTransactionPayload(
                    transactionSummary.getInventoryUserInstituteId(), transactionSummary.getStudentLite().getStudentId(), UserType.STUDENT,
                    WalletTransactionCategory.INVENTORY_TRANSACTION_CANCELLATION_REFUND, null, now, FeePaymentTransactionStatus.ACTIVE,
                    transactionSummary.getUsedWalletAmount(),
                    userId, metaData, "Refund from transaction cancel"));
        }

        if (transactionSummary.getWalletCreditAmount() != null
                && Double.compare(transactionSummary.getWalletCreditAmount(), 0d) > 0) {
            userWalletDao.addUserWalletTransactionNonAtomic(new WalletTransactionPayload(
                    transactionSummary.getInventoryUserInstituteId(), transactionSummary.getStudentLite().getStudentId(), UserType.STUDENT,
                    WalletTransactionCategory.INVENTORY_TRANSACTION_CANCELLATION_CREDIT_REFUND, null, now, FeePaymentTransactionStatus.ACTIVE,
                    transactionSummary.getWalletCreditAmount(),
                    userId, metaData, "Credit Refund from transaction cancel"));
        }

    }

    private void addWalletRefundForCancelSaleReturnTransaction(InventoryTransactionSummary transactionSummary, UUID userId) {
        if (transactionSummary.getTransactionMode() != TransactionMode.WALLET) {
            return;
        }
        if (transactionSummary.getInventoryUserType() != InventoryUserType.STUDENT) {
            logger.error("Invalid transaction mode = wallet for user {}, payload {}", transactionSummary.getInventoryUserType(), transactionSummary);
            throw new EmbrateRunTimeException("Invalid transaction mode");
        }
        int now = DateUtils.now();
        Map<String, Object> metaData = new HashMap<String, Object>();
        metaData.put(WalletTransactionMetaDataKeys.INVENTORY_TRANSACTION_ID, transactionSummary.getTransactionId());
        userWalletDao.addUserWalletTransactionNonAtomic(new WalletTransactionPayload(
                transactionSummary.getInventoryUserInstituteId(), transactionSummary.getStudentLite().getStudentId(), UserType.STUDENT,
                WalletTransactionCategory.INVENTORY_TRANSACTION_CANCELLATION_SALE_RETURN_REFUND_DEDUCTION,
                null, now, FeePaymentTransactionStatus.ACTIVE, -transactionSummary.getPaidAmount(),
                userId, metaData, "Refund deduction from transaction cancel"));
    }
}
