package com.embrate.cloud.dao.tier.inventory.v2.mappers;

import com.embrate.cloud.core.api.inventory.v2.ProductBatchData;
import com.embrate.cloud.core.api.inventory.v2.ProductMetadata;
import com.embrate.cloud.core.api.inventory.v2.brand.InventoryBrand;
import com.embrate.cloud.core.api.inventory.v2.category.InventoryCategory;
import com.embrate.cloud.core.api.inventory.v2.product.group.*;
import com.lernen.cloud.core.api.inventory.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 */

public class InventoryProductGroupRowMapper implements RowMapper<InventoryProductGroupDetailedRow> {

    private static final InventoryBrandRowMapper INVENTORY_BRAND_ROW_MAPPER = new InventoryBrandRowMapper();
    private static final InventoryCategoryRowMapper CATEGORY_ROW_MAPPER = new InventoryCategoryRowMapper();
    private static final ProductMetadataRowMapper PRODUCT_METADATA_ROW_MAPPER = new ProductMetadataRowMapper();
    private static final ProductBatchRowMapper PRODUCT_BATCH_ROW_MAPPER = new ProductBatchRowMapper();

    private static final InventoryProductGroupBasicInfoRowMapper PRODUCT_GROUP_BASIC_INFO_ROW_MAPPER = new InventoryProductGroupBasicInfoRowMapper();
    private static final String QUANTITY = "product_group_sku_mapping_v2.quantity";
    private static final String ITEM_DISCOUNT = "product_group_sku_mapping_v2.discount";

    @Override
    public InventoryProductGroupDetailedRow mapRow(ResultSet rs, int rowNum) throws SQLException {

		ProductMetadata productMetadata = PRODUCT_METADATA_ROW_MAPPER.mapRow(rs, rowNum);
		InventoryCategory category = CATEGORY_ROW_MAPPER.mapRow(rs, rowNum);
		InventoryBrand brand = null;
		if (StringUtils.isNotBlank(rs.getString("inventory_brand.brand_id"))) {
			brand = INVENTORY_BRAND_ROW_MAPPER.mapRow(rs, rowNum);
		}
		ProductBatchData productBatchData = PRODUCT_BATCH_ROW_MAPPER.mapRow(rs, rowNum);

		InventoryProductGroupBasicInfo productGroupBasicInfo = PRODUCT_GROUP_BASIC_INFO_ROW_MAPPER.mapRow(rs, rowNum);

        return new InventoryProductGroupDetailedRow(productGroupBasicInfo, productMetadata, category, brand, productBatchData, rs.getDouble(QUANTITY),
                rs.getDouble(ITEM_DISCOUNT));
    }

    /**
     * This method assumes that all elements passed in list belong to same
     * product group
     *
     * @return
     */
    public static InventoryProductGroupData getProductGroupData(List<InventoryProductGroupDetailedRow> productGroupDetailedRowList) {
        if (CollectionUtils.isEmpty(productGroupDetailedRowList)) {
            return null;
        }
		InventoryProductGroupDetailedRow firstRow = productGroupDetailedRowList.get(0);

		Map<UUID, List<InventoryProductGroupDetailedRow>> skuMap = new HashMap<>();

		for (InventoryProductGroupDetailedRow productGroupDetailedRow : productGroupDetailedRowList) {
			UUID skuId = productGroupDetailedRow.getProductMetadata().getSkuId();
			if(!skuMap.containsKey(skuId)){
				skuMap.put(skuId, new ArrayList<>());
			}
			skuMap.get(skuId).add(productGroupDetailedRow);
		}

		List<InventoryProductDataQuantity> productDataQuantityList = new LinkedList<>();

		for (Map.Entry<UUID, List<InventoryProductGroupDetailedRow>> skuEntry : skuMap.entrySet()) {
			List<ProductGroupBatchDataQuantity> productGroupBatchDataQuantities = new ArrayList<>();
			if(CollectionUtils.isEmpty(skuEntry.getValue())){
				continue;
			}
			InventoryProductGroupDetailedRow firstSKUEntryRow = skuEntry.getValue().get(0);
			for(InventoryProductGroupDetailedRow inventoryProductGroupDetailedRow : skuEntry.getValue()){
				ProductGroupBatchDataQuantity productDataQuantity = new ProductGroupBatchDataQuantity(
						inventoryProductGroupDetailedRow.getProductBatchData(), inventoryProductGroupDetailedRow.getQuantity(),
						inventoryProductGroupDetailedRow.getDiscount());
				productGroupBatchDataQuantities.add(productDataQuantity);
			}
			productDataQuantityList.add(new InventoryProductDataQuantity(firstSKUEntryRow.getProductMetadata(), firstSKUEntryRow.getCategory(), firstSKUEntryRow.getBrand(), productGroupBatchDataQuantities));
		}



        return new InventoryProductGroupData(firstRow.getProductGroupBasicInfo(), productDataQuantityList);
    }
//
    public static List<InventoryProductGroupData> getProductGroupDataList(List<InventoryProductGroupDetailedRow> productGroupDetailedRowList) {
        List<InventoryProductGroupData> productGroupDataList = new LinkedList<>();
        if (CollectionUtils.isEmpty(productGroupDetailedRowList)) {
            return productGroupDataList;
        }
        Map<UUID, List<InventoryProductGroupDetailedRow>> groupIdVsProductGroupDetailedRowList = new LinkedHashMap<>();
        for (InventoryProductGroupDetailedRow productGroupDetailedRow : productGroupDetailedRowList) {
			UUID groupId = productGroupDetailedRow.getProductGroupBasicInfo().getGroupId();
            if (!groupIdVsProductGroupDetailedRowList.containsKey(groupId)) {
				groupIdVsProductGroupDetailedRowList.put(groupId, new ArrayList<>());
            }
			groupIdVsProductGroupDetailedRowList.get(groupId).add(productGroupDetailedRow);
        }

        Iterator<Map.Entry<UUID, List<InventoryProductGroupDetailedRow>>> groupIdVsProductGroupDetailedRowListItr = groupIdVsProductGroupDetailedRowList
                .entrySet().iterator();
        while (groupIdVsProductGroupDetailedRowListItr.hasNext()) {
            Map.Entry<UUID, List<InventoryProductGroupDetailedRow>> entry = groupIdVsProductGroupDetailedRowListItr.next();
			InventoryProductGroupData productGroupData = getProductGroupData(entry.getValue());
            if (productGroupData != null) {
                productGroupDataList.add(productGroupData);
            }
        }

        return productGroupDataList;
    }

}
