package com.embrate.cloud.dao.tier.inventory.v2.mappers;

import com.embrate.cloud.core.api.inventory.v2.supplier.InventorySupplier;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class InventorySupplierRowMapper implements RowMapper<InventorySupplier> {

    protected static final String SUPPLIER_NAME = "supplier_name";
    protected static final String OUTLET_ID = "outlet_id";
    protected static final String SUPPLIER_ID = "supplier_id";
    protected static final String CONTACT_NAME = "contact_name";
    protected static final String ADDRESS = "address";
    protected static final String CITY = "city";
    protected static final String STATE = "state";
    protected static final String ZIP_CODE = "zipcode";
    protected static final String COUNTRY = "country";
    protected static final String EMAIL = "email";
    protected static final String PRIMARY_PHONE_NUMBER = "primary_phone_number";
    protected static final String SECONDARY_PHONE_NUMBER = "secondary_phone_number";
    protected static final String LANDLINE_NUMBER = "landline";

    public InventorySupplier mapRow(ResultSet rs, int rowNum) throws SQLException {
        return new InventorySupplier(UUID.fromString(rs.getString(SUPPLIER_ID)), rs.getString(SUPPLIER_NAME),
                CryptoUtils.decrypt(rs.getString(CONTACT_NAME)),
                CryptoUtils.decrypt(rs.getString(ADDRESS)), CryptoUtils.decrypt(rs.getString(CITY)),
                rs.getString(STATE), CryptoUtils.decrypt(rs.getString(ZIP_CODE)), rs.getString(COUNTRY),
                CryptoUtils.decrypt(rs.getString(EMAIL)), CryptoUtils.decrypt(rs.getString(PRIMARY_PHONE_NUMBER)),
                CryptoUtils.decrypt(rs.getString(SECONDARY_PHONE_NUMBER)),
                CryptoUtils.decrypt(rs.getString(LANDLINE_NUMBER)));
    }

}
