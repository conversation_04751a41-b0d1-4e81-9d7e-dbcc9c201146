package com.embrate.cloud.dao.tier.inventory.v2.mappers;

import com.embrate.cloud.core.api.inventory.v2.category.InventoryCategory;
import com.lernen.cloud.core.api.inventory.Category;
import com.embrate.cloud.core.api.inventory.v2.brand.InventoryBrand;
import com.embrate.cloud.core.api.inventory.v2.ProductBatchData;
import com.embrate.cloud.core.api.inventory.v2.ProductDetailsV2;
import com.embrate.cloud.core.api.inventory.v2.ProductMetadata;
import com.lernen.cloud.dao.tier.inventory.mappers.CategoryRowMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 */
public class ProductDetailsV2RowMapperMapper implements RowMapper<ProductDetailsV2> {

    private static final InventoryBrandRowMapper INVENTORY_BRAND_ROW_MAPPER = new InventoryBrandRowMapper();
    private static final InventoryCategoryRowMapper CATEGORY_ROW_MAPPER = new InventoryCategoryRowMapper();
    private static final ProductMetadataRowMapper PRODUCT_METADATA_ROW_MAPPER = new ProductMetadataRowMapper();
    private static final ProductBatchRowMapper PRODUCT_BATCH_ROW_MAPPER = new ProductBatchRowMapper();

    public static List<ProductDetailsV2> getProductDetailsList(List<ProductDetailsV2> inputProductDetailsV2List) {
        List<ProductDetailsV2> productDetailsV2List = new ArrayList<>();
        if (CollectionUtils.isEmpty(inputProductDetailsV2List)) {
            return productDetailsV2List;
        }


        Map<UUID, List<ProductBatchData>> skuProductBatchDataMap = new HashMap<>();
        Map<UUID, ProductDetailsV2> productDetailsV2Map = new HashMap<>();

        for (ProductDetailsV2 productDetailsV2 : inputProductDetailsV2List) {
            UUID skuId = productDetailsV2.getProductMetadata().getSkuId();
            if (!skuProductBatchDataMap.containsKey(skuId)) {
                skuProductBatchDataMap.put(skuId, new ArrayList<>());
            }
            skuProductBatchDataMap.get(skuId).addAll(productDetailsV2.getProductBatchDataList());
            productDetailsV2Map.put(skuId, productDetailsV2);
        }

        for (Map.Entry<UUID, ProductDetailsV2> entry : productDetailsV2Map.entrySet()) {
            ProductDetailsV2 productDetailsV2Entry = entry.getValue();
            List<ProductBatchData> productBatchDataList = skuProductBatchDataMap.get(entry.getKey());
            if(CollectionUtils.isNotEmpty(productBatchDataList)){
                Collections.sort(productBatchDataList, new Comparator<ProductBatchData>() {
                    @Override
                    public int compare(ProductBatchData o1, ProductBatchData o2) {
                        if(o1.isDefaultBatch()){
                            return -1;
                        } else if(o2.isDefaultBatch()){
                            return 1;
                        }
                        return StringUtils.compareIgnoreCase(o1.getBatchName(), o2.getBatchName());
                    }
                });
            }
            productDetailsV2List.add(new ProductDetailsV2(productDetailsV2Entry.getProductMetadata(), productDetailsV2Entry.getCategory(),
                    productDetailsV2Entry.getBrand(), productBatchDataList));
        }


        return productDetailsV2List;
    }

    @Override
    public ProductDetailsV2 mapRow(ResultSet rs, int rowNum) throws SQLException {
        ProductMetadata productMetadata = PRODUCT_METADATA_ROW_MAPPER.mapRow(rs, rowNum);
        InventoryCategory category = CATEGORY_ROW_MAPPER.mapRow(rs, rowNum);
        InventoryBrand brand = null;
        if (StringUtils.isNotBlank(rs.getString("inventory_brand.brand_id"))) {
            brand = INVENTORY_BRAND_ROW_MAPPER.mapRow(rs, rowNum);
        }
        ProductBatchData productBatchData = PRODUCT_BATCH_ROW_MAPPER.mapRow(rs, rowNum);

        List<ProductBatchData> productBatchDataList = new ArrayList<>();
        if (productBatchData != null) {
            productBatchDataList.add(productBatchData);
        }

        return new ProductDetailsV2(productMetadata, category, brand, productBatchDataList);
    }

}
