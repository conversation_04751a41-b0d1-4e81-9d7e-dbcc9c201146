package com.embrate.cloud.dao.tier.leave.management;

import com.embrate.cloud.core.api.leave.management.policy.LeavePolicyMetadata;
import com.embrate.cloud.core.api.leave.management.policy.LeavePolicyTemplate;
import com.embrate.cloud.core.api.leave.management.policy.LeavePolicyTemplatePayload;
import com.embrate.cloud.core.api.leave.management.policy.LeaveTypePolicyPayload;
import com.embrate.cloud.dao.tier.leave.management.mappers.LeavePolicyMetadataRowMapper;
import com.embrate.cloud.dao.tier.leave.management.mappers.LeavePolicyTemplateRowMapper;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class LeavePolicyTemplateDao {
    private static final Logger logger = LogManager.getLogger(LeavePolicyTemplateDao.class);

    private static final String INSERT_LEAVE_POLICY_METADATA = "INSERT INTO leave_policy_template (institute_id, academic_session_id, template_id, name, type, description, metadata) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?)";

    private static final String INSERT_LEAVE_POLICY_MAPPING = "INSERT INTO leave_policy_template_mapping (institute_id, template_id, leave_type_id, policy_details, metadata) " +
            "VALUES (?, ?, ?, ?, ?)";

    private static final String GET_LEAVE_POLICY_METADATA_FOR_INSTITUTE_SESSION = "SELECT * FROM leave_policy_template " +
            "WHERE institute_id = ? AND academic_session_id = ? order by name";

    private static final String GET_LEAVE_POLICY = "select leave_policy_template.*, leave_policy_template_mapping.*, leave_type.* " +
            "from leave_policy_template join leave_policy_template_mapping on " +
            "leave_policy_template.template_id = leave_policy_template_mapping.template_id join " +
            "leave_type on leave_type.leave_type_id = leave_policy_template_mapping.leave_type_id " +
            "where leave_policy_template.institute_id = ? and leave_policy_template.template_id = ?";

    private static final String UPDATE_LEAVE_POLICY_METADATA = "update leave_policy_template SET name = ?, type = ?, " +
            "description = ?, metadata = ? WHERE institute_id = ? and template_id = ?";

    private static final String DELETE_LEAVE_POLICY_METADATA = "delete from leave_policy_template where institute_id = ? and template_id = ?";

    private static final String DELETE_LEAVE_POLICY_MAPPING = "delete from leave_policy_template_mapping where institute_id = ? and template_id = ?";

    private static final LeavePolicyMetadataRowMapper LEAVE_POLICY_METADATA_ROW_MAPPER = new LeavePolicyMetadataRowMapper();
    private static final LeavePolicyTemplateRowMapper LEAVE_POLICY_TEMPLATE_ROW_MAPPER = new LeavePolicyTemplateRowMapper();

    private final JdbcTemplate jdbcTemplate;

    private final TransactionTemplate transactionTemplate;

    public LeavePolicyTemplateDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
    }

    public List<LeavePolicyMetadata> getLeavePolicyTemplates(int instituteId, int academicSessionId) {
        try {
            return jdbcTemplate.query(GET_LEAVE_POLICY_METADATA_FOR_INSTITUTE_SESSION, new Object[]{instituteId, academicSessionId}, LEAVE_POLICY_METADATA_ROW_MAPPER);
        } catch (Exception e) {
            logger.error("Error while getting leave policies for institute {}, academicSessionId {}", instituteId, academicSessionId, e);
        }

        return null;
    }

    public LeavePolicyTemplate getLeavePolicyTemplate(int instituteId, UUID templateId) {
        try {
            return LeavePolicyTemplateRowMapper.getLeavePolicyTemplate(jdbcTemplate.query(GET_LEAVE_POLICY, new Object[]{instituteId, templateId.toString()}, LEAVE_POLICY_TEMPLATE_ROW_MAPPER));
        } catch (Exception e) {
            logger.error("Error while getting leave policy for institute {}, templateId {}", instituteId, templateId, e);
        }

        return null;
    }

    public UUID addLeavePolicyTemplate(int instituteId, LeavePolicyTemplatePayload leavePolicyTemplatePayload) {
        try {
            return transactionTemplate.execute(new TransactionCallback<UUID>() {
                @Override
                public UUID doInTransaction(TransactionStatus status) {
                    UUID templateId = UUID.randomUUID();
                    LeavePolicyMetadata leavePolicyMetadata = leavePolicyTemplatePayload.getLeavePolicyMetadata();
                    int rows = jdbcTemplate.update(INSERT_LEAVE_POLICY_METADATA, instituteId, leavePolicyMetadata.getAcademicSessionId(),
                            templateId.toString(), leavePolicyMetadata.getName().trim(), leavePolicyMetadata.getPolicyType().name(),
                            leavePolicyMetadata.getDescription() == null ? null : leavePolicyMetadata.getDescription().trim(),
                            leavePolicyMetadata.getMetadata() == null ? null : SharedConstants.GSON.toJson(leavePolicyMetadata.getMetadata()));

                    if (rows != 1) {
                        logger.error("Unable to add leave template metadata {}, {}", instituteId, leavePolicyTemplatePayload);
                        throw new EmbrateRunTimeException("Unable to add leave template metadata");
                    }
                    createLeavePolicyMapping(leavePolicyTemplatePayload, instituteId, templateId);
                    return templateId;
                }
            });
        } catch (Exception e) {
            logger.error("Error while adding leave policy template {}, {}", instituteId, leavePolicyTemplatePayload, e);
        }
        return null;
    }

    public boolean updateLeavePolicyTemplate(int instituteId, UUID templateId, LeavePolicyTemplatePayload leavePolicyTemplatePayload) {
        try {
            return transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    LeavePolicyMetadata leavePolicyMetadata = leavePolicyTemplatePayload.getLeavePolicyMetadata();
                    int rows = jdbcTemplate.update(UPDATE_LEAVE_POLICY_METADATA, leavePolicyMetadata.getName().trim(),
                            leavePolicyMetadata.getPolicyType().name(),
                            leavePolicyMetadata.getDescription() == null ? null : leavePolicyMetadata.getDescription().trim(),
                            leavePolicyMetadata.getMetadata() == null ? null : SharedConstants.GSON.toJson(leavePolicyMetadata.getMetadata()),
                            instituteId, templateId.toString());

                    if (rows != 1) {
                        logger.error("Unable to update leave template metadata {}, {}", instituteId, leavePolicyTemplatePayload);
                        throw new EmbrateRunTimeException("Unable to update leave template metadata");
                    }

                    jdbcTemplate.update(DELETE_LEAVE_POLICY_MAPPING, instituteId, templateId.toString());

                    createLeavePolicyMapping(leavePolicyTemplatePayload, instituteId, templateId);
                    return true;
                }
            });
        } catch (Exception e) {
            logger.error("Error while updating leave policy template {}, {}", instituteId, leavePolicyTemplatePayload, e);
        }
        return false;
    }

    // Must be used inside transcation only
    private void createLeavePolicyMapping(LeavePolicyTemplatePayload leavePolicyTemplatePayload, int instituteId, UUID templateId) {
        List<Object[]> args = new ArrayList<>();
        for (LeaveTypePolicyPayload leaveTypePolicyPayload : leavePolicyTemplatePayload.getLeaveTypePolicyPayloadList()) {
            args.add(new Object[]{instituteId, templateId.toString(), leaveTypePolicyPayload.getLeaveTypeId(),
                    SharedConstants.GSON.toJson(leaveTypePolicyPayload.getLeaveTypePolicies()),
                    leaveTypePolicyPayload.getMetadata() == null ? null :
                            SharedConstants.GSON.toJson(leaveTypePolicyPayload.getMetadata())});
        }

        int[] mappingRows = jdbcTemplate.batchUpdate(INSERT_LEAVE_POLICY_MAPPING, args);
        if (mappingRows.length != args.size()) {
            logger.error("Unable to add leave template mapping {}, {}", instituteId, leavePolicyTemplatePayload);
            throw new EmbrateRunTimeException("Unable to add leave template mapping");
        }
    }

    public boolean deleteLeavePolicyTemplate(int instituteId, UUID templateId) {
        try {
            return transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    jdbcTemplate.update(DELETE_LEAVE_POLICY_MAPPING, instituteId, templateId.toString());

                    int rows = jdbcTemplate.update(DELETE_LEAVE_POLICY_METADATA, instituteId, templateId.toString());

                    if (rows != 1) {
                        logger.error("Unable to delete leave template metadata {}, {}", instituteId, templateId);
                        throw new EmbrateRunTimeException("Unable to delete leave template metadata");
                    }
                    return true;
                }
            });
        } catch (Exception e) {
            logger.error("Error while deleting leave policy template {}, {}", instituteId, templateId, e);
        }
        return false;
    }

}
