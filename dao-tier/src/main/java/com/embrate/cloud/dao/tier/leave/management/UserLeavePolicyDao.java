package com.embrate.cloud.dao.tier.leave.management;

import com.embrate.cloud.core.api.leave.management.LeaveDocumentType;
import com.embrate.cloud.core.api.leave.management.balance.UserLeaveTypeBalance;
import com.embrate.cloud.core.api.leave.management.balance.UserNetLeaveBalance;
import com.embrate.cloud.core.api.leave.management.policy.LeaveTypePolicyPayload;
import com.embrate.cloud.core.api.leave.management.policy.user.UserLeavePolicy;
import com.embrate.cloud.core.api.leave.management.policy.user.UserLeavePolicyPayload;
import com.embrate.cloud.core.api.leave.management.transaction.*;
import com.embrate.cloud.core.api.leave.management.user.LeaveReviewPayload;
import com.embrate.cloud.core.api.noticeboard.NoticeBoardDocumentType;
import com.embrate.cloud.dao.tier.leave.management.mappers.UserLeaveBalanceRowMapper;
import com.embrate.cloud.dao.tier.leave.management.mappers.UserLeavePolicyRowMapper;
import com.embrate.cloud.dao.tier.leave.management.mappers.UserLeaveTransactionRowMapper;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.PlaceholdersUtils;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.exceptions.ExceptionHandling;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.Timestamp;
import java.util.*;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

/**
 * <AUTHOR>
 */

public class UserLeavePolicyDao {
    private static final Logger logger = LogManager.getLogger(UserLeavePolicyDao.class);

    private static final String GET_USER_TYPE_LEAVE_POLICIES = "select user_leave_policy.*, leave_type.* " +
            "from user_leave_policy join leave_type on user_leave_policy.leave_type_id = leave_type.leave_type_id " +
            "where user_leave_policy.institute_id = ? and user_leave_policy.academic_session_id = ? %s";

    private static final String INSERT_USER_LEAVE_POLICY = "INSERT INTO user_leave_policy (institute_id, user_id, academic_session_id, leave_type_id, policy_details, metadata) " +
            "VALUES (?, ?, ?, ?, ?, ?)";

    private static final String DELETE_USER_LEAVE_POLICY = "delete from user_leave_policy where institute_id = ? and academic_session_id = ? and user_id = ?";


    private static final String GET_INSTITUTE_USER_LEAVE_TRANSACTIONS = "select user_leave_transaction_metadata.*, user_leave_transaction_mapping.*, leave_type.* " +
            "from user_leave_transaction_metadata join user_leave_transaction_mapping on user_leave_transaction_metadata.transaction_id = user_leave_transaction_mapping.transaction_id " +
            "join leave_type on user_leave_transaction_mapping.leave_type_id = leave_type.leave_type_id " +
            "where user_leave_transaction_metadata.institute_id = ? and user_leave_transaction_metadata.academic_session_id = ? and " +
            "user_leave_transaction_metadata.status != 'DELETED' %s";

    private static final String UPDATE_USER_LEAVE_TRANSACTION_STATUS = "update user_leave_transaction_metadata set status = ? " +
            "where institute_id = ? and academic_session_id = ? and transaction_id = ?";

    private static final String UPDATE_USER_LEAVE_TRANSACTION_REVIEW_DETAILS = "update user_leave_transaction_metadata " +
            " set status = ?, reviewed_by = ?, reviewed_at = ?, review_remarks = ? " +
            " where institute_id = ? and academic_session_id = ? and transaction_id = ?";

    private static final String UPDATE_LEAVE_DOCUMENTS = " update user_leave_transaction_metadata set attachments = ? "
            + " where user_leave_transaction_metadata.transaction_id = ? ";

    private static final String INSERT_USER_LEAVE_TRANSACTION_METADATA = "INSERT INTO user_leave_transaction_metadata (institute_id, transaction_id, " +
            "user_id, user_type, academic_session_id, type, status, category, applied_by, applied_at, description, reviewed_by, reviewed_at, review_remarks) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String INSERT_USER_LEAVE_TRANSACTION_MAPPING = "INSERT INTO user_leave_transaction_mapping (institute_id, transaction_id, " +
            "leave_type_id, start_date, leave_count) VALUES (?, ?, ?, ?, ?)";

    private static final String UPSERT_USER_LEAVE_BALANCE = "INSERT INTO user_leave_balance (institute_id, academic_session_id, " +
            "user_id, leave_type_id, opening_balance, total_credit_count, total_debit_count) VALUES (?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE " +
            " total_credit_count = total_credit_count + ? , total_debit_count = total_debit_count + ?";

    private static final String GET_USER_LEAVE_BALANCE = "select user_leave_balance.*, leave_type.* from user_leave_balance " +
            "join leave_type on user_leave_balance.leave_type_id = leave_type.leave_type_id where user_leave_balance.institute_id = ? and " +
            "user_leave_balance.academic_session_id = ? and user_leave_balance.user_id = ?";


    private static final String GET_ALL_USER_LEAVE_BALANCE = "select user_leave_balance.*, leave_type.* from user_leave_balance " +
            "join leave_type on user_leave_balance.leave_type_id = leave_type.leave_type_id where user_leave_balance.institute_id = ? and " +
            "user_leave_balance.academic_session_id = ?";

//
//
//    private static final String INSERT_LEAVE_POLICY_METADATA = "INSERT INTO leave_policy_template (institute_id, academic_session_id, template_id, name, type, description, metadata) " +
//            "VALUES (?, ?, ?, ?, ?, ?, ?)";
//
//    private static final String INSERT_LEAVE_POLICY_MAPPING = "INSERT INTO leave_policy_template_mapping (institute_id, template_id, leave_type_id, policy_details, metadata) " +
//            "VALUES (?, ?, ?, ?, ?)";
//
//    private static final String GET_LEAVE_POLICY_METADATA_FOR_INSTITUTE_SESSION = "SELECT * FROM leave_policy_template " +
//            "WHERE institute_id = ? AND academic_session_id = ? order by name";
//
//
//
//    private static final String UPDATE_LEAVE_POLICY_METADATA = "update leave_policy_template SET name = ?, type = ?, " +
//            "description = ?, metadata = ? WHERE institute_id = ? and template_id = ?";
//
//    private static final String DELETE_LEAVE_POLICY_METADATA = "delete from leave_policy_template where institute_id = ? and template_id = ?";
//
//    private static final String DELETE_LEAVE_POLICY_MAPPING = "delete from leave_policy_template_mapping where institute_id = ? and template_id = ?";

    private static final UserLeavePolicyRowMapper USER_LEAVE_POLICY_ROW_MAPPER = new UserLeavePolicyRowMapper();
    private static final UserLeaveBalanceRowMapper USER_LEAVE_BALANCE_ROW_MAPPER = new UserLeaveBalanceRowMapper();
    private static final UserLeaveTransactionRowMapper USER_LEAVE_TRANSACTION_ROW_MAPPER = new UserLeaveTransactionRowMapper();
    private final JdbcTemplate jdbcTemplate;

    private final TransactionTemplate transactionTemplate;

    public UserLeavePolicyDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
    }

    public List<UserLeavePolicy> getUserLeavePolicies(int instituteId, int academicSessionId) {
        try {
            return UserLeavePolicyRowMapper.getUserLeavePolicies(jdbcTemplate.query(String.format(GET_USER_TYPE_LEAVE_POLICIES, ""), new Object[]{instituteId, academicSessionId}, USER_LEAVE_POLICY_ROW_MAPPER));
        } catch (Exception e) {
            logger.error("Error while getting user leave policies for institute {}, academicSessionId {}", instituteId, academicSessionId, e);
        }
        return null;
    }

    public UserLeavePolicy getUserLeavePolicy(int instituteId, int academicSessionId, UUID userId) {
        try {
            List<UserLeavePolicy> userLeavePolicyList = UserLeavePolicyRowMapper.getUserLeavePolicies(jdbcTemplate.query(String.format(GET_USER_TYPE_LEAVE_POLICIES, " and user_leave_policy.user_id= ? "), new Object[]{instituteId, academicSessionId, userId.toString()}, USER_LEAVE_POLICY_ROW_MAPPER));
            return CollectionUtils.isEmpty(userLeavePolicyList) ? null : userLeavePolicyList.get(0);
        } catch (Exception e) {
            logger.error("Error while getting user leave policy for institute {}, academicSessionId {}, userId {}", instituteId, academicSessionId, userId, e);
        }
        return null;
    }

    public boolean addUserLeavePolicies(int instituteId, int academicSessionId, Set<UUID> users, List<LeaveTypePolicyPayload> leaveTypePolicyPayloadList) {
        try {
            return transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    return addUserLeavePoliciesWithoutTransaction(instituteId, academicSessionId, users, leaveTypePolicyPayloadList);
                }
            });
        } catch (Exception e) {
            logger.error("Error while adding user leave policy for institute {}, academicSessionId {}, users {}", instituteId, academicSessionId, users, e);
        }
        return false;
    }

    /**
     * This method must be used within transaction only
     */
    private boolean addUserLeavePoliciesWithoutTransaction(int instituteId, int academicSessionId, Set<UUID> users, List<LeaveTypePolicyPayload> leaveTypePolicyPayloadList) {
        List<Object[]> args = new ArrayList<>();
        for (UUID userId : users) {
            for (LeaveTypePolicyPayload leaveTypePolicyPayload : leaveTypePolicyPayloadList) {
                args.add(new Object[]{instituteId, userId.toString(), academicSessionId, leaveTypePolicyPayload.getLeaveTypeId(),
                        GSON.toJson(leaveTypePolicyPayload.getLeaveTypePolicies()),
                        leaveTypePolicyPayload.getMetadata() == null ? null :
                                GSON.toJson(leaveTypePolicyPayload.getMetadata())});
            }
        }


        int[] mappingRows = jdbcTemplate.batchUpdate(INSERT_USER_LEAVE_POLICY, args);
        if (mappingRows.length != args.size()) {
            logger.error("Unable to add leave template mapping for institute {}, academicSessionId {}, users {}", instituteId, academicSessionId, users);
            throw new EmbrateRunTimeException("Unable to add leave policy template");
        }
        return true;
    }

    public boolean updateUserLeavePolicy(int instituteId, UUID userId, UserLeavePolicyPayload userLeavePolicyPayload) {
        try {
            return transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    jdbcTemplate.update(DELETE_USER_LEAVE_POLICY, instituteId, userLeavePolicyPayload.getAcademicSessionId(), userId.toString());
                    return addUserLeavePoliciesWithoutTransaction(instituteId, userLeavePolicyPayload.getAcademicSessionId(), new HashSet<>(Arrays.asList(userId)), userLeavePolicyPayload.getLeaveTypePolicyPayloadList());
                }
            });
        } catch (Exception e) {
            logger.error("Error while updating user leave policy template {}, {}. {}", instituteId, userId, userLeavePolicyPayload, e);
        }
        return false;
    }

    public boolean deleteUserLeavePolicy(int instituteId, int academicSessionId, UUID userId) {
        try {
            return transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    //TODO : Allow when no debit transaction is present and delete all credit transaction also
                    jdbcTemplate.update(DELETE_USER_LEAVE_POLICY, instituteId, academicSessionId, userId.toString());
                    return true;
                }
            });
        } catch (Exception e) {
            logger.error("Error while deleting user leave policy {}, {}, {}", instituteId, academicSessionId, userId, e);
        }
        return false;
    }

    public UserNetLeaveBalance getUserLeaveBalance(int instituteId, int academicSessionId, UUID userId) {
        try {
            return UserLeaveBalanceRowMapper.getUserLeaveBalance(jdbcTemplate.query(GET_USER_LEAVE_BALANCE, new Object[]{instituteId, academicSessionId, userId.toString()}, USER_LEAVE_BALANCE_ROW_MAPPER));
        } catch (Exception e) {
            logger.error("Error while getting user leave balance for institute {}, academicSessionId {}, userId {}", instituteId, academicSessionId, userId, e);
        }
        return null;
    }

    public List<UserNetLeaveBalance> getAllUserLeaveBalance(int instituteId, int academicSessionId) {
        try {
            return UserLeaveBalanceRowMapper.getAllUserLeaveBalance(jdbcTemplate.query(GET_ALL_USER_LEAVE_BALANCE, new Object[]{instituteId, academicSessionId}, USER_LEAVE_BALANCE_ROW_MAPPER));
        } catch (Exception e) {
            logger.error("Error while getting user leave balance for institute {}, academicSessionId {}", instituteId, academicSessionId, e);
        }
        return null;
    }

    public List<UserLeaveTransactionDetails> getUserLeaveTransactions(int instituteId, int academicSessionId) {
        try {
            List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(academicSessionId);
            String finalQuery = String.format(GET_INSTITUTE_USER_LEAVE_TRANSACTIONS, "");
            return UserLeaveTransactionRowMapper.getUserLeaveTransactions(jdbcTemplate.query(finalQuery, args.toArray(), USER_LEAVE_TRANSACTION_ROW_MAPPER));
        } catch (Exception e) {
            logger.error("Error while getting user leave transactions for institute {}, academicSessionId {}", instituteId, academicSessionId, e);
        }
        return null;
    }
    public List<UserLeaveTransactionDetails> getUserLeaveTransactions(int instituteId, int academicSessionId, List<UserType> userTypes) {
        try {
            List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(academicSessionId);
            if(CollectionUtils.isEmpty(userTypes)){
                logger.error("Invalid user Type provided. At least one user Type is required to lock the table/no institute id column" +
                        " {}", instituteId);
                return null;
            }
            String userTypeCondition = String.format(" and user_leave_transaction_metadata.user_type IN (%s) ",
                    PlaceholdersUtils.buildPlaceholders(userTypes.size()));
            for (UserType userType : userTypes) {
                args.add(userType.toString()); // Add each userType to the args list
            }
            String finalQuery = String.format(GET_INSTITUTE_USER_LEAVE_TRANSACTIONS, userTypeCondition);
            return UserLeaveTransactionRowMapper.getUserLeaveTransactions(jdbcTemplate.query(finalQuery, args.toArray(), USER_LEAVE_TRANSACTION_ROW_MAPPER));
        } catch (Exception e) {
            logger.error("Error while getting user leave transactions for institute {}, academicSessionId {}", instituteId, academicSessionId, e);
        }
        return null;
    }

    public List<UserLeaveTransactionDetails> getUserLeaveTransactions(int instituteId, int academicSessionId, UUID userId) {
        try {
            List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(academicSessionId);
            args.add(userId.toString());
            String finalQuery = String.format(GET_INSTITUTE_USER_LEAVE_TRANSACTIONS, " and user_leave_transaction_metadata.user_id = ? ");
            return UserLeaveTransactionRowMapper.getUserLeaveTransactions(jdbcTemplate.query(finalQuery, args.toArray(), USER_LEAVE_TRANSACTION_ROW_MAPPER));
        } catch (Exception e) {
            logger.error("Error while getting user leave transactions for institute {}, academicSessionId {}", instituteId, academicSessionId, e);
        }
        return null;
    }

    public List<UserLeaveTransactionDetails> getUserLeaveTransactions(int instituteId, int academicSessionId, Set<UUID> transactionIds) {
        try {
            List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(academicSessionId);

            StringBuilder inQuery = new StringBuilder();
            inQuery.append("(");
            String delimiter = "";
            for (UUID transactionId : transactionIds) {
                args.add(transactionId.toString());
                inQuery.append(delimiter).append(" ?");
                delimiter = ",";
            }
            inQuery.append(")");

            String finalQuery = String.format(GET_INSTITUTE_USER_LEAVE_TRANSACTIONS, " and user_leave_transaction_metadata.transaction_id in " + inQuery);
            return UserLeaveTransactionRowMapper.getUserLeaveTransactions(jdbcTemplate.query(finalQuery, args.toArray(), USER_LEAVE_TRANSACTION_ROW_MAPPER));
        } catch (Exception e) {
            logger.error("Error while getting user leave transactions for institute {}, academicSessionId {}, transactionIds {}", instituteId, academicSessionId, transactionIds, e);
        }
        return null;
    }

    public Set<UUID> updateUserLeaveTransactions(int instituteId, int academicSessionId, List<UserLeaveUpdatePayload> userLeaveUpdatePayloadList) {
        try {
            return transactionTemplate.execute(new TransactionCallback<Set<UUID>>() {
                @Override
                public Set<UUID> doInTransaction(TransactionStatus status) {
                    return updateUserLeaveTransactionsWithoutTransaction( instituteId, academicSessionId, userLeaveUpdatePayloadList, true);
                }
            });
        } catch (Exception e) {
            logger.error("Error while updating user leave transactions for institute {}, academicSessionId {}, userLeaveUpdatePayloadList {}", instituteId, academicSessionId, userLeaveUpdatePayloadList, e);
        }
        return null;
    }

    /**
     * Must be used within transaction only
     */
    public Set<UUID> updateUserLeaveTransactionsWithoutTransaction(int instituteId, int academicSessionId, List<UserLeaveUpdatePayload> userLeaveUpdatePayloadList, boolean insertInDB) {
        Map<UUID, Map<Integer, UserLeaveTypeBalance>> deleteTransactionLeaveTypeBalanceMap = new HashMap<>();
        Map<UUID, Map<Integer, UserLeaveTypeBalance>> newTransactionLeaveTypeBalanceMap = new HashMap<>();
        Set<UUID> allDeleteTransactions = new HashSet<>();
        List<UserLeaveTransactionDetails> allUserLeaveTransactionDetailsList = new ArrayList<>();
        for (UserLeaveUpdatePayload userLeaveUpdatePayload : userLeaveUpdatePayloadList) {
            if (CollectionUtils.isNotEmpty(userLeaveUpdatePayload.getDeleteTransactionIds())) {
                allDeleteTransactions.addAll(userLeaveUpdatePayload.getDeleteTransactionIds());
            }

            if (CollectionUtils.isNotEmpty(userLeaveUpdatePayload.getUserLeaveTransactionDetailsList())) {
                allUserLeaveTransactionDetailsList.addAll(userLeaveUpdatePayload.getUserLeaveTransactionDetailsList());
            }
        }

        if (CollectionUtils.isNotEmpty(allDeleteTransactions)) {
            List<UserLeaveTransactionDetails> existingUserLeaveTransactionDetailsList =
                    getUserLeaveTransactions(instituteId, academicSessionId, allDeleteTransactions);
            deleteTransactionLeaveTypeBalanceMap = computeLeaveTypeBalance(existingUserLeaveTransactionDetailsList, true);
            softDeleteUserLeaveTransaction(instituteId, academicSessionId, allDeleteTransactions);
        }
        Set<UUID> newTransactions = new HashSet<>();
        if (CollectionUtils.isNotEmpty(allUserLeaveTransactionDetailsList)) {
            newTransactionLeaveTypeBalanceMap = computeLeaveTypeBalance(allUserLeaveTransactionDetailsList, false);
            /*
             * This is only for Staff Leave Count Update if Leave is already applied by the Staff 
             * and we will have only one staff data
             */
            newTransactions.add(allUserLeaveTransactionDetailsList.get(0).getMetadata().getTransactionId());
            /*
             * this if condition is used if the dataEntry is needed in database 
             */
            if(insertInDB){
                newTransactions = addNewUserLeaveTransaction(instituteId, allUserLeaveTransactionDetailsList);
            }
        }

        Map<UUID, List<UserLeaveTypeBalance>> finalUserLeaveTypeBalanceMap = getFinalUserLeaveBalanceUpdate(deleteTransactionLeaveTypeBalanceMap, newTransactionLeaveTypeBalanceMap);
        updateLeaveBalance(instituteId, academicSessionId, finalUserLeaveTypeBalanceMap);
        return newTransactions;
    }

    private Map<UUID, List<UserLeaveTypeBalance>> getFinalUserLeaveBalanceUpdate(Map<UUID, Map<Integer, UserLeaveTypeBalance>> userDeleteTransactionLeaveTypeBalanceMap, Map<UUID, Map<Integer, UserLeaveTypeBalance>> userNewTransactionLeaveTypeBalanceMap) {
        Set<UUID> allUsers = new HashSet<>();
        if (MapUtils.isNotEmpty(userDeleteTransactionLeaveTypeBalanceMap)) {
            allUsers.addAll(userDeleteTransactionLeaveTypeBalanceMap.keySet());
        }

        if (MapUtils.isNotEmpty(userNewTransactionLeaveTypeBalanceMap)) {
            allUsers.addAll(userNewTransactionLeaveTypeBalanceMap.keySet());
        }

        userDeleteTransactionLeaveTypeBalanceMap = userDeleteTransactionLeaveTypeBalanceMap == null ? new HashMap<>() : userDeleteTransactionLeaveTypeBalanceMap;
        userNewTransactionLeaveTypeBalanceMap = userNewTransactionLeaveTypeBalanceMap == null ? new HashMap<>() : userNewTransactionLeaveTypeBalanceMap;

        Map<UUID, List<UserLeaveTypeBalance>> finalUserLeaveTypeBalanceMap = new HashMap<>();
        for (UUID userId : allUsers) {
            Map<Integer, UserLeaveTypeBalance> deleteTransactionLeaveTypeBalanceMap = userDeleteTransactionLeaveTypeBalanceMap.get(userId);
            Map<Integer, UserLeaveTypeBalance> newTransactionLeaveTypeBalanceMap = userNewTransactionLeaveTypeBalanceMap.get(userId);
            Set<Integer> allLeaveTypes = new HashSet<>();
            if (MapUtils.isNotEmpty(deleteTransactionLeaveTypeBalanceMap)) {
                allLeaveTypes.addAll(deleteTransactionLeaveTypeBalanceMap.keySet());
            }

            if (MapUtils.isNotEmpty(newTransactionLeaveTypeBalanceMap)) {
                allLeaveTypes.addAll(newTransactionLeaveTypeBalanceMap.keySet());
            }

            deleteTransactionLeaveTypeBalanceMap = deleteTransactionLeaveTypeBalanceMap == null ? new HashMap<>() : deleteTransactionLeaveTypeBalanceMap;
            newTransactionLeaveTypeBalanceMap = newTransactionLeaveTypeBalanceMap == null ? new HashMap<>() : newTransactionLeaveTypeBalanceMap;


            List<UserLeaveTypeBalance> finalUserLeaveTypeBalanceList = new ArrayList<>();
            for (Integer leaveTypeId : allLeaveTypes) {
                UserLeaveTypeBalance deleteUserLeaveTypeBalance = deleteTransactionLeaveTypeBalanceMap.get(leaveTypeId);
                UserLeaveTypeBalance newUserLeaveTypeBalance = newTransactionLeaveTypeBalanceMap.get(leaveTypeId);

                if (deleteUserLeaveTypeBalance == null) {
                    deleteUserLeaveTypeBalance = UserLeaveTypeBalance.get(leaveTypeId, 0d, 0d);
                }
                if (newUserLeaveTypeBalance == null) {
                    newUserLeaveTypeBalance = UserLeaveTypeBalance.get(leaveTypeId, 0d, 0d);
                }
                finalUserLeaveTypeBalanceList.add(UserLeaveTypeBalance.get(leaveTypeId,
                        deleteUserLeaveTypeBalance.getTotalCreditCount() +
                                newUserLeaveTypeBalance.getTotalCreditCount(),
                        deleteUserLeaveTypeBalance.getTotalDebitCount() +
                                newUserLeaveTypeBalance.getTotalDebitCount()));
            }

            finalUserLeaveTypeBalanceMap.put(userId, finalUserLeaveTypeBalanceList);
        }


        return finalUserLeaveTypeBalanceMap;
    }

    // Assuming all the data belongs to a same session, so it does not take care of opening balance etc
    private Map<UUID, Map<Integer, UserLeaveTypeBalance>> computeLeaveTypeBalance(List<UserLeaveTransactionDetails> userLeaveTransactionDetailsList, boolean deleteTransaction) {
        Map<UUID, Map<Integer, UserLeaveTypeBalance>> userLeaveTypeBalanceMap = new HashMap<>();
        for (UserLeaveTransactionDetails userLeaveTransactionDetails : userLeaveTransactionDetailsList) {
            UUID userId = userLeaveTransactionDetails.getMetadata().getUserId();
            LeaveTransactionCategory category = userLeaveTransactionDetails.getMetadata().getCategory();
            LeaveTransactionType type = userLeaveTransactionDetails.getMetadata().getTransactionType();
            // In case of transaction delete, amount credit/debit would reverse to update final balance
            int sign = 1;
            if (deleteTransaction) {
                sign = -1;
            }
            for (UserLeaveTypeTransactionData userLeaveTypeTransactionData : userLeaveTransactionDetails.getLeaveTypeTransactionDataList()) {
                int leaveTypeId = userLeaveTypeTransactionData.getLeaveType().getLeaveTypeId();
                if (!userLeaveTypeBalanceMap.containsKey(userId)) {
                    userLeaveTypeBalanceMap.put(userId, new HashMap<>());
                }
                Map<Integer, UserLeaveTypeBalance> leaveTypeBalanceMap = userLeaveTypeBalanceMap.get(userId);
                if (!leaveTypeBalanceMap.containsKey(leaveTypeId)) {
                    leaveTypeBalanceMap.put(leaveTypeId, UserLeaveTypeBalance.get(leaveTypeId,
                            type == LeaveTransactionType.CREDIT ? sign * userLeaveTypeTransactionData.getLeaveCount() : 0d,
                            type == LeaveTransactionType.DEBIT ? sign * userLeaveTypeTransactionData.getLeaveCount() : 0d));
                } else {
                    UserLeaveTypeBalance existingBalance = leaveTypeBalanceMap.get(leaveTypeId);
                    leaveTypeBalanceMap.put(leaveTypeId, UserLeaveTypeBalance.get(leaveTypeId, existingBalance.getTotalCreditCount() +
                                    (type == LeaveTransactionType.CREDIT ?
                                            sign * userLeaveTypeTransactionData.getLeaveCount() : 0d),
                            existingBalance.getTotalDebitCount() +
                                    (type == LeaveTransactionType.DEBIT ? sign * userLeaveTypeTransactionData.getLeaveCount() : 0d)));
                }
            }
        }
        return userLeaveTypeBalanceMap;
    }

    /**
     * This method must be used within transaction only
     */
    public boolean softDeleteUserLeaveTransaction(int instituteId, int academicSessionId, Set<UUID> transactionIds) {
        List<Object[]> args = new ArrayList<>();
        for (UUID transactionId : transactionIds) {
            args.add(new Object[]{LeaveTransactionStatus.DELETED.name(), instituteId, academicSessionId, transactionId.toString()});
        }

        int[] mappingRows = jdbcTemplate.batchUpdate(UPDATE_USER_LEAVE_TRANSACTION_STATUS, args);
        if (mappingRows.length != args.size()) {
            logger.error("Unable to update leave transaction status for institute {}, transactionIds {}", instituteId, transactionIds);
            throw new EmbrateRunTimeException("Unable to update leave transaction status");
        }
        return true;
    }

    /**
     * This method must be used within transaction only
     */
    public Set<UUID> addNewUserLeaveTransaction(int instituteId, List<UserLeaveTransactionDetails> userLeaveTransactionDetailsList) {
        List<Object[]> metadataArgs = new ArrayList<>();
        List<Object[]> mappingArgs = new ArrayList<>();
        Set<UUID> transactionIds = new HashSet<>();
        for (UserLeaveTransactionDetails userLeaveTransactionDetails : userLeaveTransactionDetailsList) {
            UUID transactionId = userLeaveTransactionDetails.getMetadata().getTransactionId() == null ? UUID.randomUUID() : userLeaveTransactionDetails.getMetadata().getTransactionId();
            transactionIds.add(transactionId);

            UserLeaveTransactionMetadata metadata = userLeaveTransactionDetails.getMetadata();
            metadataArgs.add(new Object[]{instituteId, transactionId.toString(), metadata.getUserId().toString(), metadata.getUserType().name(),
                    metadata.getAcademicSessionId(), metadata.getTransactionType().name(), metadata.getTransactionStatus().name(), metadata.getCategory().name(),
                    metadata.getAppliedBy() == null ? null : metadata.getAppliedBy().toString(), new Timestamp(System.currentTimeMillis()),
                    metadata.getDescription() == null ? null : metadata.getDescription().trim(),
                    metadata.getReviewedBy() == null ? null : metadata.getReviewedBy().toString(), metadata.getReviewedAt() == null ? null :
                    new Timestamp(metadata.getReviewedAt() * 1000l),
                    metadata.getReviewRemarks() == null ? null : metadata.getReviewRemarks().trim()});

            for (UserLeaveTypeTransactionData userLeaveTypeTransactionData : userLeaveTransactionDetails.getLeaveTypeTransactionDataList()) {
                mappingArgs.add(new Object[]{instituteId, transactionId.toString(), userLeaveTypeTransactionData.getLeaveType().getLeaveTypeId(), userLeaveTypeTransactionData.getStartDate(), userLeaveTypeTransactionData.getLeaveCount()});
            }
        }

        int[] metadataRows = jdbcTemplate.batchUpdate(INSERT_USER_LEAVE_TRANSACTION_METADATA, metadataArgs);
        if (metadataRows.length != metadataArgs.size()) {
            logger.error("Unable to add leave transaction metadata for institute {}", instituteId);
            throw new EmbrateRunTimeException("Unable to add leave transaction metadata");
        }

        int[] mappingRows = jdbcTemplate.batchUpdate(INSERT_USER_LEAVE_TRANSACTION_MAPPING, mappingArgs);
        if (mappingRows.length != mappingArgs.size()) {
            logger.error("Unable to add leave transaction mapping for institute {}", instituteId);
            throw new EmbrateRunTimeException("Unable to add leave transaction mapping");
        }
        return transactionIds;
    }

    public boolean uploadLeaveDocument(UUID transactionId, List<Document<LeaveDocumentType>> leaveAttachments) {
        try {
            final Object[] args = { GSON.toJson(leaveAttachments), transactionId.toString() };
            return jdbcTemplate.update(UPDATE_LEAVE_DOCUMENTS, args) == 1;
        } catch (final DataAccessException dataAccessException) {
            ExceptionHandling.HandleException(dataAccessException, null, null);
            logger.error("Error while updating leave documents for transaction id {}", transactionId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while updating leave documents for transaction id {}", transactionId, e);
        }
        return false;
    }

    /**
     * This method must be used within transaction only
     */
    private void updateLeaveBalance(int instituteId, int academicSessionId, Map<UUID, List<UserLeaveTypeBalance>> finalUserLeaveTypeBalanceMap) {
        List<Object[]> args = new ArrayList<>();
        for (Map.Entry<UUID, List<UserLeaveTypeBalance>> entry : finalUserLeaveTypeBalanceMap.entrySet()) {
            for (UserLeaveTypeBalance userLeaveTypeBalance : entry.getValue()) {
                args.add(new Object[]{instituteId, academicSessionId, entry.getKey().toString(), userLeaveTypeBalance.getLeaveType().getLeaveTypeId(), userLeaveTypeBalance.getOpeningBalance(), userLeaveTypeBalance.getTotalCreditCount(), userLeaveTypeBalance.getTotalDebitCount(), userLeaveTypeBalance.getTotalCreditCount(), userLeaveTypeBalance.getTotalDebitCount()});
            }
        }

        int[] mappingRows = jdbcTemplate.batchUpdate(UPSERT_USER_LEAVE_BALANCE, args);
        if (mappingRows.length != args.size()) {
            logger.error("Unable to update leave balance for institute {}, academicSessionId {}, finalUserLeaveTypeBalanceMap {}", instituteId, academicSessionId, finalUserLeaveTypeBalanceMap);
            throw new EmbrateRunTimeException("Unable to update leave balance");
        }
    }

    public boolean updateLeaveReviewDetails(int instituteId, LeaveReviewPayload leaveReviewPayload) {
        try {
            final Object[] args = { leaveReviewPayload.getTransactionStatus().name(),
                    leaveReviewPayload.getReviewedByUser().toString(), new Timestamp(leaveReviewPayload.getReviewedAt() * 1000l),
                    leaveReviewPayload.getReviewRemarks() == null ? null : leaveReviewPayload.getReviewRemarks().trim(),
                    instituteId, leaveReviewPayload.getAcademicSessionId(), leaveReviewPayload.getTransactionId().toString() };

            return jdbcTemplate.update(UPDATE_USER_LEAVE_TRANSACTION_REVIEW_DETAILS, args) == 1;
        } catch (final DataAccessException dataAccessException) {
            ExceptionHandling.HandleException(dataAccessException, null, null);
            logger.error("Error while updating leave review details for transaction id {}", leaveReviewPayload.getTransactionId(), dataAccessException);
        } catch (final Exception e) {
            logger.error("Error while updating leave review details for transaction id {}", leaveReviewPayload.getTransactionId(), e);
        }
        return false;
    }
}
