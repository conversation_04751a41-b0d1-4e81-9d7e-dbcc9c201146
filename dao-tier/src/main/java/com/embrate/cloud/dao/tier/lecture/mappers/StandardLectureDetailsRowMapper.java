/**
 * 
 */
package com.embrate.cloud.dao.tier.lecture.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;

import com.embrate.cloud.core.api.lecture.LectureAndStats;
import com.embrate.cloud.core.api.lecture.LectureDetails;
import com.embrate.cloud.core.api.lecture.StandardCoursesLectureDetails;
import com.embrate.cloud.core.api.lecture.StandardLectureDetails;
import com.embrate.cloud.core.api.lecture.StandardLectureDetailsRow;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.institute.Stream;
import com.lernen.cloud.dao.tier.course.mappers.CourseRowMapper;
import com.lernen.cloud.dao.tier.institute.mappers.StandardRowDetailsRowMapper;

/**
 * <AUTHOR>
 *
 */
public class StandardLectureDetailsRowMapper implements RowMapper<StandardLectureDetailsRow> {

	private static final StandardRowDetailsRowMapper STANDARD_ROW_DETAILS_ROW_MAPPER = new StandardRowDetailsRowMapper();
	private static final CourseRowMapper COURSE_ROW_MAPPER = new CourseRowMapper();
	private static final LectureDetailsRowMapper LECTURE_DETAILS_ROW_MAPPER = new LectureDetailsRowMapper();

	protected static final String INSTITUTE_ID = "standards.institute_id";
	protected static final String COUNT = "n.count";

	@Override
	public StandardLectureDetailsRow mapRow(ResultSet rs, int rowNum) throws SQLException {

		final Standard standard = StandardRowDetailsRowMapper
				.getStandardResponse(STANDARD_ROW_DETAILS_ROW_MAPPER.mapRow(rs, rowNum));
		final Course course = COURSE_ROW_MAPPER.mapRow(rs, rowNum);
		final LectureDetails lectureDetails = LECTURE_DETAILS_ROW_MAPPER.mapRow(rs, rowNum);

		return new StandardLectureDetailsRow(rs.getInt(INSTITUTE_ID), standard, course, lectureDetails,
				rs.getInt(COUNT));
	}

	/**
	 * This method expects that all the rows belong to same session and standard
	 * 
	 * @param standradLectureDetailsRowList
	 * @return
	 */
	public static StandardLectureDetails getLectureDetailsByStandard(
			List<StandardLectureDetailsRow> standradLectureDetailsRowList) {

		if (CollectionUtils.isEmpty(standradLectureDetailsRowList)) {
			return null;
		}

		// standard section map
		Map<Integer, StandardSections> standardSectionMap = new HashMap<Integer, StandardSections>();

		// course Id and course
		Map<UUID, Course> courseMap = new HashMap<UUID, Course>();
		// course lecture stats map
		Map<UUID, Map<UUID, LectureAndStats>> lectureStats = new HashMap<UUID, Map<UUID, LectureAndStats>>();

		for (StandardLectureDetailsRow standradLectureDetailsRow : standradLectureDetailsRowList) {

			// standard section map
			for (StandardSections standardSection : standradLectureDetailsRow.getStandard().getStandardSectionList()) {
				if (!standardSectionMap.containsKey(standardSection.getSectionId())) {
					standardSectionMap.put(standardSection.getSectionId(), standardSection);
				}
			}

			/**
			 * For case where there are no lectures for given session and standard. Here just
			 * return the empty lecture list but want to show standard
			 */
			if (standradLectureDetailsRow.getCourse() == null) {
				continue;
			}
			if (standradLectureDetailsRow.getLectureDetails() == null) {
				continue;
			}

			// course Map
			if (!courseMap.containsKey(standradLectureDetailsRow.getCourse().getCourseId())) {
				courseMap.put(standradLectureDetailsRow.getCourse().getCourseId(),
						standradLectureDetailsRow.getCourse());
			}

			UUID courseId = standradLectureDetailsRow.getCourse().getCourseId();
			if (!lectureStats.containsKey(courseId)) {
				Map<UUID, LectureAndStats> lectureAndStats = new HashMap<>();
				lectureAndStats.put(standradLectureDetailsRow.getLectureDetails().getLectureId(),
						new LectureAndStats(standradLectureDetailsRow.getLectureDetails(),
								standradLectureDetailsRow.getNumberOfStudentMarkedCompleted()));

				lectureStats.put(courseId, lectureAndStats);
			} else if (!lectureStats.get(courseId)
					.containsKey(standradLectureDetailsRow.getLectureDetails().getLectureId())) {
				lectureStats.get(courseId).put(standradLectureDetailsRow.getLectureDetails().getLectureId(),
						new LectureAndStats(standradLectureDetailsRow.getLectureDetails(),
								standradLectureDetailsRow.getNumberOfStudentMarkedCompleted()));
			}

		}

		List<StandardCoursesLectureDetails> standardCoursesLectureDetailsList = new ArrayList<StandardCoursesLectureDetails>();
		for (Map.Entry<UUID, Course> courseEntry : courseMap.entrySet()) {
			List<LectureAndStats> lectureAndStatsList = new ArrayList<>(
					lectureStats.get(courseEntry.getKey()).values());
			standardCoursesLectureDetailsList
					.add(new StandardCoursesLectureDetails(courseEntry.getValue(), lectureAndStatsList));
		}
		List<StandardSections> standardSectionList = new ArrayList<StandardSections>(standardSectionMap.values());
		Standard standard = new Standard(standradLectureDetailsRowList.get(0).getInstituteId(),
				standradLectureDetailsRowList.get(0).getStandard().getAcademicSessionId(),
				standradLectureDetailsRowList.get(0).getStandard().getStandardId(),
				standradLectureDetailsRowList.get(0).getStandard().getStandardName(),
				standradLectureDetailsRowList.get(0).getStandard().getStream(),
				standradLectureDetailsRowList.get(0).getStandard().getLevel(), standardSectionList);

		return new StandardLectureDetails(standradLectureDetailsRowList.get(0).getInstituteId(), standard,
				standardCoursesLectureDetailsList);
	}

}
