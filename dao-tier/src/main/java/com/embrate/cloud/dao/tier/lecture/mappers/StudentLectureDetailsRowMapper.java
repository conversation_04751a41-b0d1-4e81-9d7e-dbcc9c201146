/**
 * 
 */
package com.embrate.cloud.dao.tier.lecture.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;

import com.embrate.cloud.core.api.homework.StudentHomeworkAndSubmissionDetails;
import com.embrate.cloud.core.api.homework.StudentHomeworkDetailsRow;
import com.embrate.cloud.core.api.lecture.CoursesLectureDetails;
import com.embrate.cloud.core.api.lecture.LectureAndViewDetails;
import com.embrate.cloud.core.api.lecture.LectureDetails;
import com.embrate.cloud.core.api.lecture.StudentLectureCompleteDetails;
import com.embrate.cloud.core.api.lecture.StudentLectureDetails;
import com.embrate.cloud.core.api.lecture.StudentLectureDetailsRow;
import com.embrate.cloud.core.api.lecture.StudentLectureViewDetails;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.dao.tier.course.mappers.CourseRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.StudentRowMapper;

/**
 * <AUTHOR>
 *
 */
public class StudentLectureDetailsRowMapper implements RowMapper<StudentLectureDetailsRow> {

	private static final CourseRowMapper COURSE_ROW_MAPPER = new CourseRowMapper();
	private static final StudentRowMapper STUDENT_ROW_MAPPER = new StudentRowMapper();
	private static final LectureDetailsRowMapper LECTURE_DETAILS_ROW_MAPPER = new LectureDetailsRowMapper();
	private static final StudentLectureViewDetailsRowMapper STUDENT_LECTURE_VIEW_DETAILS_ROW_MAPPER = new StudentLectureViewDetailsRowMapper();
	private static final StudentLectureCompleteDetailsRowMapper STUDENT_LECTURE_COMPLETE_DETAILS_ROW_MAPPER = new StudentLectureCompleteDetailsRowMapper();

	protected static final String INSTITUTE_ID = "students.institute_id";

	@Override
	public StudentLectureDetailsRow mapRow(ResultSet rs, int rowNum) throws SQLException {

		final StudentLite studentLite = Student.getStudentLite(STUDENT_ROW_MAPPER.mapRow(rs, rowNum));
		final Course course = COURSE_ROW_MAPPER.mapRow(rs, rowNum);
		final LectureDetails lectureDetails = LECTURE_DETAILS_ROW_MAPPER.mapRow(rs, rowNum);
		final StudentLectureViewDetails studentLectureViewDetails = STUDENT_LECTURE_VIEW_DETAILS_ROW_MAPPER.mapRow(rs,
				rowNum);
		final StudentLectureCompleteDetails studentLectureCompletedDetails = STUDENT_LECTURE_COMPLETE_DETAILS_ROW_MAPPER
				.mapRow(rs, rowNum);

		return new StudentLectureDetailsRow(rs.getInt(INSTITUTE_ID), studentLite, course, lectureDetails,
				studentLectureViewDetails, studentLectureCompletedDetails);
	}

	public static StudentLectureDetails getStudentLectureDetails(
			List<StudentLectureDetailsRow> studentLectureDetailsRowList) {
		if (CollectionUtils.isEmpty(studentLectureDetailsRowList)) {
			return null;
		}

		// course Id and course
		Map<UUID, Course> courseMap = new HashMap<UUID, Course>();
		// course Id and lecture details
		Map<UUID, Map<UUID, LectureDetails>> courseLectureMap = new HashMap<UUID, Map<UUID, LectureDetails>>();
		// lecture Id and student view details
		Map<UUID, List<StudentLectureViewDetails>> studentLectureViewMap = new HashMap<UUID, List<StudentLectureViewDetails>>();
		// lecture Id and student complete details
		Map<UUID, StudentLectureCompleteDetails> studentLectureCompleteMap = new HashMap<UUID, StudentLectureCompleteDetails>();

		for (StudentLectureDetailsRow studentLectureDetailsRow : studentLectureDetailsRowList) {

			// course Map
			if (!courseMap.containsKey(studentLectureDetailsRow.getCourse().getCourseId())) {
				courseMap.put(studentLectureDetailsRow.getCourse().getCourseId(), studentLectureDetailsRow.getCourse());
			}

			UUID courseId = studentLectureDetailsRow.getCourse().getCourseId();
			if (!courseLectureMap.containsKey(courseId)) {
				Map<UUID, LectureDetails> lectureMap = new HashMap<UUID, LectureDetails>();
				lectureMap.put(studentLectureDetailsRow.getLectureDetails().getLectureId(),
						studentLectureDetailsRow.getLectureDetails());
				courseLectureMap.put(courseId, lectureMap);
			} else if (!courseLectureMap.get(courseId)
					.containsKey(studentLectureDetailsRow.getLectureDetails().getLectureId())) {
				courseLectureMap.get(courseId).put(studentLectureDetailsRow.getLectureDetails().getLectureId(),
						studentLectureDetailsRow.getLectureDetails());
			}

			// studentViewMap
			if (studentLectureDetailsRow.getStudentLectureViewDetails() != null) {
				if (!studentLectureViewMap.containsKey(studentLectureDetailsRow.getLectureDetails().getLectureId())) {
					List<StudentLectureViewDetails> studentLectureViewDetails = new ArrayList<StudentLectureViewDetails>();
					studentLectureViewDetails.add(studentLectureDetailsRow.getStudentLectureViewDetails());
					studentLectureViewMap.put(studentLectureDetailsRow.getLectureDetails().getLectureId(),
							studentLectureViewDetails);
				} else {
					studentLectureViewMap.get(studentLectureDetailsRow.getLectureDetails().getLectureId())
							.add(studentLectureDetailsRow.getStudentLectureViewDetails());
				}
			}

			// studentCompleteMap
			if (studentLectureDetailsRow.getStudentLectureCompleteDetails() != null && !studentLectureCompleteMap
					.containsKey(studentLectureDetailsRow.getLectureDetails().getLectureId())) {
				studentLectureCompleteMap.put(studentLectureDetailsRow.getLectureDetails().getLectureId(),
						studentLectureDetailsRow.getStudentLectureCompleteDetails());
			}

		}
		List<CoursesLectureDetails> coursesLectureDetailsList = new ArrayList<CoursesLectureDetails>();
		for (Map.Entry<UUID, Course> courseEntry : courseMap.entrySet()) {
			List<LectureAndViewDetails> studentLectureViewDetailsList = new ArrayList<LectureAndViewDetails>();
			for (LectureDetails lectureDetails : courseLectureMap.get(courseEntry.getKey()).values()) {
				studentLectureViewDetailsList.add(new LectureAndViewDetails(lectureDetails,
						studentLectureViewMap.get(lectureDetails.getLectureId()),
						studentLectureCompleteMap.get(lectureDetails.getLectureId())));
			}
			coursesLectureDetailsList
					.add(new CoursesLectureDetails(courseEntry.getValue(), studentLectureViewDetailsList));
		}
		
		return new StudentLectureDetails(studentLectureDetailsRowList.get(0).getInstituteId(),
				studentLectureDetailsRowList.get(0).getStudentLite(), coursesLectureDetailsList);
	}

	public static LectureAndViewDetails getStudentLectureDetailsByLectureId(List<StudentLectureDetailsRow> studentLectureDetailsRowList) {
		if(CollectionUtils.isEmpty(studentLectureDetailsRowList)) {
			return null;
		}
		
		List<StudentLectureViewDetails> studentLectureViewDetails = new ArrayList<StudentLectureViewDetails>();

		Map<Integer, StandardSections> sectionMap = new HashMap<Integer, StandardSections>();
		for(StudentLectureDetailsRow studentLectureDetailsRow : studentLectureDetailsRowList) {
			if(!CollectionUtils.isEmpty(studentLectureDetailsRow.getLectureDetails().getStandard().getStandardSectionList())) {
				StandardSections firstRow = studentLectureDetailsRow.getLectureDetails().getStandard().getStandardSectionList().get(0);
				if(!sectionMap.containsKey(firstRow.getSectionId())) {
					sectionMap.put(firstRow.getSectionId(), firstRow);
				}
			}
			if(studentLectureDetailsRow.getStudentLectureViewDetails() != null) {
				studentLectureViewDetails.add(studentLectureDetailsRow.getStudentLectureViewDetails());
			}
		}
		
		studentLectureDetailsRowList.get(0).getLectureDetails().getStandard()
			.setStandardSectionList(new ArrayList<StandardSections>(sectionMap.values()));
		
		return new LectureAndViewDetails(studentLectureDetailsRowList.get(0).getLectureDetails(), studentLectureViewDetails.size() 
				> 0 ? studentLectureViewDetails : null, studentLectureDetailsRowList.get(0).getStudentLectureCompleteDetails());
	
	}
}
