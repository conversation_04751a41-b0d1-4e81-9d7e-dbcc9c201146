/**
 * 
 */
package com.embrate.cloud.dao.tier.noticeboard.mappers;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import com.embrate.cloud.core.api.noticeboard.NoticeBoardDocumentType;
import com.embrate.cloud.core.api.noticeboard.NoticeBoardStatus;
import com.embrate.cloud.core.api.noticeboard.UserNoticeDetails;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.user.Document;

/**
 * <AUTHOR>
 *
 */
public class UserNoticeDetailsRowMapper  implements RowMapper<UserNoticeDetails>{

	private static final Gson GSON = new Gson();
	
	protected static final String INSTITUTE_ID = "notice_details.institute_id";
	protected static final String NOTICE_ID = "notice_details.notice_id";
	protected static final String TITLE = "notice_details.title";
	protected static final String BODY = "notice_details.body";
	protected static final String ATTACHMENTS = "notice_details.attachments";
	protected static final String CREATED_BY = "notice_details.created_by";
	protected static final String CREATED_TIMESTAMP = "notice_details.created_timestamp";
	protected static final String UPDATED_BY = "notice_details.updated_by";
	protected static final String UPDATED_TIMESTAMP = "notice_details.updated_timestamp";
	protected static final String EXPIRY_DATE = "notice_details.expiry_date";
	protected static final String PINNED = "notice_details.pinned";
	protected static final String PINNED_BY = "notice_details.pinned_by";
	protected static final String PINNED_TIMESTAMP = "notice_details.pinned_timestamp";
	protected static final String STATUS = "notice_details.status";
	protected static final String BROADCASTED_BY = "notice_details.broadcasted_by";
	protected static final String BROADCASTED_TIMESTAMP = "notice_details.broadcasted_timestamp";
	
	@Override
	public UserNoticeDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
		String noticeIdStr = rs.getString(NOTICE_ID);
		if(StringUtils.isBlank(noticeIdStr)) {
			return null;
		}
		
		final Timestamp createdTimestamp = rs.getTimestamp(CREATED_TIMESTAMP);
		final Integer createdTimestampTime = createdTimestamp == null ? null
				: (int) (createdTimestamp.getTime() / 1000l);
		
		final Timestamp updatedTimestamp = rs.getTimestamp(UPDATED_TIMESTAMP);
		final Integer updatedTimestampTime = updatedTimestamp == null ? null
				: (int) (updatedTimestamp.getTime() / 1000l);
		
		final Timestamp expiryDate = rs.getTimestamp(EXPIRY_DATE);
		final Integer expiryDateTime = expiryDate == null ? null
				: (int) (expiryDate.getTime() / 1000l);
		
		final Timestamp broadcastAtDate = rs.getTimestamp(BROADCASTED_TIMESTAMP);
		final Integer broadcastAtDateTime = broadcastAtDate == null ? null
				: (int) (broadcastAtDate.getTime() / 1000l);

		final Timestamp pinnedTimestampDate = rs.getTimestamp(PINNED_TIMESTAMP);
		final Integer pinnedTimestampDateTime = pinnedTimestampDate == null ? null
				: (int) (pinnedTimestampDate.getTime() / 1000l);
		
		final String documents = rs.getString(ATTACHMENTS);
		List<Document<NoticeBoardDocumentType>> noticeBoardAttachments = null;
		if (!StringUtils.isBlank(documents)) {
			final Type collectionType = new TypeToken<List<Document<NoticeBoardDocumentType>>>() {
			}.getType();
			noticeBoardAttachments = GSON.fromJson(documents, collectionType);
		}
		
		int attachmentCount = 10;
		long attachmentSize = 1024 * 1024 * 5;
		String allowedMimeTypes = "image/*,application/pdf";

		return new UserNoticeDetails(rs.getInt(INSTITUTE_ID), UUID.fromString(noticeIdStr), rs.getString(TITLE),
				rs.getString(BODY), UUID.fromString(rs.getString(CREATED_BY)), createdTimestampTime,
				rs.getString(UPDATED_BY) == null ? null : UUID.fromString(rs.getString(UPDATED_BY)), updatedTimestampTime,
				expiryDateTime, rs.getBoolean(PINNED),
				rs.getString(PINNED_BY) == null ? null : UUID.fromString(rs.getString(PINNED_BY)), pinnedTimestampDateTime, 
				NoticeBoardStatus.getNoticeBoardStatus(rs.getString(STATUS)),
				rs.getString(BROADCASTED_BY) == null ? null : UUID.fromString(rs.getString(BROADCASTED_BY)), broadcastAtDateTime,	
				attachmentCount, attachmentSize, allowedMimeTypes, noticeBoardAttachments);
	}

}
