/**
 * 
 */
package com.embrate.cloud.dao.tier.push.notification.mappers;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.dao.tier.user.mappers.UserRowMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import com.embrate.cloud.core.api.service.notification.BellNotificationDetails;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

/**
 * <AUTHOR>
 *
 */
public class BellNotificationDetailsRowMapper implements RowMapper<BellNotificationDetails> {

	private static final Gson GSON = new Gson();
	
	protected static final String INSTITUTE_ID = "bell_notification_details_latest.institute_id";
	protected static final String NOTIFICATION_ID = "bell_notification_details_latest.notification_id";
	protected static final String TITLE = "bell_notification_details_latest.title";
	protected static final String BODY = "bell_notification_details_latest.body";
	protected static final String CREATED_ON = "bell_notification_details_latest.created_on";
	protected static final String ENTITY_NAME = "bell_notification_details_latest.entity_name";
	protected static final String ENTITY_ID = "bell_notification_details_latest.entity_id";
	protected static final String LIST_OPENED_ON = "bell_notification_details_latest.list_opened_on";
	protected static final String CLICKED_ON = "bell_notification_details_latest.clicked_on";
	protected static final String META_DATA = "bell_notification_details_latest.meta_data";

	private static final UserRowMapper USER_ROW_MAPPER = new UserRowMapper();

	@Override
	public BellNotificationDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
		if (rs.getString(NOTIFICATION_ID) == null) {
			return null;
		}

		User user = USER_ROW_MAPPER.mapRow(rs, rowNum);

		final Timestamp createdOn = rs.getTimestamp(CREATED_ON);
		final Integer createdOnTime = createdOn == null ? null
				: (int) (createdOn.getTime() / 1000l);
		
		final Timestamp listOpenedOn = rs.getTimestamp(LIST_OPENED_ON);
		final Integer listOpenedOnTime = listOpenedOn == null ? null
				: (int) (listOpenedOn.getTime() / 1000l);
		
		final Timestamp clickedOn = rs.getTimestamp(CLICKED_ON);
		final Integer clickedOnTime = clickedOn == null ? null
				: (int) (clickedOn.getTime() / 1000l);
		
		final String metaDataStr = rs.getString(META_DATA);
		Map<String, String> metaData = null;
		if (!StringUtils.isBlank(metaDataStr)) {
			final Type collectionType = new TypeToken<Map<String, String>>() {
			}.getType();
			metaData = GSON.fromJson(metaDataStr, collectionType);
		}
		
		return new BellNotificationDetails(rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(NOTIFICATION_ID)),
				rs.getString(TITLE), rs.getString(BODY), user, NotificationEntity.getNotificationEntity(
				rs.getString(ENTITY_NAME)), rs.getString(ENTITY_ID) == null ? null : UUID.fromString(rs.getString(ENTITY_ID)),
				createdOnTime, listOpenedOnTime, clickedOnTime, metaData);
	}
}