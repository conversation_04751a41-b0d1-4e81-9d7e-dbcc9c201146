/**
 *
 */
package com.embrate.cloud.dao.tier.salary.mappers;

import com.embrate.cloud.core.api.salary.AdvanceTransactionCategory;
import com.embrate.cloud.core.api.salary.FinalAdvanceDetails;
import com.embrate.cloud.core.api.salary.StaffAdvanceData;
import com.lernen.cloud.core.utils.UUIDUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 *
 */
public class FinalAdvanceDetailsRowMapper implements RowMapper<StaffAdvanceData> {

    public static final String INSTITUTE_ID = "institute_id";
    public static final String STAFF_ID = "staff_id";
    public static final String AMOUNT = "amount";
    public static final String TRANSACTION_DATE = "transaction_date";
    public static final String TRANSACTION_CATEGORY = "transaction_category";

    @Override
    public StaffAdvanceData mapRow(ResultSet rs, int rowNum) throws SQLException {

        final Timestamp transactionDate = rs.getTimestamp(TRANSACTION_DATE);
        final Integer transactionDateTime = transactionDate == null ? null : (int) (transactionDate.getTime() / 1000l);

        return new StaffAdvanceData(rs.getInt(INSTITUTE_ID), UUIDUtils.getUUID(rs.getString(STAFF_ID)),
                AdvanceTransactionCategory.getAdvanceTransactionCategory(rs.getString(TRANSACTION_CATEGORY)), rs.getDouble(AMOUNT), transactionDateTime);
    }

    // It assumes all rows belongs to same staff
    public static FinalAdvanceDetails getAdvanceDetails(List<StaffAdvanceData> advanceDetailsList) {
        if (CollectionUtils.isEmpty(advanceDetailsList)) {
            return null;
        }

        Double totalAmount = 0d;
        Double dueAmount = 0d;

        int instituteId = advanceDetailsList.get(0).getInstituteId();
        UUID staffId = advanceDetailsList.get(0).getStaffId();
        Integer dateofLastAdvance = null;
        Boolean flag = true;
        for (StaffAdvanceData advanceDetails : advanceDetailsList) {
            // ADVANCE amount is store -ve
            dueAmount += advanceDetails.getAmount();
            if (advanceDetails.getAdvanceTransactionCategory() == AdvanceTransactionCategory.ADVANCE) {
                totalAmount += Math.abs(advanceDetails.getAmount());
                if (flag) {
                    dateofLastAdvance = advanceDetails.getTransactionDateTime();
                    flag = false;
                }
                if (advanceDetails.getTransactionDateTime() > dateofLastAdvance) {
                    dateofLastAdvance = advanceDetails.getTransactionDateTime();
                }
            }
        }

        dueAmount = dueAmount < 0 ? dueAmount * -1 : dueAmount;
        totalAmount = totalAmount < 0 ? totalAmount * -1 : totalAmount;

        return new FinalAdvanceDetails(instituteId, staffId, totalAmount, dueAmount, totalAmount - dueAmount, dateofLastAdvance);
    }

    public static List<FinalAdvanceDetails> getAllStaffAdvanceDetails(List<StaffAdvanceData> advanceDetailsList) {
        if (CollectionUtils.isEmpty(advanceDetailsList)) {
            return new ArrayList<>();
        }

        Map<UUID, List<StaffAdvanceData>> staffMap = new HashMap<>();
        for (StaffAdvanceData advanceDetails : advanceDetailsList) {
            UUID staffId = advanceDetails.getStaffId();
            if (!staffMap.containsKey(staffId)) {
                staffMap.put(staffId, new ArrayList<>());
            }
            staffMap.get(staffId).add(advanceDetails);
        }

        List<FinalAdvanceDetails> finalAdvanceDetailsList = new ArrayList<>();
        for (Map.Entry<UUID, List<StaffAdvanceData>> entry : staffMap.entrySet()) {
            FinalAdvanceDetails staffFinalAdvanceDetails = getAdvanceDetails(entry.getValue());
            if (staffFinalAdvanceDetails == null) {
                continue;
            }
            finalAdvanceDetailsList.add(staffFinalAdvanceDetails);
        }

        return finalAdvanceDetailsList;
    }
}
