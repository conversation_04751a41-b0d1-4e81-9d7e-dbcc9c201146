/**
 * 
 */
package com.embrate.cloud.dao.tier.salary.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.embrate.cloud.core.api.salary.PayHeadAmount;
import com.embrate.cloud.core.api.salary.PayHeadConfiguration;
import com.embrate.cloud.core.api.salary.SalaryPayslip;
import com.embrate.cloud.core.api.salary.StaffPayslipDetailsRow;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffBasicInfo;
import com.lernen.cloud.dao.tier.staff.mappers.StaffBasicInfoRowMapper;
import com.lernen.cloud.dao.tier.staff.mappers.StaffRowMapper;

/**
 * <AUTHOR>
 *
 */
public class StaffPayslipDetailsRowMapper implements RowMapper<StaffPayslipDetailsRow> {

	public static final String INSTITUTE_ID = "salary_payslip_meta_data.institute_id";
	public static final String PAYSLIP_ID = "salary_payslip_meta_data.payslip_id";
	public static final String STAFF_ID = "salary_payslip_meta_data.staff_id";
	public static final String SALARY_CYCLE_START = "salary_payslip_meta_data.salary_cycle_start";
	public static final String STATUS = "salary_payslip_meta_data.status";
	public static final String ADVANCE_AMOUNT = "salary_payslip_meta_data.advance";
	public static final String ADVANCE_TRNASACTION_ID = "salary_payslip_meta_data.advance_transaction_id";
	public static final String DESCRIPTION = "salary_payslip_meta_data.description";
	public static final String PAY_HEAD_ID = "salary_payhead_details.pay_head_id";
	public static final String AMOUNT = "salary_payhead_details.amount";

	private static final PayHeadConfigurationRowMapper PAY_HEAD_CONFIGURATION_ROW_MAPPER = new PayHeadConfigurationRowMapper();
	private static final StaffRowMapper STAFF_ROW_MAPPER = new StaffRowMapper();
	
	@Override
	public StaffPayslipDetailsRow mapRow(ResultSet rs, int rowNum) throws SQLException {

		final PayHeadConfiguration payHeadConfiguration = PAY_HEAD_CONFIGURATION_ROW_MAPPER.mapRow(rs, rowNum);
		final Staff staff = STAFF_ROW_MAPPER.mapRow(rs, rowNum);
		
		return new StaffPayslipDetailsRow(rs.getInt(INSTITUTE_ID), rs.getString(PAYSLIP_ID) == null ? null : UUID.fromString(rs.getString(PAYSLIP_ID)), 
				 staff, rs.getInt(SALARY_CYCLE_START), 
				rs.getString(STATUS) == null ? null : FeePaymentTransactionStatus.getFeePaymentTransactionStatus(rs.getString(STATUS)), 
				rs.getString(DESCRIPTION), payHeadConfiguration, rs.getDouble(AMOUNT), rs.getDouble(ADVANCE_AMOUNT), 
				rs.getString(ADVANCE_TRNASACTION_ID) == null ? null : UUID.fromString(rs.getString(ADVANCE_TRNASACTION_ID)));
		
	}

	public static List<SalaryPayslip> getStaffPayslipDetailsPayload(List<StaffPayslipDetailsRow> staffPayslipDetailsRowList) {
		
		Map<UUID, SalaryPayslip> salaryPayslipPayloadMap = new HashMap<UUID, SalaryPayslip>();
		
		for (StaffPayslipDetailsRow staffPayslipDetailsRow : staffPayslipDetailsRowList) {
			if(salaryPayslipPayloadMap.containsKey(staffPayslipDetailsRow.getPayslipId())) {				
				salaryPayslipPayloadMap.get(staffPayslipDetailsRow.getPayslipId()).getPayHeadAmountMap().add(new PayHeadAmount(staffPayslipDetailsRow.getPayHeadConfiguration(), staffPayslipDetailsRow.getAmount()));
			}
			else {
				List<PayHeadAmount> payHeadAmount = new ArrayList<PayHeadAmount>();
				payHeadAmount.add(new PayHeadAmount(staffPayslipDetailsRow.getPayHeadConfiguration(), staffPayslipDetailsRow.getAmount()));
				salaryPayslipPayloadMap.put(staffPayslipDetailsRow.getPayslipId(), new SalaryPayslip(staffPayslipDetailsRow.getInstituteId(),
						staffPayslipDetailsRow.getPayslipId(), staffPayslipDetailsRow.getStaff(), staffPayslipDetailsRow.getStatus(),
						staffPayslipDetailsRow.getSalaryCycleStart(), payHeadAmount, staffPayslipDetailsRow.getAdvanceAmount(), staffPayslipDetailsRow.getAdvanceTransactinId(), staffPayslipDetailsRow.getDescription()));
			}
		}
		return new ArrayList<SalaryPayslip>(salaryPayslipPayloadMap.values());
	}

	public static SalaryPayslip getStaffPayslipDetailsPayloadByCycle(List<StaffPayslipDetailsRow> staffPayslipDetailsRowList) {
		List<PayHeadAmount> payHeadAmountMap = new ArrayList<PayHeadAmount>();
		for (StaffPayslipDetailsRow staffPayslipDetailsRow : staffPayslipDetailsRowList) {
			payHeadAmountMap.add(new PayHeadAmount(staffPayslipDetailsRow.getPayHeadConfiguration(), staffPayslipDetailsRow.getAmount()));
		}
		return new SalaryPayslip(staffPayslipDetailsRowList.get(0).getInstituteId(), staffPayslipDetailsRowList.get(0).getPayslipId(),
				staffPayslipDetailsRowList.get(0).getStaff(), staffPayslipDetailsRowList.get(0).getStatus(),
				staffPayslipDetailsRowList.get(0).getSalaryCycleStart(), payHeadAmountMap, 
				staffPayslipDetailsRowList.get(0).getAdvanceAmount(),staffPayslipDetailsRowList.get(0).getAdvanceTransactinId(), staffPayslipDetailsRowList.get(0).getDescription());
	}
}
