/**
 *
 */
package com.embrate.cloud.dao.tier.salary.mappers;

import com.embrate.cloud.core.api.salary.PayHeadAmount;
import com.embrate.cloud.core.api.salary.PayHeadConfiguration;
import com.embrate.cloud.core.api.salary.StructureStatus;
import com.embrate.cloud.core.api.salary.v2.SalaryCycle;
import com.embrate.cloud.core.api.salary.v2.SalaryCycleDetails;
import com.embrate.cloud.core.api.salary.v2.SalaryStructureMetadata;
import com.embrate.cloud.core.api.salary.v2.StaffIdSalaryStructureMetadata;
import com.embrate.cloud.core.api.salary.v2.user.StaffSalaryCycleStructureDetails;
import com.embrate.cloud.core.api.salary.v2.user.StaffSalaryStructureDetails;
import com.lernen.cloud.core.utils.UUIDUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 */
public class StaffSalaryStructureDetailsRowMapper implements RowMapper<StaffSalaryCycleStructureDetails> {

    public static final String SALARY_CYCLE_START = "staff_salary_structure_mapping.cycle_id";
    public static final String AMOUNT = "staff_salary_structure_mapping.amount";

    public static final String INSTITUTE_ID = "staff_salary_structure_metadata.institute_id";
    public static final String ACADEMIC_SESSION_ID = "staff_salary_structure_metadata.academic_session_id";
    public static final String STRUCTURE_ID = "staff_salary_structure_metadata.structure_id";
    public static final String STAFF_ID = "staff_salary_structure_metadata.staff_id";
    public static final String STRUCTURE_NAME = "staff_salary_structure_metadata.structure_name";
    public static final String DESCRIPTION = "staff_salary_structure_metadata.description";
    public static final String STATUS = "staff_salary_structure_metadata.status";

    private static final SalaryCycleRowMapper SALARY_CYCLE_ROW_MAPPER = new SalaryCycleRowMapper();
    private static final PayHeadConfigurationRowMapper PAY_HEAD_CONFIGURATION_ROW_MAPPER = new PayHeadConfigurationRowMapper();

    @Override
    public StaffSalaryCycleStructureDetails mapRow(ResultSet rs, int rowNum) throws SQLException {

        final SalaryCycle salaryCycle = SALARY_CYCLE_ROW_MAPPER.mapRow(rs, rowNum);
        final PayHeadConfiguration payHeadConfiguration = PAY_HEAD_CONFIGURATION_ROW_MAPPER.mapRow(rs, rowNum);

        SalaryCycleDetails salaryCycleDetails = new SalaryCycleDetails(salaryCycle, Arrays.asList(new PayHeadAmount(payHeadConfiguration, rs.getDouble(AMOUNT))));
        return new StaffSalaryCycleStructureDetails(new StaffIdSalaryStructureMetadata(UUIDUtils.getUUID(rs.getString(STAFF_ID)), new SalaryStructureMetadata(rs.getInt(ACADEMIC_SESSION_ID), UUID.fromString(rs.getString(STRUCTURE_ID)),
                rs.getString(STRUCTURE_NAME), StructureStatus.getStructureStatus(rs.getString(STATUS)), null)), salaryCycleDetails);
    }

    // This method assumes that all rows belongs to same staff and session id
    public static StaffSalaryStructureDetails getSalaryStructureDetails(List<StaffSalaryCycleStructureDetails> salaryCycleStructureDetailsList) {
        if (CollectionUtils.isEmpty(salaryCycleStructureDetailsList)) {
            return null;
        }

        Map<Integer, List<PayHeadAmount>> cyclePayHeadAmountMap = new LinkedHashMap<>();
        Map<Integer, SalaryCycle> salaryCycleMap = new HashMap<>();

        for (StaffSalaryCycleStructureDetails staffSalaryCycleStructureDetails : salaryCycleStructureDetailsList) {
            SalaryCycleDetails salaryCycleDetails = staffSalaryCycleStructureDetails.getSalaryCycleDetails();
            salaryCycleMap.put(salaryCycleDetails.getSalaryCycle().getCycleId(), salaryCycleDetails.getSalaryCycle());

            if (cyclePayHeadAmountMap.containsKey(salaryCycleDetails.getSalaryCycle().getCycleId())) {
                cyclePayHeadAmountMap.get(salaryCycleDetails.getSalaryCycle().getCycleId()).addAll(salaryCycleDetails.getPayHeadAmountList());
            } else {
                List<PayHeadAmount> payHeadAmount = new ArrayList<>(salaryCycleDetails.getPayHeadAmountList());
                cyclePayHeadAmountMap.put(salaryCycleDetails.getSalaryCycle().getCycleId(), payHeadAmount);
            }
        }
        List<SalaryCycleDetails> salaryCycleDetailsList = new ArrayList<>();
        for (Entry<Integer, List<PayHeadAmount>> entry : cyclePayHeadAmountMap.entrySet()) {
            salaryCycleDetailsList.add(new SalaryCycleDetails(salaryCycleMap.get(entry.getKey()), entry.getValue()));
        }

        return new StaffSalaryStructureDetails(salaryCycleStructureDetailsList.get(0).getStaffIdSalaryStructureMetadata().getMetadata(), salaryCycleDetailsList);
    }

    // This method assumes that all rows belongs to same session and institute id only and only single salary cycle
    // per staff would be present
    public static List<StaffSalaryCycleStructureDetails> getStaffSalaryCycleStructureDetails(List<StaffSalaryCycleStructureDetails> salaryCycleStructureDetailsList) {
        if (CollectionUtils.isEmpty(salaryCycleStructureDetailsList)) {
            return null;
        }

        Map<UUID, List<StaffSalaryCycleStructureDetails>> staffMap = new HashMap<>();
        for (StaffSalaryCycleStructureDetails staffSalaryCycleStructureDetails : salaryCycleStructureDetailsList) {
            UUID staffId = staffSalaryCycleStructureDetails.getStaffIdSalaryStructureMetadata().getStaffId();
            if (!staffMap.containsKey(staffId)) {
                staffMap.put(staffId, new ArrayList<>());
            }
            staffMap.get(staffId).add(staffSalaryCycleStructureDetails);
        }
        List<StaffSalaryCycleStructureDetails> salaryCycleDetailsList = new ArrayList<>();
        for (Entry<UUID, List<StaffSalaryCycleStructureDetails>> entry : staffMap.entrySet()) {
            StaffSalaryStructureDetails staffSalaryStructureDetails = getSalaryStructureDetails(entry.getValue());
            if (staffSalaryStructureDetails == null || CollectionUtils.isEmpty(staffSalaryStructureDetails.getSalaryCycleDetailsList())) {
                continue;
            }
            salaryCycleDetailsList.add(new StaffSalaryCycleStructureDetails(new StaffIdSalaryStructureMetadata(entry.getKey(), staffSalaryStructureDetails.getMetadata()), staffSalaryStructureDetails.getSalaryCycleDetailsList().get(0)));
        }

        return salaryCycleDetailsList;
    }
}