package com.embrate.cloud.dao.tier.scratchcard;

import com.embrate.cloud.core.api.scratchcard.ScratchCardDetails;
import com.embrate.cloud.dao.tier.scratchcard.mappers.ScratchCardDetailsRowMapper;
import com.google.gson.Gson;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.exceptions.ExceptionHandling;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class ScratchCardDao {

    private static final Logger logger = LogManager.getLogger(ScratchCardDao.class);

    private final JdbcTemplate jdbcTemplate;

    private final TransactionTemplate transactionTemplate;

    private static final Gson GSON = SharedConstants.GSON;

    private static final ScratchCardDetailsRowMapper SCRATCH_CARD_DETAILS_ROW_MAPPER = new ScratchCardDetailsRowMapper();

    private static final String ADD_SCRATCH_CARD_DETAILS = " insert into transaction_scratch_card_rewards_mapping "
            + " (scratch_card_id, transaction_id, user_id, scratch_card_type, due_date, expiry_date, is_scratched, scratched_on, rewards_details, metadata) "
            + " values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

    private static final String GET_ALL_SCRATCH_CARD_DETAILS = " select * from transaction_scratch_card_rewards_mapping "
            + " where transaction_scratch_card_rewards_mapping.user_id = ? ";

    private static final String GET_SCRATCH_CARD_DETAILS_BY_ID = " select * from transaction_scratch_card_rewards_mapping "
            + " where transaction_scratch_card_rewards_mapping.scratch_card_id = ? ";

    private static final String TRANSACTION_ID_CLAUSE = " and transaction_scratch_card_rewards_mapping.transaction_id = ? ";

    private static final String UPDATE_SCRATCH_CARD_STATUS = " update transaction_scratch_card_rewards_mapping" +
            " set is_scratched = ?, scratched_on = ? where scratch_card_id = ? ";

    public ScratchCardDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
    }


    public List<UUID> addScratchCardDetails(List<ScratchCardDetails> scratchCardDetailsList) {
        final List<Object[]> batchInsertArgs = new ArrayList<>();
        List<UUID> scratchCardIds = new ArrayList<UUID>();
        for (final ScratchCardDetails scratchCardDetails : scratchCardDetailsList) {
            final List<Object> args = new ArrayList<>();
            UUID scratchCardId = UUID.randomUUID();
            scratchCardIds.add(scratchCardId);
            args.add(scratchCardId.toString());
            args.add(scratchCardDetails.getTransactionId().toString());
            args.add(scratchCardDetails.getUserId().toString());
            args.add(scratchCardDetails.getScratchCardType().name());
            args.add(new Timestamp(scratchCardDetails.getRewardDetails().getDueDate() * 1000l));
            args.add(new Timestamp(scratchCardDetails.getRewardDetails().getExpiryDate() * 1000l));
            args.add(scratchCardDetails.isScratched());
            args.add(scratchCardDetails.isScratched() ? new Timestamp(System.currentTimeMillis()) : null);
            args.add(GSON.toJson(scratchCardDetails.getRewardDetails()));
            args.add(null);
            batchInsertArgs.add(args.toArray());
        }

        try {
            final int[] rows = jdbcTemplate.batchUpdate(ADD_SCRATCH_CARD_DETAILS, batchInsertArgs);
            if (rows.length != scratchCardIds.size()) {
                return null;
            }
            for (final int rowCount : rows) {
                if (rowCount != 1) {
                    return null;
                }
            }
            return scratchCardIds;
        } catch (final Exception e) {
            logger.error("Error while adding scratch card details ", e);
        }
        return null;
    }

    public List<ScratchCardDetails> getScratchCardDetails(int instituteId, UUID userId, UUID transactionId) {
        try {
            final List<Object> args = new ArrayList<Object>();
            String query = GET_ALL_SCRATCH_CARD_DETAILS;
            args.add(userId.toString());
            if(transactionId != null) {
                query += TRANSACTION_ID_CLAUSE;
                args.add(transactionId.toString());
            }
            return jdbcTemplate.query (query, args.toArray(), SCRATCH_CARD_DETAILS_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error while adding scratch card details ", e);
        }
        return null;
    }

    public ScratchCardDetails getScratchCardDetailsByScratchCardId(int instituteId, UUID scratchCardId) {
        try {
            final List<Object> args = new ArrayList<Object>();
            args.add(scratchCardId.toString());
            return jdbcTemplate.queryForObject(GET_SCRATCH_CARD_DETAILS_BY_ID, args.toArray(), SCRATCH_CARD_DETAILS_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error while adding scratch card details ", e);
        }
        return null;
    }

    public boolean updateScratchCardStatus(int instituteId, UUID scratchCardId, ScratchCardDetails scratchCardDetails) {
        try {
            boolean isScratched = true;
            final Object[] args = { isScratched, new Timestamp(System.currentTimeMillis()), scratchCardId.toString() };
            if(!(jdbcTemplate.update(UPDATE_SCRATCH_CARD_STATUS, args) == 1)) {
                throw new RuntimeException("Unable to mark scratch card as scratched.");
            }
            return true;
        } catch (final DataAccessException dataAccessException) {
            ExceptionHandling.HandleException(dataAccessException, null, null);
            logger.error(dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while updating scratch card status for scratch card id {}", scratchCardId, e);
        }
        return false;
    }
}
