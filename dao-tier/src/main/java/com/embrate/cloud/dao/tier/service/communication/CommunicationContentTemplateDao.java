package com.embrate.cloud.dao.tier.service.communication;

import com.embrate.cloud.core.api.wallet.WalletTransactionPayload;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.common.DBLockMode;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.Timestamp;
import java.util.UUID;

public class CommunicationContentTemplateDao {

	private static final Logger logger = LogManager.getLogger(CommunicationContentTemplateDao.class);

	private final JdbcTemplate jdbcTemplate;
	private final TransactionTemplate transactionTemplate;

	private static final Gson GSON = new Gson();

	private static final String INSERT_WALLET_TRANSACTION = "insert into user_wallet_transaction_history(institute_id, transaction_id, user_id, "
			+ "user_type, transaction_category, transaction_mode, transaction_date, transaction_status, amount, transaction_by, transaction_add_at, meta_data, description) "
			+ "values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String UPDATE_USER_WALLET = "update user_wallet set net_amount = net_amount + ? where institute_id = ? and user_id = ? and user_type = ?";

	private static final String GET_USER_WALLET_AMOUNT = "select net_amount from user_wallet where user_id = ? %s";

	public CommunicationContentTemplateDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
		this.jdbcTemplate = jdbcTemplate;
		this.transactionTemplate = transactionTemplate;
	}

	public boolean addUserWalletTransaction(WalletTransactionPayload walletTransactionPayload) {
		try {
			final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {

				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					return addUserWalletTransactionNonAtomic(walletTransactionPayload);
				}

			});
			return success.booleanValue();
		} catch (final Exception e) {
			logger.error("Unable to execute wallet transaction for institute {}, user {} ",
					walletTransactionPayload.getInstituteId(), walletTransactionPayload.getUserId(), e);
		}
		return false;
	}

	/**
	 * Only use this method inside a transaction
	 * 
	 * @param walletTransactionPayload
	 * @return
	 */
	public boolean addUserWalletTransactionNonAtomic(WalletTransactionPayload walletTransactionPayload) {
		if (!addWalletTransaction(walletTransactionPayload)) {
			throw new EmbrateRunTimeException("Unable to add wallet transaction history");
		}

		if (!updateUserWalletAmount(walletTransactionPayload)) {
			throw new EmbrateRunTimeException("Unable to update student wallet amount");
		}
		return true;
	}

	public Double getUserWalletAmount(UUID userId, DBLockMode dbLockMode) {
		if (dbLockMode == null) {
			dbLockMode = DBLockMode.NONE;
		}
		try {
			Double amount = jdbcTemplate.queryForObject(String.format(GET_USER_WALLET_AMOUNT, dbLockMode.getCommand()),
					new Object[] { userId.toString() }, Double.class);
			return amount == null ? 0d : amount;
		} catch (final Exception e) {
			logger.error("Unable get wallet amount for user {}", userId, e);
		}
		return null;
	}

	public boolean updateUserWalletAmount(WalletTransactionPayload walletTransactionPayload) {
		try {
			return jdbcTemplate.update(UPDATE_USER_WALLET, walletTransactionPayload.getAmount(),
					walletTransactionPayload.getInstituteId(), walletTransactionPayload.getUserId().toString(),
					walletTransactionPayload.getUserType().name()) == 1;
		} catch (final Exception e) {
			logger.error("Unable to update student wallet amount", e);
		}
		return false;
	}

	private boolean addWalletTransaction(WalletTransactionPayload walletTransactionPayload) {
		UUID transactionId = UUID.randomUUID();
		try {
			return jdbcTemplate.update(INSERT_WALLET_TRANSACTION, walletTransactionPayload.getInstituteId(),
					transactionId.toString(), walletTransactionPayload.getUserId().toString(),
					walletTransactionPayload.getUserType().name(),
					walletTransactionPayload.getWalletTransactionCategory().name(),
					walletTransactionPayload.getTransactionMode() == null ? null
							: walletTransactionPayload.getTransactionMode().name(),
					walletTransactionPayload.getTransactionDate() == null ? new Timestamp(DateUtils.now() * 1000l)
							: new Timestamp(walletTransactionPayload.getTransactionDate() * 1000l),
					walletTransactionPayload.getTransactionStatus() == null ? null
							: walletTransactionPayload.getTransactionStatus().name(),
					walletTransactionPayload.getAmount(), walletTransactionPayload.getTransactionBy().toString(),
					new Timestamp(DateUtils.now() * 1000l),
					walletTransactionPayload.getMetaData() == null ? null
							: GSON.toJson(walletTransactionPayload.getMetaData()),
					walletTransactionPayload.getDescription()) == 1;
		} catch (final Exception e) {
			logger.error("Unable add wallet transaction for user {}", walletTransactionPayload.getUserId(), e);
		}
		return false;
	}
}
