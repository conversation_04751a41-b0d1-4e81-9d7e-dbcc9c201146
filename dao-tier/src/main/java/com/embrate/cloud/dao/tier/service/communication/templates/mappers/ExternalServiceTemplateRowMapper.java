package com.embrate.cloud.dao.tier.service.communication.templates.mappers;

import com.embrate.cloud.core.api.service.communication.templates.CommunicationTemplate;
import com.embrate.cloud.core.api.service.communication.templates.ExternalServiceTemplateData;
import com.embrate.cloud.core.api.service.communication.templates.TemplateStatus;
import com.embrate.cloud.core.api.service.communication.templates.TemplateType;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.configurations.Entity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

/**
 *
 * <AUTHOR>
 *
 */
public class ExternalServiceTemplateRowMapper implements RowMapper<ExternalServiceTemplateData> {
	@Override
	public ExternalServiceTemplateData mapRow(ResultSet rs, int rowNum) throws SQLException {

		final CommunicationServiceProvider communicationServiceProvider = CommunicationServiceProvider.valueOf(rs.getString("service_provider"));
		final TemplateStatus status = TemplateStatus.valueOf(rs.getString("status"));

		final Type metaDataMap = new TypeToken<Map<String, Object>>() {
		}.getType();
		Map<String, Object> metaData = new HashMap<>();
		if (!StringUtils.isBlank(rs.getString("metadata"))) {
			metaData = GSON.fromJson(rs.getString("metadata"), metaDataMap);
		}

		return new ExternalServiceTemplateData(communicationServiceProvider, UUID.fromString(rs.getString("template_id")), rs.getString("external_template_id"), status,
				metaData, (int) (rs.getTimestamp("added_at").getTime() / 1000l));

	}
}
