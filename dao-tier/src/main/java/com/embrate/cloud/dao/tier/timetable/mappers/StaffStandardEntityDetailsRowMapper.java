package com.embrate.cloud.dao.tier.timetable.mappers;

import com.embrate.cloud.core.api.timetable.*;
import com.lernen.cloud.core.api.institute.StandardRowDetails;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.dao.tier.institute.mappers.StandardRowDetailsRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;


public class StaffStandardEntityDetailsRowMapper implements RowMapper<StaffStandardEntityDetailsRow> {

    public static final String ACADEMIC_SESSION_ID = "staff_entity_assignment_academic_session_id";

    private static final CoursesActivityDetailsRowMapper COURSES_ACTIVITY_DETAILS_ROW_MAPPER = new CoursesActivityDetailsRowMapper();

    private static final StandardRowDetailsRowMapper STANDARD_ROW_DETAILS_ROW_MAPPER = new StandardRowDetailsRowMapper();

    @Override
    public StaffStandardEntityDetailsRow mapRow(ResultSet rs, int rowNum) throws SQLException {
        CoursesActivityDetailsRow coursesActivityDetailsRow = COURSES_ACTIVITY_DETAILS_ROW_MAPPER.mapRow(rs, rowNum);

        Entity entity = coursesActivityDetailsRow.getEntity();
        Staff staff = coursesActivityDetailsRow.getStaff();
        UUID entityId = coursesActivityDetailsRow.getEntityId();
        String entityVal = coursesActivityDetailsRow.getEntityIdVal();

        int academicSessionId = rs.getInt(ACADEMIC_SESSION_ID);

        StandardRowDetails standardRowDetails = STANDARD_ROW_DETAILS_ROW_MAPPER.mapRow(rs, rowNum);

        return new StaffStandardEntityDetailsRow(academicSessionId, staff, standardRowDetails, entity,
                entityId, entityVal);
    }

    /**
     *
     * @param staffStandardEntityDetailsRowList
     * @return
     * Assuming all entries os of single staff
     */
    public static StaffStandardEntityDetails getStaffStandardEntityDetails(
            List<StaffStandardEntityDetailsRow> staffStandardEntityDetailsRowList) {
        if(CollectionUtils.isEmpty(staffStandardEntityDetailsRowList)) {
            return null;
        }
        Map<String, StandardEntityDetails> standardEntityDetailsMap = new HashMap<>();
        for(StaffStandardEntityDetailsRow staffStandardEntityDetailsRow : staffStandardEntityDetailsRowList) {
            StandardRowDetails standardRowDetails = staffStandardEntityDetailsRow.getStandardRowDetails();
            EntityDetails entityDetails = new EntityDetails(staffStandardEntityDetailsRow.getEntity(),
                    staffStandardEntityDetailsRow.getEntityId(), staffStandardEntityDetailsRow.getEntityIdVal());
            UUID standardId = staffStandardEntityDetailsRow.getStandardRowDetails().getStandardId();
            Integer sectionId = staffStandardEntityDetailsRow.getStandardRowDetails().getSectionId();
            String standardIdStr = standardId.toString() +
                    (sectionId == null || sectionId <= 0 ? "" : sectionId.toString());
            if(standardEntityDetailsMap.containsKey(standardIdStr)) {
                StandardEntityDetails standardEntityDetails = standardEntityDetailsMap.get(standardIdStr);
                if(!standardEntityDetails.containsEntity(entityDetails)) {
                    standardEntityDetails.getEntityDetailsList().add(entityDetails);
                }
            } else {

                List<EntityDetails> entityDetailsList = new ArrayList<>();
                entityDetailsList.add(entityDetails);
                StandardEntityDetails standardEntityDetails = new StandardEntityDetails(standardRowDetails,
                        entityDetailsList);
                standardEntityDetailsMap.put(standardIdStr, standardEntityDetails);
            }
        }

        int academicSessionId = staffStandardEntityDetailsRowList.get(0).getAcademicSessionId();
        Staff staff = staffStandardEntityDetailsRowList.get(0).getStaff();
        return new StaffStandardEntityDetails(academicSessionId, staff,
                new ArrayList<>(standardEntityDetailsMap.values()));
    }


}
