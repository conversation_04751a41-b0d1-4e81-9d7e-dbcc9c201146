/**
 * 
 */
package com.embrate.cloud.dao.tier.timetable.mappers;

import com.embrate.cloud.core.api.timetable.*;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.dao.tier.course.mappers.CourseRowMapper;
import com.lernen.cloud.dao.tier.institute.mappers.StandardRowDetailsRowMapper;
import com.lernen.cloud.dao.tier.staff.mappers.StaffRowMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.DayOfWeek;
import java.util.*;

/**
 * <AUTHOR>
 *
 */
public class TimetableDetailsRowMapper  implements RowMapper<TimetableDetailsRow> {

	public static final String INSTITUTE_ID = "timetable_metadata.institute_id";
	public static final String ACADEMIC_SESSION_ID = "timetable_metadata.academic_session_id";
	public static final String SHIFT_ID = "timetable_metadata.shift_id";
	public static final String STANDARD_ID = "timetable_metadata.standard_id";
	public static final String SECTION_ID = "timetable_metadata.section_id";
	public static final String TIMETABLE_ID = "timetable_metadata.timetable_id";
	public static final String TIMETABLE_NAME = "timetable_metadata.timetable_name";
	public static final String STATUS = "timetable_metadata.status";

	public static final String CREATED_ON = "timetable_metadata.created_on";
	public static final String CREATED_BY = "timetable_metadata.created_by";
	public static final String UPDATED_ON = "timetable_metadata.updated_on";
	public static final String UPDATED_BY = "timetable_metadata.updated_by";
	public static final String PUBLISHED_ON = "timetable_metadata.published_on";
	public static final String PUBLISHED_BY = "timetable_metadata.published_by";

	public static final String SLOT_ID = "timetable_slot_details.slot_id";
//	public static final String TIMETABLE_ID = "timetable_slot_details.timetable_id";
	public static final String PERIOD_ID = "timetable_slot_details.period_id";
	public static final String DAY = "timetable_slot_details.day";
	public static final String PERIOD_CATEGORY = "timetable_slot_details.period_category";
	public static final String PERIOD_SLOT_CATEGORY_ID = "timetable_slot_details.period_slot_category_id";
	public static final String ROOM_NUMBER = "timetable_slot_details.room_number";
	public static final String STAFF_ID = "timetable_slot_details.staff_id";

	private static final ShiftDetailsRowMapper SHIFT_DETAILS_ROW_MAPPER = new ShiftDetailsRowMapper();
	private static final StandardRowDetailsRowMapper STANDARD_ROW_DETAILS_ROW_MAPPER = new StandardRowDetailsRowMapper();
	private static final CourseRowMapper COURSE_ROW_MAPPER = new CourseRowMapper();
	private static final StaffRowMapper STAFF_ROW_MAPPER = new StaffRowMapper();
	private static final ActivityMetadataRowMapper ACTIVITY_METADATA_ROW_MAPPER = new ActivityMetadataRowMapper();
	
	@Override
	public TimetableDetailsRow mapRow(ResultSet rs, int rowNum) throws SQLException {
		
		final Standard standard = StandardRowDetailsRowMapper.getStandardResponse(
				STANDARD_ROW_DETAILS_ROW_MAPPER.mapRow(rs, rowNum));
		final SchoolShiftDetailsRow schoolShiftDetailsRow = SHIFT_DETAILS_ROW_MAPPER.mapRow(rs, rowNum);
		final Staff staff = STAFF_ROW_MAPPER.mapRow(rs, rowNum);
		final ShiftPeriodDetails shiftPeriodDetails = new ShiftPeriodDetails(schoolShiftDetailsRow.getPeriodId(),
				schoolShiftDetailsRow.isHavePeriod(), schoolShiftDetailsRow.getPeriodName(),
				schoolShiftDetailsRow.getPeriodStartTime(), schoolShiftDetailsRow.getPeriodEndTime());

		if(PeriodSlotCategory.getPeriodSlotCategory(rs.getString(PERIOD_CATEGORY)) == PeriodSlotCategory.COURSE) {
			final Course course = COURSE_ROW_MAPPER.mapRow(rs, rowNum);
			return new TimetableDetailsRow(rs.getInt(INSTITUTE_ID),
					rs.getInt(ACADEMIC_SESSION_ID),
					schoolShiftDetailsRow,
					rs.getString(TIMETABLE_ID) == null ? null : UUID.fromString(rs.getString(TIMETABLE_ID)),
					rs.getString(TIMETABLE_NAME),
					standard,
					rs.getString(STATUS) == null ? null : TimetableStatus.getTimetableStatus(rs.getString(STATUS)),
					rs.getString(CREATED_BY) == null ? null : UUID.fromString(rs.getString(CREATED_BY)),
					rs.getString(UPDATED_BY) == null ? null : UUID.fromString(rs.getString(UPDATED_BY)),
					rs.getString(DAY) == null ? null : DayOfWeek.valueOf(rs.getString(DAY)),
					shiftPeriodDetails,
					rs.getString(SLOT_ID) == null ? null : UUID.fromString(rs.getString(SLOT_ID)),
					rs.getString(PERIOD_CATEGORY) == null ? null : PeriodSlotCategory.getPeriodSlotCategory(rs.getString(PERIOD_CATEGORY)),
					course, rs.getString(ROOM_NUMBER), staff);
		} else if(PeriodSlotCategory.getPeriodSlotCategory(rs.getString(PERIOD_CATEGORY)) == PeriodSlotCategory.ACTIVITY) {
			final ActivityMetadata activityMetadata = ACTIVITY_METADATA_ROW_MAPPER.mapRow(rs, rowNum);
			return new TimetableDetailsRow(rs.getInt(INSTITUTE_ID),
					rs.getInt(ACADEMIC_SESSION_ID),
					schoolShiftDetailsRow,
					rs.getString(TIMETABLE_ID) == null ? null : UUID.fromString(rs.getString(TIMETABLE_ID)),
					rs.getString(TIMETABLE_NAME),
					standard,
					rs.getString(STATUS) == null ? null : TimetableStatus.getTimetableStatus(rs.getString(STATUS)),
					rs.getString(CREATED_BY) == null ? null : UUID.fromString(rs.getString(CREATED_BY)),
					rs.getString(UPDATED_BY) == null ? null : UUID.fromString(rs.getString(UPDATED_BY)),
					rs.getString(DAY) == null ? null : DayOfWeek.valueOf(rs.getString(DAY)),
					shiftPeriodDetails,
					rs.getString(SLOT_ID) == null ? null : UUID.fromString(rs.getString(SLOT_ID)),
					rs.getString(PERIOD_CATEGORY) == null ? null : PeriodSlotCategory.getPeriodSlotCategory(rs.getString(PERIOD_CATEGORY)),
					activityMetadata, rs.getString(ROOM_NUMBER), staff);
		} else {
			return new TimetableDetailsRow(rs.getInt(INSTITUTE_ID),
					rs.getInt(ACADEMIC_SESSION_ID),
					schoolShiftDetailsRow,
					rs.getString(TIMETABLE_ID) == null ? null : UUID.fromString(rs.getString(TIMETABLE_ID)),
					rs.getString(TIMETABLE_NAME),
					standard,
					rs.getString(STATUS) == null ? null : TimetableStatus.getTimetableStatus(rs.getString(STATUS)),
					rs.getString(CREATED_BY) == null ? null : UUID.fromString(rs.getString(CREATED_BY)),
					rs.getString(UPDATED_BY) == null ? null : UUID.fromString(rs.getString(UPDATED_BY)),
					rs.getString(DAY) == null ? null : DayOfWeek.valueOf(rs.getString(DAY)),
					shiftPeriodDetails,
					rs.getString(SLOT_ID) == null ? null : UUID.fromString(rs.getString(SLOT_ID)),
					rs.getString(PERIOD_CATEGORY) == null ? null : PeriodSlotCategory.getPeriodSlotCategory(rs.getString(PERIOD_CATEGORY)),
					null, rs.getString(ROOM_NUMBER), staff);
		}
	}

	/**
	 * it is expected that all timetable details is of one shift ID, section and standard id
	 */
	public static TimetableDetails getTimetableDetails(List<TimetableDetailsRow> timetableDetailsRowList) {

		if(CollectionUtils.isEmpty(timetableDetailsRowList)) {
			return null;
		}

		Map<DayOfWeek, Map<UUID, List<PeriodSlotDetails>>> timetableDayPeriodDetailsMap =
				 new HashMap<DayOfWeek, Map<UUID, List<PeriodSlotDetails>>>();

		Map<UUID, ShiftPeriodDetails> shiftPeriodDetailsMap = new HashMap<UUID, ShiftPeriodDetails>();

		for(TimetableDetailsRow timetableDetailsRow : timetableDetailsRowList) {

			if(timetableDetailsRow.getPeriodSlotCategory() == null) {
				continue;
			}

			PeriodSlotDetails periodSlotDetails = new PeriodSlotDetails(timetableDetailsRow.getSlotId(),
					timetableDetailsRow.getPeriodSlotCategory(),
					timetableDetailsRow.getPeriodSlotCategoryDetails(),
					timetableDetailsRow.getRoomNumber(), timetableDetailsRow.getStaff());

			if(timetableDayPeriodDetailsMap.containsKey(timetableDetailsRow.getDay())) {
				if(timetableDayPeriodDetailsMap.get(timetableDetailsRow.getDay())
						.containsKey(timetableDetailsRow.getShiftPeriodDetails().getPeriodId())) {
					timetableDayPeriodDetailsMap.get(timetableDetailsRow.getDay())
							.get(timetableDetailsRow.getShiftPeriodDetails().getPeriodId()).add(periodSlotDetails);
				} else {
					List<PeriodSlotDetails> periodSlotDetailsList = new ArrayList<PeriodSlotDetails>();
					periodSlotDetailsList.add(periodSlotDetails);
					timetableDayPeriodDetailsMap.get(timetableDetailsRow.getDay()).
						put(timetableDetailsRow.getShiftPeriodDetails().getPeriodId(),
						periodSlotDetailsList);
				}
			} else {
				List<PeriodSlotDetails> periodSlotDetailsList = new ArrayList<PeriodSlotDetails>();
				periodSlotDetailsList.add(periodSlotDetails);

				Map<UUID, List<PeriodSlotDetails>> timetableShiftPeriodDetailsMap = new HashMap<UUID, List<PeriodSlotDetails>>();

				timetableShiftPeriodDetailsMap.put(timetableDetailsRow.getShiftPeriodDetails().getPeriodId(),
						periodSlotDetailsList);

				timetableDayPeriodDetailsMap.put(timetableDetailsRow.getDay(), timetableShiftPeriodDetailsMap);
			}

			if(!shiftPeriodDetailsMap.containsKey(timetableDetailsRow.getSchoolShiftDetailsRow().getShiftId())) {
				shiftPeriodDetailsMap.put(timetableDetailsRow.getSchoolShiftDetailsRow()
						.getPeriodId(), timetableDetailsRow.getShiftPeriodDetails());
			}

		}

		TimetableDetailsRow timetableDetailsRowFirstRow = timetableDetailsRowList.get(0);
		SchoolShiftDetailsRow schoolShiftDetailsRow = timetableDetailsRowFirstRow.getSchoolShiftDetailsRow();

		SchoolShiftDetails schoolShiftDetails = new SchoolShiftDetails(schoolShiftDetailsRow.getInstituteId(),
				schoolShiftDetailsRow.getAcademicSessionId(), schoolShiftDetailsRow.getShiftId(),
				schoolShiftDetailsRow.getShiftName(), schoolShiftDetailsRow.getShiftStartTime(),
				schoolShiftDetailsRow.getShiftEndTime(), schoolShiftDetailsRow.getCreatedBy(),
				schoolShiftDetailsRow.getCreatedOn(), schoolShiftDetailsRow.getUpdatedBy(),
				schoolShiftDetailsRow.getUpdatedOn(), schoolShiftDetailsRow.getDays(),
				new ArrayList<>(shiftPeriodDetailsMap.values()));

		Map<DayOfWeek, List<TimetableShiftPeriodDetails>> timetableDayPeriodMap = null;

		if(timetableDetailsRowFirstRow.getTimetableId() != null) {
			timetableDayPeriodMap = new HashMap<DayOfWeek, List<TimetableShiftPeriodDetails>>();
			for(Map.Entry<DayOfWeek, Map<UUID, List<PeriodSlotDetails>>> entry1 : timetableDayPeriodDetailsMap.entrySet()) {
				List<TimetableShiftPeriodDetails> timetableShiftPeriodDetailsList =
					new ArrayList<TimetableShiftPeriodDetails>();
				for(Map.Entry<UUID, List<PeriodSlotDetails>> entry2 : entry1.getValue().entrySet()) {
					TimetableShiftPeriodDetails timetableShiftPeriodDetails =
							new TimetableShiftPeriodDetails(entry2.getKey(), entry2.getValue());
					timetableShiftPeriodDetailsList.add(timetableShiftPeriodDetails);
				}
				timetableDayPeriodMap.put(entry1.getKey(), timetableShiftPeriodDetailsList);
			}
		}

		return new TimetableDetails(schoolShiftDetailsRow.getInstituteId(),
				schoolShiftDetailsRow.getAcademicSessionId(), schoolShiftDetails,
				timetableDetailsRowFirstRow.getTimetableId(), timetableDetailsRowFirstRow.getTimetableName(),
				timetableDetailsRowFirstRow.getStandard(), timetableDetailsRowFirstRow.getTimetableStatus(),
				timetableDetailsRowFirstRow.getCreatedBy(), timetableDetailsRowFirstRow.getUpdatedBy(), timetableDayPeriodMap);
	}

	/**
	 *
	 * @param timetableDetailsRowList
	 * @return
	 *
	 * All the data belong to a single shift and day along multiple classes
	 * It is returning Map of (StandardId:SectionId) and (List of all the period) for a particular day
	 * Assuming for a single shift, standard and section - we will have only one publish timetable
	 *
	 */
	public static Map<String, List<TimetableShiftPeriodDetails>> getTimetableDetailsByShiftId(
			List<TimetableDetailsRow> timetableDetailsRowList, DayOfWeek day) {
		if(CollectionUtils.isEmpty(timetableDetailsRowList)) {
			return null;
		}

		// collecting all the timetable details in a map by standard id + section id
		Map<String, List<TimetableDetailsRow>> timetableDetailsRowMap = new HashMap<>();
		for(TimetableDetailsRow timetableDetailsRow : timetableDetailsRowList) {
			Standard standard = timetableDetailsRow.getStandard();
			UUID standardId = standard.getStandardId();
			if(!CollectionUtils.isEmpty(standard.getStandardSectionList())) {
				for(StandardSections standardSections : standard.getStandardSectionList()) {
					int sectionId = standardSections.getSectionId();
					String standardSectionId = standardId + ":" + sectionId;
					if(!timetableDetailsRowMap.containsKey(standardSectionId)) {
						timetableDetailsRowMap.put(standardSectionId, new ArrayList<>());
					}
					timetableDetailsRowMap.get(standardSectionId).add(timetableDetailsRow);
				}
			} else {
				String standardSectionId = standardId.toString();
				if(!timetableDetailsRowMap.containsKey(standardSectionId)) {
					timetableDetailsRowMap.put(standardSectionId, new ArrayList<>());
				}
				timetableDetailsRowMap.get(standardSectionId).add(timetableDetailsRow);
			}
		}

		Map<String, List<TimetableShiftPeriodDetails>> timetableShiftPeriodDetailsMap = new HashMap<>();
		for(Map.Entry<String, List<TimetableDetailsRow>> entry : timetableDetailsRowMap.entrySet()) {
			TimetableDetails timetableDetails = getTimetableDetails(entry.getValue());
			if(timetableDetails == null) {
				timetableShiftPeriodDetailsMap.put(entry.getKey(), new ArrayList<>());
				continue;
			}
			List<TimetableShiftPeriodDetails> timetableShiftPeriodDetailsList = timetableDetails.getTimetableDayPeriodDetailsMap().get(day);
			timetableShiftPeriodDetailsMap.put(entry.getKey(), timetableShiftPeriodDetailsList);
		}
		return timetableShiftPeriodDetailsMap;
	}

	/**
	 *
	 * it is expected that all timetable details belongs to same section and standard id
	 *
	 */
	public static List<TimetableDetails> getTimetableDetailsOfAllShifts(List<TimetableDetailsRow> timetableDetailsRowList) {
		if(CollectionUtils.isEmpty(timetableDetailsRowList)) {
			return null;
		}

		// collecting all the timetable details in a map by shift Id
		Map<UUID, List<TimetableDetailsRow>> timetableDetailsRowMap = new HashMap<UUID, List<TimetableDetailsRow>>();
		for(TimetableDetailsRow timetableDetailsRow : timetableDetailsRowList) {
			if(timetableDetailsRowMap.containsKey(timetableDetailsRow.getSchoolShiftDetailsRow().getShiftId())) {
				timetableDetailsRowMap.get(timetableDetailsRow.getSchoolShiftDetailsRow().getShiftId())
					.add(timetableDetailsRow);
			} else {
				List<TimetableDetailsRow> timetableDetailsRowNewList = new ArrayList<TimetableDetailsRow>();
				timetableDetailsRowNewList.add(timetableDetailsRow);
				timetableDetailsRowMap.put(timetableDetailsRow.getSchoolShiftDetailsRow().getShiftId(),
						timetableDetailsRowNewList);
			}
		}
		List<TimetableDetails> timetableDetailsList = new ArrayList<TimetableDetails>();
		for(Map.Entry<UUID, List<TimetableDetailsRow>> entry : timetableDetailsRowMap.entrySet()) {
			timetableDetailsList.add(getTimetableDetails(entry.getValue()));
		}
		return timetableDetailsList;
	}

	public static StudentTimetableDetails getStudentTimetableDetails(
			List<TimetableDetailsRow> timetableDetailsRowList) {

		List<TimetableDetails> timetableDetailsList = getTimetableDetailsOfAllShifts(timetableDetailsRowList);
		if(CollectionUtils.isEmpty(timetableDetailsList)) {
			return null;
		}
		Map<UUID, ShiftPeriodDetails> finalShiftPeriodDetailsMap = new HashMap<UUID, ShiftPeriodDetails>();
		Map<DayOfWeek, List<StudentTimetableShiftPeriodDetails>> studenTimeTableDetailsMap =
				new HashMap<DayOfWeek, List<StudentTimetableShiftPeriodDetails>>();
		for(TimetableDetails timetableDetails : timetableDetailsList) {

			Map<UUID, ShiftPeriodDetails> shiftPeriodDetailsMap = new HashMap<UUID, ShiftPeriodDetails>();
			for(ShiftPeriodDetails shiftPeriodDetails :
				timetableDetails.getSchoolShiftDetails().getShiftPeriodPayloadList()) {
				shiftPeriodDetailsMap.put(shiftPeriodDetails.getPeriodId(), shiftPeriodDetails);
			}

			for(Map.Entry<DayOfWeek, List<TimetableShiftPeriodDetails>> entry :
				timetableDetails.getTimetableDayPeriodDetailsMap().entrySet()) {

 				List<StudentTimetableShiftPeriodDetails> studentTimetableShiftPeriodDetailsList =
	 						new ArrayList<StudentTimetableShiftPeriodDetails>();

 				for(TimetableShiftPeriodDetails timetableShiftPeriodDetails : entry.getValue()) {

 					studentTimetableShiftPeriodDetailsList.add(new StudentTimetableShiftPeriodDetails(
 							shiftPeriodDetailsMap.get(timetableShiftPeriodDetails.getPeriodID()),
 							timetableShiftPeriodDetails.getPeriodSlotDetailsList()));

 				}

 				if(studenTimeTableDetailsMap.containsKey(entry.getKey())) {
 					if(!CollectionUtils.isEmpty(studentTimetableShiftPeriodDetailsList)) {
 						studenTimeTableDetailsMap.get(entry.getKey()).addAll(studentTimetableShiftPeriodDetailsList);
 					}
 				} else {
 					if(CollectionUtils.isEmpty(studentTimetableShiftPeriodDetailsList)) {
 						studenTimeTableDetailsMap.put(entry.getKey(), null);
 					} else {
 						studenTimeTableDetailsMap.put(entry.getKey(), studentTimetableShiftPeriodDetailsList);
 					}
 				}
			}

 			finalShiftPeriodDetailsMap.putAll(shiftPeriodDetailsMap);
		}

		return new StudentTimetableDetails(new ArrayList<ShiftPeriodDetails>(finalShiftPeriodDetailsMap.values()),
				studenTimeTableDetailsMap);
	}
}
