package com.embrate.cloud.dao.tier.utils;


import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class QueryUtils {

    public static <T> QueryArgumentData getListClause(Collection<T> queryCollection){
        if(CollectionUtils.isEmpty(queryCollection)){
            return new QueryArgumentData("", new ArrayList<>());
        }
        StringBuilder queryClauseBuilder = new StringBuilder();
        List<Object> args = new ArrayList<>();
        queryClauseBuilder.append("(");
        String delimiter = "";

        for(T entry : queryCollection){
            if(entry == null){
                continue;
            }
            args.add(entry.toString());
            queryClauseBuilder.append(delimiter).append("?");
            delimiter= ", ";
        }
        queryClauseBuilder.append(")");

        return new QueryArgumentData(queryClauseBuilder.toString(), args);

    }
}
