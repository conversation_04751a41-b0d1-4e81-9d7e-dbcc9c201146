package com.embrate.cloud.dao.tier.wallet;

import com.embrate.cloud.core.api.wallet.WalletTransactionPayload;
import com.embrate.cloud.dao.tier.wallet.mappers.UserWalletTransactionRowMapper;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.common.DBLockMode;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;

public class UserWalletDao {

    private static final Logger logger = LogManager.getLogger(UserWalletDao.class);
    private static final Gson GSON = new Gson();
    private static final UserWalletTransactionRowMapper USER_WALLET_TRANSACTION_ROW_MAPPER = new UserWalletTransactionRowMapper();
    private static final String INSERT_WALLET_TRANSACTION = "insert into user_wallet_transaction_history(institute_id, transaction_id, user_id, "
            + "user_type, transaction_category, transaction_mode, transaction_date, transaction_status, amount, transaction_by, transaction_add_at, meta_data, description) "
            + "values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    private static final String UPDATE_USER_WALLET = "update user_wallet set net_amount = net_amount + ? where institute_id = ? and user_id = ? and user_type = ?";
    private static final String GET_USER_WALLET_AMOUNT = "select net_amount from user_wallet where user_id = ? %s";
    private static final String GET_USER_WALLET_TRANSACTION = "select * from user_wallet_transaction_history where institute_id = ? and transaction_id = ?";
    private static final String GET_USER_WALLET_TRANSACTIONS = "select * from user_wallet_transaction_history where institute_id = ? and user_id = ? order by transaction_date desc";
    private static final String GET_USER_WALLET_TRANSACTIONS_BY_USER_TYPE_IN_RANGE = "select * from user_wallet_transaction_history where institute_id = ? and user_type = ? and transaction_date >= ? and transaction_date < ? order by transaction_date";
    private static final String UPDATE_WALLET_TRANSACTION_AMOUNT = 
            "update user_wallet_transaction_history set amount = ? where institute_id = ? and transaction_id = ?";
    private static final String UPDATE_USER_WALLET_AMOUNT_BY_DIFFERENCE = 
            "update user_wallet set net_amount = net_amount + ? where user_id = ? and institute_id = ?";
    private static final String DELETE_WALLET_TRANSACTION = " delete from user_wallet_transaction_history where institute_id = ? and transaction_id = ? ";
    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;

    public UserWalletDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
    }

    public WalletTransactionPayload getTransactionById(int instituteId, UUID transactionId) {
        try {
            return jdbcTemplate.queryForObject(GET_USER_WALLET_TRANSACTION, new Object[]{instituteId, transactionId.toString()}, USER_WALLET_TRANSACTION_ROW_MAPPER);
        } catch (Exception e) {
            logger.error("Error while getting the wallet transactions for instituteId {}, transactionId {}", instituteId, transactionId, e);
        }
        return null;
    }

    public List<WalletTransactionPayload> getUserWalletTransactions(int instituteId, UUID userId) {
        try {
            return jdbcTemplate.query(GET_USER_WALLET_TRANSACTIONS, new Object[]{instituteId, userId.toString()}, USER_WALLET_TRANSACTION_ROW_MAPPER);
        } catch (Exception e) {
            logger.error("Error while getting the wallet transactions for instituteId {}, userId {}", instituteId, userId, e);
        }
        return null;
    }

    public List<WalletTransactionPayload> getUserWalletTransactions(int instituteId, UserType userType, int start, int end) {
        try {
            return jdbcTemplate.query(GET_USER_WALLET_TRANSACTIONS_BY_USER_TYPE_IN_RANGE, new Object[]{instituteId, userType.name(), new Timestamp(start * 1000l), new Timestamp(end * 1000l)}, USER_WALLET_TRANSACTION_ROW_MAPPER);
        } catch (Exception e) {
            logger.error("Error while getting the wallet transactions for instituteId {}, userType {}, start {}, end {} ", instituteId, userType, start, end, e);
        }
        return null;
    }

    public UUID addUserWalletTransaction(WalletTransactionPayload walletTransactionPayload) {
        try {
            final UUID transactionId = transactionTemplate.execute(new TransactionCallback<UUID>() {

                @Override
                public UUID doInTransaction(TransactionStatus status) {
                    return addUserWalletTransactionNonAtomic(walletTransactionPayload);
                }

            });
            return transactionId;
        } catch (final Exception e) {
            logger.error("Unable to execute wallet transaction for institute {}, user {} ",
                    walletTransactionPayload.getInstituteId(), walletTransactionPayload.getUserId(), e);
        }
        return null;
    }

    /**
     * Only use this method inside a transaction
     *
     * @param walletTransactionPayload
     * @return
     */
    public UUID addUserWalletTransactionNonAtomic(WalletTransactionPayload walletTransactionPayload) {
        UUID transactionId = addWalletTransaction(walletTransactionPayload);
        if (transactionId == null) {
            throw new EmbrateRunTimeException("Unable to add wallet transaction history");
        }

        if (!updateUserWalletAmount(walletTransactionPayload)) {
            throw new EmbrateRunTimeException("Unable to update student wallet amount");
        }
        return transactionId;
    }

    public Double getUserWalletAmount(UUID userId, DBLockMode dbLockMode) {
        if (dbLockMode == null) {
            dbLockMode = DBLockMode.NONE;
        }
        try {
            Double amount = jdbcTemplate.queryForObject(String.format(GET_USER_WALLET_AMOUNT, dbLockMode.getCommand()),
                    new Object[]{userId.toString()}, Double.class);
            return amount == null ? 0d : amount;
        } catch (final Exception e) {
            logger.error("Unable get wallet amount for user {}", userId, e);
        }
        return null;
    }

    public boolean updateUserWalletAmount(WalletTransactionPayload walletTransactionPayload) {
        try {
            return jdbcTemplate.update(UPDATE_USER_WALLET, walletTransactionPayload.getAmount(),
                    walletTransactionPayload.getInstituteId(), walletTransactionPayload.getUserId().toString(),
                    walletTransactionPayload.getUserType().name()) == 1;
        } catch (final Exception e) {
            logger.error("Unable to update student wallet amount", e);
        }
        return false;
    }

    private UUID addWalletTransaction(WalletTransactionPayload walletTransactionPayload) {
        UUID transactionId = UUID.randomUUID();
        try {
            int row = jdbcTemplate.update(INSERT_WALLET_TRANSACTION, walletTransactionPayload.getInstituteId(),
                    transactionId.toString(), walletTransactionPayload.getUserId().toString(),
                    walletTransactionPayload.getUserType().name(),
                    walletTransactionPayload.getWalletTransactionCategory().name(),
                    walletTransactionPayload.getTransactionMode() == null ? null
                            : walletTransactionPayload.getTransactionMode().name(),
                    walletTransactionPayload.getTransactionDate() == null ? new Timestamp(DateUtils.now() * 1000l)
                            : new Timestamp(walletTransactionPayload.getTransactionDate() * 1000l),
                    walletTransactionPayload.getTransactionStatus() == null ? null
                            : walletTransactionPayload.getTransactionStatus().name(),
                    walletTransactionPayload.getAmount(), walletTransactionPayload.getTransactionBy().toString(),
                    new Timestamp(DateUtils.now() * 1000l),
                    walletTransactionPayload.getMetaData() == null ? null
                            : GSON.toJson(walletTransactionPayload.getMetaData()),
                    walletTransactionPayload.getDescription());
            if (row == 1) {
                return transactionId;
            }
        } catch (final Exception e) {
            logger.error("Unable add wallet transaction for user {}", walletTransactionPayload.getUserId(), e);
        }
        return null;
    }

    public boolean updateWalletTransactionAmount(int instituteId, UUID transactionId, UUID userId, 
                                                double newAmount, double differenceAmount) {
        try {
            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    // Update transaction amount in history table
                    int updatedRows = jdbcTemplate.update(UPDATE_WALLET_TRANSACTION_AMOUNT, 
                            newAmount, instituteId, transactionId.toString());
                    
                    if (updatedRows != 1) {
                        logger.error("Failed to update wallet transaction amount for transaction {}", transactionId);
                        throw new EmbrateRunTimeException("Failed to update wallet transaction amount");
                    }
                    
                    // Update user wallet balance with the difference amount
                    updatedRows = jdbcTemplate.update(UPDATE_USER_WALLET_AMOUNT_BY_DIFFERENCE, 
                            differenceAmount, userId.toString(), instituteId);
                    
                    if (updatedRows != 1) {
                        logger.error("Failed to update user wallet balance for user {}", userId);
                        throw new EmbrateRunTimeException("Failed to update user wallet balance");
                    }
                    
                    return true;
                }
            });
            
            return success != null && success;
        } catch (Exception e) {
            logger.error("Error updating wallet transaction amount for transaction {}, user {}", 
                    transactionId, userId, e);
            return false;
        }
    }


    public boolean deleteWalletTransaction(int instituteId, UUID transactionId, UUID userId, double amountToUpdateInBalance) {
        try {
            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    // Update transaction amount in history table
                    int updatedRows = jdbcTemplate.update(DELETE_WALLET_TRANSACTION, instituteId, transactionId.toString());

                    if (updatedRows != 1) {
                        logger.error("Failed to delete wallet transaction for transaction {}", transactionId);
                        throw new EmbrateRunTimeException("Failed to delete wallet transaction");
                    }

                    // Update user wallet balance with the difference amount
                    updatedRows = jdbcTemplate.update(UPDATE_USER_WALLET_AMOUNT_BY_DIFFERENCE,
                            -amountToUpdateInBalance, userId.toString(), instituteId);

                    if (updatedRows != 1) {
                        logger.error("Failed to update user wallet balance for user {}", userId);
                        throw new EmbrateRunTimeException("Failed to update user wallet balance");
                    }

                    return true;
                }
            });

            return success != null && success;
        } catch (Exception e) {
            logger.error("Error updating wallet transaction amount for transaction {}, user {}",
                    transactionId, userId, e);
            return false;
        }
    }
}
