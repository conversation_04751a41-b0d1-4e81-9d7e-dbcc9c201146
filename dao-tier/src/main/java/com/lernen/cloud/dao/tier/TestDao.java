package com.lernen.cloud.dao.tier;

import org.springframework.jdbc.core.JdbcTemplate;

public class TestDao {
	
	private JdbcTemplate jdbcTemplate;  
	
	public TestDao(JdbcTemplate jdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
	}

	public int saveEmployee(int id , String name){  
	    String query="insert into test values(" +id+",'"+name + "')";  
	    return jdbcTemplate.update(query);  
	}  
	
	public String getEmployee(int id){  
	    String query="select name from test where id="+id;  
	    return jdbcTemplate.queryForObject(query, String.class);  
	}  

}
