package com.lernen.cloud.dao.tier.appointment;

import com.embrate.cloud.dao.tier.push.notification.PushNotificationDao;
import com.lernen.cloud.core.api.appointment.StudentAppointmentDetails;
import com.lernen.cloud.core.api.appointment.AppointmentDetailsPayload;
import com.lernen.cloud.core.api.appointment.AppointmentStatus;
import com.lernen.cloud.core.api.common.PaginationInfo;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.DatabaseException;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.dao.tier.appointment.mappers.AppointmentDetailsRowMapper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

public class AppointmentDao {
    private static final Logger logger = LogManager.getLogger(AppointmentDao.class);

    private final JdbcTemplate jdbcTemplate;

    private static final AppointmentDetailsRowMapper APPOINTMENT_DETAILS_ROW_MAPPER = new AppointmentDetailsRowMapper();

    private static final String ADD_APPOINTMENT_DETAILS = "insert into user_appointment_details (" +
            "institute_id, academic_session_id, appointment_id, raised_by_user_id, raised_by_user_type, "
            + "guardian_name, guardian_contact_info, raised_for_user_id, raised_for_user_type, appointment_date, "
            + "description, status, raised_date"
            + ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String UPDATE_APPOINTMENT_DETAILS = "update user_appointment_details set " +
            "guardian_name = ?, guardian_contact_info = ?, raised_for_user_id = ?, raised_for_user_type = ?, " +
            "appointment_date = ?, description = ? " +
            "where institute_id = ? and appointment_id = ? and academic_session_id = ?";


    private static  final String GET_APPOINTMENT_DETAILS  =  "select user_appointment_details.*, students.*, academic_session.*, staff_details.*, standards.*, standard_section_mapping.*, student_academic_session_details.*, accepted_user.*, rejected_user.*, completed_user.* " +
            "from user_appointment_details " +
            "join students on user_appointment_details.raised_by_user_id = students.student_id and user_appointment_details.raised_by_user_Type = 'STUDENT'" +
            "join academic_session on user_appointment_details.academic_session_id = academic_session.academic_session_id " +
            "join student_academic_session_details on  students.student_id = student_academic_session_details.student_id and student_academic_session_details.academic_session_id = ? " +
            "join standards on student_academic_session_details.standard_id = standards.standard_id " +
            "left join standard_section_mapping on" +
            " standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = " +
            " student_academic_session_details.academic_session_id and " +
            " student_academic_session_details.section_id = standard_section_mapping.section_id " +
            "join staff_details on user_appointment_details.raised_for_user_id = staff_details.staff_id " +
            "left join users as accepted_user on user_appointment_details.accepted_by = accepted_user.user_id " +
            "left join users as rejected_user on user_appointment_details.rejected_by = rejected_user.user_id " +
            "left join users as completed_user on user_appointment_details.completed_by = completed_user.user_id " +
            "where user_appointment_details.institute_id = ? " +
            "and user_appointment_details.academic_session_id = ? ";

    private static  final String COUNT_ALL_APPOINTMENT_DETAILS = "select count(*) " +
            "from user_appointment_details " +
            "join students " +
            "    on user_appointment_details.raised_by_user_id = students.student_id " +
            "    and user_appointment_details.raised_by_user_type = 'STUDENT' " +
            "join academic_session " +
            "    on user_appointment_details.academic_session_id = academic_session.academic_session_id " +
            "join student_academic_session_details " +
            "    on students.student_id = student_academic_session_details.student_id " +
            "    and student_academic_session_details.academic_session_id = ? " +
            "join standards " +
            "    on student_academic_session_details.standard_id = standards.standard_id " +
            "left join standard_section_mapping " +
            "    on standard_section_mapping.standard_id = standards.standard_id " +
            "    and standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id " +
            "    and student_academic_session_details.section_id = standard_section_mapping.section_id " +
            "join staff_details " +
            "    on user_appointment_details.raised_for_user_id = staff_details.staff_id " +
            "left join users as accepted_user " +
            "    on user_appointment_details.accepted_by = accepted_user.user_id " +
            "left join users as rejected_user " +
            "    on user_appointment_details.rejected_by = rejected_user.user_id " +
            "left join users as completed_user " +
            "    on user_appointment_details.completed_by = completed_user.user_id " +
            "where user_appointment_details.institute_id = ? " +
            "and user_appointment_details.academic_session_id = ? ";

    private static  final String RAISED_BY_USER_ID = " and raised_by_user_id = ?";

    private static final String RAISED_FOR_USER_ID = " and raised_for_user_id = ?";

    private static final String BY_APPOINTMENT_ID = " and appointment_id = ?";

    private static final String ORDER_BY = " order by user_appointment_details.appointment_date asc limit ? offset ? ";

    private static final String UPDATE_APPOINTMENT_STATUS = "update user_appointment_details set status = ?";

    private static final String WHERE_CLAUSE = " where institute_id = ? and academic_session_id = ? and appointment_id = ? ";

    private static final String ACCEPTED_CLAUSE = " accepted_reason = ?, accepted_date = ?, accepted_by = ? ";

    private static final String REJECTED_CLAUSE = " rejected_reason = ?, rejected_date = ?, rejected_by = ? ";

    private static final String ACCEPTED_NULL_CLAUSE = " accepted_reason = null, accepted_date = null, accepted_by = null ";

    private static final String REJECTED_NULL_CLAUSE = " rejected_reason = null, rejected_date = null, rejected_by = null ";

    private static final String COMPLETED_CLAUSE = " outcome = ?, completed_date = ?, completed_by = ? ";

    private static final String APPOINTMENT_STATUS_CLAUSE = " and user_appointment_details.status in ";



    public AppointmentDao(JdbcTemplate jdbcTemplate){
        this.jdbcTemplate = jdbcTemplate;
    }

    public UUID addAppointmentDetails(AppointmentDetailsPayload appointmentDetailsPayload) {
        UUID appointmentId = UUID.randomUUID();
        try {
            Boolean result = jdbcTemplate.update(ADD_APPOINTMENT_DETAILS,
                    appointmentDetailsPayload.getInstituteId(), appointmentDetailsPayload.getAcademicSessionId(), appointmentId.toString(),
                    appointmentDetailsPayload.getRaisedByUserId().toString(),
                    appointmentDetailsPayload.getRaisedByUserType().name(),
                    appointmentDetailsPayload.getGuardianName(), appointmentDetailsPayload.getGuardianContactInfo(),
                    appointmentDetailsPayload.getRaisedForUserId().toString(), appointmentDetailsPayload.getRaisedForUserType().name(),
                    (appointmentDetailsPayload.getAppointmentDate() > 0)
                            ? new Timestamp(appointmentDetailsPayload.getAppointmentDate() * 1000l) : null,
                    appointmentDetailsPayload.getDescription(),
                    appointmentDetailsPayload.getAppointmentStatus().name(),
                    new Timestamp(System.currentTimeMillis())) == 1;

            if (!result) {
                throw new RuntimeException("Error while adding appointment details. Please try again.");
            }

            return appointmentId;
        } catch (final DatabaseException | ApplicationException ex) {
            throw ex;
        } catch (final Exception e) {
            logger.error("Error while adding appointment for institute {}", appointmentDetailsPayload.getInstituteId(), e);
        }
        return null;
    }

    public boolean updateAppointmentDetails(int instituteId, AppointmentDetailsPayload appointmentDetailsPayload) {
        try {
            return jdbcTemplate.update(UPDATE_APPOINTMENT_DETAILS,  appointmentDetailsPayload.getGuardianName(), appointmentDetailsPayload.getGuardianContactInfo(),
                    appointmentDetailsPayload.getRaisedForUserId().toString(), appointmentDetailsPayload.getRaisedForUserType().name(),
                    (appointmentDetailsPayload.getAppointmentDate() > 0)
                            ? new Timestamp(appointmentDetailsPayload.getAppointmentDate() * 1000l) : null,
                    appointmentDetailsPayload.getDescription(), appointmentDetailsPayload.getInstituteId(), appointmentDetailsPayload.getAppointmentId().toString(),
                    appointmentDetailsPayload.getAcademicSessionId()) == 1;

        } catch (final Exception e) {
            logger.error("Exception while updating appointment details for institute {}, payload {}",
                    appointmentDetailsPayload.getInstituteId(), appointmentDetailsPayload, e);
        }
        return false;
    }

    public SearchResultWithPagination<StudentAppointmentDetails> getAppointmentDetails(int instituteId, UUID userId, int academicSessionId, UserType userType, Set<AppointmentStatus> appointmentStatusSet, Integer limit, Integer offset){
        try {
            final StringBuilder query = new StringBuilder();
            final StringBuilder countQuery = new StringBuilder();
            query.append(GET_APPOINTMENT_DETAILS);
            countQuery.append(COUNT_ALL_APPOINTMENT_DETAILS);

            final List<Object> args = new ArrayList<>();
            final List<Object> countArgs = new ArrayList<>();
            args.add(academicSessionId);
            args.add(instituteId);
            args.add(academicSessionId);
            countArgs.add(academicSessionId);
            countArgs.add(instituteId);
            countArgs.add(academicSessionId);

            // If the user is a student, we fetch appointment details using the "raised by" clause, as appointments are booked by the student.
            // If the user is staff and userId is not null, it means the user does not have admin access to appointment details. In this case, we fetch the appointment details based on the userId
            if (userType == UserType.STUDENT && userId != null) {
                query.append(RAISED_BY_USER_ID);
                countQuery.append(RAISED_BY_USER_ID);
                args.add(userId.toString());
                countArgs.add(userId.toString());
            } 
            
            if (userType != UserType.STUDENT && userId != null) {
                query.append(RAISED_FOR_USER_ID);
                countQuery.append(RAISED_FOR_USER_ID);
                args.add(userId.toString());
                countArgs.add(userId.toString());
            }

            if(!CollectionUtils.isEmpty(appointmentStatusSet)) {
                query.append(APPOINTMENT_STATUS_CLAUSE);
                countQuery.append(APPOINTMENT_STATUS_CLAUSE);
                query.append(" (");
                countQuery.append(" (");

                String statusDelimiter = "";
                for(AppointmentStatus appointmentStatus : appointmentStatusSet) {
                    if(appointmentStatus == null) {
                        continue;
                    }
                    args.add(appointmentStatus.name());
                    countArgs.add(appointmentStatus.name());
                    query.append(statusDelimiter).append(" ? ");
                    countQuery.append(statusDelimiter).append(" ? ");
                    statusDelimiter = ",";
                }
                query.append(") ");
                countQuery.append(") ");
            }

            query.append(ORDER_BY);
            args.add(limit);
            args.add(offset);
            final int totalResultCount = jdbcTemplate.queryForObject(countQuery.toString(), Integer.class, countArgs.toArray());
            List<StudentAppointmentDetails> studentAppointmentDetailsList = jdbcTemplate.query(query.toString(), args.toArray(), APPOINTMENT_DETAILS_ROW_MAPPER);
            final PaginationInfo paginationInfo = new PaginationInfo(totalResultCount, limit, offset);
            return new SearchResultWithPagination<>(
                    paginationInfo, studentAppointmentDetailsList);
        } catch (final Exception e) {
            logger.error("Exception while getting appointment details for instituteId {}, userId {}", instituteId,
                    userId, e);
        }
        return null;
    }

    public StudentAppointmentDetails getAppointmentDetailsByAppointmentId(int instituteId, UUID userId, int academicSessionId, UUID appointmentId){
        try {
            final StringBuilder query = new StringBuilder();
            query.append(GET_APPOINTMENT_DETAILS);
            query.append(BY_APPOINTMENT_ID);

            final List<Object> args = new ArrayList<>();
            args.add(academicSessionId);
            args.add(instituteId);
            args.add(academicSessionId);
            args.add(appointmentId.toString());

            return jdbcTemplate.queryForObject(query.toString(), args.toArray(), APPOINTMENT_DETAILS_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception while getting appointment details for instituteId {}, appointmentId {}", instituteId,
                    appointmentId, e);
        }
        return null;
    }

    public boolean updateAppointmentStatus(int instituteId, UUID userId, int academicSessionId, UUID appointmentId, AppointmentStatus appointmentStatus, UserType userType, String reason){
        try {
            final StringBuilder query = new StringBuilder();
            final List<Object> args = new ArrayList<>();
            String delimiter = ",";
            args.add(appointmentStatus.name());
            query.append(UPDATE_APPOINTMENT_STATUS);
            if(appointmentStatus == AppointmentStatus.RAISED || appointmentStatus == AppointmentStatus.DELETED) {
                query.append(WHERE_CLAUSE);
                args.add(instituteId);
                args.add(academicSessionId);
                args.add(appointmentId.toString());
                return jdbcTemplate.update(String.format(query.toString()), args.toArray()) >= 1;
            }
            switch (appointmentStatus) {
                case ACCEPTED:
                    query.append(delimiter);
                    query.append(ACCEPTED_CLAUSE);
                    query.append(delimiter);
                    query.append(REJECTED_NULL_CLAUSE);
                    query.append(WHERE_CLAUSE);
                    break;

                case REJECTED:
                    query.append(delimiter);
                    query.append(REJECTED_CLAUSE);
                    query.append(delimiter);
                    query.append(ACCEPTED_NULL_CLAUSE);
                    query.append(WHERE_CLAUSE);
                    break;

                case COMPLETED:
                    query.append(delimiter);
                    query.append(COMPLETED_CLAUSE);
                    query.append(WHERE_CLAUSE);
                    break;
            }
            args.add(reason);
            args.add(new Timestamp(System.currentTimeMillis()));
            args.add(userId.toString());
            args.add(instituteId);
            args.add(academicSessionId);
            args.add(appointmentId.toString());


            return jdbcTemplate.update(query.toString(), args.toArray()) >= 1;
        } catch (final Exception e) {
            logger.error("Exception while updating appointment details for institute {}, appointmentstatus {}", instituteId,
                    appointmentStatus, e);
        }
        return false;
    }
}

