package com.lernen.cloud.dao.tier.appointment.mappers;


import com.lernen.cloud.core.api.appointment.StudentAppointmentDetails;
import com.lernen.cloud.core.api.appointment.AppointmentStatus;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffLite;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.UserLite;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;
import com.lernen.cloud.dao.tier.staff.mappers.StaffRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.StudentRowMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;

import static com.lernen.cloud.core.api.user.UserLite.createUserLite;


public class AppointmentDetailsRowMapper implements RowMapper<StudentAppointmentDetails> {

    private static final StaffRowMapper STAFF_ROW_MAPPER = new StaffRowMapper();
    private static final StudentRowMapper STUDENT_ROW_MAPPER = new StudentRowMapper();

    protected static final String INSTITUTE_ID = "user_appointment_details.institute_id";
    protected static final String ACADEMIC_SESSION_ID = "user_appointment_details.academic_session_id";
    protected static final String APPOINTMENT_ID = "user_appointment_details.appointment_id";
    protected static final String GUARDIAN_NAME = "user_appointment_details.guardian_name";
    protected static final String GUARDIAN_CONTACT_INFO = "user_appointment_details.guardian_contact_info";
    protected static final String APPOINTMENT_DATE = "user_appointment_details.appointment_date";
    protected static final String DESCRIPTION = "user_appointment_details.description";
    protected static final String STATUS = "user_appointment_details.status";
    protected static final String RAISED_DATE = "user_appointment_details.raised_date";
    protected static final String ACCEPTED_DATE = "user_appointment_details.accepted_date";
    protected static final String ACCEPTED_REASON = "user_appointment_details.accepted_reason";
    protected static final String REJECTED_DATE = "user_appointment_details.rejected_date";
    protected static final String REJECTED_REASON = "user_appointment_details.rejected_reason";
    protected static final String COMPLETED_DATE = "user_appointment_details.completed_date";
    protected static final String OUTCOME = "user_appointment_details.outcome";

    protected static final String COMPLETED_USER_ID = "completed_user.user_id";
    protected static final String COMPLETED_USER_INSTITUTE_ID = "completed_user.user_institute_id";
    protected static final String COMPLETED_INSTITUTE_ID = "completed_user.institute_id";
    protected static final String COMPLETED_USER_NAME = "completed_user.user_name";
    protected static final String COMPLETED_USER_TYPE = "completed_user.user_type";
    protected static final String COMPLETED_USER_STATUS = "completed_user.user_status";
    protected static final String COMPLETED_FIRST_NAME = "completed_user.first_name";
    protected static final String COMPLETED_LAST_NAME = "completed_user.last_name";

    protected static final String ACCEPTED_USER_ID = "accepted_user.user_id";
    protected static final String ACCEPTED_USER_INSTITUTE_ID = "accepted_user.user_institute_id";
    protected static final String ACCEPTED_INSTITUTE_ID = "accepted_user.institute_id";
    protected static final String ACCEPTED_USER_NAME = "accepted_user.user_name";
    protected static final String ACCEPTED_USER_TYPE = "accepted_user.user_type";
    protected static final String ACCEPTED_USER_STATUS = "accepted_user.user_status";
    protected static final String ACCEPTED_FIRST_NAME = "accepted_user.first_name";
    protected static final String ACCEPTED_LAST_NAME = "accepted_user.last_name";

    private static final String REJECTED_USER_ID = "rejected_user.user_id";
    private static final String REJECTED_USER_INSTITUTE_ID = "rejected_user.user_institute_id";
    private static final String REJECTED_INSTITUTE_ID = "rejected_user.institute_id";
    private static final String REJECTED_USER_NAME = "rejected_user.user_name";
    private static final String REJECTED_USER_TYPE = "rejected_user.user_type";
    private static final String REJECTED_USER_STATUS = "rejected_user.user_status";
    private static final String REJECTED_FIRST_NAME = "rejected_user.first_name";
    private static final String REJECTED_LAST_NAME = "rejected_user.last_name";

    @Override
    public StudentAppointmentDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
        if (rs.getString(APPOINTMENT_ID) == null) {
            return null;
        }

        final StaffLite staffLite = Staff.getStaffLite(STAFF_ROW_MAPPER.mapRow(rs, rowNum));
        final StudentLite studentLite = Student.getStudentLite(STUDENT_ROW_MAPPER.mapRow(rs, rowNum));

        UserLite acceptedUserLite = null;
        if (rs.getString(ACCEPTED_USER_ID) != null) {
            acceptedUserLite = createUserLite(UUID.fromString(rs.getString(ACCEPTED_USER_ID)), CryptoUtils.decrypt(rs.getString(ACCEPTED_FIRST_NAME)),
                    CryptoUtils.decrypt(rs.getString(ACCEPTED_LAST_NAME)), rs.getString(ACCEPTED_USER_INSTITUTE_ID), rs.getInt(ACCEPTED_INSTITUTE_ID),
                    UserType.getUserType(rs.getString(ACCEPTED_USER_TYPE)), UserStatus.getUserStatus(rs.getString(ACCEPTED_USER_STATUS)), rs.getString(ACCEPTED_USER_NAME));
        }

        UserLite rejectUserLite = null;
        if (rs.getString(REJECTED_USER_ID) != null) {
            rejectUserLite = createUserLite(UUID.fromString(rs.getString(REJECTED_USER_ID)), CryptoUtils.decrypt(rs.getString(REJECTED_FIRST_NAME)),
                    CryptoUtils.decrypt(rs.getString(REJECTED_LAST_NAME)), rs.getString(REJECTED_USER_INSTITUTE_ID), rs.getInt(REJECTED_INSTITUTE_ID),
                    UserType.getUserType(rs.getString(REJECTED_USER_TYPE)), UserStatus.getUserStatus(rs.getString(REJECTED_USER_STATUS)), rs.getString(REJECTED_USER_NAME));
        }

        UserLite completedUserLite = null;
        if (rs.getString(COMPLETED_USER_ID) != null) {
            completedUserLite = createUserLite(UUID.fromString(rs.getString(COMPLETED_USER_ID)), CryptoUtils.decrypt(rs.getString(COMPLETED_FIRST_NAME)),
                    CryptoUtils.decrypt(rs.getString(COMPLETED_LAST_NAME)), rs.getString(COMPLETED_USER_INSTITUTE_ID), rs.getInt(COMPLETED_INSTITUTE_ID),
                    UserType.getUserType(rs.getString(COMPLETED_USER_TYPE)), UserStatus.getUserStatus(rs.getString(COMPLETED_USER_STATUS)), rs.getString(COMPLETED_USER_NAME));
        }

        final Timestamp appointmentDate = rs.getTimestamp(APPOINTMENT_DATE);
        final Integer appointmentDateTime = appointmentDate == null ? null
                : (int) (appointmentDate.getTime() / 1000L);

        final Timestamp raisedDate = rs.getTimestamp(RAISED_DATE);
        final Integer raisedDateTime = raisedDate == null ? null
                : (int) (raisedDate.getTime() / 1000L);

        final Timestamp acceptedDate = rs.getTimestamp(ACCEPTED_DATE);
        final Integer acceptedDateTime = acceptedDate == null ? null
                : (int) (acceptedDate.getTime() / 1000L);

        final Timestamp rejectedDate = rs.getTimestamp(REJECTED_DATE);
        final Integer rejectedDateTime = rejectedDate == null ? null
                : (int) (rejectedDate.getTime() / 1000L);

        final Timestamp completedDate = rs.getTimestamp(COMPLETED_DATE);
        final Integer completedDateTime = completedDate == null ? null
                : (int) (completedDate.getTime() / 1000L);

        return new StudentAppointmentDetails(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID), UUID.fromString(rs.getString(APPOINTMENT_ID)),
                studentLite, rs.getString(GUARDIAN_NAME), rs.getString(GUARDIAN_CONTACT_INFO), staffLite, appointmentDateTime, rs.getString(DESCRIPTION),
                AppointmentStatus.getAppointmentStatus(rs.getString(STATUS)), raisedDateTime, acceptedDateTime, acceptedUserLite, rs.getString(ACCEPTED_REASON),
                rejectedDateTime, rejectUserLite, rs.getString(REJECTED_REASON), completedDateTime, completedUserLite, rs.getString(OUTCOME)
        );
    }

}

