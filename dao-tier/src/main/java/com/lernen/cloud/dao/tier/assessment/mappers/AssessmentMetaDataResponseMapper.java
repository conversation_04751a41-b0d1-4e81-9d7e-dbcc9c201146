package com.lernen.cloud.dao.tier.assessment.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.assessment.AssessmentEntityMappingResponse;
import com.lernen.cloud.core.api.assessment.AssessmentMetaDataResponseRow;
import com.lernen.cloud.core.api.assessment.AssessmentStatus;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.ExamCoursePublishedStatus;
import com.lernen.cloud.core.api.institute.Time;
import com.lernen.cloud.core.utils.DateUtils;

public class AssessmentMetaDataResponseMapper implements RowMapper<AssessmentMetaDataResponseRow> {

    // Column name constants
    private static final String ASSESSMENT_ID = "assessment_metadata.assessment_id";
    private static final String ASSESSMENT_NAME = "assessment_metadata.assessment_name";
    private static final String ASSESSMENT_TOTAL_MARKS = "assessment_metadata.total_marks";
    private static final String START_TIME = "assessment_metadata.start_time";
    private static final String ASSESSMENT_DATE = "assessment_metadata.assessment_date";
    private static final String DURATION = "assessment_metadata.duration";
    private static final String PASSKEY = "assessment_metadata.passkey";
    private static final String ASSESSMENT_INSTRUCTIONS = "assessment_metadata.assessment_instructions";
    private static final String AUTO_SUBMIT_TIMER = "assessment_metadata.auto_submit_timer";
    private static final String SHOW_RESULT_IMMEDIATELY = "assessment_metadata.show_result_immediately";
    private static final String PUBLISHED_STATUS = "assessment_metadata.published_status";
    private static final String CREATED_AT = "assessment_metadata.created_at";

    // Exam-related columns
    private static final String EXAM_ID = "assessment_entity_mapping.exam_id";
    private static final String EXAM_NAME = "exam_metadata.exam_name";

    // Course-related columns
    private static final String COURSE_ID = "assessment_entity_mapping.course_id";
    private static final String COURSE_TYPE = "class_courses.course_type";
    private static final String COURSE_NAME = "class_courses.course_name";

    // Standard-related columns
    private static final String STANDARD_ID = "assessment_entity_mapping.standard_id";
    private static final String STANDARD_NAME = "standards.standard_name";
    private static final String STANDARD_STREAM = "standards.stream";

    // Section-related columns
    private static final String SECTION_ID = "assessment_entity_mapping.section_id";
    private static final String SECTION_NAME = "standard_section_mapping.section_name";

    @Override
    public AssessmentMetaDataResponseRow mapRow(ResultSet rs, int rowNum) throws SQLException {
        if (rs.getString(ASSESSMENT_ID) == null) {
            return null;
        }
        AssessmentStatus status = AssessmentStatus.UPCOMING;
        Timestamp startTime = rs.getTimestamp(START_TIME);
        int durationMinutes = rs.getInt(DURATION);
        if (startTime != null) {
            long startTimestamp = startTime.getTime();
            long durationMillis = durationMinutes * 60 * 1000L;
            long endTimestamp = startTimestamp + durationMillis;
    
            long currentTimestamp = System.currentTimeMillis();
    
            if (currentTimestamp < startTimestamp) {
                status = AssessmentStatus.UPCOMING;
            } else if (currentTimestamp >= startTimestamp && currentTimestamp < endTimestamp) {
                status = AssessmentStatus.ONGOING;
            } else {
                status = AssessmentStatus.COMPLETED;
            }
        }
        return new AssessmentMetaDataResponseRow(UUID.fromString(rs.getString(ASSESSMENT_ID)),
            rs.getString(ASSESSMENT_NAME),
            rs.getDouble(ASSESSMENT_TOTAL_MARKS),
            startTime != null ? (int) (startTime.getTime() / 1000l) : null,
            rs.getTimestamp(ASSESSMENT_DATE) != null ? (int) (rs.getTimestamp(ASSESSMENT_DATE).getTime() / 1000l) : null,
            durationMinutes,
            rs.getString(PASSKEY),
            rs.getString(ASSESSMENT_INSTRUCTIONS),
            rs.getBoolean(AUTO_SUBMIT_TIMER),
            rs.getBoolean(SHOW_RESULT_IMMEDIATELY),
            ExamCoursePublishedStatus.valueOf(rs.getString(PUBLISHED_STATUS)),
            rs.getTimestamp(CREATED_AT) != null ? (int) (rs.getTimestamp(CREATED_AT).getTime() / 1000l) : null,

            new AssessmentEntityMappingResponse(
                rs.getString(EXAM_ID) != null ? UUID.fromString(rs.getString(EXAM_ID)) : null,
                rs.getString(EXAM_NAME),
                rs.getString(COURSE_ID) != null ? UUID.fromString(rs.getString(COURSE_ID)) : null,
                rs.getString(COURSE_TYPE) != null ? CourseType.valueOf(rs.getString(COURSE_TYPE)) : null,
                rs.getString(COURSE_NAME),
                rs.getString(STANDARD_ID) != null ? UUID.fromString(rs.getString(STANDARD_ID)) : null,
                rs.getString(STANDARD_NAME),
                rs.getString(STANDARD_STREAM),
                rs.getInt(SECTION_ID),
                rs.getString(SECTION_NAME)
            ),
            status
        );
    }
}
