package com.lernen.cloud.dao.tier.assessment.mappers;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.assessment.QuestionBank;
import com.lernen.cloud.core.api.assessment.QuestionBankRow;
import com.lernen.cloud.core.api.assessment.QuestionLevel;
import com.lernen.cloud.core.api.assessment.QuestionOption;
import com.lernen.cloud.core.api.assessment.QuestionType;
import com.lernen.cloud.core.utils.SharedConstants;

/**
 * Mapper class for QuestionBank entity.
 */
public class QuestionBankMapper implements RowMapper<QuestionBankRow> {

    private static final QuestionOptionMapper QUESTION_OPTION_MAPPER = new QuestionOptionMapper();
    private static final Gson GSON = SharedConstants.GSON;

    private static final String QUESTION_ID = "question_bank.question_id";
    private static final String QUESTION_TEXT = "question_bank.question_text";
    private static final String QUESTION_TYPE = "question_bank.question_type";
    private static final String ANSWER = "question_bank.answer";
    private static final String MARKS = "question_bank.marks";
    private static final String IS_ACTIVE = "question_bank.is_active";
    private static final String DIFFICULTY_LEVEL = "question_bank.difficulty_level";
    private static final String TAGS = "question_bank.tags";
    private static final String SEQUENCE = "assessment_questions_mapping.sequence";

    @Override
    public QuestionBankRow mapRow(ResultSet rs, int rowNum) throws SQLException {
        if (rs.getString(QUESTION_ID) == null) {
            return null;
        }

        QuestionOption questionOption = QUESTION_OPTION_MAPPER.mapRow(rs, rowNum);
        
        final Type collectionType = new TypeToken<List<UUID>>() {
			}.getType();

        return new QuestionBankRow(
            UUID.fromString(rs.getString(QUESTION_ID)),
            rs.getString(QUESTION_TEXT),
            rs.getString(QUESTION_TYPE) != null ? QuestionType.valueOf(rs.getString(QUESTION_TYPE)) : null,
            rs.getString(ANSWER) != null ? GSON.fromJson(rs.getString(ANSWER), collectionType) : null,
            rs.getInt(MARKS),
            rs.getBoolean(IS_ACTIVE),
            rs.getString(DIFFICULTY_LEVEL) != null ? QuestionLevel.valueOf(rs.getString(DIFFICULTY_LEVEL)) : null,
            rs.getString(TAGS),
            questionOption,
            rs.getInt(SEQUENCE)
        );
    }
}
