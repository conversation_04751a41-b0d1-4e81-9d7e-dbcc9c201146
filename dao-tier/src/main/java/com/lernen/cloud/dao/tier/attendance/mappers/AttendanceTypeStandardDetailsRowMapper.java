package com.lernen.cloud.dao.tier.attendance.mappers;

import com.lernen.cloud.core.api.attendance.AttendanceType;
import com.lernen.cloud.core.api.attendance.AttendanceTypeMetadata;
import com.lernen.cloud.core.api.attendance.AttendanceTypeRow;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

public class AttendanceTypeStandardDetailsRowMapper implements RowMapper<AttendanceTypeRow> {

    private static final String INSTITUTE_ID = "institute_id";
    private static final String ACADEMIC_SESSION_ID = "academic_session_id";
    private static final String ATTENDANCE_TYPE_ID = "attendance_type_id";
    private static final String NAME = "name";
    private static final String DESCRIPTION = "description";

    private static final String METADATA = "metadata";
    private static final String STANDARD_ID = "standard_attendance_type.standard_id";

    @Override
    public AttendanceTypeRow mapRow(ResultSet rs, int rowNum) throws SQLException {
        if (rs.getInt(ATTENDANCE_TYPE_ID) <= 0) {
            return null;
        }
        String standardIdStr = rs.getString(STANDARD_ID);
        UUID standardId = null;
        if (StringUtils.isNotBlank(standardIdStr)) {
            standardId = UUID.fromString(standardIdStr);
        }
        String metadataStr = rs.getString(METADATA);
        AttendanceTypeMetadata attendanceTypeMetadata = null;
        if(StringUtils.isNotBlank(metadataStr)){
            attendanceTypeMetadata = SharedConstants.GSON.fromJson(metadataStr, AttendanceTypeMetadata.class);
        }
        return new AttendanceTypeRow(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID),
                rs.getInt(ATTENDANCE_TYPE_ID), rs.getString(NAME), rs.getString(DESCRIPTION), attendanceTypeMetadata, standardId);
    }

    public static List<AttendanceType> getAttendanceTypes(List<AttendanceTypeRow> attendanceTypeRowList) {
        if (CollectionUtils.isEmpty(attendanceTypeRowList)) {
            return null;
        }
        Map<Integer, List<AttendanceTypeRow>> attendanceTypeMap = new HashMap<>();
        AttendanceTypeRow firstRow = attendanceTypeRowList.get(0);
        Set<UUID> standardIds = new HashSet<>();
        for (AttendanceTypeRow attendanceTypeRow : attendanceTypeRowList) {
            if (!attendanceTypeMap.containsKey(attendanceTypeRow.getAttendanceTypeId())) {
                attendanceTypeMap.put(attendanceTypeRow.getAttendanceTypeId(), new ArrayList<>());
            }
            attendanceTypeMap.get(attendanceTypeRow.getAttendanceTypeId()).add(attendanceTypeRow);
        }
        List<AttendanceType> attendanceTypes = new ArrayList<>();
        for (Map.Entry<Integer, List<AttendanceTypeRow>> entry : attendanceTypeMap.entrySet()) {
            attendanceTypes.add(getAttendanceType(entry.getValue()));
        }
        return attendanceTypes;
    }

    /**
     * This method is expected to have same attendance id rows
     *
     * @param attendanceTypeRowList
     * @return
     */
    public static AttendanceType getAttendanceType(List<AttendanceTypeRow> attendanceTypeRowList) {
        if (CollectionUtils.isEmpty(attendanceTypeRowList)) {
            return null;
        }
        AttendanceTypeRow firstRow = attendanceTypeRowList.get(0);
        Set<UUID> standardIds = new HashSet<>();
        for (AttendanceTypeRow attendanceTypeRow : attendanceTypeRowList) {
            if (attendanceTypeRow.getStandardId() != null) {
                standardIds.add(attendanceTypeRow.getStandardId());
            }
        }
        return new AttendanceType(firstRow.getInstituteId(), firstRow.getAcademicSessionId(), firstRow.getAttendanceTypeId(), firstRow.getName(), firstRow.getDescription()
                , firstRow.getAttendanceTypeMetadata(), standardIds);
    }

    public static AttendanceType getAttendanceType(AttendanceTypeRow attendanceTypeRow) {
        return new AttendanceType(attendanceTypeRow.getInstituteId(), attendanceTypeRow.getAcademicSessionId(), attendanceTypeRow.getAttendanceTypeId(), attendanceTypeRow.getName(), attendanceTypeRow.getDescription()
                , attendanceTypeRow.getAttendanceTypeMetadata(), null);
    }
}
