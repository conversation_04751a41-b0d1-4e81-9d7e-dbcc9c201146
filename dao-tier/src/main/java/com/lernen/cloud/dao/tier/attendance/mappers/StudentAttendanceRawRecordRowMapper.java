package com.lernen.cloud.dao.tier.attendance.mappers;

import com.lernen.cloud.core.api.attendance.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

import static com.lernen.cloud.dao.tier.attendance.mappers.StudentAttendanceRowMapper.*;

public class StudentAttendanceRawRecordRowMapper implements RowMapper<AttendanceRawRecord> {

	@Override
	public AttendanceRawRecord mapRow(ResultSet rs, int rowNum) throws SQLException {

		final Timestamp attendanceDate = rs.getTimestamp(ATTENDANCE_DATE);
		final Integer attendanceDateTime = attendanceDate == null ? null
				: (int) (attendanceDate.getTime() / 1000l);
		
		return new AttendanceRawRecord(UUID.fromString(rs.getString(STUDENT_ID)),  rs.getInt(ACADEMIC_SESSION_ID), rs.getInt(ATTENDANCE_TYPE_ID),
				AttendanceStatus.getAttendanceStatus(rs.getString(ATTENDANCE_STATUS)), attendanceDateTime, rs.getString(REMARKS),
				StringUtils.isBlank(rs.getString(CREATED_BY)) ? null : UUID.fromString(rs.getString(CREATED_BY)),
				StringUtils.isBlank(rs.getString(UPDATED_BY)) ? null : UUID.fromString(rs.getString(UPDATED_BY)),
				rs.getTimestamp(CREATED_AT) == null ? null : (int) (rs.getTimestamp(CREATED_AT).getTime() / 1000l),
				rs.getTimestamp(UPDATED_AT) == null ? null : (int) (rs.getTimestamp(UPDATED_AT).getTime() / 1000l));
	}
}
