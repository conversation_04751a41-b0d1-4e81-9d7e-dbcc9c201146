/**
 * 
 */
package com.lernen.cloud.dao.tier.attendance.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.UUID;

import com.lernen.cloud.core.api.attendance.AttendanceTypeRow;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.attendance.AttendanceType;
import com.lernen.cloud.core.api.attendance.StudentAttendanceRecord;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentDetailedRow;
import com.lernen.cloud.dao.tier.student.mappers.StudentRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.StudentWithoutSessionRowMapper;

/**
 * <AUTHOR>
 *
 */
public class StudentWithSessionAttendanceDetailsRowMapper implements RowMapper<StudentAttendanceRecord> {
	
	private static final StudentWithoutSessionRowMapper STUDENT_WITHOUT_SESSION_ROW_MAPPER = new StudentWithoutSessionRowMapper();
	private static final AttendanceTypeRowMapper ATTENDANCE_TYPE_ROW_MAPPER = new AttendanceTypeRowMapper();

	private static final String ATTENDANCE_STATUS = "attendance_status";
	private static final String ATTENDANCE_DATE = "attendance_date";
	private static final String REMARKS = "remarks";
	private static final String CREATED_BY = "created_by";
	private static final String UPDATED_BY = "updated_by";
	private static final String CREATED_AT = "created_at";
	private static final String UPDATED_AT = "updated_at";

	@Override
	public StudentAttendanceRecord mapRow(ResultSet rs, int rowNum) throws SQLException {

		final StudentDetailedRow studentDetailedRow = STUDENT_WITHOUT_SESSION_ROW_MAPPER.mapRow(rs, rowNum);
		final AttendanceType attendanceType = ATTENDANCE_TYPE_ROW_MAPPER.mapRow(rs, rowNum);
		final Student student = StudentRowMapper.getStudent(studentDetailedRow);
		
		final Timestamp attendanceDate = rs.getTimestamp(ATTENDANCE_DATE);
		final Integer attendanceDateTime = attendanceDate == null ? null
				: (int) (attendanceDate.getTime() / 1000l);
		
		return new StudentAttendanceRecord(attendanceType, student,
				AttendanceStatus.getAttendanceStatus(rs.getString(ATTENDANCE_STATUS)), attendanceDateTime, rs.getString(REMARKS),
				StringUtils.isBlank(rs.getString(CREATED_BY)) ? null : UUID.fromString(rs.getString(CREATED_BY)),
				StringUtils.isBlank(rs.getString(UPDATED_BY)) ? null : UUID.fromString(rs.getString(UPDATED_BY)),
				rs.getTimestamp(CREATED_AT) == null ? null : (int) (rs.getTimestamp(CREATED_AT).getTime() / 1000l),
				rs.getTimestamp(UPDATED_AT) == null ? null : (int) (rs.getTimestamp(UPDATED_AT).getTime() / 1000l));
	}

}
