package com.lernen.cloud.dao.tier.attendance.mappers.staff;


import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.attendance.staff.*;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffTimingDetails;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.dao.tier.staff.mappers.StaffRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 */
public class StaffAttendanceDetailsRowMapper implements RowMapper<StaffAttendanceRowDetails> {

    private static final String INSTITUTE_ID = "staff_attendance_register.institute_id";
    private static final String ATTENDANCE_DATE = "staff_attendance_register.attendance_date";
    private static final String TOTAL_DURATION = "staff_attendance_register.total_duration";
    private static final String METADATA = "staff_attendance_register.metadata";
    private static final String ATTENDANCE_STATUS = "staff_attendance_register.attendance_status";

    private static final String ATTENDANCE_TYPE = "staff_attendance_logs.type";
    private static final String TIME_OF_ACTION = "staff_attendance_logs.time_of_action";
    private static final String ADDED_BY = "staff_attendance_logs.added_by";
    private static final String ADDED_AT = "staff_attendance_logs.added_at";
    private static final String REMARKS = "staff_attendance_logs.remarks";

    private static final Gson GSON = new Gson();
    public static final TimeZone DEFAULT_TIMEZONE = TimeZone.getTimeZone("Asia/Kolkata");
    private static final StaffRowMapper STAFF_ROW_MAPPER = new StaffRowMapper();

    @Override
    public StaffAttendanceRowDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
        final Staff staff = STAFF_ROW_MAPPER.mapRow(rs, rowNum);
        if(staff == null) {
            return null;
        }

        StaffTimingDetails staffTimingDetails = rs.getString(METADATA) == null ? null : GSON.fromJson(rs.getString(METADATA),
                new TypeToken<StaffTimingDetails>(){}.getType());

        final Timestamp timeOfActionDate = rs.getTimestamp(TIME_OF_ACTION);
        final Integer timeOfActionDateTime = timeOfActionDate == null ? null
                : (int) (timeOfActionDate.getTime() / 1000l);

        final Timestamp addedAtDate = rs.getTimestamp(ADDED_AT);
        final Integer addedAtDateTime = addedAtDate == null ? null
                : (int) (addedAtDate.getTime() / 1000l);

        final Timestamp attendanceDate = rs.getTimestamp(ATTENDANCE_DATE);
        final Integer attendanceDateTime = attendanceDate == null ? null
                : (int) (attendanceDate.getTime() / 1000l);

        //Converting total duration in hrs when fetching from DB
        Double totalDuration = rs.getString(TOTAL_DURATION) == null ? null : rs.getDouble(TOTAL_DURATION) / 60d;

        return new StaffAttendanceRowDetails(rs.getInt(INSTITUTE_ID), attendanceDateTime, staff, totalDuration,
                rs.getString(ATTENDANCE_STATUS) == null ? null : StaffAttendanceStatus.getStaffAttendanceStatus(rs.getString(ATTENDANCE_STATUS)),
                staffTimingDetails, rs.getString(ATTENDANCE_TYPE) == null ? null : StaffAttendanceType.getStaffAttendanceType(rs.getString(ATTENDANCE_TYPE)),
                timeOfActionDateTime == null ? null : DateUtils.calculateTime(timeOfActionDateTime),
                rs.getString(ADDED_BY) == null ? null : UUID.fromString(rs.getString(ADDED_BY)),
                addedAtDateTime, rs.getString(REMARKS));
    }

    public static Map<UUID, StaffAttendanceDetails> getStaffAttendanceDetailsMap(
            List<StaffAttendanceRowDetails> staffAttendanceRowDetailsList) {
        if(CollectionUtils.isEmpty(staffAttendanceRowDetailsList)) {
            return null;
        }

        //Map of staff id and StaffAttendanceDetails
        Map<UUID, StaffAttendanceDetails> staffAttendanceDetailsMap = new HashMap<UUID, StaffAttendanceDetails>();

        for(StaffAttendanceRowDetails staffAttendanceRowDetails : staffAttendanceRowDetailsList) {

            UUID staffId = staffAttendanceRowDetails.getStaff().getStaffId();
            StaffAttendanceTimeDetails staffAttendanceTimeDetails = null;
            if(staffAttendanceRowDetails.getTimeOfAction() != null) {
                staffAttendanceTimeDetails = new StaffAttendanceTimeDetails(
                        staffAttendanceRowDetails.getTimeOfAction(), staffAttendanceRowDetails.getAddedBy(),
                        staffAttendanceRowDetails.getAddedAt(), staffAttendanceRowDetails.getRemarks());
            }

            if(staffAttendanceTimeDetails == null) {
                staffAttendanceDetailsMap.put(staffId, new StaffAttendanceDetails(staffAttendanceRowDetails.getInstituteId(),
                        staffAttendanceRowDetails.getAttendanceDate(), staffAttendanceRowDetails.getStaff(),
                        0d, null, null, null));
                continue;
            }

            if(staffAttendanceDetailsMap.containsKey(staffId)) {

                StaffAttendanceDetails staffAttendanceDetails = staffAttendanceDetailsMap.get(staffId);
                Map<StaffAttendanceType, List<StaffAttendanceTimeDetails>> staffAttendanceTimeTypeMap
                        = staffAttendanceDetails.getStaffAttendanceTimeTypeMap();

                if(staffAttendanceTimeTypeMap != null && !CollectionUtils.isEmpty(staffAttendanceTimeTypeMap) &&
                        staffAttendanceTimeTypeMap.containsKey(staffAttendanceRowDetails.getStaffAttendanceType())) {

                    staffAttendanceTimeTypeMap.get(staffAttendanceRowDetails.getStaffAttendanceType()).add(staffAttendanceTimeDetails);

                } else {

                    if(staffAttendanceTimeTypeMap == null || CollectionUtils.isEmpty(staffAttendanceTimeTypeMap.entrySet())) {
                        staffAttendanceTimeTypeMap = new HashMap<>();
                    }
                    List<StaffAttendanceTimeDetails> staffAttendanceTimeDetailsList = new ArrayList<>();
                    staffAttendanceTimeDetailsList.add(staffAttendanceTimeDetails);
                    staffAttendanceTimeTypeMap.put(staffAttendanceRowDetails.getStaffAttendanceType(),
                            staffAttendanceTimeDetailsList);

                }
            } else {

                List<StaffAttendanceTimeDetails> staffAttendanceTimeDetailsList = new ArrayList<>();
                staffAttendanceTimeDetailsList.add(staffAttendanceTimeDetails);
                Map<StaffAttendanceType, List<StaffAttendanceTimeDetails>> staffAttendanceTimeTypeMap = new HashMap<StaffAttendanceType, List<StaffAttendanceTimeDetails>>();
                staffAttendanceTimeTypeMap.put(staffAttendanceRowDetails.getStaffAttendanceType(), staffAttendanceTimeDetailsList);
                StaffAttendanceDetails staffAttendanceDetails = new StaffAttendanceDetails(staffAttendanceRowDetails.getInstituteId(),
                        staffAttendanceRowDetails.getAttendanceDate(), staffAttendanceRowDetails.getStaff(),
                        staffAttendanceRowDetails.getTotalDuration(), staffAttendanceRowDetails.getStaffAttendanceStatus(),
                        staffAttendanceRowDetails.getStaffTimingDetails(), staffAttendanceTimeTypeMap);
                staffAttendanceDetailsMap.put(staffId, staffAttendanceDetails);

            }
        }

        if(staffAttendanceDetailsMap == null || CollectionUtils.isEmpty(staffAttendanceDetailsMap)) {
            return null;
        }

        return sortByStaffTimings(setNextStaffAttendanceType(staffAttendanceDetailsMap));
    }

    private static Map<UUID, StaffAttendanceDetails> setNextStaffAttendanceType(Map<UUID, StaffAttendanceDetails> staffAttendanceDetailsMap) {
        for(Map.Entry<UUID, StaffAttendanceDetails> entry : staffAttendanceDetailsMap.entrySet()) {
            if(entry.getValue() == null) {
                continue;
            }
            if(CollectionUtils.isEmpty(entry.getValue().getStaffAttendanceTimeTypeMap())) {
                entry.getValue().setNextStaffAttendanceType(StaffAttendanceType.IN);
                continue;
            }
            List<StaffAttendanceTimeDetails> in = entry.getValue().getStaffAttendanceTimeTypeMap().get(StaffAttendanceType.IN);
            List<StaffAttendanceTimeDetails> out = entry.getValue().getStaffAttendanceTimeTypeMap().get(StaffAttendanceType.OUT);
            if(CollectionUtils.isEmpty(in)) {
                entry.getValue().setNextStaffAttendanceType(StaffAttendanceType.IN);
                continue;
            }
            if(!CollectionUtils.isEmpty(in) && CollectionUtils.isEmpty(out)) {
                entry.getValue().setNextStaffAttendanceType(StaffAttendanceType.OUT);
                continue;
            }
            if(in.size() == out.size()) {
                entry.getValue().setNextStaffAttendanceType(StaffAttendanceType.IN);
                continue;
            } else if (in.size() > out.size()) {
                entry.getValue().setNextStaffAttendanceType(StaffAttendanceType.OUT);
                continue;
            } else { //Can remove as this scenario should never happen
                entry.getValue().setNextStaffAttendanceType(StaffAttendanceType.IN);
                continue;
            }
        }
        return staffAttendanceDetailsMap;
    }

    private static Map<UUID, StaffAttendanceDetails> sortByStaffTimings(Map<UUID, StaffAttendanceDetails> staffAttendanceDetailsMap) {
        for(Map.Entry<UUID, StaffAttendanceDetails> entry : staffAttendanceDetailsMap.entrySet()) {
            if(entry == null || entry.getKey() == null || entry.getValue().getStaffAttendanceTimeTypeMap() == null
                    || CollectionUtils.isEmpty(entry.getValue().getStaffAttendanceTimeTypeMap().entrySet())) {
                continue;
            }
            for (Map.Entry<StaffAttendanceType, List<StaffAttendanceTimeDetails>> staffAttendanceDetails : entry.getValue().getStaffAttendanceTimeTypeMap().entrySet()) {
                if (staffAttendanceDetails == null || staffAttendanceDetails.getKey() == null || CollectionUtils.isEmpty(staffAttendanceDetails.getValue())) {
                    continue;
                }
                Collections.sort(staffAttendanceDetails.getValue(), new Comparator<StaffAttendanceTimeDetails>() {
                    @Override
                    public int compare(StaffAttendanceTimeDetails s1, StaffAttendanceTimeDetails s2) {
                        return s2.getTimeOfAction().compareTo(s1.getTimeOfAction());
                    }
                });
            }
        }
        return staffAttendanceDetailsMap;
    }

    public static List<StaffAttendanceDetails> getStaffAttendanceDetailsList(
            List<StaffAttendanceRowDetails> staffAttendanceRowDetailsList) {

        Map<UUID, StaffAttendanceDetails> staffAttendanceDetailsMap =
                getStaffAttendanceDetailsMap(staffAttendanceRowDetailsList);

        return CollectionUtils.isEmpty(staffAttendanceDetailsMap) ? null :
                new ArrayList<>(staffAttendanceDetailsMap.values());
    }

    public static Map<Integer, List<StaffAttendanceDetails>> getStaffAttendanceDetailsByDate(List<StaffAttendanceRowDetails> staffAttendanceRowDetailsList) {
        Map<Integer, List<StaffAttendanceRowDetails>> staffAttendanceRowDetailsDateMap = new HashMap<Integer, List<StaffAttendanceRowDetails>>();

        for(StaffAttendanceRowDetails staffAttendanceRowDetails : staffAttendanceRowDetailsList) {
            if(staffAttendanceRowDetails.getAttendanceDate() == null) {
                continue;
            }
            Integer date = DateUtils.getDayStart(staffAttendanceRowDetails.getAttendanceDate(), DEFAULT_TIMEZONE);
            if(date == null) {
                continue;
            }
            if(staffAttendanceRowDetailsDateMap.containsKey(date)) {
                staffAttendanceRowDetailsDateMap.get(date).add(staffAttendanceRowDetails);
            } else {
                List<StaffAttendanceRowDetails> staffAttendanceRowDetailsListEntry = new ArrayList<StaffAttendanceRowDetails>();
                staffAttendanceRowDetailsListEntry.add(staffAttendanceRowDetails);
                staffAttendanceRowDetailsDateMap.put(date, staffAttendanceRowDetailsListEntry);
            }
        }

        Map<Integer, List<StaffAttendanceDetails>> staffAttendanceDetailsDateMap = new HashMap<Integer, List<StaffAttendanceDetails>>();
        for(Map.Entry<Integer, List<StaffAttendanceRowDetails>> staffAttendanceRowDetailsEntry : staffAttendanceRowDetailsDateMap.entrySet()) {
            if(staffAttendanceRowDetailsEntry == null) {
                continue;
            }
            Integer date = DateUtils.getDayStart(staffAttendanceRowDetailsEntry.getKey(), DEFAULT_TIMEZONE);
            if(date == null) {
                continue;
            }
            staffAttendanceDetailsDateMap.put(date, getStaffAttendanceDetailsList(staffAttendanceRowDetailsEntry.getValue()));
        }

        return CollectionUtils.isEmpty(staffAttendanceDetailsDateMap) ? new HashMap<>() : staffAttendanceDetailsDateMap;
    }

}
