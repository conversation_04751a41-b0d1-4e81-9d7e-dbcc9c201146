package com.lernen.cloud.dao.tier.attendance.mappers.staff;


import com.google.gson.Gson;
import com.lernen.cloud.core.api.attendance.staff.*;
import com.lernen.cloud.core.utils.DateUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 */
public class StaffAttendanceLogRowMapper implements RowMapper<StaffAttendanceLogRow> {

    private static final String STAFF_ID = "staff_attendance_logs.staff_id";
    private static final String ATTENDANCE_DATE = "staff_attendance_logs.attendance_date";
    private static final String TIME_OF_ACTION = "staff_attendance_logs.time_of_action";
    private static final String ADDED_BY = "staff_attendance_logs.added_by";
    private static final String UPDATED_AT = "staff_attendance_logs.updated_at";
    private static final String REMARKS = "staff_attendance_logs.remarks";

    private static final Gson GSON = new Gson();

    public static final TimeZone DEFAULT_TIMEZONE = DateUtils.DEFAULT_TIMEZONE;

    @Override
    public StaffAttendanceLogRow mapRow(ResultSet rs, int rowNum) throws SQLException {

        final Timestamp timeOfActionDate = rs.getTimestamp(TIME_OF_ACTION);
        final Integer timeOfActionDateTime = timeOfActionDate == null ? null
                : (int) (timeOfActionDate.getTime() / 1000l);

        final Timestamp updatedAtDate = rs.getTimestamp(UPDATED_AT);
        final Integer updatedAtDateTime = updatedAtDate == null ? null
                : (int) (updatedAtDate.getTime() / 1000l);

        final Timestamp attendanceDate = rs.getTimestamp(ATTENDANCE_DATE);
        final Integer attendanceDateTime = attendanceDate == null ? null
                : (int) (attendanceDate.getTime() / 1000l);


        return new StaffAttendanceLogRow(UUID.fromString(rs.getString(STAFF_ID)), attendanceDateTime,
                timeOfActionDateTime == null ? null : DateUtils.calculateTime(timeOfActionDateTime), rs.getString(ADDED_BY) == null ? null : UUID.fromString(rs.getString(ADDED_BY)),
                updatedAtDateTime, rs.getString(REMARKS));
    }

    public static List<StaffAttendanceLog> getStaffAttendanceLogs(List<StaffAttendanceLogRow> staffAttendanceLogRowList) {
        List<StaffAttendanceLog> staffAttendanceLogs = new ArrayList<>();
        if (CollectionUtils.isEmpty(staffAttendanceLogRowList)) {
            return staffAttendanceLogs;
        }

        //Map of staff id and StaffAttendanceDetails
        Map<UUID, Map<Integer, List<StaffAttendanceLogRow>>> staffAttendanceLogRowMap = new HashMap<>();


        for (StaffAttendanceLogRow staffAttendanceLogRow : staffAttendanceLogRowList) {
            UUID staffId = staffAttendanceLogRow.getStaffId();
            if(!staffAttendanceLogRowMap.containsKey(staffId)){
                staffAttendanceLogRowMap.put(staffId, new HashMap<>());
            }
            int attendanceDate = staffAttendanceLogRow.getAttendanceDate();

            Map<Integer, List<StaffAttendanceLogRow>> staffAttendanceDateLogMap = staffAttendanceLogRowMap.get(staffId);
            if(!staffAttendanceDateLogMap.containsKey(attendanceDate)){
                staffAttendanceDateLogMap.put(attendanceDate, new ArrayList<>());
            }

            staffAttendanceDateLogMap.get(attendanceDate).add(staffAttendanceLogRow);
        }

        for(Map.Entry<UUID, Map<Integer, List<StaffAttendanceLogRow>>> staffEntry : staffAttendanceLogRowMap.entrySet()){
            UUID staffId = staffEntry.getKey();
            List<StaffAttendanceDayLog> staffAttendanceDayLogs = new ArrayList<>();
            for(Map.Entry<Integer, List<StaffAttendanceLogRow>> staffDateEntry : staffEntry.getValue().entrySet()){
                int attendanceDate = staffDateEntry.getKey();
                List<StaffAttendanceTimeDetails> staffAttendanceTimeDetailsList = new ArrayList<>();
                for(StaffAttendanceLogRow staffAttendanceLogRow : staffDateEntry.getValue()){
                    staffAttendanceTimeDetailsList.add(new StaffAttendanceTimeDetails(staffAttendanceLogRow.getTimeOfAction(), staffAttendanceLogRow.getAddedBy(), staffAttendanceLogRow.getUpdatedAt(), staffAttendanceLogRow.getRemarks()));
                }
                Collections.sort(staffAttendanceTimeDetailsList);
                staffAttendanceDayLogs.add(new StaffAttendanceDayLog(attendanceDate, staffAttendanceTimeDetailsList));
            }
            Collections.sort(staffAttendanceDayLogs);
            staffAttendanceLogs.add(new StaffAttendanceLog(staffId, staffAttendanceDayLogs));
        }

        return staffAttendanceLogs;
    }

}
