package com.lernen.cloud.dao.tier.attendance.mappers.staff;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceStatus;
import com.lernen.cloud.core.api.attendance.staff.v2.*;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.dao.tier.staff.mappers.StaffRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 */
public class StaffIdAttendanceRegisterRowMapperV2 implements RowMapper<StaffIdAttendanceRegisterRow> {


    private static final String STAFF_ID = "staff_attendance_register.staff_id";
    private static final String ATTENDANCE_DATE = "staff_attendance_register.attendance_date";
    private static final String TOTAL_DURATION = "staff_attendance_register.total_duration";
    private static final String METADATA = "staff_attendance_register.metadata";
    private static final String ATTENDANCE_STATUS = "staff_attendance_register.attendance_status";
    private static final String REMARKS = "staff_attendance_register.remarks";

    private static final Gson GSON = new Gson();
    public static final TimeZone DEFAULT_TIMEZONE = TimeZone.getTimeZone("Asia/Kolkata");

    @Override
    public StaffIdAttendanceRegisterRow mapRow(ResultSet rs, int rowNum) throws SQLException {
        StaffAttendanceDayMetadata staffAttendanceDayMetadata = rs.getString(METADATA) == null ? null : GSON.fromJson(rs.getString(METADATA),
                new TypeToken<StaffAttendanceDayMetadata>() {
                }.getType());


        final Timestamp attendanceDate = rs.getTimestamp(ATTENDANCE_DATE);
        final Integer attendanceDateTime = attendanceDate == null ? null
                : (int) (attendanceDate.getTime() / 1000l);

        Double totalDuration = rs.getString(TOTAL_DURATION) == null ? null : rs.getDouble(TOTAL_DURATION);
        String remarks = rs.getString(REMARKS) == null ? null : rs.getString(REMARKS);

        return new StaffIdAttendanceRegisterRow(UUID.fromString(rs.getString(STAFF_ID)), attendanceDateTime, totalDuration,
                StaffAttendanceStatus.getStaffAttendanceStatus(rs.getString(ATTENDANCE_STATUS)), staffAttendanceDayMetadata, remarks);
    }

    public static List<StaffAttendanceSummary> getStaffAttendanceSummary(List<StaffIdAttendanceRegisterRow> staffAttendanceRegisterRows) {
        List<StaffAttendanceSummary> staffAttendanceRegisterDataList = new ArrayList<>();
        if (CollectionUtils.isEmpty(staffAttendanceRegisterRows)) {
            return staffAttendanceRegisterDataList;
        }

        Map<UUID, List<StaffIdAttendanceRegisterRow>> staffAttendanceRegisterRowMap = new HashMap<>();
        for (StaffIdAttendanceRegisterRow staffAttendanceRegisterRow : staffAttendanceRegisterRows) {
            if (staffAttendanceRegisterRow == null) {
                continue;
            }
            UUID staffId = staffAttendanceRegisterRow.getStaffId();
            if (!staffAttendanceRegisterRowMap.containsKey(staffId)) {
                staffAttendanceRegisterRowMap.put(staffId, new ArrayList<>());
            }

            staffAttendanceRegisterRowMap.get(staffId).add(staffAttendanceRegisterRow);
        }


        for (Map.Entry<UUID, List<StaffIdAttendanceRegisterRow>> entry : staffAttendanceRegisterRowMap.entrySet()) {
            StaffIdAttendanceRegisterRow firstStaffAttendanceRegisterRow = entry.getValue().get(0);
            List<StaffAttendanceDaySummary> staffAttendanceDaySummaryList = new ArrayList<>();
            for (StaffIdAttendanceRegisterRow staffAttendanceRegisterRow : entry.getValue()) {
                if(firstStaffAttendanceRegisterRow.getAttendanceDate() != null){
                    staffAttendanceDaySummaryList.add(new StaffAttendanceDaySummary(staffAttendanceRegisterRow.getAttendanceDate(),
                            staffAttendanceRegisterRow.getTotalDurationInSec(), staffAttendanceRegisterRow.getStaffAttendanceStatus(),
                            staffAttendanceRegisterRow.getStaffAttendanceDayMetadata(), staffAttendanceRegisterRow.getRemarks()));
                }
            }
            staffAttendanceRegisterDataList.add(new StaffAttendanceSummary(firstStaffAttendanceRegisterRow.getStaffId(), staffAttendanceDaySummaryList));
        }

        return staffAttendanceRegisterDataList;
    }
}
