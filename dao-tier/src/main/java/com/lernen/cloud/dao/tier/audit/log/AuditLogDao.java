package com.lernen.cloud.dao.tier.audit.log;

import com.google.gson.Gson;
import com.lernen.cloud.core.api.audit.log.AuditLog;
import com.lernen.cloud.core.api.audit.log.AuditLogPayload;
import com.lernen.cloud.core.api.common.PaginationInfo;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.dao.tier.audit.log.mappers.AuditLogRowMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 *
 */
public class AuditLogDao {

	private static final Logger logger = LogManager.getLogger(AuditLogDao.class);
	private final JdbcTemplate jdbcTemplate;
	private static final Gson GSON = SharedConstants.GSON;
	private static final AuditLogRowMapper AUDIT_LOG_ROW_MAPPER = new AuditLogRowMapper();

	private static final String GET_LOGS = "select audit_logs.*, users.* from audit_logs join users on audit_logs.user_id = users.user_id ";
	private static final String GET_LOGS_WHERE_CLAUSE = " where audit_logs.institute_id = ? %s and log_timestamp >= ? and log_timestamp <= ? ";
	private static final String OFFSET_CLAUSE = " order by log_timestamp desc limit ? offset ? ";
	private static final String USER_FILTER_CLAUSE = " and audit_logs.user_id = ? ";

	private static final String GET_LOGS_COUNT_QUERY = "select count(*) from audit_logs join users on audit_logs.user_id = users.user_id ";

	private static final String USER_SEARCH_CONDITION = " and concat_ws('/', users.user_institute_id, users.user_name) like ? ";

	private static final String GET_LOG_BY_ID = "select audit_logs.*, users.* from audit_logs join users on "
			+ " audit_logs.user_id = users.user_id  where audit_logs.institute_id = ? and log_id = ? ";

	private static final String INSERT_LOG = "insert into audit_logs(institute_id, log_id, user_id, module, action_type, action, action_data, previous_state, final_state, mini_log_statement, detailed_log_statement) "
			+ "values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	public AuditLogDao(JdbcTemplate jdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
	}

	public boolean addLog(AuditLogPayload auditLogPayload) {
		try {
			final UUID logId = UUID.randomUUID();
			final List<Object> args = new ArrayList<>();
			args.add(auditLogPayload.getInstituteId());
			args.add(logId.toString());
			args.add(auditLogPayload.getUserId().toString());
			args.add(auditLogPayload.getAuditLogActionData().getAuditLogAction().getModule().name());
			args.add(auditLogPayload.getAuditLogActionData().getAuditLogAction().getAuditLogActionType().name());
			args.add(auditLogPayload.getAuditLogActionData().getAuditLogAction().name());
			args.add(GSON.toJson(auditLogPayload.getAuditLogActionData()));
			args.add(null);
			args.add(null);
			args.add(auditLogPayload.getMiniLogStatement());
			args.add(auditLogPayload.getDetailedLogStatement());
			return jdbcTemplate.update(INSERT_LOG, args.toArray()) == 1;
		} catch (final Exception e) {
			logger.error("Error while adding audit log for institute {}, user {}", auditLogPayload.getInstituteId(),
					auditLogPayload.getUserId(), e);
			return false;
		}
	}

	public SearchResultWithPagination<AuditLog> getAuditLogs(int instituteId, String searchText, int start, int end,
			int offset, int limit) {

		try {

			String query = GET_LOGS;
			String countQuery = GET_LOGS_COUNT_QUERY;

			final List<Object> args = new ArrayList<>();
			args.add(instituteId);
			if (StringUtils.isBlank(searchText)) {
				final String whereClause = String.format(GET_LOGS_WHERE_CLAUSE, "");
				query = query + whereClause + OFFSET_CLAUSE;
				countQuery += whereClause;
			} else {
				String userFilterClause = "";
				final String[] keywords = searchText.toLowerCase().split(" ");
	
				for (String keyword : keywords) {
					keyword = keyword.trim();
					if (StringUtils.isBlank(keyword)) {
						continue;
					}
					userFilterClause += USER_SEARCH_CONDITION;
					args.add("%" + keyword + "%");
				}
				final String whereClause = String.format(GET_LOGS_WHERE_CLAUSE, userFilterClause);
				query = query + whereClause + OFFSET_CLAUSE;
				countQuery += whereClause;

			}

			args.add(new Timestamp(start * 1000l));
			args.add(new Timestamp(end * 1000l));

			final int totalResultCount = jdbcTemplate.queryForObject(countQuery, Integer.class, args.toArray());
			final PaginationInfo paginationInfo = new PaginationInfo(totalResultCount, limit, offset);

			args.add(limit);
			args.add(offset);
			final List<AuditLog> auditLogs = jdbcTemplate.query(query, args.toArray(), AUDIT_LOG_ROW_MAPPER);
			final SearchResultWithPagination<AuditLog> resultWithPagination = new SearchResultWithPagination<>(
					paginationInfo, auditLogs);
			return resultWithPagination;
		} catch (final Exception e) {
			logger.error("Error while fetching logs for instituteId {}, start {}, end {}, offset {}, limit {}",
					instituteId, start, end, offset, limit, e);
			return null;
		}

	}

	public AuditLog getAuditLog(int instituteId, UUID logId) {

		try {
			final List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(logId.toString());

			return jdbcTemplate.queryForObject(GET_LOG_BY_ID, args.toArray(), AUDIT_LOG_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while fetching log for instituteId {}, logId {}", instituteId, logId, e);
			return null;
		}

	}

}
