package com.lernen.cloud.dao.tier.complainbox.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.complainbox.StudentComplaintResponses;

public class ComplaintResponsesRowMapper implements RowMapper<StudentComplaintResponses>{
    private static final String RESPONSE_ID = "complain_responses.response_id";
    private static final String RESPONSE = "complain_responses.response";
    private static final String RESPONSE_BY = "complain_responses.response_by";
    private static final String RESPONSE_AT = "complain_responses.response_at";
    private static final String COMPLAIN_ID = "complain_responses.complain_id";

    @Override
    public StudentComplaintResponses mapRow(ResultSet rs, int rowNum) throws SQLException {
        if(rs.getString(RESPONSE_ID) == null) {
            return null;
        }
        final Timestamp responseAt = rs.getTimestamp(RESPONSE_AT);
		final Integer responseTime = responseAt == null ? null
				: (int) (responseAt.getTime() / 1000l);
        return new StudentComplaintResponses(UUID.fromString(rs.getString(RESPONSE_ID)),
                rs.getString(RESPONSE), UUID.fromString(rs.getString(RESPONSE_BY)),
                UUID.fromString(rs.getString(COMPLAIN_ID)), responseTime);
    }
}
