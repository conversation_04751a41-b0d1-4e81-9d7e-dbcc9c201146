package com.lernen.cloud.dao.tier.complainbox.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.complainbox.ComplainCategoryPayload;
import com.lernen.cloud.core.api.complainbox.StandardComplainResponse;
import com.lernen.cloud.core.api.complainbox.StandardResponseDetails;

public class StandardComplainResponseDetailsRowMapper implements RowMapper<StandardResponseDetails>{

    private static final ComplainCategoryRowMapper COMPLAINT_CATEGORY_ROW_MAPPER = new ComplainCategoryRowMapper();
    private static final StandardComplainResponseRowMapper STANDARD_RESPONSE_ROW_MAPPER = new StandardComplainResponseRowMapper();

    @Override
    public StandardResponseDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
        final ComplainCategoryPayload complainCategoryPayload = COMPLAINT_CATEGORY_ROW_MAPPER.mapRow(rs, rowNum);
        if(complainCategoryPayload == null) {
            return null;
        }
        final StandardComplainResponse standardComplainResponse = STANDARD_RESPONSE_ROW_MAPPER.mapRow(rs, rowNum);
        return new StandardResponseDetails(standardComplainResponse, complainCategoryPayload);
    }
}
