package com.lernen.cloud.dao.tier.course.mappers;

import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.CourseType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class CourseRowMapper implements RowMapper<Course> {

	public static final String COURSE_ID = "course_id";
	private static final String COURSE_KEY = "course_key";
	private static final String COURSE_TYPE = "course_type";
	public static final String COURSE_NAME = "course_name";
	private static final String MANDATORY = "mandatory";
	private static final String SEQUENCE = "course_sequence";

	@Override
	public Course mapRow(ResultSet rs, int rowNum) throws SQLException {
		String courseId = rs.getString(COURSE_ID);
		if(StringUtils.isBlank(courseId)) {
			return null;
		}
		String sequenceStr = rs.getString(SEQUENCE);
		Integer sequence = null;
		if(!StringUtils.isBlank(sequenceStr)) {
			sequence = Integer.parseInt(sequenceStr);
		}

		return new Course(courseId, rs.getString(COURSE_KEY), rs.getString(COURSE_TYPE) == null ? null :
				CourseType.getCourseType(rs.getString(COURSE_TYPE)), rs.getString(COURSE_NAME),
				rs.getBoolean(MANDATORY), sequence);
	}
}