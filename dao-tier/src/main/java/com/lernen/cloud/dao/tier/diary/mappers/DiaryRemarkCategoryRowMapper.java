package com.lernen.cloud.dao.tier.diary.mappers;

import com.lernen.cloud.core.api.diary.DiaryRemarkCategory;
import com.lernen.cloud.core.api.diary.RemarkUserType;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class DiaryRemarkCategoryRowMapper implements RowMapper<DiaryRemarkCategory> {

    private static final String INSTITUTE_ID = "diary_remarks_category.institute_id";
    private static final String CATEGORY_ID = "diary_remarks_category.category_id";
    private static final String CATEGORY_NAME = "diary_remarks_category.category_name";
    private static final String REMARK_USER_TYPE = "diary_remarks_category.remark_user_type";

    @Override
    public DiaryRemarkCategory mapRow(ResultSet rs, int rowNum) throws SQLException {
        return new DiaryRemarkCategory(rs.getInt(INSTITUTE_ID), rs.getInt(CATEGORY_ID), rs.getString(CATEGORY_NAME),
                RemarkUserType.getRemarkUserType(rs.getString(REMARK_USER_TYPE)));
    }
}

