package com.lernen.cloud.dao.tier.examination.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.examination.ExamCourse;
import com.lernen.cloud.core.api.examination.ExamCoursePublishedStatus;
import com.lernen.cloud.core.api.examination.ExamCoursesData;
import com.lernen.cloud.core.api.examination.ExamDimensionValues;
import com.lernen.cloud.core.api.examination.ExamMetaData;
import com.lernen.cloud.dao.tier.course.mappers.CourseRowMapper;

/**
 *
 * <AUTHOR>
 *
 */
public class ExamCourseRowMapper implements RowMapper<ExamCoursesData> {

	private static final ExamMetaDataRowMapper EXAM_META_DATA_ROW_MAPPER = new ExamMetaDataRowMapper();
	private static final ExamCourseDimensionValuesRowMapper EXAM_COURSE_DIMENSION_VALUES_ROW_MAPPER = new ExamCourseDimensionValuesRowMapper();
	private static final CourseRowMapper COURSE_ROW_MAPPER = new CourseRowMapper();

	@Override
	public ExamCoursesData mapRow(ResultSet rs, int rowNum) throws SQLException {
		final ExamMetaData examMetaData = EXAM_META_DATA_ROW_MAPPER.mapRow(rs, rowNum);
		final ExamDimensionValues examCourseDimensionValues = EXAM_COURSE_DIMENSION_VALUES_ROW_MAPPER.mapRow(rs,
				rowNum);
		final Course course = COURSE_ROW_MAPPER.mapRow(rs, rowNum);
		final List<ExamDimensionValues> examDimensionValuesList = new ArrayList<>();
		examDimensionValuesList.add(examCourseDimensionValues);
		return new ExamCoursesData(examMetaData, Arrays.asList(new ExamCourse(course, examDimensionValuesList)));

	}

	/**
	 * This method assumes that all the rows belong to same exam id
	 *
	 * @param examCourses
	 * @return
	 */
	public static ExamCoursesData getExamCoursesDataForSingleExam(List<ExamCoursesData> examCoursesDatas) {
		if (CollectionUtils.isEmpty(examCoursesDatas)) {
			return null;

		}
		final ExamMetaData examMetaData = examCoursesDatas.get(0).getExamMetaData();
		final Map<UUID, ExamCourse> examCoursesMap = new HashMap<>();
		for (final ExamCoursesData examCoursesData : examCoursesDatas) {
			final ExamCourse examCourse = examCoursesData.getExamCourses().get(0);
			final UUID courseId = examCourse.getCourse().getCourseId();
			if (!examCoursesMap.containsKey(courseId)) {
				examCoursesMap.put(courseId,
						new ExamCourse(examCourse.getCourse(), examCourse.getExamDimensionValues()));
				continue;
			}
			examCoursesMap.get(courseId).getExamDimensionValues().addAll(examCourse.getExamDimensionValues());
		}
		return new ExamCoursesData(examMetaData, new ArrayList<>(examCoursesMap.values()));
	}

	public static List<ExamCoursesData> getExamCoursesDataForMultipleExams(List<ExamCoursesData> examCoursesDatasRows) {
		final List<ExamCoursesData> examCoursesDatas = new ArrayList<>();
		if (CollectionUtils.isEmpty(examCoursesDatasRows)) {
			return examCoursesDatas;
		}
		final Map<UUID, List<ExamCoursesData>> examCourseDataMap = new HashMap<>();
		for (final ExamCoursesData examCoursesData : examCoursesDatasRows) {
			if (!examCourseDataMap.containsKey(examCoursesData.getExamMetaData().getExamId())) {
				final List<ExamCoursesData> examCoursesDataList = new ArrayList<>();
				examCoursesDataList.add(examCoursesData);
				examCourseDataMap.put(examCoursesData.getExamMetaData().getExamId(), examCoursesDataList);
				continue;
			}
			examCourseDataMap.get(examCoursesData.getExamMetaData().getExamId()).add(examCoursesData);
		}
		for (final Entry<UUID, List<ExamCoursesData>> entry : examCourseDataMap.entrySet()) {
			examCoursesDatas.add(getExamCoursesDataForSingleExam(entry.getValue()));
		}
		return examCoursesDatas;
	}
}
