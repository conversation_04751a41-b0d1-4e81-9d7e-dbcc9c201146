package com.lernen.cloud.dao.tier.examination.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.examination.ExamDimension;
import com.lernen.cloud.core.api.examination.ExamDimensionValues;

/**
 *
 * <AUTHOR>
 *
 */
public class ExamDefaultDimensionValuesRowMapper implements RowMapper<ExamDimensionValues> {

	private static final String DEFAULT_MAX_MARKS = "default_max_marks";
	private static final String DEFAULT_MIN_MARKS = "default_min_marks";
	private static final String DEFAULT_MAX_GRADE = "default_max_grade";
	private static final String DEFAULT_MIN_GRADE = "default_min_grade";
	//
	// private static final String EXAM_ID = "exam_id";
	// private static final String COURSE_TYPE = "course_type";
	// private static final String DIMENSION_ID = "dimension_id";
	//
	// private static final String EXAM_ID = "exam_id";
	// private static final String CHILD_EXAM_ID = "child_exam_id";
	// private static final String EXAM_ID = "exam_id";
	// private static final String COURSE_ID = "course_id";
	// private static final String DIMENSION_ID = "dimension_id";
	// private static final String MAX_MARKS = "max_marks";
	// private static final String MIN_MARKS = "min_marks";
	// private static final String MAX_GRADE = "max_grade";
	// private static final String MIN_GRADE = "min_grade";

	private static final ExamDimensionRowMapper EXAM_DIMENSION_ROW_MAPPER = new ExamDimensionRowMapper();

	@Override
	public ExamDimensionValues mapRow(ResultSet rs, int rowNum) throws SQLException {
		final ExamDimension examDimension = EXAM_DIMENSION_ROW_MAPPER.mapRow(rs, rowNum);

		Double defaultMaxMarks = rs.getDouble(DEFAULT_MAX_MARKS);
		if (rs.wasNull()) {
			defaultMaxMarks = null;
		}

		Double defaultMinMarks = rs.getDouble(DEFAULT_MIN_MARKS);
		if (rs.wasNull()) {
			defaultMinMarks = null;
		}

		return new ExamDimensionValues(examDimension, defaultMaxMarks, defaultMaxMarks, defaultMinMarks, null, null, null);
	}

}
