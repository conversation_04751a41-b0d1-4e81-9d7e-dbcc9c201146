package com.lernen.cloud.dao.tier.examination.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.examination.ExamDimension;
import com.lernen.cloud.core.api.examination.ExamDimensionType;
import com.lernen.cloud.core.api.examination.ExamEvaluationType;

/**
 *
 * <AUTHOR>
 *
 */
public class ExamDimensionRowMapper implements RowMapper<ExamDimension> {

	private static final String DIMENSION_ID = "dimension_id";
	private static final String INSTITUTE_ID = "institute_id";
	private static final String DIMENSION_NAME = "dimension_name";
	private static final String DIMENSION_TYPE = "dimension_type";
	private static final String EVALUATION_TYPE = "evaluation_type";
	private static final String IS_TOTAL = "is_total";

	@Override
	public ExamDimension mapRow(ResultSet rs, int rowNum) throws SQLException {
		return new ExamDimension(rs.getInt(DIMENSION_ID), rs.getString(DIMENSION_NAME),
				ExamDimensionType.valueOf(rs.getString(DIMENSION_TYPE)),
				ExamEvaluationType.valueOf(rs.getString(EVALUATION_TYPE)), rs.getBoolean(IS_TOTAL));
	}

}
