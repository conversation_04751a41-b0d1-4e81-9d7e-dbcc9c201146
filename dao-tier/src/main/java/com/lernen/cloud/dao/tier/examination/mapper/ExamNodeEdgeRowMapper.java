package com.lernen.cloud.dao.tier.examination.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.examination.ExamMetaData;
import com.lernen.cloud.core.api.examination.ExamNodeData;

/**
 *
 * <AUTHOR>
 *
 */
public class ExamNodeEdgeRowMapper implements RowMapper<ExamNodeData> {

	private static final String CHILD_EXAM_ID = "child_exam_id";

	private static final ExamMetaDataRowMapper EXAM_META_DATA_ROW_MAPPER = new ExamMetaDataRowMapper();

	@Override
	public ExamNodeData mapRow(ResultSet rs, int rowNum) throws SQLException {
		final ExamMetaData examMetaData = EXAM_META_DATA_ROW_MAPPER.mapRow(rs, rowNum);
		Set<UUID> childExamId = null;
		final String childExamIdString = rs.getString(CHILD_EXAM_ID);
		if (!StringUtils.isBlank(childExamIdString)) {
			childExamId = new HashSet<>(Arrays.asList(UUID.fromString(childExamIdString)));
		}
		return new ExamNodeData(examMetaData, null, childExamId);
	}

	public static List<ExamNodeData> getExamNodeData(List<ExamNodeData> examNodeEdges) {
		final List<ExamNodeData> examNodeEdgesCombined = new ArrayList<>();
		if (CollectionUtils.isEmpty(examNodeEdges)) {
			return examNodeEdgesCombined;
		}
		final Map<UUID, ExamNodeData> examNodeEdgesMap = new HashMap<>();
		for (final ExamNodeData examNodeEdge : examNodeEdges) {
			final UUID examId = examNodeEdge.getExamMetaData().getExamId();
			if (!examNodeEdgesMap.containsKey(examId)) {
				examNodeEdgesMap.put(examId,
						new ExamNodeData(examNodeEdge.getExamMetaData(), examNodeEdge.getCourses(), new HashSet<>()));
			}
			if (!CollectionUtils.isEmpty(examNodeEdge.getChildIds())) {
				examNodeEdgesMap.get(examId).getChildIds().addAll(examNodeEdge.getChildIds());
			}
		}
		return new ArrayList<>(examNodeEdgesMap.values());
	}

}
