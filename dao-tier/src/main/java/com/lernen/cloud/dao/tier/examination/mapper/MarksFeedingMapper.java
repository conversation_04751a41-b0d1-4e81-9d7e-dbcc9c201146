package com.lernen.cloud.dao.tier.examination.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.examination.MarksFeedData;

/**
 *
 * <AUTHOR>
 *
 */

public class MarksFeedingMapper implements RowMapper<MarksFeedData> {

	private static final String STUDENT_ID = "student_id";
	private static final String EXAM_ID = "exam_id";
	private static final String COURSE_ID = "course_id";
	private static final String DIMENSION_ID = "dimension_id";
	private static final String MARKS_OBTAINED = "marks_obtained";
	private static final String GRADE_OBTAINED = "grade_obtained";
	private static final String GRACE_MARKS = "grace_marks";

	/**
	 * will only give studentId, examID and courseId 
	 * of marks added for a student in a particular session
	 * Doesnot contain dimension values
	 */
	@Override
	public MarksFeedData mapRow(ResultSet rs, int rowNum) throws SQLException {

		String studentId = rs.getString(STUDENT_ID);
		String examId = rs.getString(EXAM_ID);
		String courseId = rs.getString(COURSE_ID);
		
		if(StringUtils.isEmpty(studentId) || StringUtils.isEmpty(examId) || StringUtils.isEmpty(courseId)) {
			return null;
		}
		 return new MarksFeedData(UUID.fromString(studentId), UUID.fromString(examId), UUID.fromString(courseId));
	}

}
