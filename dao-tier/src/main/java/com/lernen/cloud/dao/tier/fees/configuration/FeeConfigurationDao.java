package com.lernen.cloud.dao.tier.fees.configuration;

import com.embrate.cloud.core.api.cache.ICacheKey;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.fees.*;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.utils.cache.CacheFactory;
import com.lernen.cloud.core.utils.cache.ICache;
import com.lernen.cloud.core.utils.cache.ICacheLoader;
import com.lernen.cloud.core.utils.exceptions.ExceptionHandling;
import com.lernen.cloud.dao.tier.fees.configuration.mappers.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 */
public class FeeConfigurationDao {

    private static final Logger logger = LogManager.getLogger(FeeConfigurationDao.class);
    private static final String FOR_UPDATE_CLAUSE = "FOR UPDATE";
    private static final String FOR_SHARE_CLAUSE = "LOCK IN SHARE MODE";
    private static final FeeConfigurationRowMapper FEE_CONFIGURATION_ROW_MAPPER = new FeeConfigurationRowMapper();
    private static final FeeHeadConfigurationResponseRowMapper FEE_HEAD_CONFIGURATION_RESPONSE_ROW_MAPPER = new FeeHeadConfigurationResponseRowMapper();
    private static final FeeAssignmentRowMapper FEE_ASSIGNMENT_ROW_MAPPER = new FeeAssignmentRowMapper();
    private static final DefaultFeeAssignmentStructureRowMapper DEFAULT_FEE_ASSIGNMENT_STRUCTURE_ROW_MAPPER = new DefaultFeeAssignmentStructureRowMapper();
    private static final FeeCategoryRowMapper FEE_CATEGORY_ROW_MAPPER = new FeeCategoryRowMapper();
    private static final FeeHeadConfigurationRowMapper FEE_HEAD_CONFIGURATION_ROW_MAPPER = new FeeHeadConfigurationRowMapper();
    private static final FeeAssignmentPermissionRowMapper FEE_ASSIGNMENT_PERMISSION_ROW_MAPPER = new FeeAssignmentPermissionRowMapper();
    private static final AuthorizedFeeAssignmentRowMapper AUTHORIZED_FEE_ASSIGNMENT_ROW_MAPPER = new AuthorizedFeeAssignmentRowMapper();
    private static final FeeStructureMetaDataRowMapper FEE_STRUCTURE_META_DATA_ROW_MAPPER = new FeeStructureMetaDataRowMapper();
    private static final String FEE_HEAD = "Fee Head";
    private static final String FEES = "Fee";
    // fee_configuration_month_year_mapping
    // Fee Category Queries
    private static final String ADD_FEE_CATEGORY = "insert into fee_category (institute_id, fee_category, description) values (?, ?, ?)";
    private static final String GET_FEE_CATEGORY = "select * from fee_category where institute_id = ?";
    private static final String GET_FEE_HEAD_CONFIGURATION = "select * from fee_head_configuration where institute_id = ? and fee_head = ?";
    private static final String GET_AUTHORIZED_FEE_ASSIGNMENTS = "select fee_configuration.*, academic_session.*, fee_head_configuration.*, "
            + "fee_category.*, fee_assignment_permissions.* from fee_configuration join academic_session on "
            + "fee_configuration.academic_session_id = academic_session.academic_session_id join fee_head_configuration on "
            + "fee_configuration.institute_id = fee_head_configuration.institute_id join fee_category on "
            + "fee_head_configuration.fee_category_id = fee_category.fee_category_id left join fee_assignment_permissions on "
            + "fee_configuration.fee_id = fee_assignment_permissions.fee_id and fee_head_configuration.fee_head_id = fee_assignment_permissions.fee_head_id "
            + "where fee_configuration.institute_id = ? and fee_configuration.academic_session_id = ? "
            + "order by fee_configuration.fee_name , fee_head_configuration.fee_head";
    private static final String ADD_FEE_CONFIGURATION = "insert into fee_configuration(institute_id, fee_id, fee_name, fee_type, due_date, "
            + "academic_session_id, start_month, start_year, end_month, end_year, allow_pending_enrollment, fine_applicable, transfer_to_wallet, description) "
            + "values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    private static final String GET_FEE_CONFIGURATION_DETAILS = "select fee_configuration.*, academic_session.* from fee_configuration join "
            + "academic_session on fee_configuration.academic_session_id = academic_session.academic_session_id where "
            + "fee_configuration.institute_id = ? and fee_configuration.fee_id = ?";


    //

    // Fee Configuration Queries
    private static final String GET_FEE_CONFIGURATIONS_DETAILS = "select fee_configuration.*, academic_session.* from fee_configuration join "
            + "academic_session on fee_configuration.academic_session_id = academic_session.academic_session_id where "
            + "fee_configuration.institute_id = ? and fee_configuration.fee_id in %s order by fee_configuration.start_year, fee_configuration.start_month asc";
    private static final String GET_FEE_CONFIGURATION_BY_ACADEMIC_SESSION = "select fee_configuration.*, academic_session.* from "
            + "fee_configuration join academic_session on fee_configuration.academic_session_id = academic_session.academic_session_id where "
            + "fee_configuration.institute_id = ? and fee_configuration.academic_session_id = ? order by fee_configuration.start_year, fee_configuration.start_month asc";
    private static final String GET_ALL_FEE_CONFIGURATION = "select fee_configuration.*, academic_session.* from "
            + "fee_configuration join academic_session on fee_configuration.academic_session_id = academic_session.academic_session_id where "
            + "fee_configuration.institute_id = ? order by fee_configuration.start_year, fee_configuration.start_month asc";
    private static final String UPDATE_FEE_CONFIGURATION_BY_FEE_ID = "update fee_configuration set fee_name = ?, due_date = ?, "
            + "description = ?, allow_pending_enrollment = ?, fine_applicable = ?, transfer_to_wallet = ? where institute_id = ? and fee_id = ?";
    private static final String UPDATE_SPECIAL_FEE_CONFIGURATION_BY_FEE_ID = "update fee_configuration set fee_name = ?, due_date = ?, start_month = ?, "
            + "start_year = ?, end_month = ?, end_year = ?, description = ?, allow_pending_enrollment = ?, fine_applicable = ?, transfer_to_wallet = ? where institute_id = ? and fee_id = ?";
    private static final String DELETE_REGULAR_FEE_CONFIGURATION_BY_ACADEMIC_SESSION = "delete from fee_configuration where institute_id = ? and academic_session_id = ? and fee_type = ?";
    private static final String DELETE_FEE_CONFIGURATION_BY_FEE_ID = "delete from fee_configuration where institute_id = ? and fee_id = ? and fee_type = ?";
    // Delete Fee Structure Queries
    private static final String DELETE_FROM_DEFAULT_FEE_STRUCTURE = "delete from default_fee_assignment_structure where structure_id = ?";
    private static final String DELETE_FROM_DEFAULT_FEE_STRUCTURE_META_DATA = "delete from default_fee_assignment_structure_meta_data where structure_id = ?";
    // Update Fee structure Name
    private static final String UPDATE_DEFAULT_FEE_STRUCTURE = "update default_fee_assignment_structure_meta_data set structure_name = ?, structure_type = ? where structure_id =?";
    // Fee head Configuration Queries
    private static final String ADD_FEE_HEAD_CONFIGURATION = "insert into fee_head_configuration(institute_id, fee_category_id, fee_head, "
            + "refundable, description, fee_head_type, fee_head_tag) values(?, ?, ?, ?, ?, ?, ?)";
    private static final String GET_FEE_HEAD_CONFIGURATION_DETAILS_BY_INSTITUTE_ID = "select fee_head_configuration.*, fee_category.* from fee_head_configuration join fee_category on fee_head_configuration.fee_category_id = fee_category.fee_category_id where fee_head_configuration.institute_id = ?";
    private static final String GET_TRANSPORT_FEE_HEAD_BY_INSTITUTE_ID = "select fee_head_id from fee_head_configuration where institute_id = ? and fee_head_type = 'SYSTEM' and fee_head_tag = 'TRANSPORT' ";
    private static final String UPDATE_FEE_HEAD_CONFIGURATION_DETAILS_BY_INSTITUTE_ID_FEE_HEAD_ID = "update fee_head_configuration set "
            + "fee_head = ?, fee_category_id = ?, refundable = ?, description = ? where institute_id = ? and fee_head_id = ? and fee_head_type = 'CUSTOM'";
    private static final String UPDATE_SYSTEM_FEE_HEAD_CONFIGURATION_DETAILS_BY_INSTITUTE_ID_FEE_HEAD_ID = "update fee_head_configuration set "
            + "fee_head = ?, fee_category_id = ?, refundable = ?, description = ? where institute_id = ? and fee_head_id = ? and fee_head_type = 'SYSTEM'";
    private static final String DELETE_FEE_HEAD_CONFIGURATION_DETAILS_BY_INSTITUTE_ID_FEE_HEAD_ID = "delete from fee_head_configuration "
            + "where institute_id = ? and fee_head_id = ? and fee_head_type = 'CUSTOM'";
    private static final String DELETE_SYSTEM_FEE_HEAD_CONFIGURATION_DETAILS_BY_INSTITUTE_ID_FEE_HEAD_ID = "delete from fee_head_configuration "
            + "where institute_id = ? and fee_head_id = ? and fee_head_type = 'SYSTEM'";
    // Assign fees Queries
    private static final String ASSIGN_FEES = "insert into fee_assignment(institute_id, fee_id, entity_id, entity_name, fee_head_id, amount)"
            + "values(?, ?, ?, ?, ?, ?)";
    private static final String UPDATE_FEE_ASSIGNMENT = "update fee_assignment set amount = ? where fee_id = ? and entity_id = ? and "
            + "fee_head_id = ? and institute_id = ?";
    private static final String GET_FEE_ASSIGNMENT_IN_ACADEMIC_SESSION = "select fee_assignment.*, fee_configuration.*, "
            + "fee_head_configuration.*, academic_session.*, fee_assignment_permissions.module_name from fee_assignment join fee_configuration on "
            + "fee_assignment.fee_id = fee_configuration.fee_id %s "
            + "join fee_head_configuration on fee_head_configuration.fee_head_id = fee_assignment.fee_head_id join academic_session on "
            + "fee_configuration.academic_session_id = academic_session.academic_session_id left join fee_assignment_permissions on "
            + "fee_assignment.fee_id = fee_assignment_permissions.fee_id and fee_assignment.fee_head_id = fee_assignment_permissions.fee_head_id "
            + "where fee_assignment.entity_id in %s and " + "fee_assignment.institute_id = ? %s";
    private static final String GET_FEE_ASSIGNMENT_BY_FEE_IDS = "select fee_assignment.*, fee_configuration.*, "
            + "fee_head_configuration.*, academic_session.*, fee_assignment_permissions.module_name from fee_assignment join fee_configuration on "
            + "fee_assignment.fee_id = fee_configuration.fee_id join "
            + "fee_head_configuration on fee_head_configuration.fee_head_id = fee_assignment.fee_head_id join academic_session on "
            + "fee_configuration.academic_session_id = academic_session.academic_session_id left join fee_assignment_permissions on "
            + "fee_assignment.fee_id = fee_assignment_permissions.fee_id and fee_assignment.fee_head_id = fee_assignment_permissions.fee_head_id "
            + "where fee_assignment.entity_id in %s and "
            + "fee_configuration.fee_id in %s and fee_assignment.institute_id = ? %s";
    private static final String DELETE_ASSIGNED_FEES_BY_ENTITY_ID_FEE_HEAD = "delete from fee_assignment where institute_id = ? and fee_id = ? "
            + "and fee_head_id in %s and entity_id in %s";
    private static final String DELETE_ASSIGNED_BY_ENTITY_ID_FEE_HEAD = "delete from fee_assignment where institute_id = ? "
            + " and fee_head_id in %s and entity_id in %s and fee_id in (select fee_id from fee_configuration where institute_id = ? and academic_session_id = ?)";
    private static final String DELETE_ASSIGNED_FEES_BY_FEE_HEAD = "delete from fee_assignment where institute_id = ? and fee_id in %s "
            + "and fee_head_id = ? and entity_id = ? ";
    // Fee assignment permissions
    private static final String ADD_FEE_ASSIGN_PERMISSION = "insert into fee_assignment_permissions(fee_id, fee_head_id, module_name, "
            + "institute_id, proportion_numerator, proportion_denominator) " + "values(?, ?, ?, ?, ?, ?)";
    private static final String GET_FEE_ASSIGN_PERMISSIONS = "select * from fee_assignment_permissions where institute_id = ?";
    private static final String DELETE_FEE_ASSIGN_PERMISSION_BY_ACADEMIC_SESSION = "delete from fee_assignment_permissions where fee_id in "
            + "(select fee_id from fee_configuration where academic_session_id = ?) and module_name = ? and institute_id = ?";
    private static final String GET_DEFAULT_FEE_ASSIGNMENT_STRUCTURE_IN_ACADEMIC_SESSION = "select default_fee_assignment_structure_meta_data.*, default_fee_assignment_structure.*, fee_configuration.*, "
            + " fee_head_configuration.*, academic_session.*, fee_assignment_permissions.module_name from default_fee_assignment_structure_meta_data "
            + " join default_fee_assignment_structure on default_fee_assignment_structure_meta_data.structure_id = default_fee_assignment_structure.structure_id "
            + " join fee_configuration on default_fee_assignment_structure.fee_id = fee_configuration.fee_id "
            + "	join fee_head_configuration on fee_head_configuration.fee_head_id = default_fee_assignment_structure.fee_head_id "
            + " join academic_session on default_fee_assignment_structure_meta_data.academic_session_id = academic_session.academic_session_id "
            + " left join fee_assignment_permissions on default_fee_assignment_structure.fee_id = fee_assignment_permissions.fee_id and default_fee_assignment_structure.fee_head_id = fee_assignment_permissions.fee_head_id "
            + " left join standards on standards.standard_id = default_fee_assignment_structure.entity_id "
            + " where default_fee_assignment_structure_meta_data.academic_session_id = ? and default_fee_assignment_structure_meta_data.institute_id = ? %s "
            + " order by standards.level, fee_configuration.due_date %s";
    private static final String GET_DEFAULT_FEE_ASSIGNMENT_STRUCTURE_IN_ACADEMIC_SESSION_BY_STRUCTURE_ID = "select default_fee_assignment_structure_meta_data.*, default_fee_assignment_structure.*, fee_configuration.*, "
            + "fee_head_configuration.*, academic_session.*, fee_assignment_permissions.module_name from default_fee_assignment_structure_meta_data "
            + " join default_fee_assignment_structure on default_fee_assignment_structure_meta_data.structure_id = default_fee_assignment_structure.structure_id "
            + " join fee_configuration on default_fee_assignment_structure.fee_id = fee_configuration.fee_id "
            + " join fee_head_configuration on fee_head_configuration.fee_head_id = default_fee_assignment_structure.fee_head_id "
            + " join academic_session on default_fee_assignment_structure_meta_data.academic_session_id = academic_session.academic_session_id "
            + " left join fee_assignment_permissions on default_fee_assignment_structure.fee_id = fee_assignment_permissions.fee_id and default_fee_assignment_structure.fee_head_id = fee_assignment_permissions.fee_head_id "
            + " where fee_configuration.academic_session_id = ? and default_fee_assignment_structure_meta_data.institute_id = ? and "
            + " default_fee_assignment_structure_meta_data.structure_id in (%s) %s";
    private static final String CREATE_DEFAULT_FEE_ASSIGNMENT_STRUCTURE_META_DATA = "insert into default_fee_assignment_structure_meta_data"
            + "(institute_id, academic_session_id, structure_name, structure_id, structure_type) values (?, ?, ?, ?, ?)";

    // Default fee assignment structure
//	private static final String GET_DEFAULT_FEE_ASSIGNMENT_STRUCTURE_IN_ACADEMIC_SESSION = "select default_fee_assignment_structure_meta_data.*, "
//			+ "default_fee_assignment_structure.*, fee_configuration.*, "
//			+ "fee_head_configuration.*, academic_session.*, fee_assignment_permissions.module_name from "
//			+ "default_fee_assignment_structure_meta_data join default_fee_assignment_structure on "
//			+ "default_fee_assignment_structure_meta_data.structure_id = default_fee_assignment_structure.structure_id join fee_configuration on "
//			+ "default_fee_assignment_structure.fee_id = fee_configuration.fee_id and fee_configuration.academic_session_id = ? "
//			+ "join fee_head_configuration on fee_head_configuration.fee_head_id = default_fee_assignment_structure.fee_head_id join academic_session on "
//			+ "fee_configuration.academic_session_id = academic_session.academic_session_id left join fee_assignment_permissions on "
//			+ "default_fee_assignment_structure.fee_id = fee_assignment_permissions.fee_id and default_fee_assignment_structure.fee_head_id = "
//			+ "fee_assignment_permissions.fee_head_id "
//			+ "left join standards on standards.standard_id = default_fee_assignment_structure.entity_id "
//			+ "where default_fee_assignment_structure_meta_data.institute_id = ? "
//			+ "order by standards.level, fee_configuration.due_date %s";
    private static final String CREATE_DEFAULT_FEE_ASSIGNMENT_STRUCTURE = "insert into default_fee_assignment_structure(structure_id, "
            + "entity_id, entity_name, fee_id, fee_head_id, amount) values (?, ?, ?, ?, ?, ?)";

    // Default fee assignment structure for given structure id
//	private static final String GET_DEFAULT_FEE_ASSIGNMENT_STRUCTURE_IN_ACADEMIC_SESSION_BY_STRUCTURE_ID = "select default_fee_assignment_structure_meta_data.*, "
//			+ "default_fee_assignment_structure.*, fee_configuration.*, "
//			+ "fee_head_configuration.*, academic_session.*, fee_assignment_permissions.module_name from default_fee_assignment_structure_meta_data "
//			+ "join default_fee_assignment_structure on default_fee_assignment_structure_meta_data.structure_id = "
//			+ "default_fee_assignment_structure.structure_id join fee_configuration on "
//			+ "default_fee_assignment_structure.fee_id = fee_configuration.fee_id and fee_configuration.academic_session_id = ? "
//			+ "join fee_head_configuration on fee_head_configuration.fee_head_id = default_fee_assignment_structure.fee_head_id join academic_session on "
//			+ "fee_configuration.academic_session_id = academic_session.academic_session_id left join fee_assignment_permissions on "
//			+ "default_fee_assignment_structure.fee_id = fee_assignment_permissions.fee_id and default_fee_assignment_structure.fee_head_id = "
//			+ "fee_assignment_permissions.fee_head_id where default_fee_assignment_structure_meta_data.institute_id = ? and "
//			+ "default_fee_assignment_structure_meta_data.structure_id = ? %s";
    private static final String GET_FEE_STRUCTURE_META_DATA = "select * from default_fee_assignment_structure_meta_data "
            + " inner join default_fee_assignment_structure on default_fee_assignment_structure.structure_id = default_fee_assignment_structure_meta_data.structure_id "
            + " and (entity_id = ?  or  entity_id = ?) and institute_id = ? and academic_session_id = ? ";
    private static final String DELETE_STUDENT_ASSIGNED_FEES_BY_ENTITY_ID = "delete from fee_assignment " +
            " where institute_id = ? and entity_id = ? and entity_name = 'STUDENT' " +
            " and fee_id in (select fee_id from fee_configuration where academic_session_id = ?)";
    private static final String FEE_HEAD_ID_NOT_IN_TRANSPORT = " and fee_head_id not in "
            + " (select fee_head_id from fee_head_configuration  "
            + " where fee_head = 'Transport' and fee_head_type = 'SYSTEM' and institute_id = ?) ";
    private static final String GET_FEE_ASSIGNMENT_OF_STANDARD = "select * from fee_assignment "
            + " join fee_configuration on fee_assignment.fee_id = fee_configuration.fee_id %s "
            + " join fee_head_configuration on fee_head_configuration.fee_head_id = fee_assignment.fee_head_id "
            + " join academic_session on fee_configuration.academic_session_id = academic_session.academic_session_id "
            + " left join fee_assignment_permissions on fee_assignment.fee_id = fee_assignment_permissions.fee_id and fee_assignment.fee_head_id = fee_assignment_permissions.fee_head_id "
            + " left join student_academic_session_details on (student_academic_session_details.student_id = fee_assignment.entity_id and fee_assignment.entity_name = 'STUDENT' and student_academic_session_details.standard_id in %s ) "
            + " left join students on student_academic_session_details.student_id = students.student_id "
            + " left join standards on (standards.standard_id = fee_assignment.entity_id and fee_assignment.entity_name = 'CLASS' and fee_assignment.entity_id in %s) "
            + " where fee_assignment.entity_name in ('INSTITUTE', 'CLASS', 'STUDENT') and "
            + " fee_assignment.institute_id = ? %s %s %s ";


//	private static final String DELETE_STUDENT_ASSIGNED_FEES_BY_ENTITY_ID = "delete from fee_assignment "
//			+ " where institute_id = ? and entity_id = ? and entity_name = 'STUDENT' ";
    private static final String GET_FEE_ASSIGNMENT_OF_STUDENT = "select * from fee_assignment "
            + " join fee_configuration on fee_assignment.fee_id = fee_configuration.fee_id %s "
            + " join fee_head_configuration on fee_head_configuration.fee_head_id = fee_assignment.fee_head_id "
            + " join academic_session on fee_configuration.academic_session_id = academic_session.academic_session_id "
            + " left join fee_assignment_permissions on fee_assignment.fee_id = fee_assignment_permissions.fee_id and fee_assignment.fee_head_id = fee_assignment_permissions.fee_head_id "
            + " left join student_academic_session_details on (student_academic_session_details.student_id = fee_assignment.entity_id and fee_assignment.entity_name = 'STUDENT' and student_academic_session_details.academic_session_id = ? and student_academic_session_details.student_id in %s) "
            + " left join students on student_academic_session_details.student_id = students.student_id "
            + " left join standards on (standards.standard_id = fee_assignment.entity_id and fee_assignment.entity_name = 'CLASS' and fee_assignment.entity_id = student_academic_session_details.standard_id) "
            + " where fee_assignment.entity_name in ('INSTITUTE', 'CLASS', 'STUDENT') and "
            + " fee_assignment.institute_id = ? and student_academic_session_details.session_status = 'ENROLLED' %s %s ";
    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;
    private final ICache<FeeConfigurationDao.FeeConfigCacheKey, FeeConfigurationResponse> feeConfigCache;

    public FeeConfigurationDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate,
                               CacheFactory cacheFactory) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
        this.feeConfigCache = cacheFactory.getInMemoryCache(new FeeConfigurationDao.FeeConfigCacheLoader());

    }

    // Fee Category

    public boolean addFeeCategoryList(int instituteId, List<FeeCategoryPayload> feeCategoryList) {
        try {
            List<Object[]> args = new ArrayList<>();
            for (FeeCategoryPayload feeCategory : feeCategoryList) {
                args.add(new Object[]{instituteId, feeCategory.getFeeCategoryName().trim(), StringUtils.isBlank(feeCategory.getDescription()) ? null : feeCategory.getDescription().trim()});
            }

            return jdbcTemplate.batchUpdate(ADD_FEE_CATEGORY, args).length == args.size();
        } catch (final Exception e) {
            logger.error("Error while adding fee categories for institute {}, feeCategoryList {}", instituteId, feeCategoryList, e);
        }
        return false;
    }

    public List<FeeCategory> getCategoryList(int instituteId) {
        try {
            final Object[] args = {instituteId};
            return jdbcTemplate.query(GET_FEE_CATEGORY, args, FEE_CATEGORY_ROW_MAPPER);
        } catch (final DataAccessException dataAccessException) {
            dataAccessException.printStackTrace();
        } catch (final Exception e) {
            logger.error("Exception while getting fee category list for institute {}",
                    instituteId, e);
        }
        return null;
    }

    public FeeHeadConfiguration getFeeHeadConfiguration(int instituteId, String feeHeadName) {
        try {
            final Object[] args = {instituteId, feeHeadName};
            return jdbcTemplate.queryForObject(GET_FEE_HEAD_CONFIGURATION, args, FEE_HEAD_CONFIGURATION_ROW_MAPPER);
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException while getting fee configuration for institute {} and feeHead {}",
                    instituteId, feeHeadName, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while getting fee configuration for institute {} and feeHead {}",
                    instituteId, feeHeadName, e);
        }
        return null;
    }

    // Fee Configurations

    public List<AuthorizedFeeAssignment> getAuthorizedFeeAssignment(int instituteId, int academicSessionId) {
        try {
            final Object[] args = {instituteId, academicSessionId};
            return AuthorizedFeeAssignmentRowMapper.getAuthorizedFeeAssignment(
                    jdbcTemplate.query(GET_AUTHORIZED_FEE_ASSIGNMENTS, args, AUTHORIZED_FEE_ASSIGNMENT_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Exception while getting assigned fees list for institute {}, academicSession {}",
                    instituteId, academicSessionId, e);
        }

        return null;
    }

    public UUID addFeeConfiguration(FeeConfigurationBasicInfo feeConfiguration) {
        try {
            final UUID feeConfigurationId = transactionTemplate.execute(new TransactionCallback<UUID>() {

                @Override
                public UUID doInTransaction(TransactionStatus status) {
                    final List<UUID> feeConfigurationUUIDs = addFeeConfigurations(Arrays.asList(feeConfiguration));
                    if (CollectionUtils.isEmpty(feeConfigurationUUIDs)) {
                        throw new RuntimeException("Transaction could not update all entities in desired way");
                    }
                    return feeConfigurationUUIDs.get(0);
                }
            });
            return feeConfigurationId;
        } catch (final Exception e) {
            logger.error("Unable to add fee configurations", e);
        }
        return null;
    }

    public List<UUID> addFeeConfiguration(List<FeeConfigurationBasicInfo> feeConfigurationList) {
        try {
            final List<UUID> feeConfigurationIds = transactionTemplate.execute(new TransactionCallback<List<UUID>>() {

                @Override
                public List<UUID> doInTransaction(TransactionStatus status) {
                    final List<UUID> feeConfigurationUUIDs = addFeeConfigurations(feeConfigurationList);
                    if (CollectionUtils.isEmpty(feeConfigurationUUIDs)) {
                        throw new RuntimeException("Transaction could not update all entities in desired way");
                    }
                    return feeConfigurationUUIDs;
                }
            });
            return feeConfigurationIds;
        } catch (final Exception e) {
            logger.error("Unable to add fee configurations", e);
        }
        return null;
    }

    private List<UUID> addFeeConfigurations(List<FeeConfigurationBasicInfo> feeConfigurationList) {
        final List<Object[]> batchInsertArgs = new ArrayList<>();
        final List<UUID> feeIds = new ArrayList<>();
        for (final FeeConfigurationBasicInfo feeConfigurationBasicInfo : feeConfigurationList) {
            final List<Object> args = new ArrayList<>();
            final UUID feeConfigurationId = UUID.randomUUID();
            feeIds.add(feeConfigurationId);
            args.add(feeConfigurationBasicInfo.getInstituteId());
            args.add(feeConfigurationId.toString());
            args.add(feeConfigurationBasicInfo.getFeeName());
            args.add(feeConfigurationBasicInfo.getFeeType().name());
            args.add((feeConfigurationBasicInfo.getDueDate() != null) && (feeConfigurationBasicInfo.getDueDate() > 0)
                    ? new Timestamp(feeConfigurationBasicInfo.getDueDate() * 1000l)
                    : null);
            args.add(feeConfigurationBasicInfo.getAcademicSessionId());
            args.add(feeConfigurationBasicInfo.getStartMonthYear() != null
                    ? feeConfigurationBasicInfo.getStartMonthYear().getMonth().getValue()
                    : null);
            args.add(feeConfigurationBasicInfo.getStartMonthYear() != null
                    ? feeConfigurationBasicInfo.getStartMonthYear().getYear()
                    : null);
            args.add(feeConfigurationBasicInfo.getEndMonthYear() != null
                    ? feeConfigurationBasicInfo.getEndMonthYear().getMonth().getValue()
                    : null);
            args.add(feeConfigurationBasicInfo.getEndMonthYear() != null
                    ? feeConfigurationBasicInfo.getEndMonthYear().getYear()
                    : null);
            args.add(feeConfigurationBasicInfo.isAllowPendingEnrollment());
            args.add(feeConfigurationBasicInfo.isFineApplicable());
            args.add(feeConfigurationBasicInfo.isTransferToWallet());
            args.add(feeConfigurationBasicInfo.getDescription());

            batchInsertArgs.add(args.toArray());
        }

        try {
            final int[] rows = jdbcTemplate.batchUpdate(ADD_FEE_CONFIGURATION, batchInsertArgs);
            if (rows.length != feeConfigurationList.size()) {
                return null;
            }
            for (final int rowCount : rows) {
                if (rowCount != 1) {
                    return null;
                }
            }
            return feeIds;
        } catch (final Exception e) {
            logger.error("Unable to add fee configurations", e);
        }
        return null;
    }

    public FeeConfigurationResponse getFeeConfiguration(int instituteId, UUID feeId) {
        return feeConfigCache.get(new FeeConfigCacheKey(instituteId, feeId));
    }

    public FeeConfigurationResponse getFeeConfigurationUnCached(int instituteId, UUID feeId) {
        try {
            final Object[] args = {instituteId, feeId.toString()};
            return jdbcTemplate.queryForObject(GET_FEE_CONFIGURATION_DETAILS, args, FEE_CONFIGURATION_ROW_MAPPER);
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException while getting fee configuration for institute {} and feeId {}",
                    instituteId, feeId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while getting fee configuration for institute {} and feeId {}",
                    instituteId, feeId, e);
        }

        return null;
    }

    public List<FeeConfigurationResponse> getFeeConfigurationByAcademicYear(int instituteId, int academicSessionId) {
        try {
            final Object[] args = {instituteId, academicSessionId};
            return jdbcTemplate.query(GET_FEE_CONFIGURATION_BY_ACADEMIC_SESSION, args, FEE_CONFIGURATION_ROW_MAPPER);
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException while getting fee configuration for institute {} and academicSessionId {}",
                    instituteId, academicSessionId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while getting fee configuration for institute {} and academicSessionId {}",
                    instituteId, academicSessionId, e);
        }

        return null;
    }

    public Map<Integer, List<FeeConfigurationResponse>> getAllFeeConfiguration(int instituteId) {
        try {
            final Object[] args = {instituteId};
            final List<FeeConfigurationResponse> feeConfigurationResponses = jdbcTemplate
                    .query(GET_ALL_FEE_CONFIGURATION, args, FEE_CONFIGURATION_ROW_MAPPER);
            if (CollectionUtils.isEmpty(feeConfigurationResponses)) {
                return new HashMap<>();
            }
            final Map<Integer, List<FeeConfigurationResponse>> acacdemicSessionWiseFees = new HashMap<>();
            for (final FeeConfigurationResponse feeConfigurationResponse : feeConfigurationResponses) {
                final int academicSessionId = feeConfigurationResponse.getAcademicSession().getAcademicSessionId();
                if (!acacdemicSessionWiseFees.containsKey(academicSessionId)) {
                    acacdemicSessionWiseFees.put(academicSessionId, new ArrayList<>());
                }
                acacdemicSessionWiseFees.get(academicSessionId).add(feeConfigurationResponse);
            }
            return acacdemicSessionWiseFees;

        } catch (final Exception e) {
            logger.error("Exception while getting fee configurations for institute {}", instituteId, e);
        }

        return null;
    }

    public List<FeeConfigurationResponse> getFeeConfigurations(int instituteId, Set<UUID> feeIds) {
        try {
            final StringBuilder inQuery = new StringBuilder();
            final List<Object> args = new ArrayList<>();
            args.add(instituteId);
            inQuery.append("(");
            boolean first = true;
            for (final UUID feeId : feeIds) {
                args.add(feeId.toString());
                if (first) {
                    inQuery.append("?");
                    first = false;
                    continue;
                }
                inQuery.append(", ?");
            }
            inQuery.append(")");
            return jdbcTemplate.query(String.format(GET_FEE_CONFIGURATIONS_DETAILS, inQuery.toString()), args.toArray(),
                    FEE_CONFIGURATION_ROW_MAPPER);
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException while getting fee configurations for institute {}", instituteId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while getting fee configurations for institute {}", instituteId, e);
        }

        return null;
    }

    public UUID updateFeeConfiguration(FeeConfigurationUpdatePayload feeConfigurationUpdatePayload) {
        try {
            final Object[] args = {feeConfigurationUpdatePayload.getFeeName(),
                    (feeConfigurationUpdatePayload.getDueDate() != null)
                            && (feeConfigurationUpdatePayload.getDueDate() > 0)
                            ? new Timestamp(feeConfigurationUpdatePayload.getDueDate() * 1000l)
                            : null,
                    feeConfigurationUpdatePayload.getDescription(),
                    feeConfigurationUpdatePayload.isAllowPendingEnrollment(),
                    feeConfigurationUpdatePayload.isFineApplicable(),
                    feeConfigurationUpdatePayload.isTransferToWallet(),
                    feeConfigurationUpdatePayload.getInstituteId(),
                    feeConfigurationUpdatePayload.getFeeId().toString()};
            final int row = jdbcTemplate.update(UPDATE_FEE_CONFIGURATION_BY_FEE_ID, args);
            if (row == 1) {
                return feeConfigurationUpdatePayload.getFeeId();
            }
        } catch (final DataAccessException dataAccessException) {
            logger.error("Exception while updating fee configurations", dataAccessException);
            ExceptionHandling.HandleException(dataAccessException, FEES, "fees name");
        } catch (final Exception e) {
            logger.error("Exception while updating fee configurations", e);
        } finally {
            invalidateInstituteHouseCache(feeConfigurationUpdatePayload.getInstituteId(), feeConfigurationUpdatePayload.getFeeId());
        }
        return null;
    }

    public UUID updateSpecialFeeConfiguration(FeeConfigurationUpdatePayload feeConfigurationUpdatePayload,
                                              MonthYear startMonthYear, MonthYear endMonthYear) {
        try {
            final Object[] args = {feeConfigurationUpdatePayload.getFeeName(),
                    (feeConfigurationUpdatePayload.getDueDate() != null)
                            && (feeConfigurationUpdatePayload.getDueDate() > 0)
                            ? new Timestamp(feeConfigurationUpdatePayload.getDueDate() * 1000l)
                            : null,

                    startMonthYear != null ? startMonthYear.getMonth().getValue() : null,
                    startMonthYear != null ? startMonthYear.getYear() : null,
                    endMonthYear != null ? endMonthYear.getMonth().getValue() : null,
                    endMonthYear != null ? endMonthYear.getYear() : null,
                    feeConfigurationUpdatePayload.getDescription(),
                    feeConfigurationUpdatePayload.isAllowPendingEnrollment(),
                    feeConfigurationUpdatePayload.isFineApplicable(),
                    feeConfigurationUpdatePayload.isTransferToWallet(),
                    feeConfigurationUpdatePayload.getInstituteId(),
                    feeConfigurationUpdatePayload.getFeeId().toString()};
            final int row = jdbcTemplate.update(UPDATE_SPECIAL_FEE_CONFIGURATION_BY_FEE_ID, args);
            if (row == 1) {
                return feeConfigurationUpdatePayload.getFeeId();
            }
        } catch (final DataAccessException dataAccessException) {
            logger.error("Exception while updating fee configurations", dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while updating fee configurations", e);
        } finally {
            invalidateInstituteHouseCache(feeConfigurationUpdatePayload.getInstituteId(), feeConfigurationUpdatePayload.getFeeId());
        }
        return null;
    }

    public boolean deleteRegularFeeConfigurationByAcademicSession(int instituteId, int academicSessionId) {
        try {
            final Boolean deletedRegularFeeConfiguration = transactionTemplate
                    .execute(new TransactionCallback<Boolean>() {

                        @Override
                        public Boolean doInTransaction(TransactionStatus status) {
                            final boolean deletedFeeConfigAndItsMapping = deleteRegularFeeConfigWithMappings(
                                    instituteId, academicSessionId);
                            if (!deletedFeeConfigAndItsMapping) {
                                throw new RuntimeException("Transaction could not update all entities in desired way");
                            }
                            return deletedFeeConfigAndItsMapping;
                        }
                    });
            return deletedRegularFeeConfiguration;
        } catch (final Exception e) {
            logger.error("Exception while deleting regulr fee configurations by academic session for institute {} and academic session {}",
                    instituteId, academicSessionId, e);
        }
        return false;
    }

    private boolean deleteRegularFeeConfigWithMappings(int instituteId, int academicSessionId) {

        final Object[] args = {instituteId, academicSessionId, FeeType.REGULAR.name()};
        try {
            return jdbcTemplate.update(DELETE_REGULAR_FEE_CONFIGURATION_BY_ACADEMIC_SESSION, args) > 0;
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException while deleting fee configurations for institute {} and academic session {}",
                    instituteId, academicSessionId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while deleting regular fee configurations with mapping for institute {} and academic session {}",
                    instituteId, academicSessionId, e);
        }
        return false;
    }

    public boolean deleteFeeConfigurationByFeeId(int instituteId, UUID feeId, FeeType feeType) {
        try {
            final Boolean deletedSpecialFeeConfiguration = transactionTemplate
                    .execute(new TransactionCallback<Boolean>() {

                        @Override
                        public Boolean doInTransaction(TransactionStatus status) {
                            final boolean deletedFeeConfigAndItsMapping = deleteFeeConfigWithMappings(instituteId,
                                    feeId, feeType);
                            if (!deletedFeeConfigAndItsMapping) {
                                throw new RuntimeException("Transaction could not update all entities in desired way");
                            }
                            return deletedFeeConfigAndItsMapping;
                        }
                    });
            return deletedSpecialFeeConfiguration;
        } catch (final Exception e) {
            logger.error("Exception while deleting fee configurations by fee Id for institute {}, feeId {}",
                    instituteId, feeId, e);
        } finally {
            invalidateInstituteHouseCache(instituteId, feeId);
        }
        return false;
    }

    protected boolean deleteFeeConfigWithMappings(int instituteId, UUID feeId, FeeType feeType) {
        final Object[] args = {instituteId, feeId.toString(), feeType.name()};
        try {
            /*
             * Regular fees deletion should not be allowed at Fee Id level
             */
            if (FeeType.REGULAR == feeType) {
                return false;
            }
            return jdbcTemplate.update(DELETE_FEE_CONFIGURATION_BY_FEE_ID, args) > 0;

        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException while deleting fee configurations with mapping for institute {} and feeId {}",
                    instituteId, feeId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while deleting fee configurations with mapping for institute {} and fee Id {}",
                    instituteId, feeId, e);
        } finally {
            invalidateInstituteHouseCache(instituteId, feeId);

        }
        return false;
    }

    // Fee Head Configuration

    /**
     * All fee heads specified in list should of same type
     *
     * @param feeHeadConfigurationList
     * @param systemFeeHeads
     * @return
     */
    public boolean addFeeHeadConfiguration(List<FeeHeadConfiguration> feeHeadConfigurationList,
                                           boolean systemFeeHeads) {
        final boolean success = true;
        final List<Object[]> argsList = new ArrayList<>();
        for (final FeeHeadConfiguration feeHeadConfiguration : feeHeadConfigurationList) {
            final List<Object> args = new ArrayList<>();
            args.add(feeHeadConfiguration.getInstituteId());
            args.add(feeHeadConfiguration.getFeeCategoryId());
            args.add(feeHeadConfiguration.getFeeHead());
            args.add(feeHeadConfiguration.isRefundable());
            args.add(feeHeadConfiguration.getDescription());
            if (systemFeeHeads) {
                args.add(FeeHeadType.SYSTEM.name());
            } else {
                args.add(FeeHeadType.CUSTOM.name());
            }

            args.add(feeHeadConfiguration.getFeeHeadTag() == null ? null : feeHeadConfiguration.getFeeHeadTag().name());
            argsList.add(args.toArray());
        }

        final int[] rows = jdbcTemplate.batchUpdate(ADD_FEE_HEAD_CONFIGURATION, argsList);
        if (rows.length != feeHeadConfigurationList.size()) {
            throw new RuntimeException(
                    "Unable to add fee head configuration. check if the fead head configuration list is empty");
        }
        return success;
    }

    public int updateFeeHeadConfiguration(FeeHeadConfiguration feeHeadConfiguration, boolean overrideSystemType) {
        final boolean success = true;
        final String query = overrideSystemType
                ? UPDATE_SYSTEM_FEE_HEAD_CONFIGURATION_DETAILS_BY_INSTITUTE_ID_FEE_HEAD_ID
                : UPDATE_FEE_HEAD_CONFIGURATION_DETAILS_BY_INSTITUTE_ID_FEE_HEAD_ID;
        try {
            jdbcTemplate.update(query, feeHeadConfiguration.getFeeHead(), feeHeadConfiguration.getFeeCategoryId(),
                    feeHeadConfiguration.isRefundable(), feeHeadConfiguration.getDescription(),
                    feeHeadConfiguration.getInstituteId(), feeHeadConfiguration.getFeeHeadId());
        } catch (final DataAccessException dataAccessException) {
            ExceptionHandling.HandleException(dataAccessException, FEE_HEAD, "fee head name");
            logger.error("DataAccessException while updating fee head", dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while updating fee head", e);
        }

        return success ? feeHeadConfiguration.getFeeHeadId() : 0;
    }

    public boolean deleteFeeHeadConfiguration(int instituteId, int feeHeadId, boolean overrideSystemType) {
        final String query = overrideSystemType
                ? DELETE_SYSTEM_FEE_HEAD_CONFIGURATION_DETAILS_BY_INSTITUTE_ID_FEE_HEAD_ID
                : DELETE_FEE_HEAD_CONFIGURATION_DETAILS_BY_INSTITUTE_ID_FEE_HEAD_ID;
        try {
            return jdbcTemplate.update(query, instituteId, feeHeadId) == 1;
        } catch (final DataAccessException dataAccessException) {
            ExceptionHandling.HandleException(dataAccessException, FEE_HEAD, "fee head name");
            logger.error("DataAccessException while deleting fee head", dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while deleting fee head", e);
        }
        return false;
    }

    public List<FeeHeadConfigurationResponse> getFeeHeadConfiguration(int instituteId) {
        try {
            final Object[] args = {instituteId};
            return jdbcTemplate.query(GET_FEE_HEAD_CONFIGURATION_DETAILS_BY_INSTITUTE_ID, args,
                    FEE_HEAD_CONFIGURATION_RESPONSE_ROW_MAPPER);
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException while getting fee head", dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while getting fee head", e);
        }

        return null;
    }

    public Integer getTransportFeeHead(int instituteId) {
        try {
            final Object[] args = {instituteId};
            return jdbcTemplate.queryForInt(GET_TRANSPORT_FEE_HEAD_BY_INSTITUTE_ID, args);
        } catch (final Exception e) {
            logger.error("Exception while getting transport fee head", e);
        }
        return null;
    }

    // Fee Assignment
    public boolean assignFeesWithTransaction(FeeAssignmentDetails feeAssignmentDetails, Module module,
                                             FeeAssignmentState feeAssignmentState) {
        try {
            final Boolean feesAssigned = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    return assignFees(feeAssignmentDetails, module, feeAssignmentState);
                }
            });
            return feesAssigned;
        } catch (final Exception e) {
            logger.error("Exception while assigning fee with transaction", e);
        }
        return false;
    }

    /**
     * This method must be used inside a transaction
     *
     * @param feeAssignmentDetails
     * @param module
     * @return
     */
    public boolean assignFeesWithoutTransaction(FeeAssignmentDetails feeAssignmentDetails, Module module,
                                                FeeAssignmentState feeAssignmentState) {
        return assignFees(feeAssignmentDetails, module, feeAssignmentState);
    }

    private boolean assignFees(FeeAssignmentDetails feeAssignmentDetails, Module module,
                               FeeAssignmentState feeAssignmentState) {

        final List<FeeAssignmentPermission> feeAssignmentPermissions = getFeeAssignmentPermission(
                feeAssignmentDetails.getInstituteId());
        final Pair<Map<UUID, Set<Integer>>, Map<UUID, Set<Integer>>> allowedAndBlockedFeeAssignments = getAllowedAndBlockedFeeAssignments(
                module, feeAssignmentPermissions, feeAssignmentDetails.getFeeId());
        final Map<UUID, Set<Integer>> allowedFeeAssignmentPermission = allowedAndBlockedFeeAssignments.getFirst();
        final Map<UUID, Set<Integer>> blockedFeeAssignmentPermission = allowedAndBlockedFeeAssignments.getSecond();

        if (!authorizeFeeAssignment(module, feeAssignmentPermissions, feeAssignmentDetails,
                allowedFeeAssignmentPermission, blockedFeeAssignmentPermission)) {
            logger.error("Unauthorized fee assignment feeId =" + feeAssignmentDetails.getFeeId().toString());
            throw new RuntimeException("Unauthorized fee assignment.");
        }

        switch (feeAssignmentState) {
            case NEW:
                if (!addFeeAssginment(feeAssignmentDetails)) {
                    logger.error(
                            "Unable to add new fee assignment feeId =" + feeAssignmentDetails.getFeeId().toString());
                    throw new RuntimeException("Failed to assign fees");
                }
                break;
            case PARTIAL_UPDATE:
                if (!updateFeeAssginment(feeAssignmentDetails)) {
                    logger.error(
                            "Unable to update fee assignment feeId =" + feeAssignmentDetails.getFeeId().toString());
                    throw new RuntimeException("Failed to updated fee assignment");
                }
                break;

            case COMPLETE_UPDATE:
                final List<FeeEntityData> feeEntityDataList = new ArrayList<>();
                for (final EntityFees entityFees : feeAssignmentDetails.getEntityFees()) {
                    feeEntityDataList.add(new FeeEntityData(entityFees.getFeeEntity(), entityFees.getEntityId()));
                }
                if (!deleteAuthorizedFeeAssignment(feeAssignmentDetails.getInstituteId(), feeAssignmentDetails.getFeeId(),
                        feeEntityDataList, allowedFeeAssignmentPermission, blockedFeeAssignmentPermission, module)) {
                    logger.error("Unable to delete existing old fee assignment feeId ="
                            + feeAssignmentDetails.getFeeId().toString());
                    throw new RuntimeException("Unable to delete existing old fee assignment.");
                }
                if (!addFeeAssginment(feeAssignmentDetails)) {
                    logger.error(
                            "Unable to add new fee assignment feeId =" + feeAssignmentDetails.getFeeId().toString());
                    throw new RuntimeException("Failed to assign fees");
                }
                break;
            default:
                break;
        }
        return true;

    }

    private Pair<Map<UUID, Set<Integer>>, Map<UUID, Set<Integer>>> getAllowedAndBlockedFeeAssignments(Module module,
                                                                                                      final List<FeeAssignmentPermission> feeAssignmentPermissions, UUID feeId) {

        final Map<UUID, Set<Integer>> allowedFeeAssignmentPermission = new HashMap<>();
        final Map<UUID, Set<Integer>> blockedFeeAssignmentPermission = new HashMap<>();

        for (final FeeAssignmentPermission feeAssignmentPermission : feeAssignmentPermissions) {
            if (!feeAssignmentPermission.getFeeId().equals(feeId)) {
                continue;
            }

            for (final FeeHeadAssginmentPermission feeHeadAssginmentPermission : feeAssignmentPermission
                    .getFeeHeadAssginmentPermissions()) {
                final int feeHeadId = feeHeadAssginmentPermission.getFeeHeadId();
                if (feeHeadAssginmentPermission.getModules().contains(module)) {
                    if (!allowedFeeAssignmentPermission.containsKey(feeAssignmentPermission.getFeeId())) {
                        allowedFeeAssignmentPermission.put(feeId, new HashSet<>());
                    }
                    allowedFeeAssignmentPermission.get(feeId).add(feeHeadId);
                }

                if (!feeHeadAssginmentPermission.getModules().contains(module)) {
                    if (!blockedFeeAssignmentPermission.containsKey(feeAssignmentPermission.getFeeId())) {
                        blockedFeeAssignmentPermission.put(feeId, new HashSet<>());
                    }
                    blockedFeeAssignmentPermission.get(feeId).add(feeHeadId);
                }
            }
        }
        return new Pair<>(allowedFeeAssignmentPermission, blockedFeeAssignmentPermission);
    }

    private boolean authorizeFeeAssignment(Module module, final List<FeeAssignmentPermission> feeAssignmentPermissions,
                                           FeeAssignmentDetails feeAssignmentDetails, final Map<UUID, Set<Integer>> allowedFeeAssignmentPermission,
                                           final Map<UUID, Set<Integer>> blockedFeeAssignmentPermission) {

        final UUID feeId = feeAssignmentDetails.getFeeId();
        if (module == Module.FEES) {
            if (!blockedFeeAssignmentPermission.containsKey(feeId)) {
                return true;
            }
            for (final EntityFees entityFees : feeAssignmentDetails.getEntityFees()) {
                for (final FeeHeadAmount feeHeadAmount : entityFees.getFeeHeadAmount()) {
                    final int feeHeadId = feeHeadAmount.getFeeHeadId();
                    if (blockedFeeAssignmentPermission.get(feeId).contains(feeHeadId)) {
                        logger.info("Fee id " + feeId + " , fee head " + feeHeadId + " is blocked for module "
                                + module.name());
                        return false;
                    }
                }
            }
            return true;
        }
        if (!allowedFeeAssignmentPermission.containsKey(feeId)) {
            logger.info("Fee id " + feeId + " is blocked for module " + module.name());
            return false;
        }

        for (final EntityFees entityFees : feeAssignmentDetails.getEntityFees()) {
            if (CollectionUtils.isEmpty(entityFees.getFeeHeadAmount())) {
                continue;
            }
            for (final FeeHeadAmount feeHeadAmount : entityFees.getFeeHeadAmount()) {
                final int feeHeadId = feeHeadAmount.getFeeHeadId();
                if (!allowedFeeAssignmentPermission.get(feeId).contains(feeHeadId)) {
                    logger.error(
                            "Fee id " + feeId + " , fee head " + feeHeadId + " is blocked for module " + module.name());
                    return false;
                }
            }
        }
        return true;

    }

    public boolean deleteAuthorizedFeeAssignment(int instituteId, UUID feeId, List<FeeEntityData> feeEntityDataList,
                                                 Module module) {
        final List<FeeAssignmentPermission> feeAssignmentPermissions = getFeeAssignmentPermission(instituteId);
        final Pair<Map<UUID, Set<Integer>>, Map<UUID, Set<Integer>>> allowedAndBlockedFeeAssignments = getAllowedAndBlockedFeeAssignments(
                module, feeAssignmentPermissions, feeId);
        final Map<UUID, Set<Integer>> allowedFeeAssignmentPermission = allowedAndBlockedFeeAssignments.getFirst();
        final Map<UUID, Set<Integer>> blockedFeeAssignmentPermission = allowedAndBlockedFeeAssignments.getSecond();

        return deleteAuthorizedFeeAssignment(instituteId, feeId, feeEntityDataList,
                allowedFeeAssignmentPermission, blockedFeeAssignmentPermission, module);
    }

    private boolean deleteAuthorizedFeeAssignment(int instituteId, UUID feeId,
                                                  final List<FeeEntityData> feeEntityDataList, final Map<UUID, Set<Integer>> allowedFeeAssignmentPermission,
                                                  final Map<UUID, Set<Integer>> blockedFeeAssignmentPermission, Module module) {

        final Map<String, EntityFeeAssignment> entityFeeAssignment = getFeeAssignment(instituteId, feeEntityDataList,
                Arrays.asList(feeId));

        final Set<Integer> feeHeadsToDelete = new HashSet<>();
        for (final Entry<String, EntityFeeAssignment> assignmentEntry : entityFeeAssignment.entrySet()) {
            for (final FeeIdFeeHeadDetails feeIdFeeHeadDetails : assignmentEntry.getValue()
                    .getFeeIdFeeHeadDetailsList()) {

                if (!feeIdFeeHeadDetails.getFeeConfigurationResponse().getFeeConfigurationBasicInfo().getFeeId()
                        .equals(feeId)) {
                    continue;
                }
                for (final FeeHeadAmountDetails feeHeadAmountDetails : feeIdFeeHeadDetails
                        .getFeeHeadAmountDetailsList()) {
                    feeHeadsToDelete.add(feeHeadAmountDetails.getFeeHeadId());
                }
            }
        }

        if (CollectionUtils.isEmpty(feeHeadsToDelete)) {
            return true;
        }

        final Iterator<Integer> iterator = feeHeadsToDelete.iterator();

        while (iterator.hasNext()) {
            final int feeHeadId = iterator.next();
            if (((module == Module.FEES) && blockedFeeAssignmentPermission.containsKey(feeId)
                    && blockedFeeAssignmentPermission.get(feeId).contains(feeHeadId))) {
                iterator.remove();
                continue;
            }
            if (((module != Module.FEES) && (!allowedFeeAssignmentPermission.containsKey(feeId)
                    || !allowedFeeAssignmentPermission.get(feeId).contains(feeHeadId)))) {
                iterator.remove();
                continue;
            }

        }

        if (CollectionUtils.isEmpty(feeHeadsToDelete)) {
            return true;
        }

        return deleteAssignedFees(instituteId, feeId, feeEntityDataList, feeHeadsToDelete);
    }

    /**
     * This method adds the entries for new fee assignment rows
     *
     * @param feeAssignmentDetails
     * @return
     */
    private boolean addFeeAssginment(FeeAssignmentDetails feeAssignmentDetails) {

        final List<Object[]> argsList = new ArrayList<>();
        for (final EntityFees entityFees : feeAssignmentDetails.getEntityFees()) {
            if (CollectionUtils.isEmpty(entityFees.getFeeHeadAmount())) {
                continue;
            }
            for (final FeeHeadAmount feeHeadAmount : entityFees.getFeeHeadAmount()) {
                if (feeHeadAmount.getAmount() == null) {
                    continue;
                }
                final List<Object> args = new ArrayList<>();
                args.add(feeAssignmentDetails.getInstituteId());
                args.add(feeAssignmentDetails.getFeeId().toString());
                args.add(entityFees.getEntityId());
                args.add(entityFees.getFeeEntity().name());
                args.add(feeHeadAmount.getFeeHeadId());
                args.add(feeHeadAmount.getAmount());
                argsList.add(args.toArray());
            }
        }
        try {
            final int[] rows = jdbcTemplate.batchUpdate(ASSIGN_FEES, argsList);
            if (rows.length != argsList.size()) {
                throw new RuntimeException("Unable to add fee assignment. Check fead head amount list is empty");
            }
            return true;
        } catch (final Exception e) {
            logger.error("Exception while adding fee assignment", e);
        }
        return false;
    }

    private boolean updateFeeAssginment(FeeAssignmentDetails feeAssignmentDetails) {

        final List<Object[]> argsList = new ArrayList<>();
        for (final EntityFees entityFees : feeAssignmentDetails.getEntityFees()) {
            for (final FeeHeadAmount feeHeadAmount : entityFees.getFeeHeadAmount()) {
                final List<Object> args = new ArrayList<>();
                args.add(feeHeadAmount.getAmount());
                args.add(feeAssignmentDetails.getFeeId().toString());
                args.add(entityFees.getEntityId());
                args.add(feeHeadAmount.getFeeHeadId());
                args.add(feeAssignmentDetails.getInstituteId());

                argsList.add(args.toArray());
            }
        }
        try {
            final int[] rows = jdbcTemplate.batchUpdate(UPDATE_FEE_ASSIGNMENT, argsList);
            if (rows.length != argsList.size()) {
                throw new RuntimeException("Unable to update fee assignment. Check fead heads amount");
            }
            return true;
        } catch (final Exception e) {
            logger.error("Exception while updating fee assignment", e);
        }
        return false;
    }

    // public FeeAssignmentDetails
    // getStudentFeeConfigurationByInstituteIdFeeId(int instituteId, String
    // feeId) {
    // try {
    // Object[] args = { instituteId, feeId };
    // return FeeAssignmentRowMapper.getStudentFeeConfigurationForFeeId(
    // jdbcTemplate.query(GET_STUDENT_FEE_CONFIGURATION_DETAILS_BY_INSTITUTE_ID_FEE_ID,
    // args,
    // STUDENT_FEE_CONFIGURATION_ROW_MAPPER));
    // } catch (DataAccessException dataAccessException) {
    // System.out.println(dataAccessException);
    // dataAccessException.printStackTrace();
    // } catch (Exception e) {
    // System.out.println("Exception" + e.getMessage());
    // }
    //
    // return null;
    // }

    /**
     * This method deletes all the given fee heads for a particular feeId for given
     * entities.
     *
     * @param feeHeadIds
     * @return
     */
    public boolean deleteAssignedFees(int instituteId, UUID feeId, final List<FeeEntityData> feeEntityDataList,
                                      Set<Integer> feeHeadIds) {
        final List<Object> args = new ArrayList<>();
        args.add(instituteId);
        args.add(feeId.toString());

        final StringBuilder feeHeadInQueryParams = new StringBuilder();
        feeHeadInQueryParams.append("(");
        boolean first = true;
        for (final Integer feeHeadId : feeHeadIds) {
            args.add(feeHeadId);
            if (first) {
                feeHeadInQueryParams.append("?");
                first = false;
                continue;
            }
            feeHeadInQueryParams.append(", ?");
        }
        feeHeadInQueryParams.append(")");

        final StringBuilder inQueryParams = new StringBuilder();
        inQueryParams.append("(");
        first = true;
        for (final FeeEntityData feeEntityData : feeEntityDataList) {
            args.add(feeEntityData.getEntityId());
            if (first) {
                inQueryParams.append("?");
                first = false;
                continue;
            }
            inQueryParams.append(", ?");
        }
        inQueryParams.append(")");

        try {
            jdbcTemplate.update(String.format(DELETE_ASSIGNED_FEES_BY_ENTITY_ID_FEE_HEAD,
                    feeHeadInQueryParams.toString(), inQueryParams.toString()), args.toArray());

            return true;
        } catch (final Exception e) {
            logger.error("Exception while deleting fee assignment", e);
        }
        return false;
    }

    public boolean deleteAssignedFeesByFeeHead(int instituteId, int academicSessionId, final List<FeeEntityData> feeEntityDataList,
                                               Set<Integer> feeHeadIds) {
        final List<Object> args = new ArrayList<>();
        args.add(instituteId);

        final StringBuilder feeHeadInQueryParams = new StringBuilder();
        feeHeadInQueryParams.append("(");
        boolean first = true;
        for (final Integer feeHeadId : feeHeadIds) {
            args.add(feeHeadId);
            if (first) {
                feeHeadInQueryParams.append("?");
                first = false;
                continue;
            }
            feeHeadInQueryParams.append(", ?");
        }
        feeHeadInQueryParams.append(")");

        final StringBuilder inQueryParams = new StringBuilder();
        inQueryParams.append("(");
        first = true;
        for (final FeeEntityData feeEntityData : feeEntityDataList) {
            args.add(feeEntityData.getEntityId());
            if (first) {
                inQueryParams.append("?");
                first = false;
                continue;
            }
            inQueryParams.append(", ?");
        }
        inQueryParams.append(")");

        args.add(instituteId);
        args.add(academicSessionId);
        try {
            return jdbcTemplate.update(String.format(DELETE_ASSIGNED_BY_ENTITY_ID_FEE_HEAD,
                    feeHeadInQueryParams, inQueryParams), args.toArray()) >= 0;
        } catch (final Exception e) {
            logger.error("Exception while deleting fee assignment", e);
        }
        return false;
    }

    public boolean deleteAssignedFeesByFeeIds(int instituteId, List<UUID> feeIds, UUID entityId,
                                              Integer feeHeadId) {

        final List<Object> args = new ArrayList<>();
        args.add(instituteId);

        final StringBuilder feeIdInQueryParams = new StringBuilder();
        feeIdInQueryParams.append("(");
        boolean first = true;
        for (final UUID feeId : feeIds) {
            args.add(feeId.toString());
            if (first) {
                feeIdInQueryParams.append("?");
                first = false;
                continue;
            }
            feeIdInQueryParams.append(", ?");
        }
        feeIdInQueryParams.append(")");

        args.add(feeHeadId);
        args.add(entityId.toString());

        try {
            return jdbcTemplate.update(String.format(DELETE_ASSIGNED_FEES_BY_FEE_HEAD,
                    feeIdInQueryParams.toString()), args.toArray()) > 0;

        } catch (final Exception e) {
            logger.error("Exception while deleting fee assignment", e);
        }
        return false;
    }

    /**
     * Gets fee assigned for given academic session fees
     *
     * @param instituteId
     * @param feeEntityDataList
     * @param academicSessionId
     * @return
     */
    public Map<String, EntityFeeAssignment> getFeeAssignment(int instituteId, List<FeeEntityData> feeEntityDataList,
                                                             Integer academicSessionId) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(academicSessionId);
            final StringBuilder inQueryParams = new StringBuilder();
            inQueryParams.append("(");
            boolean first = true;
            for (final FeeEntityData feeEntityData : feeEntityDataList) {
                args.add(feeEntityData.getEntityId());
                if (first) {
                    inQueryParams.append("?");
                    first = false;
                    continue;
                }
                inQueryParams.append(", ?");
            }
            inQueryParams.append(")");
            args.add(instituteId);
            return FeeAssignmentRowMapper
                    .getFeeAssignments(jdbcTemplate.query(String.format(GET_FEE_ASSIGNMENT_IN_ACADEMIC_SESSION,
                            " and fee_configuration.academic_session_id = ? ", inQueryParams.toString(),
                            FOR_SHARE_CLAUSE), args.toArray(), FEE_ASSIGNMENT_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while getting entity assignment for institute {}, entities {}", instituteId,
                    feeEntityDataList, e);
        }
        return null;
    }


    public Map<UUID, Map<String, EntityFeeAssignment>> getFeeAssignmentOfStandard(int instituteId, Set<UUID> standardIds,
                                                                                  Integer academicSessionId, Set<Integer> feeHeadIds, Set<StudentStatus> statusSet) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(academicSessionId);

            final StringBuilder inQuery1 = new StringBuilder();
            inQuery1.append("(");
            String delimiter1 = "";
            for (final UUID standardId : standardIds) {
                args.add(standardId.toString());
                inQuery1.append(delimiter1).append("?");
                delimiter1 = ",";
            }
            inQuery1.append(")");

            final StringBuilder inQuery2 = new StringBuilder();
            inQuery2.append("(");
            String delimiter2 = "";
            for (final UUID standardId : standardIds) {
                args.add(standardId.toString());
                inQuery2.append(delimiter2).append("?");
                delimiter2 = ",";
            }
            inQuery2.append(")");
            args.add(instituteId);


            final StringBuilder inQueryHead = new StringBuilder();
            if (!CollectionUtils.isEmpty(feeHeadIds)) {
                inQueryHead.append(" and fee_head_configuration.fee_head_id in ");
                inQueryHead.append("(");
                String delimiterHead = "";
                for (final Integer feeHeadId : feeHeadIds) {
                    if (feeHeadId != null && feeHeadId > 0) {
                        args.add(feeHeadId);
                        inQueryHead.append(delimiterHead).append("?");
                        delimiterHead = ",";
                    }
                }
                inQueryHead.append(")");
            }

            final StringBuilder statusInQuery = new StringBuilder();
            if (!org.apache.commons.collections.CollectionUtils.isEmpty(statusSet)) {
                statusInQuery.append(" and student_academic_session_details.session_status in ( ");
                String delimiterStatus = "";
                for (final StudentStatus status : statusSet) {
                    args.add(status.name());
                    statusInQuery.append(delimiterStatus).append("?");
                    delimiterStatus = ",";
                }
                statusInQuery.append(")");
            }

            logger.info(String.format(GET_FEE_ASSIGNMENT_OF_STANDARD,
                    " and fee_configuration.academic_session_id = ? ", inQuery1, inQuery2,
                    inQueryHead, statusInQuery, FOR_SHARE_CLAUSE));
            for(Object arg : args) {
                logger.info(arg);
            }
            return FeeAssignmentRowMapper.getFeeAssignmentOfStandard(jdbcTemplate.query(String.format(GET_FEE_ASSIGNMENT_OF_STANDARD,
                    " and fee_configuration.academic_session_id = ? ", inQuery1, inQuery2,
                    inQueryHead, statusInQuery, FOR_SHARE_CLAUSE), args.toArray(), FEE_ASSIGNMENT_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while getting entity assignment for institute {}, standardId {}", instituteId,
                    standardIds, e);
        }
        return null;
    }

    public Map<UUID, Map<String, EntityFeeAssignment>> getFeeAssignmentByStudentIds(int instituteId, Set<UUID> studentIds,
                                                                                    Integer academicSessionId, Set<Integer> feeHeadIds) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(academicSessionId);
            args.add(academicSessionId);
            final StringBuilder inQuery1 = new StringBuilder();
            inQuery1.append("(");
            String delimiter1 = "";
            for (final UUID studentId : studentIds) {
                args.add(studentId.toString());
                inQuery1.append(delimiter1).append("?");
                delimiter1 = ",";
            }
            inQuery1.append(")");
            args.add(instituteId);

            final StringBuilder inQueryHead = new StringBuilder();
            if (!CollectionUtils.isEmpty(feeHeadIds)) {
                inQueryHead.append(" and fee_head_configuration.fee_head_id in ");
                inQueryHead.append("(");
                String delimiterHead = "";
                for (final Integer feeHeadId : feeHeadIds) {
                    if (feeHeadId != null && feeHeadId > 0) {
                        args.add(feeHeadId);
                        inQueryHead.append(delimiterHead).append("?");
                        delimiterHead = ",";
                    }
                }
                inQueryHead.append(")");
            }
            return FeeAssignmentRowMapper.getFeeAssignmentOfStandard(jdbcTemplate.query(String.format(GET_FEE_ASSIGNMENT_OF_STUDENT,
                    " and fee_configuration.academic_session_id = ? ", inQuery1.toString(),
                    inQueryHead.toString(), FOR_SHARE_CLAUSE), args.toArray(), FEE_ASSIGNMENT_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while getting entity assignment for institute {}, standardId {}", instituteId,
                    studentIds, e);
        }
        return null;
    }

    /**
     * Gets fee assigned for given all academic session fees
     *
     * @param instituteId
     * @param feeEntityDataList
     * @return
     */
    public Map<Integer, Map<String, EntityFeeAssignment>> getFeeAssignment(int instituteId,
                                                                           List<FeeEntityData> feeEntityDataList) {
        try {
            final List<Object> args = new ArrayList<>();
            final StringBuilder inQueryParams = new StringBuilder();
            inQueryParams.append("(");
            boolean first = true;
            for (final FeeEntityData feeEntityData : feeEntityDataList) {
                args.add(feeEntityData.getEntityId());
                if (first) {
                    inQueryParams.append("?");
                    first = false;
                    continue;
                }
                inQueryParams.append(", ?");
            }
            inQueryParams.append(")");
            args.add(instituteId);
            return FeeAssignmentRowMapper.getAllSessionFeeAssignments(
                    jdbcTemplate.query(String.format(GET_FEE_ASSIGNMENT_IN_ACADEMIC_SESSION, "",
                            inQueryParams.toString(), FOR_SHARE_CLAUSE), args.toArray(), FEE_ASSIGNMENT_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while getting entity assignment for institute {}, entities {}", instituteId,
                    feeEntityDataList, e);
        }
        return null;
    }

    /**
     * Gets fee assigned for given list of fees
     *
     * @param instituteId
     * @param feeEntityDataList
     * @param feeIds
     * @return
     */
    public Map<String, EntityFeeAssignment> getFeeAssignment(int instituteId, List<FeeEntityData> feeEntityDataList,
                                                             List<UUID> feeIds) {
        try {
            final List<Object> args = new ArrayList<>();
            final StringBuilder feeEntityInQueryParams = new StringBuilder();
            feeEntityInQueryParams.append("(");
            boolean first = true;
            for (final FeeEntityData feeEntityData : feeEntityDataList) {
                args.add(feeEntityData.getEntityId());
                if (first) {
                    feeEntityInQueryParams.append("?");
                    first = false;
                    continue;
                }
                feeEntityInQueryParams.append(", ?");
            }
            feeEntityInQueryParams.append(")");

            final StringBuilder feeIdInQueryParams = new StringBuilder();
            feeIdInQueryParams.append("( ");
            first = true;
            for (final UUID feeId : feeIds) {
                args.add(feeId.toString());
                if (first) {
                    feeIdInQueryParams.append("?");
                    first = false;
                    continue;
                }
                feeIdInQueryParams.append(", ?");
            }
            feeIdInQueryParams.append(" )");

            args.add(instituteId);

            return FeeAssignmentRowMapper.getFeeAssignments(jdbcTemplate.query(
                    String.format(GET_FEE_ASSIGNMENT_BY_FEE_IDS, feeEntityInQueryParams.toString(),
                            feeIdInQueryParams.toString(), FOR_SHARE_CLAUSE),
                    args.toArray(), FEE_ASSIGNMENT_ROW_MAPPER));
        } catch (final DataAccessException dataAccessException) {
            logger.error("Exception while getting fee assignment for instiute {}", instituteId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while getting fee assignment for instiute {}", instituteId, e);
        }
        return null;
    }

    /**
     * here entity ID is studentId
     *
     * @param instituteId
     * @param entityId
     * @return
     */
    public boolean deleteStudentAssignedFeesByEntityId(int instituteId, int academicSessionId, UUID entityId) {
        try {
            final Object[] args = {instituteId, entityId.toString(), academicSessionId, instituteId};
            return jdbcTemplate.update(DELETE_STUDENT_ASSIGNED_FEES_BY_ENTITY_ID + FEE_HEAD_ID_NOT_IN_TRANSPORT, args) >= 0;
        } catch (final DataAccessException dataAccessException) {
            logger.error("Exception while deleting assign fee for instiute {}", instituteId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while deleting assign fee for instiute {}", instituteId, e);
        }
        return false;
    }


    public boolean addFeeAssignmentPermission(List<FeeAssignmentPermission> feeAssignmentPermissions, int instituteId) {
        try {
            final List<Object[]> argsList = new ArrayList<>();
            for (final FeeAssignmentPermission feeAssignmentPermission : feeAssignmentPermissions) {
                final UUID feeId = feeAssignmentPermission.getFeeId();
                for (final FeeHeadAssginmentPermission feeHeadAssginmentPermission : feeAssignmentPermission
                        .getFeeHeadAssginmentPermissions()) {
                    for (final ModuleFeeProportion moduleFeeProportion : feeHeadAssginmentPermission
                            .getModuleFeeProportions()) {
                        final List<Object> args = new ArrayList<>();
                        args.add(feeId.toString());
                        args.add(feeHeadAssginmentPermission.getFeeHeadId());
                        args.add(moduleFeeProportion.getModule().name());
                        args.add(instituteId);
                        args.add(moduleFeeProportion.getFeeProportionNumerator());
                        args.add(moduleFeeProportion.getFeeProportionDenominator());
                        argsList.add(args.toArray());
                    }

                }
            }
            if (CollectionUtils.isEmpty(argsList)) {
                return false;
            }

            final int[] rows = jdbcTemplate.batchUpdate(ADD_FEE_ASSIGN_PERMISSION, argsList);
            if (rows.length != argsList.size()) {
                throw new RuntimeException("Unable to add fee assignment permission.");
            }
            return true;
        } catch (final Exception e) {
            logger.error("Exception while adding fee assignement permission for instiute {}", instituteId, e);
        }
        return false;
    }

    public List<FeeAssignmentPermission> getFeeAssignmentPermission(int instituteId) {
        try {
            return FeeAssignmentPermissionRowMapper.getFeeAssignmentPermission(jdbcTemplate.query(
                    GET_FEE_ASSIGN_PERMISSIONS, new Object[]{instituteId}, FEE_ASSIGNMENT_PERMISSION_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Exception while getting fee assignement permission for instiute {}", instituteId, e);
        }
        return null;
    }

    public boolean deleteFeeAssignmentPermission(int instituteId, int academicSessionId, Module module) {
        try {
            jdbcTemplate.update(DELETE_FEE_ASSIGN_PERMISSION_BY_ACADEMIC_SESSION, academicSessionId, module.name(),
                    instituteId);
            return true;
        } catch (final Exception e) {
            logger.error("Exception while deleting fee assignement permission for instiute {}", instituteId, e);
        }
        return false;
    }

    public List<DefaultEntityFeeAssignmentStructure> getDefaultFeeAssignmentStructure(int instituteId,
                                                                                      int academicSessionId, List<UUID> feeStructureIdsList) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(academicSessionId);
            args.add(instituteId);
            final StringBuilder structureIdsQuery = new StringBuilder();
            if(!CollectionUtils.isEmpty(feeStructureIdsList)){
                structureIdsQuery.append(" and default_fee_assignment_structure_meta_data.structure_id in (");
                String delimiterStatus = "";
                for (final UUID feeStructureIds : feeStructureIdsList) {
                    args.add(feeStructureIds.toString());
                    structureIdsQuery.append(delimiterStatus).append("?");
                    delimiterStatus = ", ";
                }
                structureIdsQuery.append(")");
            }
            return DefaultFeeAssignmentStructureRowMapper.getDefaultEntityFeeAssignmentStructures(jdbcTemplate.query(
                    String.format(GET_DEFAULT_FEE_ASSIGNMENT_STRUCTURE_IN_ACADEMIC_SESSION, structureIdsQuery.toString(), FOR_SHARE_CLAUSE),
                    args.toArray(), DEFAULT_FEE_ASSIGNMENT_STRUCTURE_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while reading fee assignment for instituteId {}, academicSessionId {}", instituteId,
                    academicSessionId, e);
        }
        return null;
    }

    /**
     * This method expects fresh structures
     *
     * @param instituteId
     * @param defaultEntityFeeAssignmentStructurePayloads
     * @return
     */
    public boolean createDefaultFeeAssignmentStructure(int instituteId, int academicSessionId,
                                                       List<DefaultEntityFeeAssignmentStructurePayload> defaultEntityFeeAssignmentStructurePayloads) {

        try {
            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    final List<Object[]> metaDataArgs = new ArrayList<>();
                    final List<Object[]> structureArgs = new ArrayList<>();
                    for (final DefaultEntityFeeAssignmentStructurePayload defaultEntityFeeAssignmentStructurePayload : defaultEntityFeeAssignmentStructurePayloads) {
                        final UUID structureId = UUID.randomUUID();
                        final List<Object> metaDataRowArgs = new ArrayList<>();
                        metaDataRowArgs.add(instituteId);
                        metaDataRowArgs.add(academicSessionId);
                        metaDataRowArgs.add(defaultEntityFeeAssignmentStructurePayload.getStructureName().trim());
                        metaDataRowArgs.add(structureId.toString());
                        metaDataRowArgs.add(defaultEntityFeeAssignmentStructurePayload.getFeeStructureType().name());
                        metaDataArgs.add(metaDataRowArgs.toArray());

                        for (final EntityFeeAssignmentPayload entityFeeAssignmentPayload : defaultEntityFeeAssignmentStructurePayload
                                .getEntityFeeAssignmentPayloads()) {
                            for (final FeeIdFeeHead feeIdFeeHead : entityFeeAssignmentPayload.getFeeIdFeeHeadsList()) {
                                for (final FeeHeadAmount feeHeadAmount : feeIdFeeHead.getFeeHeadAmountList()) {
                                    final List<Object> rowArgs = new ArrayList<>();
                                    rowArgs.add(structureId.toString());
                                    rowArgs.add(entityFeeAssignmentPayload.getEntityId());
                                    rowArgs.add(entityFeeAssignmentPayload.getFeeEntity().name());
                                    rowArgs.add(feeIdFeeHead.getFeeId().toString());
                                    rowArgs.add(feeHeadAmount.getFeeHeadId());
                                    rowArgs.add(feeHeadAmount.getAmount());
                                    structureArgs.add(rowArgs.toArray());
                                }
                            }
                        }
                    }
                    final int[] rows = jdbcTemplate.batchUpdate(CREATE_DEFAULT_FEE_ASSIGNMENT_STRUCTURE_META_DATA,
                            metaDataArgs);
                    if (rows.length != metaDataArgs.size()) {
                        logger.error("Unable to create fee structure meta data for institute {}", instituteId);
                        throw new EmbrateRunTimeException("Unable to create fee structure meta data");
                    }

                    return jdbcTemplate.batchUpdate(CREATE_DEFAULT_FEE_ASSIGNMENT_STRUCTURE,
                            structureArgs).length == structureArgs.size();
                }
            });
            return success.booleanValue();
        } catch (final Exception e) {
            logger.error("Exception while adding fee assignement structure for instiute {}", instituteId, e);
        }
        return false;
    }

    public boolean deleteDefaultFeeAssignmentStructure(int instituteId, int academicSessionId, UUID structureId) {
        final Object[] args = {structureId.toString()};
        try {
            final Boolean result = transactionTemplate.execute(new TransactionCallback<Boolean>() {

                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    final boolean deleteFromDefaultFeeAssignmentStructure = jdbcTemplate
                            .update(DELETE_FROM_DEFAULT_FEE_STRUCTURE, args) > 0;
                    if (!deleteFromDefaultFeeAssignmentStructure) {
                        throw new RuntimeException(
                                "Transaction could not delete all entities from DefaultFeeAssignmentStructure in desired way");
                    }
                    final boolean deleteFromDefaultFeeAssignmentStructureMetaData = jdbcTemplate
                            .update(DELETE_FROM_DEFAULT_FEE_STRUCTURE_META_DATA, args) > 0;
                    if (!deleteFromDefaultFeeAssignmentStructureMetaData) {
                        throw new RuntimeException(
                                "Transaction could not delete all entities from DefaultFeeAssignmentStructureMetaData in desired way");
                    }
                    return deleteFromDefaultFeeAssignmentStructureMetaData && deleteFromDefaultFeeAssignmentStructure;
                }
            });
            return result;
        } catch (final Exception e) {
            logger.error("Exception while deleting fee assignement structure for instiute {}", instituteId, e);
        }
        return false;
    }

    public boolean updateDefaultFeeAssignmentStructure(int instituteId, int academicSessionId, UUID structureId,
                                                       List<DefaultEntityFeeAssignmentStructurePayload> defaultEntityFeeAssignmentStructurePayload) {
        try {
            final DefaultEntityFeeAssignmentStructurePayload defaultEntityFeeAssignmentStructurePayloads = defaultEntityFeeAssignmentStructurePayload
                    .get(0);
            final Object[] args = {defaultEntityFeeAssignmentStructurePayloads.getStructureName(), defaultEntityFeeAssignmentStructurePayloads.getFeeStructureType().name(),
                    structureId.toString()};
            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {

                    final boolean deleteFromDefaultFeeAssignmentStructure = jdbcTemplate
                            .update(DELETE_FROM_DEFAULT_FEE_STRUCTURE, structureId.toString()) > 0;
                    if (!deleteFromDefaultFeeAssignmentStructure) {
                        throw new RuntimeException(
                                "Transaction could not delete all entities from DefaultFeeAssignmentStructure in desired way");
                    }

                    final List<Object[]> structureArgs = new ArrayList<>();
                    for (final EntityFeeAssignmentPayload entityFeeAssignmentPayload : defaultEntityFeeAssignmentStructurePayloads
                            .getEntityFeeAssignmentPayloads()) {
                        for (final FeeIdFeeHead feeIdFeeHead : entityFeeAssignmentPayload.getFeeIdFeeHeadsList()) {
                            for (final FeeHeadAmount feeHeadAmount : feeIdFeeHead.getFeeHeadAmountList()) {
                                final List<Object> rowArgs = new ArrayList<>();
                                rowArgs.add(structureId.toString());
                                rowArgs.add(entityFeeAssignmentPayload.getEntityId());
                                rowArgs.add(entityFeeAssignmentPayload.getFeeEntity().name());
                                rowArgs.add(feeIdFeeHead.getFeeId().toString());
                                rowArgs.add(feeHeadAmount.getFeeHeadId());
                                rowArgs.add(feeHeadAmount.getAmount());
                                structureArgs.add(rowArgs.toArray());
                            }
                        }
                    }

                    final boolean result = jdbcTemplate.batchUpdate(CREATE_DEFAULT_FEE_ASSIGNMENT_STRUCTURE,
                            structureArgs).length == structureArgs.size();

                    final boolean flag = jdbcTemplate.update(UPDATE_DEFAULT_FEE_STRUCTURE, args) > 0;

                    return result && flag && deleteFromDefaultFeeAssignmentStructure;
                }
            });
            return success.booleanValue();
        } catch (final Exception e) {
            logger.error("Error while create default exam structure for intitute {}", instituteId, e);
        }
        return false;
    }

    public List<FeeStructureMetaData> getFeeStructureMetaDataForStandard(int instituteId, Integer academicSessionId,
                                                                         UUID standardId, FeeStructureType feeStructureType) {
        try {
            String query = GET_FEE_STRUCTURE_META_DATA;
            final List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(standardId.toString());
            args.add(instituteId);
            args.add(academicSessionId);
            if (feeStructureType != null) {
                query += " and default_fee_assignment_structure_meta_data.structure_type = ? ";
                args.add(feeStructureType.name());
            }
            return FeeStructureMetaDataRowMapper.getFeeStructureMetaDataList(jdbcTemplate.query(query,
                    args.toArray(), FEE_STRUCTURE_META_DATA_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while reading fee assignment for instituteId {}, academicSessionId {}", instituteId,
                    academicSessionId, e);
        }
        return null;
    }

    private void invalidateInstituteHouseCache(int instituteId, UUID feeId) {
        feeConfigCache.delete(new FeeConfigCacheKey(instituteId, feeId));
    }

    private static class FeeConfigCacheKey implements ICacheKey {

        private final int instituteId;
        private final UUID feeId;

        public FeeConfigCacheKey(int instituteId, UUID feeId) {
            this.instituteId = instituteId;
            this.feeId = feeId;
        }

        public int getInstituteId() {
            return instituteId;
        }

        public UUID getFeeId() {
            return feeId;
        }

        @Override
        public String getKeyId() {
            return instituteId + "|" + feeId;
        }
    }

    private class FeeConfigCacheLoader implements ICacheLoader<FeeConfigurationDao.FeeConfigCacheKey, FeeConfigurationResponse> {

        @Override
        public FeeConfigurationResponse load(FeeConfigurationDao.FeeConfigCacheKey key) {
            return getFeeConfigurationUnCached(key.getInstituteId(), key.getFeeId());
        }
    }
}
