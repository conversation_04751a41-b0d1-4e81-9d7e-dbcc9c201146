package com.lernen.cloud.dao.tier.fees.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.fees.DefaultEntityFeeAssignmentStructure;
import com.lernen.cloud.core.api.fees.DefaultFeeAssignmentStructureDetailsRow;
import com.lernen.cloud.core.api.fees.EntityFeeAssignment;
import com.lernen.cloud.core.api.fees.FeeAssignmentDetailsRow;
import com.lernen.cloud.core.api.fees.FeeStructureType;

public class DefaultFeeAssignmentStructureRowMapper implements RowMapper<DefaultFeeAssignmentStructureDetailsRow> {

	private static final String STRUCTURE_NAME = "structure_name";
	private static final String STRUCTURE_ID = "structure_id";
	private static final String STRUCTURE_TYPE = "structure_type";

	private static final FeeAssignmentRowMapper FEE_ASSIGNMENT_ROW_MAPPER = new FeeAssignmentRowMapper();

	@Override
	public DefaultFeeAssignmentStructureDetailsRow mapRow(ResultSet rs, int rowNum) throws SQLException {
		final FeeAssignmentDetailsRow feeAssignmentDetailsRow = FEE_ASSIGNMENT_ROW_MAPPER.mapRow(rs, rowNum);

		final String structureName = rs.getString(STRUCTURE_NAME);
		final UUID structureId = UUID.fromString(rs.getString(STRUCTURE_ID));
		final FeeStructureType feeStructureType = FeeStructureType.valueOf(rs.getString(STRUCTURE_TYPE));
		return new DefaultFeeAssignmentStructureDetailsRow(structureName, structureId, feeStructureType,
				feeAssignmentDetailsRow);
	}

	/**
	 * This method returns all structures with different entities in them
	 *
	 * @return
	 */
	public static List<DefaultEntityFeeAssignmentStructure> getDefaultEntityFeeAssignmentStructures(
			List<DefaultFeeAssignmentStructureDetailsRow> defaultFeeAssignmentStructureDetailsRows) {
		final List<DefaultEntityFeeAssignmentStructure> defaultEntityFeeAssignmentStructures = new ArrayList<>();
		if (CollectionUtils.isEmpty(defaultFeeAssignmentStructureDetailsRows)) {
			return defaultEntityFeeAssignmentStructures;
		}

		final Map<UUID, FeeStructureMetaDataData> structureNameMap = new LinkedHashMap<>();
		final Map<UUID, List<FeeAssignmentDetailsRow>> structureWiseRows = new LinkedHashMap<>();
		for (final DefaultFeeAssignmentStructureDetailsRow defaultFeeAssignmentStructureDetailsRow : defaultFeeAssignmentStructureDetailsRows) {
			final UUID structureId = defaultFeeAssignmentStructureDetailsRow.getStructureId();
			structureNameMap.put(structureId,
					new FeeStructureMetaDataData(defaultFeeAssignmentStructureDetailsRow.getStructureName(),
							defaultFeeAssignmentStructureDetailsRow.getFeeStructureType()));
			if (!structureWiseRows.containsKey(structureId)) {
				structureWiseRows.put(structureId, new ArrayList<>());
			}
			structureWiseRows.get(structureId)
					.add(defaultFeeAssignmentStructureDetailsRow.getFeeAssignmentDetailsRow());
		}

		for (final Entry<UUID, List<FeeAssignmentDetailsRow>> structureEntry : structureWiseRows.entrySet()) {
			final UUID structureId = structureEntry.getKey();
			final Map<String, EntityFeeAssignment> entityFeeAssignments = FeeAssignmentRowMapper
					.getFeeAssignments(structureEntry.getValue());
			defaultEntityFeeAssignmentStructures
					.add(new DefaultEntityFeeAssignmentStructure(structureNameMap.get(structureId).getStructureName(),
							structureId, structureNameMap.get(structureId).getFeeStructureType(),
							new ArrayList<>(entityFeeAssignments.values())));
		}
		return defaultEntityFeeAssignmentStructures;
	}

	private static class FeeStructureMetaDataData {
		private final String structureName;
		private final FeeStructureType feeStructureType;

		public FeeStructureMetaDataData(String structureName, FeeStructureType feeStructureType) {
			this.structureName = structureName;
			this.feeStructureType = feeStructureType;
		}

		public String getStructureName() {
			return structureName;
		}

		public FeeStructureType getFeeStructureType() {
			return feeStructureType;
		}

	}

}
