package com.lernen.cloud.dao.tier.fees.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.fees.FeeCategory;

public class FeeCategoryRowMapper implements RowMapper<FeeCategory> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String FEE_CATEGORY_ID = "fee_category_id";
	private static final String FEE_CATEGORY = "fee_category";
	private static final String DESCRIPTION = "description";

	@Override
	public FeeCategory mapRow(ResultSet rs, int rowNum) throws SQLException {
		return new FeeCategory(rs.getInt(FEE_CATEGORY_ID), rs.getInt(INSTITUTE_ID), rs.getString(FEE_CATEGORY),
				rs.getString(DESCRIPTION));
	}
}
