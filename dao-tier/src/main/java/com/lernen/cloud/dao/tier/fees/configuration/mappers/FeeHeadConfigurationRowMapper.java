package com.lernen.cloud.dao.tier.fees.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.fees.FeeHeadConfiguration;
import com.lernen.cloud.core.api.fees.FeeHeadTag;
import com.lernen.cloud.core.api.fees.FeeHeadType;

public class FeeHeadConfigurationRowMapper implements RowMapper<FeeHeadConfiguration> {

	public static final String INSTITUTE_ID = "institute_id";
	public static final String FEE_HEAD_ID = "fee_head_id";
	public static final String FEE_CATEGORY_ID = "fee_category_id";
	public static final String FEE_HEAD = "fee_head";
	public static final String REFUNDABLE = "refundable";
	public static final String DESCRIPTION = "description";
	public static final String FEE_HEAD_TYPE = "fee_head_type";
	public static final String FEE_HEAD_TAG = "fee_head_tag";

	@Override
	public FeeHeadConfiguration mapRow(ResultSet rs, int rowNum) throws SQLException {

		return new FeeHeadConfiguration(rs.getInt(INSTITUTE_ID), rs.getInt(FEE_HEAD_ID), rs.getInt(FEE_CATEGORY_ID),
				rs.getString(FEE_HEAD), rs.getBoolean(REFUNDABLE), rs.getString(DESCRIPTION),
				FeeHeadType.valueOf(rs.getString(FEE_HEAD_TYPE)), StringUtils.isBlank(rs.getString(FEE_HEAD_TAG)) ? null
				: FeeHeadTag.valueOf(rs.getString(FEE_HEAD_TAG)));
	}
}