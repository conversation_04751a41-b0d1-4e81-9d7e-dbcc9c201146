/**
 * 
 */
package com.lernen.cloud.dao.tier.fees.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.fees.FeeStructureMetaData;
import com.lernen.cloud.core.api.fees.FeeStructureType;

/**
 * <AUTHOR>
 *
 */
public class FeeStructureMetaDataRowMapper implements RowMapper<FeeStructureMetaData> {

	private static final String STRUCTURE_ID = "structure_id";
	private static final String STRUCTURE_NAME = "structure_name";
	private static final String STRUCTURE_TYPE = "structure_type";
	
	
	@Override
	public FeeStructureMetaData mapRow(ResultSet rs, int rowNum) throws SQLException {
		if(rs.getString(STRUCTURE_ID) == null) {
			return null;
		}
		return new FeeStructureMetaData(UUID.fromString(rs.getString(STRUCTURE_ID)),
				rs.getString(STRUCTURE_NAME), FeeStructureType.getFeeStructureType(rs.getString(STRUCTURE_TYPE)));
	}


	public static List<FeeStructureMetaData> getFeeStructureMetaDataList(List<FeeStructureMetaData> feeStructureMetaDataList) {
		if(CollectionUtils.isEmpty(feeStructureMetaDataList)) {
			return null;
		}
		
		Map<UUID, FeeStructureMetaData> feeStructureMetaDataMap = new HashMap<UUID, FeeStructureMetaData>();
		for(FeeStructureMetaData feeStructureMetaData : feeStructureMetaDataList) {
			if(!feeStructureMetaDataMap.containsKey(feeStructureMetaData.getStructureId())) {
				feeStructureMetaDataMap.put(feeStructureMetaData.getStructureId(), feeStructureMetaData);
			} 
		}
		return new ArrayList<FeeStructureMetaData>(feeStructureMetaDataMap.values());
	}

	

}
