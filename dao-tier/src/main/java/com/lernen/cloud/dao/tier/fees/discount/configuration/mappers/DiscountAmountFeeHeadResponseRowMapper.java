package com.lernen.cloud.dao.tier.fees.discount.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.fees.DiscountAmountFeeHeadResponse;
import com.lernen.cloud.core.api.fees.FeeHeadConfiguration;
import com.lernen.cloud.dao.tier.fees.configuration.mappers.FeeHeadConfigurationRowMapper;

public class DiscountAmountFeeHeadResponseRowMapper implements RowMapper<DiscountAmountFeeHeadResponse> {

	private static final String IS_PERCENT = "is_percent";
	private static final String AMOUNT = "amount";

	private static final FeeHeadConfigurationRowMapper FEE_HEAD_CONFIGURATION_ROW_MAPPER = new FeeHeadConfigurationRowMapper();

	@Override
	public DiscountAmountFeeHeadResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
		final FeeHeadConfiguration feeHeadConfiguration = FEE_HEAD_CONFIGURATION_ROW_MAPPER.mapRow(rs, rowNum);

		return new DiscountAmountFeeHeadResponse(feeHeadConfiguration, rs.getBoolean(IS_PERCENT), rs.getDouble(AMOUNT));
	}
}
