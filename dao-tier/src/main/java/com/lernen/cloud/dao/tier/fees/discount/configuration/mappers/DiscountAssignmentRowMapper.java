package com.lernen.cloud.dao.tier.fees.discount.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.fees.DiscountAssignmentData;
import com.lernen.cloud.core.api.fees.DiscountAssignmentRow;
import com.lernen.cloud.core.api.fees.DiscountConfigurationDetailedRow;
import com.lernen.cloud.core.api.fees.DiscountConfigurationResponse;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.dao.tier.student.mappers.StudentRowMapper;

@Deprecated
public class DiscountAssignmentRowMapper implements RowMapper<DiscountAssignmentRow> {

	private static final DiscountConfigurationRowMapper DISCOUNT_CONFIGURATION_ROW_MAPPER = new DiscountConfigurationRowMapper();
	private static final StudentRowMapper STUDENT_ROW_MAPPER = new StudentRowMapper();

	@Override
	public DiscountAssignmentRow mapRow(ResultSet rs, int rowNum) throws SQLException {
		final DiscountConfigurationDetailedRow discountConfigurationDetailedRow = DISCOUNT_CONFIGURATION_ROW_MAPPER
				.mapRow(rs, rowNum);
		final Student student = STUDENT_ROW_MAPPER.mapRow(rs, rowNum);

		return new DiscountAssignmentRow(student, discountConfigurationDetailedRow);
	}

	/**
	 * This method assumes that all elements passed in list belong to same
	 * student id
	 *
	 * @return
	 */
	public static DiscountAssignmentData getDiscountAssignmentData(
			List<DiscountAssignmentRow> discountAssignmentRowList) {
		if (CollectionUtils.isEmpty(discountAssignmentRowList)) {
			return null;
		}
		final List<DiscountConfigurationDetailedRow> discountConfigurationDetailedRows = new LinkedList<>();
		for (final DiscountAssignmentRow discountAssignmentRow : discountAssignmentRowList) {
			discountConfigurationDetailedRows.add(discountAssignmentRow.getDiscountConfigurationDetailedRow());
		}

		final List<DiscountConfigurationResponse> discountConfigurations = DiscountConfigurationRowMapper
				.getDiscountConfigurations(discountConfigurationDetailedRows);

		final DiscountAssignmentRow firstRow = discountAssignmentRowList.get(0);

		return new DiscountAssignmentData(firstRow.getStudentData(), discountConfigurations);
	}

	public static List<DiscountAssignmentData> getDiscountAssignmentDataList(
			List<DiscountAssignmentRow> discountAssignmentRowList) {
		final List<DiscountAssignmentData> discountAssignmentDataList = new ArrayList<>();
		if (CollectionUtils.isEmpty(discountAssignmentRowList)) {
			return discountAssignmentDataList;
		}

		final Map<UUID, List<DiscountAssignmentRow>> studentIdVsDiscountAssignmentRowMap = new LinkedHashMap<>();

		for (final DiscountAssignmentRow discountAssignmentRow : discountAssignmentRowList) {
			if (studentIdVsDiscountAssignmentRowMap
					.containsKey(discountAssignmentRow.getStudentData().getStudentId())) {
				studentIdVsDiscountAssignmentRowMap.get(discountAssignmentRow.getStudentData().getStudentId())
						.add(discountAssignmentRow);
				continue;
			}
			final List<DiscountAssignmentRow> discountAssignmentRowDetails = new ArrayList<>();
			discountAssignmentRowDetails.add(discountAssignmentRow);
			studentIdVsDiscountAssignmentRowMap.put(discountAssignmentRow.getStudentData().getStudentId(),
					discountAssignmentRowDetails);
		}

		final Iterator<Map.Entry<UUID, List<DiscountAssignmentRow>>> studentIdVsDiscountAssignmentRowMapItr = studentIdVsDiscountAssignmentRowMap
				.entrySet().iterator();
		while (studentIdVsDiscountAssignmentRowMapItr.hasNext()) {
			final Map.Entry<UUID, List<DiscountAssignmentRow>> entry = studentIdVsDiscountAssignmentRowMapItr.next();
			final DiscountAssignmentData discountAssignmentData = getDiscountAssignmentData(entry.getValue());
			discountAssignmentDataList.add(discountAssignmentData);
		}
		return discountAssignmentDataList;
	}
}
