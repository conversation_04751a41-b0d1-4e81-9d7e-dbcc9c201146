package com.lernen.cloud.dao.tier.fees.discount.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.fees.DiscountAmountFeeHeadResponse;
import com.lernen.cloud.core.api.fees.DiscountBasicInfo;
import com.lernen.cloud.core.api.fees.DiscountConfigurationDetailedRow;
import com.lernen.cloud.core.api.fees.DiscountConfigurationResponse;

public class DiscountConfigurationRowMapper implements RowMapper<DiscountConfigurationDetailedRow> {

	private static final DiscountBasicInfoRowMapper DISCOUNT_BASIC_INFO_ROW_MAPPER = new DiscountBasicInfoRowMapper();
	private static final DiscountAmountFeeHeadResponseRowMapper DISCOUNT_AMOUNT_FEE_HEAD_ROW_MAPPER = new DiscountAmountFeeHeadResponseRowMapper();

	@Override
	public DiscountConfigurationDetailedRow mapRow(ResultSet rs, int rowNum) throws SQLException {
		final DiscountBasicInfo discountBasicInfo = DISCOUNT_BASIC_INFO_ROW_MAPPER.mapRow(rs, rowNum);
		final DiscountAmountFeeHeadResponse discountAmountFeeHead = DISCOUNT_AMOUNT_FEE_HEAD_ROW_MAPPER.mapRow(rs,
				rowNum);
		return new DiscountConfigurationDetailedRow(discountBasicInfo, discountAmountFeeHead);
	}

	/**
	 * This method assumes that all elements passed in list belong to same discount
	 * id
	 *
	 * @return
	 */
	public static DiscountConfigurationResponse getDiscountConfiguration(
			List<DiscountConfigurationDetailedRow> discountConfigurationDetailedRowList) {
		if (CollectionUtils.isEmpty(discountConfigurationDetailedRowList)) {
			return null;
		}
		final DiscountConfigurationDetailedRow firstRow = discountConfigurationDetailedRowList.get(0);

		final List<DiscountAmountFeeHeadResponse> discountAmountFeeHeadResponseList = new ArrayList<>();

		for (final DiscountConfigurationDetailedRow discountConfigurationDetailedRow : discountConfigurationDetailedRowList) {
			discountAmountFeeHeadResponseList.add(discountConfigurationDetailedRow.getDiscountAmountFeahHeadResponse());
		}
		return new DiscountConfigurationResponse(firstRow.getDiscountBasicInfo(), discountAmountFeeHeadResponseList);
	}

	public static List<DiscountConfigurationResponse> getDiscountConfigurations(
			List<DiscountConfigurationDetailedRow> discountConfigurationDetailedRowList) {
		final List<DiscountConfigurationResponse> discountConfigurations = new ArrayList<>();
		if (CollectionUtils.isEmpty(discountConfigurationDetailedRowList)) {
			return new ArrayList<>();
		}
		final Map<UUID, List<DiscountConfigurationDetailedRow>> discountRowsMap = new HashMap<>();
		for (final DiscountConfigurationDetailedRow discountConfigurationDetailedRow : discountConfigurationDetailedRowList) {
			final UUID discountId = discountConfigurationDetailedRow.getDiscountBasicInfo().getDiscountId();
			if (!discountRowsMap.containsKey(discountId)) {
				discountRowsMap.put(discountId, new ArrayList<>());
			}
			discountRowsMap.get(discountId).add(discountConfigurationDetailedRow);
		}

		for (final Entry<UUID, List<DiscountConfigurationDetailedRow>> entry : discountRowsMap.entrySet()) {
			discountConfigurations.add(getDiscountConfiguration(entry.getValue()));
		}
		return discountConfigurations;
	}
}
