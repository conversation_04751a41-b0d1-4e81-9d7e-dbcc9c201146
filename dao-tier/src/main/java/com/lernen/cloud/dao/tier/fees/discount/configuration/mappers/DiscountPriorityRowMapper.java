package com.lernen.cloud.dao.tier.fees.discount.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.fees.DiscountPriority;
/**
 * 
 * <AUTHOR>
 *
 */
public class DiscountPriorityRowMapper implements RowMapper<DiscountPriority> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String DISCOUNT_ID = "discount_id";
	private static final String PRIORITY_LEVEL = "priority_level";

	@Override
	public DiscountPriority mapRow(ResultSet rs, int rowNum) throws SQLException {
		return new DiscountPriority(rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(DISCOUNT_ID)),
				rs.getInt(PRIORITY_LEVEL));
	}
}
