package com.lernen.cloud.dao.tier.fees.discount.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.UUID;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.fee.discount.StudentDiscountData;
import com.lernen.cloud.core.api.fee.discount.StudentFeeDiscountAssignment;
import com.lernen.cloud.core.api.fee.discount.StudentFeeDiscountAssignmentRow;
import com.lernen.cloud.core.api.fee.discount.StudentFeeDiscountPayload;
import com.lernen.cloud.core.api.fee.discount.StudentFeeHeadDiscountPayload;

public class StudentDiscountAssignmentRowMapper implements RowMapper<StudentFeeDiscountAssignmentRow> {

	private static final String INSTITUTE_ID = "student_fee_discount_assignment.institute_id";
	private static final String STRUCTURE_ID = "student_fee_discount_assignment.structure_id";
	private static final String STUDENT_ID = "student_id";
	private static final String ACADMIC_SESSION_ID = "student_fee_discount_assignment.academic_session_id";
	private static final String STRUCTURE_NAME = "structure_name";
	private static final String FEE_ID = "fee_id";
	private static final String FEE_HEAD_ID = "fee_head_id";
	private static final String IS_PERCENT = "is_percent";
	private static final String AMOUNT = "amount";

	@Override
	public StudentFeeDiscountAssignmentRow mapRow(ResultSet rs, int rowNum) throws SQLException {

		return new StudentFeeDiscountAssignmentRow(rs.getInt(INSTITUTE_ID), rs.getInt(ACADMIC_SESSION_ID),
				UUID.fromString(rs.getString(STRUCTURE_ID)), rs.getString(STRUCTURE_NAME),
				UUID.fromString(rs.getString(STUDENT_ID)), UUID.fromString(rs.getString(FEE_ID)),
				rs.getInt(FEE_HEAD_ID), rs.getBoolean(IS_PERCENT), rs.getDouble(AMOUNT));
	}

	/**
	 * 
	 * This method assumes, all rows belong to a single student and multiple
	 * academic session
	 * 
	 * @param studentFeeDiscountAssignmentRows
	 * @return
	 */
	public static Map<Integer, StudentFeeDiscountAssignment> getStudentAllSessionFeeDiscountAssignment(
			List<StudentFeeDiscountAssignmentRow> studentFeeDiscountAssignmentRows) {

		Map<Integer, StudentFeeDiscountAssignment> studentAllSessionFeeDiscountAssignment = new HashMap<>();
		if (CollectionUtils.isEmpty(studentFeeDiscountAssignmentRows)) {
			return studentAllSessionFeeDiscountAssignment;
		}

		Map<Integer, List<StudentFeeDiscountAssignmentRow>> studentFeeDiscountAssignmentRowSessionMap = new HashMap<>();
		for (StudentFeeDiscountAssignmentRow studentFeeDiscountAssignmentRow : studentFeeDiscountAssignmentRows) {
			Integer academicSessionId = studentFeeDiscountAssignmentRow.getAcademicSessionId();
			if (!studentFeeDiscountAssignmentRowSessionMap.containsKey(academicSessionId)) {
				studentFeeDiscountAssignmentRowSessionMap.put(academicSessionId, new ArrayList<>());
			}

			studentFeeDiscountAssignmentRowSessionMap.get(academicSessionId).add(studentFeeDiscountAssignmentRow);
		}

		for (Entry<Integer, List<StudentFeeDiscountAssignmentRow>> sessionEntry : studentFeeDiscountAssignmentRowSessionMap
				.entrySet()) {
			studentAllSessionFeeDiscountAssignment.put(sessionEntry.getKey(),
					getStudentFeeDiscountAssignment(sessionEntry.getValue()));
		}

		return studentAllSessionFeeDiscountAssignment;
	}
	
	/**
	 * 
	 * This method assumes, all rows belong to a single academic session and multiple
	 * student
	 * 
	 * @param studentFeeDiscountAssignmentRows
	 * @return
	 */
	public static Map<UUID, StudentFeeDiscountAssignment> getFeeDiscountAssignmentOfStandard(
			List<StudentFeeDiscountAssignmentRow> studentFeeDiscountAssignmentRows) {

		Map<UUID, StudentFeeDiscountAssignment> studentAllSessionFeeDiscountAssignment = new HashMap<>();
		if (CollectionUtils.isEmpty(studentFeeDiscountAssignmentRows)) {
			return studentAllSessionFeeDiscountAssignment;
		}

		Map<UUID, List<StudentFeeDiscountAssignmentRow>> studentFeeDiscountAssignmentRowSessionMap = new HashMap<>();
		for (StudentFeeDiscountAssignmentRow studentFeeDiscountAssignmentRow : studentFeeDiscountAssignmentRows) {
			UUID studentId = studentFeeDiscountAssignmentRow.getStudentId();
			if (!studentFeeDiscountAssignmentRowSessionMap.containsKey(studentId)) {
				studentFeeDiscountAssignmentRowSessionMap.put(studentId, new ArrayList<>());
			}

			studentFeeDiscountAssignmentRowSessionMap.get(studentId).add(studentFeeDiscountAssignmentRow);
		}

		for (Entry<UUID, List<StudentFeeDiscountAssignmentRow>> sessionEntry : studentFeeDiscountAssignmentRowSessionMap
				.entrySet()) {
			studentAllSessionFeeDiscountAssignment.put(sessionEntry.getKey(),
					getStudentFeeDiscountAssignment(sessionEntry.getValue()));
		}

		return studentAllSessionFeeDiscountAssignment;
	}

	/**
	 * This method assumes, all rows belong to a single student and academic session
	 * 
	 * @param studentFeeDiscountAssignmentRows
	 * @return
	 */
	public static StudentFeeDiscountAssignment getStudentFeeDiscountAssignment(
			List<StudentFeeDiscountAssignmentRow> studentFeeDiscountAssignmentRows) {

		if (CollectionUtils.isEmpty(studentFeeDiscountAssignmentRows)) {
			return null;
		}

		StudentFeeDiscountAssignmentRow firstStudentFeeDiscountAssignmentRow = studentFeeDiscountAssignmentRows.get(0);

		Map<UUID, Map<UUID, Map<Integer, StudentFeeDiscountAssignmentRow>>> discountFeeIdFeeHeadMap = new HashMap<>();

		for (StudentFeeDiscountAssignmentRow studentFeeDiscountAssignmentRow : studentFeeDiscountAssignmentRows) {
			UUID discountId = studentFeeDiscountAssignmentRow.getDiscountId();
			UUID feeId = studentFeeDiscountAssignmentRow.getFeeId();
			Integer feeHeadId = studentFeeDiscountAssignmentRow.getFeeHeadId();

			if (!discountFeeIdFeeHeadMap.containsKey(discountId)) {
				discountFeeIdFeeHeadMap.put(discountId, new HashMap<>());
			}

			Map<UUID, Map<Integer, StudentFeeDiscountAssignmentRow>> feeIdFeeHeadMap = discountFeeIdFeeHeadMap
					.get(discountId);
			if (!feeIdFeeHeadMap.containsKey(feeId)) {
				feeIdFeeHeadMap.put(feeId, new HashMap<>());
			}

			Map<Integer, StudentFeeDiscountAssignmentRow> feeHeadMap = feeIdFeeHeadMap.get(feeId);

			feeHeadMap.put(feeHeadId, studentFeeDiscountAssignmentRow);
		}

		List<StudentDiscountData> studentDiscountDataList = new ArrayList<>();
		for (Entry<UUID, Map<UUID, Map<Integer, StudentFeeDiscountAssignmentRow>>> discountFeeIdFeeHeadEntry : discountFeeIdFeeHeadMap
				.entrySet()) {
			UUID discountId = discountFeeIdFeeHeadEntry.getKey();
			String discountName = null;
			List<StudentFeeDiscountPayload> studentFeeDiscountPayloadList = new ArrayList<>();
			for (Entry<UUID, Map<Integer, StudentFeeDiscountAssignmentRow>> feeIdFeeHeadEntry : discountFeeIdFeeHeadEntry
					.getValue().entrySet()) {

				UUID feeId = feeIdFeeHeadEntry.getKey();
				List<StudentFeeHeadDiscountPayload> studentFeeHeadDiscountPayloadList = new ArrayList<>();
				for (Entry<Integer, StudentFeeDiscountAssignmentRow> feeHeadEntry : feeIdFeeHeadEntry.getValue()
						.entrySet()) {
					if (discountName == null) {
						discountName = feeHeadEntry.getValue().getDiscountName();
					}
					studentFeeHeadDiscountPayloadList.add(new StudentFeeHeadDiscountPayload(feeHeadEntry.getKey(),
							feeHeadEntry.getValue().isPercent(), feeHeadEntry.getValue().getAmount()));
				}

				studentFeeDiscountPayloadList
						.add(new StudentFeeDiscountPayload(feeId, studentFeeHeadDiscountPayloadList));
			}

			studentDiscountDataList
					.add(new StudentDiscountData(discountId, discountName, studentFeeDiscountPayloadList));

		}

		return new StudentFeeDiscountAssignment(firstStudentFeeDiscountAssignmentRow.getInstituteId(),
				firstStudentFeeDiscountAssignmentRow.getAcademicSessionId(),
				firstStudentFeeDiscountAssignmentRow.getStudentId(), studentDiscountDataList);
	}
}
