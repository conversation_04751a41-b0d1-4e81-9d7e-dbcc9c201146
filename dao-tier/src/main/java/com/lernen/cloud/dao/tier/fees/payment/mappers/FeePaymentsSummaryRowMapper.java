package com.lernen.cloud.dao.tier.fees.payment.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.fees.payment.FeeHeadPaymentData;
import com.lernen.cloud.core.api.fees.payment.FeePaymentSummary;
import com.lernen.cloud.core.api.fees.payment.FeePaymentSummaryRowData;

public class FeePaymentsSummaryRowMapper implements RowMapper<FeePaymentSummaryRowData> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String STUDENT_ID = "student_id";
	private static final String FEE_ID = "fee_id";
	private static final String FEE_HEAD_ID = "fee_head_id";
	private static final String ASSIGNED_AMOUNT = "assigned_amount";
	private static final String PAID_AMOUNT = "paid_amount";
	private static final String INSTANT_DISCOUNT_AMOUNT = "instant_discount_amount";
	private static final String ASSIGNED_DISCOUNT_AMOUNT = "assigned_discount_amount";
	private static final String DISCOUNT_GROUP_ID = "discount_group_id";
	private static final String TRANSACTION_COUNT = "transaction_count";

	@Override
	public FeePaymentSummaryRowData mapRow(ResultSet rs, int rowNum) throws SQLException {
		return new FeePaymentSummaryRowData(rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(STUDENT_ID)),
				UUID.fromString(rs.getString(FEE_ID)), rs.getInt(FEE_HEAD_ID), rs.getDouble(ASSIGNED_AMOUNT),
				rs.getDouble(PAID_AMOUNT), rs.getDouble(INSTANT_DISCOUNT_AMOUNT),
				rs.getDouble(ASSIGNED_DISCOUNT_AMOUNT), StringUtils.isEmpty(rs.getString(DISCOUNT_GROUP_ID)) ? null
						: UUID.fromString(rs.getString(DISCOUNT_GROUP_ID)),
				rs.getInt(TRANSACTION_COUNT));
	}

	/**
	 * This method assumes that all elements passed in list belong to same Fee
	 * id and student id
	 * 
	 * @return
	 */
	private static FeePaymentSummary getFeePaymentSummary(List<FeePaymentSummaryRowData> feePaymentRowDataList) {
		if (CollectionUtils.isEmpty(feePaymentRowDataList)) {
			return null;
		}
		FeePaymentSummaryRowData feePaymentRowDataRow = feePaymentRowDataList.get(0);
		List<FeeHeadPaymentData> feeHeadPaymentDataList = new ArrayList<>();
		for (FeePaymentSummaryRowData feePaymentRowData : feePaymentRowDataList) {
			feeHeadPaymentDataList.add(new FeeHeadPaymentData(feePaymentRowData.getFeeHeadId(),
					feePaymentRowData.getAssignedAmount(), feePaymentRowData.getPaidAmount(),
					feePaymentRowData.getInstantDiscountAmount(), feePaymentRowData.getAssignedDiscountAmount(),
					feePaymentRowData.getDiscountGroupId(), feePaymentRowData.getTransactionCount()));
		}

		return new FeePaymentSummary(feePaymentRowDataRow.getStudentId(), feePaymentRowDataRow.getInstituteId(),
				feePaymentRowDataRow.getFeeId(), feeHeadPaymentDataList);
	}

	/**
	 * This method assumes that all elements passed in list belong to same
	 * student id
	 * 
	 * @return
	 */
	public static List<FeePaymentSummary> getFeePaymentSummaryList(
			List<FeePaymentSummaryRowData> feePaymentRowDataList) {
		List<FeePaymentSummary> feePaymentDetailsList = new ArrayList<>();
		if (CollectionUtils.isEmpty(feePaymentRowDataList)) {
			return feePaymentDetailsList;
		}
		Map<UUID, List<FeePaymentSummaryRowData>> feesRows = new HashMap<>();
		for (FeePaymentSummaryRowData feePaymentRowData : feePaymentRowDataList) {
			UUID feeId = feePaymentRowData.getFeeId();
			if (!feesRows.containsKey(feeId)) {
				feesRows.put(feeId, new ArrayList<>());
			}
			feesRows.get(feeId).add(feePaymentRowData);
		}

		for (Entry<UUID, List<FeePaymentSummaryRowData>> entry : feesRows.entrySet()) {
			feePaymentDetailsList.add(getFeePaymentSummary(entry.getValue()));
		}

		return feePaymentDetailsList;
	}
}
