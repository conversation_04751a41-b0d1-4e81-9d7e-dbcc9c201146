package com.lernen.cloud.dao.tier.fees.payment.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionMetaData;
import com.lernen.cloud.core.api.fees.payment.TransactionWiseDayLevelAmount;
import com.lernen.cloud.core.api.student.StudentBasicInfo;
import com.lernen.cloud.dao.tier.student.mappers.StudentBasicInfoRowMapper;

public class TransactionWiseDayLevelAmountDetailsRowMapper implements RowMapper<TransactionWiseDayLevelAmount> {

	private static final String PAID_AMOUNT = "paid_amount";
	private static final String INSTANT_DISCOUNT_AMOUNT = "instant_discount_amount";
	private static final String FINE_AMOUNT = "fine_amount";

	private static final FeePaymentTransactionMetaDataRowMapper FEE_PAYMENT_TRANSACTION_META_DATA_ROW_MAPPER = new FeePaymentTransactionMetaDataRowMapper();
	private static final StudentBasicInfoRowMapper STUDENT_BASIC_INFO_ROW_MAPPER = new StudentBasicInfoRowMapper();

	@Override
	public TransactionWiseDayLevelAmount mapRow(ResultSet rs, int rowNum) throws SQLException {
		final FeePaymentTransactionMetaData feePaymentTransactionMetaData = FEE_PAYMENT_TRANSACTION_META_DATA_ROW_MAPPER
				.mapRow(rs, rowNum);
		final StudentBasicInfo studentBasicInfo = STUDENT_BASIC_INFO_ROW_MAPPER.mapRow(rs, rowNum);
		return new TransactionWiseDayLevelAmount(feePaymentTransactionMetaData, studentBasicInfo,
				rs.getDouble(PAID_AMOUNT), rs.getDouble(INSTANT_DISCOUNT_AMOUNT), rs.getDouble(FINE_AMOUNT));

	}

}
