package com.lernen.cloud.dao.tier.hostelmanagement;

import com.embrate.cloud.core.api.hostelmanagement.HostelDetails;
import com.lernen.cloud.dao.tier.hostelmanagement.mappers.HostelDetailsRowMapper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class HostelManagementDao {
    private static final Logger logger = LogManager.getLogger(HostelManagementDao.class);

    private final JdbcTemplate jdbcTemplate;
    private final HostelDetailsRowMapper HOSTEl_DETAILS_ROW_MAPPER = new HostelDetailsRowMapper();

    private  static  final  String ADD_HOSTEL_DETAILS = "insert into hostel_management (" +
            "institute_id, hostel_id, hostel_name, " +
            "primary_contact_no, secondary_contact_no, address, email_id) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?)";

    private  static  final String UPDATE_HOSTEL_DETAILS = "update hostel_management set " +
            "hostel_name = ?, primary_contact_no = ?, secondary_contact_no = ?, address = ?, " +
            "email_id = ? where institute_id = ? and hostel_id = ?";

    private static final String DELETE_HOSTEL_DETAILS = "delete from hostel_management where institute_id = ? and hostel_id = ?";

    private static final String GET_HOSTEL_DETAILS_BY_INSTITUTE_ID = "select * from hostel_management where institute_id = ?";

    private static final String COUNT_STUDENTS_BY_HOSTEL_ID = "select count(*) from student_academic_session_details where hostel_id = ?";

    public HostelManagementDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public boolean addHostelDetails(int instituteId, HostelDetails hostelDetailsPayload){
        UUID hostelId = UUID.randomUUID();
        try {
             return jdbcTemplate.update(ADD_HOSTEL_DETAILS,
                    hostelDetailsPayload.getInstituteId(), hostelId.toString(), hostelDetailsPayload.getHostelName(), hostelDetailsPayload.getPrimaryContactNo(), hostelDetailsPayload.getSecondaryContactNo(),
                    hostelDetailsPayload.getAddress(), hostelDetailsPayload.getEmailId()) == 1;
        } catch (final Exception e) {
            logger.error("Error while adding hostel details for institute {}", hostelDetailsPayload.getInstituteId(), e);
        }
        return false;
    }

    public boolean updateHostelDetails(int instituteId, HostelDetails hostelDetailsPayload) {
        try {
            return jdbcTemplate.update(UPDATE_HOSTEL_DETAILS,  hostelDetailsPayload.getHostelName(),
                    hostelDetailsPayload.getPrimaryContactNo(), hostelDetailsPayload.getSecondaryContactNo(),
                    hostelDetailsPayload.getAddress(), hostelDetailsPayload.getEmailId(), hostelDetailsPayload.getInstituteId(), hostelDetailsPayload.getHostelId().toString()) == 1;

        } catch (final Exception e) {
            logger.error("Exception while updating hostel details for institute {}, payload {}",
                   hostelDetailsPayload.getInstituteId(), hostelDetailsPayload, e);
        }
        return false;
    }

    public boolean deleteHostelDetails(int instituteId, UUID hostelId) {
        try {
            return jdbcTemplate.update(DELETE_HOSTEL_DETAILS,  instituteId, hostelId.toString()) == 1;
        } catch (final Exception e) {
            logger.error("Exception while updating hostel details for institute {}, hostelId {}",
                    instituteId, hostelId, e);
        }
        return false;
    }

    public List<HostelDetails> getHostelDetails(int instituteId) {
        try {
            List<Object> args = new ArrayList<>();
            args.add(instituteId);
            return jdbcTemplate.query(GET_HOSTEL_DETAILS_BY_INSTITUTE_ID, args.toArray(),
                    HOSTEl_DETAILS_ROW_MAPPER);
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException while getting hostel details", dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while getting hostel details", e);
        }

        return null;
    }

    public int getAssignedStudentCount(UUID hostelId) {
        try {
            return jdbcTemplate.queryForObject(COUNT_STUDENTS_BY_HOSTEL_ID, Integer.class, hostelId.toString());
        } catch (final Exception e) {
            logger.error("Exception while fetch count of hostel for , hostelId {}", hostelId, e);
        }
        return 0;
    }
}
