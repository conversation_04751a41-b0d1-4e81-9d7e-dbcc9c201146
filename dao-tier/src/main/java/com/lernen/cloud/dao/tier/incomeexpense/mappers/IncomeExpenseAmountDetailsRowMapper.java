/**
 * 
 */
package com.lernen.cloud.dao.tier.incomeexpense.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.incomeexpense.IncomeExpenseAmountDetails;

/**
 * <AUTHOR>
 *
 */
public class IncomeExpenseAmountDetailsRowMapper implements RowMapper<IncomeExpenseAmountDetails> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String CATEGORY_ID = "income_expense_category.category_id";
	private static final String CATEGORY_NAME = "income_expense_category.category_name";
	private static final String TRANSACTION_DATE = "income_expense_transactions.transaction_date";
	private static final String AMOUNT = "income_expense_transactions.amount";
	
	@Override
	public IncomeExpenseAmountDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
		
		final Timestamp transactionDate = rs.getTimestamp(TRANSACTION_DATE);
		final Integer transactionDateTime = transactionDate == null ? null : (int) (transactionDate.getTime() / 1000l);
		
		
		return new IncomeExpenseAmountDetails(rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(CATEGORY_ID)), rs.getString(CATEGORY_NAME),
				transactionDateTime, rs.getDouble(AMOUNT));
	}
	
}
