/**
 *
 */
package com.lernen.cloud.dao.tier.incomeexpense.mappers;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.UUID;

import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.dao.tier.staff.mappers.StaffRowMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.incomeexpense.IncomeExpenseCategory;
import com.lernen.cloud.core.api.incomeexpense.IncomeExpenseEntity;
import com.lernen.cloud.core.api.incomeexpense.IncomeExpenseTransactionDetails;
import com.lernen.cloud.core.api.incomeexpense.IncomeExpenseTransactionDocumentType;
import com.lernen.cloud.core.api.incomeexpense.IncomeExpenseTransactionStatus;
import com.lernen.cloud.core.api.incomeexpense.IncomeExpenseTransactionType;
import com.lernen.cloud.core.api.user.Document;

/**
 * <AUTHOR>
 *
 */
public class IncomeExpenseTransactionDetailsRowMapper implements RowMapper<IncomeExpenseTransactionDetails> {
	private static final String INSTITUTE_ID = "institute_id";
	private static final String ACADEMIC_SESSION_ID = "academic_session_id";
	private static final String TRANSACTION_ID = "transaction_id";
	private static final String TRANSACTION_TITLE = "transaction_title";
	private static final String TRANSACTION_MODE = "transaction_mode";
	private static final String AMOUNT = "amount";
	private static final String TRANSACTION_DATE = "transaction_date";
	private static final String ADD_TO_TOTAL = "add_in_total";
	private static final String TRANSACTION_DESCRIPTION = "transaction_description";
	private static final String TRANSACTION_DOCUMENTS = "transaction_documents";
	private static final String TRANSACTION_STATUS = "transaction_status";
	private static final String TRANSACTION_TYPE = "transaction_type";
	private static final String CREATED_USER_ID = "created_user_id";
	private static final String CREATED_TIMESTAMP = "created_timestamp";
	private static final String UPDATED_USER_ID = "updated_user_id";
	private static final String UPDATED_TIMESTAMP = "updated_timestamp";
	private static final String TRANSACTION_BY = "transaction_by";

	private static final IncomeExpenseEntityRowMapper INCOME_EXPENSE_ENTITY_ROW_MAPPER = new IncomeExpenseEntityRowMapper();
	private static final IncomeExpenseCategoryRowMapper INCOME_EXPENSE_CATEGORY_ROW_MAPPER = new IncomeExpenseCategoryRowMapper();

	private static final StaffRowMapper STAFF_ROW_MAPPER = new StaffRowMapper();
	private static final Gson GSON = new Gson();

	@Override
	public IncomeExpenseTransactionDetails mapRow(ResultSet rs, int rowNum) throws SQLException {

		final IncomeExpenseEntity incomeExpenseEntity = INCOME_EXPENSE_ENTITY_ROW_MAPPER.mapRow(rs, rowNum);
		final IncomeExpenseCategory incomeExpenseCategory = INCOME_EXPENSE_CATEGORY_ROW_MAPPER.mapRow(rs, rowNum);
		Staff transactionByStaff = STAFF_ROW_MAPPER.mapRow(rs, rowNum);

		final Timestamp transactionDate = rs.getTimestamp(TRANSACTION_DATE) == null ? null
				: rs.getTimestamp(TRANSACTION_DATE);
		final Integer transactionDateTime = transactionDate == null ? null : (int) (transactionDate.getTime() / 1000l);
		final Timestamp createdTimestamp = rs.getTimestamp(CREATED_TIMESTAMP) == null ? null
				: rs.getTimestamp(CREATED_TIMESTAMP);
		final Integer createdTime = createdTimestamp == null ? null : (int) (createdTimestamp.getTime() / 1000l);
		final Timestamp updatedTimestamp = rs.getTimestamp(UPDATED_TIMESTAMP) == null ? null
				: rs.getTimestamp(UPDATED_TIMESTAMP);
		final Integer updatedTime = updatedTimestamp == null ? null : (int) (updatedTimestamp.getTime() / 1000l);
		final String transactionDocument = rs.getString(TRANSACTION_DOCUMENTS);
		Document<IncomeExpenseTransactionDocumentType> incomeExpenseTransactionDocument = null;
		if (!StringUtils.isBlank(transactionDocument)) {
			final Type collectionType = new TypeToken<Document<IncomeExpenseTransactionDocumentType>>() {
			}.getType();
			incomeExpenseTransactionDocument = GSON.fromJson(transactionDocument, collectionType);
		}
		return new IncomeExpenseTransactionDetails(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID),
				UUID.fromString(rs.getString(TRANSACTION_ID)), rs.getString(TRANSACTION_TITLE),
				TransactionMode.getTransactionMode(rs.getString(TRANSACTION_MODE)), rs.getDouble(AMOUNT),
				transactionDateTime, incomeExpenseCategory, incomeExpenseEntity, rs.getBoolean(ADD_TO_TOTAL),
				rs.getString(TRANSACTION_DESCRIPTION), incomeExpenseTransactionDocument,
				IncomeExpenseTransactionStatus.getIncomeExpenseTransactionStatus(rs.getString(TRANSACTION_STATUS)),
				IncomeExpenseTransactionType.getTransactionType(rs.getString(TRANSACTION_TYPE)), createdTime,
				rs.getString(CREATED_USER_ID) == null ? null : UUID.fromString(rs.getString(CREATED_USER_ID)),
				rs.getString(UPDATED_USER_ID) == null ? null : UUID.fromString(rs.getString(UPDATED_USER_ID)),
				updatedTime, rs.getString(TRANSACTION_BY) == null ? null : UUID.fromString(rs.getString(TRANSACTION_BY)),
				transactionByStaff);

	}
}
