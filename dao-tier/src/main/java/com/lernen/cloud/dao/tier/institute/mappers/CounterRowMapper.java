package com.lernen.cloud.dao.tier.institute.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.common.CounterType;
import com.lernen.cloud.core.api.institute.CounterData;

public class CounterRowMapper implements RowMapper<CounterData> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String COUNTER_TYPE = "counter_type";
	private static final String COUNT = "count";
	private static final String COUNTER_PREFIX = "counter_prefix";

	@Override
	public CounterData mapRow(ResultSet rs, int rowNum) throws SQLException {
		return new CounterData(rs.getInt(INSTITUTE_ID), CounterType.valueOf(rs.getString(COUNTER_TYPE)),
				rs.getInt(COUNT), rs.getString(COUNTER_PREFIX));
	}

}
