package com.lernen.cloud.dao.tier.institute.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.institute.StandardRowDetails;

public class StandardRowDetailsWithCountRowMapper implements RowMapper<StandardRowDetails> {

	private static final String STUDENT_COUNT = "student_count";
	private static final StandardRowDetailsRowMapper STANDARD_ROW_DETAILS_ROW_MAPPER = new StandardRowDetailsRowMapper();

	@Override
	public StandardRowDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
		final StandardRowDetails standardRowDetails = STANDARD_ROW_DETAILS_ROW_MAPPER.mapRow(rs, rowNum);
		final Integer studentCount = rs.getInt(STUDENT_COUNT);
		standardRowDetails.setStudentCount(studentCount);
		return standardRowDetails;
	}

}
