package com.lernen.cloud.dao.tier.institute.mappers;

import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.institute.StandardRowDetails;
import com.lernen.cloud.core.api.institute.StandardSessionDocumentType;
import com.lernen.cloud.core.api.institute.StandardWithStaffDetails;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffLite;
import com.lernen.cloud.core.api.student.StudentDocumentType;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.dao.tier.staff.mappers.StaffRowMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

public class StandardRowDetailsWithStaffRowMapper implements RowMapper<StandardWithStaffDetails> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String ACADEMIC_SESSION_ID = "academic_session_id";
	private static final String CLASS_TEACHER_STAFF_ID = "class_teacher_staff_id";
	private static final String STANDARD_SESSION_DOCUMENTS = "standard_session_documents";
	private static final StaffRowMapper STAFF_ROW_MAPPER = new StaffRowMapper();
	private static final StandardRowDetailsRowMapper STANDARD_ROW_DETAILS_ROW_MAPPER = new StandardRowDetailsRowMapper();

	@Override
	public StandardWithStaffDetails mapRow(ResultSet rs, int rowNum) throws SQLException {

		final StandardRowDetails standardRowDetails = STANDARD_ROW_DETAILS_ROW_MAPPER.mapRow(rs, rowNum);

		final Staff staff = STAFF_ROW_MAPPER.mapRow(rs, rowNum);
		StaffLite staffLite = Staff.getStaffLite(staff);

		final String documents = rs.getString(STANDARD_SESSION_DOCUMENTS);
		List<Document<StandardSessionDocumentType>> standardSessionDocuments = null;
		if (!StringUtils.isBlank(documents)) {
			final Type collectionType = new TypeToken<List<Document<StandardSessionDocumentType>>>() {
			}.getType();
			standardSessionDocuments = GSON.fromJson(documents, collectionType);
		}

		return new StandardWithStaffDetails(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID),
				standardRowDetails, staffLite, standardSessionDocuments);
	}

}
