/**
 * 
 */
package com.lernen.cloud.dao.tier.institute.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.institute.StandardSections;

/**
 * <AUTHOR>
 *
 */
public class StandardSectionDetailsRowMapper implements RowMapper<StandardSections>{

	private static final String SECTION_ID = "standard_section_mapping.section_id";
	private static final String SECTION_NAME = "standard_section_mapping.section_name";
	
	@Override
	public StandardSections mapRow(ResultSet rs, int rowNum) throws SQLException {
		// TODO Auto-generated method stub
		return new StandardSections(rs.getInt(SECTION_ID), rs.getString(SECTION_NAME));
	}

}
