package com.lernen.cloud.dao.tier.institute.mappers;

import com.lernen.cloud.core.api.institute.*;
import com.lernen.cloud.core.api.user.Gender;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;
import com.lernen.cloud.core.api.institute.StandardSectionGenderCount;
import com.lernen.cloud.core.api.institute.StandardSectionGenderCountRow;
import com.lernen.cloud.core.api.institute.Stream;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

public class StandardSectionGenderCountRowMapper implements RowMapper<StandardSectionGenderCountRow> {
	private static final String INSTITUTE_ID = "institute_id";
	private static final String ACADEMIC_SESSION_ID = "academic_session_id";
	private static final String STANDARD_ID = "standard_id";
	private static final String STANDARD_NAME = "standard_name";
	private static final String STREAM = "stream";
	private static final String LEVEL = "level";
	private static final String SECTION_ID = "section_id";
	private static final String SECTION_NAME = "section_name";
	private static final String STUDENT_COUNT = "student_count";
	private static final String GENDER = "gender";
	private static final String GENDER_COUNT = "gender_count";

	@Override
	public StandardSectionGenderCountRow mapRow(ResultSet rs, int rowNum) throws SQLException {
		if (rs.getString(STANDARD_ID) == null) {
			return null;
		}
		return new StandardSectionGenderCountRow(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID),
				UUID.fromString(rs.getString(STANDARD_ID)), Stream.getStream(rs.getString(STREAM)), rs.getString(STANDARD_NAME),
				rs.getInt(LEVEL), rs.getInt(SECTION_ID), rs.getString(SECTION_NAME), rs.getInt(STUDENT_COUNT),
				Gender.getGender(rs.getString(GENDER)), rs.getInt(GENDER_COUNT));

	}

	public static StandardSectionGenderCount getStandardSectionGenderCountResponse(List<StandardSectionGenderCountRow> standardSectionGenderCountRowList) {
		if (CollectionUtils.isEmpty(standardSectionGenderCountRowList)) {
			return null;
		}

		final Map<Integer, List<StandardSectionGenderCountRow>> standardSectionGenderCountRowMap = new HashMap<>();

		for (final StandardSectionGenderCountRow standardRowDetails : standardSectionGenderCountRowList) {
			final Integer sectionId = standardRowDetails.getSectionId();
			if (!standardSectionGenderCountRowMap.containsKey(sectionId)) {
				standardSectionGenderCountRowMap.put(sectionId, new ArrayList<>());
			}
			standardSectionGenderCountRowMap.get(sectionId).add(standardRowDetails);
		}

		final StandardSectionGenderCountRow firstRow = standardSectionGenderCountRowList.get(0);
		final List<SectionGenderCount> standardSectionList = new ArrayList<>();
		int totalStudentCount = 0;
		for (Map.Entry<Integer, List<StandardSectionGenderCountRow>> entry : standardSectionGenderCountRowMap.entrySet()) {
			final Integer sectionId = entry.getKey();
			String sectionName = entry.getValue().get(0).getSectionName();
			int studentCount = 0;
			List<GenderCount> genderList = new ArrayList<>();
			List<StandardSectionGenderCountRow> sectionGenderCountList = entry.getValue();
			for (StandardSectionGenderCountRow sectiondetails : sectionGenderCountList) {
				studentCount = sectiondetails.getStudentCount();
				genderList.add(new GenderCount(sectiondetails.getGender(), sectiondetails.getGenderCount()));
			}
			totalStudentCount += studentCount;
			standardSectionList.add(new SectionGenderCount(sectionId,
					sectionName, genderList, studentCount));
		}

		return new StandardSectionGenderCount(firstRow.getInstituteId(), firstRow.getAcademicSessionId(), firstRow.getStandardId(),
				firstRow.getStandardName(), firstRow.getStream(), firstRow.getLevel(), standardSectionList, totalStudentCount);
	}

	public static List<StandardSectionGenderCount> getStandardSectionGenderCountList(List<StandardSectionGenderCountRow> StandardSectionwiseCountRowDetailList) {
		final List<StandardSectionGenderCount> standardSectionGenderCountList = new ArrayList<>();
		if (CollectionUtils.isEmpty(StandardSectionwiseCountRowDetailList)) {
			return standardSectionGenderCountList;
		}

		final Map<UUID, List<StandardSectionGenderCountRow>> standardIdVsStandardRowDetailsMap = new HashMap<>();
		for (final StandardSectionGenderCountRow standardRowDetail : StandardSectionwiseCountRowDetailList) {
			if (!standardIdVsStandardRowDetailsMap.containsKey(standardRowDetail.getStandardId())) {
				final List<StandardSectionGenderCountRow> standardDetailedRows = new ArrayList<>();
				standardDetailedRows.add(standardRowDetail);
				standardIdVsStandardRowDetailsMap.put(standardRowDetail.getStandardId(), standardDetailedRows);
				continue;
			}
			standardIdVsStandardRowDetailsMap.get(standardRowDetail.getStandardId()).add(standardRowDetail);
		}

		final Iterator<Map.Entry<UUID, List<StandardSectionGenderCountRow>>> standardIdVsStandardDetailedRowMapItr = standardIdVsStandardRowDetailsMap.entrySet().iterator();
		while (standardIdVsStandardDetailedRowMapItr.hasNext()) {
			final Map.Entry<UUID, List<StandardSectionGenderCountRow>> entry = standardIdVsStandardDetailedRowMapItr.next();
			List<StandardSectionGenderCountRow> standardRowDetailsListArg = entry.getValue();

			final StandardSectionGenderCount standardResponse = getStandardSectionGenderCountResponse(standardRowDetailsListArg);
			standardSectionGenderCountList.add(standardResponse);
		}
		Collections.sort(standardSectionGenderCountList);
		return standardSectionGenderCountList;
	}

}
