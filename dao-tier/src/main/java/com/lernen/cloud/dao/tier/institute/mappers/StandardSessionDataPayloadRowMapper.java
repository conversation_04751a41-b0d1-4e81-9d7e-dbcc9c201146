package com.lernen.cloud.dao.tier.institute.mappers;

import com.embrate.cloud.core.api.application.mobile.StandardAppVersion;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.institute.StandardRowDetails;
import com.lernen.cloud.core.api.institute.StandardSessionDataPayload;
import com.lernen.cloud.core.api.institute.StandardSessionDocumentType;
import com.lernen.cloud.core.api.institute.StandardWithStaffDetails;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffLite;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.dao.tier.staff.mappers.StaffRowMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.UUID;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

public class StandardSessionDataPayloadRowMapper implements RowMapper<StandardSessionDataPayload> {

	private static final String INSTITUTE_ID = "standard_session_data.institute_id";
	private static final String ACADEMIC_SESSION_ID = "standard_session_data.academic_session_id";
	private static final String STANDARD_ID = "standard_session_data.standard_id";
	private static final String SECTION_ID = "standard_session_data.section_id";
	private static final String CLASS_TEACHER_STAFF_ID = "standard_session_data.class_teacher_staff_id";
	private static final String STANDARD_SESSION_DOCUMENTS = "standard_session_data.standard_session_documents";

	@Override
	public StandardSessionDataPayload mapRow(ResultSet rs, int rowNum) throws SQLException {
		if(rs.getString(STANDARD_ID) == null) {
			return null;
		}

		final String documents = rs.getString(STANDARD_SESSION_DOCUMENTS);
		List<Document<StandardSessionDocumentType>> standardSessionDocuments = null;
		if (!StringUtils.isBlank(documents)) {
			final Type collectionType = new TypeToken<List<Document<StandardSessionDocumentType>>>() {
			}.getType();
			standardSessionDocuments = GSON.fromJson(documents, collectionType);
		}

		return new StandardSessionDataPayload(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID),
				UUID.fromString(rs.getString(STANDARD_ID)), rs.getInt(SECTION_ID),
				rs.getString(CLASS_TEACHER_STAFF_ID) == null ? null : UUID.fromString(rs.getString(CLASS_TEACHER_STAFF_ID)),
				standardSessionDocuments);
	}

}
