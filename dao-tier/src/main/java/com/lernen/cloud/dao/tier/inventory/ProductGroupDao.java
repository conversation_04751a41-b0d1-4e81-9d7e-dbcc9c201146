package com.lernen.cloud.dao.tier.inventory;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.inventory.ProductGroupBasicInfo;
import com.lernen.cloud.core.api.inventory.ProductGroupData;
import com.lernen.cloud.core.api.inventory.ProductGroupItem;
import com.lernen.cloud.core.api.inventory.ProductGroupPayload;
import com.lernen.cloud.dao.tier.inventory.mappers.ProductGroupBasicInfoRowMapper;
import com.lernen.cloud.dao.tier.inventory.mappers.ProductGroupRowMapper;

/**
 *
 * <AUTHOR>
 *
 */
public class ProductGroupDao {

	private static final ProductGroupBasicInfoRowMapper PRODUCT_GROUP_ROW_DETAILS_ROW_MAPPER = new ProductGroupBasicInfoRowMapper();
	private static final ProductGroupRowMapper PRODUCT_GROUP_ROW_MAPPER = new ProductGroupRowMapper();

	private static final String GET_PRODUCT_GROUP_BY_GROUP_ID_INSTITUTE_ID = "select * from product_group where institute_id = ? and group_id = ?";

	private static final String ADD_PRODUCT_GROUP = "insert into product_group(institute_id, group_id, group_name, discount)"
			+ "values(?, ?, ?, ?)";
	private static final String ADD_PRODUCT_GROUP_SKU_MAPPING = "insert into product_group_sku_mapping(group_id, sku_id, quantity, discount)"
			+ "values(?, ?, ?, ?)";
	private static final String DELETE_PRODUCT_GROUP_BY_GROUP_ID_AND_INSTITUTE_ID = "delete from product_group where institute_id = ? and group_id = ?";
	private static final String DELETE_PRODUCT_GROUP_SKU_MAPPING_BY_GROUP_ID = "delete from product_group_sku_mapping where group_id = ?";

	private static final String UPDATE_PRODUCT_GROUP = "update product_group set group_name = ? , discount = ? where institute_id = ? and group_id = ?";
	private static final String GET_PRODUCT_GROUP_DETAILS_BY_GROUP_ID = "select product_group.*, products.*, brands.*, brand_category_mapping.*, "
			+ "categories.*, colors.*, product_group_sku_mapping.* from product_group join product_group_sku_mapping on product_group.group_id = "
			+ "product_group_sku_mapping.group_id join products on product_group_sku_mapping.sku_id = products.sku_id join categories on "
			+ "products.category_id = categories.category_id join brands on products.brand_id = brands.brand_id join brand_category_mapping "
			+ "on brand_category_mapping.brand_id = brands.brand_id and products.category_id = brand_category_mapping.category_id left join "
			+ "colors on colors.color_id = products.color_id where product_group.institute_id = ? and product_group.group_id = ?";
	private static final String GET_PRODUCT_GROUP_DETAILS_BY_GROUP_IDs = "select product_group.*, products.*, brands.*, brand_category_mapping.*, "
			+ "categories.*, colors.*, product_group_sku_mapping.* from product_group join product_group_sku_mapping on product_group.group_id = "
			+ "product_group_sku_mapping.group_id join products on product_group_sku_mapping.sku_id = products.sku_id join categories on "
			+ "products.category_id = categories.category_id join brands on products.brand_id = brands.brand_id join brand_category_mapping on "
			+ "brand_category_mapping.brand_id = brands.brand_id and products.category_id = brand_category_mapping.category_id left join colors "
			+ "on colors.color_id = products.color_id where product_group.institute_id = ? and product_group.group_id in (%s)";

	private static final String SEARCH_PRODUCT_GROUP_DETAILS_BY_INSTITUTE_ID = "select product_group.*, products.*, brands.*, brand_category_mapping.*, "
			+ "categories.*, colors.*, product_group_sku_mapping.* from product_group join product_group_sku_mapping on product_group.group_id = "
			+ "product_group_sku_mapping.group_id join products on product_group_sku_mapping.sku_id = products.sku_id join categories on "
			+ "products.category_id = categories.category_id join brands on products.brand_id = brands.brand_id join brand_category_mapping on "
			+ "brand_category_mapping.brand_id = brands.brand_id and products.category_id = brand_category_mapping.category_id left join colors "
			+ "on colors.color_id = products.color_id where product_group.institute_id = ? and product_group.group_name like ? order by "
			+ "product_group_sku_mapping.updated desc, product_group.updated desc";

	private final JdbcTemplate jdbcTemplate;
	private final TransactionTemplate transactionTemplate;

	public ProductGroupDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
		this.jdbcTemplate = jdbcTemplate;
		this.transactionTemplate = transactionTemplate;
	}

	public UUID saveProductGroup(ProductGroupPayload productGroup, boolean isUpdate) {
		try {
			final UUID productGroupId = transactionTemplate.execute(new TransactionCallback<UUID>() {

				@Override
				public UUID doInTransaction(TransactionStatus status) {
					final UUID productGroupUUID = saveProductGroups(productGroup, isUpdate);
					if (productGroupUUID == null) {
						throw new RuntimeException("Transaction could not update all entities in desired way");
					}
					return productGroupUUID;
				}
			});
			return productGroupId;
		} catch (final Exception e) {
			System.out.println("Unable to execute transaction");
			e.printStackTrace();
		}
		return null;
	}

	private UUID saveProductGroups(ProductGroupPayload productGroup, boolean isUpdate) {
		UUID productGroupId = null;
		if (productGroup.getProductGroupBasicInfo().getGroupId() != null) {
			productGroupId = productGroup.getProductGroupBasicInfo().getGroupId();
		} else {
			productGroupId = UUID.randomUUID();
		}

		int row = 0;
		if (isUpdate) {
			row = jdbcTemplate.update(UPDATE_PRODUCT_GROUP, productGroup.getProductGroupBasicInfo().getGroupName(),
					productGroup.getProductGroupBasicInfo().getDiscount() == null ? 0
							: productGroup.getProductGroupBasicInfo().getDiscount(),
					productGroup.getProductGroupBasicInfo().getInstituteId(), productGroupId.toString());
		} else {
			row = jdbcTemplate.update(ADD_PRODUCT_GROUP, productGroup.getProductGroupBasicInfo().getInstituteId(),
					productGroupId.toString(), productGroup.getProductGroupBasicInfo().getGroupName(),
					productGroup.getProductGroupBasicInfo().getDiscount() == null ? 0
							: productGroup.getProductGroupBasicInfo().getDiscount());
		}
		if ((row != 1) || CollectionUtils.isEmpty(productGroup.getProductGroupItems())) {
			throw new RuntimeException("Unable to add product group. Check if group item list exist");
		}

		final List<Object[]> argsList = new ArrayList<>();
		for (final ProductGroupItem productGroupItem : productGroup.getProductGroupItems()) {
			final List<Object> args = new ArrayList<>();
			args.add(productGroupId.toString());
			args.add(productGroupItem.getSkuId().toString());
			args.add(productGroupItem.getQuantity());
			args.add(productGroupItem.getDiscount() == null ? 0 : productGroupItem.getDiscount());
			argsList.add(args.toArray());
		}
		final int[] rows = jdbcTemplate.batchUpdate(ADD_PRODUCT_GROUP_SKU_MAPPING, argsList);
		if (rows.length != productGroup.getProductGroupItems().size()) {
			throw new RuntimeException("Unable to add product group. Check if sku list exist");
		}

		for (final int rowCount : rows) {
			if (rowCount != 1) {
				throw new RuntimeException("Unable to add product group item.");
			}
		}
		return productGroupId;
	}

	public boolean deleteProductGroup(int instituteId, String groupId) {
		final Object[] args = { instituteId, groupId };
		try {
			deleteProductGroupSkuMapping(groupId);
			return jdbcTemplate.update(DELETE_PRODUCT_GROUP_BY_GROUP_ID_AND_INSTITUTE_ID, args) == 1;
		} catch (final DataAccessException dataAccessException) {
			System.out.println(
					"Could not delete product group with group id : " + groupId + " and institute id : " + instituteId);
		} catch (final Exception e) {
			System.out.println("Exception" + e.getMessage());
		}
		return false;
	}

	public ProductGroupBasicInfo getGroupByGroupIdInstituteId(int instituteId, String groupId) {
		final Object[] args = { instituteId, groupId };
		return jdbcTemplate.queryForObject(GET_PRODUCT_GROUP_BY_GROUP_ID_INSTITUTE_ID, args,
				PRODUCT_GROUP_ROW_DETAILS_ROW_MAPPER);
	}

	public int deleteProductGroupSkuMapping(String groupId) {
		return jdbcTemplate.update(DELETE_PRODUCT_GROUP_SKU_MAPPING_BY_GROUP_ID, groupId);
	}

	public ProductGroupData getProductGroupData(int instituteId, UUID groupId) {
		try {
			final Object[] args = { instituteId, groupId.toString() };
			return ProductGroupRowMapper.getProductGroupData(
					jdbcTemplate.query(GET_PRODUCT_GROUP_DETAILS_BY_GROUP_ID, args, PRODUCT_GROUP_ROW_MAPPER));
		} catch (final DataAccessException dataAccessException) {
			System.out.println(dataAccessException);
			dataAccessException.printStackTrace();
		} catch (final Exception e) {
			System.out.println("Exception" + e.getMessage());
		}

		return null;
	}

	public List<ProductGroupData> getProductGroupData(int instituteId, List<UUID> groupIds) {

		try {
			final List<Object> args = new ArrayList<>();
			args.add(instituteId);
			final StringBuilder params = new StringBuilder();
			boolean first = true;
			for (final UUID groupId : groupIds) {
				args.add(groupId.toString());
				if (first) {
					params.append("?");
					first = false;
					continue;
				}
				params.append(", ?");
			}
			return ProductGroupRowMapper.getProductGroupDataList(
					jdbcTemplate.query(String.format(GET_PRODUCT_GROUP_DETAILS_BY_GROUP_IDs, params.toString()),
							args.toArray(), PRODUCT_GROUP_ROW_MAPPER));
		} catch (final DataAccessException dataAccessException) {
			System.out.println(dataAccessException);
			dataAccessException.printStackTrace();
		} catch (final Exception e) {
			System.out.println("Exception" + e.getMessage());
		}

		return null;
	}

	public List<ProductGroupData> searchProductGroupDataList(int instituteId, String searchText) {
		try {
			final Object[] args = { instituteId, "%" + searchText + "%" };
			return ProductGroupRowMapper.getProductGroupDataList(
					jdbcTemplate.query(SEARCH_PRODUCT_GROUP_DETAILS_BY_INSTITUTE_ID, args, PRODUCT_GROUP_ROW_MAPPER));
		} catch (final DataAccessException dataAccessException) {
			System.out.println(dataAccessException);
			dataAccessException.printStackTrace();
		} catch (final Exception e) {
			System.out.println("Exception" + e.getMessage());
		}

		return null;
	}

	public UUID updateProductGroup(ProductGroupPayload productGroup) {

		try {
			final UUID productGroupId = transactionTemplate.execute(new TransactionCallback<UUID>() {
				@Override
				public UUID doInTransaction(TransactionStatus status) {
					final int row = deleteProductGroupSkuMapping(
							productGroup.getProductGroupBasicInfo().getGroupId().toString());
					if (row > 0) {
						return saveProductGroup(productGroup, true);
					} else {
						throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION,
								"Group with group id : "
										+ productGroup.getProductGroupBasicInfo().getGroupId().toString()
										+ " does not exist."));
					}
				}
			});
			return productGroupId;
		} catch (final Exception e) {
			System.out.println("Unable to execute transaction");
			e.printStackTrace();
		}
		return null;
	}
}
