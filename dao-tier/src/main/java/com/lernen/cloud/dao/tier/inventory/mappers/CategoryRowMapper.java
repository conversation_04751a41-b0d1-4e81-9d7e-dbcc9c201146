package com.lernen.cloud.dao.tier.inventory.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.inventory.Category;

public class CategoryRowMapper implements RowMapper<Category> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String CATEGORY_ID = "category_id";
	private static final String CATEGORY_NAME = "category_name";
	private static final String USER_GROUPS = "user_groups";
	private static final String COLORS = "colors";
	private static final String SIZES = "sizes";
	private static final String GENDERS = "genders";
	private static final String DESCRIPTION = "description";

	public Category mapRow(ResultSet rs, int rowNum) throws SQLException {
		return new Category(rs.getInt(INSTITUTE_ID), rs.getInt(CATEGORY_ID), rs.getString(CATEGORY_NAME),
				rs.getBoolean(USER_GROUPS), rs.getBoolean(COLORS), rs.getBoolean(SIZES), rs.getBoolean(GENDERS),
				rs.getString(DESCRIPTION));
	}
}
