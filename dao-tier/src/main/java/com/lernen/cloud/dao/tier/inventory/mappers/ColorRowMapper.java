package com.lernen.cloud.dao.tier.inventory.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import com.lernen.cloud.core.api.inventory.Color;

public class ColorRowMapper implements RowMapper<Color> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String COLOR_ID = "color_id";
	private static final String COLOR_NAME = "color_name";

	public Color mapRow(ResultSet rs, int rowNum) throws SQLException {
		if (rs.getInt(COLOR_ID) <= 0) {
			return null;
		}
		return new Color(rs.getInt(COLOR_ID), rs.getInt(INSTITUTE_ID), rs.getString(COLOR_NAME));
	}

}
