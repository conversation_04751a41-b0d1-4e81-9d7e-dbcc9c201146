package com.lernen.cloud.dao.tier.inventory.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.inventory.ProductGroupBasicInfo;
/**
 * 
 * <AUTHOR>
 *
 */
public class ProductGroupBasicInfoRowMapper implements RowMapper<ProductGroupBasicInfo> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String GROUP_ID = "group_id";
	private static final String GROUP_NAME = "group_name";
	private static final String DISCOUNT = "discount";
	
	@Override
	public ProductGroupBasicInfo mapRow(ResultSet rs, int rowNum) throws SQLException {
		return new ProductGroupBasicInfo(rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(GROUP_ID)),
				rs.getString(GROUP_NAME), rs.getDouble(DISCOUNT));
	}
}
