package com.lernen.cloud.dao.tier.inventory.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.inventory.ProductDataQuantity;
import com.lernen.cloud.core.api.inventory.ProductDetails;
import com.lernen.cloud.core.api.inventory.ProductGroupBasicInfo;
import com.lernen.cloud.core.api.inventory.ProductGroupData;
import com.lernen.cloud.core.api.inventory.ProductGroupDetailedRow;

/**
 * 
 * <AUTHOR>
 *
 */

public class ProductGroupRowMapper implements RowMapper<ProductGroupDetailedRow> {

	private static final ProductMapper PRODUCT_MAPPER = new ProductMapper();
	private static final ProductGroupBasicInfoRowMapper PRODUCT_GROUP_BASIC_INFO_ROW_MAPPER = new ProductGroupBasicInfoRowMapper();
	private static final String QUANTITY = "product_group_sku_mapping.quantity";
	private static final String ITEM_DISCOUNT = "product_group_sku_mapping.discount";

	@Override
	public ProductGroupDetailedRow mapRow(ResultSet rs, int rowNum) throws SQLException {

		ProductGroupBasicInfo productGroupBasicInfo = PRODUCT_GROUP_BASIC_INFO_ROW_MAPPER.mapRow(rs, rowNum);
		ProductDetails productRow = PRODUCT_MAPPER.mapRow(rs, rowNum);
		return new ProductGroupDetailedRow(productGroupBasicInfo, productRow, rs.getDouble(QUANTITY),
				rs.getDouble(ITEM_DISCOUNT));
	}

	/**
	 * This method assumes that all elements passed in list belong to same
	 * product group
	 * 
	 * @return
	 */
	public static ProductGroupData getProductGroupData(List<ProductGroupDetailedRow> productGroupDetailedRowList) {
		if (CollectionUtils.isEmpty(productGroupDetailedRowList)) {
			return null;
		}
		ProductGroupDetailedRow firstRow = productGroupDetailedRowList.get(0);

		List<ProductDataQuantity> productDataQuantityList = new LinkedList<>();

		for (ProductGroupDetailedRow productGroupDetailedRow : productGroupDetailedRowList) {
			ProductDataQuantity productDataQuantity = new ProductDataQuantity(
					productGroupDetailedRow.getProductDetails(), productGroupDetailedRow.getQuantity(),
					productGroupDetailedRow.getDiscount());
			productDataQuantityList.add(productDataQuantity);
		}
		return new ProductGroupData(firstRow.getProductGroupBasicInfo(), productDataQuantityList);
	}

	public static List<ProductGroupData> getProductGroupDataList(
			List<ProductGroupDetailedRow> productGroupDetailedRowList) {
		List<ProductGroupData> productGroupDataList = new LinkedList<>();
		if (CollectionUtils.isEmpty(productGroupDetailedRowList)) {
			return productGroupDataList;
		}
		Map<UUID, List<ProductGroupDetailedRow>> groupIdVsProductGroupDetailedRowList = new LinkedHashMap<>();
		for (ProductGroupDetailedRow productGroupDetailedRow : productGroupDetailedRowList) {
			if (groupIdVsProductGroupDetailedRowList
					.containsKey(productGroupDetailedRow.getProductGroupBasicInfo().getGroupId())) {
				groupIdVsProductGroupDetailedRowList
						.get(productGroupDetailedRow.getProductGroupBasicInfo().getGroupId())
						.add(productGroupDetailedRow);
			} else {
				List<ProductGroupDetailedRow> productGroupDetailedRows = new LinkedList<>();
				productGroupDetailedRows.add(productGroupDetailedRow);
				groupIdVsProductGroupDetailedRowList
						.put(productGroupDetailedRow.getProductGroupBasicInfo().getGroupId(), productGroupDetailedRows);
			}
		}

		Iterator<Map.Entry<UUID, List<ProductGroupDetailedRow>>> groupIdVsProductGroupDetailedRowListItr = groupIdVsProductGroupDetailedRowList
				.entrySet().iterator();
		while (groupIdVsProductGroupDetailedRowListItr.hasNext()) {
			Map.Entry<UUID, List<ProductGroupDetailedRow>> entry = groupIdVsProductGroupDetailedRowListItr.next();
			ProductGroupData productGroupData = getProductGroupData(entry.getValue());
			if (productGroupData != null) {
				productGroupDataList.add(productGroupData);
			}
		}

		return productGroupDataList;
	}

}
