package com.lernen.cloud.dao.tier.inventory.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.inventory.ProductData;
import com.lernen.cloud.core.api.inventory.UserGroup;
import com.lernen.cloud.core.api.user.Gender;

/**
 * 
 * <AUTHOR>
 *
 */
public class ProductRowMapper implements RowMapper<ProductData> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String ID = "id";
	private static final String SKU = "sku";
	private static final String SKU_ID = "sku_id";
	private static final String GENERAL_NAME = "general_name";
	private static final String CATEGORY_ID = "category_id";
	private static final String BRAND_ID = "brand_id";
	private static final String SIZE = "size";
	private static final String COLOR_ID = "color_id";
	private static final String USER_GROUP = "user_group";
	private static final String GENDER = "gender";
	private static final String TOTAL_QUANTITY = "total_quantity";
	private static final String INITIAL_QUANTITY = "initial_quantity";
	private static final String INITIAL_QUANTITY_AS_OF = "initial_quantity_as_of";
	private static final String SELLING_PRICE = "selling_price";
	private static final String IN_USE = "in_use";
	private static final String DISCOUNT = "discount";
	private static final String DESCRIPTION = "description";

	public ProductData mapRow(ResultSet rs, int rowNum) throws SQLException {

		Integer initialQuantityAsOf = rs.getTimestamp(INITIAL_QUANTITY_AS_OF) == null ? null
				: (int) (rs.getTimestamp(INITIAL_QUANTITY_AS_OF).getTime() / 1000l);

		return new ProductData(rs.getInt(INSTITUTE_ID), rs.getString(SKU), UUID.fromString(rs.getString(SKU_ID)),
				UUID.fromString(rs.getString(ID)), rs.getString(GENERAL_NAME), rs.getString(SIZE),
				UserGroup.getUserGroup(rs.getString(USER_GROUP)), Gender.getGender(rs.getString(GENDER)),
				rs.getDouble(TOTAL_QUANTITY), rs.getDouble(INITIAL_QUANTITY), initialQuantityAsOf,
				rs.getObject(SELLING_PRICE) == null ? null : rs.getDouble(SELLING_PRICE),
				rs.getBoolean(IN_USE), rs.getDouble(DISCOUNT), rs.getString(DESCRIPTION),
				rs.getInt(CATEGORY_ID), UUID.fromString(rs.getString(BRAND_ID)), rs.getInt(COLOR_ID));

	}

}
