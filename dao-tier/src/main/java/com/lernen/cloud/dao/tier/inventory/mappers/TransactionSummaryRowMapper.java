package com.lernen.cloud.dao.tier.inventory.mappers;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.Map.Entry;

import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.inventory.InventoryTransactionStatus;
import com.lernen.cloud.core.api.inventory.InventoryTransactionType;
import com.lernen.cloud.core.api.inventory.InventoryUserType;
import com.lernen.cloud.core.api.inventory.PaymentStatus;
import com.lernen.cloud.core.api.inventory.PurchasedProductSummary;
import com.lernen.cloud.core.api.inventory.TransactionRowDetails;
import com.lernen.cloud.core.api.inventory.TransactionSummary;
import com.lernen.cloud.core.api.inventory.Vendor;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;
import com.lernen.cloud.dao.tier.student.mappers.StudentRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.StudentWithoutSessionRowMapper;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;
import static com.lernen.cloud.dao.tier.inventory.mappers.TransactionRowMapper.*;

/**
 *
 * <AUTHOR>
 *
 */
public class TransactionSummaryRowMapper implements RowMapper<TransactionRowDetails> {

	protected static final String TRANSACTION_BY_NAME = "transaction_by_name";
	private static final VendorRowMapper VENDOR_ROW_MAPPER = new VendorRowMapper();
	private static final StudentWithoutSessionRowMapper STUDENT_WITHOUT_SESSION_ROW_MAPPER = new StudentWithoutSessionRowMapper();

	@Override
	public TransactionRowDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
		final InventoryUserType inventoryUserType = InventoryUserType
				.valueOf(rs.getString(TransactionRowMapper.INVENTORY_USER_TYPE));
		String transactionToName = null;
		Vendor vendor = null;
		StudentLite studentLite = null;
		if (inventoryUserType == InventoryUserType.SELLER) {
			vendor = VENDOR_ROW_MAPPER.mapRow(rs, rowNum);
		} else if (inventoryUserType == InventoryUserType.STUDENT) {
			studentLite = Student
					.getStudentLite(StudentRowMapper.getStudent(STUDENT_WITHOUT_SESSION_ROW_MAPPER.mapRow(rs, rowNum)));
		} else {
			transactionToName = rs.getString(TransactionRowMapper.TRANSACTION_TO);
		}


		final Type metaDataMap = new TypeToken<Map<String, Object>>() {
		}.getType();
		Map<String, Object> metaData = new HashMap<>();
		if (!StringUtils.isBlank(rs.getString(TransactionRowMapper.META_DATA))) {
			metaData = GSON.fromJson(rs.getString(TransactionRowMapper.META_DATA), metaDataMap);
		}

		return new TransactionRowDetails(rs.getInt(TransactionRowMapper.INSTITUTE_ID),
				UUID.fromString(rs.getString(TransactionRowMapper.TRANSACTION_ID)),
				rs.getString(TransactionRowMapper.REFERENCE),
				CryptoUtils.decrypt(rs.getString(TransactionRowMapper.EMAIL)),
				UUID.fromString(rs.getString(TransactionRowMapper.SKU_ID)),
				rs.getString(TransactionRowMapper.PRODUCT_NAME),
				InventoryTransactionType.valueOf(rs.getString(TransactionRowMapper.TRANSACTION_TYPE)),
				inventoryUserType, rs.getDouble(TransactionRowMapper.QUANTITY),
				rs.getDouble(TransactionRowMapper.TOTAL_PRICE), rs.getDouble(TransactionRowMapper.DISCOUNT),
				rs.getDouble(TransactionRowMapper.TAX), rs.getDouble(TransactionRowMapper.INITITAL_QUANTITY),
				rs.getDouble(TransactionRowMapper.FINAL_QUANTITY), rs.getString(TransactionRowMapper.TRANSACTION_TO),
				rs.getTimestamp(TransactionRowMapper.TRANSACTION_DATE).getTime(),
				rs.getTimestamp(TransactionRowMapper.TRANSACTION_ADDED_TIME).getTime(),
				rs.getString(TransactionRowMapper.TRANSACTION_BY),
				PaymentStatus.valueOf(rs.getString(TransactionRowMapper.PAYMENT_STATUS)),
				rs.getString(TransactionRowMapper.DESCRIPTION), rs.getDouble(TransactionRowMapper.ADDITIONAL_COST),
				rs.getDouble(TransactionRowMapper.ADDITIONAL_DISCOUNT), transactionToName,
				CryptoUtils.decrypt(rs.getString(TRANSACTION_BY_NAME)), vendor,
				TransactionMode.valueOf(rs.getString(TransactionRowMapper.TRANSACTION_MODE)),
				InventoryTransactionStatus.valueOf(rs.getString(TransactionRowMapper.TRANSACTION_STATUS)), studentLite, rs.getDouble(WALLET_DEBIT_AMOUNT), rs.getDouble(WALLET_BASED_CREDIT_AMOUNT), rs.getDouble(PAID_AMOUNT), metaData);

	}

	/**
	 * This method assumes that all elements passed in list belong to same
	 * transactionID
	 *
	 * @return
	 */
	public static TransactionSummary getTransactionSummary(List<TransactionRowDetails> transactionRowDetailsList) {
		if (CollectionUtils.isEmpty(transactionRowDetailsList)) {
			return null;
		}
		final TransactionRowDetails firstRow = transactionRowDetailsList.get(0);

		final List<PurchasedProductSummary> purchasedProducts = new ArrayList<PurchasedProductSummary>();

		for (final TransactionRowDetails transactionRowDetails : transactionRowDetailsList) {
			purchasedProducts.add(new PurchasedProductSummary(transactionRowDetails.getSkuId(),
					transactionRowDetails.getProductName(), transactionRowDetails.getQuantity(),
					transactionRowDetails.getTotalPrice(), transactionRowDetails.getTotalDiscount(),
					transactionRowDetails.getTotalTax(), transactionRowDetails.getInitialQuantity(),
					transactionRowDetails.getFinalQuantity()));
		}
		return new TransactionSummary(firstRow.getTransactionId(), firstRow.getInstituteId(),
				firstRow.getTransactionType(), firstRow.getInventoryUserType(), UUID.fromString(firstRow.getTransactionBy()), 
				firstRow.getTransactionByName(), firstRow.getTransactionToName(), firstRow.getEmail(), 
				firstRow.getReference(), firstRow.getVendor(),
				firstRow.getStudentLite(), firstRow.getTransactionDate(), firstRow.getTransactionAddedAt(),
				firstRow.getPaymentStatus(), firstRow.getDescription(), firstRow.getAdditionalCost(),
				firstRow.getAdditionalDiscount(), firstRow.getTransactionMode(),
				firstRow.getInventoryTransactionStatus(), firstRow.getUsedWalletAmount(), firstRow.getWalletCreditAmount(), firstRow.getPaidAmount(), firstRow.getMetadata(), purchasedProducts);
	}

	public static List<TransactionSummary> getTransactionSummaryList(List<TransactionRowDetails> transactionRowDetailsList) {
		if(CollectionUtils.isEmpty(transactionRowDetailsList)) {
			return null;
		}
		
		Map<UUID, List<TransactionRowDetails>> transactionRowDetailsListMap = new HashMap<UUID, List<TransactionRowDetails>>();
		for(TransactionRowDetails transactionRowDetails : transactionRowDetailsList) {
			if(transactionRowDetailsListMap.containsKey(transactionRowDetails.getTransactionId())) {
				transactionRowDetailsListMap.get(transactionRowDetails.getTransactionId()).add(transactionRowDetails);
			} else {
				List<TransactionRowDetails> addTransactionRowDetailsList = new ArrayList<TransactionRowDetails>();
				addTransactionRowDetailsList.add(transactionRowDetails);
				transactionRowDetailsListMap.put(transactionRowDetails.getTransactionId(), addTransactionRowDetailsList);
			}
		}
		
		List<TransactionSummary> transactionSummaryList = new ArrayList<TransactionSummary>();
		for(Entry<UUID, List<TransactionRowDetails>> transactionRowDetailsListEntrySet : transactionRowDetailsListMap.entrySet()) {
			transactionSummaryList.add(getTransactionSummary(transactionRowDetailsListEntrySet.getValue()));
			
		}
		return transactionSummaryList;
	}

}
