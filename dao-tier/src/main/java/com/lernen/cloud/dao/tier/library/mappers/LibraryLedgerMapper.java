package com.lernen.cloud.dao.tier.library.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.library.IssueStatus;
import com.lernen.cloud.core.api.library.LibraryLedgerPayload;
import com.lernen.cloud.core.api.user.UserType;

/**
 * 
 * <AUTHOR>
 *
 */
public class LibraryLedgerMapper implements RowMapper<LibraryLedgerPayload> {

	private static final String INSTITUTE_ID = "library_ledger.institute_id";
	private static final String ACADEMIC_SESSION_ID = "library_ledger.academic_session_id";
	private static final String TRANSACTION_ID = "library_ledger.transaction_id";
	private static final String USER_ID = "library_ledger.user_id";
	private static final String USER_TYPE = "library_ledger.user_type";
	private static final String BOOK_ID = "library_ledger.book_id";
	private static final String ACCESSION_ID = "library_ledger.accession_id";
	private static final String DURATION = "library_ledger.duration";
	private static final String ISSUED_AT = "library_ledger.issued_timestamp";
	private static final String RETURNED_AT = "library_ledger.returned_timestamp";
	private static final String ISSUED_BY = "library_ledger.issued_by";
	private static final String RECEIVED_BY = "library_ledger.received_by";
	private static final String STATUS = "library_ledger.status";

	public LibraryLedgerPayload mapRow(ResultSet rs, int rowNum) throws SQLException {

		return new LibraryLedgerPayload(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID), UUID.fromString(rs.getString(TRANSACTION_ID)), 
				UUID.fromString(rs.getString(USER_ID)), UserType.valueOf(rs.getString(USER_TYPE).toUpperCase()), UUID.fromString(rs.getString(BOOK_ID)),
				UUID.fromString(rs.getString(ACCESSION_ID)), rs.getInt(DURATION),(int) (rs.getTimestamp(ISSUED_AT).getTime() / 1000l),UUID.fromString(rs.getString(ISSUED_BY)),
				rs.getTimestamp(RETURNED_AT) != null ? (int) (rs.getTimestamp(RETURNED_AT).getTime() / 1000l) : null, 
				rs.getString(RECEIVED_BY) != null ? UUID.fromString(rs.getString(RECEIVED_BY)) : null, IssueStatus.getStatusFromString(rs.getString(STATUS)));
	}

}
