package com.lernen.cloud.dao.tier.notification;

import com.embrate.cloud.core.api.service.communication.CommunicationServiceTransaction;
import com.embrate.cloud.core.api.service.communication.CommunicationServiceTransactionType;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.common.*;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.institute.CounterData;
import com.lernen.cloud.core.api.notification.*;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.communication.service.CommunicationServiceUtils;
import com.lernen.cloud.dao.tier.institute.InstituteDao;
import com.lernen.cloud.dao.tier.notification.mappers.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;

/**
 *
 * <AUTHOR>
 *
 */
public class NotificationStatusDao {
	private static final Logger logger = LogManager.getLogger(NotificationStatusDao.class);
	private static final Gson GSON = new Gson();
	private final JdbcTemplate jdbcTemplate;
	private final InstituteDao instituteDao;
	private final TransactionTemplate transactionTemplate;

	private static final NotificationDetailsRowMapper NOTIFICATION_DETAILS_ROW_MAPPER = new NotificationDetailsRowMapper();
	private static final StudentFollowUpNotificationStatusRowMapper STUDENT_FOLLOW_UP_NOTIFICATION_STATUS_ROW_MAPPER = new StudentFollowUpNotificationStatusRowMapper();
	private static final StudentNotificationDetailsRowMapper STUDENT_NOTIFICATION_DETAILS_ROW_MAPPER = new StudentNotificationDetailsRowMapper();
	private static final StaffNotificationDetailsRowMapper STAFF_NOTIFICATION_DETAILS_ROW_MAPPER = new StaffNotificationDetailsRowMapper();
	private static final BatchNotificationEntryRowMapper BATCH_NOTIFICATION_ENTRY_ROW_MAPPER = new BatchNotificationEntryRowMapper();
	private static final CommunicationServiceTransactionRowMapper COMMUNICATION_SERVICE_TRANSACTION_ROW_MAPPER = new CommunicationServiceTransactionRowMapper();

	private static final String GET_NOTIFICATION_DETAILS = "select * from notification_status where institute_id = ? and academic_session_id = ? "
			+ " and notification_type = ? and delivery_mode = ? order by generation desc";

	private static final String GET_NOTIFICATION_DETAILS_BY_USER_ID = "select * from notification_status where institute_id = ? and user_id = ? and user_type = 'STUDENT' and notification_type = ? ";

	private static final String GET_STUDENT_NOTIFICATION_DETAILS_BY_USER_ID = "select  * from students " +
			"inner join notification_status on students.student_id = notification_status.user_id and notification_status.user_type = 'STUDENT' and notification_status.notification_type = ? ";


	private static final String GET_UNKNOWN_NOTIFICATION_IN_RANGE = "select * from notification_status where service_provider in %s and "
			+ "delivery_mode = ? and status in ('GENERATED', 'SENT') and generation >= ? and generation <= ? order by generation";

	private static final String GET_BATCH_NOTIFICATION_DETAILS_WITH_PAGINATION = "select notification_status.batch_id, "
			+ "notification_status.batch_name, user_type, notification_type, batch_generation_time, status, delivered, "
			+ "notification_content, meta_data, credits_used, refunded_credits from notification_status join (select batch_id, min(generation) as batch_generation_time from "
			+ "notification_status where batch_id is not null and institute_id = ? %s "
			+ "and delivery_mode = ? %s and notification_type in %s group by batch_id order by batch_generation_time desc limit ? offset ?) "
			+ "as batch_notifications on notification_status.batch_id = batch_notifications.batch_id order by batch_notifications.batch_id";

	private static final String GET_BATCH_NOTIFICATION_COUNT = "select count(distinct(batch_id)) from "
			+ "notification_status where batch_id is not null and institute_id = ? %s "
			+ "and delivery_mode = ? %s and notification_type in %s ";


//	select batch_id, min(generation) as batch_generation_time from notification_status where batch_id is not null and institute_id = 10265 and academic_session_id = 221 and delivery_mode = 'SMS' and user_type = 'STUDENT' and notification_type in ('DUE_FEES','FEE_PAYMENT','FEE_PAYMENT_CANCELLATION','FEE_PAYMENT_CANCELLATION_ADMIN') group by batch_id order by batch_generation_time desc limit 10 offset 0
//
//	select count(*) from notification_status where batch_id is null and institute_id = 10265 and academic_session_id = 221 and delivery_mode = 'SMS' and user_type = 'STUDENT' and notification_type in ('DUE_FEES','FEE_PAYMENT','FEE_PAYMENT_CANCELLATION','FEE_PAYMENT_CANCELLATION_ADMIN');
//
//	select notification_status.*, students.*, academic_session.*, standards.*,  standard_section_mapping.*, student_academic_session_details.* from notification_status join students on notification_status.user_id =  students.student_id and notification_status.user_type = 'STUDENT' left join student_academic_session_details on students.student_id = student_academic_session_details.student_id and notification_status.academic_session_id =  student_academic_session_details.academic_session_id left join academic_session on student_academic_session_details.academic_session_id = academic_session.academic_session_id left join standards on student_academic_session_details.standard_id = standards.standard_id left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id =  student_academic_session_details.academic_session_id and  student_academic_session_details.section_id = standard_section_mapping.section_id where batch_id is null and  students.institute_id = 10265 and notification_status.academic_session_id = 221 and delivery_mode = 'SMS' and  notification_type in ('DUE_FEES','FEE_PAYMENT','FEE_PAYMENT_CANCELLATION','FEE_PAYMENT_CANCELLATION_ADMIN') order by generation desc limit 10 offset 0;

	@Deprecated
	private static final String GET_STUDENT_NOTIFICATION_DETAILS = "select notification_status.*, students.*, academic_session.*, standards.*, "
			+ " standard_section_mapping.*, student_academic_session_details.* from notification_status join students on notification_status.user_id = "
			+ " students.student_id and notification_status.user_type = 'STUDENT' left join student_academic_session_details on"
			+ " students.student_id = student_academic_session_details.student_id and notification_status.academic_session_id = "
			+ " student_academic_session_details.academic_session_id left join academic_session on"
			+ " student_academic_session_details.academic_session_id = academic_session.academic_session_id left join standards on"
			+ " student_academic_session_details.standard_id = standards.standard_id left join standard_section_mapping on"
			+ " standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = "
			+ " student_academic_session_details.academic_session_id and "
			+ " student_academic_session_details.section_id = standard_section_mapping.section_id where"
			+ " students.institute_id = ? and notification_status.academic_session_id = ? and delivery_mode = ? and "
			+ " notification_type in %s order by generation desc limit ? offset ?";

	private static final String GET_STUDENT_NOTIFICATION_DETAILS_BY_BATCH_ID = "select notification_status.*, students.*, academic_session.*, standards.*, "
			+ " standard_section_mapping.*, student_academic_session_details.* from notification_status join students on notification_status.user_id = "
			+ " students.student_id and notification_status.user_type = 'STUDENT' left join student_academic_session_details on"
			+ " students.student_id = student_academic_session_details.student_id and notification_status.academic_session_id = "
			+ " student_academic_session_details.academic_session_id left join academic_session on"
			+ " student_academic_session_details.academic_session_id = academic_session.academic_session_id left join standards on"
			+ " student_academic_session_details.standard_id = standards.standard_id left join standard_section_mapping on"
			+ " standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = "
			+ " student_academic_session_details.academic_session_id and "
			+ " student_academic_session_details.section_id = standard_section_mapping.section_id where "
			+ " students.institute_id = ? and batch_id = ? order by generation";

	private static final String GET_INDIVIDUAL_STUDENT_NOTIFICATION_DETAILS = "select notification_status.*, students.*, academic_session.*, standards.*, "
			+ " standard_section_mapping.*, student_academic_session_details.* from notification_status join students on notification_status.user_id = "
			+ " students.student_id and notification_status.user_type = 'STUDENT' left join student_academic_session_details on"
			+ " students.student_id = student_academic_session_details.student_id and notification_status.academic_session_id = "
			+ " student_academic_session_details.academic_session_id left join academic_session on"
			+ " student_academic_session_details.academic_session_id = academic_session.academic_session_id left join standards on"
			+ " student_academic_session_details.standard_id = standards.standard_id left join standard_section_mapping on"
			+ " standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = "
			+ " student_academic_session_details.academic_session_id and "
			+ " student_academic_session_details.section_id = standard_section_mapping.section_id where batch_id is null and "
			+ " students.institute_id = ? and notification_status.academic_session_id = ? and delivery_mode = ? and "
			+ " notification_type in %s order by generation desc limit ? offset ?";



	private static final String GET_STUDENT_INDIVIDUAL_NOTIFICATION_COUNT = "select count(*) from notification_status "
			+ "where batch_id is null and institute_id = ? and academic_session_id = ? "
			+ "and delivery_mode = ? and user_type = 'STUDENT' and notification_type in %s ";



	@Deprecated
	private static final String GET_STAFF_NOTIFICATION_DETAILS = "select notification_status.*, staff_details.*, academic_session.* "
			+ " from notification_status join staff_details on notification_status.user_id = "
			+ " staff_details.staff_id and notification_status.user_type = 'STAFF' join academic_session on"
			+ " notification_status.academic_session_id = academic_session.academic_session_id where "
			+ " staff_details.institute_id = ? and notification_status.academic_session_id = ? and "
			+ " notification_type = ? and delivery_mode = ? order by generation desc";

	private static final String GET_STAFF_NOTIFICATION_DETAILS_BY_BATCH_ID = "select notification_status.*, staff_details.*, academic_session.* "
			+ " from notification_status join staff_details on notification_status.user_id = "
			+ " staff_details.staff_id and notification_status.user_type = 'STAFF' join academic_session on"
			+ " notification_status.academic_session_id = academic_session.academic_session_id where batch_id is not null and "
			+ " staff_details.institute_id = ? and batch_id = ? order by generation desc ";

	private static final String GET_STAFF_NOTIFICATION_DETAILS_OF_OTHER_USER_TYPE = "select * from notification_status " +
			" join academic_session on notification_status.academic_session_id = academic_session.academic_session_id " +
			" where batch_id is not null and notification_status.institute_id = ? and batch_id = ? and notification_status.user_type = 'OTHER' " +
			" order by generation desc ";

	private static final String GET_INDIVIDUAL_STAFF_NOTIFICATION_DETAILS = "select notification_status.*, staff_details.*, academic_session.* "
			+ " from notification_status join staff_details on notification_status.user_id = "
			+ " staff_details.staff_id and notification_status.user_type = 'STAFF' join academic_session on"
			+ " notification_status.academic_session_id = academic_session.academic_session_id where batch_id is null and "
			+ " staff_details.institute_id = ? and notification_status.academic_session_id = ? and "
			+ " delivery_mode = ? and notification_type in %s order by generation desc limit ? offset ?";

	private static final String GET_STAFF_INDIVIDUAL_NOTIFICATION_COUNT = "select count(*) from notification_status "
			+ "where batch_id is null and institute_id = ? and academic_session_id = ? "
			+ "and delivery_mode = ? and user_type = 'STAFF' and notification_type in %s ";

	private static final String INSERT_NOTIFICATION_STATUS = "insert into notification_status(notification_id, institute_id, user_id, user_type, "
			+ "academic_session_id, service_provider, notification_type, delivery_mode, delivery_destination, batch_id, batch_name, notification_title, "
			+ "notification_content, status, generation, external_unique_id, meta_data, credits_used, credits_transaction_id, refunded_credits) values "
			+ "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String UPDATE_NOTIFICATION_STATUS = "update notification_status set status = ?, delivered = ? "
			+ "where notification_id = ?";

	private static final String UPDATE_NOTIFICATION_STATUS_BY_EXTERNAL_ID = "update notification_status set status = ?, delivered = ? "
			+ "where external_unique_id = ? and service_provider = ?";

	private static final String UPDATE_NOTIFICATION_STATUS_WITH_REFUND_CREDITS = "update notification_status set status = ?, delivered = ?, " +
			"refunded_credits = ?, meta_data = ? where notification_id = ?";

	private static final String INSERT_COMM_SERVICE_TRANSACTIONS = " insert into communication_service_transactions (institute_id, transaction_id, "
			+ " delivery_mode, service_provider, user_type, transaction_by, transaction_add_at, amount, credit_updated, transaction_type, status, "
			+ " meta_data, description) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

	private static final String DELETE_SMS_TRANSACTIONS = " delete from communication_service_transactions where transaction_id = ? "
			+ " and institute_id = ? and delivery_mode = 'SMS' ";

	private static final String GET_SMS_TRANSACTION_DETAILS = " select * from communication_service_transactions where institute_id = ? "
			+ " and delivery_mode = 'SMS' order by transaction_add_at desc ";

	private static final String GET_SMS_TRANSACTION_DETAILS_FOR_TRANSACTION_TYPE = " select * from communication_service_transactions where institute_id = ? "
			+ " and delivery_mode = 'SMS' and transaction_type = ? order by transaction_add_at desc ";

	private static final String GET_SMS_TRANSACTION_DETAILS_BY_TRANSACTION_ID = "select * from communication_service_transactions where "
			+ " transaction_id = ? and institute_id = ? and delivery_mode = 'SMS' order by transaction_add_at desc ";

	private static final String ORDER_GENERATION_ON_CLAUSE = " order by notification_status.generation desc ";

	public NotificationStatusDao(JdbcTemplate jdbcTemplate, InstituteDao instituteDao,
			TransactionTemplate transactionTemplate) {
		this.jdbcTemplate = jdbcTemplate;
		this.instituteDao = instituteDao;
		this.transactionTemplate = transactionTemplate;
	}

	public List<NotificationDetails> getNotificationDetails(int instituteId, int academicSessionId,
			DeliveryMode deliveryMode, NotificationType notificationType) {
		try {
			return jdbcTemplate.query(GET_NOTIFICATION_DETAILS,
					new Object[] { instituteId, academicSessionId, notificationType.name(), deliveryMode.name() },
					NOTIFICATION_DETAILS_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error(
					"Error occurred while getting notification for institute {}, session {}, deliveryMode {}, notificationType {}",
					instituteId, academicSessionId, deliveryMode, notificationType, e);
		}
		return null;
	}

	public List<NotificationDetails> getNotificationDetailsByUserId(int instituteId, UUID userId,NotificationType notificationType,
																	Set<DeliveryMode> deliveryModeSet) {
		try {


			final List<Object> args = new ArrayList<Object>();
			args.add(instituteId);
			args.add(userId.toString());
			args.add(notificationType.name());
			String query = GET_NOTIFICATION_DETAILS_BY_USER_ID;
			StringBuilder inQueryBuilder = new StringBuilder();
			if(!CollectionUtils.isEmpty(deliveryModeSet)) {
				query += " and delivery_mode in %s ";

				String delimiter = "";
				inQueryBuilder.append("(");
				for (DeliveryMode deliveryMode : deliveryModeSet) {
					args.add(deliveryMode.name());
					inQueryBuilder.append(delimiter).append("?");
					delimiter = ",";
				}
				inQueryBuilder.append(")");

			}
			query += ORDER_GENERATION_ON_CLAUSE;
			return jdbcTemplate.query(String.format(query, inQueryBuilder), args.toArray(), NOTIFICATION_DETAILS_ROW_MAPPER);

		} catch (final Exception e) {
			e.printStackTrace();
			logger.error(
					"Error occurred while getting notification for institute {}",
					instituteId, e);
		}
		return null;
	}

	public List<StudentFollowUpNotificationDetails> getStudentNotificationDetailsByUserId(int instituteId,NotificationType notificationType,
																		   Set<DeliveryMode> deliveryModeSet){
		try {


			final List<Object> args = new ArrayList<Object>();
			args.add(notificationType.name());
			String query = GET_STUDENT_NOTIFICATION_DETAILS_BY_USER_ID;
			StringBuilder inQueryBuilder = new StringBuilder();
			if(!CollectionUtils.isEmpty(deliveryModeSet)) {
				query += " and notification_status.delivery_mode in %s ";

				String delimiter = "";
				inQueryBuilder.append("(");
				for (DeliveryMode deliveryMode : deliveryModeSet) {
					args.add(deliveryMode.name());
					inQueryBuilder.append(delimiter).append("?");
					delimiter = ",";
				}
				inQueryBuilder.append(")");

			}
			query += ORDER_GENERATION_ON_CLAUSE;
			return jdbcTemplate.query(String.format(query, inQueryBuilder), args.toArray(), STUDENT_FOLLOW_UP_NOTIFICATION_STATUS_ROW_MAPPER);

		} catch (final Exception e) {
			e.printStackTrace();
			logger.error(
					"Error occurred while getting student notification for institute {}",
					instituteId, e);
		}
		return null;
	}



	public List<NotificationDetails> getNotificationsInRange(
			Set<CommunicationServiceProvider> communicationServiceProviders, DeliveryMode deliveryMode,
			int generationStart, int generationEnd) {
		try {
			List<Object> args = new ArrayList<>();
			StringBuilder serviceProviders = new StringBuilder();
			String delimiter = "";
			serviceProviders.append("(");
			for (CommunicationServiceProvider communicationServiceProvider : communicationServiceProviders) {
				args.add(communicationServiceProvider.name());
				serviceProviders.append(delimiter).append(" ?");
				delimiter = ",";
			}
			serviceProviders.append(" )");
			args.add(deliveryMode.name());
			args.add(new Timestamp(generationStart * 1000l));
			args.add(new Timestamp(generationEnd * 1000l));

			return jdbcTemplate.query(String.format(GET_UNKNOWN_NOTIFICATION_IN_RANGE, serviceProviders.toString()),
					args.toArray(), NOTIFICATION_DETAILS_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error(
					"Error occurred while getting notification for communicationServiceProviders {}, deliveryMode {}, generationStart {}, generationEnd {}",
					communicationServiceProviders, deliveryMode, generationStart, generationEnd, e);
		}
		return null;
	}

	public SearchResultWithPagination<BatchNotificationEntry> getBatchNotificationEntries(int instituteId,
			int academicSessionId, DeliveryMode deliveryMode, Set<NotificationType> notificationTypes,
			UserType userType, int offset, int limit) {
		try {
			List<Object> args = new ArrayList<Object>();
			args.add(instituteId);

			String academicSessionIdInQuery = "";
			if(academicSessionId > 0) {
				academicSessionIdInQuery = " and academic_session_id = ? ";
				args.add(academicSessionId);
			}

			args.add(deliveryMode.name());

			String userTypeInQuery = "";
			if(userType != null) {
				userTypeInQuery = " and user_type = ? ";
				args.add(userType.name());
			}


			String delimiter = "";
			StringBuilder inQueryBuilder = new StringBuilder();
			inQueryBuilder.append("(");
			for (NotificationType notificationType : notificationTypes) {
				args.add(notificationType.name());
				inQueryBuilder.append(delimiter).append("?");
				delimiter = ",";
			}
			inQueryBuilder.append(")");

			String notificationTypeInQuery = inQueryBuilder.toString();
			final int totalResultCount = jdbcTemplate
					.queryForObject(String.format(GET_BATCH_NOTIFICATION_COUNT, academicSessionIdInQuery, userTypeInQuery, notificationTypeInQuery), Integer.class, args.toArray());

			args.add(limit);
			args.add(offset);

			List<BatchNotificationEntry> batchNotificationEntries = jdbcTemplate.query(
					String.format(GET_BATCH_NOTIFICATION_DETAILS_WITH_PAGINATION, academicSessionIdInQuery, userTypeInQuery, notificationTypeInQuery),
					args.toArray(), BATCH_NOTIFICATION_ENTRY_ROW_MAPPER);

			final PaginationInfo paginationInfo = new PaginationInfo(totalResultCount, limit, offset);

			return new SearchResultWithPagination<>(paginationInfo, batchNotificationEntries);

		} catch (final Exception e) {
			logger.error(
					"Error occurred while getting batch notification for institute {}, session {}, deliveryMode {}, notificationType {}",
					instituteId, academicSessionId, deliveryMode, notificationTypes, e);
		}
		return null;
	}

	public SearchResultWithPagination<StudentNotificationDetails> getStudentNotificationDetails(int instituteId,
			int academicSessionId, DeliveryMode deliveryMode, Set<NotificationType> notificationTypes, int offset,
			int limit) {
		try {
			List<Object> args = new ArrayList<Object>();
			args.add(instituteId);
			args.add(academicSessionId);
			args.add(deliveryMode.name());

			String delimiter = "";
			StringBuilder inQueryBuilder = new StringBuilder();
			inQueryBuilder.append("(");
			for (NotificationType notificationType : notificationTypes) {
				args.add(notificationType.name());
				inQueryBuilder.append(delimiter).append("?");
				delimiter = ",";
			}
			inQueryBuilder.append(")");

			String notificationTypeInQuery = inQueryBuilder.toString();

			final int totalResultCount = jdbcTemplate.queryForObject(
					String.format(GET_STUDENT_INDIVIDUAL_NOTIFICATION_COUNT, notificationTypeInQuery), Integer.class, args.toArray());

			args.add(limit);
			args.add(offset);

			List<StudentNotificationDetails> studentNotificationDetails = jdbcTemplate.query(
					String.format(GET_INDIVIDUAL_STUDENT_NOTIFICATION_DETAILS, inQueryBuilder.toString()),
					args.toArray(), STUDENT_NOTIFICATION_DETAILS_ROW_MAPPER);

			final PaginationInfo paginationInfo = new PaginationInfo(totalResultCount, limit, offset);

			return new SearchResultWithPagination<>(paginationInfo, studentNotificationDetails);

		} catch (final Exception e) {
			logger.error(
					"Error occurred while getting notifications for institute {}, session {}, deliveryMode {}, notificationType {}",
					instituteId, academicSessionId, deliveryMode, notificationTypes, e);
		}
		return null;
	}

	public List<StudentNotificationDetails> getStudentBatchNotificationDetails(int instituteId, UUID batchId) {
		try {
			List<Object> args = new ArrayList<Object>();
			args.add(instituteId);
			args.add(batchId.toString());

			return jdbcTemplate.query(GET_STUDENT_NOTIFICATION_DETAILS_BY_BATCH_ID, args.toArray(),
					STUDENT_NOTIFICATION_DETAILS_ROW_MAPPER);

		} catch (final Exception e) {
			logger.error("Error occurred while getting notifications for institute {}, batchId {} ", instituteId,
					batchId, e);
		}
		return null;
	}

	public SearchResultWithPagination<StaffNotificationDetails> getStaffNotificationDetails(int instituteId,
			int academicSessionId, DeliveryMode deliveryMode, Set<NotificationType> notificationTypes, int offset,
			int limit) {
		try {
			List<Object> args = new ArrayList<Object>();
			args.add(instituteId);
			args.add(academicSessionId);
			args.add(deliveryMode.name());

			String delimiter = "";
			StringBuilder inQueryBuilder = new StringBuilder();
			inQueryBuilder.append("(");
			for (NotificationType notificationType : notificationTypes) {
				args.add(notificationType.name());
				inQueryBuilder.append(delimiter).append("?");
				delimiter = ",";
			}
			inQueryBuilder.append(")");

			String notificationTypeInQuery = inQueryBuilder.toString();

			final int totalResultCount = jdbcTemplate.queryForObject(
					String.format(GET_STAFF_INDIVIDUAL_NOTIFICATION_COUNT, notificationTypeInQuery), Integer.class, args.toArray());

			args.add(limit);
			args.add(offset);

			List<StaffNotificationDetails> staffNotificationDetails = jdbcTemplate.query(
					String.format(GET_INDIVIDUAL_STAFF_NOTIFICATION_DETAILS, notificationTypeInQuery), args.toArray(),
					STAFF_NOTIFICATION_DETAILS_ROW_MAPPER);

			final PaginationInfo paginationInfo = new PaginationInfo(totalResultCount, limit, offset);

			return new SearchResultWithPagination<>(paginationInfo, staffNotificationDetails);

		} catch (final Exception e) {
			logger.error(
					"Error occurred while getting staff notification for institute {}, session {}, deliveryMode {}, notificationType {}",
					instituteId, academicSessionId, deliveryMode, notificationTypes, e);
		}
		return null;
	}

	public List<StaffNotificationDetails> getStaffBatchNotificationDetails(int instituteId, UUID batchId) {
		try {
			List<Object> args = new ArrayList<Object>();
			args.add(instituteId);
			args.add(batchId.toString());

			return jdbcTemplate.query(GET_STAFF_NOTIFICATION_DETAILS_BY_BATCH_ID, args.toArray(),
					STAFF_NOTIFICATION_DETAILS_ROW_MAPPER);

		} catch (final Exception e) {
			logger.error("Error occurred while getting notifications for institute {}, batchId {}", instituteId,
					batchId, e);
		}
		return null;
	}

	public List<NotificationDetails> getOtherBatchNotificationDetails(int instituteId, UUID batchId) {
		try {
			List<Object> args = new ArrayList<Object>();
			args.add(instituteId);
			args.add(batchId.toString());

			return jdbcTemplate.query(GET_STAFF_NOTIFICATION_DETAILS_OF_OTHER_USER_TYPE, args.toArray(),
					NOTIFICATION_DETAILS_ROW_MAPPER);

		} catch (final Exception e) {
			logger.error("Error occurred while getting notifications for institute {}, batchId {}", instituteId,
					batchId, e);
		}
		return null;
	}

	public boolean addNotification(NotificationDetails notificationDetails) {
		try {
			final UUID notficationId = UUID.randomUUID();
			final long currentTime = System.currentTimeMillis();
			final NotificationStatus notificationStatus = NotificationStatus.GENERATED;
			return jdbcTemplate.update(INSERT_NOTIFICATION_STATUS,
					new Object[] { notficationId.toString(), notificationDetails.getInstituteId(),
							notificationDetails.getUserId() == null ? null : notificationDetails.getUserId().toString(),
							notificationDetails.getUserType() == null ? null : notificationDetails.getUserType().name(),
							notificationDetails.getAcademicSessionId(), notificationDetails.getCommunicationServiceProvider().name(),
							notificationDetails.getNotificationType().name(),
							notificationDetails.getDeliveryMode().name(), notificationDetails.getDeliveryDestination(),
							notificationDetails.getBatchId() == null ? null
									: notificationDetails.getBatchId().toString(),
							notificationDetails.getBatchName(), notificationDetails.getNotificationTitle(),
							notificationDetails.getNotificationContent(), notificationStatus.name(),
							new Timestamp(currentTime), notificationDetails.getExternalUniqueId(),
							CollectionUtils.isEmpty(notificationDetails.getMetaData()) ? null
									: GSON.toJson(notificationDetails.getMetaData()), notificationDetails.getCreditsUsed(), notificationDetails.getCreditTransactionId() == null ? null : notificationDetails.getCreditTransactionId().toString(), notificationDetails.getRefundCredits()}) == 1;
		} catch (final Exception e) {
			logger.error("Error occurred while saving notification {}", notificationDetails, e);
		}
		return false;
	}

	public boolean updateNotification(UUID notificationId, NotificationStatus notificationStatus,
			Long deliveryTime) {
		try {
			if (notificationId == null || notificationStatus == null) {
				return false;
			}
			if (notificationStatus != NotificationStatus.DELIVERED && notificationStatus != NotificationStatus.ANSWERED) {
				deliveryTime = null;
			}

			return jdbcTemplate.update(UPDATE_NOTIFICATION_STATUS, new Object[] { notificationStatus.name(),
					deliveryTime == null ? null : new Timestamp(deliveryTime), notificationId.toString() }) == 1;
		} catch (final Exception e) {
			logger.error(
					"Error occurred while updating notification status for notificationId {}, notificationStatus {}, deliveryTime {}",
					notificationId, notificationStatus, deliveryTime, e);
		}
		return false;
	}

	public boolean updateNotification(String externalUniqueId, CommunicationServiceProvider communicationServiceProvider, NotificationStatus notificationStatus,
									  Long deliveryTime) {
		try {
			if (StringUtils.isBlank(externalUniqueId) || communicationServiceProvider == null || notificationStatus == null) {
				return false;
			}

			if (notificationStatus != NotificationStatus.DELIVERED && notificationStatus != NotificationStatus.ANSWERED) {
				deliveryTime = null;
			}

			return jdbcTemplate.update(UPDATE_NOTIFICATION_STATUS_BY_EXTERNAL_ID, new Object[] { notificationStatus.name(),
					deliveryTime == null ? null : new Timestamp(deliveryTime), externalUniqueId, communicationServiceProvider.name() }) == 1;
		} catch (final Exception e) {
			logger.error(
					"Error occurred while updating notification status for externalUniqueId {}, notificationStatus {}, deliveryTime {}, communicationServiceProvider {}",
					externalUniqueId, notificationStatus, deliveryTime, communicationServiceProvider, e);
		}
		return false;
	}

	public boolean updateNotificationWithRefund(UUID notificationId, NotificationStatus notificationStatus, Long deliveryTime,
									  Integer refundCredits, Map<String, Object> metadata) {
		try {
			if (notificationId == null || notificationStatus == null) {
				return false;
			}

			if (notificationStatus != NotificationStatus.DELIVERED && notificationStatus != NotificationStatus.ANSWERED) {
				deliveryTime = null;
			}

			String metadataJson = CollectionUtils.isEmpty(metadata) ? null : GSON.toJson(metadata);
			return jdbcTemplate.update(UPDATE_NOTIFICATION_STATUS_WITH_REFUND_CREDITS, new Object[] { notificationStatus.name(), deliveryTime == null ? null : new Timestamp(deliveryTime),
					refundCredits, metadataJson, notificationId.toString() }) == 1;
		} catch (final Exception e) {
			logger.error(
					"Error occurred while updating notification status for notificationId {}, notificationStatus {}, refundCredits {}",
					notificationId, notificationStatus, refundCredits, e);
		}
		return false;
	}



	public UUID addCommunicationServiceTransaction(int instituteId, CommunicationServiceTransaction communicationServiceTransaction) {
		try {
			final UUID transactionId = transactionTemplate.execute(new TransactionCallback<UUID>() {

				@Override
				public UUID doInTransaction(TransactionStatus status) {
					return addCommunicationServiceTransactionNonAtomic(instituteId, communicationServiceTransaction, true);
				}
			});
			return transactionId;
		} catch (final Exception e) {
			logger.error("Unable to execute sms transaction for institute {}", instituteId, e);
		}
		return null;
	}

	/**
	 * Always use within a transaction
	 * 
	 * @param instituteId
	 * @param transactionPayload
	 * @return
	 */
	public UUID addCommunicationServiceTransactionNonAtomic(int instituteId, CommunicationServiceTransaction transactionPayload,
			boolean lockCounter) {

		UUID transactionId = UUID.randomUUID();
		CounterType counterType = CommunicationServiceUtils.getCounterForService(transactionPayload.getServiceProvider());

		/**
		 * This flag dictates whether lock is already taken in parent method while doing
		 * counter validations or need to take here
		 */
		if (lockCounter) {
			final CounterData counterData = instituteDao.getCounter(instituteId, counterType, true);
			if (counterData == null) {
				logger.error(counterType + " counter is not present for institute {} ", instituteId);
				throw new EmbrateRunTimeException(counterType + " is not present for institute");
			}
		}

		boolean result = jdbcTemplate.update(INSERT_COMM_SERVICE_TRANSACTIONS,
				new Object[] { instituteId, transactionId.toString(), transactionPayload.getDeliveryMode().name(), transactionPayload.getServiceProvider().name(),
						transactionPayload.getUserType() == null ? null : transactionPayload.getUserType().name(),
						transactionPayload.getTransactionBy().toString(), new Timestamp(DateUtils.now() * 1000l),
						transactionPayload.getAmount(), transactionPayload.getCreditUpdatedCount(),
						transactionPayload.getTransactionType() == null ? null
								: transactionPayload.getTransactionType().name(),
						transactionPayload.getStatus() == null ? null : transactionPayload.getStatus().name(),
						transactionPayload.getMetaData() == null ? null
								: GSON.toJson(transactionPayload.getMetaData()),
						transactionPayload.getDescription() }) == 1;

		if (!result) {
			throw new EmbrateRunTimeException("Error while adding comm service transaction details.");
		}

		result &= instituteDao.updateCounter(instituteId, counterType,
				transactionPayload.getCreditUpdatedCount());

		if (!result) {
			throw new EmbrateRunTimeException("Error while adding " + counterType + " in counter.");
		}
		return transactionId;
	}

//	public Boolean deleteSMSTransaction(int instituteId, UUID transactionId) {
//		final Object[] args = { transactionId.toString(), instituteId };
//		try {
//			SMSTransactionPayload smsTransactionPayload = getSMSTransactionByTransactionId(instituteId, transactionId);
//			if (smsTransactionPayload.getSmsUpdatedCount() <= 0) {
//				return true;
//			}
//			final Boolean status = transactionTemplate.execute(new TransactionCallback<Boolean>() {
//				@Override
//				public Boolean doInTransaction(TransactionStatus status) {
//					boolean result = jdbcTemplate.update(DELETE_SMS_TRANSACTIONS, args) > 0;
//					if (!result) {
//						throw new RuntimeException("Error while removing sns transaction details.");
//					}	
//					result &= instituteDao.updateCounter(instituteId, CounterType.SMS_COUNTER, smsTransactionPayload.getSmsUpdatedCount() * -1);
//					if (!result) {
//						throw new RuntimeException("Error while deleting sms count in counter.");
//					}
//					return result;
//				}
//			});
//			return status;
//		} catch (final Exception e) {
//			logger.error("Error occurred while removing sms transaction details for institute {}", instituteId, e);
//		}
//		return false;
//	}

	public List<CommunicationServiceTransaction> getSMSTransactions(int instituteId) {
		try {
			return jdbcTemplate.query(GET_SMS_TRANSACTION_DETAILS, new Object[] { instituteId },
					COMMUNICATION_SERVICE_TRANSACTION_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error occurred while getting sms transaction details for institute {}", instituteId, e);
		}
		return null;
	}

	public List<CommunicationServiceTransaction> getSMSTransactions(int instituteId,
																	CommunicationServiceTransactionType transactionType) {
		try {
			return jdbcTemplate.query(GET_SMS_TRANSACTION_DETAILS_FOR_TRANSACTION_TYPE, new Object[] { instituteId , transactionType.name()},
					COMMUNICATION_SERVICE_TRANSACTION_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error occurred while getting sms transaction details for institute {}, transactionType {}", instituteId, transactionType, e);
		}
		return null;
	}

	public CommunicationServiceTransaction getSMSTransactionByTransactionId(int instituteId, UUID transactionId) {
		try {
			return jdbcTemplate.queryForObject(GET_SMS_TRANSACTION_DETAILS_BY_TRANSACTION_ID,
					new Object[] { transactionId.toString(), instituteId }, COMMUNICATION_SERVICE_TRANSACTION_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error occurred while getting sms transaction details for institute {}", instituteId, e);
		}
		return null;
	}

	public boolean addNotificationBatch(List<NotificationDetails> notificationDetailsList) {

		final List<Object[]> addNewRecordsArgs = new ArrayList<>();
		for (NotificationDetails notificationDetails : notificationDetailsList) {

			final UUID notficationId = UUID.randomUUID();
			final long currentTime = System.currentTimeMillis();
			final NotificationStatus notificationStatus = notificationDetails == null ? NotificationStatus.GENERATED
					: notificationDetails.getNotificationStatus();

			final List<Object> args = new ArrayList<>();
			args.add(notficationId.toString());
			args.add(notificationDetails.getInstituteId());
			args.add(notificationDetails.getUserId() == null ? null : notificationDetails.getUserId().toString());
			args.add(notificationDetails.getUserType() == null ? null : notificationDetails.getUserType().name());
			args.add(notificationDetails.getAcademicSessionId());
			args.add(notificationDetails.getCommunicationServiceProvider().name());
			args.add(notificationDetails.getNotificationType().name());
			args.add(notificationDetails.getDeliveryMode().name());
			args.add(notificationDetails.getDeliveryDestination());
			args.add(notificationDetails.getBatchId() == null ? null : notificationDetails.getBatchId().toString());
			args.add(notificationDetails.getBatchName());
			args.add(notificationDetails.getNotificationTitle());
			args.add(notificationDetails.getNotificationContent());
			args.add(notificationStatus.name());
			args.add(new Timestamp(currentTime));
			args.add(notificationDetails.getExternalUniqueId());
			args.add(CollectionUtils.isEmpty(notificationDetails.getMetaData()) ? null
					: GSON.toJson(notificationDetails.getMetaData()));
			args.add(notificationDetails.getCreditsUsed());
			args.add(notificationDetails.getCreditTransactionId() == null ? null : notificationDetails.getCreditTransactionId().toString());
			args.add(notificationDetails.getRefundCredits());

			addNewRecordsArgs.add(args.toArray());
		}
		try {
			final int[] rows = jdbcTemplate.batchUpdate(INSERT_NOTIFICATION_STATUS, addNewRecordsArgs);
			if (rows.length != addNewRecordsArgs.size()) {
				return false;
			}
			for (final int rowCount : rows) {
				if (rowCount != 1) {
					return false;
				}
			}
			return true;
		} catch (final Exception e) {
			logger.error("Error occurred while adding notification details", e);
		}
		return false;
	}
}