package com.lernen.cloud.dao.tier.staff;

import com.embrate.cloud.core.api.attendance.AttendanceDeviceServiceProviderType;
import com.embrate.cloud.core.api.attendance.DeviceUpdateUserData;
import com.embrate.cloud.core.api.attendance.DeviceUpdateUserIdentityData;
import com.embrate.cloud.dao.tier.utils.QueryArgumentData;
import com.embrate.cloud.dao.tier.utils.QueryUtils;
import com.embrate.cloud.dao.tier.wallet.UserWalletDao;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.common.CounterType;
import com.lernen.cloud.core.api.common.DBLockMode;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.institute.CounterData;
import com.lernen.cloud.core.api.staff.*;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.api.user.biometric.FaceData;
import com.lernen.cloud.core.api.user.biometric.FingerprintData;
import com.lernen.cloud.core.api.user.biometric.RFIDCardData;
import com.lernen.cloud.core.api.user.biometric.UserBiometricIdentificationData;
import com.lernen.cloud.dao.tier.institute.InstituteDao;
import com.lernen.cloud.dao.tier.staff.mappers.StaffCategoryRowMapper;
import com.lernen.cloud.dao.tier.staff.mappers.StaffDepartmentRowMapper;
import com.lernen.cloud.dao.tier.staff.mappers.StaffDesignationRowMapper;
import com.lernen.cloud.dao.tier.staff.mappers.StaffRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.UserBiometricIdentificationRowMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.Timestamp;
import java.util.*;

import static com.lernen.cloud.core.utils.biometric.BiometricUtils.*;

/**
 * <AUTHOR>
 *
 */
public class StaffDao {

	private static final Logger logger = LogManager.getLogger(StaffDao.class);

	private final JdbcTemplate jdbcTemplate;

	private final TransactionTemplate transactionTemplate;

	private final UserWalletDao userWalletDao;

	private final InstituteDao instituteDao;

	private static final UserBiometricIdentificationRowMapper USER_BIOMETRIC_IDENTIFICATION_ROW_MAPPER = new UserBiometricIdentificationRowMapper();

	private static final StaffRowMapper STAFF_ROW_MAPPER = new StaffRowMapper();

	private static final StaffCategoryRowMapper STAFF_CATEGORY_ROW_MAPPER = new StaffCategoryRowMapper();
	private static final StaffDepartmentRowMapper STAFF_DEPARTMENT_ROW_MAPPER = new StaffDepartmentRowMapper();
	private static final StaffDesignationRowMapper STAFF_DESIGNATION_ROW_MAPPER = new StaffDesignationRowMapper();

	private static final Gson GSON = new Gson();

	private static final String ADD_STAFF_DETAILS = "insert into staff_details(institute_id, staff_institute_id, staff_id, "
			+ " name, gender, staff_category_id, department_designation_mapping, service_start_date, "
			+ " service_end_date, date_of_birth, birth_place, category, religion, aadhar_number, pan_number, mother_tongue, "
			+ " specially_abled, bpl, permanent_address_1, permanent_address_2, present_address_1,present_address_2, "
			+ " present_city, present_state, present_country, present_zipcode, permanent_city, permanent_state, "
			+ " permanent_country, permanent_zipcode, primary_contact_number, primary_email, secondary_email, father_name, "
			+ " mother_name, alternate_number, experience, highest_qualification, background_verification, status, "
			+ " offer_acceptance_date, tentative_date_of_joining, marital_status, relieve_date, last_organization_name, "
			+ " last_organization_address, last_designation, last_job_duration, rehire, emergency_contact_number, absconding_yn, "
			+ " account_type, bank_name, account_holder_name, account_number, ifsc_code, epf_consent, esic_consent, initials, device_user_id, transport_staff,  oasis_id, national_code, nature_of_appointment,"
			+ " s_blood_group, s_blood_pressure, s_pulse, s_height, s_weight, s_date_of_physical_examination) "
			+ " values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
			+ " ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ) ";

	private static final String ONBOARD_STAFF = "update staff_details set status = ?, timing_details = ? where staff_id = ? and institute_id = ?";

	private static final String RELIEVE_STAFF = "update staff_details set status = ? where staff_id = ? and institute_id = ?";

	private static final String GET_STAFF_DETAILS_BY_STAFF_INSTITUTE_ID = "select * from staff_details where institute_id = ? and staff_institute_id = ?";

	private static final String GET_STAFF_DETAILS_BY_DEVICE_USER_ID = "select * from staff_details where device_user_id = ? %s";

	private static final String GET_STAFF_DETAILS_BY_STAFF_ID_WITHOUT_INSTITUTE = "select * from staff_details where staff_id = ?";

	private static final String GET_STAFF_DETAILS_BY_STAFF_ID = "select * from staff_details where institute_id = ? and staff_id = ?";

	private static final String GET_STAFF_DETAILS_BY_STAFF_IDS = "select * from staff_details where institute_id = ? and staff_id in %s %s";

	private static final String GET_STAFF_DETAILS_BY_STAFF_STATUS_INSTITUTE_ID = "select staff_details.* from staff_details where institute_id = ? and status = ? ";

	private static final String GET_ORGANISATION_LEVEL_STAFF_DETAILS = "select staff_details.* from staff_details LEFT JOIN users ON users.user_id = staff_details.staff_id  WHERE (staff_details.institute_id = ? AND staff_details.status = ?) OR (users.institute_scope LIKE ? AND staff_details.status = ? AND users.user_status = ?)";

	private static final String GET_STAFF_DETAILS_BY_STAFF_STATUS = "select * from staff_details where institute_id = ? %s %s ";

	private static final String GET_ONBOARDED_TRANSPORT_STAFF_DETAILS = "select * from staff_details where institute_id = ? and transport_staff = true " +
			"and status = 'ONBOARD' ";

	private static final String GET_STAFF_DETAILS_BY_INSTITUTE_ID = "select * from staff_details where institute_id = ? order by name asc";

	private static final String GET_STAFF_DETAILS_ONLY_BY_INSTITUTE_ID = "select * from staff_details where institute_id = ? ";

	private static final String UPDATE_STAFF_DOCUMENTS = "update staff_details set documents = ? where staff_id = ? and institute_id = ?";

	private static final String UPDATE_BULK_STAFF_DETAILS = "update staff_details set ";

	private static final String UPDATE_TIMING_DETAILS_CLAUSE = "timing_details = ? ";

	private static final String UPDATE_VISITING_DETAILS_CLAUSE = "visiting_hours = ? ";

	private static final String COMMA = " , ";

	private static final String SEARCH_STAFF = "select * from staff_details ";

	private static final String WHERE = " where ";

	private static final String AND = " and ";

	private static final String UPDATE_STAFF_DETAILS_WHERE_CONDITION = " where staff_id = ? ";

	private static final String WHERE_SEARCH_STAFF = " staff_details.institute_id = ? and staff_details.status = ? ";

	private static final String STAFF_SEARCH_CONDITION = " concat_ws('/', staff_details.name, staff_details.staff_institute_id) like ? ";

	private static final String STAFF_CATEGORY_CONDTION = " staff_details.staff_category_id in (%s) ";

	private static final String STAFF_CATEGORY_NAME_CONDTION = " and  staff_details.category in %s ";

	private static final String STAFF_STATUS_CONDTION = " and staff_details.status in %s ";

	private static final String STAFF_GENDER_CONDITION = " and  staff_details.gender in %s ";

	private static final String STAFF_RELIGION_CONDITION = " and staff_details.religion in %s ";

	private static final String STAFF_STATE_CONDITION = " and ( staff_details.present_state in %s or staff_details.permanent_state in %s ) ";

	private static final String ORDER_BY_STAFF_NAME_ASC = "  order by name asc ";

	private static final String UPDATE_STAFF_DETAILS = " update staff_details set staff_institute_id = ?, name = ?, gender = ?, "
			+ " staff_category_id = ?, department_designation_mapping = ?, service_start_date = ?, "
			+ " service_end_date = ?, date_of_birth = ?, birth_place = ?, category = ?, religion = ?, aadhar_number = ?, "
			+ " pan_number = ?, mother_tongue = ?, specially_abled = ?, bpl = ?, permanent_address_1 = ?, "
			+ " permanent_address_2 = ?, present_address_1 = ?, present_address_2 = ?, present_city = ?, present_state = ?, "
			+ " present_country = ?, present_zipcode = ?, permanent_city = ?, permanent_state = ?, permanent_country = ?, "
			+ " permanent_zipcode = ?, primary_contact_number = ?, primary_email = ?, secondary_email = ?, father_name = ?, "
			+ " mother_name = ?, alternate_number = ?, experience = ?, highest_qualification = ?, background_verification = ?, "
			+ " status = ?, offer_acceptance_date = ?, tentative_date_of_joining = ?, marital_status = ?, relieve_date = ?, "
			+ " last_organization_name = ?, last_organization_address = ?, last_designation = ?, last_job_duration = ?, "
			+ " rehire = ?, emergency_contact_number = ?, absconding_yn = ?, account_type = ?, bank_name = ?, "
			+ " account_holder_name = ?, account_number = ?,  ifsc_code = ?, epf_consent = ?, esic_consent = ?, "
			+ " timing_details = ?, initials = ?, "
			+ " transport_staff = ?, visiting_hours =  ?,  oasis_id = ?, national_code = ?, nature_of_appointment = ?,"
			+ " s_blood_group = ?, s_blood_pressure = ?, s_pulse = ?, s_height = ?, s_weight = ?, s_date_of_physical_examination = ? where institute_id = ? and staff_id = ? ";

	private static final String DELETE_STAFF_BY_STAFF_ID = "delete from staff_details where institute_id = ? and staff_id = ?";

	private static final String NOT_IN_USER_CLAUSE = " staff_details.staff_institute_id not in (select user_institute_id from users where institute_id = ?)";

	/*
	 * Staff Module Enhancement staff_category, staff_departments, staff_designations
	 */

	//staff_category
	private static final String ADD_STAFF_CATEGORY_DETAILS = "insert into staff_category (institute_id, "
			+ " staff_category_id, staff_category_name, created_by) values(?, ?, ?, ?)";

	private static final String GET_STAFF_CATEGORY_DETAILS_BY_NAME = " select * from staff_category "
			+ " where staff_category.institute_id = ? and staff_category.staff_category_name = ? ";

	private static final String GET_STAFF_CATEGORY_DETAILS_BY_ID = " select * from staff_category "
			+ " where staff_category.institute_id = ? and staff_category.staff_category_id = ? ";

	private static final String GET_STAFF_CATEGORY_LIST = " select * from staff_category "
			+ " where staff_category.institute_id = ? order by staff_category.staff_category_name asc ";

	private static final String UPDATE_STAFF_CATEGORY_DETAILS = " update staff_category "
			+ " set staff_category_name = ?, updated_by = ? "
			+ " where staff_category.institute_id = ? and staff_category.staff_category_id = ? ";

	private static final String DELETE_STAFF_CATEGORY_BY_ID = "delete from staff_category "
			+ " where staff_category.institute_id = ? and staff_category.staff_category_id = ? ";

	//staff_department
	private static final String ADD_STAFF_DEPARTMENT_DETAILS = "insert into staff_department (institute_id, "
			+ " staff_department_id, staff_department_name, created_by) values(?, ?, ?, ?)";

	private static final String GET_STAFF_DEPARTMENT_DETAILS_BY_NAME = " select * from staff_department "
			+ " where staff_department.institute_id = ? and staff_department.staff_department_name = ? ";

	private static final String GET_STAFF_DEPARTMENT_DETAILS_BY_ID = " select * from staff_department "
			+ " where staff_department.institute_id = ? and staff_department.staff_department_id = ? ";

	private static final String GET_STAFF_DEPARTMENT_LIST = " select * from staff_department "
			+ " where staff_department.institute_id = ? order by staff_department.staff_department_name asc ";

	private static final String UPDATE_STAFF_DEPARTMENT_DETAILS = " update staff_department "
			+ " set staff_department_name = ?, updated_by = ? "
			+ " where staff_department.institute_id = ? and staff_department.staff_department_id = ? ";

	private static final String DELETE_STAFF_DEPARTMENT_BY_ID = "delete from staff_department "
			+ " where staff_department.institute_id = ? and staff_department.staff_department_id = ? ";

	//staff_department
	private static final String ADD_STAFF_DESIGNATION_DETAILS = "insert into staff_designation (institute_id, "
			+ " staff_designation_id, staff_designation_name, created_by) values(?, ?, ?, ?)";

	private static final String GET_STAFF_DESIGNATION_DETAILS_BY_NAME = " select * from staff_designation "
			+ " where staff_designation.institute_id = ? and staff_designation.staff_designation_name = ? ";

	private static final String GET_STAFF_DESIGNATION_DETAILS_BY_ID = " select * from staff_designation "
			+ " where staff_designation.institute_id = ? and staff_designation.staff_designation_id = ? ";

	private static final String GET_STAFF_DESIGNATION_LIST = " select * from staff_designation "
			+ " where staff_designation.institute_id = ? order by staff_designation.staff_designation_name asc ";

	private static final String UPDATE_STAFF_DESIGNATION_DETAILS = " update staff_designation "
			+ " set staff_designation_name = ?, updated_by = ? "
			+ " where staff_designation.institute_id = ? and staff_designation.staff_designation_id = ? ";

	private static final String DELETE_STAFF_DESIGNATION_BY_ID = "delete from staff_designation "
			+ " where staff_designation.institute_id = ? and staff_designation.staff_designation_id = ? ";

	private static final String GET_STAFF_DETAILS_BY_CATEGORY_ID = " select * from staff_details "
			+ " where institute_id = ? and staff_category_id = ? ";

	private static final String GET_STAFF_BIOMETRIC_DATA_BY_ID = "select device_user_id, rfid_card_data, fingerprint_data, " +
			"face_data from staff_details where staff_id = ? %s ";

	private static final String UPDATE_STAFF_BIOMETRIC_DEVICE_IDENTIFICATION = "update staff_details set rfid_card_data = ?, fingerprint_data = ?, face_data = ? where staff_id = ?";

	private static final String GET_RELIGION_BY_INSTITUTE = "select distinct staff_details.religion from staff_details where staff_details.religion is "
			+ " not NULL and staff_details.religion <> \"\" and staff_details.institute_id = ? ";


	private static final String UPDATE_STAFF_DEVICE_USER_ID =  "update staff_details set device_user_id = ? where " +
			" institute_id = ? and staff_id = ?";

	public StaffDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate, UserWalletDao userWalletDao,
					InstituteDao instituteDao) {
		this.jdbcTemplate = jdbcTemplate;
		this.transactionTemplate = transactionTemplate;
		this.userWalletDao = userWalletDao;
		this.instituteDao = instituteDao;
	}

	public UUID addStaff(RegisterStaffPayload staffPaylaod, boolean staffCounter) {
		try {
			final UUID staffId = transactionTemplate.execute(new TransactionCallback<UUID>() {

				@Override
				public UUID doInTransaction(TransactionStatus status) {
					final UUID staffUUID = addStaffDetails(staffPaylaod, staffCounter);
					if (staffUUID == null) {
						throw new RuntimeException("Unable to create staff");
					}
					return staffUUID;
				}
			});
			return staffId;
		} catch (final Exception e) {
			logger.error("Unable to add staff for payload {} ", staffPaylaod, e);
		}
		return null;
	}

	private UUID addStaffDetails(RegisterStaffPayload staffPaylaod, boolean staffCounter) {
		UUID staffId = staffPaylaod.getStaffId() == null ? UUID.randomUUID() : staffPaylaod.getStaffId();
		staffPaylaod.setStaffStatus(staffPaylaod.getStaffStatus() == null ? StaffStatus.JOINER : staffPaylaod.getStaffStatus());
		try {

			String staffInstituteId = staffPaylaod.getStaffBasicInfo().getStaffInstituteId();
			final CounterType counterType = CounterType.STAFF_NUMBER;
			if (staffCounter) {
				staffInstituteId = getStaffNumber(staffInstituteId, counterType,
						staffPaylaod.getInstituteId(), staffCounter);
			}

			final int row = jdbcTemplate.update(ADD_STAFF_DETAILS, staffPaylaod.getInstituteId(),
					staffInstituteId.trim(), staffId.toString(),
					staffPaylaod.getStaffBasicInfo().getName(),
					staffPaylaod.getStaffBasicInfo().getGender().name(),
					staffPaylaod.getStaffBasicInfo().getStaffCategoryId().toString(),
					GSON.toJson(staffPaylaod.getStaffBasicInfo().getDepartmentDesignationMapping()),
					(staffPaylaod.getServiceStartDate() != null) && (staffPaylaod.getServiceStartDate() > 0)
					? new Timestamp(staffPaylaod.getServiceStartDate() * 1000l) : null,
					(staffPaylaod.getServiceEndDate() != null) && (staffPaylaod.getServiceEndDate() > 0)
							? new Timestamp(staffPaylaod.getServiceEndDate() * 1000l) : null,
					(staffPaylaod.getStaffBasicInfo().getDateOfBirth() != null)
					&& (staffPaylaod.getStaffBasicInfo().getDateOfBirth() > 0)
					? new Timestamp(staffPaylaod.getStaffBasicInfo().getDateOfBirth() * 1000l) : null,
					staffPaylaod.getStaffBasicInfo().getBirthPlace(), staffPaylaod.getStaffBasicInfo().getCategory() == null
					? null : staffPaylaod.getStaffBasicInfo().getCategory().name(), staffPaylaod.getStaffBasicInfo().getReligion(),
					staffPaylaod.getStaffBasicInfo().getAadharNumber(),
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getPanNumber(),
					staffPaylaod.getStaffBasicInfo().getMotherTongue(),
					staffPaylaod.getStaffBasicInfo().getSpeciallyAbled(), staffPaylaod.getStaffBasicInfo().getBpl(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPermanentAddress1(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPermanentAddress2(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPresentAddress1(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPresentAddress2(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPresentCity(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPresentState(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPresentCountry(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPresentZipcode(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPermanentCity(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPermanentState(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPermanentCountry(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPermanentZipcode(),
					staffPaylaod.getStaffBasicInfo().getPrimaryContactNumber(),
					staffPaylaod.getStaffBasicInfo().getPrimaryEmail(),
					staffPaylaod.getStaffBasicInfo().getSecondaryEmail(),
					staffPaylaod.getStaffBasicInfo().getFatherName(), staffPaylaod.getStaffBasicInfo().getMotherName(),
					staffPaylaod.getStaffBasicInfo().getAlternateContactNumber(),
					staffPaylaod.getStaffQualificationExprienceInfo() == null ? null : staffPaylaod.getStaffQualificationExprienceInfo().getExperience(),
					staffPaylaod.getStaffQualificationExprienceInfo() == null ? null : staffPaylaod.getStaffQualificationExprienceInfo().getHighestQualification(),
					staffPaylaod.getStaffJoiningInfo() == null ? null : staffPaylaod.getStaffJoiningInfo().getBackgroundVerification(),
					staffPaylaod.getStaffStatus().name(), staffPaylaod.getStaffJoiningInfo() == null ? null : staffPaylaod.getStaffJoiningInfo().getOfferAcceptanceDate(),
					staffPaylaod.getStaffJoiningInfo() == null ? null : staffPaylaod.getStaffJoiningInfo().getTentativeDateOfJoining(),
					staffPaylaod.getStaffBasicInfo().getMaritalStatus() == null ? null :
					staffPaylaod.getStaffBasicInfo().getMaritalStatus().name(), null,
					staffPaylaod.getStaffQualificationExprienceInfo() == null ? null : staffPaylaod.getStaffQualificationExprienceInfo().getLastOrganizationName(),
					staffPaylaod.getStaffQualificationExprienceInfo() == null ? null : staffPaylaod.getStaffQualificationExprienceInfo().getLastOrganizationAddress(),
					staffPaylaod.getStaffQualificationExprienceInfo() == null ? null : staffPaylaod.getStaffQualificationExprienceInfo().getLastDesignation(),
					staffPaylaod.getStaffQualificationExprienceInfo() == null ? null : staffPaylaod.getStaffQualificationExprienceInfo().getLastJobDuration(), null,
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getEmergancyContactNumber(), null,
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getAccountType()  == null ? null :
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getAccountType().name(),
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getBankName(),
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getAccountHolderName(),
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getAccountNumber(),
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getIfscCode(),
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getEpfConsentType() == null
							? null : staffPaylaod.getStaffBankInfo().getEpfConsentType().name(),
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getEsicConsentType() == null
							? null : staffPaylaod.getStaffBankInfo().getEsicConsentType().name(),
					staffPaylaod.getStaffBasicInfo().getInitials(), null, staffPaylaod.isTransportStaff(), staffPaylaod.getStaffBasicInfo().getOasisId() == null ? null : staffPaylaod.getStaffBasicInfo().getOasisId(),
					staffPaylaod.getStaffBasicInfo().getNationalCode() == null ? null : staffPaylaod.getStaffBasicInfo().getNationalCode(),
					staffPaylaod.getStaffBasicInfo().getStaffNatureOfAppointment() == null ? null : staffPaylaod.getStaffBasicInfo().getStaffNatureOfAppointment().name(),
					staffPaylaod.getStaffMedicalInfo() == null ? null : staffPaylaod.getStaffMedicalInfo().getBloodGroup() == null ? null : staffPaylaod.getStaffMedicalInfo().getBloodGroup().name(),
					staffPaylaod.getStaffMedicalInfo() == null ? null : staffPaylaod.getStaffMedicalInfo().getBloodPressure(),
					staffPaylaod.getStaffMedicalInfo() == null ? null : staffPaylaod.getStaffMedicalInfo().getPulse(),
					staffPaylaod.getStaffMedicalInfo() == null ? null : staffPaylaod.getStaffMedicalInfo().getHeight(),
					staffPaylaod.getStaffMedicalInfo() == null ? null : staffPaylaod.getStaffMedicalInfo().getWeight(),
					staffPaylaod.getStaffMedicalInfo() == null ? null : staffPaylaod.getStaffMedicalInfo().getDateOfPhysicalExamination() == null || staffPaylaod.getStaffMedicalInfo().getDateOfPhysicalExamination() <= 0 ? null : new Timestamp(staffPaylaod.getStaffMedicalInfo().getDateOfPhysicalExamination() * 1000l));
//			return row == 1 ? staffId : null;

			if (row != 1) {
				return null;
			}

			if (staffCounter
					&& !instituteDao.incrementCounter(staffPaylaod.getInstituteId(), counterType)) {
				logger.error(
						"Error while updating counter for staff with staffInstituteId {}, for student {}, institute {}, staffCounter {}",
						staffInstituteId, staffId, staffPaylaod.getInstituteId(), staffCounter);
				throw new RuntimeException(
						"Unable to increment registration counter for institute " + staffPaylaod.getInstituteId());
			}
			return staffId;
		} catch (final Exception e) {
			e.printStackTrace();
			logger.error("Exception while inserting staff details for institute {}, staffPaylaod {}",
					staffPaylaod.getInstituteId(), staffPaylaod, e);
		}
		return null;
	}

	private String getStaffNumber(String staffInstituteId, CounterType counterType,
								  int instituteId, boolean staffCounter) {
		if (!staffCounter) {
			return staffInstituteId;
		}

		final CounterData counterData = instituteDao.getCounter(instituteId, counterType, true);
		if (counterData == null) {
			throw new EmbrateRunTimeException(
					counterType.name() + " counter is not present for institute " + instituteId);
		}
		return counterData.getFullCounterValue();
	}

	public boolean onboardStaff(UUID staffId, int instituteId,
								StaffTimingDetails staffTimingDetails) {
		try {
			//Conversion from hrs to min
			if(staffTimingDetails != null) {
				staffTimingDetails.setHalfDayDuration(
						staffTimingDetails.getHalfDayDuration() == null ?
								null : staffTimingDetails.getHalfDayDuration() * 60);
				staffTimingDetails.setFullDayDuration(
						staffTimingDetails.getFullDayDuration() == null ?
								null : staffTimingDetails.getFullDayDuration() * 60);
			}
			final Object[] args = { StaffStatus.ONBOARD.name(),
					staffTimingDetails == null ? null : GSON.toJson(staffTimingDetails),
					staffId.toString(), instituteId };
			return jdbcTemplate.update(ONBOARD_STAFF, args) == 1;
		} catch (final Exception e) {
			e.printStackTrace();
			logger.error("Exception while onboarding staff {}, instituteId {}", staffId, instituteId, e);
		}
		return false;
	}

	public boolean relieveStaff(UUID staffId, int instituteId) {
		try {
			final Object[] args = { StaffStatus.RELIEVED.name(), staffId.toString(),
					instituteId };
			return jdbcTemplate.update(RELIEVE_STAFF, args) == 1;
		} catch (final Exception e) {
			logger.error("Exception while relieving staff {}, instituteId {}", staffId, instituteId, e);
		}
		return false;
	}

	public List<Staff> getStaff(int instituteId, StaffStatus staffStatus, String includeUserStatus, UserStatus userStatus, boolean organisationStaffData) {
		try {
			List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(staffStatus.name());
			StringBuilder query = new StringBuilder();
			if(!organisationStaffData){
				query.append(GET_STAFF_DETAILS_BY_STAFF_STATUS_INSTITUTE_ID);
				if (includeUserStatus != null) {
					if (includeUserStatus.equals("USER_PAGE")) {
						args.add(instituteId);
						query.append(AND).append(NOT_IN_USER_CLAUSE);
					}
				}
			}else{
				query.append(GET_ORGANISATION_LEVEL_STAFF_DETAILS);
				args.add("%" + instituteId + "%");
				args.add(staffStatus.name());
				args.add(userStatus.name());
			}
			
			query.append(ORDER_BY_STAFF_NAME_ASC);
			
			String finalQuery = query.toString();
			return jdbcTemplate.query(finalQuery, args.toArray(), STAFF_ROW_MAPPER);

		} catch (final Exception e) {
			logger.error("Exception while getting staff for instituteId {}, staffStatus {}", instituteId, staffStatus,
					e);
		}
		return null;
	}

	public List<Staff> getStaff(int instituteId, List<StaffStatus> staffStatusSet) {
		try {
			List<Object> args = new ArrayList<>();
			args.add(instituteId);

			StringBuilder inQuery = new StringBuilder();

			String delimiter = "";
			if(!CollectionUtils.isEmpty(staffStatusSet)){
				inQuery.append(" and status in ( ");
				for(StaffStatus staffStatus : staffStatusSet){
					args.add(staffStatus.name());
					inQuery.append(delimiter).append(" ? ");
					delimiter = ",";
				}
				inQuery.append(")");
			}

			return jdbcTemplate.query(String.format(GET_STAFF_DETAILS_BY_STAFF_STATUS, inQuery,ORDER_BY_STAFF_NAME_ASC), args.toArray(), STAFF_ROW_MAPPER);

		} catch (final Exception e) {
			logger.error("Exception while getting staff for instituteId {}, staffStatus {}", instituteId, staffStatusSet,e);
		}
		return null;
	}

	public List<Staff> getOnboardedTransportStaff(int instituteId) {
		try {
			return jdbcTemplate.query(GET_ONBOARDED_TRANSPORT_STAFF_DETAILS, new Object[]{instituteId}, STAFF_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting transport staff for instituteId {} ", instituteId,e);
		}
		return null;
	}

	public List<Staff> searchStaffDetailsWithFilter(int instituteId,
	StaffFilterationCriteria stafffilterationCriteria){
		try{
			final StringBuilder query = new StringBuilder();
			query.append(GET_STAFF_DETAILS_ONLY_BY_INSTITUTE_ID);
			final List<Object> args = new ArrayList<>();
			args.add(instituteId);

			//Filteration of the staff on the basis of the staff status logic

			if(stafffilterationCriteria.getStaffStatus() != null){
				final StringBuilder inQuery = new StringBuilder();
				inQuery.append("(");
				boolean first = true;
				List<StaffStatus> staffStatusArray = stafffilterationCriteria.getStaffStatus();
				for(final StaffStatus staffStatus : staffStatusArray){
					args.add(staffStatus.name());
					if (first) {
						inQuery.append("?");
						first = false;
						continue;
					}
					inQuery.append(", ?");
				}
				inQuery.append(")");
				query.append(String.format(STAFF_STATUS_CONDTION, inQuery.toString()));
			}

			//Filteration of the staff on the basis of the staff user category logic

			if (stafffilterationCriteria.getUserCategory() != null) {
				final StringBuilder inQuery = new StringBuilder();
				inQuery.append("(");
				boolean first = true;
				List<UserCategory> categoryArray = stafffilterationCriteria.getUserCategory();
				for (final UserCategory category : categoryArray) {
					args.add(category.name());
					if (first) {
						inQuery.append("?");
						first = false;
						continue;
					}
					inQuery.append(", ?");
				}
				inQuery.append(")");
				query.append(String.format(STAFF_CATEGORY_NAME_CONDTION, inQuery.toString()));
			}

			//Filteration of the staff on the basis of the staff gender logic

			if (stafffilterationCriteria.getGender() != null) {
				final StringBuilder inQuery = new StringBuilder();
				inQuery.append("(");
				boolean first = true;
				List<Gender> genderArray = stafffilterationCriteria.getGender();
				for (final Gender gender : genderArray) {
					args.add(gender.name());
					if (first) {
						inQuery.append("?");
						first = false;
						continue;
					}
					inQuery.append(", ?");
				}
				inQuery.append(")");
				query.append(String.format(STAFF_GENDER_CONDITION, inQuery.toString()));
			}

			//Filteration of the staff on the basis of the staff religion logic

			if (stafffilterationCriteria.getReligion() != null) {
				final StringBuilder inQuery = new StringBuilder();
				inQuery.append("(");
				boolean first = true;
				List<String> religionArray = stafffilterationCriteria.getReligion();
				for (final String religion : religionArray) {
					args.add(religion);
					if (first) {
						inQuery.append("?");
						first = false;
						continue;
					}
					inQuery.append(", ?");
				}
				inQuery.append(")");
				query.append(String.format(STAFF_RELIGION_CONDITION, inQuery.toString()));
			}

			//Filteration of the staff on the basis of the staff state logic

			if (stafffilterationCriteria.getState() != null) {
				final StringBuilder presentInQuery = new StringBuilder();
				presentInQuery.append("(");
				boolean first = true;
				List<String> stateArray = stafffilterationCriteria.getState();
				for (final String state : stateArray) {
					args.add(state);
					if (first) {
						presentInQuery.append("?");
						first = false;
						continue;
					}
					presentInQuery.append(", ?");
				}
				presentInQuery.append(")");

				final StringBuilder permanenetInQuery = new StringBuilder();
				permanenetInQuery.append("(");
				first = true;
				stateArray = stafffilterationCriteria.getState();
				for (final String state : stateArray) {
					args.add(state);
					if (first) {
						permanenetInQuery.append("?");
						first = false;
						continue;
					}
					permanenetInQuery.append(", ?");
				}
				permanenetInQuery.append(")");

				query.append(String.format(STAFF_STATE_CONDITION, presentInQuery.toString(), permanenetInQuery.toString()));
			}

			//Filteration of the staff on the basis of the staff category logic

			if (!CollectionUtils.isEmpty(stafffilterationCriteria.getStaffCategory())) {
				String inArgs = "";
				String delimiter = "";
				for (UUID uuidStaffCategory : stafffilterationCriteria.getStaffCategory()) {
					args.add(uuidStaffCategory.toString());
					inArgs = inArgs + delimiter + "?";
					delimiter = ", ";
				}
				query.append(AND).append(String.format(STAFF_CATEGORY_CONDTION, inArgs));
			}

			query.append(ORDER_BY_STAFF_NAME_ASC);
			return jdbcTemplate.query(query.toString(), args.toArray(),STAFF_ROW_MAPPER);

		}catch(final Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public Staff getStaff(int instituteId, String staffInstituteId) {
		try {
			final Object[] args = { instituteId, staffInstituteId };
			return jdbcTemplate.queryForObject(GET_STAFF_DETAILS_BY_STAFF_INSTITUTE_ID, args, STAFF_ROW_MAPPER);
		} catch (final Exception e) {
			e.printStackTrace();

			logger.error("Exception while getting staff for instituteId {}, staffInstituteId {}", instituteId,
					staffInstituteId, e);
		}
		return null;
	}

	public List<Staff> getStaffByDeviceUserId(String deviceUserId, Set<Integer> institutes) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(deviceUserId);
			StringBuilder inQuery = new StringBuilder();
			inQuery.append(" and institute_id in ( ");
			String delimiter = "";
			for(Integer instituteId : institutes){
				args.add(instituteId);
				inQuery.append(delimiter).append(" ? ");
				delimiter = ",";
			}
			inQuery.append(")");

			return jdbcTemplate.query(String.format(GET_STAFF_DETAILS_BY_DEVICE_USER_ID, inQuery), args.toArray(), STAFF_ROW_MAPPER);
		} catch (final Exception e) {
			e.printStackTrace();
			logger.error("Exception while getting staff for instituteId {}, deviceUserId {}", institutes,
					deviceUserId, e);
		}
		return null;
	}


	public List<Staff> getStaff(int instituteId) {
		try {
			final Object[] args = { instituteId };
			return jdbcTemplate.query(GET_STAFF_DETAILS_BY_INSTITUTE_ID, args, STAFF_ROW_MAPPER);
		} catch (final Exception e) {
			e.printStackTrace();
			logger.error("Exception while getting staff for instituteId {}", instituteId, e);
		}
		return null;
	}

	public List<Staff> getStaffDetailsByCategories(int instituteId,Set<UUID> staffCategories) {

		try {

			final StringBuilder query = new StringBuilder();
			query.append(GET_STAFF_DETAILS_ONLY_BY_INSTITUTE_ID);
			final List<Object> args = new ArrayList<>();
			args.add(instituteId);

			//Filteration of the staff on the basis of the staff category logic
			if (!CollectionUtils.isEmpty(staffCategories)) {
				String inArgs = "";
				String delimiter = "";
				for (UUID uuidStaffCategory : staffCategories) {
					args.add(uuidStaffCategory.toString());
					inArgs = inArgs + delimiter + "?";
					delimiter = ", ";
				}
				query.append(AND).append(String.format(STAFF_CATEGORY_CONDTION, inArgs));
			}

			return jdbcTemplate.query(query.toString(), args.toArray(), STAFF_ROW_MAPPER);

		} catch (final Exception e) {
			logger.error("Exception while getting staff for instituteId {}", instituteId, e);
		}
		return null;
	}

	public Staff getStaff(int instituteId, UUID staffId) {
		try {
			final Object[] args = { instituteId, staffId.toString() };
			return jdbcTemplate.queryForObject(GET_STAFF_DETAILS_BY_STAFF_ID, args, STAFF_ROW_MAPPER);
		} catch (final Exception e) {
			e.printStackTrace();
			logger.error("Exception while getting staff for instituteId {}, staffId {}", instituteId, staffId, e);
		}

		return null;
	}

	public boolean updateBulkStaffDetails(BulkStaffDetailsPayload updateBulkStaffDetailsPayload){

		boolean isTimingDetailsThere = updateBulkStaffDetailsPayload.getStaffDetailsParametersList().contains(StaffDetailsParameters.TIMING_DETAILS);
		boolean isVisitingHoursThere = updateBulkStaffDetailsPayload.getStaffDetailsParametersList().contains(StaffDetailsParameters.VISITING_HOURS);
        String delimiter = ",";
		boolean isFirst = false;
		StringBuilder query = new StringBuilder();
		query.append(UPDATE_BULK_STAFF_DETAILS);
		final List<Object[]> batchInsertArgs = new ArrayList<>();
		int count = 0;
		if (isTimingDetailsThere) {
			isFirst = true;
			query.append(UPDATE_TIMING_DETAILS_CLAUSE);
			for (StaffTimingsPayload staffPayload : updateBulkStaffDetailsPayload.getStaffTimingDetails()) {
				if (staffPayload.getStaffTimingDetails() != null) {
					staffPayload.getStaffTimingDetails().setHalfDayDuration(
							staffPayload.getStaffTimingDetails().getHalfDayDuration() == null ?
									null : staffPayload.getStaffTimingDetails().getHalfDayDuration() * 60);
					staffPayload.getStaffTimingDetails().setFullDayDuration(
							staffPayload.getStaffTimingDetails().getFullDayDuration() == null ?
									null : staffPayload.getStaffTimingDetails().getFullDayDuration() * 60);
				}
				final List<Object> args = new ArrayList<>();
				args.add(GSON.toJson(staffPayload.getStaffTimingDetails()));
				args.add(staffPayload.getStaffId().toString());
				count++;
				batchInsertArgs.add(args.toArray());
			}
		}
		if (isVisitingHoursThere) {
			if (isFirst) {
				query.append(delimiter);
			}
			query.append(UPDATE_VISITING_DETAILS_CLAUSE);
			for (UUID staffId : updateBulkStaffDetailsPayload.getStaffVisitingDaysPayload().getStaffIdList()) {
				final List<Object> args = new ArrayList<>();
				args.add(GSON.toJson(updateBulkStaffDetailsPayload.getStaffVisitingDaysPayload().getStaffVisitingDaysList()));
				args.add(staffId.toString());
				count++;
				batchInsertArgs.add((args.toArray()));
			}
		}
		try {
			query.append(UPDATE_STAFF_DETAILS_WHERE_CONDITION);
			final int[] rows = jdbcTemplate.batchUpdate(query.toString(), batchInsertArgs);

			if (rows.length != count) {
				return false;
			}
			for (final int rowCount : rows) {
				if (rowCount != 1) {
					return false;
				}
			}
			return true;
		} catch (final Exception e) {
			logger.error("Unable to update bulk staff details for institute {}", updateBulkStaffDetailsPayload.getInstituteId(), e);
		}
		return false;
	}


	public List<Staff> getStaff(int instituteId, Set<UUID> staffIds) {
		return getStaff(instituteId, staffIds, false);
	}

	public List<Staff> getStaff(int instituteId, Set<UUID> staffIds, boolean forUpdate) {
		if(CollectionUtils.isEmpty(staffIds)){
			return new ArrayList<>();
		}
		try {
			String forUpdateClause = forUpdate ? DBLockMode.FOR_UPDATE.getCommand() : "";
			final List<Object> args = new ArrayList<>();
			args.add(instituteId);
			QueryArgumentData queryArgumentData = QueryUtils.getListClause(staffIds);
			args.addAll(queryArgumentData.getArgs());
			String query = String.format(GET_STAFF_DETAILS_BY_STAFF_IDS, queryArgumentData.getQueryClause(), forUpdateClause);

			return jdbcTemplate.query(query, args.toArray(), STAFF_ROW_MAPPER);
		} catch (final Exception e) {
			e.printStackTrace();
			logger.error("Exception while getting staff ids for instituteId {}, staffIds {}", instituteId, staffIds, e);
		}

		return null;
	}



	public Staff getStaff(UUID staffId) {
		try {
			final Object[] args = { staffId.toString() };
			return jdbcTemplate.queryForObject(GET_STAFF_DETAILS_BY_STAFF_ID_WITHOUT_INSTITUTE, args, STAFF_ROW_MAPPER);
		} catch (final Exception e) {
			e.printStackTrace();
			logger.error("Exception while getting staff for staffId {}", staffId, e);
		}

		return null;
	}

	public boolean updateDocuments(int instituteId, UUID staffId, List<Document<StaffDocumentType>> staffDocuments) {
		try {
			final Object[] args = { GSON.toJson(staffDocuments), staffId.toString(), instituteId };
			return jdbcTemplate.update(UPDATE_STAFF_DOCUMENTS, args) == 1;
		} catch (final Exception e) {
			logger.error("Exception whle updating staff documents instituteId {}, staffId {}", instituteId, staffId, e);
		}
		return false;
	}
	
	public List<Staff> searchStaff(int instituteId, String searchText, StaffStatus staffStatus,
			String location) {
		if (instituteId <= 0 || staffStatus == null) {
			logger.error("Invalid institute {} or staffStatus {}", instituteId, staffStatus);
			return null;
		}
		try {
			final StringBuilder query = new StringBuilder();
			query.append(SEARCH_STAFF);
			final List<Object> args = new ArrayList<>();
			if (StringUtils.isBlank(searchText)) {
				query.append(WHERE);

				if (location != null) {
					if (location.equals("USER_PAGE")) {
						args.add(instituteId);
						query.append(NOT_IN_USER_CLAUSE).append(AND);
					}
				}

				query.append(WHERE_SEARCH_STAFF);
			} else {
				final String[] keywords = searchText.toLowerCase().split(" ");
				boolean first = true;
				for (String keyword : keywords) {
					keyword = keyword.trim();
					if (StringUtils.isBlank(keyword)) {
						continue;
					}
					if (first) {
						query.append(WHERE).append(STAFF_SEARCH_CONDITION);
						first = false;
					} else {
						query.append(AND).append(STAFF_SEARCH_CONDITION);
					}
					args.add("%" + keyword + "%");
				}

				if (location != null) {
					if (location.equals("USER_PAGE")) {
						args.add(instituteId);
						query.append(AND).append(NOT_IN_USER_CLAUSE);
					}
				}

				query.append(AND).append(WHERE_SEARCH_STAFF);
			}
			args.add(instituteId);
			args.add(staffStatus.name());

			query.append(ORDER_BY_STAFF_NAME_ASC);

			return jdbcTemplate.query(String.format(query.toString()), args.toArray(), STAFF_ROW_MAPPER);

		} catch (final Exception e) {
			logger.error("error while searching staff for text {}, instituteId {}, staffStatus {}",
					searchText, instituteId, staffStatus, e);
		}
		return null;
	}

	public List<Staff> advanceSearchStaff(int instituteId, String searchText, StaffStatus staffStatus, String location,
			List<UUID> staffCategoryList) {
		if (instituteId <= 0 || staffStatus == null) {
			logger.error("Invalid institute {} or staffStatus {}", instituteId, staffStatus);
			return null;
		}
		try {
			final StringBuilder query = new StringBuilder();
			query.append(SEARCH_STAFF);
			final List<Object> args = new ArrayList<>();
			if (StringUtils.isBlank(searchText)) {
				query.append(WHERE);

				if (location != null) {
					if (location.equals("USER_PAGE")) {
						args.add(instituteId);
						query.append(NOT_IN_USER_CLAUSE).append(AND);
					}
				}

				query.append(WHERE_SEARCH_STAFF);
			} else {
				final String[] keywords = searchText.toLowerCase().split(" ");
				boolean first = true;
				for (String keyword : keywords) {
					keyword = keyword.trim();
					if (StringUtils.isBlank(keyword)) {
						continue;
					}
					if (first) {
						query.append(WHERE).append(STAFF_SEARCH_CONDITION);
						first = false;
					} else {
						query.append(AND).append(STAFF_SEARCH_CONDITION);
					}
					args.add("%" + keyword + "%");
				}

				if (location != null) {
					if (location.equals("USER_PAGE")) {
						args.add(instituteId);
						query.append(AND).append(NOT_IN_USER_CLAUSE);
					}
				}

				query.append(AND).append(WHERE_SEARCH_STAFF);
			}
			args.add(instituteId);
			args.add(staffStatus.name());

			if (!CollectionUtils.isEmpty(staffCategoryList)) {
				String inArgs = "";
				String delimiter = "";
				for (UUID uuidStaffCategory : staffCategoryList) {
					args.add(uuidStaffCategory.toString());
					inArgs = inArgs + delimiter + "?";
					delimiter = ", ";
				}
				query.append(AND).append(String.format(STAFF_CATEGORY_CONDTION, inArgs));
			}

			query.append(ORDER_BY_STAFF_NAME_ASC);

			return jdbcTemplate.query(String.format(query.toString()), args.toArray(), STAFF_ROW_MAPPER);

		} catch (final Exception e) {
			logger.error("error while searching staff for text {}, instituteId {}, staffStatus {}",
					searchText, instituteId, staffStatus, e);
		}
		return null;
	}
	
	public UUID updateStaff(RegisterStaffPayload updateStaffPaylaod) {
		try {
			final UUID staffId = transactionTemplate.execute(new TransactionCallback<UUID>() {

				@Override
				public UUID doInTransaction(TransactionStatus status) {
					final UUID staffUUID = updateStaffDetails(updateStaffPaylaod);
					if (staffUUID == null) {
						throw new RuntimeException("Unable to update staff");
					}
					return staffUUID;
				}
			});
			return staffId;
		} catch (final Exception e) {
			logger.error("Unable to update staff for payload {} ", updateStaffPaylaod, e);
		}
		return null;
	}

	private UUID updateStaffDetails(RegisterStaffPayload staffPaylaod) {
		try {
			if(staffPaylaod.getStaffTimingDetails() != null) {
				staffPaylaod.getStaffTimingDetails().setHalfDayDuration(
						staffPaylaod.getStaffTimingDetails().getHalfDayDuration() == null ?
						null : staffPaylaod.getStaffTimingDetails().getHalfDayDuration() * 60);
				staffPaylaod.getStaffTimingDetails().setFullDayDuration(
						staffPaylaod.getStaffTimingDetails().getFullDayDuration() == null ?
						null : staffPaylaod.getStaffTimingDetails().getFullDayDuration() * 60);
			}
			final int row = jdbcTemplate.update(UPDATE_STAFF_DETAILS,
					staffPaylaod.getStaffBasicInfo().getStaffInstituteId(),
					staffPaylaod.getStaffBasicInfo().getName(),
					staffPaylaod.getStaffBasicInfo().getGender().name(), 
					staffPaylaod.getStaffBasicInfo().getStaffCategoryId().toString(),
					GSON.toJson(staffPaylaod.getStaffBasicInfo().getDepartmentDesignationMapping()),
					(staffPaylaod.getServiceStartDate() != null) && (staffPaylaod.getServiceStartDate() > 0)
					? new Timestamp(staffPaylaod.getServiceStartDate() * 1000l) : null,
					(staffPaylaod.getServiceEndDate() != null) && (staffPaylaod.getServiceEndDate() > 0)
							? new Timestamp(staffPaylaod.getServiceEndDate() * 1000l) : null,
					(staffPaylaod.getStaffBasicInfo().getDateOfBirth() != null)
					&& (staffPaylaod.getStaffBasicInfo().getDateOfBirth() > 0)
					? new Timestamp(staffPaylaod.getStaffBasicInfo().getDateOfBirth() * 1000l) : null,
					staffPaylaod.getStaffBasicInfo().getBirthPlace(), staffPaylaod.getStaffBasicInfo().getCategory() == null 
					? null : staffPaylaod.getStaffBasicInfo().getCategory().name(), staffPaylaod.getStaffBasicInfo().getReligion(),
					staffPaylaod.getStaffBasicInfo().getAadharNumber(), 
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getPanNumber(),
					staffPaylaod.getStaffBasicInfo().getMotherTongue(), 
					staffPaylaod.getStaffBasicInfo().getSpeciallyAbled(), staffPaylaod.getStaffBasicInfo().getBpl(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPermanentAddress1(), 
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPermanentAddress2(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPresentAddress1(), 
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPresentAddress2(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPresentCity(), 
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPresentState(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPresentCountry(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPresentZipcode(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPermanentCity(), 
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPermanentState(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPermanentCountry(),
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getPermanentZipcode(),
					staffPaylaod.getStaffBasicInfo().getPrimaryContactNumber(),
					staffPaylaod.getStaffBasicInfo().getPrimaryEmail(), 
					staffPaylaod.getStaffBasicInfo().getSecondaryEmail(),
					staffPaylaod.getStaffBasicInfo().getFatherName(), staffPaylaod.getStaffBasicInfo().getMotherName(),
					staffPaylaod.getStaffBasicInfo().getAlternateContactNumber(), 
					staffPaylaod.getStaffQualificationExprienceInfo() == null ? null : staffPaylaod.getStaffQualificationExprienceInfo().getExperience(),
					staffPaylaod.getStaffQualificationExprienceInfo() == null ? null : staffPaylaod.getStaffQualificationExprienceInfo().getHighestQualification(),
					staffPaylaod.getStaffJoiningInfo() == null ? null : staffPaylaod.getStaffJoiningInfo().getBackgroundVerification(),
					staffPaylaod.getStaffStatus().name(), staffPaylaod.getStaffJoiningInfo() == null ? null :
					(staffPaylaod.getStaffJoiningInfo().getOfferAcceptanceDate() != null) && (staffPaylaod.getStaffJoiningInfo().getOfferAcceptanceDate() > 0)
					? new Timestamp(staffPaylaod.getStaffJoiningInfo().getOfferAcceptanceDate() * 1000l) : null,
					staffPaylaod.getStaffJoiningInfo() == null ? null : 
						(staffPaylaod.getStaffJoiningInfo().getTentativeDateOfJoining() != null) && (staffPaylaod.getStaffJoiningInfo().getTentativeDateOfJoining() > 0)
						? new Timestamp(staffPaylaod.getStaffJoiningInfo().getTentativeDateOfJoining() * 1000l) : null,
					staffPaylaod.getStaffBasicInfo().getMaritalStatus() == null ? null : staffPaylaod.getStaffBasicInfo().getMaritalStatus().name(), 
							staffPaylaod.getStaffJoiningInfo() == null ? null : 
								(staffPaylaod.getStaffJoiningInfo().getRelieveDate() != null) && (staffPaylaod.getStaffJoiningInfo().getRelieveDate() > 0)
								? new Timestamp(staffPaylaod.getStaffJoiningInfo().getRelieveDate() * 1000l) : null,
					staffPaylaod.getStaffQualificationExprienceInfo() == null ? null : staffPaylaod.getStaffQualificationExprienceInfo().getLastOrganizationName(),
					staffPaylaod.getStaffQualificationExprienceInfo() == null ? null : staffPaylaod.getStaffQualificationExprienceInfo().getLastOrganizationAddress(),
					staffPaylaod.getStaffQualificationExprienceInfo() == null ? null : staffPaylaod.getStaffQualificationExprienceInfo().getLastDesignation(), 
					staffPaylaod.getStaffQualificationExprienceInfo() == null ? null : staffPaylaod.getStaffQualificationExprienceInfo().getLastJobDuration(), 
							staffPaylaod.getStaffJoiningInfo() == null ? null : staffPaylaod.getStaffJoiningInfo().getRehire(), 
					staffPaylaod.getStaffAddressContactInfo() == null ? null : staffPaylaod.getStaffAddressContactInfo().getEmergancyContactNumber(), 
							staffPaylaod.getStaffJoiningInfo() == null ? null : staffPaylaod.getStaffJoiningInfo().getAbsconding(),
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getAccountType()  == null ? null : 
					staffPaylaod.getStaffBankInfo().getAccountType().name(), 
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getBankName(), 
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getAccountHolderName(),
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getAccountNumber(),
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getIfscCode(),
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getEpfConsentType() == null ?
					null : staffPaylaod.getStaffBankInfo().getEpfConsentType().name(),
					staffPaylaod.getStaffBankInfo() == null ? null : staffPaylaod.getStaffBankInfo().getEsicConsentType() == null ?
							null : staffPaylaod.getStaffBankInfo().getEsicConsentType().name(),
					staffPaylaod.getStaffTimingDetails() == null ? null : GSON.toJson(staffPaylaod.getStaffTimingDetails()),
					staffPaylaod.getStaffBasicInfo().getInitials(), staffPaylaod.isTransportStaff(),
					staffPaylaod.getStaffVisitingDaysList() ==  null ? null : GSON.toJson(staffPaylaod.getStaffVisitingDaysList()),
					staffPaylaod.getStaffBasicInfo().getOasisId() == null ? null : staffPaylaod.getStaffBasicInfo().getOasisId(),
					staffPaylaod.getStaffBasicInfo().getNationalCode() == null ? null : staffPaylaod.getStaffBasicInfo().getNationalCode(),
					staffPaylaod.getStaffBasicInfo().getStaffNatureOfAppointment() == null ? null : staffPaylaod.getStaffBasicInfo().getStaffNatureOfAppointment().name(),
					staffPaylaod.getStaffMedicalInfo() == null ? null : staffPaylaod.getStaffMedicalInfo().getBloodGroup() == null ? null : staffPaylaod.getStaffMedicalInfo().getBloodGroup().name(),
					staffPaylaod.getStaffMedicalInfo() == null ? null : staffPaylaod.getStaffMedicalInfo().getBloodPressure(),
					staffPaylaod.getStaffMedicalInfo() == null ? null : staffPaylaod.getStaffMedicalInfo().getPulse(), staffPaylaod.getStaffMedicalInfo() == null ? null : staffPaylaod.getStaffMedicalInfo().getHeight(),
					staffPaylaod.getStaffMedicalInfo() == null ? null : staffPaylaod.getStaffMedicalInfo().getWeight(),
					staffPaylaod.getStaffMedicalInfo() == null ? null : staffPaylaod.getStaffMedicalInfo().getDateOfPhysicalExamination() == null || staffPaylaod.getStaffMedicalInfo().getDateOfPhysicalExamination() <= 0 ? null : new Timestamp(staffPaylaod.getStaffMedicalInfo().getDateOfPhysicalExamination() * 1000l),
					staffPaylaod.getInstituteId(), staffPaylaod.getStaffId().toString());;
			return row == 1 ? staffPaylaod.getStaffId() : null;

		} catch (final Exception e) {
			logger.error("Exception while updating staff for institute {}, payload {}", staffPaylaod.getInstituteId(),
					staffPaylaod, e);
		}
		return null;
	}

	public boolean deleteStaff(UUID staffId, int instituteId) {
		final Object[] args = { instituteId, staffId.toString() };
		try {
			return jdbcTemplate.update(DELETE_STAFF_BY_STAFF_ID, args) > 0;
		} catch (final Exception e) {
			logger.error("Exception while deleting staff {}, instituteId {}", staffId, instituteId, e);
		}
		return false;
	}

	public Double getWalletAmount(UUID studentId, DBLockMode dbLockMode) {
		return userWalletDao.getUserWalletAmount(studentId, dbLockMode);
	}

	/*
	 * Staff Module Enhancement staff_category, staff_department, staff_designation
	 */
	
	//staff_category
	public UUID addStaffCategory(StaffCategory staffCategory, UUID userId) {
		UUID staffCategoryId = UUID.randomUUID();
		try {
			final int row = jdbcTemplate.update(ADD_STAFF_CATEGORY_DETAILS, 
					staffCategory.getInstituteId(), staffCategoryId.toString(), 
					staffCategory.getStaffCategoryName(), userId.toString());

			return row == 1 ? staffCategoryId : null;
		} catch (final Exception e) {
			logger.error("Exception while inserting staff details for institute {}, staffPaylaod {}",
					staffCategory.getInstituteId(), staffCategory, e);
		}
		return null;
	}

	public StaffCategory checkStaffCategoryByName(int instituteId, String staffCategoryName) {
		try {
			final Object[] args = { instituteId, staffCategoryName };
			return jdbcTemplate.queryForObject(GET_STAFF_CATEGORY_DETAILS_BY_NAME, args, STAFF_CATEGORY_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting staffCategory for instituteId {}, staffCategoryName {}", instituteId,
					staffCategoryName, e);
		}
		return null;
	}

	public StaffCategory getStaffCategoryDetailsByCategoryId(int instituteId, UUID staffCategoryId) {
		try {
			final Object[] args = { instituteId, staffCategoryId };
			return jdbcTemplate.queryForObject(GET_STAFF_CATEGORY_DETAILS_BY_ID, args, STAFF_CATEGORY_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting staffCategory for instituteId {}, staffCategoryId {}", instituteId,
					staffCategoryId, e);
		}
		return null;
	}

	public List<StaffCategory> getStaffCategoryList(int instituteId) {
		try {
			final Object[] args = { instituteId };
			return jdbcTemplate.query(GET_STAFF_CATEGORY_LIST, args, STAFF_CATEGORY_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting staffCategory for instituteId {}", instituteId, e);
		}
		return null;
	}

	public boolean updateStaffCategory(StaffCategory staffCategory, UUID userId) {
		try {
			final Object[] args = { staffCategory.getStaffCategoryName(), userId.toString(),
					staffCategory.getInstituteId(), staffCategory.getStaffCategoryId().toString() };
			return jdbcTemplate.update(UPDATE_STAFF_CATEGORY_DETAILS, args) == 1;
		} catch (final Exception e) {
			logger.error("Exception while inserting staff details for institute {}, staffPaylaod {}",
					staffCategory.getInstituteId(), staffCategory, e);
		}
		return false;
	}

	public boolean deleteStaffCategory(int instituteId, UUID staffCategoryId) {
		final Object[] args = { instituteId, staffCategoryId.toString() };
		try {
			return jdbcTemplate.update(DELETE_STAFF_CATEGORY_BY_ID, args) == 1;
		} catch (final Exception e) {
			logger.error("Exception while deleting staffCategory {}, instituteId {}", staffCategoryId, instituteId, e);
		}
		return false;
	}
	
	
	//staff_department
	public UUID addStaffDepartment(StaffDepartment staffDepartment, UUID userId) {
		UUID staffDepartmentId = UUID.randomUUID();
		try {
			final int row = jdbcTemplate.update(ADD_STAFF_DEPARTMENT_DETAILS, 
					staffDepartment.getInstituteId(), staffDepartmentId.toString(), 
					staffDepartment.getStaffDepartmentName(), userId.toString());

			return row == 1 ? staffDepartmentId : null;
		} catch (final Exception e) {
			logger.error("Exception while inserting staff details for institute {}, staffPaylaod {}",
					staffDepartment.getInstituteId(), staffDepartment, e);
		}
		return null;
	}

	public StaffDepartment checkStaffDepartmentByName(int instituteId, String staffDepartmentName) {
		try {
			final Object[] args = { instituteId, staffDepartmentName };
			return jdbcTemplate.queryForObject(GET_STAFF_DEPARTMENT_DETAILS_BY_NAME, args, STAFF_DEPARTMENT_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting staffDepartment for instituteId {}, staffDepartmentName {}", instituteId,
					staffDepartmentName, e);
		}
		return null;
	}

	public StaffDepartment getStaffDepartmentDetailsByDepartmentId(int instituteId, UUID staffDepartmentId) {
		try {
			final Object[] args = { instituteId, staffDepartmentId };
			return jdbcTemplate.queryForObject(GET_STAFF_DEPARTMENT_DETAILS_BY_ID, args, STAFF_DEPARTMENT_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting staffDepartment for instituteId {}, staffDepartmentId {}", instituteId,
					staffDepartmentId, e);
		}
		return null;
	}

	public List<StaffDepartment> getStaffDepartmentList(int instituteId) {
		try {
			final Object[] args = { instituteId };
			return jdbcTemplate.query(GET_STAFF_DEPARTMENT_LIST, args, STAFF_DEPARTMENT_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting staffDepartment for instituteId {}", instituteId, e);
		}
		return null;
	}

	public boolean updateStaffDepartment(StaffDepartment staffDepartment, UUID userId) {
		try {
			final Object[] args = { staffDepartment.getStaffDepartmentName(), userId.toString(),
					staffDepartment.getInstituteId(), staffDepartment.getStaffDepartmentId().toString() };
			return jdbcTemplate.update(UPDATE_STAFF_DEPARTMENT_DETAILS, args) == 1;
		} catch (final Exception e) {
			logger.error("Exception while inserting staff details for institute {}, staffPaylaod {}",
					staffDepartment.getInstituteId(), staffDepartment, e);
		}
		return false;
	}

	public boolean deleteStaffDepartment(int instituteId, UUID staffDepartmentId) {
		final Object[] args = { instituteId, staffDepartmentId.toString() };
		try {
			return jdbcTemplate.update(DELETE_STAFF_DEPARTMENT_BY_ID, args) == 1;
		} catch (final Exception e) {
			logger.error("Exception while deleting staffDepartment {}, instituteId {}", staffDepartmentId, instituteId, e);
		}
		return false;
	}
	
	
	//staff_designation
	public UUID addStaffDesignation(StaffDesignation staffDesignation, UUID userId) {
		UUID staffDesignationId = UUID.randomUUID();
		try {
			final int row = jdbcTemplate.update(ADD_STAFF_DESIGNATION_DETAILS, 
					staffDesignation.getInstituteId(), staffDesignationId.toString(), 
					staffDesignation.getStaffDesignationName(), userId.toString());

			return row == 1 ? staffDesignationId : null;
		} catch (final Exception e) {
			logger.error("Exception while inserting staff details for institute {}, staffPaylaod {}",
					staffDesignation.getInstituteId(), staffDesignation, e);
		}
		return null;
	}

	public StaffDesignation checkStaffDesignationByName(int instituteId, String staffDesignationName) {
		try {
			final Object[] args = { instituteId, staffDesignationName };
			return jdbcTemplate.queryForObject(GET_STAFF_DESIGNATION_DETAILS_BY_NAME, args, STAFF_DESIGNATION_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting staffDesignation for instituteId {}, staffDesignationName {}", instituteId,
					staffDesignationName, e);
		}
		return null;
	}

	public StaffDesignation getStaffDesignationDetailsByDesignationId(int instituteId, UUID staffDesignationId) {
		try {
			final Object[] args = { instituteId, staffDesignationId };
			return jdbcTemplate.queryForObject(GET_STAFF_DESIGNATION_DETAILS_BY_ID, args, STAFF_DESIGNATION_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting staffDesignation for instituteId {}, staffDesignationId {}", instituteId,
					staffDesignationId, e);
		}
		return null;
	}

	public List<StaffDesignation> getStaffDesignationList(int instituteId) {
		try {
			final Object[] args = { instituteId };
			return jdbcTemplate.query(GET_STAFF_DESIGNATION_LIST, args, STAFF_DESIGNATION_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting staffDesignation for instituteId {}", instituteId, e);
		}
		return null;
	}

	public boolean updateStaffDesignation(StaffDesignation staffDesignation, UUID userId) {
		try {
			final Object[] args = { staffDesignation.getStaffDesignationName(), userId.toString(),
					staffDesignation.getInstituteId(), staffDesignation.getStaffDesignationId().toString() };
			return jdbcTemplate.update(UPDATE_STAFF_DESIGNATION_DETAILS, args) == 1;
		} catch (final Exception e) {
			logger.error("Exception while inserting staff details for institute {}, staffPaylaod {}",
					staffDesignation.getInstituteId(), staffDesignation, e);
		}
		return false;
	}

	public boolean deleteStaffDesignation(int instituteId, UUID staffDesignationId) {
		final Object[] args = { instituteId, staffDesignationId.toString() };
		try {
			return jdbcTemplate.update(DELETE_STAFF_DESIGNATION_BY_ID, args) == 1;
		} catch (final Exception e) {
			logger.error("Exception while deleting staffDesignation {}, instituteId {}", staffDesignationId, instituteId, e);
		}
		return false;
	}

	public List<Staff> getStaffDetailsByCategory(int instituteId, UUID staffCategoryId) {
		try {
			final Object[] args = { instituteId, staffCategoryId.toString() };
			return jdbcTemplate.query(GET_STAFF_DETAILS_BY_CATEGORY_ID, args, STAFF_ROW_MAPPER);
		} catch (final Exception e) {
			e.printStackTrace();
			logger.error("Exception while getting staff for instituteId {}", instituteId, e);
		}
		return null;
	}

	public UserBiometricIdentificationData getBiometricDeviceIdentification(UUID staffId, boolean forUpdate){
		try {
			String forUpdateClause = forUpdate ? " FOR UPDATE " : "";
			return jdbcTemplate.queryForObject(String.format(GET_STAFF_BIOMETRIC_DATA_BY_ID, forUpdateClause), new Object[]{staffId.toString()}, USER_BIOMETRIC_IDENTIFICATION_ROW_MAPPER);
		}catch (Exception e){
			logger.error("Unable to get staff biometric data {}", staffId, e);
		}
		return null;
	}

	public boolean updateBiometricDeviceIdentification(UUID staffId, AttendanceDeviceServiceProviderType serviceProviderType, DeviceUpdateUserData deviceUpdateUserData){
		try {
			return transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					UserBiometricIdentificationData userBiometricIdentificationData = getBiometricDeviceIdentification(staffId, true);
					if(userBiometricIdentificationData == null){
						return false;
					}
					List<RFIDCardData> rfidCardDataList = userBiometricIdentificationData.getRfidCardDataList() == null ? new ArrayList<>() : userBiometricIdentificationData.getRfidCardDataList();
					List<FingerprintData> fingerprintDataList = userBiometricIdentificationData.getFingerprintDataList() == null ? new ArrayList<>() : userBiometricIdentificationData.getFingerprintDataList();
					List<FaceData> faceDataList =userBiometricIdentificationData.getFaceDataList() == null ? new ArrayList<>() : userBiometricIdentificationData.getFaceDataList();

					for(DeviceUpdateUserIdentityData deviceUpdateUserIdentityData : deviceUpdateUserData.getDeviceUpdateUserIdentityDataList()){
						if(deviceUpdateUserIdentityData.getAttendanceInputCategory() == null){
							continue;
						}

						switch (deviceUpdateUserIdentityData.getAttendanceInputCategory()){
							case CARD:
								if(!cardExists(rfidCardDataList, deviceUpdateUserIdentityData)){
									rfidCardDataList.add(new RFIDCardData(serviceProviderType, deviceUpdateUserIdentityData.getData(), deviceUpdateUserData.getOperationTime()));
								}
								break;
							case FINGER_PRINT:
								if(!fingerprintExists(fingerprintDataList, deviceUpdateUserIdentityData)){
									fingerprintDataList.add(new FingerprintData(serviceProviderType, deviceUpdateUserIdentityData.getData(), deviceUpdateUserIdentityData.getSize(), deviceUpdateUserIdentityData.getIndex(), deviceUpdateUserData.getOperationTime()));
								}
								break;
							case FACE:
								if(!faceExists(faceDataList, deviceUpdateUserIdentityData)){
									faceDataList.add(new FaceData(serviceProviderType, deviceUpdateUserIdentityData.getData(), deviceUpdateUserIdentityData.getSize(), deviceUpdateUserIdentityData.getIndex(), deviceUpdateUserData.getOperationTime()));
								}
								break;
						}
					}

					return jdbcTemplate.update(UPDATE_STAFF_BIOMETRIC_DEVICE_IDENTIFICATION, GSON.toJson(rfidCardDataList), GSON.toJson(fingerprintDataList), GSON.toJson(faceDataList), staffId.toString()) == 1;
				}
			});
		}catch (Exception e){
			logger.error("Unable to update biometric device identification for staffId {}, serviceProviderType {},  deviceUpdateUserData {}", staffId, serviceProviderType, deviceUpdateUserData, e);
			return false;
		}

	}

	public List<String> getReligionDetails(int instituteId) {
		try {
			final Object[] args = { instituteId };
			return jdbcTemplate.queryForList(GET_RELIGION_BY_INSTITUTE, args, String.class);
		} catch (final Exception e) {
			logger.error("Exception while getting religions for instituteId {}", instituteId, e);
		}

		return null;
	}

	public boolean updateStaffDeviceUserId(int instituteId, Map<UUID, String> uuidDeviceUserIdMap) {
		try {
			return transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus transactionStatus) {
					List<Object[]> args = new ArrayList<>();
					for (Map.Entry<UUID, String> entry : uuidDeviceUserIdMap.entrySet()) {
						args.add(new Object[]{entry.getValue(), instituteId, entry.getKey().toString()});
					}
					return jdbcTemplate.batchUpdate(UPDATE_STAFF_DEVICE_USER_ID, args).length == args.size();
				}
			});
		} catch (final Exception e) {
			logger.error("Exception while updating device user id for instituteId {}, uuidDeviceUserIdMap {}", instituteId, uuidDeviceUserIdMap, e);
		}
		return false;
	}
}
