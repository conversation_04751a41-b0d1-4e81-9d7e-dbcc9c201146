/**
 * 
 */
package com.lernen.cloud.dao.tier.staff.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.staff.StaffAddressContactInfo;

/**
 * <AUTHOR>
 *
 */
public class StaffAddressContactInfoRowMapper implements RowMapper<StaffAddressContactInfo> {
	
	private static final String PERMANENT_ADDRESS_1 = "permanent_address_1";
	private static final String PERMANENT_ADDRESS_2 = "permanent_address_2";
	private static final String PRESENT_ADDRESS_1 = "present_address_1";
	private static final String PRESENT_ADDRESS_2 = "present_address_2";
	private static final String PRESENT_CITY = "present_city";
	private static final String PRESENT_STATE = "present_state";
	private static final String PRESENT_COUNTRY = "present_country";
	private static final String PRESENT_ZIPCODE = "present_zipcode";
	private static final String PERMANENT_CITY = "permanent_city";
	private static final String PERMANENT_STATE = "permanent_state";
	private static final String PERMANENT_COUNTRY = "permanent_country";
	private static final String PERMANENT_ZIPCODE = "permanent_zipcode";
	private static final String EMERGENCY_CONTACT_NUMBER = "emergency_contact_number";
	
	@Override
	public StaffAddressContactInfo mapRow(ResultSet rs, int rowNum) throws SQLException {
		return new StaffAddressContactInfo(rs.getString(PERMANENT_ADDRESS_1), rs.getString(PERMANENT_ADDRESS_2),
				rs.getString(PERMANENT_CITY), rs.getString(PERMANENT_STATE),
				rs.getString(PERMANENT_ZIPCODE), rs.getString(PERMANENT_COUNTRY),
				rs.getString(PRESENT_ADDRESS_1), rs.getString(PRESENT_ADDRESS_2), rs.getString(PRESENT_CITY),
				rs.getString(PRESENT_STATE), rs.getString(PRESENT_ZIPCODE), rs.getString(PRESENT_COUNTRY), 
				 rs.getString(EMERGENCY_CONTACT_NUMBER));
	}

}
