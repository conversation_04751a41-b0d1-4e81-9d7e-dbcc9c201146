/**
 *
 */
package com.lernen.cloud.dao.tier.staff.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import com.lernen.cloud.core.api.staff.ConsentType;
import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.staff.AccountType;
import com.lernen.cloud.core.api.staff.StaffBankInfo;

/**
 * <AUTHOR>
 *
 */
public class StaffBankInfoRowMapper implements RowMapper<StaffBankInfo> {

    private static final String ACCOUNT_TYPE = "account_type";
    private static final String BANK_NAME = "bank_name";
    private static final String ACCOUNT_HOLDER_NAME = "account_holder_name";
    private static final String ACCOUNT_NAME = "account_number";
    private static final String IFSC_CODE = "ifsc_code";
    private static final String PAN_NUMBER = "pan_number";
    private static final String EPF_STATUS = "epf_consent";
    private static final String ESIC_STATUS = "esic_consent";

    @Override
    public StaffBankInfo mapRow(ResultSet rs, int rowNum) throws SQLException {
        String epfConsentStr = rs.getString(EPF_STATUS);
        String esicConsentStr = rs.getString(ESIC_STATUS);
        ConsentType epfConsent = epfConsentStr == null ? ConsentType.DEFAULT : ConsentType.valueOf(epfConsentStr);
        ConsentType esicConsent = esicConsentStr == null ? ConsentType.DEFAULT : ConsentType.valueOf(esicConsentStr);
        return new StaffBankInfo(rs.getString(ACCOUNT_TYPE) == null ? null :
                AccountType.getAccountType(rs.getString(ACCOUNT_TYPE)), rs.getString(BANK_NAME), rs.getString(ACCOUNT_HOLDER_NAME),
                rs.getString(ACCOUNT_NAME), rs.getString(IFSC_CODE), rs.getString(PAN_NUMBER),
                epfConsent, esicConsent);
    }
}
