/**
 * 
 */
package com.lernen.cloud.dao.tier.staff.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.staff.StaffDepartment;

/**
 * <AUTHOR>
 *
 */
public class StaffDepartmentRowMapper implements RowMapper<StaffDepartment> {

	private static final String INSTITUTE_ID = "staff_department.institute_id";
	private static final String STAFF_DEPARTMENT_ID = "staff_department.staff_department_id";
	private static final String STAFF_DEPARTMENT_NAME = "staff_department.staff_department_name";

	@Override
	public StaffDepartment mapRow(ResultSet rs, int rowNum) throws SQLException {

		return new StaffDepartment(rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(STAFF_DEPARTMENT_ID)), 
				rs.getString(STAFF_DEPARTMENT_NAME));
	}

}
