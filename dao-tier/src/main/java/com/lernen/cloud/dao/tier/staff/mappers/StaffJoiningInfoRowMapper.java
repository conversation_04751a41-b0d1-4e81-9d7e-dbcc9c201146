/**
 * 
 */
package com.lernen.cloud.dao.tier.staff.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.staff.StaffJoiningInfo;

/**
 * <AUTHOR>
 *
 */
public class StaffJoiningInfoRowMapper implements RowMapper<StaffJoiningInfo>{

	private static final String OFFER_ACCEPTANCE_DATE = "offer_acceptance_date";
	private static final String TENTATIVE_DATE_OF_JOINING = "tentative_date_of_joining";
	private static final String RELIEVE_DATE = "relieve_date";
	private static final String REHIRE = "rehire";
	private static final String ABSCONDING_YN = "absconding_yn";
	private static final String BACKGROUND_VERIFICATION = "background_verification";
		
	@Override
	public StaffJoiningInfo mapRow(ResultSet rs, int rowNum) throws SQLException {

		final Timestamp offerAcceptanceDate = rs.getTimestamp(OFFER_ACCEPTANCE_DATE);
		final Integer offerAcceptanceDateTime = offerAcceptanceDate == null ? null
				: (int) (offerAcceptanceDate.getTime() / 1000l);
		
		
		final Timestamp tentativeDateJoining = rs.getTimestamp(TENTATIVE_DATE_OF_JOINING);
		final Integer tentativeDateJoiningTime = tentativeDateJoining == null ? null
				: (int) (tentativeDateJoining.getTime() / 1000l);
		
		
		final Timestamp relieveDate = rs.getTimestamp(RELIEVE_DATE);
		final Integer relieveDateTime = relieveDate == null ? null
				: (int) (relieveDate.getTime() / 1000l);
		
		Boolean rehire = rs.getBoolean(REHIRE);
		if (rs.wasNull()) {
			rehire = null;
		}
		
		Boolean abscondingYn = rs.getBoolean(ABSCONDING_YN);
		if (rs.wasNull()) {
			abscondingYn = null;
		}
		
		Boolean backgroundVerification = rs.getBoolean(BACKGROUND_VERIFICATION);
		if (rs.wasNull()) {
			backgroundVerification = null;
		}
		
		return new StaffJoiningInfo(offerAcceptanceDateTime, tentativeDateJoiningTime, relieveDateTime,
				rehire, abscondingYn, backgroundVerification);
	}

}
