package com.lernen.cloud.dao.tier.student;

import com.embrate.cloud.core.api.student.registration.StudentRegistrationIdentifier;
import com.embrate.cloud.core.api.student.registration.StudentRegistrationPaymentData;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.common.CounterType;
import com.lernen.cloud.core.api.common.PaginationInfo;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.fees.FeeHeadAmountDetails;
import com.lernen.cloud.core.api.fees.FeeIdFeeHead;
import com.lernen.cloud.core.api.fees.FeeIdFeeHeadDetails;
import com.lernen.cloud.core.api.institute.CounterData;
import com.lernen.cloud.core.api.student.*;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.dao.tier.institute.InstituteDao;
import com.lernen.cloud.dao.tier.student.mappers.StudentRegistrationFeeAssignmentRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.StudentRegistrationRowMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class StudentRegistrationDao {

	private static final Logger logger = LogManager.getLogger(StudentRegistrationDao.class);

	private static final StudentRegistrationRowMapper STUDENT_REGISTRATION_ROW_MAPPER = new StudentRegistrationRowMapper();
	private static final StudentRegistrationFeeAssignmentRowMapper STUDENT_REGISTRATION_FEE_ASSIGNMENT_ROW_MAPPER = new StudentRegistrationFeeAssignmentRowMapper();
	private static final Gson GSON = SharedConstants.GSON;

	private final JdbcTemplate jdbcTemplate;
	private final TransactionTemplate transactionTemplate;
	private final InstituteDao instituteDao;

	private static final String ADD_STUDENT_DETAILS = "insert into student_registration(institute_id, institute_unique_code, registration_id, institute_registration_id, registration_date, "
			+ " admission_academic_session, registration_standard_id, status, name, gender, date_of_birth, birth_place, category, religion, rte, aadhar_number, mother_tongue, "
			+ " area_type, specially_abled, bpl, permanent_address, permanent_city, permanent_state, permanent_zipcode, permanent_country, present_address, present_city, present_state, "
			+ " present_zipcode, present_country, nationality, primary_contact_number, primary_email, father_name, mother_name, "
			+ " father_qualification, mother_qualification, father_occupation, father_annual_income, mother_occupation, mother_annual_income, father_contact_number, mother_contact_number, father_aadhar_number, mother_aadhar_number, "
			+ " family_approx_income, guardians_details, previous_school_name, class_passed, previous_school_medium, result, percentage, year_of_passing,"
			+ " blood_group, blood_pressure, pulse, height, weight, date_of_physical_examination, student_documents, caste, whatsapp_number, is_sponsored, "
			+ " permanent_post_office, permanent_police_station, present_post_office, present_police_station, is_admission_tc_based, previous_school_tc_number, specially_abled_type, assigned_amount) "
			+ " values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,"
			+ " ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String ADD_STUDENT_REGISTRATION_FEE_ASSIGNMENT = "insert into student_registration_fee_assignment" +
			" (institute_id, registration_id, fee_id, fee_head_id, amount) values (?, ?, ?, ?, ?)";

	private static final String GET_STUDENT_REGISTRATION_FEE_ASSIGNMENT = "select * from student_registration_fee_assignment where " +
			" institute_id = ? and registration_id = ?";

	private static final String UPDATE_STUDENT_STATUS = "update student_registration set status = ? where registration_id = ?";

	private static final String UPDATE_STUDENT_FEE_PAYMENT = "update student_registration set paid_amount = ?, discount_amount = ?, " +
			" transaction_mode = ?, transaction_date = ?, transaction_reference = ?, transaction_added_at = ?, transaction_added_by = ? " +
			" where institute_id = ? and registration_id = ?";

	private static final String GET_STUDENT_BY_REGISTRATION_ID = "select student_registration.*, academic_session.*, standards.* "
			+ " from student_registration join academic_session on student_registration.admission_academic_session = "
			+ " academic_session.academic_session_id join standards on"
			+ " student_registration.registration_standard_id = standards.standard_id where registration_id = ?";

	private static final String SEARCH_STUDENTS_IN_ACADEMIC_SESSION = "select student_registration.*, academic_session.*, standards.* "
			+ " from student_registration join academic_session on student_registration.admission_academic_session = "
			+ " academic_session.academic_session_id join standards on"
			+ " student_registration.registration_standard_id = standards.standard_id ";

	private static final String COUNT_RESULT_SEARCH_STUDENTS_IN_ACADEMIC_SESSION = "select count(*) "
			+ " from student_registration join academic_session on student_registration.admission_academic_session = "
			+ " academic_session.academic_session_id join standards on"
			+ " student_registration.registration_standard_id = standards.standard_id ";

	private static final String STUDENT_SEARCH_CONDITION = " concat_ws('/', student_registration.institute_registration_id, student_registration.name, student_registration.father_name, standards.standard_name) like ? ";

	private static final String AND = " and ";
	private static final String WHERE = " where ";
	private static final String WHERE_CLAUSE_FOR_STUDENT_SEARCH = " student_registration.institute_id = ? and student_registration.admission_academic_session = ? "
			+ " and student_registration.status in %s order by standards.level, student_registration.name limit ? offset ?";

	private static final String COUNT_WHERE_CLAUSE_FOR_STUDENT_SEARCH = " student_registration.institute_id = ? and student_registration.admission_academic_session = ? "
			+ " and student_registration.status in %s ";

	public StudentRegistrationDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate,
								  InstituteDao instituteDao) {
		this.jdbcTemplate = jdbcTemplate;
		this.transactionTemplate = transactionTemplate;
		this.instituteDao = instituteDao;
	}

	public StudentRegistrationIdentifier registerStudent(int instituteId, int academicSessionId,
														 StudentRegistrationPayload studentRegistrationPayload,
														 List<Document<StudentDocumentType>> documents,
														 List<FeeIdFeeHeadDetails> feeIdFeeHeadDetailsList) {
		try {
			final StudentRegistrationIdentifier studentRegistrationNumber = transactionTemplate.execute(new TransactionCallback<StudentRegistrationIdentifier>() {

				@Override
				public StudentRegistrationIdentifier doInTransaction(TransactionStatus status) {
					final StudentRegistrationIdentifier studentRegistrationIdentifier = registerStudentData(instituteId, academicSessionId,
							studentRegistrationPayload, documents, feeIdFeeHeadDetailsList);
					if (studentRegistrationIdentifier == null || StringUtils.isBlank(studentRegistrationIdentifier.getStudentRegistrationNumber())) {
						throw new EmbrateRunTimeException("Unable to register student");
					}
					return studentRegistrationIdentifier;
				}
			});
			return studentRegistrationNumber;
		} catch (final Exception e) {
			logger.error("Unable to execute register student transaction", e);
		}
		return null;
	}

	private StudentRegistrationIdentifier registerStudentData(int instituteId, int academicSessionId,
															  StudentRegistrationPayload studentRegistrationPayload,
															  List<Document<StudentDocumentType>> studentDocuments,
															  List<FeeIdFeeHeadDetails> feeIdFeeHeadDetailsList) {
		final UUID studentRegistrationId = UUID.randomUUID();
		Long now = System.currentTimeMillis();
		try {
			String instituteRegistrationNumber = getRegistrationNumber(instituteId);
			final int row = jdbcTemplate.update(ADD_STUDENT_DETAILS,
					instituteId, studentRegistrationPayload.getInstituteUniqueCode().toString(), studentRegistrationId.toString(),
					instituteRegistrationNumber, new Timestamp(now), academicSessionId,
					studentRegistrationPayload.getStandardId().toString(), StudentRegistrationStatus.APPLIED.name(),
					studentRegistrationPayload.getStudentBasicInfo().getName(),
					studentRegistrationPayload.getStudentBasicInfo().getGender() == null ? null
							: studentRegistrationPayload.getStudentBasicInfo().getGender().name(),
					(studentRegistrationPayload.getStudentBasicInfo().getDateOfBirth() != null)
							&& (studentRegistrationPayload.getStudentBasicInfo().getDateOfBirth() > 0)
							? new Timestamp(
							studentRegistrationPayload.getStudentBasicInfo().getDateOfBirth() * 1000l)
							: null,
					studentRegistrationPayload.getStudentBasicInfo().getBirthPlace(),
					studentRegistrationPayload.getStudentBasicInfo().getUserCategory() == null ? null
							: studentRegistrationPayload.getStudentBasicInfo().getUserCategory().name(),
					studentRegistrationPayload.getStudentBasicInfo().getReligion(),
					studentRegistrationPayload.getStudentBasicInfo().isRte(),
					studentRegistrationPayload.getStudentBasicInfo().getAadharNumber(),
					studentRegistrationPayload.getStudentBasicInfo().getMotherTongue(),
					studentRegistrationPayload.getStudentBasicInfo().getAreaType() == null ? null
							: studentRegistrationPayload.getStudentBasicInfo().getAreaType().name(),
					studentRegistrationPayload.getStudentBasicInfo().isSpeciallyAbled(),
					studentRegistrationPayload.getStudentBasicInfo().isBpl(),
					studentRegistrationPayload.getStudentBasicInfo().getPermanentAddress(),
					studentRegistrationPayload.getStudentBasicInfo().getPermanentCity(),
					studentRegistrationPayload.getStudentBasicInfo().getPermanentState(),
					studentRegistrationPayload.getStudentBasicInfo().getPermanentZipcode(),
					studentRegistrationPayload.getStudentBasicInfo().getPermanentCountry(),
					studentRegistrationPayload.getStudentBasicInfo().getPresentAddress(),
					studentRegistrationPayload.getStudentBasicInfo().getPresentCity(),
					studentRegistrationPayload.getStudentBasicInfo().getPresentState(),
					studentRegistrationPayload.getStudentBasicInfo().getPresentZipcode(),
					studentRegistrationPayload.getStudentBasicInfo().getPresentCountry(),
					studentRegistrationPayload.getStudentBasicInfo().getNationality(),
					studentRegistrationPayload.getStudentBasicInfo().getPrimaryContactNumber(),
					studentRegistrationPayload.getStudentBasicInfo().getPrimaryEmail(),
					studentRegistrationPayload.getStudentFamilyInfo() == null ? null
							: studentRegistrationPayload.getStudentFamilyInfo().getFathersName(),
					studentRegistrationPayload.getStudentFamilyInfo() == null ? null
							: studentRegistrationPayload.getStudentFamilyInfo().getMothersName(),
					studentRegistrationPayload.getStudentFamilyInfo() == null ? null
							: studentRegistrationPayload.getStudentFamilyInfo().getFathersQualification(),
					studentRegistrationPayload.getStudentFamilyInfo() == null ? null
							: studentRegistrationPayload.getStudentFamilyInfo().getMothersQualification(),
					studentRegistrationPayload.getStudentFamilyInfo() == null ? null
							: studentRegistrationPayload.getStudentFamilyInfo().getFathersOccupation(),
					studentRegistrationPayload.getStudentFamilyInfo() == null ? null
							: studentRegistrationPayload.getStudentFamilyInfo().getFathersAnnualIncome(),
					studentRegistrationPayload.getStudentFamilyInfo() == null ? null
							: studentRegistrationPayload.getStudentFamilyInfo().getMothersOccupation(),
					studentRegistrationPayload.getStudentFamilyInfo() == null ? null
							: studentRegistrationPayload.getStudentFamilyInfo().getMothersAnnualIncome(),
					studentRegistrationPayload.getStudentFamilyInfo() == null ? null
							: studentRegistrationPayload.getStudentFamilyInfo().getFathersContactNumber(),
					studentRegistrationPayload.getStudentFamilyInfo() == null ? null
							: studentRegistrationPayload.getStudentFamilyInfo().getMothersContactNumber(),
					studentRegistrationPayload.getStudentFamilyInfo() == null ? null
							: studentRegistrationPayload.getStudentFamilyInfo().getFathersAadharNumber(),
					studentRegistrationPayload.getStudentFamilyInfo() == null ? null
							: studentRegistrationPayload.getStudentFamilyInfo().getMothersAadharNumber(),
					studentRegistrationPayload.getStudentFamilyInfo() == null ? null
							: studentRegistrationPayload.getStudentFamilyInfo().getApproxFamilyIncome(),

					studentRegistrationPayload.getStudentGuardianInfoList() == null ? null
							: GSON.toJson(studentRegistrationPayload.getStudentGuardianInfoList()),

					studentRegistrationPayload.getStudentPreviousSchoolInfo() == null ? null
							: studentRegistrationPayload.getStudentPreviousSchoolInfo().getSchoolName(),
					studentRegistrationPayload.getStudentPreviousSchoolInfo() == null ? null
							: studentRegistrationPayload.getStudentPreviousSchoolInfo().getClassPassed(),
					studentRegistrationPayload.getStudentPreviousSchoolInfo() == null ? null
							: studentRegistrationPayload.getStudentPreviousSchoolInfo().getMedium(),
					studentRegistrationPayload.getStudentPreviousSchoolInfo() == null ? null
							: studentRegistrationPayload.getStudentPreviousSchoolInfo().getResult(),
					studentRegistrationPayload.getStudentPreviousSchoolInfo() == null ? null
							: studentRegistrationPayload.getStudentPreviousSchoolInfo().getPercentage(),
					studentRegistrationPayload.getStudentPreviousSchoolInfo() == null ? null
							: studentRegistrationPayload.getStudentPreviousSchoolInfo().getYearOfPassing(),
					(studentRegistrationPayload.getStudentMedicalInfo() == null)
							|| (studentRegistrationPayload.getStudentMedicalInfo().getBloodGroup() == null) ? null
							: studentRegistrationPayload.getStudentMedicalInfo().getBloodGroup().name(),
					studentRegistrationPayload.getStudentMedicalInfo() == null ? null
							: studentRegistrationPayload.getStudentMedicalInfo().getBloodPressure(),
					studentRegistrationPayload.getStudentMedicalInfo() == null ? null
							: studentRegistrationPayload.getStudentMedicalInfo().getPulse(),
					studentRegistrationPayload.getStudentMedicalInfo() == null ? null
							: studentRegistrationPayload.getStudentMedicalInfo().getHeight(),
					studentRegistrationPayload.getStudentMedicalInfo() == null ? null
							: studentRegistrationPayload.getStudentMedicalInfo().getWeight(),
					(studentRegistrationPayload.getStudentMedicalInfo() != null)
							&& (studentRegistrationPayload.getStudentMedicalInfo()
							.getDateOfPhysicalExamination() != null)
							&& (studentRegistrationPayload.getStudentMedicalInfo().getDateOfPhysicalExamination() > 0)
							? new Timestamp(studentRegistrationPayload.getStudentMedicalInfo()
							.getDateOfPhysicalExamination() * 1000l)
							: null,
					studentDocuments == null ? null : GSON.toJson(studentDocuments),
					studentRegistrationPayload.getStudentBasicInfo().getCaste(),
					studentRegistrationPayload.getStudentBasicInfo().getWhatsappNumber(),
					studentRegistrationPayload.getStudentBasicInfo().isSponsored(),
					studentRegistrationPayload.getStudentBasicInfo().getPermanentPostOffice(),
					studentRegistrationPayload.getStudentBasicInfo().getPermanentPoliceStation(),
					studentRegistrationPayload.getStudentBasicInfo().getPresentPostOffice(),
					studentRegistrationPayload.getStudentBasicInfo().getPresentPoliceStation(),
					studentRegistrationPayload.getStudentPreviousSchoolInfo() != null && studentRegistrationPayload.getStudentPreviousSchoolInfo().isAdmissionTcBased(),
					studentRegistrationPayload.getStudentPreviousSchoolInfo() == null ? null : studentRegistrationPayload.getStudentPreviousSchoolInfo().getTcNumber(),
					studentRegistrationPayload.getStudentBasicInfo().getSpeciallyAbledType(),
					studentRegistrationPayload.getPaymentData() == null ? 0d : studentRegistrationPayload.getPaymentData().getAssignedAmount());
			boolean success = row == 1;
			if (!success) {
				logger.error("Unable to register the student for institute {}, academicSessionId {}, studentRegistrationId {}", instituteId, academicSessionId, studentRegistrationId);
				return null;
			}

			if (studentRegistrationPayload.getPaymentData() != null
					&& Double.compare(studentRegistrationPayload.getPaymentData().getAssignedAmount(), 0d) > 0
					&& !CollectionUtils.isEmpty(feeIdFeeHeadDetailsList)) {

				List<Object []> batchArgs = new ArrayList<>();
				for(FeeIdFeeHeadDetails feeIdFeeHeadDetails : feeIdFeeHeadDetailsList){
					UUID feeId = feeIdFeeHeadDetails.getFeeConfigurationResponse().getFeeConfigurationBasicInfo().getFeeId();
					for(FeeHeadAmountDetails feeHeadAmountDetails : feeIdFeeHeadDetails.getFeeHeadAmountDetailsList()){
						int feeHeadId = feeHeadAmountDetails.getFeeHeadId();
						batchArgs.add(new Object[]{instituteId, studentRegistrationId.toString(), feeId.toString(), feeHeadId, feeHeadAmountDetails.getAmount()});
					}
				}

				int [] assignmentRows = jdbcTemplate.batchUpdate(ADD_STUDENT_REGISTRATION_FEE_ASSIGNMENT, batchArgs);
				if(assignmentRows.length != batchArgs.size()){
					logger.error("Unable to add the fee assignment entries for the student for institute {}, academicSessionId {}, studentRegistrationId {}", instituteId, academicSessionId, studentRegistrationId);
					throw new EmbrateRunTimeException(
							"Unable to add the fee assignment entries for the student for institute " + instituteId);
				}
			}

			if (!instituteDao.incrementCounter(instituteId, CounterType.ONLINE_REGISTRATION_NUMBER)) {
				throw new EmbrateRunTimeException(
						"Unable to increment online registration counter for institute " + instituteId);
			}

			return new StudentRegistrationIdentifier(studentRegistrationId, instituteRegistrationNumber);

		} catch (final Exception e) {
			logger.error("Exception while adding student registration for institute code {}",
					studentRegistrationPayload.getInstituteUniqueCode(), e);
		}
		return null;
	}

	private String getRegistrationNumber(int instituteId) {
		final CounterData counterData = instituteDao.getCounter(instituteId, CounterType.ONLINE_REGISTRATION_NUMBER,
				true);
		if (counterData == null) {
			throw new EmbrateRunTimeException(CounterType.ONLINE_REGISTRATION_NUMBER.name()
					+ " counter is not present for institute " + instituteId);
		}
		return counterData.getFullCounterValue();
	}

	public RegistrationStudent getRegistrationStudent(UUID studentRegistrationId) {

		try {
			return jdbcTemplate.queryForObject(GET_STUDENT_BY_REGISTRATION_ID,
					new Object[]{studentRegistrationId.toString()}, STUDENT_REGISTRATION_ROW_MAPPER);
		} catch (Exception e) {
			logger.error("Error while getting student with reg id {}", studentRegistrationId, e);
		}
		return null;
	}

	public List<FeeIdFeeHead> getRegistrationStudentFeeAssignment(int instituteId, UUID studentRegistrationId) {
		try {
			return StudentRegistrationFeeAssignmentRowMapper.getFeeAssignment(jdbcTemplate.query(GET_STUDENT_REGISTRATION_FEE_ASSIGNMENT,
					new Object[]{instituteId, studentRegistrationId.toString()}, STUDENT_REGISTRATION_FEE_ASSIGNMENT_ROW_MAPPER));
		} catch (Exception e) {
			logger.error("Error while getting student with instituteId {}, reg id {}", instituteId, studentRegistrationId, e);
		}
		return null;
	}

	public SearchResultWithPagination<RegistrationStudent> searchRegistrationStudents(int instituteId,
																					  int academicSessionId, String searchText, List<StudentRegistrationStatus> studentRegistrationStatusList,
																					  int offset, int limit) {
		try {
			final StringBuilder query = new StringBuilder();
			final StringBuilder statusInQuery = new StringBuilder();
			final StringBuilder countQuery = new StringBuilder();
			query.append(SEARCH_STUDENTS_IN_ACADEMIC_SESSION);
			countQuery.append(COUNT_RESULT_SEARCH_STUDENTS_IN_ACADEMIC_SESSION);
			final List<Object> args = new ArrayList<>();
			if (StringUtils.isBlank(searchText)) {
				query.append(WHERE);
				query.append(WHERE_CLAUSE_FOR_STUDENT_SEARCH);
				countQuery.append(WHERE).append(COUNT_WHERE_CLAUSE_FOR_STUDENT_SEARCH);
			} else {
				final String[] keywords = searchText.toLowerCase().split(" ");
				boolean first = true;
				for (String keyword : keywords) {
					keyword = keyword.trim();
					if (StringUtils.isBlank(keyword)) {
						continue;
					}
					if (first) {
						query.append("where " + STUDENT_SEARCH_CONDITION);
						countQuery.append("where " + STUDENT_SEARCH_CONDITION);
						first = false;
					} else {
						query.append(AND).append(STUDENT_SEARCH_CONDITION);
						countQuery.append(AND).append(STUDENT_SEARCH_CONDITION);
					}
					args.add("%" + keyword + "%");
				}

				query.append(AND).append(WHERE_CLAUSE_FOR_STUDENT_SEARCH);
				countQuery.append(AND).append(COUNT_WHERE_CLAUSE_FOR_STUDENT_SEARCH);
			}
			args.add(instituteId);
			args.add(academicSessionId);

			statusInQuery.append("(");

			boolean firstStatus = true;
			for (final StudentRegistrationStatus studentRegistrationStatus : studentRegistrationStatusList) {
				args.add(studentRegistrationStatus.name());
				if (firstStatus) {
					statusInQuery.append("?");
					firstStatus = false;
					continue;
				}
				statusInQuery.append(", ?");
			}
			statusInQuery.append(")");

			final int totalResultCount = jdbcTemplate
					.queryForObject(String.format(countQuery.toString(), statusInQuery.toString()), Integer.class, args.toArray());
			final PaginationInfo paginationInfo = new PaginationInfo(totalResultCount, limit, offset);
			args.add(limit);
			args.add(offset);
			final List<RegistrationStudent> studentResponseList = jdbcTemplate.query(
					String.format(query.toString(), statusInQuery.toString()), args.toArray(),
					STUDENT_REGISTRATION_ROW_MAPPER);

			final SearchResultWithPagination<RegistrationStudent> resultWithPagination = new SearchResultWithPagination<>(
					paginationInfo, studentResponseList);
			return resultWithPagination;
		} catch (final Exception e) {
			logger.error("error while searching registered students for instituteId {}, academicSessionId {}",
					instituteId, academicSessionId, e);
		}
		return null;
	}

	public boolean updateStudentRegistrationStatus(UUID studentRegistrationId,
												   StudentRegistrationStatus studentRegistrationStatus) {

		try {
			return jdbcTemplate.update(UPDATE_STUDENT_STATUS,
					new Object[]{studentRegistrationStatus.name(), studentRegistrationId.toString()}) == 1;
		} catch (Exception e) {
			logger.error("Error while updating student registration status {}", studentRegistrationId, e);
		}
		return false;
	}

	public boolean updateStudentRegistrationPaymentData(int instituteId, UUID studentRegistrationId,
														  StudentRegistrationPaymentData paymentData) {

		try {
			long currentTime = System.currentTimeMillis();
			Timestamp transactionDate = new Timestamp(DateUtils.getDayStartDefaultTimezone(currentTime) * 1000l);
			Timestamp currentTimestamp = new Timestamp(currentTime);

			return jdbcTemplate.update(UPDATE_STUDENT_FEE_PAYMENT,
					new Object[]{paymentData.getPaidAmount(), paymentData.getDiscountAmount(),
							paymentData.getTransactionMode() == null ? null : paymentData.getTransactionMode().name(),
							transactionDate, StringUtils.isBlank(paymentData.getTransactionReference()) ? null :
							paymentData.getTransactionReference().trim(), currentTimestamp,
							paymentData.getTransactionAddedBy() == null ? null : paymentData.getTransactionAddedBy().toString(),
							instituteId, studentRegistrationId.toString()}) == 1;
		} catch (Exception e) {
			logger.error("Error while updating student payment data instituteId {}, studentRegistrationId {}, paymentData {}", instituteId, studentRegistrationId, paymentData, e);
		}
		return false;
	}
}
