/**
 * 
 */
package com.lernen.cloud.dao.tier.student.mappers;

import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.Stream;
import com.lernen.cloud.core.api.student.StudentCastClassWiseDetails;
import com.lernen.cloud.core.api.student.StudentCastReport;
import com.lernen.cloud.core.api.student.StudentCastReportRow;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.UserCategory;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 *
 */
public class StudentCastReportRowMapper implements RowMapper<StudentCastReportRow> {
	
	private static final String INSTITUTE_ID = "students.institute_id";
	private static final String STUDENT_COUNT = "student_count";
	private static final String CATEGORY = "category";
	private static final String GENDER = "gender";
	private static final String SPECIALLY_ABLED = "specially_abled";
	private static final String STANDARD_ID = "student_academic_session_details.standard_id";
	private static final String STANDARD_NAME = "standards.standard_name";
	private static final String STREAM_NAME = "standards.stream";
	
	public StudentCastReportRow mapRow(ResultSet rs, int rowNum) throws SQLException {
		Stream stream = Stream.getStream(rs.getString(STREAM_NAME));
		String standardName = rs.getString(STANDARD_NAME);
		String displayStandardName = Standard.getStandardDisplayName(stream, standardName);
		return new StudentCastReportRow(rs.getInt(INSTITUTE_ID), rs.getInt(STUDENT_COUNT),
				rs.getString(CATEGORY) == null ? null : UserCategory.getCategory(rs.getString(CATEGORY)), 
				rs.getBoolean(SPECIALLY_ABLED), rs.getString(GENDER) == null ? null : Gender.getGender(rs.getString(GENDER)), 
				UUID.fromString(rs.getString(STANDARD_ID)), displayStandardName);
	}

	public static StudentCastClassWiseDetails getStudentCastReport(List<StudentCastReportRow> studentCastReportRowList) {
		
		int instituteId = 0;
		Map<String, StudentCastReport> studentCastReport = new LinkedHashMap<>();
		Map<String, Map<Gender, Integer>> totalCount = new HashMap<>();
		
		for(StudentCastReportRow studentCastReportRow : studentCastReportRowList) {
			instituteId = studentCastReportRow.getInstituteId();

			if(studentCastReport.containsKey(studentCastReportRow.getStandardName())) {
				Map<Gender, Integer> totalGenderCount = totalCount.get(studentCastReportRow.getStandardName());
				
				if(studentCastReportRow.getSpeciallyAbled()) {
					if(studentCastReportRow.getGender() == Gender.FEMALE) {
						if(studentCastReport.get(studentCastReportRow.getStandardName()).getSpeciallyAbledStudentCount().containsKey(Gender.FEMALE)) {
							studentCastReport.get(studentCastReportRow.getStandardName()).getSpeciallyAbledStudentCount().put(Gender.FEMALE, studentCastReport.get(studentCastReportRow.getStandardName()).getSpeciallyAbledStudentCount().get(Gender.FEMALE) + studentCastReportRow.getStudentCount());
						}
						else {
							studentCastReport.get(studentCastReportRow.getStandardName()).getSpeciallyAbledStudentCount().put(Gender.FEMALE, studentCastReportRow.getStudentCount());
						}
					}
					else if(studentCastReportRow.getGender() == Gender.MALE) {
						if(studentCastReport.get(studentCastReportRow.getStandardName()).getSpeciallyAbledStudentCount().containsKey(Gender.MALE)) {
							studentCastReport.get(studentCastReportRow.getStandardName()).getSpeciallyAbledStudentCount().put(Gender.MALE, studentCastReport.get(studentCastReportRow.getStandardName()).getSpeciallyAbledStudentCount().get(Gender.MALE) + studentCastReportRow.getStudentCount());
						}
						else {
							studentCastReport.get(studentCastReportRow.getStandardName()).getSpeciallyAbledStudentCount().put(Gender.MALE, studentCastReportRow.getStudentCount());
						}
					}
				}
				
				if(studentCastReportRow.getCategory() != null) {
					if(studentCastReportRow.getGender() == Gender.FEMALE) {
						if(studentCastReport.get(studentCastReportRow.getStandardName()).getUserCategoryFemaleStudentCount().containsKey(studentCastReportRow.getCategory())) {
							studentCastReport.get(studentCastReportRow.getStandardName()).getUserCategoryFemaleStudentCount().put(studentCastReportRow.getCategory(), studentCastReport.get(studentCastReportRow.getStandardName()).getUserCategoryFemaleStudentCount().get(studentCastReportRow.getCategory()) + studentCastReportRow.getStudentCount());
						}
						else {
							studentCastReport.get(studentCastReportRow.getStandardName()).getUserCategoryFemaleStudentCount().put(studentCastReportRow.getCategory(), studentCastReportRow.getStudentCount());
						}
						
						totalGenderCount.put(Gender.FEMALE, totalGenderCount.get(Gender.FEMALE) + studentCastReportRow.getStudentCount());
					}
					else if(studentCastReportRow.getGender() == Gender.MALE) {
						if(studentCastReport.get(studentCastReportRow.getStandardName()).getUserCategoryMaleStudentCount().containsKey(studentCastReportRow.getCategory())) {
							studentCastReport.get(studentCastReportRow.getStandardName()).getUserCategoryMaleStudentCount().put(studentCastReportRow.getCategory(), studentCastReport.get(studentCastReportRow.getStandardName()).getUserCategoryMaleStudentCount().get(studentCastReportRow.getCategory()) + studentCastReportRow.getStudentCount());
						}
						else {
							studentCastReport.get(studentCastReportRow.getStandardName()).getUserCategoryMaleStudentCount().put(studentCastReportRow.getCategory(), studentCastReportRow.getStudentCount());
						}
						totalGenderCount.put(Gender.MALE, totalGenderCount.get(Gender.MALE) + studentCastReportRow.getStudentCount());
					}
				}
				
				studentCastReport.get(studentCastReportRow.getStandardName()).setTotalMale(totalGenderCount.get(Gender.MALE));
				studentCastReport.get(studentCastReportRow.getStandardName()).setTotalFemale(totalGenderCount.get(Gender.FEMALE));
			}
			else {
				Map<UserCategory, Integer> userCategoryMaleStudentCount = new HashMap<>();
				Map<UserCategory, Integer> userCategoryFemaleStudentCount = new HashMap<>();
				Map<Gender, Integer> speciallyAbledStudentCount = new HashMap<>();
				
				Map<Gender, Integer> totalGenderCount = new HashMap<>();
				totalGenderCount.put(Gender.MALE, 0);
				totalGenderCount.put(Gender.FEMALE, 0);
				
				totalCount.put(studentCastReportRow.getStandardName(), totalGenderCount);
				if(studentCastReportRow.getSpeciallyAbled()) {
					if(studentCastReportRow.getGender() == Gender.FEMALE) {
						if(speciallyAbledStudentCount.containsKey(Gender.FEMALE)) {
							speciallyAbledStudentCount.put(Gender.FEMALE, speciallyAbledStudentCount.get(Gender.FEMALE) + studentCastReportRow.getStudentCount());
						}
						else {
							speciallyAbledStudentCount.put(Gender.FEMALE, studentCastReportRow.getStudentCount());
						}
					}
					else if(studentCastReportRow.getGender() == Gender.MALE) {
						if(speciallyAbledStudentCount.containsKey(Gender.MALE)) {
							speciallyAbledStudentCount.put(Gender.MALE, speciallyAbledStudentCount.get(Gender.MALE) + studentCastReportRow.getStudentCount());
						}
						else {
							speciallyAbledStudentCount.put(Gender.MALE, studentCastReportRow.getStudentCount());
						}
					}
				}
				
				if(studentCastReportRow.getCategory() != null) {
					if(studentCastReportRow.getGender() == Gender.FEMALE) {
						if(userCategoryFemaleStudentCount.containsKey(studentCastReportRow.getCategory())) {
							userCategoryFemaleStudentCount.put(studentCastReportRow.getCategory(), userCategoryFemaleStudentCount.get(studentCastReportRow.getCategory()) + studentCastReportRow.getStudentCount());
						}
						else {
							userCategoryFemaleStudentCount.put(studentCastReportRow.getCategory(), studentCastReportRow.getStudentCount());
						}
						totalGenderCount.put(Gender.FEMALE, totalGenderCount.get(Gender.FEMALE) + studentCastReportRow.getStudentCount());
					}
					else if(studentCastReportRow.getGender() == Gender.MALE) {
						if(userCategoryMaleStudentCount.containsKey(studentCastReportRow.getCategory())) {
							userCategoryMaleStudentCount.put(studentCastReportRow.getCategory(), userCategoryMaleStudentCount.get(studentCastReportRow.getCategory()) + studentCastReportRow.getStudentCount());
						}
						else {
							userCategoryMaleStudentCount.put(studentCastReportRow.getCategory(), studentCastReportRow.getStudentCount());
						}
						totalGenderCount.put(Gender.MALE, totalGenderCount.get(Gender.MALE) + studentCastReportRow.getStudentCount());
					}
				}
				studentCastReport.put(studentCastReportRow.getStandardName(), new StudentCastReport(studentCastReportRow.getStandardId(), totalGenderCount.get(Gender.MALE), totalGenderCount.get(Gender.FEMALE),
						userCategoryMaleStudentCount, userCategoryFemaleStudentCount, speciallyAbledStudentCount));
				
			}
			
		}
		return new StudentCastClassWiseDetails(instituteId, studentCastReport); 
	}
}
