package com.lernen.cloud.dao.tier.student.mappers;

import com.embrate.cloud.core.api.student.registration.StudentRegistrationPaymentData;
import com.embrate.cloud.core.api.student.registration.StudentRegistrationPaymentStatus;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.common.AreaType;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.Stream;
import com.lernen.cloud.core.api.student.*;
import com.lernen.cloud.core.api.user.BloodGroup;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.utils.UUIDUtils;
import com.lernen.cloud.dao.tier.institute.mappers.AcademicSessionRowMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.WordUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class StudentRegistrationRowMapper implements RowMapper<RegistrationStudent> {
	private static final Logger logger = LogManager.getLogger(StudentRegistrationRowMapper.class);

	private static final AcademicSessionRowMapper ACADEMIC_SESSION_ROW_MAPPER = new AcademicSessionRowMapper();
	private static final Gson GSON = new Gson();

	private static final String INSTITUTE_UNIQUE_ID = "institute_unique_id";
	private static final String REGISTRATION_ID = "registration_id";
	private static final String INSTITUTE_REGISTRATION_ID = "institute_registration_id";
	private static final String REGISTRATION_DATE = "registration_date";
	private static final String NAME = "name";
	private static final String DATE_OF_BIRTH = "date_of_birth";
	private static final String BIRTH_PLACE = "birth_place";
	private static final String GENDER = "gender";
	private static final String CATEGORY = "category";
	private static final String MOTHER_TONGUE = "mother_tongue";
	private static final String AREA_TYPE = "area_type";
	private static final String SPECIALLY_ABLED = "specially_abled";
	private static final String BPL = "bpl";
	private static final String RELIGION = "religion";
	private static final String RTE = "rte";
	private static final String AADHAR_NUMBER = "aadhar_number";
	private static final String PERMANENT_ADDRESS = "permanent_address";
	private static final String PERMANENT_CITY = "permanent_city";
	private static final String PERMANENT_STATE = "permanent_state";
	private static final String PERMANENT_POST_OFFICE = "permanent_post_office";
	private static final String PERMANENT_POLICE_STATION = "permanent_police_station";
	private static final String PERMANENT_ZIPCODE = "permanent_zipcode";
	private static final String PERMANENT_COUNTRY = "permanent_country";
	private static final String PRESENT_ADDRESS = "present_address";
	private static final String PRESENT_CITY = "present_city";
	private static final String PRESENT_STATE = "present_state";
	private static final String PRESENT_POST_OFFICE = "present_post_office";
	private static final String PRESENT_POLICE_STATION = "present_police_station";
	private static final String PRESENT_ZIPCODE = "present_zipcode";
	private static final String PRESENT_COUNTRY = "present_country";
	private static final String NATIONALITY = "nationality";
	private static final String PRIMARY_CONTACT_NUMBER = "primary_contact_number";
	private static final String PRIMARY_EMAIL = "primary_email";
	private static final String FATHER_NAME = "father_name";
	private static final String MOTHER_NAME = "mother_name";
	protected static final String FATHER_QUALIFICATION = "father_qualification";
	protected static final String MOTHER_QUALIFICATION = "mother_qualification";
	private static final String FATHER_OCCUPATION = "father_occupation";
	private static final String FATHER_ANNUAL_INCOME  = "father_annual_income";
	private static final String MOTHER_OCCUPATION = "mother_occupation";
	private static final String MOTHER_ANNUAL_INCOME = "mother_annual_income";
	private static final String FATHER_CONTACT_NUMBER = "father_contact_number";
	private static final String MOTHER_CONTACT_NUMBER = "mother_contact_number";
	private static final String FATHER_AADHAR_NUMBER = "father_aadhar_number";
	private static final String MOTHER_AADHAR_NUMBER = "mother_aadhar_number";
	private static final String FATHER_PAN_CARD_DETAILS = "father_pan_card_details";
	private static final String MOTHER_PAN_CARD_DETAILS = "mother_pan_card_details";
	private static final String APPROX_FAMILY_INCOME = "family_approx_income";
	private static final String GUARDIANS_INFO_LIST = "guardians_details";
	private static final String SCHOOL_NAME = "previous_school_name";
	private static final String CLASS_PASSED = "class_passed";
	private static final String MEDIUM = "previous_school_medium";
	private static final String PERCENTAGE = "percentage";
	private static final String RESULT = "result";
	private static final String YEAR_OF_PASSING = "year_of_passing";
	private static final String BLOOD_GROUP = "blood_group";
	private static final String BLOOD_PRESSURE = "blood_pressure";
	private static final String PULSE = "pulse";
	private static final String HEIGHT = "height";
	private static final String WEIGHT = "weight";
	private static final String DATE_OF_PHYSICAL_EXAMINATION = "date_of_physical_examination";
	private static final String STATUS = "student_registration.status";
	private static final String STUDENT_DOCUMENTS = "student_documents";

	private static final String INSTITUTE_ID = "institute_id";
	private static final String STANDARD_ID = "standard_id";
	private static final String STANDARD_NAME = "standard_name";
	private static final String STREAM = "stream";
	private static final String LEVEL = "level";

	private static final String CASTE = "caste";

	private static final String IS_SPONSORED = "is_sponsored";
	private static final String WHATSAPP_NUMBER = "whatsapp_number";

	private static final String IS_ADMISSION_TC_BASED = "is_admission_tc_based";
	private static final String PREVIOUS_SCHOOL_TC_NUMBER = "previous_school_tc_number";

	private static final String SPECIALLY_ABLED_TYPE = "specially_abled_type";

	private static final String ASSIGNED_AMOUNT = "assigned_amount";
	private static final String PAID_AMOUNT = "paid_amount";
	private static final String DISCOUNT_AMOUNT = "discount_amount";
	private static final String TRANSACTION_MODE = "transaction_mode";
	private static final String TRANSACTION_DATE = "transaction_date";
	private static final String TRANSACTION_REFERENCE = "transaction_reference";
	private static final String TRANSACTION_ADDED_AT = "transaction_added_at";
	private static final String TRANSACTION_ADDED_BY = "transaction_added_by";


	@Override
	public RegistrationStudent mapRow(ResultSet rs, int rowNum) throws SQLException {
		final AcademicSession academicSession = ACADEMIC_SESSION_ROW_MAPPER.mapRow(rs, rowNum);

		Standard standard = new Standard(rs.getInt(INSTITUTE_ID), academicSession.getAcademicSessionId(),
				UUID.fromString(rs.getString(STANDARD_ID)), rs.getString(STANDARD_NAME),
				Stream.getStream(rs.getString(STREAM)), rs.getInt(LEVEL), null);

		final Timestamp dobTimestamp = rs.getTimestamp(DATE_OF_BIRTH);
		final Integer dobTime = dobTimestamp == null ? null : (int) (dobTimestamp.getTime() / 1000l);

		Boolean bpl = rs.getBoolean(BPL);
		if (rs.wasNull()) {
			bpl = null;
		}

		Boolean speciallyAbled = rs.getBoolean(SPECIALLY_ABLED);
		if (rs.wasNull()) {
			speciallyAbled = null;
		}

		final String documents = rs.getString(STUDENT_DOCUMENTS);
		List<Document<StudentDocumentType>> studentDocuments = null;
		if (!StringUtils.isBlank(documents)) {
			final Type collectionType = new TypeToken<List<Document<StudentDocumentType>>>() {
			}.getType();
			studentDocuments = GSON.fromJson(documents, collectionType);
		}

		boolean isSponsored = rs.getBoolean(IS_SPONSORED);
		if (rs.wasNull()) {
			isSponsored = false;
		}

		StudentBasicInfo studentBasicInfo = new StudentBasicInfo(null, null, rs.getString(INSTITUTE_REGISTRATION_ID), null, null,
				(int) (rs.getTimestamp(REGISTRATION_DATE).getTime() / 1000l), null, null, null,
				WordUtils.capitalizeFully(rs.getString(NAME)), dobTime,
				WordUtils.capitalizeFully(rs.getString(BIRTH_PLACE)), Gender.getGender(rs.getString(GENDER)),
				UserCategory.getCategory(rs.getString(CATEGORY)),
				WordUtils.capitalizeFully(rs.getString(MOTHER_TONGUE)), AreaType.getAreaType(rs.getString(AREA_TYPE)),
				bpl, speciallyAbled, rs.getString(SPECIALLY_ABLED_TYPE), WordUtils.capitalizeFully(rs.getString(RELIGION)), rs.getBoolean(RTE),
				rs.getString(AADHAR_NUMBER), WordUtils.capitalizeFully(rs.getString(PERMANENT_ADDRESS)),
				WordUtils.capitalizeFully(rs.getString(PERMANENT_CITY)),
				WordUtils.capitalizeFully(rs.getString(PERMANENT_STATE)),
				WordUtils.capitalizeFully(rs.getString(PERMANENT_POST_OFFICE)),
				WordUtils.capitalizeFully(rs.getString(PERMANENT_POLICE_STATION)),
				rs.getString(PERMANENT_ZIPCODE),
				rs.getString(PERMANENT_COUNTRY), WordUtils.capitalizeFully(rs.getString(PRESENT_ADDRESS)),
				WordUtils.capitalizeFully(rs.getString(PRESENT_CITY)),
				WordUtils.capitalizeFully(rs.getString(PRESENT_STATE)),
				WordUtils.capitalizeFully(rs.getString(PRESENT_POST_OFFICE)),
				WordUtils.capitalizeFully(rs.getString(PRESENT_POLICE_STATION)),
				rs.getString(PRESENT_ZIPCODE),
				rs.getString(PRESENT_COUNTRY), rs.getString(NATIONALITY), rs.getString(PRIMARY_CONTACT_NUMBER), rs.getString(PRIMARY_EMAIL),
				rs.getString(CASTE), null, isSponsored, false, rs.getString(WHATSAPP_NUMBER), null,
				null, null, null, null, null, null);

		final Timestamp dateOfPhysicalExamination = rs.getTimestamp(DATE_OF_PHYSICAL_EXAMINATION);
		final Integer dateOfPhysicalExaminationTime = dateOfPhysicalExamination == null ? null
				: (int) (dateOfPhysicalExamination.getTime() / 1000l);

		Integer yearOfPassing = rs.getInt(YEAR_OF_PASSING);
		if (rs.wasNull()) {
			yearOfPassing = null;
		}

		final StudentFamilyInfo studentFamilyInfo = new StudentFamilyInfo(
				WordUtils.capitalizeFully(rs.getString(MOTHER_NAME)),
				WordUtils.capitalizeFully(rs.getString(FATHER_NAME)),
				rs.getString(MOTHER_QUALIFICATION), rs.getString(FATHER_QUALIFICATION),
				rs.getString(MOTHER_CONTACT_NUMBER),
				rs.getString(FATHER_CONTACT_NUMBER), WordUtils.capitalizeFully(rs.getString(MOTHER_OCCUPATION)), rs.getString(MOTHER_ANNUAL_INCOME),
				WordUtils.capitalizeFully(rs.getString(FATHER_OCCUPATION)), rs.getString(FATHER_ANNUAL_INCOME), rs.getString(MOTHER_AADHAR_NUMBER),
				rs.getString(FATHER_AADHAR_NUMBER), rs.getString(MOTHER_PAN_CARD_DETAILS), rs.getString(FATHER_PAN_CARD_DETAILS), rs.getString(APPROX_FAMILY_INCOME));

		String guardianInfo = rs.getString(GUARDIANS_INFO_LIST);
		List<StudentGuardianInfo> studentGuardianInfoList = null;
		if (StringUtils.isNotBlank(guardianInfo)) {
			studentGuardianInfoList = GSON.fromJson(guardianInfo, new TypeToken<List<StudentGuardianInfo>>() {
			}.getType());
		}

		final StudentMedicalInfo studentMedicalInfo = new StudentMedicalInfo(
				BloodGroup.getBloodGroup(rs.getString(BLOOD_GROUP)), rs.getString(BLOOD_PRESSURE), rs.getString(PULSE),
				rs.getString(HEIGHT), rs.getString(WEIGHT), dateOfPhysicalExaminationTime);

		boolean isAdmissionTcBased = rs.getBoolean(IS_ADMISSION_TC_BASED);
		if (rs.wasNull()) {
			isAdmissionTcBased = false;
		}

		final StudentPreviousSchoolInfo studentPreviousSchoolInfo = new StudentPreviousSchoolInfo(isAdmissionTcBased,
				rs.getString(PREVIOUS_SCHOOL_TC_NUMBER),
				WordUtils.capitalizeFully(rs.getString(SCHOOL_NAME)), rs.getString(CLASS_PASSED),
				WordUtils.capitalizeFully(rs.getString(MEDIUM)), rs.getString(PERCENTAGE), rs.getString(RESULT),
				yearOfPassing);

		double assignedAmount = rs.getDouble(ASSIGNED_AMOUNT);
		double paidAmount = rs.getDouble(PAID_AMOUNT);
		double discountAmount = rs.getDouble(DISCOUNT_AMOUNT);
		TransactionMode transactionMode = TransactionMode.getTransactionMode(rs.getString(TRANSACTION_MODE));
		String transactionReference = rs.getString(TRANSACTION_REFERENCE);
		UUID transactionAddedBy = UUIDUtils.getUUID(rs.getString(TRANSACTION_ADDED_BY));

		final Timestamp transactionDateTs = rs.getTimestamp(TRANSACTION_DATE);
		final Integer transactionDate = transactionDateTs == null ? null : (int) (transactionDateTs.getTime() / 1000l);

		final Timestamp transactionAddedAtTs = rs.getTimestamp(TRANSACTION_ADDED_AT);
		final Long transactionAddedAt = transactionAddedAtTs == null ? null : transactionAddedAtTs.getTime();

		final StudentRegistrationPaymentData paymentData = new StudentRegistrationPaymentData(
				assignedAmount, paidAmount, discountAmount, transactionMode,
				transactionDate, transactionReference, transactionAddedAt,
				transactionAddedBy);

		StudentRegistrationPaymentStatus paymentStatus = Double.compare(assignedAmount, 0d) <= 0 ?
				StudentRegistrationPaymentStatus.NOT_APPLICABLE :
				Double.compare(assignedAmount, paidAmount + discountAmount) <= 0 ?
						StudentRegistrationPaymentStatus.PAID :
						StudentRegistrationPaymentStatus.DUE;

		return new RegistrationStudent(UUID.fromString(rs.getString(REGISTRATION_ID)), academicSession, standard,
				studentBasicInfo, studentFamilyInfo, studentGuardianInfoList, studentPreviousSchoolInfo,
				studentMedicalInfo, StudentRegistrationStatus.getStatus(rs.getString(STATUS)), studentDocuments,
				paymentStatus, paymentData);

	}

}
