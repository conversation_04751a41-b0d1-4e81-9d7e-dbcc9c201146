/**
 * 
 */
package com.lernen.cloud.dao.tier.student.mappers;

import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.Stream;
import com.lernen.cloud.core.api.student.StudentReligionClassWiseDetails;
import com.lernen.cloud.core.api.student.StudentReligionReport;
import com.lernen.cloud.core.api.student.StudentReligionReportRow;
import com.lernen.cloud.core.api.user.Gender;
import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 *
 */
public class StudentReligionReportRowMapper implements RowMapper<StudentReligionReportRow> {
	
	private static final String INSTITUTE_ID = "students.institute_id";
	private static final String STUDENT_COUNT = "student_count";
	private static final String RELIGION = "religion";
	private static final String GENDER = "gender";
	private static final String STANDARD_ID = "student_academic_session_details.standard_id";
	private static final String STANDARD_NAME = "standards.standard_name";
	private static final String STREAM_NAME = "standards.stream";
	
	public StudentReligionReportRow mapRow(ResultSet rs, int rowNum) throws SQLException {
		Stream stream = Stream.getStream(rs.getString(STREAM_NAME));
		String standardName = rs.getString(STANDARD_NAME);
		String displayStandardName = Standard.getStandardDisplayName(stream, standardName);
		return new StudentReligionReportRow(rs.getInt(INSTITUTE_ID), rs.getInt(STUDENT_COUNT),
				rs.getString(RELIGION), rs.getString(GENDER) == null ? null : Gender.getGender(rs.getString(GENDER)),
				UUID.fromString(rs.getString(STANDARD_ID)), displayStandardName);
	}

	public static StudentReligionClassWiseDetails getStudentReligionReport(List<StudentReligionReportRow> studentReligionReportRowList) {
		
		int instituteId = 0;
		Map<String, StudentReligionReport> studentReligionReport = new LinkedHashMap<>();
		Map<String, Map<Gender, Integer>> totalCount = new HashMap<>();
		List<String> religionList = new ArrayList<>();
		
		for(StudentReligionReportRow studentReligionReportRow : studentReligionReportRowList) {
			instituteId = studentReligionReportRow.getInstituteId();
			String standardName = studentReligionReportRow.getStandardName();
			if(studentReligionReport.containsKey(standardName)) {
				Map<Gender, Integer> totalGenderCount = totalCount.get(standardName);
				
				if(!StringUtils.isBlank(studentReligionReportRow.getReligion())) {
					String religion = studentReligionReportRow.getReligion().toLowerCase();
					if(!religionList.contains(religion)) {
						religionList.add(religion);
					}
					if(studentReligionReportRow.getGender() == Gender.FEMALE) {
						if(studentReligionReport.get(standardName).getReligionFemaleStudentCount().containsKey(religion)) {
							studentReligionReport.get(standardName).getReligionFemaleStudentCount().put(religion, studentReligionReport.get(standardName).getReligionFemaleStudentCount().get(religion) + studentReligionReportRow.getStudentCount());
						}
						else {
							studentReligionReport.get(standardName).getReligionFemaleStudentCount().put(religion, studentReligionReportRow.getStudentCount());
						}
						
						totalGenderCount.put(Gender.FEMALE, totalGenderCount.get(Gender.FEMALE) + studentReligionReportRow.getStudentCount());
					}
					else if(studentReligionReportRow.getGender() == Gender.MALE) {
						if(studentReligionReport.get(standardName).getReligionMaleStudentCount().containsKey(religion)) {
							studentReligionReport.get(standardName).getReligionMaleStudentCount().put(religion, studentReligionReport.get(standardName).getReligionMaleStudentCount().get(religion) + studentReligionReportRow.getStudentCount());
						}
						else {
							studentReligionReport.get(standardName).getReligionMaleStudentCount().put(religion, studentReligionReportRow.getStudentCount());
						}
						totalGenderCount.put(Gender.MALE, totalGenderCount.get(Gender.MALE) + studentReligionReportRow.getStudentCount());
					}
				}

				studentReligionReport.get(standardName).setTotalMale(totalGenderCount.get(Gender.MALE));
				studentReligionReport.get(standardName).setTotalFemale(totalGenderCount.get(Gender.FEMALE));
			}
			else {
				Map<String, Integer> religionMaleStudentCount = new HashMap<>();
				Map<String, Integer> religionFemaleStudentCount = new HashMap<>();
				
				Map<Gender, Integer> totalGenderCount = new HashMap<>();
				totalGenderCount.put(Gender.MALE, 0);
				totalGenderCount.put(Gender.FEMALE, 0);
				
				totalCount.put(standardName, totalGenderCount);
				if(!StringUtils.isBlank(studentReligionReportRow.getReligion())) {
					String religion = studentReligionReportRow.getReligion().toLowerCase();
					if(!religionList.contains(religion)) {
						religionList.add(religion);
					}
					if(studentReligionReportRow.getGender() == Gender.FEMALE) {
						if(religionFemaleStudentCount.containsKey(religion)) {
							religionFemaleStudentCount.put(religion, religionFemaleStudentCount.get(religion) + studentReligionReportRow.getStudentCount());
						}
						else {
							religionFemaleStudentCount.put(religion, studentReligionReportRow.getStudentCount());
						}
						totalGenderCount.put(Gender.FEMALE, totalGenderCount.get(Gender.FEMALE) + studentReligionReportRow.getStudentCount());
					}
					else if(studentReligionReportRow.getGender() == Gender.MALE) {
						if(religionMaleStudentCount.containsKey(religion)) {
							religionMaleStudentCount.put(religion, religionMaleStudentCount.get(religion) + studentReligionReportRow.getStudentCount());
						}
						else {
							religionMaleStudentCount.put(religion, studentReligionReportRow.getStudentCount());
						}
						totalGenderCount.put(Gender.MALE, totalGenderCount.get(Gender.MALE) + studentReligionReportRow.getStudentCount());
					}
				}
				studentReligionReport.put(standardName, new StudentReligionReport(studentReligionReportRow.getStandardId(), totalGenderCount.get(Gender.MALE), totalGenderCount.get(Gender.FEMALE),
						religionMaleStudentCount, religionFemaleStudentCount));
				
			}
			
		}
		return new StudentReligionClassWiseDetails(instituteId, religionList, studentReligionReport);
	}
}
