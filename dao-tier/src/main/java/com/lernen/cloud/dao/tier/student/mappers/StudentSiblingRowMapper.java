package com.lernen.cloud.dao.tier.student.mappers;

import com.lernen.cloud.core.api.student.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

public class StudentSiblingRowMapper implements RowMapper<StudentSiblingDetailsRow> {

    private static final StudentRowMapper STUDENT_ROW_MAPPER = new StudentRowMapper();
    private static final String SIBLING_GROUP_ID = "students.sibling_group_id";
    protected static final String INSTITUTE_ID = "students.institute_id";

    @Override
    public StudentSiblingDetailsRow mapRow(ResultSet rs, int rowNum) throws SQLException {
        int instituteId = rs.getInt(INSTITUTE_ID);
        UUID siblingGroupId = rs.getString(SIBLING_GROUP_ID) == null ? null :
                UUID.fromString(rs.getString(SIBLING_GROUP_ID));
        if(siblingGroupId == null) {
            return null;
        }
        final Student student = STUDENT_ROW_MAPPER.mapRow(rs, rowNum);
        return new StudentSiblingDetailsRow(instituteId, siblingGroupId, Student.getStudentLite(student));
    }
}
