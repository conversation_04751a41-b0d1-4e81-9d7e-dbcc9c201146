package com.lernen.cloud.dao.tier.tracking.events;

import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;

import com.lernen.cloud.core.api.tracking.events.TrackingEventModuleCount;
import com.lernen.cloud.dao.tier.tracking.events.mappers.TrackingEventModuleCountRowMapper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.CollectionUtils;

import com.google.gson.Gson;
import com.lernen.cloud.core.api.common.Channel;
import com.lernen.cloud.core.api.tracking.events.TrackingEventData;
import com.lernen.cloud.core.api.tracking.events.TrackingEventName;
import com.lernen.cloud.core.api.tracking.events.TrackingEventPayload;
import com.lernen.cloud.dao.tier.tracking.events.mappers.TrackingEventsRowMapper;

/**
 *
 * <AUTHOR>
 *
 */
public class TrackingEventsDao {
	private static final Logger logger = LogManager.getLogger(TrackingEventsDao.class);
	private static final Gson GSON = new Gson();
	private final JdbcTemplate jdbcTemplate;

	private static final TrackingEventsRowMapper TRACKING_EVENTS_ROW_MAPPER = new TrackingEventsRowMapper();
	private static final TrackingEventModuleCountRowMapper TRACKING_EVENTS_MODULE_COUNT_ROW_MAPPER = new TrackingEventModuleCountRowMapper();


	private static final String INSERT_TRACKING_EVENT = "insert into tracking_events(tracking_id, channel, event_name, "
			+ "event_time, ip_address, session_id, institute_id, user_id, meta_data) values (?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String GET_CHANNEL_TRACKING_EVENTS_IN_RANGE = "select * from tracking_events where channel = ? "
			+ " and event_time >= ? and event_time <= ? order by event_time desc";

	private static final String GET_PARTICULAR_TRACKING_EVENTS_IN_RANGE = "select * from tracking_events where channel = ? and event_name = ? "
			+ " and event_time >= ? and event_time <= ? order by event_time desc";

	private static final String GET_TRACKING_EVENTS_IN_RANGE = "select te.event_name, te.channel, count(*) as event_count from tracking_events te " +
					"inner join users u on te.user_id = u.user_id " +
					"where u.user_type <> 'INTERNAL' and te.institute_id = ? and te.event_time >= ? and te.event_time <= ? " +
					"group by te.event_name, te.channel;";

	private static final String GET_INSTITUTE_TRACKING_EVENTS_IN_RANGE = "select * from tracking_events where institute_id = ? and channel = ? "
			+ "and event_name = ? and event_time >= ? and event_time <= ? order by event_time desc";

	private static final String GET_INSTITUTE_CHANNEL_TRACKING_EVENTS_IN_RANGE = "select * from tracking_events where institute_id = ? and channel = ? "
			+ "and event_time >= ? and event_time <= ? order by event_time desc";

	public TrackingEventsDao(JdbcTemplate jdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
	}

	public boolean addTrackingEvent(TrackingEventPayload trackingEventPayload) {
		try {
			final UUID trackingId = UUID.randomUUID();
			return jdbcTemplate.update(INSERT_TRACKING_EVENT,
					new Object[] { trackingId.toString(), trackingEventPayload.getChannel().name(),
							trackingEventPayload.getTrackingEventName().name(),
							new Timestamp(trackingEventPayload.getEventTime()), trackingEventPayload.getIpAddress(),
							trackingEventPayload.getSessionId(), trackingEventPayload.getInstituteId(),
							trackingEventPayload.getUserId() == null ? null
									: trackingEventPayload.getUserId().toString(),
							CollectionUtils.isEmpty(trackingEventPayload.getMetaData()) ? null
									: GSON.toJson(trackingEventPayload.getMetaData()) }) == 1;
		} catch (final Exception e) {
			logger.error("Error occurred while saving tracking event {}", trackingEventPayload, e);
		}
		return false;
	}

	public List<TrackingEventData> getTrackingEvents(Channel channel, int start, int end) {
		try {
			return jdbcTemplate.query(GET_CHANNEL_TRACKING_EVENTS_IN_RANGE,
					new Object[] { channel.name(), new Timestamp(start * 1000l), new Timestamp(end * 1000l) },
					TRACKING_EVENTS_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error occurred while fetching tracking events for channel {}, start {}, end {}", channel,
					start, end, e);
		}
		return null;
	}

	public List<TrackingEventData> getTrackingEvents(Channel channel, TrackingEventName trackingEventName, int start,
			int end) {
		try {
			return jdbcTemplate.query(GET_PARTICULAR_TRACKING_EVENTS_IN_RANGE, new Object[] { channel.name(),
					trackingEventName.name(), new Timestamp(start * 1000l), new Timestamp(end * 1000l) },
					TRACKING_EVENTS_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error(
					"Error occurred while fetching tracking events for channel {}, trackingEventName {}, start {}, end {}",
					channel, trackingEventName, start, end, e);
		}
		return null;
	}

	public List<TrackingEventData> getTrackingEvents(int instituteId, Channel channel,
			TrackingEventName trackingEventName, int start, int end) {
		try {
			return jdbcTemplate.query(
					GET_INSTITUTE_TRACKING_EVENTS_IN_RANGE, new Object[] { instituteId, channel.name(),
							trackingEventName.name(), new Timestamp(start * 1000l), new Timestamp(end * 1000l) },
					TRACKING_EVENTS_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error(
					"Error occurred while fetching tracking events for instituteId {}, channel {}, trackingEventName {}, start {}, end {}",
					instituteId, channel, trackingEventName, start, end, e);
		}
		return null;
	}

	public List<TrackingEventData> getTrackingEvents(int instituteId, Channel channel, int start, int end) {
		try {
			return jdbcTemplate.query(GET_INSTITUTE_CHANNEL_TRACKING_EVENTS_IN_RANGE, new Object[] { instituteId,
					channel.name(), new Timestamp(start * 1000l), new Timestamp(end * 1000l) },
					TRACKING_EVENTS_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error(
					"Error occurred while fetching tracking events for instituteId {}, channel {}, start {}, end {}",
					instituteId, channel, start, end, e);
		}
		return null;
	}

	public List<TrackingEventModuleCount> getTrackingEventModuleUsageCount(int instituteId, int startDate, int endDate) {
		try {
			return jdbcTemplate.query(GET_TRACKING_EVENTS_IN_RANGE, new Object[] { instituteId, new Timestamp(startDate * 1000l), new Timestamp(endDate * 1000l) },
					TRACKING_EVENTS_MODULE_COUNT_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error(
					"Error occurred while fetching tracking events for instituteId {}",
					instituteId,  e);
		}
		return null;
	}

}
