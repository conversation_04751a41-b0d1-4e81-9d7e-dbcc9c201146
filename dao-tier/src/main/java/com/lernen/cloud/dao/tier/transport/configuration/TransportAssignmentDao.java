package com.lernen.cloud.dao.tier.transport.configuration;

import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.transport.*;
import com.lernen.cloud.dao.tier.student.mappers.StudentRowMapper;
import com.lernen.cloud.dao.tier.transport.configuration.mappers.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.text.StrBuilder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 */
public class TransportAssignmentDao {

    private static final Logger logger = LogManager.getLogger(TransportAssignmentDao.class);
//    private static final TransportHistoryRowMapper TRANSPORT_HISTORY_ROW_MAPPER = new TransportHistoryRowMapper();

    private static final StudentTransportAssignmentPaymentStatusRowMapper STUDENT_TRANSPORT_ASSIGNMENT_PAYMENT_STATUS_ROW_MAPPER = new StudentTransportAssignmentPaymentStatusRowMapper();
    private static final StudentTransportDataRowMapper STUDENT_TRANSPORT_DATA_ROW_MAPPER = new StudentTransportDataRowMapper();
    private static final StudentTransportFeeDataRowMapper STUDENT_TRANSPORT_FEE_DATA_ROW_MAPPER = new StudentTransportFeeDataRowMapper();
//    private static final TransportServiceRouteStatsRowMapper TRANSPORT_SERVICE_ROUTE_STATS_ROW_MAPPER = new TransportServiceRouteStatsRowMapper();
    private static final TransportAreaStatsRowMapper TRANSPORT_AREA_STATS_ROW_MAPPER = new TransportAreaStatsRowMapper();
    private static final StudentTransportAreaRowMapper STUDENT_TRANSPORT_AREA_ROW_MAPPER = new StudentTransportAreaRowMapper();


    private static final String ADD_TRANSPORT_HISTORY = "insert into transport_history(institute_id, transport_history_id, academic_session_id, student_id, pickup_service_route_id, drop_service_route_id, area_id, current_time_stamp, start_date, end_date, transport_status)"
            + "values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    private static final String UPDATE_TRANSPORT_STATUS = "update transport_history set transport_status = 'INACTIVE' where student_id = ? and "
            + "transport_status = 'ACTIVE' and academic_session_id = ? and institute_id = ?";
    private static final String ADD_TRANSPORT_HISTORY_FEE_ID_MAPPING = "insert into transport_history_fee_id_mapping(transport_history_id, fee_id, amount)"
            + "values(?, ?, ?)";
    private static final String GET_STUDENT_TRANSPORT_DATA = "select transport_history.*, transport_service_route.*, " +
            "transport_area.*, transport_vehicle.*, transport_vehicle_type.* from transport_history join transport_area on " +
            "transport_history.area_id = transport_area.area_id left join transport_service_route " +
            "on (transport_history.pickup_service_route_id = transport_service_route.service_route_id or transport_history.drop_service_route_id = " +
            "transport_service_route.service_route_id) left join transport_vehicle on transport_service_route.vehicle_id = transport_vehicle.vehicle_id " +
            "left join transport_vehicle_type on transport_vehicle_type.vehicle_type_id = transport_vehicle.vehicle_type_id " +
            "where transport_history.institute_id = ? and transport_history.academic_session_id = ? " +
            "and transport_history.transport_status = ? %s ";


    private static final String GET_TRANSPORT_FEE_AMOUNT = "select fee_configuration.*, transport_history_fee_id_mapping.* " +
            "from transport_history join transport_history_fee_id_mapping on " +
            "transport_history.transport_history_id = transport_history_fee_id_mapping.transport_history_id " +
            "join fee_configuration on transport_history_fee_id_mapping.fee_id = fee_configuration.fee_id " +
            "where transport_history.transport_history_id = ? ";

    private static final String GET_ACTIVE_TRANSPORT_FEE_AMOUNT_FOR_SESSION = "select fee_configuration.*, transport_history_fee_id_mapping.* " +
            "from transport_history join transport_history_fee_id_mapping on " +
            "transport_history.transport_history_id = transport_history_fee_id_mapping.transport_history_id " +
            "join fee_configuration on transport_history_fee_id_mapping.fee_id = fee_configuration.fee_id " +
            "where transport_history.institute_id = ? and transport_history.academic_session_id = ? and transport_history.transport_status = 'ACTIVE' ";

    private static final String GET_ACTIVE_TRANSPORT_STUDENT_COUNT = "select count(*) from transport_history where " +
            "transport_history.institute_id = ? and transport_history.academic_session_id = ? and transport_status = 'ACTIVE'";

    private static final String GET_ACTIVE_SERVICE_ROUTE_STUDENTS = "select transport_history.*, academic_session.*, students.*, "
            + " student_academic_session_details.*, standards.*, standard_section_mapping.* from transport_history join "
            + " academic_session on transport_history.academic_session_id = academic_session.academic_session_id  "
            + " join students on transport_history.student_id = students.student_id join student_academic_session_details on "
            + " transport_history.student_id = student_academic_session_details.student_id and transport_history.academic_session_id = "
            + " student_academic_session_details.academic_session_id join standards on student_academic_session_details.standard_id ="
            + " standards.standard_id left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id "
            + " and standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id and "
            + " student_academic_session_details.section_id = standard_section_mapping.section_id where "
            + " transport_history.institute_id = ? and transport_history.transport_status = 'ACTIVE' %s ";



    private static final String GET_ACTIVE_SERVICE_ROUTE_STUDENTS_TRANSPORT_DATA = "select transport_history.*, t.student_id, transport_area.parent_id" +
            " from student_academic_session_details join transport_history on " +
            " transport_history.student_id = student_academic_session_details.student_id and " +
            " student_academic_session_details.academic_session_id = transport_history.academic_session_id " +
            " and transport_history.transport_status = 'ACTIVE' left join (select distinct student_id from " +
            " student_fee_payments join fee_configuration on student_fee_payments.fee_id = fee_configuration.fee_id " +
            " where student_fee_payments.institute_id = ? and fee_configuration.academic_session_id = ? " +
            " and fee_head_id = ? and (paid_amount > 0 or instant_discount_amount > 0) ) t on " +
            " t.student_id = transport_history.student_id " +
            " left join transport_area on transport_area.area_id = transport_history.area_id" +
            " where student_academic_session_details.academic_session_id = ? and transport_history.institute_id = ? %s";


    private static final String GET_TRANSPORT_HISTORY = "select transport_history.*,"
            + " transport_history_fee_id_mapping.*, academic_session.*, students.*, student_academic_session_details.*, standards.*, "
            + " standard_section_mapping.*, transport_service_route.*,"
            + " transport_service_route_stoppages.*, transport_area.*, transport_vehicle.*, transport_vehicle_type.*, fee_configuration.* from transport_history join"
            + " transport_history_fee_id_mapping on transport_history.transport_history_id = transport_history_fee_id_mapping.transport_history_id"
            + " left join academic_session on transport_history.academic_session_id = academic_session.academic_session_id"
            + " left join students on transport_history.student_id = students.student_id join student_academic_session_details on "
            + " transport_history.student_id = student_academic_session_details.student_id and transport_history.academic_session_id = "
            + " student_academic_session_details.academic_session_id join standards on student_academic_session_details.standard_id ="
            + " standards.standard_id left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id "
            + " and standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id and "
            + " student_academic_session_details.section_id = standard_section_mapping.section_id "
            + " left join transport_service_route on transport_history.pickup_service_route_id = transport_service_route.service_route_id"
            + " left join transport_area on transport_history.area_id = transport_area.area_id"
            + " left join transport_service_route_stoppages on transport_service_route.service_route_id = transport_service_route_stoppages.service_route_id"
            + " and transport_service_route_stoppages.area_id = transport_area.area_id"
            + " left join transport_vehicle on transport_service_route.vehicle_id = transport_vehicle.vehicle_id"
            + " left join transport_vehicle_type on transport_vehicle_type.vehicle_type_id = transport_vehicle.vehicle_type_id "
            + " left join fee_configuration on transport_history_fee_id_mapping.fee_id = fee_configuration.fee_id where"
            + " transport_history.institute_id = ? and transport_history.academic_session_id = ? and transport_history.student_id = ? order by transport_history.current_time_stamp DESC limit ?";

//    private static final String GET_ACTIVE_TRANSPORT_FOR_ALL_STUDENTS = "select transport_history.*,"
//            + " transport_history_fee_id_mapping.*, academic_session.*, students.*, student_academic_session_details.*, standards.*, "
//            + " standard_section_mapping.*, transport_service_route.*,"
//            + " transport_service_route_stoppages.*, transport_area.*, transport_vehicle.*, transport_vehicle_type.*, fee_configuration.* from transport_history join"
//            + " transport_history_fee_id_mapping on transport_history.transport_history_id = transport_history_fee_id_mapping.transport_history_id"
//            + " join academic_session on transport_history.academic_session_id = academic_session.academic_session_id"
//            + " join students on transport_history.student_id = students.student_id join student_academic_session_details on "
//            + " transport_history.student_id = student_academic_session_details.student_id and transport_history.academic_session_id = "
//            + " student_academic_session_details.academic_session_id join standards on student_academic_session_details.standard_id ="
//            + " standards.standard_id left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id "
//            + " and standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id and "
//            + " student_academic_session_details.section_id = standard_section_mapping.section_id "
//            + " join transport_service_route on transport_history.service_route_id = transport_service_route.service_route_id"
//            + " join transport_area on transport_history.area_id = transport_area.area_id"
//            + " join transport_service_route_stoppages on transport_service_route.service_route_id = transport_service_route_stoppages.service_route_id"
//            + " and transport_service_route_stoppages.area_id = transport_area.area_id"
//            + " join transport_vehicle on transport_service_route.vehicle_id = transport_vehicle.vehicle_id"
//            + " join transport_vehicle_type on transport_vehicle_type.vehicle_type_id = transport_vehicle.vehicle_type_id "
//            + " left join fee_configuration on transport_history_fee_id_mapping.fee_id = fee_configuration.fee_id where"
//            + " transport_history.institute_id = ? and transport_history.academic_session_id = ? and transport_history.transport_status = 'ACTIVE'";

    private static final String GET_TRANSPORT_AREA_WITH_STUDENT_COUNT = "select transport_area.*, area_count.student_count from transport_area "
            + " left join (select area_id, count(transport_history.student_id) as student_count from transport_history "
            + " inner join student_academic_session_details on student_academic_session_details.student_id = transport_history.student_id and student_academic_session_details.academic_session_id = transport_history.academic_session_id "
            + " where transport_history.academic_session_id = ? and transport_history.institute_id = ? and transport_history.transport_status = 'ACTIVE' and student_academic_session_details.session_status = 'ENROLLED' group by area_id order by student_count desc) "
            + " area_count on area_count.area_id = "
            + " transport_area.area_id where transport_area.institute_id = ? order by transport_area.area asc ";

    private static final String GET_STUDENT_COUNT_WITH_TRANSPORT_ACTIVE_STATUS = " select count(distinct transport_history.student_id) as active_student_count from  transport_history inner join student_academic_session_details on student_academic_session_details.academic_session_id = transport_history.academic_session_id and student_academic_session_details.student_id = transport_history.student_id where transport_history.institute_id = ? and transport_history.academic_session_id = ? and transport_history.transport_status = 'ACTIVE' and student_academic_session_details.session_status = 'ENROLLED'";

    private static final String DELETE_TRANSPORT_HISTROY_FEE_ID_MAPPING = "delete transport_history_fee_id_mapping.* "
            + " from transport_history_fee_id_mapping "
            + " inner join transport_history on transport_history_fee_id_mapping.transport_history_id = transport_history.transport_history_id "
            + " where student_id = ? and academic_session_id = ? and institute_id = ? ";
    private static final String DELETE_TRANSPORT = " delete  from transport_history "
            + " where student_id = ? and academic_session_id = ? and institute_id = ? ";
    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;

    public TransportAssignmentDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
    }

    public List<TransportAreaStats> getAreaStats(int instituteId, int academicSessionId) {
        try {
            
            return jdbcTemplate.query(GET_TRANSPORT_AREA_WITH_STUDENT_COUNT,
                    new Object[]{academicSessionId, instituteId, instituteId},
                    TRANSPORT_AREA_STATS_ROW_MAPPER);
        } catch (final Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public int getTransportActiveStudentCount(int instituteId, int academicSessionId) {
        try {
            Integer studentCount = jdbcTemplate.queryForObject(GET_STUDENT_COUNT_WITH_TRANSPORT_ACTIVE_STATUS,
                    new Object[]{instituteId, academicSessionId}, Integer.class);
            return studentCount == null ? 0 : studentCount;
        } catch (final Exception e) {
             logger.error("Error while getting Transport Active students count for instituteId {} ", instituteId, e);
        }
        return 0;
    }

    public Boolean addTransportHistoryWithTransaction(TransportAssignmentPayload transportHistoryPayload) {
        try {
            final Boolean addedTransportHistory = transactionTemplate.execute(new TransactionCallback<Boolean>() {

                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    return addTransportHistory(transportHistoryPayload.getInstituteId(), transportHistoryPayload.getAcademicSessionId(), transportHistoryPayload);
                }
            });
            return addedTransportHistory;
        } catch (final Exception e) {
            System.out.println("Unable to execute transaction");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * This method must be used inside a transaction
     *
     * @param transportHistoryPayload
     * @return
     */
    public Boolean addTransportHistoryWithoutTransaction(int instituteId, int academicSessionId, TransportAssignmentPayload transportHistoryPayload) {
        return addTransportHistory(instituteId, academicSessionId, transportHistoryPayload);
    }

    /**
     * This method must be used inside a transaction
     *
     * @return
     */
    public Boolean addTransportHistoryWithoutTransaction(int instituteId, int academicSessionId, List<TransportAssignmentPayload> transportHistoryPayloadList) {
        return addTransportHistory(instituteId, academicSessionId, transportHistoryPayloadList);
    }

    private Boolean addTransportHistory(int instituteId, int academicSessionId, TransportAssignmentPayload transportHistoryPayload) {
        return addTransportHistory(instituteId, academicSessionId, Arrays.asList(transportHistoryPayload));
    }

    private Boolean addTransportHistory(int instituteId, int academicSessionId, List<TransportAssignmentPayload> transportHistoryPayloadList) {
        Set<UUID> studentIds = new HashSet<>();
        for(TransportAssignmentPayload transportHistoryPayload : transportHistoryPayloadList){
            studentIds.add(transportHistoryPayload.getStudentId());
        }
        markExistingTransportServiceInactive(instituteId, academicSessionId, studentIds);

        return addTransportHistoryWithFeeIdAmounts(
                instituteId, academicSessionId, transportHistoryPayloadList);
    }

//    public boolean markExistingTransportServiceInactive(UUID studentId, int academicSessionId, int instituteId) {
//        return jdbcTemplate.update(UPDATE_TRANSPORT_STATUS, studentId.toString(), academicSessionId, instituteId) >= 0;
//    }

    public boolean markExistingTransportServiceInactive(int instituteId, int academicSessionId, Set<UUID> studentIds) {
        List<Object []> args = new ArrayList<>();
        for(UUID studentId : studentIds){
            args.add(new Object[]{studentId.toString(), academicSessionId, instituteId});
        }
        return jdbcTemplate.batchUpdate(UPDATE_TRANSPORT_STATUS, args).length == studentIds.size();
    }

//    public Boolean addTransportHistoryWithFeeIdAmounts(TransportAssignmentPayload transportHistoryPayload) {
//        try {
//            final UUID transportHistoryId = UUID.randomUUID();
//            final long currentTime = System.currentTimeMillis();
//            final int row = jdbcTemplate.update(ADD_TRANSPORT_HISTORY, transportHistoryPayload.getInstituteId(),
//                    transportHistoryId.toString(), transportHistoryPayload.getAcademicSessionId(),
//                    transportHistoryPayload.getStudentId().toString(),
//                    transportHistoryPayload.getPickupServiceRouteId() == null ? null : transportHistoryPayload.getPickupServiceRouteId().toString(),
//                    transportHistoryPayload.getDropServiceRouteId() == null ? null : transportHistoryPayload.getDropServiceRouteId().toString(),
//                    transportHistoryPayload.getAreaId(),
//                    new Timestamp(currentTime),
//                    transportHistoryPayload.getStartDate() > 0
//                            ? new Timestamp(transportHistoryPayload.getStartDate() * 1000l) : null,
//                    transportHistoryPayload.getEndDate() > 0
//                            ? new Timestamp(transportHistoryPayload.getEndDate() * 1000l) : null,
//                    transportHistoryPayload.getTransportStatus().name());
//            if (row == 1) {
//                final List<Object[]> batchInsertArgs = new ArrayList<>();
//                for (final TransportHistoryFeeIdAmount studentTransportHistoryFeeIdAmount : transportHistoryPayload
//                        .getTransportHistoryFeeIdAmountList()) {
//                    final List<Object> args = new ArrayList<>();
//                    args.add(transportHistoryId.toString());
//                    args.add(studentTransportHistoryFeeIdAmount.getFeeId().toString());
//                    args.add(studentTransportHistoryFeeIdAmount.getAmount());
//                    batchInsertArgs.add(args.toArray());
//                }
//
//                final int[] rows = jdbcTemplate.batchUpdate(ADD_TRANSPORT_HISTORY_FEE_ID_MAPPING, batchInsertArgs);
//                if (rows.length != transportHistoryPayload.getTransportHistoryFeeIdAmountList().size()) {
//                    return null;
//                }
//            }
//            return true;
//        } catch (final DataAccessException dataAccessException) {
//            System.out.println(dataAccessException);
//            dataAccessException.printStackTrace();
//        } catch (final Exception e) {
//            System.out.println("Exception " + e.getMessage());
//            e.printStackTrace();
//        }
//        return null;
//    }

    public Boolean addTransportHistoryWithFeeIdAmounts(int instituteId, int academicSessionId, List<TransportAssignmentPayload> transportHistoryPayloadList) {
        List<Object[]> metaArgs = new ArrayList<>();
        List<Object[]> mappingArgs = new ArrayList<>();
        for (TransportAssignmentPayload transportHistoryPayload : transportHistoryPayloadList) {
            final UUID transportHistoryId = UUID.randomUUID();
            final long currentTime = System.currentTimeMillis();
            metaArgs.add(new Object[]{instituteId, transportHistoryId.toString(), academicSessionId, transportHistoryPayload.getStudentId().toString(),
                    transportHistoryPayload.getPickupServiceRouteId() == null ? null : transportHistoryPayload.getPickupServiceRouteId().toString(),
                    transportHistoryPayload.getDropServiceRouteId() == null ? null : transportHistoryPayload.getDropServiceRouteId().toString(),
                    transportHistoryPayload.getAreaId(),
                    new Timestamp(currentTime),
                    transportHistoryPayload.getStartDate() > 0
                            ? new Timestamp(transportHistoryPayload.getStartDate() * 1000l) : null,
                    transportHistoryPayload.getEndDate() > 0
                            ? new Timestamp(transportHistoryPayload.getEndDate() * 1000l) : null,
                    transportHistoryPayload.getTransportStatus().name()});


            for (final TransportHistoryFeeIdAmount studentTransportHistoryFeeIdAmount : transportHistoryPayload
                    .getTransportHistoryFeeIdAmountList()) {
                final List<Object> args = new ArrayList<>();
                args.add(transportHistoryId.toString());
                args.add(studentTransportHistoryFeeIdAmount.getFeeId().toString());
                args.add(studentTransportHistoryFeeIdAmount.getAmount());
                mappingArgs.add(args.toArray());
            }
        }

        final int[] metaRows = jdbcTemplate.batchUpdate(ADD_TRANSPORT_HISTORY, metaArgs);
        if (metaRows.length != transportHistoryPayloadList.size()) {
            logger.error("Unable to add metadata for instituteId {}, academicSessionId {}", instituteId, academicSessionId);
            throw new EmbrateRunTimeException("Unable to add metadata");
        }

        final int[] rows = jdbcTemplate.batchUpdate(ADD_TRANSPORT_HISTORY_FEE_ID_MAPPING, mappingArgs);
        if (rows.length != mappingArgs.size()) {
            logger.error("Unable to add fees mappings for instituteId {}, academicSessionId {}", instituteId, academicSessionId);
            throw new EmbrateRunTimeException("Unable to add fee mappings");
        }
        return true;

    }

    public Map<Integer, List<Student>> getServiceRouteStudents(int instituteId, UUID serviceRouteId, RouteType routeType){
        String routeWhereClause = "";
        if(routeType == RouteType.PICK_UP){
            routeWhereClause = " and pickup_service_route_id = ? ";
        }
        if(routeType == RouteType.DROP_OFF){
            routeWhereClause = " and drop_service_route_id = ? ";
        }
        try{
            final Object[] args = {instituteId, serviceRouteId.toString()};
            return StudentTransportAreaRowMapper.getAreaWiseStudents(jdbcTemplate.query(String.format(GET_ACTIVE_SERVICE_ROUTE_STUDENTS, routeWhereClause) , args, STUDENT_TRANSPORT_AREA_ROW_MAPPER));
        }catch (Exception e){
            logger.error("Error while getting service route students for instituteId {}, serviceRouteId {}, routeType {} ", instituteId, serviceRouteId, routeType, e);
        }
        return null;
    }

//    public StudentTransportDetails getStudentCurrentTransportAssignmentDetails(int instituteId, int academicSessionId,
//                                                                               UUID studentId) {
//        try {
//            final Object[] args = {instituteId, academicSessionId, studentId.toString(), 4};
//            return TransportHistoryRowMapper
//                    .getTransportHistory(jdbcTemplate.query(GET_TRANSPORT_HISTORY, args, TRANSPORT_HISTORY_ROW_MAPPER));
//        } catch (final DataAccessException dataAccessException) {
//            System.out.println(dataAccessException);
//            dataAccessException.printStackTrace();
//        } catch (final Exception e) {
//            System.out.println("Exception " + e.getMessage());
//            e.printStackTrace();
//        }
//
//        return null;
//    }

    public StudentTransportData getStudentCurrentTransportData(int instituteId, int academicSessionId,
                                                                         UUID studentId) {
        try {
            final Object[] args = {instituteId, academicSessionId, TransportStatus.ACTIVE.name(), studentId.toString()};
            return StudentTransportDataRowMapper
                    .getStudentTransportData(jdbcTemplate.query(String.format(GET_STUDENT_TRANSPORT_DATA, " and transport_history.student_id = ? "), args, STUDENT_TRANSPORT_DATA_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while getting student current transport data instituteId {}, academicSessionId {}, studentId {}", instituteId, academicSessionId, studentId, e);
        }
        return null;
    }

    public List<StudentTransportData> getStudentInactiveTransportData(int instituteId, int academicSessionId,
                                                                         UUID studentId) {
        try {
            final Object[] args = {instituteId, academicSessionId, TransportStatus.INACTIVE.name(), studentId.toString()};
            return StudentTransportDataRowMapper
                    .getStudentTransportDataList(jdbcTemplate.query(String.format(GET_STUDENT_TRANSPORT_DATA, " and transport_history.student_id = ? "), args, STUDENT_TRANSPORT_DATA_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while getting student inactive transport data instituteId {}, academicSessionId {}, studentId {}", instituteId, academicSessionId, studentId, e);
        }
        return null;
    }

    public List<StudentTransportFeeAmount> getTransportFeeAmounts(UUID transportHistoryId) {
        try {
            final Object[] args = {transportHistoryId.toString()};
            return StudentTransportFeeDataRowMapper.getTransportFeeAmounts(jdbcTemplate.query(GET_TRANSPORT_FEE_AMOUNT, args, STUDENT_TRANSPORT_FEE_DATA_ROW_MAPPER));

        } catch (final Exception e) {
            logger.error("Error while getting transport fee assignment, transportHistoryId {}", transportHistoryId, e);
        }
        return null;
    }

    public Map<UUID, List<StudentTransportFeeAmount>> getTransportFeeAmounts(int instituteId, int academicSessionId) {
        try {
            final Object[] args = {instituteId, academicSessionId};
            return StudentTransportFeeDataRowMapper.getTransportFeeAmountsMap(jdbcTemplate.query(GET_ACTIVE_TRANSPORT_FEE_AMOUNT_FOR_SESSION, args, STUDENT_TRANSPORT_FEE_DATA_ROW_MAPPER));

        } catch (final Exception e) {
            logger.error("Error while getting transport fee assignment, academicSessionId {}", academicSessionId, e);
        }
        return null;
    }



    public Boolean anyActiveTransportAssignmentExists(int instituteId, int academicSessionId) {
        try {
            final Object[] args = {instituteId, academicSessionId};
            return jdbcTemplate.queryForInt(GET_ACTIVE_TRANSPORT_STUDENT_COUNT, args) > 0;

        } catch (final Exception e) {
            logger.error("Error while getting active transport, instituteId {}, academicSessionId {}",instituteId, academicSessionId, e);
        }
        return null;
    }


    public List<StudentTransportData> getStudentCurrentTransportData(int instituteId, int academicSessionId) {
        try {
            final Object[] args = {instituteId, academicSessionId, TransportStatus.ACTIVE.name()};
            return StudentTransportDataRowMapper
                    .getStudentTransportDataList(jdbcTemplate.query(String.format(GET_STUDENT_TRANSPORT_DATA, ""), args, STUDENT_TRANSPORT_DATA_ROW_MAPPER));
        } catch (final Exception e) {
            e.printStackTrace();
            logger.error("Error while getting current transport data for students in instituteId {}, academicSessionId {}", instituteId, academicSessionId, e);
        }
        return null;
    }

    public List<StudentTransportAssignmentPaymentStatus> getStudentTransportAssignmentPaymentStatus(int instituteId, int academicSessionId, Set<UUID> standardIds, int transportFeeHeadId) {
        try {
            StringBuilder sb = new StringBuilder();
            List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(academicSessionId);
            args.add(transportFeeHeadId);
            args.add(academicSessionId);
            args.add(instituteId);

            if(CollectionUtils.isNotEmpty(standardIds)){
                sb.append(" and student_academic_session_details.standard_id in (");
                String delimiter = "";
                for(UUID standardId : standardIds){
                    args.add(standardId.toString());
                    sb.append(delimiter).append(" ?");
                    delimiter = ",";
                }
                sb.append(")");
            }
            return jdbcTemplate.query(String.format(GET_ACTIVE_SERVICE_ROUTE_STUDENTS_TRANSPORT_DATA, sb), args.toArray(), STUDENT_TRANSPORT_ASSIGNMENT_PAYMENT_STATUS_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error while getting current transport payment status data in instituteId {}, academicSessionId {}, standardIds {}, transportFeeHeadId {}", instituteId, academicSessionId, standardIds, transportFeeHeadId, e);
        }
        return null;
    }



//    public List<StudentTransportDetails> getAllStudentActiveTransportAssignmentDetails(int instituteId,
//                                                                                       int academicSessionId) {
//        try {
//            final Object[] args = {instituteId, academicSessionId};
//            return TransportHistoryRowMapper.getTransportHistoryList(
//                    jdbcTemplate.query(GET_ACTIVE_TRANSPORT_FOR_ALL_STUDENTS, args, TRANSPORT_HISTORY_ROW_MAPPER));
//        } catch (final DataAccessException dataAccessException) {
//            System.out.println(dataAccessException);
//            dataAccessException.printStackTrace();
//        } catch (final Exception e) {
//            System.out.println("Exception " + e.getMessage());
//            e.printStackTrace();
//        }
//
//        return null;
//    }

//    public List<StudentTransportDetails> getStudentTransportHistoryList(int instituteId, int academicSessionId,
//                                                                        UUID studentId) {
//        try {
//            System.out.println(GET_TRANSPORT_HISTORY);
//            final Object[] args = {instituteId, academicSessionId, studentId.toString(), 1000000};
//            return TransportHistoryRowMapper.getTransportHistoryList(
//                    jdbcTemplate.query(GET_TRANSPORT_HISTORY, args, TRANSPORT_HISTORY_ROW_MAPPER));
//        } catch (final DataAccessException dataAccessException) {
//            System.out.println(dataAccessException);
//            dataAccessException.printStackTrace();
//        } catch (final Exception e) {
//            System.out.println("Exception " + e.getMessage());
//            e.printStackTrace();
//        }
//
//        return null;
//    }

    public boolean deleteTransportConfigurationByStudent(int instituteId, int academicSessionId, UUID entityId) {
        try {

            Object[] args = {entityId.toString(), academicSessionId, instituteId};
            boolean deleted = jdbcTemplate.update(DELETE_TRANSPORT_HISTROY_FEE_ID_MAPPING, args) > 0;

            if (!deleted) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS,
                        "Error occured while deleting assigned fees. Please try again."));
            }

            deleted = jdbcTemplate.update(DELETE_TRANSPORT, args) > 0;

            if (!deleted) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS,
                        "Error occured while deleting assigned fees. Please try again."));
            }

            return true;

        } catch (final Exception e) {
            e.printStackTrace();
        }
        return false;

    }
}
