package com.lernen.cloud.dao.tier.transport.configuration.mappers;

import com.lernen.cloud.core.api.transport.*;
import com.lernen.cloud.dao.tier.fees.configuration.mappers.FeeConfigurationBasicInfoRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentTransportDataRowMapper implements RowMapper<StudentTransportDataRow> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String TRANSPORT_HISTORY_ID = "transport_history_id";
	private static final TransportServiceRouteMetadataRowMapper TRANSPORT_SERVICE_ROUTE_METADATA_ROW_MAPPER = new TransportServiceRouteMetadataRowMapper();
	private static final TransportAreaRowMapper TRANSPORT_AREA_ROW_MAPPER = new TransportAreaRowMapper();
	private static final String CURRENT_TIME_STAMP = "current_time_stamp";
	private static final String START_DATE = "start_date";
	private static final String END_DATE = "end_date";
	private static final String TRANSPORT_STATUS = "transport_status";
	private static final String ACADEMIC_SESSION_ID = "academic_session_id";
	private static final String STUDENT_ID = "student_id";

	@Override
	public StudentTransportDataRow mapRow(ResultSet rs, int rowNum) throws SQLException {

		TransportServiceRouteMetadata transportServiceRouteMetadata = TRANSPORT_SERVICE_ROUTE_METADATA_ROW_MAPPER.mapRow(rs, rowNum);
		final TransportArea transportArea = TRANSPORT_AREA_ROW_MAPPER.mapRow(rs, rowNum);

		final Timestamp currentTimeStamp = rs.getTimestamp(CURRENT_TIME_STAMP);
		final Integer currentTime = currentTimeStamp == null ? 0 : (int) (currentTimeStamp.getTime() / 1000l);

		final Timestamp startDateTimeStamp = rs.getTimestamp(START_DATE);
		final Integer startDate = startDateTimeStamp == null ? 0 : (int) (startDateTimeStamp.getTime() / 1000l);

		final Timestamp endDateTimeStamp = rs.getTimestamp(END_DATE);
		final Integer endDate = endDateTimeStamp == null ? 0 : (int) (endDateTimeStamp.getTime() / 1000l);

		return new StudentTransportDataRow(rs.getInt(INSTITUTE_ID),
				UUID.fromString(rs.getString(TRANSPORT_HISTORY_ID)), rs.getInt(ACADEMIC_SESSION_ID), UUID.fromString(rs.getString(STUDENT_ID)),
				transportServiceRouteMetadata, transportArea, currentTime, startDate, endDate,
				TransportStatus.getTransportStatus(rs.getString(TRANSPORT_STATUS)));
	}



	/**
	 * This method assumes all rows belong to same user and transport history id.
	 * So that at most 2 items will be present in list
	 *
	 * @return
	 */
	public static StudentTransportData getStudentTransportData(
			List<StudentTransportDataRow> studentTransportDataRows) {
		if (CollectionUtils.isEmpty(studentTransportDataRows)) {
			return null;
		}

		StudentTransportDataRow pickupStudentTransportDataRow = null;
		StudentTransportDataRow dropStudentTransportDataRow = null;
		for(StudentTransportDataRow studentTransportDataRow : studentTransportDataRows){
			if(studentTransportDataRow.getTransportServiceRouteMetadata().getRouteType() == RouteType.PICK_UP){
				pickupStudentTransportDataRow = studentTransportDataRow;
			}
			else if(studentTransportDataRow.getTransportServiceRouteMetadata().getRouteType() == RouteType.DROP_OFF){
				dropStudentTransportDataRow = studentTransportDataRow;
			}
		}

		final StudentTransportDataRow firstRow = studentTransportDataRows.get(0);
		return new StudentTransportData(firstRow.getInstituteId(), firstRow.getTransportHistoryId(), firstRow.getAcademicSessionId(), firstRow.getStudentId(),
				pickupStudentTransportDataRow == null ? null : pickupStudentTransportDataRow.getTransportServiceRouteMetadata(),
				dropStudentTransportDataRow == null ? null : dropStudentTransportDataRow.getTransportServiceRouteMetadata(), firstRow.getTransportArea(),
				firstRow.getCurrentTimestamp(), firstRow.getStartDate(), firstRow.getEndDate(), firstRow.getTransportStatus());
	}

	public static List<StudentTransportData> getStudentTransportDataList(List<StudentTransportDataRow> studentTransportDataRows){
		if (CollectionUtils.isEmpty(studentTransportDataRows)) {
			return new ArrayList<>();
		}
		Map<UUID,  List<StudentTransportDataRow>> transportIdDataMap = new HashMap<>();
		for(StudentTransportDataRow studentTransportDataRow : studentTransportDataRows){
			if(!transportIdDataMap.containsKey(studentTransportDataRow.getTransportHistoryId())){
				transportIdDataMap.put(studentTransportDataRow.getTransportHistoryId(), new ArrayList<>());
			}
			transportIdDataMap.get(studentTransportDataRow.getTransportHistoryId()).add(studentTransportDataRow);
		}
		List<StudentTransportData> studentTransportDataList = new ArrayList<>();
		for(List<StudentTransportDataRow> transportIdRow : transportIdDataMap.values()){
			studentTransportDataList.add(getStudentTransportData(transportIdRow));
		}

		return studentTransportDataList;
	}

}
