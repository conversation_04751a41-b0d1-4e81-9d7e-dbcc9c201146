package com.lernen.cloud.dao.tier.transport.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.transport.TransportParentArea;

public class TransportParentAreaRowMapper implements RowMapper<TransportParentArea> {

    private static final String INSTITUTE_ID = "transport_parent_area.institute_id";
	private static final String PARENT_ID = "transport_parent_area.parent_id";
	private static final String PARENT_NAME = "transport_parent_area.parent_name";

	@Override
	public TransportParentArea mapRow(ResultSet rs, int rowNum) throws SQLException {
		if(rs.getString(PARENT_ID) == null) {
			return null;
		}
		return new TransportParentArea(rs.getInt(INSTITUTE_ID),  rs.getString(PARENT_ID) == null ? null : UUID.fromString(rs.getString(PARENT_ID)), rs.getString(PARENT_NAME));
	}
    
}
