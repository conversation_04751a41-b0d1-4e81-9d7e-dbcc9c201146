package com.lernen.cloud.dao.tier.transport.configuration.mappers;

import com.lernen.cloud.core.api.transport.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 */
public class TransportServiceRouteMetadataRowMapper implements RowMapper<TransportServiceRouteMetadata> {

    private static final String INSTITUTE_ID = "institute_id";
    private static final String ACADEMIC_SESSION_ID = "academic_session_id";
    private static final String SERVICE_ROUTE_ID = "service_route_id";
    private static final String SERVICE_ROUTE_NAME = "service_route_name";
    private static final String SERVICE_ROUTE_TYPE = "route_type";
    private static final String SERVICE_ROUTE_DEFAULT_DRIVER = "default_driver";
    private static final String SERVICE_ROUTE_DEFAULT_CONDUCTOR = "default_conductor";
    private static final TransportVehicleRowMapper TRANSPORT_VEHICLE_ROW_MAPPER = new TransportVehicleRowMapper();

    @Override
    public TransportServiceRouteMetadata mapRow(ResultSet rs, int rowNum) throws SQLException {

        final Vehicle vehicle = TRANSPORT_VEHICLE_ROW_MAPPER.mapRow(rs, rowNum);
//        UUID defaultDriver = null;
//        UUID defaultConductor = null;
//        String defaultDriverStr = rs.getString(SERVICE_ROUTE_DEFAULT_DRIVER);
//        String defaultConductorStr = rs.getString(SERVICE_ROUTE_DEFAULT_CONDUCTOR);
//        if (StringUtils.isNotBlank(defaultDriverStr)) {
//            defaultDriver = UUID.fromString(defaultDriverStr);
//        }
//        if (StringUtils.isNotBlank(defaultConductorStr)) {
//            defaultConductor = UUID.fromString(defaultConductorStr);
//        }
        return new TransportServiceRouteMetadata(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID),
                UUID.fromString(rs.getString(SERVICE_ROUTE_ID)), rs.getString(SERVICE_ROUTE_NAME), vehicle, RouteType.getRouteType(rs.getString(SERVICE_ROUTE_TYPE)));
    }
}
