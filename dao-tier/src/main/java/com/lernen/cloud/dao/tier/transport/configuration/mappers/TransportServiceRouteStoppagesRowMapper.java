package com.lernen.cloud.dao.tier.transport.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import com.lernen.cloud.core.api.transport.TransportServiceRouteStoppageDetails;
import org.springframework.jdbc.core.RowMapper;

import com.google.gson.Gson;
import com.lernen.cloud.core.api.institute.Time;
import com.lernen.cloud.core.api.transport.TransportArea;
import com.lernen.cloud.core.api.transport.TransportServiceRouteStoppagesResponse;

/**
 *
 * <AUTHOR>
 *
 */
public class TransportServiceRouteStoppagesRowMapper implements RowMapper<TransportServiceRouteStoppageDetails> {

	private static final TransportAreaRowMapper TRANSPORT_AREA_ROW_MAPPER = new TransportAreaRowMapper();
	private static final String TIME = "time";
	private static final Gson GSON = new Gson();

	@Override
	public TransportServiceRouteStoppageDetails mapRow(ResultSet rs, int rowNum) throws SQLException {

		final TransportArea transportArea = TRANSPORT_AREA_ROW_MAPPER.mapRow(rs, rowNum);
		return new TransportServiceRouteStoppageDetails(transportArea,
				GSON.fromJson(rs.getString(TIME), Time.class));
	}
}
