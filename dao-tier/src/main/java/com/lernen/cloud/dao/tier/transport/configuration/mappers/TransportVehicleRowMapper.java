package com.lernen.cloud.dao.tier.transport.configuration.mappers;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;

import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.transport.*;
import com.lernen.cloud.core.api.user.Document;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

public class TransportVehicleRowMapper implements RowMapper<Vehicle> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String VEHICLE_ID = "vehicle_id";
	private static final String VEHICLE_NUMBER = "vehicle_number";
	private static final String VEHICLE_CODE = "vehicle_code";
	private static final String REGISTRATION_NUMBER = "registration_number";
	private static final String CAPACITY = "capacity";
	private static final String ACTIVE = "active";
	private static final String ENGINE_NUMBER = "engine_number";
	private static final String BATTERY_NUMBER = "battery_number";
	private static final String PURCHASE_DATE = "purchase_date";
	private static final String CHASSIS_NUMBER = "chassis_number";
	private static final String TYRE_NUMBER = "tyre_number";
	private static final String MANUFACTURER = "manufacturer";
	private static final String MILEAGE = "mileage";
	private static final String BODY_COST = "body_cost";
	private static final String MAKE = "make";
	private static final String MODEL = "model";
	private static final String YEAR = "year";
	private static final String FINANCER = "financer";
	private static final String CHASSIS_COST = "chassis_cost";
	private static final String PUC_NUMBER = "puc_number";
	private static final String VEHICLE_DOCUMENT = "documents";
	private static final TransportVehicleTypeRowMapper TRANSPORT_VEHICLE_TYPE_ROW_MAPPER = new TransportVehicleTypeRowMapper();

	@Override
	public Vehicle mapRow(ResultSet rs, int rowNum) throws SQLException {
		final VehicleType vehicleType = TRANSPORT_VEHICLE_TYPE_ROW_MAPPER.mapRow(rs, rowNum);
		final String documents = rs.getString(VEHICLE_DOCUMENT);
		List<Document<VehicleDocumentType>> vehicleDocuments = null;
		if (!StringUtils.isBlank(documents)) {
			final Type collectionType = new TypeToken<List<Document<VehicleDocumentType>>>() {}.getType();
			vehicleDocuments = GSON.fromJson(documents, collectionType);
		}
		final Timestamp purchaseDateTimestamp = rs.getTimestamp(PURCHASE_DATE);
		final Integer purchaseDateTimestampTime = purchaseDateTimestamp == null ? null
				: (int) (purchaseDateTimestamp.getTime() / 1000l);

		return new Vehicle(rs.getInt(INSTITUTE_ID), rs.getInt(VEHICLE_ID), rs.getString(VEHICLE_NUMBER), rs.getString(VEHICLE_CODE), rs.getString(REGISTRATION_NUMBER),
				vehicleType, rs.getInt(CAPACITY), rs.getBoolean(ACTIVE), rs.getString(ENGINE_NUMBER), rs.getString(BATTERY_NUMBER),  rs.getString(CHASSIS_NUMBER),
				purchaseDateTimestampTime, rs.getString(TYRE_NUMBER), rs.getString(MANUFACTURER), rs.getInt(MILEAGE), rs.getInt(BODY_COST),
				rs.getString(MAKE), rs.getString(MODEL), rs.getInt(YEAR), rs.getString(FINANCER), rs.getDouble(CHASSIS_COST), rs.getString(PUC_NUMBER), vehicleDocuments);
	}
	}
