package com.lernen.cloud.dao.tier.transport.tracking.mappers;

import com.embrate.cloud.core.api.transport.tracking.TransportTrackingData;
import com.embrate.cloud.core.api.transport.tracking.TransportTrackingStatus;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

/**
 * <AUTHOR>
 */
public class TransportTrackingDetailsRowMapper implements RowMapper<TransportTrackingData> {

    private static final String INSTITUTE_ID = "institute_id";
    private static final String TRANSPORT_TRIP_ID = "transport_trip_id";
    private static final String SERVICE_ROUTE_ID = "service_route_id";
    private static final String TRIP_DATE = "trip_date";
    private static final String DRIVER_ID = "driver_id";
    private static final String CONDUCTOR_ID = "conductor_id";
    private static final String TRIP_START_TIME = "trip_start_time";
    private static final String TRIP_END_TIME = "trip_end_time";
    private static final String TRIP_STATUS = "trip_status";
    private static final String METADATA = "metadata";
    private static final String DESCRIPTION = "description";

    @Override
    public TransportTrackingData mapRow(ResultSet rs, int rowNum) throws SQLException {
        UUID conductorId = null;
        String conductorIdStr = rs.getString(CONDUCTOR_ID);
        if (StringUtils.isNotBlank(conductorIdStr)) {
            conductorId = UUID.fromString(conductorIdStr);
        }

        Timestamp startTimestamp = rs.getTimestamp(TRIP_START_TIME);
        Timestamp endTimestamp = rs.getTimestamp(TRIP_END_TIME);
        Long tripStartTime = null;
        Long tripEndTime = null;
        if (startTimestamp != null) {
            tripStartTime = startTimestamp.getTime();
        }
        if (endTimestamp != null) {
            tripEndTime = endTimestamp.getTime();
        }

        String metadataStr = rs.getString(METADATA);
        Map<String, Object> metaData = new HashMap<>();
        if (StringUtils.isNotBlank(metadataStr)) {
            final Type metaDataMap = new TypeToken<Map<String, Object>>() {
            }.getType();
            metaData = GSON.fromJson(metadataStr, metaDataMap);
        }


        return new TransportTrackingData(rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(TRANSPORT_TRIP_ID)), UUID.fromString(rs.getString(SERVICE_ROUTE_ID)),
                rs.getInt(TRIP_DATE), UUID.fromString(rs.getString(DRIVER_ID)), conductorId, tripStartTime, tripEndTime, TransportTrackingStatus.getStatus(rs.getString(TRIP_STATUS)),
                metaData, rs.getString(DESCRIPTION));
    }

}
