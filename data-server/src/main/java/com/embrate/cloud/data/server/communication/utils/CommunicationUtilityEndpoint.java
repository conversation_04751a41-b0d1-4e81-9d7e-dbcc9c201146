package com.embrate.cloud.data.server.communication.utils;

import com.embrate.cloud.core.api.service.communication.CommunicationServiceTransaction;
import com.embrate.cloud.core.api.service.communication.CommunicationServiceTransactionType;
import com.embrate.cloud.core.lib.communication.utils.CommunicationUtilityManager;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */

@Path("/2.0/communication-utility")
public class CommunicationUtilityEndpoint {
	private static final Logger logger = LogManager.getLogger(CommunicationUtilityEndpoint.class);

	private final CommunicationUtilityManager communicationUtilityManager;

	public CommunicationUtilityEndpoint(CommunicationUtilityManager communicationUtilityManager) {
		this.communicationUtilityManager = communicationUtilityManager;
	}

	@POST
	@Path("sms-recharge")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addSMSTransaction(@QueryParam("institute_id") int instituteId,
									  CommunicationServiceTransaction rechargeDetails) {
		final UUID transactionId = communicationUtilityManager.addSMSTransaction(instituteId, rechargeDetails);
		if (transactionId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to add sms transaction"));
		}
		final Map<String, String> results = new HashMap<>();
		results.put("transactionId", transactionId.toString());
		return Response.status(Response.Status.OK.getStatusCode()).entity(results).build();
	}

	@GET
	@Path("sms-recharge-history")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getSMSTransaction(@QueryParam("institute_id") int instituteId) {
		final List<CommunicationServiceTransaction> rechargeDetails = communicationUtilityManager.getSMSTransactions(instituteId, CommunicationServiceTransactionType.RECHARGE);
		return Response.status(Response.Status.OK.getStatusCode()).entity(rechargeDetails).build();
	}

//	@POST
//	@Path("sms-transaction/{institute_id}")
//	@Consumes(MediaType.APPLICATION_JSON)
//	@Produces(MediaType.APPLICATION_JSON)
//	public Response addSMSTransaction(@PathParam("institute_id") int instituteId,
//									  CommunicationServiceTransaction rechargeDetails) {
//		final UUID transactionId = notificationManager.addSMSTransaction(instituteId, rechargeDetails);
//		if (transactionId == null) {
//			throw new ApplicationException(
//					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to add sms transaction"));
//		}
//		final Map<String, String> results = new HashMap<>();
//		results.put("transactionId", transactionId.toString());
//		return Response.status(Response.Status.OK.getStatusCode()).entity(results).build();
//	}


//	@GET
//	@Path("sms-transaction")
//	@Consumes(MediaType.APPLICATION_JSON)
//	@Produces(MediaType.APPLICATION_JSON)
//	public Response getSMSTransaction(@QueryParam("institute_id") int instituteId) {
//		final List<CommunicationServiceTransaction> rechargeDetails = notificationManager.getSMSTransaction(instituteId);
//		return Response.status(Response.Status.OK.getStatusCode()).entity(rechargeDetails).build();
//	}


	//	@POST
	//	@Path("delete-sms-transaction/{institute_id}/{transaction_id}")
	//	@Consumes(MediaType.APPLICATION_JSON)
	//	@Produces(MediaType.APPLICATION_JSON)
	//	public Response deleteSMSTransaction(@PathParam("institute_id") int instituteId,
	//			@PathParam("transaction_id") UUID transactionId) {
	//		Boolean status = customSMSHandler.deleteSMSTransaction(instituteId, transactionId);
	//		return Response.status(Response.Status.OK.getStatusCode()).entity(status).build();
	//	}

}
