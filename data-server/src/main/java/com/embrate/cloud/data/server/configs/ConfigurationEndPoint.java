package com.embrate.cloud.data.server.configs;

import java.util.List;
import java.util.Map;

import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.lernen.cloud.core.api.configurations.Entity;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.lib.configs.ConfigurationManager;

/**
 *
 * <AUTHOR>
 *
 */
@Path("/2.0/configs")
public class ConfigurationEndPoint {

	private final ConfigurationManager configurationManager;

	public ConfigurationEndPoint(ConfigurationManager configurationManager) {
		this.configurationManager = configurationManager;
	}

	@GET
	@Path("{entity_name}/{entity_id}/{config_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getConfiguration(@PathParam("entity_name") Entity entityName,
			@PathParam("entity_id") String entityId, @PathParam("config_type") String configType) {

		if (entityName == null || StringUtils.isBlank(entityId) || StringUtils.isBlank(configType)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid config entity information"));
		}
		final Map<String, String> configKeyValues = configurationManager.getConfiguration(entityName, entityId,
				configType);

		return Response.status(Response.Status.OK.getStatusCode()).entity(configKeyValues).build();
	}

	@POST
	@Path("{entity_name}/{entity_id}/{config_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response upsertConfiguration(@PathParam("entity_name") Entity entityName,
			@PathParam("entity_id") String entityId, @PathParam("config_type") String configType,
			Map<String, String> configKeyValues) {

		if (entityName == null || StringUtils.isBlank(entityId) || StringUtils.isBlank(configType)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid config entity information"));
		}

		if (MapUtils.isEmpty(configKeyValues)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Empty configurations provided"));
		}

		final boolean success = configurationManager.upsertConfiguration(entityName, entityId, configType,
				configKeyValues);

		if (success) {
			final Map<String, String> updatedConfigKeyValues = configurationManager.getConfiguration(entityName,
					entityId, configType);

			return Response.status(Response.Status.OK.getStatusCode()).entity(updatedConfigKeyValues).build();
		}

		throw new ApplicationException(
				new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to update configs"));
	}

}
