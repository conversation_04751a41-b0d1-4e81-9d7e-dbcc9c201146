package com.embrate.cloud.data.server.courses;

import com.embrate.cloud.core.api.courses.config.CloneCoursesRequest;
import com.embrate.cloud.core.api.courses.config.CloneCoursesResponse;
import com.embrate.cloud.core.lib.courses.CourseConfigManager;
import com.embrate.cloud.core.lib.examination.ExamConfigManager;
import com.lernen.cloud.core.api.examination.config.CloneClassExamRequest;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 */

@Path("/2.0/course-config")
public class CourseConfigEndPoint {
    private static final Logger logger = LogManager.getLogger(CourseConfigEndPoint.class);

    private final CourseConfigManager courseConfigManager;

    public CourseConfigEndPoint(CourseConfigManager courseConfigManager) {
        this.courseConfigManager = courseConfigManager;
    }

    @POST
    @Path("clone")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response cloneClassExam(CloneCoursesRequest cloneCoursesRequest) {
        CloneCoursesResponse cloneCoursesResponse = courseConfigManager.cloneClassCourses(cloneCoursesRequest);
        if (cloneCoursesResponse == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to clone class courses."));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(cloneCoursesResponse).build();
    }
}