package com.embrate.cloud.data.server.courses;


import com.embrate.cloud.pdf.reports.PdfReportGenerator;
import com.lernen.cloud.core.api.course.CourseReportType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.report.HTMLReportTable;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.embrate.cloud.core.lib.courses.CourseReportGenerator;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.reports.ExcelReportGenerator;
import com.lernen.cloud.core.utils.report.ReportUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.UUID;

@Path("/2.0/course-reports/")
public class CourseReportEndpoint {

    private final ExcelReportGenerator excelReportGenerator;
    private final PdfReportGenerator pdfReportGenerator;
    private final CourseReportGenerator courseReportGenerator;

    public CourseReportEndpoint(ExcelReportGenerator excelReportGenerator, PdfReportGenerator pdfReportGenerator,CourseReportGenerator courseReportGenerator) {
        this.excelReportGenerator = excelReportGenerator;
        this.pdfReportGenerator = pdfReportGenerator;
        this.courseReportGenerator = courseReportGenerator;
    }

    @GET
    @Path("view-report/{report_type}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getReport(@PathParam("report_type") CourseReportType courseReportType, @QueryParam("standard_id") UUID standardId, @QueryParam("course_id") String courseIdStr,
                              @QueryParam("institute_id") int instituteId,
                              @QueryParam("academic_session_id") Integer academicSessionId,
                              @QueryParam("section_id") String sectionIdStr,
                              @QueryParam("user_id") UUID userId) {


        final ReportDetails reportDetails = courseReportGenerator.generateReport(instituteId, academicSessionId,
                standardId, sectionIdStr, courseIdStr, null, userId, courseReportType);

        if(reportDetails == null){
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to render report"));
        }

        HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
        return Response.status(Response.Status.OK.getStatusCode()).entity(htmlReportTable).build();
    }


    @GET
    @Path("reports/{report_type}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    public Response getCourseReport(@PathParam("report_type") CourseReportType courseReportType,@QueryParam("standard_id") UUID standardId, @QueryParam("course_id") String courseIdStr,
                                    @QueryParam("institute_id") int instituteId,
                                    @QueryParam("academic_session_id") Integer academicSessionId,
                                    @QueryParam("section_id") String sectionIdStr,
                                    @QueryParam("download_format") DownloadFormat downloadFormat,
                                    @QueryParam("user_id") UUID userId) throws IOException {

        final ReportDetails reportDetails = courseReportGenerator.generateReport(instituteId, academicSessionId,
                standardId, sectionIdStr, courseIdStr, downloadFormat, userId, courseReportType);

        if (downloadFormat == DownloadFormat.PDF) {
            final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.COURSES);
            if (documentOutput == null) {
                return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
            }
            return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
                    .header("Content-Disposition", "filename=" + documentOutput.getName())
                    .entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
        } else {
            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
            if (reportOutput == null) {
                return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
            }
            return Response.status(Response.Status.OK.getStatusCode())
                    .header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
                    .entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
        }
    }
}
