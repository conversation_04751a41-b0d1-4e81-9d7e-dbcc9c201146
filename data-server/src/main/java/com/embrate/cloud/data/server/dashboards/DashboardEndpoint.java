package com.embrate.cloud.data.server.dashboards;

import com.embrate.cloud.core.api.dashboards.InstituteStats;
import com.embrate.cloud.core.lib.dashboards.DashboardManager;
import com.lernen.cloud.core.api.transport.StudentTransportInfo;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @created_at 26/06/23 : 11:40
 **/
@Path("/2.0/dashboards")
public class DashboardEndpoint {

    private final DashboardManager dashboardManager;

    public DashboardEndpoint(DashboardManager dashboardManager) {
        this.dashboardManager = dashboardManager;
    }

    @GET
    @Path("institute/{institute_id}/{academic_session_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getInstituteStats(@PathParam("institute_id") int instituteId,
                                                    @PathParam("academic_session_id") int academicSessionId,
                                                    @QueryParam("date") int date) {
        final InstituteStats instituteStats = dashboardManager.getInstituteStats(instituteId, academicSessionId, date);
        return Response.status(Response.Status.OK.getStatusCode()).entity(instituteStats).build();
    }

    @GET
    @Path("organization/{organization_id}/{user_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getOrganizationStats(@PathParam("organization_id") UUID organizationId,
                                         @PathParam("user_id") UUID userId, @QueryParam("date") int date,
                                         @QueryParam("selected_academic_session_id") Integer selectedAcademicSessionId) {
        final List<InstituteStats> instituteStatsList = dashboardManager.getOrganizationStats(organizationId, userId, date,
                selectedAcademicSessionId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(instituteStatsList).build();
    }
}
