package com.embrate.cloud.data.server.dashboards.admission;

import com.embrate.cloud.core.api.dashboards.admission.StudentAdmissionOrgStats;
import com.embrate.cloud.core.api.dashboards.admission.StudentBirthdayStats;
import com.embrate.cloud.core.lib.dashboards.admission.UserDashboardManager;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.UUID;

/**
 * <AUTHOR>
 **/
@Path("2.1/dashboards/users")
public class UserDashboardEndpoint {

	private final UserDashboardManager userDashboardManager;

	public UserDashboardEndpoint(UserDashboardManager userDashboardManager) {
		this.userDashboardManager = userDashboardManager;
	}
//
//	@GET
//	@Path("institute/{institute_id}/{academic_session_id}")
//	@Consumes(MediaType.APPLICATION_JSON)
//	@Produces(MediaType.APPLICATION_JSON)
//	public Response getInstituteStats(@PathParam("institute_id") int instituteId,
//									  @PathParam("academic_session_id") int academicSessionId,
//									  @QueryParam("date") int date) {
//		final InstituteStats instituteStats = dashboardManager.getInstituteStats(instituteId, academicSessionId, date);
//		return Response.status(Response.Status.OK.getStatusCode()).entity(instituteStats).build();
//	}

	@GET
	@Path("student/organisation/{organisation_id}/admission")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getOrganisationStats(@PathParam("organisation_id") UUID organisationId,
										 @QueryParam("institute_ids") String selectedInstituteIds,
										 @QueryParam("user_id") UUID userId, @QueryParam("start_date") int startDate,
										 @QueryParam("end_date") int endDate) {
		final StudentAdmissionOrgStats admissionOrgStats = userDashboardManager.getStudentAdmissionOrgStats(organisationId, selectedInstituteIds, userId, startDate,
				endDate);
		return Response.status(Response.Status.OK.getStatusCode()).entity(admissionOrgStats).build();
	}

	@GET
	@Path("student/organisation/{organisation_id}/birthday")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getBirthdayStats(@PathParam("organisation_id") UUID organisationId,
										 @QueryParam("institute_ids") String selectedInstituteIds,
										 @QueryParam("user_id") UUID userId,  @QueryParam("override_date") int overrideDate) {
		final StudentBirthdayStats birthdayStats = userDashboardManager.getStudentBirthdayStats(organisationId, selectedInstituteIds, userId, overrideDate);
		return Response.status(Response.Status.OK.getStatusCode()).entity(birthdayStats).build();
	}
}
