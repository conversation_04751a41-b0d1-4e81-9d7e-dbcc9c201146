package com.embrate.cloud.data.server.fees;

import com.embrate.cloud.core.api.fee.configuration.FeeConfigurationCloneRequest;
import com.embrate.cloud.core.api.fee.discount.assignment.StudentDiscountStructureAssignmentPayload;
import com.embrate.cloud.core.api.fee.setup.FeeStructureCloneClassRequest;
import com.embrate.cloud.core.api.fee.setup.StructureCloneRequest;
import com.embrate.cloud.core.lib.fees.config.FeeSetupUtilityManager;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 */
@Path("/2.0/fee-setup")
public class FeeSetupUtilityEndPoint {

	private final FeeSetupUtilityManager feeSetupUtilityManager;

	public FeeSetupUtilityEndPoint(FeeSetupUtilityManager feeSetupUtilityManager) {
		this.feeSetupUtilityManager = feeSetupUtilityManager;
	}

	@POST
	@Path("fee-structure/clone/session")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response cloneFeeStructureAcrossSession(StructureCloneRequest request) {
		final boolean status = feeSetupUtilityManager.cloneFeeStructureAcrossSessions(request);
		if (!status) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Unable to clone fee structure across sessions."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("fee-structure/clone/class")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response cloneFeeStructureClass(FeeStructureCloneClassRequest request) {
		final boolean status = feeSetupUtilityManager.cloneFeeStructureClass(request);
		if (!status) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Unable to clone fee structure class."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("assign-discount-structure")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response assignDiscountStructure(
			StudentDiscountStructureAssignmentPayload studentDiscountStructureAssignmentPayload,
			@QueryParam("institute_id") int instituteId) {
		final boolean addedStudentDiscountConfig = feeSetupUtilityManager.assignStudentDiscountStructure(
				instituteId, studentDiscountStructureAssignmentPayload);
		if (!addedStudentDiscountConfig) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DISCOUNT_CONFIGURATION,
					"Unable to add discount configuration"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("discount-structure/clone/session")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response cloneDiscountStructureAcrossSession(StructureCloneRequest request) {
		final boolean status = feeSetupUtilityManager.cloneDiscountStructureAcrossSessions(request);
		if (!status) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Unable to clone discount structure across sessions."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("fees/clone/session")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response cloneFeeConfigurationAcrossSession(FeeConfigurationCloneRequest feeConfigurationCloneRequest){
		final boolean cloneFeeConfiguration = feeSetupUtilityManager.cloneFeeConfigurationAcrossSession(feeConfigurationCloneRequest);
		if (!cloneFeeConfiguration) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION,
					"Unable to clone fee configuration"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}
}
