package com.embrate.cloud.data.server.fontdesk;

import com.embrate.cloud.core.api.frontdesk.GatePassDetails;
import com.embrate.cloud.core.api.frontdesk.GatePassPayload;
import com.embrate.cloud.core.api.frontdesk.GatePassResponse;
import com.embrate.cloud.core.api.frontdesk.GatePassStatus;
import com.embrate.cloud.core.lib.frontdesk.FrontDeskManager;
import com.embrate.cloud.push.notifications.handler.GatePassPushNotificationHandler;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.pdf.gatepass.GatePassHandler;
import com.lernen.cloud.sms.handler.FrontDeskSMSHandler;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.UUID;


/**
 * <AUTHOR>
 *
 */
@Path("/2.0/front-desk")
public class FrontDeskEndpoint {

    private final FrontDeskManager frontDeskManager;
    private final FrontDeskSMSHandler frontDeskSMSHandler;
    private final GatePassPushNotificationHandler gatePassPushNotificationHandler;
    private final GatePassHandler gatePassHandler;

    public FrontDeskEndpoint(FrontDeskManager frontDeskManager, GatePassPushNotificationHandler gatePassPushNotificationHandler,
                             FrontDeskSMSHandler frontDeskSMSHandler, GatePassHandler gatePassHandler) {
        this.frontDeskManager = frontDeskManager;
        this.gatePassPushNotificationHandler = gatePassPushNotificationHandler;
        this.frontDeskSMSHandler = frontDeskSMSHandler;
        this.gatePassHandler = gatePassHandler;
    }

    // Gate pass Details
    @POST
    @Path("gate-pass")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addGatePassDetails(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                      GatePassPayload gatePassPayload) {
        final GatePassResponse gatePassResponse = frontDeskManager.addGatePassDetails(instituteId, userId, gatePassPayload);
        if (gatePassResponse == null || gatePassResponse.getGatePassId() == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to add gate pass details."));
        }

        if(gatePassPayload.isSendMobileAppNotification()) {
            gatePassPushNotificationHandler.sendGatePassBroadcastNotificationsAsync(instituteId, gatePassResponse.getGatePassId(), userId, false);
        }

        if(gatePassPayload.isSendEarlyDepartureMessage()) {
            frontDeskSMSHandler.sendEarlyDepartureSMSAsync(instituteId, gatePassResponse.getGatePassId(), userId);
        }

        return Response.status(Response.Status.OK.getStatusCode()).entity(gatePassResponse).build();
    }

    @PUT
    @Path("{gate_pass_id}/gate-pass-status/{gate_pass_status}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateGatePassStatus(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
            @PathParam("gate_pass_id") UUID gatePassId, @PathParam("gate_pass_status") GatePassStatus gatePassStatus) {
        final boolean updateGatePassStatus = frontDeskManager.updateGatePassStatus(instituteId, userId, gatePassId,
                gatePassStatus);
        if (!updateGatePassStatus) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to update gate pass status."));
        }
        if(gatePassStatus == GatePassStatus.CANCELLED) {
            gatePassPushNotificationHandler.sendGatePassCancelledNotificationAsync(instituteId, gatePassId, userId, false);
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @GET
    @Path("gate-pass-details/{gate_pass_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getGatePassDetailsById(@QueryParam("institute_id") int instituteId,
                                           @PathParam("gate_pass_id") UUID gatePassId) {
        final GatePassDetails gatePassDetails = frontDeskManager.getGatePassDetailsById(instituteId, gatePassId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(gatePassDetails).build();
    }

    @GET
    @Path("gate-pass-details")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getGatePassDetailList(@QueryParam("institute_id") int instituteId,
                                          @QueryParam("gate_pass_status") GatePassStatus gatePassStatus) {
        final List<GatePassDetails> gatePassDetailsList = frontDeskManager.getGatePassDetailList(instituteId, gatePassStatus, false);
        return Response.status(Response.Status.OK.getStatusCode()).entity(gatePassDetailsList).build();
    }

    @GET
    @Path("gate-pass/{gate_pass_id}/pdf")
    @Produces("application/pdf")
    public Response getTransactionInvoiceSummaryPDF(@PathParam("gate_pass_id") UUID gatePassId,
            @QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId) {
        final DocumentOutput documentOutput = gatePassHandler.generateGatePassDocument(instituteId, gatePassId, userId, false);
        if (documentOutput == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate gate-pass document"));
        }

        // Just write filename= no need for attachment/inline for displaying pdf
        return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
                .header("Content-Disposition", "filename=" + documentOutput.getName())
                .entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
    }
}
