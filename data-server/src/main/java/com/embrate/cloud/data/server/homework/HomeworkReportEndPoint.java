package com.embrate.cloud.data.server.homework;

import com.embrate.cloud.core.api.homework.HomeworkReportType;
import com.embrate.cloud.core.lib.homework.HomeworkReportGenerator;
import com.embrate.cloud.pdf.reports.PdfReportGenerator;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.report.*;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.reports.ExcelReportGenerator;
import com.lernen.cloud.core.utils.report.ReportUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.UUID;

/**
 *
 * <AUTHOR> yadav
 *
 */

@Path("/2.0/homework-reports/")
public class HomeworkReportEndPoint {
	private static final Logger logger = LogManager.getLogger(HomeworkReportEndPoint.class);

	private final HomeworkReportGenerator homeworkReportGenerator;
	private final ExcelReportGenerator excelReportGenerator;
	private final PdfReportGenerator pdfReportGenerator;

	public HomeworkReportEndPoint(HomeworkReportGenerator homeworkReportGenerator, ExcelReportGenerator excelReportGenerator, PdfReportGenerator pdfReportGenerator) {
		this.homeworkReportGenerator = homeworkReportGenerator;
		this.excelReportGenerator = excelReportGenerator;
		this.pdfReportGenerator = pdfReportGenerator;
	}

	@GET
	@Path("view-report/{report_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getReport(@PathParam("report_type") HomeworkReportType homeworkReportType, @QueryParam("standard_id") UUID standardId, @QueryParam("course_id") String courseIdStr,
							  @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") Integer academicSessionId, @QueryParam("section_id") String sectionIdStr,
							  @QueryParam("homework_status_str") String homeworkStatusStr, @QueryParam("homework_type_str") String homeworkTypeStr,
							  @QueryParam("user_id") UUID userId, @QueryParam("start_date") Integer startDate, @QueryParam("end_date") Integer endDate) {


		final ReportDetails reportDetails = homeworkReportGenerator.generateReport(instituteId, academicSessionId,
				standardId, sectionIdStr, courseIdStr, null, userId, homeworkReportType, startDate, endDate, homeworkStatusStr, homeworkTypeStr);

		if(reportDetails == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to render report"));
		}

		HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
		return Response.status(Response.Status.OK.getStatusCode()).entity(htmlReportTable).build();
	}


	@GET
	@Path("{report_type}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response getHomeworkReport(@PathParam("report_type") HomeworkReportType homeworkReportType,@QueryParam("standard_id") UUID standardId, @QueryParam("course_id") String courseIdStr,
									  @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") Integer academicSessionId, @QueryParam("section_id") String sectionIdStr,
									  @QueryParam("homework_status_str") String homeworkStatusStr, @QueryParam("homework_type_str") String homeworkTypeStr, @QueryParam("download_format") DownloadFormat downloadFormat,
									  @QueryParam("user_id") UUID userId, @QueryParam("start_date") Integer startDate, @QueryParam("end_date") Integer endDate) throws IOException {

		final ReportDetails reportDetails = homeworkReportGenerator.generateReport(instituteId, academicSessionId,
				standardId, sectionIdStr, courseIdStr, downloadFormat, userId, homeworkReportType, startDate, endDate, homeworkStatusStr, homeworkTypeStr);

		if (downloadFormat == DownloadFormat.PDF) {
			final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.HOMEWORK_MANAGEMENT);
			if (documentOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
					.header("Content-Disposition", "filename=" + documentOutput.getName())
					.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
		} else {
			final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
			if (reportOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode())
					.header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
					.entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
		}
	}

}