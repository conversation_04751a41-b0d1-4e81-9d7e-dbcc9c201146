package com.embrate.cloud.data.server.institute.management;

import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.lib.institute.management.InstitutePreferenceManager;
import com.lernen.cloud.core.api.configurations.Entity;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Path("/2.0/institute-management")
public class InstitutePreferenceEndPoint {

    private final InstitutePreferenceManager institutePreferenceManager;

    public InstitutePreferenceEndPoint(InstitutePreferenceManager institutePreferenceManager) {
        this.institutePreferenceManager = institutePreferenceManager;
    }

    @GET
    @Path("preferences/{institute_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getInstitutePreferences(@PathParam("institute_id") int institute_id) {
        final List<ProductFeature> productFeatureList = institutePreferenceManager.getAllPreferences(Entity.INSTITUTE, String.valueOf(institute_id));
        if (productFeatureList == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Unable to get feature list for institute"));
        }

        return Response.status(Response.Status.OK.getStatusCode()).entity(productFeatureList).build();
    }

    @GET
    @Path("global-preferences")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getGlobalPreferences() {
        final List<ProductFeature> productFeatureList = institutePreferenceManager.getAllPreferences(Entity.GLOBAL, Entity.GLOBAL.name());
        if (productFeatureList == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Unable to get feature list for institute"));
        }

        return Response.status(Response.Status.OK.getStatusCode()).entity(productFeatureList).build();
    }

    @POST
    @Path("preferences/{institute_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response setInstitutePreferences(@PathParam("institute_id") int instituteId, Map<String, Map<String, Map<String, String>>> configPreferenceMap) {
        final boolean success = institutePreferenceManager.updatePreference(Entity.INSTITUTE, String.valueOf(instituteId), configPreferenceMap);
        if (!success) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Unable to update institute preference"));
        }

        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @POST
    @Path("global-preferences")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response setGlobalPreferences(Map<String, Map<String, Map<String, String>>> configPreferenceMap) {
        final boolean success = institutePreferenceManager.updatePreference(Entity.GLOBAL, Entity.GLOBAL.name(), configPreferenceMap);
        if (!success) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Unable to update institute preference"));
        }

        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

}
