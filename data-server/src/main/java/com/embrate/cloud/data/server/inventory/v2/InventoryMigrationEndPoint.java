package com.embrate.cloud.data.server.inventory.v2;

import com.embrate.cloud.core.api.inventory.v2.brand.InventoryBrand;
import com.embrate.cloud.core.api.inventory.v2.category.InventoryCategory;
import com.embrate.cloud.core.api.inventory.v2.migration.InventoryMigrationPayload;
import com.embrate.cloud.core.api.inventory.v2.supplier.InventorySupplier;
import com.embrate.cloud.core.lib.inventory.v2.InventoryMigrationManager;
import com.embrate.cloud.core.lib.inventory.v2.InventoryOutletManager;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.student.StudentStatus;
import org.apache.commons.lang3.StringUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Path("2.1/inventory-migration")
public class InventoryMigrationEndPoint {

    private final InventoryMigrationManager inventoryMigrationManager;

    public InventoryMigrationEndPoint(InventoryMigrationManager inventoryMigrationManager) {
        this.inventoryMigrationManager = inventoryMigrationManager;
    }

    @POST
    @Path("setup-clone")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response migrateToV2(InventoryMigrationPayload inventoryMigrationPayload) {

        final boolean success =  inventoryMigrationManager.createOutletAndMigrateToV2(inventoryMigrationPayload);
        if (!success) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SELLER, "Unable to setup & migrate"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @POST
    @Path("v2/{institute_id}/{user_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response migrateToV2(@PathParam("institute_id") int instituteId, @PathParam("user_id") UUID userId) {

        final boolean success =  inventoryMigrationManager.migrateToV2(instituteId, userId);
        if (!success) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SELLER, "Unable to migrate"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }
}
