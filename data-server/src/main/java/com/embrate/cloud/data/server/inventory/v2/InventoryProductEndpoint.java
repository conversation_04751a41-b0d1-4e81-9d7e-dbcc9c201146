package com.embrate.cloud.data.server.inventory.v2;

import com.embrate.cloud.core.api.inventory.v2.*;
import com.embrate.cloud.core.lib.inventory.v2.InventoryProductManager;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import org.apache.commons.collections4.CollectionUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Path("2.1/inventory/product")
public class InventoryProductEndpoint {

    private final InventoryProductManager inventoryProductManager;

    public InventoryProductEndpoint(InventoryProductManager inventoryProductManager) {
        this.inventoryProductManager = inventoryProductManager;
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addNewProduct(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, NewProductRequestV2 newProductRequest) {
        List<UUID> skuIds = inventoryProductManager.addNewProduct(instituteId, userId, newProductRequest);

        if (CollectionUtils.isEmpty(skuIds)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_ADDITION, "Unable to add new products."));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(skuIds).build();
    }

    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateProduct(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                  ProductUpdatePayload productUpdatePayload) {

        boolean updatedProduct = inventoryProductManager.updateProduct(instituteId, userId, productUpdatePayload);
        if (!updatedProduct) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_UPDATION, "Could not update product."));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @GET
    @Path("paginate-search")
    @Produces(MediaType.APPLICATION_JSON)
    public Response searchProducts(@QueryParam("institute_id") int instituteId,
                                   @QueryParam("search_text") String searchText, @QueryParam("offset") int offset,
                                   @QueryParam("limit") int limit) {

        SearchResultWithPagination<ProductDetailsV2> searchResultWithPagination = inventoryProductManager.searchProducts(instituteId, searchText, offset, limit);
        if (searchResultWithPagination == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.PRODUCT_SEARCH_ERROR, "Could not get requested products"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(searchResultWithPagination).build();
    }

    @GET
    @Path("search")
    @Produces(MediaType.APPLICATION_JSON)
    public Response searchProducts(@QueryParam("institute_id") int instituteId, @QueryParam("search_text") String searchText,  @QueryParam("active_batch_only") boolean activeBatchOnly) {

        List<ProductDetailsV2> products = inventoryProductManager.searchProducts(instituteId, searchText, activeBatchOnly);
        if (products == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.PRODUCT_SEARCH_ERROR, "Could not get requested products"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(products).build();
    }

    @POST
    @Path("variations")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getProductsVariations(@QueryParam("institute_id") int instituteId, ProductVariationsRequest productVariationsRequest) {
        List<ProductVariationOutput> productVariationOutputList = inventoryProductManager.getProductVariations(instituteId, productVariationsRequest);
        if (productVariationOutputList == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(productVariationOutputList).build();
    }


    @GET
    @Path("sku/{sku_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getProductsById(@PathParam("sku_id") UUID skuId, @QueryParam("institute_id") int instituteId) {

        ProductDetailsV2 productDetails = inventoryProductManager.getProductDetails(instituteId, skuId);
        if (productDetails == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(productDetails).build();
    }

	@DELETE
    @Path("sku/{sku_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteProduct(@PathParam("sku_id") UUID skuId, @QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId) {

		boolean deleted = inventoryProductManager.deleteProduct(instituteId, userId, skuId);
		if (!deleted) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.PRODUCT_DELETE_ERROR, "Product cannot be deleted"));
		}

		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

}
