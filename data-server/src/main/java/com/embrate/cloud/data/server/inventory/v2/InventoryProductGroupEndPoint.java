package com.embrate.cloud.data.server.inventory.v2;

import com.embrate.cloud.core.api.inventory.v2.product.group.InventoryProductGroupData;
import com.embrate.cloud.core.api.inventory.v2.product.group.InventoryProductGroupPayload;
import com.embrate.cloud.core.lib.inventory.v2.InventoryProductGroupManager;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.inventory.ProductGroupData;
import com.lernen.cloud.core.api.inventory.ProductGroupPayload;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

@Path("/2.1/inventory/product-group")
public class InventoryProductGroupEndPoint {

    private final InventoryProductGroupManager inventoryProductGroupManager;

    public InventoryProductGroupEndPoint(InventoryProductGroupManager inventoryProductGroupManager) {
        this.inventoryProductGroupManager = inventoryProductGroupManager;
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addProductGroup(@QueryParam("institute_id") int instituteId, InventoryProductGroupPayload productGroupPayload) {

        UUID productGroupId = inventoryProductGroupManager.addProductGroup(instituteId, productGroupPayload);
        if (productGroupId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_GROUP, "Unable to add product group."));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @DELETE
    @Path("{group_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response deleteProductGroup(@PathParam("group_id") UUID groupId, @QueryParam("institute_id") int instituteId) {

        boolean deletedProductGroup = inventoryProductGroupManager.deleteProductGroup(instituteId, groupId);
        if (!deletedProductGroup) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_GROUP, "Unable to delete product group."));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(deletedProductGroup).build();
    }

    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateProductGroup(@QueryParam("institute_id") int instituteId, InventoryProductGroupPayload productGroupPayload) {

        UUID productGroupId = inventoryProductGroupManager.updateProductGroup(instituteId, productGroupPayload);
        if (productGroupId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_GROUP, "Unable to update product group."));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(productGroupId).build();
    }

    @GET
    @Path("{group_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getProductGroupByGroupId(@PathParam("group_id") UUID groupId, @QueryParam("institute_id") int instituteId) {

        InventoryProductGroupData productGroupData = inventoryProductGroupManager.getProductGroupByGroupId(instituteId, groupId);
        if (productGroupData == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_GROUP, "No product group found."));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(productGroupData).build();
    }

    @GET
    @Path("/search")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response searchProductGroup(@QueryParam("institute_id") int instituteId, @QueryParam("search_text") String searchText) {
        List<InventoryProductGroupData> productGroupDataList = inventoryProductGroupManager.searchProductGroup(instituteId, searchText);
        if (productGroupDataList == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_GROUP, "No product group found."));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(productGroupDataList).build();
    }
}
