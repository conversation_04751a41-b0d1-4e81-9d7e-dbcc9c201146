package com.embrate.cloud.data.server.inventory.v2;

import com.embrate.cloud.core.api.inventory.v2.NewPurchasePayload;
import com.embrate.cloud.core.api.inventory.v2.issue.ProductIssuePayload;
import com.embrate.cloud.core.api.inventory.v2.sale.ExchangeOrderPayload;
import com.embrate.cloud.core.api.inventory.v2.sale.ExchangeTransactionResponse;
import com.embrate.cloud.core.api.inventory.v2.sale.NewTradePayload;
import com.embrate.cloud.core.api.inventory.v2.transaction.InventoryTransactionMetadata;
import com.embrate.cloud.core.api.inventory.v2.transaction.InventoryTransactionSummary;
import com.embrate.cloud.core.lib.inventory.v2.InventoryTransactionsManager;
import com.embrate.cloud.pdf.inventory.store.v2.StoreInventoryPDFInvoiceHandler;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Path("/2.1/inventory/transactions")
public class InventoryTransactionsEndpoint {

    private final InventoryTransactionsManager inventoryTransactionsManager;
    private final StoreInventoryPDFInvoiceHandler storeInventoryPDFInvoiceHandlerV2;

    public InventoryTransactionsEndpoint(InventoryTransactionsManager inventoryTransactionsManager, StoreInventoryPDFInvoiceHandler storeInventoryPDFInvoiceHandlerV2) {
        this.inventoryTransactionsManager = inventoryTransactionsManager;
        this.storeInventoryPDFInvoiceHandlerV2 = storeInventoryPDFInvoiceHandlerV2;
    }

    @GET
    @Path("{transaction_id}/details")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getTransaction(@PathParam("transaction_id") UUID transactionId, @QueryParam("institute_id") int instituteId) {
        InventoryTransactionSummary transactionSummary = inventoryTransactionsManager.getTransactionDetails(instituteId,
                transactionId);
        if (transactionSummary == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(transactionSummary).build();
    }

    @GET
    @Path("invoice-details")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getInvoiceDetails(@QueryParam("institute_id") int instituteId, @QueryParam("invoice_id") String invoiceId) {
        InventoryTransactionSummary transactionSummary = inventoryTransactionsManager.getTransactionDetailsByInvoiceId(instituteId,
                invoiceId);
        if (transactionSummary == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(transactionSummary).build();
    }

    @GET
    @Path("{transaction_id}/pdf-invoice")
    @Produces("application/pdf")
    public Response getTransactionPDFInvoice(@PathParam("transaction_id") UUID transactionId, @QueryParam("institute_id") int instituteId,
                                             @QueryParam("store_copy") boolean storeCopy, @QueryParam("user_id") UUID userId) {
        DocumentOutput documentOutput = storeInventoryPDFInvoiceHandlerV2.generateInvoice(instituteId, transactionId, storeCopy, userId);

        if (documentOutput == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No transaction found"));
        }

        return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
                .header("Content-Disposition", "filename=" + documentOutput.getName())
                .entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
    }

    @GET
    @Path("meta-data")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getTransactionsMetaData(@QueryParam("institute_id") int instituteId) {
        List<InventoryTransactionMetadata> transactionMetaDataList = inventoryTransactionsManager
                .getTransactionsMetaData(instituteId);
        if (transactionMetaDataList == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Error occurred while fetching transactions "));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(transactionMetaDataList).build();
    }

//	@GET
//	@Path("stats/{institute_id}")
//	@Produces(MediaType.APPLICATION_JSON)
//	public Response getTransactionStatistics(@PathParam("institute_id") int instituteId,
//			@QueryParam("start") Integer start, @QueryParam("end") Integer end) {
//		if (instituteId <= 0 || start == null || end == null || start < 0 || end < 0 || end < start) {
//			throw new ApplicationException(
//					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid parameters provided "));
//		}
//		TransactionsStatistics transactionsStatistics = productTransactionsManager
//				.getTransactionsStatsDayWise(instituteId, start, end);
//		if (transactionsStatistics == null) {
//			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
//					"Error occured while fetching transactions stats "));
//		}
//		return Response.status(Response.Status.OK.getStatusCode()).entity(transactionsStatistics).build();
//	}

    @POST
    @Path("purchase")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addPurchaseTransaction(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                           NewPurchasePayload newPurchasePayload) {
        UUID purchaseTransactionId = inventoryTransactionsManager.addNewPurchaseTransaction(instituteId, userId, newPurchasePayload);
        if (purchaseTransactionId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PURCHASE_ORDER, "Error occurred while adding new purchase"));
        }
        Map<String, Object> data = new HashMap<String, Object>();
        data.put("purchaseTransactionId", purchaseTransactionId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(data).build();
    }

    @POST
    @Path("trade")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addSaleTransaction(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, NewTradePayload newTradePayload) {
        UUID saleTransactionId = inventoryTransactionsManager.addNewTradeTransaction(instituteId, userId, newTradePayload);
        if (saleTransactionId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Error occurred while adding new sale"));
        }
        Map<String, Object> data = new HashMap<>();
        data.put("saleTransactionId", saleTransactionId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(data).build();
    }

    @POST
    @Path("issue")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addProductIssueTransaction(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, ProductIssuePayload productIssuePayload) {
        UUID issueTransactionId = inventoryTransactionsManager.addProductIssueTransaction(instituteId, userId, productIssuePayload);
        if (issueTransactionId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Error occurred while issuing products"));
        }
        Map<String, Object> data = new HashMap<>();
        data.put("issueTransactionId", issueTransactionId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(data).build();
    }

    @POST
    @Path("exchange")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addExchangeTransaction(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, ExchangeOrderPayload exchangeOrderPayload) {
        ExchangeTransactionResponse exchangeTransactionResponse = inventoryTransactionsManager.addExchangeTransaction(instituteId, userId, exchangeOrderPayload);
        if (exchangeTransactionResponse == null || !exchangeTransactionResponse.isSuccess()) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Error occurred while adding exchange transaction"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(exchangeTransactionResponse).build();
    }


    //
//	@POST
//	@Path("return-sales-order")
//	@Consumes(MediaType.APPLICATION_JSON)
//	@Produces(MediaType.APPLICATION_JSON)
//	public Response returnSalesTransaction(@QueryParam("institute_id") int instituteId, ReturnSalesOrder returnSalesOrder) {
//		UUID salesReturnTransactionId = productTransactionsManager.returnSalesTransaction(instituteId, returnSalesOrder);
//		if (salesReturnTransactionId == null) {
//			throw new ApplicationException(
//					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Error occurred while adding sales order"));
//		}
//		Map<String, Object> data = new HashMap<String, Object>();
//		data.put("salesReturnTransactionId", salesReturnTransactionId);
//		return Response.status(Response.Status.OK.getStatusCode()).entity(data).build();
//	}
//
    @DELETE
    @Path("{transaction_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response deleteTransaction(@PathParam("transaction_id") UUID transactionId, @QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId) {
        boolean success = inventoryTransactionsManager.deleteTransaction(instituteId, userId, transactionId);
        if (!success) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Error occurred while deleting transaction"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }
}
