package com.embrate.cloud.data.server.leave.management;

import com.embrate.cloud.core.api.leave.management.LeaveDocumentType;
import com.embrate.cloud.core.api.leave.management.LeaveType;
import com.embrate.cloud.core.api.leave.management.staff.StaffLeaveAttendanceErrorPayload;
import com.embrate.cloud.core.api.leave.management.staff.StaffLeaveDetails;
import com.embrate.cloud.core.api.leave.management.staff.StaffLeavePayload;
import com.embrate.cloud.core.api.leave.management.transaction.LeaveTransactionStatus;
import com.embrate.cloud.core.api.leave.management.user.LeaveReviewPayload;
import com.embrate.cloud.core.lib.leave.management.StaffLeaveManagementManager;
import com.embrate.cloud.push.notifications.handler.StaffLeaveApplicationPushNotificationHandler;
import com.embrate.cloud.push.notifications.handler.StaffLeaveReviewPushNotificationHandler;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.student.DownloadDocumentWrapper;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.data.server.RestFormDataHandler;
import com.sun.jersey.multipart.FormDataBodyPart;
import com.sun.jersey.multipart.FormDataParam;

import com.lernen.cloud.core.api.user.UserType;
import org.springframework.util.CollectionUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;
/**
 * <AUTHOR>
 * @created_at 26/09/24 : 11:30
 **/

@Path("2.0/staff-leave")
public class StaffLeaveManagementEndpoint {
    
    private final StaffLeaveManagementManager staffLeaveManagementManager;
    private final StaffLeaveApplicationPushNotificationHandler staffLeaveApplicationPushNotificationHandler;
    private final StaffLeaveReviewPushNotificationHandler staffLeaveReviewPushNotificationHandler;

    public StaffLeaveManagementEndpoint(StaffLeaveManagementManager staffLeaveManagementManager, StaffLeaveApplicationPushNotificationHandler staffLeaveApplicationPushNotificationHandler, StaffLeaveReviewPushNotificationHandler staffLeaveReviewPushNotificationHandler) {
        this.staffLeaveManagementManager = staffLeaveManagementManager;
        this.staffLeaveApplicationPushNotificationHandler = staffLeaveApplicationPushNotificationHandler;
        this.staffLeaveReviewPushNotificationHandler = staffLeaveReviewPushNotificationHandler;
    }

    @GET
    @Path("leaves/{staff_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getStaffLeaves(@PathParam("staff_id") UUID staffId,
                                     @QueryParam("institute_id") int instituteId,
                                     @QueryParam("academic_session_id") int academicSessionId) {
        List<StaffLeaveDetails> staffLeaveDetailsList = staffLeaveManagementManager.getStaffLeaves(instituteId, academicSessionId, staffId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(staffLeaveDetailsList).build();
    }

    @POST
    @Path("{staff_id}")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addStaffLeaves(@FormDataParam("staff_leave_payload") String staffLeavePayloadJson,
                                     @FormDataParam("file") List<FormDataBodyPart> bodyParts,
                                     @QueryParam("institute_id") int instituteId, @PathParam("staff_id") UUID staffId) {

        final List<FileData> attachments = RestFormDataHandler.getFileDataFromFormDataBodyPart(bodyParts);
        final StaffLeavePayload staffLeavePayload = GSON.fromJson(staffLeavePayloadJson, StaffLeavePayload.class);
        Set<UUID> transactionIdSet = staffLeaveManagementManager.addStaffLeaves(instituteId, staffLeavePayload, attachments);
        if (CollectionUtils.isEmpty(transactionIdSet)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to add staff leaves"));
        }
        staffLeaveApplicationPushNotificationHandler.sendStaffLeaveApplicationPushNotificationsAsync(instituteId, transactionIdSet, staffLeavePayload.getAcademicSessionId());
        return Response.status(Response.Status.OK.getStatusCode()).build();

    }

    @GET
    @Path("leaveTypes/{institute_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getUserLeaveType(@PathParam("institute_id") int instituteId, @QueryParam("user_type") UserType userType) {

        List<LeaveType> leaveTypesList = staffLeaveManagementManager.getStaffLeaveType(instituteId, userType);
        if (leaveTypesList == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(leaveTypesList).build();
    }

    @DELETE
    @Path("transaction/{transaction_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response deleteStaffLeaves(@PathParam("transaction_id") UUID transactionId, @QueryParam("institute_id") int instituteId,
                                        @QueryParam("academic_session_id") int academicSessionId) {

        final boolean success = staffLeaveManagementManager.deleteStaffLeaves(instituteId, academicSessionId, transactionId);
        if (!success) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to delete leave type"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @GET
    @Path("/download-leave-document/{transaction_id}/{document_id}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    public Response downloadLeaveDocument(@PathParam("document_id") UUID documentId, @PathParam("transaction_id") UUID transactionId,
                                          @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
        final DownloadDocumentWrapper<Document<LeaveDocumentType>> documentWrapper = staffLeaveManagementManager
                .downloadStaffLeaveDocument(instituteId, academicSessionId, documentId, transactionId);
        if (documentWrapper == null || documentWrapper.getDocumentContent() == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Unable to download file"));
        }
        return Response.status(Response.Status.OK.getStatusCode())
                .header("Content-Disposition",
                        "attachment;filename=" + documentWrapper.getDocumentDetails().getDocumentName() + "."
                                + documentWrapper.getDocumentDetails().getFileExtension())
                .entity(new ByteArrayInputStream(documentWrapper.getDocumentContent().toByteArray())).build();
    }

    @GET
    @Path("{institute_id}/leaves/{academic_session_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getAllLeaves(@PathParam("institute_id") int instituteId,
                                     @PathParam("academic_session_id") int academicSessionId, @QueryParam("user_id") UUID userId) {
        List<StaffLeaveDetails> staffLeaveDetailsList = staffLeaveManagementManager.getAllLeaves(instituteId, academicSessionId, userId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(staffLeaveDetailsList).build();
    }

    @GET
    @Path("{institute_id}/leaves-by-status/{academic_session_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getAllLeavesByStatus(@PathParam("institute_id") int instituteId,
                                         @PathParam("academic_session_id") int academicSessionId, @QueryParam("user_id") UUID userId) {
        Map<LeaveTransactionStatus, List<StaffLeaveDetails>> leaveTransactionStatusStaffLeaveDetailsListMap = staffLeaveManagementManager.getAllLeavesByStatus(instituteId, academicSessionId, userId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(leaveTransactionStatusStaffLeaveDetailsListMap).build();
    }

    @PUT
    @Path("review-leave")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateLeaveReviewDetails(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
        LeaveReviewPayload leaveReviewPayload) {
        final boolean leaveReviewDetailsUpdated = staffLeaveManagementManager.updateLeaveReviewDetails(instituteId, userId, leaveReviewPayload);
        if (!leaveReviewDetailsUpdated) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_HOMEWORK_DETAILS, "Unable to update leave review details."));
        }

        if(leaveReviewPayload.getTransactionStatus() == LeaveTransactionStatus.APPROVED
            ||leaveReviewPayload.getTransactionStatus() == LeaveTransactionStatus.REJECTED) {
            staffLeaveReviewPushNotificationHandler.sendStaffLeaveReviewedNotificationsAsync(instituteId,
                    leaveReviewPayload.getAcademicSessionId(),
                    leaveReviewPayload);
        }

        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @GET
    @Path("error-reason")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getStaffLeaveAttendanceErrorReason(@QueryParam("institute_id") int instituteId,
            @QueryParam("transaction_id") UUID transactionId, @QueryParam("academic_session_id") int academicSessionId) {
        final StaffLeaveAttendanceErrorPayload staffLeaveAttendanceErrorPayload = staffLeaveManagementManager.getStaffLeaveAttendanceErrorReason(instituteId, academicSessionId, transactionId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(staffLeaveAttendanceErrorPayload).build();
    }

    @GET
    @Path("{institute_id}/transaction/{transaction_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getLeaveByTransactionId(@PathParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
                                 @PathParam("transaction_id") UUID transactionId) {
        StaffLeaveDetails staffLeaveDetails = staffLeaveManagementManager.getLeaveByTransactionId(instituteId,
                academicSessionId, transactionId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(staffLeaveDetails).build();
    }
    
}
