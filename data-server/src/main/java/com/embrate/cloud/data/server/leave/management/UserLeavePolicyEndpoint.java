package com.embrate.cloud.data.server.leave.management;

import com.embrate.cloud.core.api.leave.management.LeaveType;
import com.embrate.cloud.core.api.leave.management.balance.UserNetLeaveBalance;
import com.embrate.cloud.core.api.leave.management.policy.user.StaffLeavePolicy;
import com.embrate.cloud.core.api.leave.management.policy.user.UserLeavePolicy;
import com.embrate.cloud.core.api.leave.management.policy.user.UserLeavePolicyPayload;
import com.embrate.cloud.core.api.leave.management.policy.user.UserLeavePolicyTemplateAssignPayload;
import com.embrate.cloud.core.api.leave.management.transaction.UserLeaveSchedule;
import com.embrate.cloud.core.api.leave.management.transaction.UserLeaveTransactionDetails;
import com.embrate.cloud.core.lib.leave.management.UserLeavePolicyManager;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 */

@Path("2.0/leave-policy")
public class UserLeavePolicyEndpoint {

    private final UserLeavePolicyManager userLeavePolicyManager;

    public UserLeavePolicyEndpoint(UserLeavePolicyManager userLeavePolicyManager) {
        this.userLeavePolicyManager = userLeavePolicyManager;
    }

    @GET
    @Path("search-staff")
    @Produces(MediaType.APPLICATION_JSON)
    public Response searchStaffLeavePolicies(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
                                             @QueryParam("search_text") String searchText,
                                             @QueryParam("categories") String categories, @QueryParam("departments") String departments,
                                             @QueryParam("designations") String designations) {

        List<StaffLeavePolicy> staffLeavePolicyList = userLeavePolicyManager.searchStaffLeavePolicies(instituteId, academicSessionId, searchText, categories, departments, designations);
        if (staffLeavePolicyList == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(staffLeavePolicyList).build();
    }

    @POST
    @Path("staff-template-assignment")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response assignStaffLeavePolicyTemplate(@QueryParam("institute_id") int instituteId, UserLeavePolicyTemplateAssignPayload userHolidayTemplateAssignPayload) {
        boolean success = userLeavePolicyManager.assignStaffLeavePolicyTemplate(instituteId, userHolidayTemplateAssignPayload);
        if (!success) {
            return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @GET
    @Path("users/{user_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getUserLeavePolicy(@PathParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {

        UserLeavePolicy userLeavePolicy = userLeavePolicyManager.getUserLeavePolicy(instituteId, academicSessionId, userId);
        if (userLeavePolicy == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(userLeavePolicy).build();
    }

    @PUT
    @Path("users/{user_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateLeavePolicy(@PathParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId, UserLeavePolicyPayload userLeavePolicyPayload) {

        final boolean success = userLeavePolicyManager.updateUserLeavePolicy(instituteId, userId, userLeavePolicyPayload);
        if (!success) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to update user leave policy"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @DELETE
    @Path("users/{user_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response deleteUserLeavePolicy(@PathParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {

        final boolean success = userLeavePolicyManager.deleteUserLeavePolicy(instituteId, academicSessionId, userId);
        if (!success) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to delete user leave policy"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }


    @GET
    @Path("leave-balance/users/{user_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getUserLeaveBalance(@PathParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {

        UserNetLeaveBalance userNetLeaveBalance = userLeavePolicyManager.getUserLeaveBalance(instituteId, academicSessionId, userId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(userNetLeaveBalance).build();
    }

    @GET
    @Path("leave-balance-history/users/{user_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getUserLeaveBalanceHistory(@PathParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
        List<UserLeaveTransactionDetails> userLeaveTransactionDetailsList = userLeavePolicyManager.getUserLeaveTransactions(instituteId, academicSessionId, userId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(userLeaveTransactionDetailsList).build();
    }
    @GET
    @Path("leave-schedule/users/{user_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response viewUserLeaveSchedule(@PathParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
                                          @QueryParam("compute_day") Integer computeDay) {

        List<UserLeaveSchedule> userLeaveScheduleList = userLeavePolicyManager.computeLeavesBasedOnPolicy(instituteId, academicSessionId, userId, computeDay);
        if (userLeaveScheduleList == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to compute user leave schedule"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(userLeaveScheduleList).build();
    }

    @POST
    @Path("compute-leave")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response computeAllUserLeaves(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
                                      @QueryParam("compute_day") Integer computeDay) {
        Set<UUID> newTransactionIds = userLeavePolicyManager.computeUserLeaves(instituteId, academicSessionId, computeDay);
        if (newTransactionIds == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to compute user leaves"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(newTransactionIds).build();
    }
    @POST
    @Path("compute-leave/users/{user_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response computeUserLeaves(@PathParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
                                      @QueryParam("compute_day") Integer computeDay) {
        Set<UUID> newTransactionIds = userLeavePolicyManager.computeUserLeaves(instituteId, academicSessionId, userId, computeDay);
        if (newTransactionIds == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to compute user leaves"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(newTransactionIds).build();
    }



}
