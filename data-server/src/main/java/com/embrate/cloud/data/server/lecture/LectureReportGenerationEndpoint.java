/**
 * 
 */
package com.embrate.cloud.data.server.lecture;

import java.io.ByteArrayInputStream;
import java.util.UUID;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.StringUtils;

import com.embrate.cloud.core.api.lecture.LectureReportType;
import com.embrate.cloud.core.lib.lecture.LectureReportGenerator;
import com.lernen.cloud.core.api.report.ReportOutput;

/**
 * <AUTHOR>
 *
 */
@Path("/2.0/lecture-management/reports")
public class LectureReportGenerationEndpoint {

	private final LectureReportGenerator lectureReportGenerator;

	public LectureReportGenerationEndpoint(LectureReportGenerator lectureReportGenerator) {
		this.lectureReportGenerator = lectureReportGenerator;
	}

	@GET
	@Path("/generate-report/{institute_id}/{report_type}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response generateReport(@PathParam("institute_id") int instituteId,
			@PathParam("report_type") String reportTypeStr,
			@QueryParam("academic_session_id") int academicSessionId,
			@QueryParam("standard_id") UUID standardId,
			@QueryParam("course_id") UUID courseId,
			@QueryParam("start") Integer start,
			@QueryParam("end") Integer end) {
		
		if (StringUtils.isBlank(reportTypeStr)) {
			return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
		}
		if (academicSessionId <= 0) {
			return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
		}
		
		LectureReportType lectureReportType = LectureReportType.getLectureReportType(reportTypeStr);
		final ReportOutput reportOutput = lectureReportGenerator.generateReport(instituteId,
				academicSessionId, lectureReportType, standardId, courseId, start, end);
		
		if (reportOutput == null) {
			return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
		}
		
		return Response.status(Response.Status.OK.getStatusCode())
				.header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
				.entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();	
	}
}
