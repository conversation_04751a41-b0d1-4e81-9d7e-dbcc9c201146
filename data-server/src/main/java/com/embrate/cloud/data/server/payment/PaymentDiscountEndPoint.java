package com.embrate.cloud.data.server.payment;

import com.embrate.cloud.core.api.payment.PaymentApplicableDiscount;
import com.embrate.cloud.core.api.payment.PaymentRequest;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.embrate.cloud.core.lib.payment.PaymentDiscountManager;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 */

@Path("/2.0/payment-discount")
public class PaymentDiscountEndPoint {

    private final PaymentDiscountManager paymentDiscountManager;

    public PaymentDiscountEndPoint(PaymentDiscountManager paymentDiscountManager) {
        this.paymentDiscountManager = paymentDiscountManager;
    }

    @POST
    @Path("calculate")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response calculateDiscount(@QueryParam("institute_id") int instituteId, PaymentRequest paymentRequest) {

        final PaymentApplicableDiscount paymentApplicableDiscount = paymentDiscountManager
                .getApplicableDiscount(instituteId, paymentRequest);
        if (paymentApplicableDiscount == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Unable to calculate payment discount"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(paymentApplicableDiscount).build();
    }
}
