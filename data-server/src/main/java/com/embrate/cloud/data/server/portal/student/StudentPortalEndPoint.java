/**
 * 
 */
package com.embrate.cloud.data.server.portal.student;

import java.util.UUID;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import com.embrate.cloud.core.api.portal.student.StudentDashboardDetails;
import com.embrate.cloud.core.lib.portal.student.StudentPortalManager;

/**
 * <AUTHOR>
 *
 */
@Path("/2.0/student-portal")
public class StudentPortalEndPoint {

	private final StudentPortalManager studentPortalManager;

	public StudentPortalEndPoint(StudentPortalManager studentPortalManager) {
		this.studentPortalManager = studentPortalManager;
	}

	@GET
	@Path("/dashboard/{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentDashboardDetails(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId) {
		final StudentDashboardDetails studentDashboardDetails = studentPortalManager
				.getStudentDashboardDetails(instituteId, studentId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentDashboardDetails).build();
	}

}
