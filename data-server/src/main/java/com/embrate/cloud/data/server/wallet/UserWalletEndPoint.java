package com.embrate.cloud.data.server.wallet;

import com.embrate.cloud.core.api.wallet.UserWalletDetails;
import com.embrate.cloud.core.api.wallet.WalletTransactionPayload;
import com.embrate.cloud.core.lib.wallet.UserWalletManager;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.pdf.invoice.wallet.WalletInvoiceHandler;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 *
 */
@Path("/2.0/user-wallet")
public class UserWalletEndPoint {

	private final UserWalletManager userWalletManager;
	private final WalletInvoiceHandler walletInvoiceHandler;

	public UserWalletEndPoint(UserWalletManager userWalletManager, WalletInvoiceHandler walletInvoiceHandler) {
		this.userWalletManager = userWalletManager;
		this.walletInvoiceHandler = walletInvoiceHandler;
	}

	@POST
	@Path("recharge")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response rechargeWallet(@QueryParam("institute_id") final int instituteId,
			WalletTransactionPayload walletTransactionPayload) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (userWalletManager.rechargeWallet(instituteId, walletTransactionPayload)) {
			return Response.status(Response.Status.OK.getStatusCode()).build();
		}
		return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
	}

	@GET
	@Path("{user_id}/history")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getWalletHistory(@PathParam("user_id") UUID userId, @QueryParam("institute_id") final int instituteId) {
		if (instituteId <= 0 || userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
		}
		List<WalletTransactionPayload> walletTransactions = userWalletManager.getUserWalletTransactions(instituteId, userId);
		if (walletTransactions == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.UNKNOWN_ERROR, "Error while getting wallet transactions"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(walletTransactions).build();
	}

	@GET
	@Path("{user_id}/details")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getWalletDetails(@PathParam("user_id") UUID userId, @QueryParam("institute_id") final int instituteId) {
		if (instituteId <= 0 || userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
		}
		UserWalletDetails userWalletDetails = userWalletManager.getUserWalletDetails(instituteId, userId);
		if (userWalletDetails == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.UNKNOWN_ERROR, "Error while getting wallet details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(userWalletDetails).build();
	}

	@GET
	@Path("invoice/{transaction_id}/pdf")
	@Produces("application/pdf")
	public Response generateWalletInvoice(@PathParam("transaction_id") UUID transactionId, @QueryParam("institute_id") int instituteId,
													@QueryParam("user_type_str") String userTypeStr) {
		final DocumentOutput documentOutput = walletInvoiceHandler.generateWalletInvoice(instituteId, transactionId, userTypeStr);
		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@PUT
	@Path("transaction/{transaction_id}/update-amount")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateWalletTransactionAmount(@PathParam("transaction_id") UUID transactionId,
			@QueryParam("institute_id") final int instituteId, @QueryParam("user_id") final UUID userId,
			@QueryParam("amount") final Double amount, @QueryParam("updated_by") final UUID updatedBy) {
		if (instituteId <= 0 || userId == null || transactionId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request parameters"));
		}
		
		boolean updated = userWalletManager.updateWalletTransactionAmount(instituteId, transactionId, userId, amount, updatedBy);
		if (updated) {
			return Response.status(Response.Status.OK.getStatusCode()).build();
		}
		return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
	}

	@DELETE
	@Path("transaction/{transaction_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteWalletTransaction(@PathParam("transaction_id") UUID transactionId,
											 @QueryParam("institute_id") final int instituteId, @QueryParam("user_id") final UUID userId, @QueryParam("deleted_by") final UUID deletedBy) {
		if (instituteId <= 0 || userId == null || transactionId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request parameters"));
		}

		boolean updated = userWalletManager.deleteWalletTransaction(instituteId, transactionId, userId, deletedBy);
		if (updated) {
			return Response.status(Response.Status.OK.getStatusCode()).build();
		}
		return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
	}

}
