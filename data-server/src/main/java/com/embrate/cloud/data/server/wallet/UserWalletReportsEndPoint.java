package com.embrate.cloud.data.server.wallet;

import com.embrate.cloud.core.api.wallet.UserWalletReportType;
import com.embrate.cloud.core.lib.wallet.UserWalletReportsManager;
import com.lernen.cloud.core.api.report.ReportOutput;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;

/**
 * Currently this endpoint is not being used as wallet reports are to be served from fee module.
 *
 * <AUTHOR>
 */
@Path("/2.0/user-wallet/reports")
public class UserWalletReportsEndPoint {

    private final UserWalletReportsManager userWalletReportsManager;

    public UserWalletReportsEndPoint(UserWalletReportsManager userWalletReportsManager) {
        this.userWalletReportsManager = userWalletReportsManager;
    }


    @GET
    @Path("{institute_id}/generate-report/{report_type}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    public Response generateReport(@PathParam("institute_id") int instituteId,
                                   @PathParam("report_type") UserWalletReportType userWalletReportType,
                                   @QueryParam("academic_session_id") Integer academicSessionId,
                                   @QueryParam("start") Integer start,
                                   @QueryParam("end") Integer end, @QueryParam("requiredHeaders") String requiredHeaders,
                                   @QueryParam("requiredStandards") String requiredStandards,
                                   @QueryParam("student_status") String studentStatusCSV) {
        if (userWalletReportType == null) {
            return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
        }
        ReportOutput reportOutput = userWalletReportsManager.generateReport(instituteId, academicSessionId,
                requiredStandards, userWalletReportType, start, end, requiredHeaders);
        if (reportOutput == null) {
            return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
        }
        return Response.status(Response.Status.OK.getStatusCode())
                .header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
                .entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
    }

}
