package com.embrate.cloud.data.server.whatsapp;

import com.embrate.cloud.core.api.whatsapp.WhatsappContentPayload;
import com.embrate.cloud.core.api.whatsapp.WhatsappResponse;
import com.embrate.cloud.core.api.whatsapp.WhatsappSendDetails;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.sms.CustomNotificationPayload;
import com.lernen.cloud.core.api.student.RegisterStudentPayload;
import com.lernen.cloud.core.lib.notifications.NotificationManager;
import com.lernen.cloud.data.server.RestFormDataHandler;
import com.lernen.cloud.whatsapp.handler.CustomWhatsappHandler;
import com.lernen.cloud.whatsapp.handler.WhatsappManager;
import com.lernen.cloud.whatsapp.service.IWhatsappSender;
import com.sun.jersey.multipart.FormDataBodyPart;
import com.sun.jersey.multipart.FormDataParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContextException;
import org.springframework.util.CollectionUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

/**
 *
 * <AUTHOR>
 *
 */

@Path("/2.0/whatsapp")
public class CustomWhatsappSender {
	private static final Logger logger = LogManager.getLogger(CustomWhatsappSender.class);

	private final IWhatsappSender whatsappSender;
	private final CustomWhatsappHandler customWhatsappHandler;
	private final WhatsappManager whatsappManager;
	private final NotificationManager notificationManager;

	public CustomWhatsappSender(IWhatsappSender whatsappSender, CustomWhatsappHandler customWhatsappHandler,
								WhatsappManager whatsappManager, NotificationManager notificationManager) {
		this.whatsappSender = whatsappSender;
		this.customWhatsappHandler = customWhatsappHandler;
		this.whatsappManager = whatsappManager;
		this.notificationManager = notificationManager;
	}

	/**
	 * CAUTION: Use only for debugging sender service
	 * @param instituteId
	 * @param mobileNumber
	 * @param templateName
	 * @return
	 */
	@POST
	@Path("send")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response sendCustomWhatsapp(@QueryParam("mobile") String mobileNumber, @QueryParam("templateName") String templateName,
								  @QueryParam("institute_id") int instituteId) {
		if (StringUtils.isBlank(mobileNumber) || StringUtils.isBlank(templateName)) {
			logger.error("Invalid mobile number : {} or templateName : {}", mobileNumber, templateName);
			throw new ApplicationContextException("Invalid mobile number or templateName");
		}

		// Take count of number of message send and subtract that from counter table
		final WhatsappSendDetails whatsappSendDetails = whatsappSender.sendWhatsappMessage(instituteId,
				new WhatsappContentPayload(mobileNumber, templateName,
						null, null, null));
		final Map<String, String> results = new HashMap<>();
		results.put("whatsappMaskId", whatsappSendDetails.getMaskId());
		return Response.status(Response.Status.OK.getStatusCode()).entity(results).build();
	}

	@POST
	@Path("bulk-send")
	@Consumes(MediaType.MULTIPART_FORM_DATA)
	@Produces(MediaType.APPLICATION_JSON)
	public Response sendBulkCustomWhatsapp(@FormDataParam("custom_notification_payload") String studentCustomNotificationPayloadJson,
							   @FormDataParam("file") List<FormDataBodyPart> bodyParts, @QueryParam("user_id") UUID userId) {

		final List<FileData> files = RestFormDataHandler.getFileDataFromFormDataBodyPart(bodyParts);
		FileData image = null;
		if (!CollectionUtils.isEmpty(files)) {
			image = files.get(0);
		}
		CustomNotificationPayload customNotificationPayload =  GSON.fromJson(studentCustomNotificationPayloadJson, CustomNotificationPayload.class);
		final WhatsappResponse whatsappResponse = customWhatsappHandler.bulkSend(customNotificationPayload, image, userId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(whatsappResponse).build();
	}

	@GET
	@Path("balance/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response balance(@PathParam("institute_id") int instituteId) {
		final Double balance = whatsappManager.balance(instituteId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(balance).build();
	}

}
