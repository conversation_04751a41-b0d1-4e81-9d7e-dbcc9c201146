package com.lernen.cloud.data.server;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.common.FileData;
import com.sun.jersey.multipart.BodyPartEntity;
import com.sun.jersey.multipart.FormDataBodyPart;

/**
 *
 * <AUTHOR>
 *
 */
public class RestFormDataHandler {
	private static final Logger logger = LogManager.getLogger(RestFormDataHandler.class);

	public static List<FileData> getFileDataFromFormDataBodyPart(List<FormDataBodyPart> bodyParts) {
		final List<FileData> files = new ArrayList<>();
		if (CollectionUtils.isEmpty(bodyParts)) {
			return files;
		}
		for (int i = 0; i < bodyParts.size(); i++) {
			/*
			 * Casting FormDataBodyPart to BodyPartEntity, which can give us InputStream for
			 * uploaded file
			 */
			final BodyPartEntity bodyPartEntity = (BodyPartEntity) bodyParts.get(i).getEntity();
			final String fileName = bodyParts.get(i).getContentDisposition().getFileName();

			try {
				files.add(new FileData(IOUtils.toByteArray(bodyPartEntity.getInputStream()), fileName));
			} catch (final IOException e) {
				logger.error("Error occurred while converting to byte array file {}", fileName);
			}
		}
		return files;
	}
}
