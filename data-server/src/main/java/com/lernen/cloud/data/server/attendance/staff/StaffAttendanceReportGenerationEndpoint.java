/**
 * 
 */
package com.lernen.cloud.data.server.attendance.staff;

import com.embrate.cloud.pdf.reports.PdfReportGenerator;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceReportType;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceStatus;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.report.HTMLReportTable;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.attendance.staff.StaffAttendanceReportGenerator;
import com.lernen.cloud.core.lib.reports.ExcelReportGenerator;
import com.lernen.cloud.core.utils.report.ReportUtils;
import org.apache.commons.lang3.StringUtils;


import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */

@Path("/2.0/staff-attendance/reports")
public class StaffAttendanceReportGenerationEndpoint {

	private final StaffAttendanceReportGenerator staffAttendanceReportGenerator;
	private final ExcelReportGenerator excelReportGenerator;
	private final PdfReportGenerator pdfReportGenerator;

	public StaffAttendanceReportGenerationEndpoint(StaffAttendanceReportGenerator staffAttendanceReportGenerator,
												   ExcelReportGenerator excelReportGenerator,
												   PdfReportGenerator pdfReportGenerator) {
		this.staffAttendanceReportGenerator = staffAttendanceReportGenerator;
		this.excelReportGenerator = excelReportGenerator;
		this.pdfReportGenerator = pdfReportGenerator;
	}

//	@GET
//	@Path("/generate-report/{institute_id}/{report_type}")
//	@Produces(MediaType.APPLICATION_OCTET_STREAM)
//	public Response generateReport(@PathParam("institute_id") int instituteId,
//								   @PathParam("report_type") String reportTypeStr,
//								   @QueryParam("attendance_status") String attendanceStatusStr,
//								   @QueryParam("start") Integer start, @QueryParam("end") Integer end, @QueryParam("staffCategories") String staffCategoriesStr) {
//
//		if (instituteId <= 0) {
//			return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
//		}
//
//		if (StringUtils.isBlank(reportTypeStr)) {
//			return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
//		}
//
//		if (start <= 0) {
//			return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
//		}
//
//		if (end <= 0) {
//			return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
//		}
//
//		StaffAttendanceReportType staffAttendanceReportType = StaffAttendanceReportType.getStaffAttendanceReportType(reportTypeStr);
//
//		List<StaffAttendanceStatus> staffAttendanceStatusList = null;
//		if(!StringUtils.isBlank(attendanceStatusStr)) {
//			staffAttendanceStatusList = new ArrayList<StaffAttendanceStatus>();
//			String [] attendanceStatusArr = attendanceStatusStr.split(",");
//			for(String attendanceStatus : attendanceStatusArr) {
//				staffAttendanceStatusList.add(StaffAttendanceStatus.getStaffAttendanceStatus((attendanceStatus)));
//			}
//		}

//		final ReportOutput reportOutput = staffAttendanceReportGenerator.generateReport(instituteId,
//				staffAttendanceReportType, staffAttendanceStatusList, start, end,staffCategoriesStr);
//
//		if (reportOutput == null) {
//			return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
//		}
//
//		return Response.status(Response.Status.OK.getStatusCode())
//				.header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
//				.entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
//	}

	@GET
	@Path("view-report/{institute_id}/{report_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getReportData(@PathParam("institute_id") int instituteId, @PathParam("report_type") String reportTypeStr,
							  	@QueryParam("attendance_status") String attendanceStatusStr, @QueryParam("start") Integer start,
							  	@QueryParam("end") Integer end, @QueryParam("staffCategories") String staffCategoriesStr,
							  	@QueryParam("staff_status_str") String staffStatusStr, @QueryParam("download_format") DownloadFormat downloadFormat,
								@QueryParam("user_id") UUID userId) {

		final ReportDetails reportDetails = staffAttendanceReportGenerator.generateReport(instituteId,
				reportTypeStr, attendanceStatusStr, staffCategoriesStr, staffStatusStr, start, end, userId, downloadFormat);

		if(reportDetails == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to render report"));
		}

		HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
		return Response.status(Response.Status.OK.getStatusCode()).entity(htmlReportTable).build();

	}

	@GET
	@Path("/generate-report/{institute_id}/{report_type}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response generateReport(@PathParam("institute_id") int instituteId, @PathParam("report_type") String reportTypeStr,
								   @QueryParam("attendance_status") String attendanceStatusStr, @QueryParam("start") Integer start,
								   @QueryParam("end") Integer end, @QueryParam("staffCategories") String staffCategoriesStr,
								   @QueryParam("staff_status_str") String staffStatusStr, @QueryParam("download_format") DownloadFormat downloadFormat,
								   @QueryParam("user_id") UUID userId) throws IOException {

		if (StringUtils.isBlank(reportTypeStr)) {
			return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
		}

		final ReportDetails reportDetails = staffAttendanceReportGenerator.generateReport(instituteId,
				reportTypeStr, attendanceStatusStr, staffCategoriesStr, staffStatusStr, start, end, userId, downloadFormat);

		if (reportDetails == null) {
			return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
		}

		if (downloadFormat == DownloadFormat.PDF) {
			final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.STAFF_ATTENDANCE);
			if (documentOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
					.header("Content-Disposition", "filename=" + documentOutput.getName())
					.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
		} else {
			final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
			if (reportOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode())
					.header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
					.entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
		}
	}
}