package com.lernen.cloud.data.server.client.payment;

import java.util.List;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import com.lernen.cloud.core.api.client.payment.ClientBillingData;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.lib.client.payment.ClientPaymentManager;

/**
 *
 * <AUTHOR>
 *
 */
@Path("/2.0/client-payment")
public class ClientPaymentEndpoint {

	private final ClientPaymentManager clientPaymentManager;

	public ClientPaymentEndpoint(ClientPaymentManager clientPaymentManager) {
		this.clientPaymentManager = clientPaymentManager;
	}

	@GET
	@Path("billingdata")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getClientBillingDataList(@QueryParam("institute_id") int instituteId) {
		final List<ClientBillingData> clientBillingDataList = clientPaymentManager
				.getClientBillingDataList(instituteId);
		if (clientBillingDataList == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to get client billing data"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(clientBillingDataList).build();
	}

	@GET
	@Path("billingdata/latest")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getLatestClientBillingData(@QueryParam("institute_id") int instituteId) {
		final ClientBillingData clientBillingData = clientPaymentManager.getLatestClientBillingData(instituteId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(clientBillingData).build();
	}

	@POST
	@Path("billingdata")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getLatestClientBillingData(@QueryParam("institute_id") int instituteId,
			List<ClientBillingData> clientBillingDataList) {
		final boolean success = clientPaymentManager.addBillingEntry(instituteId, clientBillingDataList);
		if (!success) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to add client billing data"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}
}
