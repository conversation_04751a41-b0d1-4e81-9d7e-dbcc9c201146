package com.lernen.cloud.data.server.enquiry;

import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.enquiry.DatewiseEnquiryDetails;
import com.lernen.cloud.core.api.enquiry.EnquiryDetailsWithFollowup;
import com.lernen.cloud.core.api.enquiry.EnquiryDetailsPayload;
import com.lernen.cloud.core.api.enquiry.EnquiryStatus;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.lib.enquiry.EnquiryDetailsManager;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.*;


@Path("/2.0/enquiry")
public class EnquiryDetailsEndpoint {

    private static final Logger logger = LogManager.getLogger(EnquiryDetailsEndpoint.class);
    private final EnquiryDetailsManager enquiryDetailsManager;

    public EnquiryDetailsEndpoint(EnquiryDetailsManager enquiryDetailsManager) {
        this.enquiryDetailsManager = enquiryDetailsManager;
    }

    @POST
    @Path("enquiry")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addEnquiryDetails(@QueryParam("recaptcha_authentication") String recaptchaAuthenticationKey, EnquiryDetailsPayload enquiryDetailsPayload) {
        final String trackingId = enquiryDetailsManager.addEnquiryDetails(recaptchaAuthenticationKey, enquiryDetailsPayload);
        if (StringUtils.isEmpty(trackingId)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ENQUIRY_DETAILS, "Unable to add enquiry."));
        }
        Map<String, String> responseMap = new HashMap<>();
        responseMap.put("trackingId", trackingId);

        return Response.status(Response.Status.OK.getStatusCode()).entity(responseMap).build();

    }

    @GET
    @Path("enquiry-details-with-tracking-id")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getEnquiryDetailsByTrackingId(@QueryParam("institute_unique_code") UUID instituteUniqueCode, @QueryParam("tracking_id") String trackingId) {
        final EnquiryDetailsWithFollowup enquiryDetailsWithFollowup = enquiryDetailsManager.getEnquiryDetailsWithTrackingId(instituteUniqueCode, trackingId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(enquiryDetailsWithFollowup).build();
    }

    @PUT
    @Path("enquiry-status")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateEnquiryStatus(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, @QueryParam("academic_session_id") int academicSessionId, EnquiryDetailsPayload enquiryDetailsPayload) {
        final boolean updateEnquiryStatus = enquiryDetailsManager.updateEnquiryStatus(instituteId, userId, enquiryDetailsPayload);
        if (!updateEnquiryStatus) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ENQUIRY_DETAILS, "Unable to update enquiry status."));
        }

        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @GET
    @Path("enquiry-details")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getEnquiryDetails(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                          @QueryParam("academic_session_id") int academicSessionId, @QueryParam("enquiry_status_str") String enquiryStatusSetStr, @QueryParam("limit") Integer limit, @QueryParam("offset") Integer offset) {
        Set<EnquiryStatus> enquiryStatusSet = EnquiryStatus.getEnquiryStatusSet(enquiryStatusSetStr);
        final List<EnquiryDetailsWithFollowup> enquiryDetailsWithFollowupList = enquiryDetailsManager.getEnquiryDetails(instituteId, userId, academicSessionId, enquiryStatusSet, limit, offset);
        return Response.status(Response.Status.OK.getStatusCode()).entity(enquiryDetailsWithFollowupList).build();
    }

    @GET
    @Path("enquiry-details-with-pagination")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getEnquiryDetailsPagination(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                                    @QueryParam("academic_session_id") int academicSessionId, @QueryParam("enquiry_status_str") String enquiryStatusSetStr, @QueryParam("limit") Integer limit, @QueryParam("offset") Integer offset) {
        Set<EnquiryStatus> enquiryStatusSet = EnquiryStatus.getEnquiryStatusSet(enquiryStatusSetStr);
        final SearchResultWithPagination<EnquiryDetailsWithFollowup> enquiryDetailsWithPagination = enquiryDetailsManager.getEnquiryDetailsWithPagination(instituteId, userId, academicSessionId, enquiryStatusSet, limit, offset);
        return Response.status(Response.Status.OK.getStatusCode()).entity(enquiryDetailsWithPagination).build();
    }

    @GET
    @Path("datewise-enquiry-details")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getEnquiryDetailsDatewise(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                                  @QueryParam("academic_session_id") int academicSessionId, @QueryParam("enquiry_status_str") String enquiryStatusSetStr, @QueryParam("limit") Integer limit, @QueryParam("offset") Integer offset) {
        Set<EnquiryStatus> enquiryStatusSet = EnquiryStatus.getEnquiryStatusSet(enquiryStatusSetStr);
        List<DatewiseEnquiryDetails> enquiryDetailsList = enquiryDetailsManager.getEnquiryDetailsDatewise(instituteId, userId, academicSessionId, enquiryStatusSet, limit, offset);
        return Response.status(Response.Status.OK.getStatusCode()).entity(enquiryDetailsList).build();
    }

    @GET
    @Path("enquiry-detail/{enquiry_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getEnquiryDetailsById(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                              @QueryParam("academic_session_id") int academicSessionId, @PathParam("enquiry_id") UUID enquiryId) {
        final EnquiryDetailsWithFollowup enquiryDetailsWithFollowup = enquiryDetailsManager.getEnquiryDetailsByEnquiryId(instituteId, userId, academicSessionId, enquiryId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(enquiryDetailsWithFollowup).build();
    }

}
