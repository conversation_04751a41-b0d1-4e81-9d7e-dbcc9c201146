package com.lernen.cloud.data.server.fees;

import com.embrate.cloud.pdf.reports.PdfReportGenerator;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.report.ReportRowColumnDataVisibility;
import com.lernen.cloud.core.api.fees.FeeReportDataType;
import com.lernen.cloud.core.api.fees.FeesReportType;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.report.HTMLReportTable;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.fees.payment.FeeReportDetailsGenerator;
import com.lernen.cloud.core.lib.reports.ExcelReportGenerator;
import com.lernen.cloud.core.utils.report.ReportUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Path("/2.0/fees/reports")
public class FeesReportGenerationEndPoint {
    private static final Logger logger = LogManager.getLogger(FeesReportGenerationEndPoint.class);

    private final FeeReportDetailsGenerator feeReportDetailsGenerator;

    private final ExcelReportGenerator excelReportGenerator;

    private final PdfReportGenerator pdfReportGenerator;

    public FeesReportGenerationEndPoint(FeeReportDetailsGenerator feeReportDetailsGenerator, ExcelReportGenerator excelReportGenerator,
                                        PdfReportGenerator pdfReportGenerator) {
        this.feeReportDetailsGenerator = feeReportDetailsGenerator;
        this.excelReportGenerator = excelReportGenerator;
        this.pdfReportGenerator = pdfReportGenerator;
    }

    @GET
	@Path("headers/{report_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getReportHeader(@PathParam("report_type") FeesReportType feesReportType) {
		if (feesReportType == null) {
			return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
		}
		return Response.status(Response.Status.OK.getStatusCode())
				.entity(feeReportDetailsGenerator.getReportHeader(feesReportType)).build();
	}

	@GET
	@Path("headers")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getAllReportHeader() {
		return Response.status(Response.Status.OK.getStatusCode()).entity(feeReportDetailsGenerator.getReportHeader())
				.build();
	}

    @GET
    @Path("{institute_id}/view-report/{report_type}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getReport(@PathParam("institute_id") int instituteId,
                              @PathParam("report_type") FeesReportType reportType,
                              @QueryParam("academic_session_id") Integer academicSessionId, @QueryParam("requiredHeaders") String requiredHeaders,
                              @QueryParam("multiple_academic_session_ids") String multipleAcademicSessionIds, @QueryParam("start") Integer start,
                              @QueryParam("end") Integer end, @QueryParam("fee_due_date") Integer feeDueDate, @QueryParam("fee_ids") String feeIds,
                              @QueryParam("required_standards") String requiredStandards, @QueryParam("student_status") String studentStatusCSV,
                              @QueryParam("user_id") UUID userId, @QueryParam("download_format") DownloadFormat downloadFormat, @QueryParam("fee_head_ids") String feeHeadIds,
                              @QueryParam("fee_payment_transaction_status") FeePaymentTransactionStatus feePaymentTransactionStatus,
                              @QueryParam("fee_report_data_type") FeeReportDataType feeReportDataType, @QueryParam("transaction_mode") String transactionMode, @QueryParam("hide_student_with_zero_fees_assignment") boolean hideStudentWithZeroFeesAssignment, @QueryParam("report_row_column_data_visibility") ReportRowColumnDataVisibility reportRowColumnDataVisibility) throws IOException {

        final ReportDetails reportDetails = feeReportDetailsGenerator.generateReport(instituteId, academicSessionId,
                reportType, start, end, feeDueDate, feeIds, requiredStandards, feePaymentTransactionStatus,requiredHeaders,
                userId, downloadFormat,studentStatusCSV,feeHeadIds,multipleAcademicSessionIds, feeReportDataType,transactionMode, hideStudentWithZeroFeesAssignment, reportRowColumnDataVisibility);

        if(reportDetails == null){
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to generate report"));
        }

        HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
        return Response.status(Response.Status.OK.getStatusCode()).entity(htmlReportTable).build();

    }

    @GET
    @Path("{institute_id}/generate-report/{report_type}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    public Response generateReport(@PathParam("institute_id") int instituteId,
           @PathParam("report_type") FeesReportType reportType,
           @QueryParam("academic_session_id") Integer academicSessionId,@QueryParam("requiredHeaders") String requiredHeaders,
           @QueryParam("multiple_academic_session_ids") String multipleAcademicSessionIds, @QueryParam("start") Integer start,
           @QueryParam("end") Integer end, @QueryParam("fee_due_date") Integer feeDueDate, @QueryParam("fee_ids") String feeIds,
           @QueryParam("required_standards") String requiredStandards, @QueryParam("student_status") String studentStatusCSV,
           @QueryParam("user_id") UUID userId, @QueryParam("download_format") DownloadFormat downloadFormat, @QueryParam("fee_head_ids") String feeHeadIds,
           @QueryParam("fee_payment_transaction_status") FeePaymentTransactionStatus feePaymentTransactionStatus,
           @QueryParam("fee_report_data_type") FeeReportDataType feeReportDataType,@QueryParam("transaction_mode") String transactionMode, @QueryParam("hide_student_with_zero_fees_assignment") boolean hideStudentWithZeroFeesAssignment,  @QueryParam("report_row_column_data_visibility") ReportRowColumnDataVisibility reportRowColumnDataVisibility) throws IOException {

        final ReportDetails reportDetails = feeReportDetailsGenerator.generateReport(instituteId, academicSessionId,
                reportType, start, end, feeDueDate, feeIds, requiredStandards, feePaymentTransactionStatus,requiredHeaders,
                userId, downloadFormat,studentStatusCSV,feeHeadIds,multipleAcademicSessionIds, feeReportDataType,transactionMode, hideStudentWithZeroFeesAssignment, reportRowColumnDataVisibility);
           
        if (downloadFormat == DownloadFormat.PDF) {
            final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.FEES);
            if (documentOutput == null) {
                return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
            }
            return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
                    .header("Content-Disposition", "filename=" + documentOutput.getName())
                    .entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
        } else {
            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
            if (reportOutput == null) {
                return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
            }
            return Response.status(Response.Status.OK.getStatusCode())
                        .header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
                        .entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
        }
    }
}