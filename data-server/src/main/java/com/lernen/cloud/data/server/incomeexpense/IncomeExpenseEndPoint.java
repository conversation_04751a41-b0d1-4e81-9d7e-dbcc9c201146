/**
 * 
 */
package com.lernen.cloud.data.server.incomeexpense;

import com.google.gson.Gson;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.incomeexpense.*;
import com.lernen.cloud.core.lib.incomeexpense.IncomeExpenseManager;
import com.sun.jersey.multipart.BodyPartEntity;
import com.sun.jersey.multipart.FormDataBodyPart;
import com.sun.jersey.multipart.FormDataParam;
import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
@Path("/2.0/income-expense")

public class IncomeExpenseEndPoint {
	private static final Logger logger = LogManager.getLogger(IncomeExpenseEndPoint.class);
	private static final Gson GSON = new Gson();
	
	private static IncomeExpenseManager incomeExpenseManager;
	
	public IncomeExpenseEndPoint(IncomeExpenseManager incomeExpenseManager) {
		this.incomeExpenseManager = incomeExpenseManager;
	}
	
	@POST
	@Path("/add-category")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addIncomeExpenseCategory(IncomeExpenseCategory incomeExpenseCategoryPayload,
			@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId) {
		
		final UUID categoryId = incomeExpenseManager.addIncomeExpenseCategory(incomeExpenseCategoryPayload, instituteId, userId);
		if (categoryId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS, "Unable to add Income Expense category details"));
		}

		return Response.status(Response.Status.OK.getStatusCode()).build();
		
	}
	
//	@GET
//	@Path("/category/{category_id}")
//	@Consumes(MediaType.APPLICATION_JSON)
//	@Produces(MediaType.APPLICATION_JSON)
//	public Response getIncomeExpenseCategory(@PathParam("category_id") UUID categoryId,
//			@QueryParam("institute_id") int instituteId) {
//		
//		final IncomeExpenseCategory incomeExpenseCategory = incomeExpenseManager.getIncomeExpenseCategory(
//				categoryId, instituteId);
//		
//		if (incomeExpenseCategory == null) {
//			throw new ApplicationException(
//					new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS, "Unable to get Income Expense category details"));
//		}
//		return Response.status(Response.Status.OK.getStatusCode()).entity(incomeExpenseCategory).build();
//	}
	
	@GET
	@Path("/category/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getIncomeExpenseCategorys(@PathParam("institute_id") int instituteId) {
		
		final List<IncomeExpenseCategory> incomeExpenseCategoryList = incomeExpenseManager.getIncomeExpenseCategorys(instituteId);
		
		if (incomeExpenseCategoryList == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS, "Unable to get Income Expense category details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(incomeExpenseCategoryList).build();
	}
	
	@POST
	@Path("/update-category")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateIncomeExpenseCategory(IncomeExpenseCategory incomeExpenseCategoryPayload,
			@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId) {
		
		final UUID categoryId = incomeExpenseManager.updateIncomeExpenseCategory(incomeExpenseCategoryPayload, instituteId, userId);
		if (categoryId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS, "Unable to update Income Expense category details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(categoryId).build();
	}
	
	@GET
	@Path("/delete-category/{category_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	public Response deleteIncomeExpenseCategory(@PathParam("category_id") UUID categoryId,
			@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId) {
		final boolean deleteIncomeExpenseCategory = incomeExpenseManager.deleteIncomeExpenseCategory(categoryId, instituteId, userId);
		if (!deleteIncomeExpenseCategory) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS,
					"Unable to delete Income Expense category details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}
	
	@POST
	@Path("/add-entity")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addIncomeExpenseEntity(IncomeExpenseEntity incomeExpenseEntityPayload,
			@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId) {
		
		final UUID entityId = incomeExpenseManager.addIncomeExpenseEntity(incomeExpenseEntityPayload, instituteId, userId);
		if (entityId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS, "Unable to add Income Expense entity details"));
		}

		return Response.status(Response.Status.OK.getStatusCode()).build();
		
	}
	
//	@GET
//	@Path("/entity/{entity_id}")
//	@Consumes(MediaType.APPLICATION_JSON)
//	@Produces(MediaType.APPLICATION_JSON)
//	public Response getIncomeExpenseEntity(@PathParam("entity_id") UUID entityId,
//			@QueryParam("institute_id") int instituteId) {
//		
//		final IncomeExpenseEntity incomeExpenseEntity = incomeExpenseManager.getIncomeExpenseEntity(
//				entityId, instituteId);
//		
//		if (incomeExpenseEntity == null) {
//			throw new ApplicationException(
//					new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS, "Unable to get Income Expense entity details"));
//		}
//		return Response.status(Response.Status.OK.getStatusCode()).entity(incomeExpenseEntity).build();
//	}
	
	@GET
	@Path("/entity/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getIncomeExpenseEntities(@PathParam("institute_id") int instituteId) {
		final List<IncomeExpenseEntity> incomeExpenseEntityList = incomeExpenseManager.getIncomeExpenseEntities(instituteId);
		if (incomeExpenseEntityList == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS, "Unable to get Income Expense entities details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(incomeExpenseEntityList).build();
	}
	
	@POST
	@Path("/update-entity")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateIncomeExpenseEntity(IncomeExpenseEntity incomeExpenseEntityPayload,
			@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId) {
		
		final UUID entityId = incomeExpenseManager.updateIncomeExpenseEntity(incomeExpenseEntityPayload, instituteId, userId);
		if (entityId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS, "Unable to update Income Expense entity details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}
	
	@GET
	@Path("/delete-entity/{entity_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	public Response deleteIncomeExpenseEntity(@PathParam("entity_id") UUID entityId,
			@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId) {
		final boolean deleteIncomeExpenseEntity = incomeExpenseManager.deleteIncomeExpenseEntity(entityId, instituteId, userId);
		if (!deleteIncomeExpenseEntity) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS,
					"Unable to delete Income Expense entity details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}
	
	@POST
	@Path("/add-transaction")
	@Consumes(MediaType.MULTIPART_FORM_DATA)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addIncomeExpenseTransactionDetails(@FormDataParam("add_transaction_payload") String incomeExpenseTransactionDetailsPayloadJson,
			@FormDataParam("file") List<FormDataBodyPart> bodyParts, @FormDataParam("documentName") String documentName,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
													   @QueryParam("user_id") UUID userId) {
		
		final List<FileData> files = getFileDataFromFormDataBodyPart(bodyParts);
		FileData image = null;

		if (!CollectionUtils.isEmpty(files)) {
			image = files.get(0);
		}
		final IncomeExpenseTransactionDetailsPayload incomeExpenseTransactionDetailsPayload = GSON.fromJson(incomeExpenseTransactionDetailsPayloadJson,
				IncomeExpenseTransactionDetailsPayload.class);
		final UUID transactionId = incomeExpenseManager.addIncomeExpenseTransactionDetails(incomeExpenseTransactionDetailsPayload, instituteId,
				academicSessionId, documentName, image, userId);
		if (transactionId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS, "Unable to add transaction details"));
		}

		final IncomeExpenseTransactionDetails incomeExpenseTransactionDetails = incomeExpenseManager.getIncomeExpenseTransactionDetails(
				transactionId, instituteId, academicSessionId);

		return Response.status(Response.Status.OK.getStatusCode()).entity(incomeExpenseTransactionDetails).build();
		
	}
	
	private List<FileData> getFileDataFromFormDataBodyPart(List<FormDataBodyPart> bodyParts) {
		final List<FileData> files = new ArrayList<>();
		if (CollectionUtils.isEmpty(bodyParts)) {
			return files;
		}
		for (int i = 0; i < bodyParts.size(); i++) {
			final BodyPartEntity bodyPartEntity = (BodyPartEntity) bodyParts.get(i).getEntity();
			final String fileName = bodyParts.get(i).getContentDisposition().getFileName();

			try {
				files.add(new FileData(IOUtils.toByteArray(bodyPartEntity.getInputStream()), fileName));
			} catch (final IOException e) {
				logger.error("Error occurred while converting to byte array file {}", fileName);
			}
		}
		return files;
	}
	
//	@GET
//	@Path("/{transaction_id}")
//	@Consumes(MediaType.APPLICATION_JSON)
//	@Produces(MediaType.APPLICATION_JSON)
//	public Response getIncomeExpenseTransactionDetails(@PathParam("transaction_id") UUID transactionId,
//			@QueryParam("academic_session_id") int academicSessionId, @QueryParam("institute_id") int instituteId) {
//		
//		final IncomeExpenseTransactionDetails incomeExpenseDetails = incomeExpenseManager.getIncomeExpenseTransactionDetails(
//				transactionId, instituteId, academicSessionId);
//		
//		if (incomeExpenseDetails == null) {
//			throw new ApplicationException(
//					new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS, "Unable to get transaction details"));
//		}
//		return Response.status(Response.Status.OK.getStatusCode()).entity(incomeExpenseDetails).build();
//	}
	
	@GET
	@Path("/transactions/{transaction_type}/{academic_session_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getIncomeExpenseTransactionDetails(@PathParam("transaction_type") String incomeExpenseTransactionTypeString, @PathParam("academic_session_id") int academicSessionId, 
			@QueryParam("institute_id") int instituteId) {
		IncomeExpenseTransactionType incomeExpenseTransactionType = IncomeExpenseTransactionType.getTransactionType(incomeExpenseTransactionTypeString);
		final List<IncomeExpenseTransactionDetails> incomeExpenseDetailsList = incomeExpenseManager.getIncomeExpenseTransactionDetails(incomeExpenseTransactionType, instituteId, academicSessionId);
		if (incomeExpenseDetailsList == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS, "Unable to get transactions"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(incomeExpenseDetailsList).build();
	}
	
	@PUT
	@Path("/update-transaction-status/{transaction_id}/{transaction_status}/{updated_by}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateIncomeExpenseStatus(@PathParam("updated_by") UUID updatedBy,
			@PathParam("transaction_status") String incomeExpenseTransactionStatusString, 
			@PathParam("transaction_id") UUID transactionId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
		IncomeExpenseTransactionStatus incomeExpenseTransactionStatus =  IncomeExpenseTransactionStatus.getIncomeExpenseTransactionStatus(incomeExpenseTransactionStatusString);
		final boolean cancelIncomeExpense = incomeExpenseManager.updateIncomeExpenseStatus(transactionId, incomeExpenseTransactionStatus, updatedBy, academicSessionId, instituteId);
		if (!cancelIncomeExpense) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS, "Unable to cancel transaction details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}
	
//	@GET
//	@Path("/delete-transaction/{transaction_id}")
//	@Consumes(MediaType.APPLICATION_JSON)
//	public Response deleteIncomeExpenseTransactionDetails(@PathParam("transaction_id") UUID transactionId, @QueryParam("institute_id") int instituteId,
//			@QueryParam("academic_session_id") int academicSessionId) {
//		final boolean deleteIncomeExpense = incomeExpenseManager.deleteIncomeExpenseTransactionDetails(transactionId, instituteId, academicSessionId);
//		if (!deleteIncomeExpense) {
//			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSACTION_DETAILS,
//					"Unable to delete Income Expense transaction details"));
//		}
//		return Response.status(Response.Status.OK.getStatusCode()).build();
//	}
//	
//	@GET
//	@Path("/download-document/{transaction_id}/{document_id}")
//	@Produces(MediaType.APPLICATION_OCTET_STREAM)
//	public Response downloadTransactionDocument(@PathParam("transaction_id") UUID transactionId,
//			@PathParam("document_id") UUID documentId, @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
//		final DownloadDocumentWrapper<IncomeExpenseTransactionDocument> documentWrapper = incomeExpenseManager.downloadTransactionDocument(instituteId,
//				transactionId, documentId, academicSessionId);
//		if (documentWrapper == null || documentWrapper.getDocumentContent() == null) {
//			throw new ApplicationException(
//					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Unable to download file"));
//		}
//		return Response.status(Response.Status.OK.getStatusCode())
//				.header("Content-Disposition",
//						"attachment;filename=" + documentWrapper.getDocumentDetails().getDocumentName())
//				.entity(new ByteArrayInputStream(documentWrapper.getDocumentContent().toByteArray())).build();
//	}
	
	@GET
	@Path("/total-income-expense/{institute_id}/{academic_session_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getTotalIncomeExpense(@PathParam("institute_id") int instituteId, @PathParam("academic_session_id") Integer academicSessionId) {
		final IncomeExpenseAggregatedData incomeExpenseAggregatedData = incomeExpenseManager
				.getTotalIncomeExpense(instituteId, academicSessionId);
		if (incomeExpenseAggregatedData == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Error occured while fetching transactions stats "));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(incomeExpenseAggregatedData).build();
	}
}
