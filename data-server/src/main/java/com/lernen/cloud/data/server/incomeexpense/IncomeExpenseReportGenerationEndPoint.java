/**
 * 
 */
package com.lernen.cloud.data.server.incomeexpense;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.UUID;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import com.embrate.cloud.pdf.reports.PdfReportGenerator;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.incomeexpense.IncomeExpenseReportType;
import com.lernen.cloud.core.api.incomeexpense.IncomeExpenseTransactionType;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.report.HTMLReportTable;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.incomeexpense.IncomeExpenseReportGenerator;
import com.lernen.cloud.core.lib.reports.ExcelReportGenerator;
import com.lernen.cloud.core.utils.report.ReportUtils;

/**
 * <AUTHOR>
 *
 */
@Path("/2.0/income-expense/reports")

public class IncomeExpenseReportGenerationEndPoint {
	
	private final IncomeExpenseReportGenerator incomeExpenseReportGenerator;

	private final PdfReportGenerator pdfReportGenerator;

	private final ExcelReportGenerator excelReportGenerator;
	
	public IncomeExpenseReportGenerationEndPoint(IncomeExpenseReportGenerator incomeExpenseReportGenerator, PdfReportGenerator pdfReportGenerator, ExcelReportGenerator excelReportGenerator) {
		this.incomeExpenseReportGenerator = incomeExpenseReportGenerator;
		this.pdfReportGenerator = pdfReportGenerator;
		this.excelReportGenerator = excelReportGenerator;
	}

	@GET
    @Path("{institute_id}/view-report/{report_type}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getReport(@PathParam("institute_id") int instituteId,
			@PathParam("report_type") IncomeExpenseReportType reportType,
			@QueryParam("start") Integer start,
			@QueryParam("end") Integer end, 
			@QueryParam("income_expense_transaction_type") String incomeExpenseTransactionTypeString,
			@QueryParam("user_id") UUID userId,@QueryParam("download_format") DownloadFormat downloadFormat) throws IOException {
		
        IncomeExpenseTransactionType incomeExpenseTransactionType = IncomeExpenseTransactionType.getTransactionType(incomeExpenseTransactionTypeString);
		final ReportDetails reportDetails = incomeExpenseReportGenerator.generateReport(instituteId,
				reportType, start, end, incomeExpenseTransactionType, userId,downloadFormat);

        if(reportDetails == null){
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to render report"));
        }
	
        HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
        return Response.status(Response.Status.OK.getStatusCode()).entity(htmlReportTable).build();
    }
	
	@GET
	@Path("{institute_id}/generate-report/{report_type}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response generateReport(@PathParam("institute_id") int instituteId,
			@PathParam("report_type") IncomeExpenseReportType reportType,
			@QueryParam("start") Integer start,
			@QueryParam("end") Integer end, 
			@QueryParam("income_expense_transaction_type") String incomeExpenseTransactionTypeString,
			@QueryParam("user_id") UUID userId,@QueryParam("download_format") DownloadFormat downloadFormat) throws IOException {

		IncomeExpenseTransactionType incomeExpenseTransactionType = IncomeExpenseTransactionType.getTransactionType(incomeExpenseTransactionTypeString);
		final ReportDetails reportDetails = incomeExpenseReportGenerator.generateReport(instituteId,
				reportType, start, end, incomeExpenseTransactionType, userId,downloadFormat);

		if (reportDetails == null) {
			return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
		}	

		if (downloadFormat == DownloadFormat.PDF) {
			final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.INCOME_EXPENSE);
			if (documentOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
					.header("Content-Disposition", "filename=" + documentOutput.getName())
					.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
		} else {
			final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
			if (reportOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode())
					.header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
					.entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
		}
	}
}
