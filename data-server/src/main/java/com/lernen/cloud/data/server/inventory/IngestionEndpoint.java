//package com.lernen.cloud.data.server.inventory;
//
//import javax.ws.rs.Consumes;
//import javax.ws.rs.POST;
//import javax.ws.rs.Path;
//import javax.ws.rs.PathParam;
//import javax.ws.rs.Produces;
//import javax.ws.rs.core.MediaType;
//import javax.ws.rs.core.Response;
//
//import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
//import com.lernen.cloud.core.api.exception.ApplicationException;
//import com.lernen.cloud.core.api.exception.ErrorResponse;
//import com.lernen.cloud.core.api.ingest.StockIngestionRequest;
//
///**
// * 
// * <AUTHOR>
// *
// */
//@Path("/2.0/inventory/ingestion")
//public class IngestionEndpoint {
//
////	private final StockIngester stockIngester;
////
////	public IngestionEndpoint(StockIngester stockIngester) {
////		this.stockIngester = stockIngester;
////	}
////
////	@POST
////	@Path("/{institute_id}/stock")
////	@Consumes(MediaType.APPLICATION_JSON)
////	@Produces(MediaType.APPLICATION_JSON)
////	public Response ingestStock(@PathParam("institute_id") int instituteId,
////			StockIngestionRequest stockIngestionRequest) {
////		boolean success = stockIngester.ingestFile(instituteId, stockIngestionRequest.getFilePath());
////
////		if (!success) {
////			throw new ApplicationException(
////					new ErrorResponse(ApplicationErrorCode.INAVLID_FILE, "Could not ingest given file"));
////		}
////		return Response.status(Response.Status.OK.getStatusCode()).entity(Boolean.TRUE).build();
////	}
//
//}
