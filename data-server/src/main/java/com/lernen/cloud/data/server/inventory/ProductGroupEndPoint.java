package com.lernen.cloud.data.server.inventory;

import java.util.List;
import java.util.UUID;

import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.inventory.ProductGroupData;
import com.lernen.cloud.core.api.inventory.ProductGroupPayload;
import com.lernen.cloud.core.lib.inventory.ProductGroupManager;

/**
 * 
 * <AUTHOR>
 *
 */

@Path("/2.0/product-group")
public class ProductGroupEndPoint {

	private final ProductGroupManager productGroupManager;

	public ProductGroupEndPoint(ProductGroupManager productGroupManager) {
		this.productGroupManager = productGroupManager;
	}

	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addProductGroup(ProductGroupPayload productGroupPayload) {

		UUID productGroupId = productGroupManager.addProductGroup(productGroupPayload);
		if (productGroupId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_GROUP, "Unable to add product group."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@DELETE
	@Path("/instituteId/{institute_id}/groupId/{group_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteProductGroup(@PathParam("institute_id") int instituteId,
			@PathParam("group_id") String groupId) {

		boolean deletedProductGroup = productGroupManager.deleteProductGroup(instituteId, groupId);
		if (!deletedProductGroup) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_GROUP, "Unable to delete product group."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(deletedProductGroup).build();
	}

	@PUT
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateProductGroup(ProductGroupPayload productGroupPayload) {

		UUID productGroupId = productGroupManager.updateProductGroup(productGroupPayload);
		if (productGroupId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_GROUP, "Unable to update product group."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(productGroupId).build();
	}

	@GET
	@Path("/instituteId/{institute_id}/groupId/{group_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getProductGroupByGroupId(@PathParam("institute_id") int instituteId,
			@PathParam("group_id") UUID groupId) {

		ProductGroupData productGroupData = productGroupManager.getProductGroupByGroupId(instituteId, groupId);
		if (productGroupData == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_GROUP, "No product group found."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(productGroupData).build();
	}

	@GET
	@Path("/search/instituteId/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response searchProductGroup(@PathParam("institute_id") int instituteId,
			@QueryParam("search_text") String searchText) {
		List<ProductGroupData> productGroupDataList = productGroupManager.searchProductGroup(instituteId, searchText);
		if (productGroupDataList == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PRODUCT_GROUP, "No product group found."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(productGroupDataList).build();
	}
}
