package com.lernen.cloud.data.server.sms;

import com.embrate.cloud.core.api.whatsapp.WhatsappContentPayload;
import com.embrate.cloud.core.api.whatsapp.WhatsappSendDetails;
import com.lernen.cloud.core.api.sms.CustomNotificationPayload;
import com.lernen.cloud.core.api.sms.SMSContentPayload;
import com.lernen.cloud.core.api.sms.SMSResponse;
import com.lernen.cloud.core.api.sms.SMSSendDetails;
import com.lernen.cloud.core.lib.notifications.NotificationManager;
import com.lernen.cloud.core.utils.rest.RestAPIHandler;
import com.lernen.cloud.sms.handler.CustomSMSHandler;
import com.lernen.cloud.sms.service.ISMSSender;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContextException;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 *
 */

@Path("/2.0/sms")
public class CustomSMSSender {
	private static final Logger logger = LogManager.getLogger(CustomSMSSender.class);

	private final ISMSSender smsSender;
	private final CustomSMSHandler customSMSHandler;
	private final NotificationManager notificationManager;

	public CustomSMSSender(ISMSSender smsSender, CustomSMSHandler customSMSHandler,
			NotificationManager notificationManager) {
		this.smsSender = smsSender;
		this.customSMSHandler = customSMSHandler;
		this.notificationManager = notificationManager;
	}

	/**
	 * CAUTION: Use only for debugging sender service
	 *
	 * @param mobileNumber
	 * @param message
	 * @return
	 */
	@POST
	@Path("send")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response sendCustomSMS(@QueryParam("mobile") String mobileNumber, @QueryParam("message") String message,
								  @QueryParam("institute_id") int instituteId) {
		if (StringUtils.isBlank(mobileNumber) || StringUtils.isBlank(message)) {
			logger.error("Invalid mobile number : {} or message : {}", mobileNumber, message);
			throw new ApplicationContextException("Invalid mobile number or message");
		}

		// Take count of number of message send and substract that from counter table
		final SMSSendDetails smsSendDetails = smsSender.sendSMS(instituteId, mobileNumber, new SMSContentPayload(message, null));
		final Map<String, String> results = new HashMap<>();
		results.put("smsRequestId", smsSendDetails.getSmsUniqueId());
		return Response.status(Response.Status.OK.getStatusCode()).entity(results).build();
	}

	@POST
	@Path("bulk-send")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response sendCustomSMSToStudents(@QueryParam("user_id") UUID userId,
			CustomNotificationPayload studentCustomNotificationPayload) {

		final SMSResponse smsResponse = customSMSHandler.bulkSend(studentCustomNotificationPayload, userId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(smsResponse).build();
	}


//	@GET
//	@Path("history/{user_type}")
//	@Consumes(MediaType.APPLICATION_JSON)
//	@Produces(MediaType.APPLICATION_JSON)
//	public Response getRemindersHistory(@PathParam("user_type") UserType userType,
//			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
//
//		final NotificationHistory<NotificationDetails> notificationHistory = notificationManager.getNotificationHistory(
//				instituteId, academicSessionId, NotificationType.CUSTOM, DeliveryMode.SMS, userType);
//
//		return Response.status(Response.Status.OK.getStatusCode()).entity(notificationHistory).build();
//	}

}
