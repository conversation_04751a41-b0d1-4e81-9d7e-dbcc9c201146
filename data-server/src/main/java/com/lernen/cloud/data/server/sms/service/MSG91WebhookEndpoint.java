package com.lernen.cloud.data.server.sms.service;

import java.io.IOException;
import java.util.List;

import javax.ws.rs.Consumes;
import javax.ws.rs.FormParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lernen.cloud.core.api.sms.msg91.MSG91SMSWebhookPayload;
import com.lernen.cloud.core.lib.sms.MSG91SMSWebhookManager;

/**
 *
 * <AUTHOR>
 *
 */
@Path("/2.0/msg91-webhook")
public class MSG91WebhookEndpoint {

	private static final Logger logger = LogManager.getLogger(MSG91WebhookEndpoint.class);

	private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
	private final MSG91SMSWebhookManager msg91SMSWebhookManager;

	public MSG91WebhookEndpoint(MSG91SMSWebhookManager msg91SMSWebhookManager) {
		this.msg91SMSWebhookManager = msg91SMSWebhookManager;
	}

	@POST
	@Path("sms")
	@Consumes(MediaType.APPLICATION_FORM_URLENCODED)
	@Produces(MediaType.APPLICATION_JSON)
	public Response ingestSMSWebhook(@FormParam("data") String payload)
			throws JsonParseException, JsonMappingException, IOException {
		logger.info("Incoming webhook payload {} ", payload);
		if (StringUtils.isBlank(payload)) {
			logger.error("Invalid webhook payload = {}. Skipping.", payload);
			return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
		}

		final List<MSG91SMSWebhookPayload> msg91smsWebhookPayloads = OBJECT_MAPPER.readValue(payload,
				new TypeReference<List<MSG91SMSWebhookPayload>>() {
				});

		if (msg91SMSWebhookManager.handleWebhookRequest(msg91smsWebhookPayloads)) {
			logger.info("Successfully processed webhook {}", msg91smsWebhookPayloads);
			return Response.status(Response.Status.OK.getStatusCode()).build();
		}
		logger.error("Unable to process webhook {}", msg91smsWebhookPayloads);
		return Response.status(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()).build();

	}

}
