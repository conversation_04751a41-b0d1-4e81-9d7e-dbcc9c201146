package com.lernen.cloud.data.server.staff;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.report.HTMLReportTable;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.utils.report.ReportUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.embrate.cloud.pdf.reports.PdfReportGenerator;
import com.lernen.cloud.core.lib.reports.ExcelReportGenerator;
import com.lernen.cloud.core.lib.staff.StaffReportGenerator;
import com.lernen.cloud.core.api.staff.StaffFilterationCriteria;
import com.lernen.cloud.core.api.staff.StaffReportType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportOutput;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.UUID;



@Path("/2.0/staff-management/reports")
public class StaffReportEndpoint {
    private static final Logger logger = LogManager.getLogger(StaffReportEndpoint.class);
    
    private final StaffReportGenerator staffReportGenerator;

    private final ExcelReportGenerator excelReportGenerator;

    private final PdfReportGenerator pdfReportGenerator;
    
    public StaffReportEndpoint(StaffReportGenerator staffReportGenerator, ExcelReportGenerator excelReportGenerator,
            PdfReportGenerator pdfReportGenerator) {
        this.staffReportGenerator = staffReportGenerator;
        this.excelReportGenerator = excelReportGenerator;
        this.pdfReportGenerator = pdfReportGenerator;
    }

    @GET
	@Path("headers/{report_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getReportHeader(@PathParam("report_type") StaffReportType staffReportType) {
		if (staffReportType == null) {
			return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
		}
		return Response.status(Response.Status.OK.getStatusCode())
				.entity(staffReportGenerator.getReportHeader(staffReportType)).build();
	}

	@GET
	@Path("headers")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getAllReportHeader() {
		return Response.status(Response.Status.OK.getStatusCode()).entity(staffReportGenerator.getReportHeader())
				.build();
	}

	@POST
	@Path("{institute_id}/view-report/{report_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStaffReportData(@PathParam("institute_id") int instituteId, @PathParam("report_type") StaffReportType reportType,
									   @QueryParam("user_id") UUID userId,
									   @QueryParam("requiredHeaders") String requiredHeaders, StaffFilterationCriteria staffFilterationCriteria) {

		final ReportDetails reportDetails = staffReportGenerator.generateReport(instituteId, userId, reportType, null, requiredHeaders, staffFilterationCriteria);

		if (reportDetails == null) {
			logger.error("Unable to render report");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to render report"));
		}

		HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);

		return Response.status(Response.Status.OK.getStatusCode()).entity(htmlReportTable).build();
	}

	@POST
	@Path("{institute_id}/generate-report/{report_type}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
    public Response generateReport(@PathParam("institute_id") int instituteId,@PathParam("report_type") StaffReportType reportType,@QueryParam("user_id") UUID userId,@QueryParam("download_format") DownloadFormat downloadFormat,@QueryParam("requiredHeaders") String requiredHeaders,StaffFilterationCriteria staffFilterationCriteria ) throws IOException {

        final ReportDetails reportDetails = staffReportGenerator.generateReport(instituteId,userId,
                reportType,downloadFormat,requiredHeaders,staffFilterationCriteria);

        if (downloadFormat == DownloadFormat.PDF) {
            final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.STAFF_MANAGEMENT);
            if (documentOutput == null) {
                return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
            }
            return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
                    .header("Content-Disposition", "filename=" + documentOutput.getName())
                    .entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
        } else {
            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
            if (reportOutput == null) {
                return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
            }
            return Response.status(Response.Status.OK.getStatusCode())
                        .header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
                        .entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
        }
    }
}
