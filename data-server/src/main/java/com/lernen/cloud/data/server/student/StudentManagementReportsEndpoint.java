package com.lernen.cloud.data.server.student;


import com.embrate.cloud.pdf.reports.PdfReportGenerator;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.report.HTMLReportTable;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.student.StudentManagementReportsType;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.reports.ExcelReportGenerator;
import com.lernen.cloud.core.lib.student.StudentManagementReportsGenerator;
import com.lernen.cloud.core.utils.report.ReportUtils;
import com.lernen.cloud.data.server.staff.StaffReportEndpoint;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.UUID;

@Path("/2.0/student-management/reports")
public class StudentManagementReportsEndpoint {
    private static final Logger logger = LogManager.getLogger(StudentManagementReportsEndpoint.class);

    private final StudentManagementReportsGenerator studentManagementReportsGenerator;
    private final PdfReportGenerator pdfReportGenerator;
    private final ExcelReportGenerator excelReportGenerator;

    public StudentManagementReportsEndpoint(StudentManagementReportsGenerator studentManagementReportsGenerator, PdfReportGenerator pdfReportGenerator,ExcelReportGenerator excelReportGenerator){
        this.studentManagementReportsGenerator = studentManagementReportsGenerator;
        this.pdfReportGenerator = pdfReportGenerator;
        this.excelReportGenerator = excelReportGenerator;
    }

    @GET
    @Path("view-report/{report_type}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getStudentReportData(@PathParam("report_type") StudentManagementReportsType studentManagementReportsType,
                                         @QueryParam("institute_id") int instituteId,
                                         @QueryParam("academic_session_id") int academicSessionId,
                                         @QueryParam("user_id") UUID userId) {

        final ReportDetails reportDetails = studentManagementReportsGenerator.generateStudentReport(instituteId,
                academicSessionId, userId, null, studentManagementReportsType);

        if (reportDetails == null) {
            logger.error("Unable to render report");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to render report"));
        }

        HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);

        return Response.status(Response.Status.OK.getStatusCode()).entity(htmlReportTable).build();
    }

    @GET
    @Path("generate-student-report/{report_type}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    public Response generateStudentReport(@PathParam("report_type") StudentManagementReportsType studentManagementReportsType,
                                          @QueryParam("institute_id") int instituteId,
                                          @QueryParam("academic_session_id") int academicSessionId,
                                          @QueryParam("user_id") UUID userId, @QueryParam("download_format") DownloadFormat downloadFormat) throws IOException {

        final ReportDetails reportDetails = studentManagementReportsGenerator.generateStudentReport(instituteId,
                    academicSessionId, userId,downloadFormat,studentManagementReportsType);

        if (downloadFormat == DownloadFormat.PDF) {
            final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.STUDENT_MANAGEMENT);
            if (documentOutput == null) {
                return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
            }
            return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
                    .header("Content-Disposition", "filename=" + documentOutput.getName())
                    .entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
        }

        else {
            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);

            if (reportOutput == null) {
                return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
            }
            return Response.status(Response.Status.OK.getStatusCode())
                    .header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
                    .entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
        }
    }
}
