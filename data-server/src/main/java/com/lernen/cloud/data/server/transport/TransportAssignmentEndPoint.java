package com.lernen.cloud.data.server.transport;

import java.util.List;
import java.util.UUID;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.transport.*;
import com.lernen.cloud.core.lib.transport.configuration.TransportAssignmentManager;

/**
 * 
 * <AUTHOR>
 *
 */
@Path("/2.0/transport/assignment")
public class TransportAssignmentEndPoint {

	private final TransportAssignmentManager transportAssignmentManager;

	public TransportAssignmentEndPoint(TransportAssignmentManager transportAssignmentManager) {
		this.transportAssignmentManager = transportAssignmentManager;
	}

	@GET
	@Path("/stats/session/{academic_session_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getTransportStats(@PathParam("academic_session_id") int academicSessionId,
			@QueryParam("institute_id") int instituteId) {

		List<TransportAreaStats> areaStats = transportAssignmentManager.getAreaStats(instituteId, academicSessionId);
		if (areaStats == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_FEE_CONFIGURATION,
					"Unable to fetch transport statistics"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(areaStats).build();
	}

	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addTransport(TransportAssignmentPayload transportAssignmentPayload, @QueryParam("user_id") UUID userId) {

		boolean addedTransport = transportAssignmentManager.addTransport(transportAssignmentPayload, userId);
		if (!addedTransport) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_FEE_ASSIGNMENT,
					"Unable to assign transport to student"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("student/{student_id}/current-transport/details")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentCurrentTransportAssignmentDetails(@PathParam("student_id") UUID studentId, @QueryParam("institute_id") int instituteId,
			@QueryParam("academic_session_id") int academicSessionId) {

		StudentTransportDetails studentTransportDetails = transportAssignmentManager
				.getStudentCurrentTransportDetails(instituteId, academicSessionId, studentId);
		if (studentTransportDetails == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to fetch current transport details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentTransportDetails).build();
	}

	@GET
	@Path("student/{student_id}/current-transport/metadata")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentCurrentTransportAssignmentData( @PathParam("student_id") UUID studentId, @QueryParam("institute_id") int instituteId,
																@QueryParam("academic_session_id") int academicSessionId) {
		StudentTransportData studentTransportData = transportAssignmentManager
				.getStudentCurrentTransportData(instituteId, academicSessionId, studentId);
		if (studentTransportData == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to fetch current transport data"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentTransportData).build();
	}

	@GET
	@Path("student/{student_id}/history")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentTransportHistoryList(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {

		List<StudentTransportData> inactiveStudentTransportDataList = transportAssignmentManager
				.getStudentInactiveTransportData(instituteId, academicSessionId, studentId);
		if (inactiveStudentTransportDataList == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to fetch transport history"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(inactiveStudentTransportDataList).build();
	}

	@PUT
	@Path("/update-transport-status/{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateTransportArea(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
			@QueryParam("user_id") UUID userId) {
		boolean updatedArea = transportAssignmentManager.updateTransportStatus(instituteId, academicSessionId, userId, studentId);
		if (!updatedArea) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_AREA, "Unable to update transport status."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("bulk-student")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getBulkStudentTransportAssignmentList(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
						  @QueryParam("standard_section_ids") String standardSectionIdsCsv,
						  @QueryParam("service_route_ids") String serviceRouteIdsCsv,
						  @QueryParam("required_areas") String requiredAreasCsv,
						  @QueryParam("required_parent_areas") String requiredParentAreasCsv,
						  @QueryParam("show_only_students_with_transport_assign") boolean showOnlyStudentsWithTransportAssign) {

		List<BulkStudentTransportData> bulkStudentTransportDataList = transportAssignmentManager
				.getBulkStudentTransportData(instituteId, academicSessionId, standardSectionIdsCsv, serviceRouteIdsCsv, requiredAreasCsv, requiredParentAreasCsv, showOnlyStudentsWithTransportAssign);
		if (bulkStudentTransportDataList == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to fetch bulk transport data"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(bulkStudentTransportDataList).build();
	}

	@POST
	@Path("bulk-student")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateBulkStudentTransport(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId, @QueryParam("user_id") UUID userId, BulkTransportAssignmentPayload bulkTransportAssignmentPayload) {

		boolean addedTransport = transportAssignmentManager.updateBulkStudentTransport(instituteId, academicSessionId, bulkTransportAssignmentPayload, userId);
		if (!addedTransport) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_TRANSPORT_FEE_ASSIGNMENT,
					"Unable to assign transport to students"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
    @Path("{institute_id}/student-transport-info/{academic_session_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getStudentTransportInfoList(@PathParam("institute_id") int instituteId,
                                                    @PathParam("academic_session_id") int academicSessionId) {
        final List<StudentTransportInfo> transportAssignedStudentsList = transportAssignmentManager.getStudentTransportInfoList(instituteId, academicSessionId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(transportAssignedStudentsList).build();
    }

	@GET
    @Path("{institute_id}/stoppage-student-route-details/{academic_session_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getStoppageStudentRouteDetails(@PathParam("institute_id") int instituteId,
                                                    @PathParam("academic_session_id") int academicSessionId) {
        final List<StoppageStudentRouteDetails> transportAssignedStudentsList = transportAssignmentManager.getStoppageStudentRouteDetails(instituteId, academicSessionId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(transportAssignedStudentsList).build();
    }
}
