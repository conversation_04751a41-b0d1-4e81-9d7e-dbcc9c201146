package com.lernen.cloud.data.server.transport;

import com.embrate.cloud.pdf.reports.PdfReportGenerator;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.report.HTMLReportTable;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.transport.TransportReportType;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.reports.ExcelReportGenerator;
import com.lernen.cloud.core.lib.reports.transport.TransportReportsGenerator;
import com.lernen.cloud.core.utils.report.ReportUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;


import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
@Path("/2.0/transport/reports")
public class TransportReportsEndPoint {

	private final TransportReportsGenerator transportReportsGenerator;
	private final ExcelReportGenerator excelReportGenerator;
    private final PdfReportGenerator pdfReportGenerator;

	public TransportReportsEndPoint(TransportReportsGenerator transportReportsGenerator, PdfReportGenerator pdfReportGenerator, ExcelReportGenerator excelReportGenerator) {
		this.transportReportsGenerator = transportReportsGenerator;
		this.pdfReportGenerator = pdfReportGenerator;
		this.excelReportGenerator = excelReportGenerator;
	}

	@GET
	@Path("headers/{report_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getReportHeader(@PathParam("report_type") TransportReportType transportReportType) {
		if (transportReportType == null) {
			return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
		}
		return Response.status(Response.Status.OK.getStatusCode())
				.entity(transportReportsGenerator.getReportHeader(transportReportType)).build();
	}

	@GET
	@Path("headers")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getAllReportHeader() {
		return Response.status(Response.Status.OK.getStatusCode()).entity(transportReportsGenerator.getReportHeader())
				.build();
	}

	@GET
	@Path("{institute_id}/generate-report/{report_type}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response generateReport(@PathParam("institute_id") int instituteId,
			@PathParam("report_type") TransportReportType transportReportType,
			@QueryParam("academic_session_id") int academicSessionId, @QueryParam("start") Integer start,
			@QueryParam("end") Integer end, @QueryParam("requiredHeaders") String requiredHeaders,
			@QueryParam("requiredStandards") String requiredStandards,
			@QueryParam("fee_ids") String feeIds,@QueryParam("parent_area_ids") String parentAreaIdsStr,
			@QueryParam("only_enrolled_students") boolean onlyEnrolledStudents,@QueryParam("area_ids") String areaIdStr,
			@QueryParam("pickup_ids") String pickupIdStr,@QueryParam("drop_ids") String dropIdStr,@QueryParam("download_format") DownloadFormat downloadFormat, @QueryParam("user_id") UUID userId) throws IOException{
		if (transportReportType == null || academicSessionId <= 0) {
			return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
		}
		ReportDetails reportDetails = transportReportsGenerator.generateReport(instituteId, academicSessionId,
			transportReportType, start, end, requiredHeaders, requiredStandards, feeIds, onlyEnrolledStudents,parentAreaIdsStr,areaIdStr,pickupIdStr,dropIdStr, downloadFormat, userId);
			if (downloadFormat == DownloadFormat.PDF) {
				final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.TRANSPORT);
				if (documentOutput == null) {
					return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
				}
				return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
						.header("Content-Disposition", "filename=" + documentOutput.getName())
						.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
			} else {
				final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
				if (reportOutput == null) {
					return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
				}
				return Response.status(Response.Status.OK.getStatusCode())
						.header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
						.entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
			}
	}

	@GET
    @Path("{institute_id}/view-report/{report_type}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getReport(@PathParam("institute_id") int instituteId,
			@PathParam("report_type") TransportReportType transportReportType,
			@QueryParam("academic_session_id") int academicSessionId, @QueryParam("start") Integer start,
			@QueryParam("end") Integer end, @QueryParam("requiredHeaders") String requiredHeaders,
			@QueryParam("requiredStandards") String requiredStandards,@QueryParam("parent_area_ids") String parentAreaIdsStr,
			@QueryParam("fee_ids") String feeIds, @QueryParam("only_enrolled_students") boolean onlyEnrolledStudents,@QueryParam("area_ids") String areaIdStr,
			@QueryParam("pickup_ids") String pickupIdStr,@QueryParam("drop_ids") String dropIdStr,@QueryParam("download_format") DownloadFormat downloadFormat, @QueryParam("user_id") UUID userId) throws IOException {
		
        ReportDetails reportDetails = transportReportsGenerator.generateReport(instituteId, academicSessionId,
			transportReportType, start, end, requiredHeaders, requiredStandards, feeIds, onlyEnrolledStudents,parentAreaIdsStr,areaIdStr,pickupIdStr,dropIdStr, downloadFormat, userId);
        if(reportDetails == null){
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to render report"));
        }

        HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
        return Response.status(Response.Status.OK.getStatusCode()).entity(htmlReportTable).build();

    }
}
