package com.lernen.cloud.data.server.visitor;

import com.embrate.cloud.push.notifications.handler.VisitorDetailsAddPushNotificationHandler;
import com.embrate.cloud.push.notifications.handler.VisitorStatusUpdatePushNotificationHandler;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.student.DownloadDocumentWrapper;
import com.lernen.cloud.core.api.student.RegisterStudentPayload;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.api.visitor.VisitorDetails;
import com.lernen.cloud.core.api.visitor.VisitorDetailsPayload;
import com.lernen.cloud.core.api.visitor.VisitorDocumentType;
import com.lernen.cloud.core.api.visitor.VisitorStatus;
import com.lernen.cloud.core.lib.visitor.VisitorDetailsManager;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.data.server.RestFormDataHandler;
import com.lernen.cloud.pdf.visitor.identitycard.VisitorIdentityCardHandler;
import com.sun.jersey.multipart.FormDataBodyPart;
import com.sun.jersey.multipart.FormDataParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Path("/2.0/visitors-management")
public class VisitorDetailsEndPoint {
	private static final Logger logger = LogManager.getLogger(VisitorDetailsEndPoint.class);
	private static final Gson GSON = SharedConstants.GSON;

	private final VisitorDetailsManager visitorDetailsManager;
	private final VisitorDetailsAddPushNotificationHandler visitorDetailsAddPushNotificationHandler;
	private final VisitorStatusUpdatePushNotificationHandler visitorStatusUpdatePushNotificationHandler;
	private final VisitorIdentityCardHandler visitorIdentityCardHandler;

	public VisitorDetailsEndPoint(VisitorDetailsManager visitorDetailsManager, VisitorDetailsAddPushNotificationHandler visitorDetailsAddPushNotificationHandler, VisitorStatusUpdatePushNotificationHandler visitorStatusUpdatePushNotificationHandler, VisitorIdentityCardHandler visitorIdentityCardHandler) {
		this.visitorDetailsManager = visitorDetailsManager;
		this.visitorDetailsAddPushNotificationHandler = visitorDetailsAddPushNotificationHandler;
		this.visitorStatusUpdatePushNotificationHandler = visitorStatusUpdatePushNotificationHandler;
		this.visitorIdentityCardHandler = visitorIdentityCardHandler;
	}

	@POST
	@Path("visitor-detail")
	@Consumes(MediaType.MULTIPART_FORM_DATA)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addVisitorDetails(@FormDataParam("visitorDetailsPayload") String visitorDetailsPayloadJson,
									  @FormDataParam("visitorImage") List<FormDataBodyPart> visitorImage, @FormDataParam("visitorImageName") String visitorImageName,
									  @FormDataParam("visitorIdFile") List<FormDataBodyPart> bodyPartsVisitorIdFile,
									  @FormDataParam("visitorIdDocumentName") String visitorIdDocumentName,
									  @QueryParam("recaptcha_authentication") String recaptchaAuthenticationKey) {
		final List<FileData> files = RestFormDataHandler.getFileDataFromFormDataBodyPart(visitorImage);
		FileData image = null;

		if (!CollectionUtils.isEmpty(files)) {
			image = files.get(0);
		}

		final List<FileData> visitorIdFiles = RestFormDataHandler.getFileDataFromFormDataBodyPart(bodyPartsVisitorIdFile);
		FileData visitorDocument = null;

		if (!CollectionUtils.isEmpty(visitorIdFiles)) {
			visitorDocument = visitorIdFiles.get(0);
		}

		final VisitorDetailsPayload visitorDetailsPayload = GSON.fromJson(visitorDetailsPayloadJson, VisitorDetailsPayload.class);
		visitorImageName = getValidDocumentName(visitorImageName, VisitorDocumentType.VISITOR_PROFILE_IMAGE);
		final UUID visitorId = visitorDetailsManager.addVisitorDetails(visitorDetailsPayload.getInstituteId(), visitorDetailsPayload.getAcademicSessionId(), visitorDetailsPayload, visitorImageName, image, visitorIdDocumentName, visitorDocument, recaptchaAuthenticationKey);
		if (visitorId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_VISITOR_DETAILS, "Unable to add visitor details."));
		}
//		NEED TO ADD NOTIFICATION IN PENDING STATE TO RECEPTIONIST WHEN FORM IS FILLED BY VISITOR
//		final VisitorDetails visitorDetails = visitorDetailsManager.getVisitorDetailsByVisitorId(visitorDetailsPayload.getInstituteId(), visitorDetailsPayload.getAcademicSessionId(), visitorId);
//		if(!visitorDetails.getStatus().equals(VisitorStatus.PENDING)) {
//			visitorDetailsAddPushNotificationHandler.sendVisitorDetailsAddNotificationsAsync(visitorDetailsPayload.getInstituteId(), visitorDetails, visitorDetailsPayload.getAcademicSessionId());
//		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("visitor-detail/{visitor_id}")
	@Consumes(MediaType.MULTIPART_FORM_DATA)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateVisitorDetails(@FormDataParam("visitorDetailsPayload") String visitorDetailsPayloadJson,
										 @FormDataParam("visitorImage") List<FormDataBodyPart> bodyParts, @FormDataParam("visitorImageName") String documentName,
										 @FormDataParam("visitorIdFile") List<FormDataBodyPart> bodyPartsVisitorIdFile,
										 @FormDataParam("visitorIdDocumentName") String visitorIdDocumentName, @PathParam("visitor_id") UUID visitorId,
										 @QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId) {
		final List<FileData> files = RestFormDataHandler.getFileDataFromFormDataBodyPart(bodyParts);
		FileData visitorImage = null;

		if (!CollectionUtils.isEmpty(files)) {
			visitorImage = files.get(0);
		}

		final List<FileData> visitorIdFiles = RestFormDataHandler.getFileDataFromFormDataBodyPart(bodyPartsVisitorIdFile);
		FileData visitorDocument = null;

		if (!CollectionUtils.isEmpty(visitorIdFiles)) {
			visitorDocument = visitorIdFiles.get(0);
		}
		final VisitorDetailsPayload visitorDetailsPayload = GSON.fromJson(visitorDetailsPayloadJson,
				VisitorDetailsPayload.class);

		final boolean updateVisitorDetails = visitorDetailsManager.updateVisitorDetails(instituteId, userId, visitorDetailsPayload, documentName, visitorImage, visitorIdDocumentName, visitorDocument);
		if (!updateVisitorDetails) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_VISITOR_DETAILS, "Unable to update visitor details."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("visitor-details")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getVisitorDetails(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
									  @QueryParam("academic_session_id") int academicSessionId, @QueryParam("visitor_status_str") Set<VisitorStatus> visitorStatusSet, @QueryParam("limit") Integer limit, @QueryParam("offset") Integer offset) {
		final List<VisitorDetails> visitorDetailsList = visitorDetailsManager.getVisitorDetails(instituteId, userId, academicSessionId, visitorStatusSet, limit, offset);
		return Response.status(Response.Status.OK.getStatusCode()).entity(visitorDetailsList).build();
	}

	@GET
	@Path("visitor-details-with-pagination")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getVisitorDetailsWithPagination(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, @QueryParam("academic_session_id") int academicSessionId,
													@QueryParam("visitor_status_str") Set<VisitorStatus> visitorStatusSet, @QueryParam("limit") Integer limit, @QueryParam("offset") Integer offset) {
		final SearchResultWithPagination<VisitorDetails> visitorDetailsList = visitorDetailsManager.getVisitorDetailsWithPagination(instituteId, userId, academicSessionId, visitorStatusSet, limit, offset);
		return Response.status(Response.Status.OK.getStatusCode()).entity(visitorDetailsList).build();
	}

	@GET
	@Path("visitor-details/{visitor_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getVisitorDetails(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
									  @QueryParam("visitor_id") UUID visitorId) {
		final VisitorDetails visitorDetails = visitorDetailsManager.getVisitorDetailsByVisitorId(instituteId, academicSessionId, visitorId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(visitorDetails).build();
	}

	@PUT
	@Path("transfer-staff")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateVisitorAssignedStaff(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, @QueryParam("academic_session_id") int academicSessionId,
											   @QueryParam("visitor_id") UUID visitorId, @QueryParam("staff_id") UUID staffId) {
		if (!visitorDetailsManager.updateVisitorAssignedStaff(instituteId, userId, academicSessionId, visitorId, staffId)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_VISITOR_DETAILS, "Unable to transfer staff."));
		}
		final VisitorDetails visitorDetails = visitorDetailsManager.getVisitorDetailsByVisitorId(instituteId, academicSessionId, visitorId);
//		No need to send the Notification when status is PENDING and REJECTED
		if(!visitorDetails.getStatus().equals(VisitorStatus.PENDING) && !visitorDetails.getStatus().equals(VisitorStatus.REJECTED)) {
			visitorDetailsAddPushNotificationHandler.sendVisitorDetailsAddNotificationsAsync(instituteId, visitorDetails, academicSessionId);
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@PUT
	@Path("visitor-status/{visitor_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateVisitorStatus(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, @QueryParam("academic_session_id") int academicSessionId,
										@PathParam("visitor_id") UUID visitorId, @QueryParam("visitor_status") VisitorStatus visitorStatus, @QueryParam("reason") String reason, @QueryParam("comment") String comment) {
		final boolean updateVisitorStatus = visitorDetailsManager.updateVisitorStatus(instituteId, userId, academicSessionId, visitorId,
				visitorStatus, reason, comment);
		if (!updateVisitorStatus) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_VISITOR_DETAILS, "Unable to update visitor status."));
		}

		visitorStatusUpdatePushNotificationHandler.sendVisitorStatusUpdateNotificationsAsync(instituteId, visitorId, academicSessionId, userId);
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	public String getValidDocumentName(String documentName, VisitorDocumentType visitorDocumentType) {
		return StringUtils.isEmpty(documentName) ? visitorDocumentType.getDisplayName().replace(" ", "_").toLowerCase() :
				documentName;
	}

	@POST
	@Path("/upload-document/{visitor_id}")
	@Consumes(MediaType.MULTIPART_FORM_DATA)
	@Produces(MediaType.APPLICATION_JSON)
	public Response uploadVisitorDocument(@PathParam("visitor_id") UUID visitorId,
										  @FormDataParam("file") List<FormDataBodyPart> bodyParts,
										  @FormDataParam("documentType") VisitorDocumentType visitorDocumentType,
										  @FormDataParam("documentName") String documentName, @QueryParam("institute_id") int instituteId,
										  @QueryParam("user_id") UUID userId, @QueryParam("academic_session_id") int academicSessionId) {

		final List<FileData> files = RestFormDataHandler.getFileDataFromFormDataBodyPart(bodyParts);
		if (CollectionUtils.isEmpty(files)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "No file attached for upload."));
		}
		documentName = getValidDocumentName(documentName, visitorDocumentType);
		final List<Document<VisitorDocumentType>> updatedDocuments = visitorDetailsManager.uploadDocument(instituteId,
				visitorId, visitorDocumentType, academicSessionId, documentName, files.get(0));
		if (updatedDocuments == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Unable to upload file"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(updatedDocuments).build();
	}

	@GET
	@Path("/download-document/{visitor_id}/{document_id}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response downloadVisitorDocument(@PathParam("visitor_id") UUID visitorId,
											@PathParam("document_id") UUID documentId, @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
		final DownloadDocumentWrapper<Document<VisitorDocumentType>> documentWrapper = visitorDetailsManager
				.downloadDocument(instituteId, academicSessionId, visitorId, documentId);
		if (documentWrapper == null || documentWrapper.getDocumentContent() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Unable to download file"));
		}

		String documentName = getValidDocumentName(documentWrapper.getDocumentDetails().getDocumentName(),
				documentWrapper.getDocumentDetails().getDocumentType());
		return Response.status(Response.Status.OK.getStatusCode())
				.header("Content-Disposition",
						"attachment;filename=" + documentName + "."
								+ documentWrapper.getDocumentDetails().getFileExtension())
				.entity(new ByteArrayInputStream(documentWrapper.getDocumentContent().toByteArray())).build();
	}

	@POST
	@Path("/delete-document/{visitor_id}/{document_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteVisitorDocument(@PathParam("visitor_id") UUID visitorId,
										  @PathParam("document_id") UUID documentId, @QueryParam("institute_id") int instituteId,
										  @QueryParam("user_id") UUID userId, @QueryParam("academic_session_id") int academicSessionId) {
		final List<Document<VisitorDocumentType>> updatedDocuments = visitorDetailsManager.deleteDocument(instituteId,
				academicSessionId, visitorId, documentId);
		if (updatedDocuments == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Unable to delete document"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(updatedDocuments).build();
	}

	@GET
	@Path("{visitor_id}/visitor-card/pdf")
	@Produces("application/pdf")
	public Response generateVisitorCard(@PathParam("visitor_id") UUID visitorId, @QueryParam("institute_id") int instituteId,
										@QueryParam("academic_session_id") int academicSessionId, @QueryParam("user_id") UUID userId) {

		final DocumentOutput documentOutput = visitorIdentityCardHandler.generateIdentityCard(instituteId,
				academicSessionId, visitorId, userId);
		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate identity cards"));
		}

		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}
}
