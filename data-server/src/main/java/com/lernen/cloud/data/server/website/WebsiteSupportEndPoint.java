package com.lernen.cloud.data.server.website;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.lernen.cloud.core.api.website.WebsiteContactEnquiryForm;
import com.lernen.cloud.emailer.website.WebsiteContactEmailHandler;

/**
 *
 * <AUTHOR>
 *
 */
@Path("/2.0/website")
public class WebsiteSupportEndPoint {

	private static final Logger logger = LogManager.getLogger(WebsiteSupportEndPoint.class);

	private final WebsiteContactEmailHandler websiteContactEmailHandler;

	public WebsiteSupportEndPoint(WebsiteContactEmailHandler websiteContactEmailHandler) {
		this.websiteContactEmailHandler = websiteContactEmailHandler;
	}

	@POST
	@Path("submit-enquiry")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response submitEnquiry(WebsiteContactEnquiryForm websiteContactEnquiryForm, @QueryParam("recaptcha_authentication") String recaptchaAuthenticationKey) {
		final boolean success = websiteContactEmailHandler.sendWebsiteEnquiryFormEmail(websiteContactEnquiryForm, recaptchaAuthenticationKey);
		if (success) {
			return Response.ok().build();
		}
		return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
	}

}
