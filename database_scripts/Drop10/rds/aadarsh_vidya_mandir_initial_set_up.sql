use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2) 
VALUES (10030,'ADARSH VIDYA MANDIR', 'Vill & P.O - <PERSON><PERSON>', null, '<PERSON><PERSON>, <PERSON><PERSON><PERSON>', 'Uttar Pradesh', 'India', '201008', null, '', '<EMAIL>', '9350507045', 'Vill & P.O - Piyawali Tajpur, Dadri', '');


INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10030, 2019, 2020, 4, 3);

insert into fee_category (institute_id, fee_category, description) values (10030, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10030, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10030, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10030, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10030, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10030, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10030, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10030, (SELECT fee_category_id from fee_category where institute_id = 10030 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10030,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10030,'books',0);
insert into categories (institute_id,category_name) values (10030,'clothing');
insert into categories (institute_id,category_name) values (10030,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10030,'note book',0,0);
insert into categories (institute_id,category_name) values (10030,'art & craft');
insert into categories (institute_id,category_name) values (10030,'personal care');
insert into categories (institute_id,category_name) values (10030,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10030,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10030,'accessories');
insert into categories (institute_id,category_name) values (10030,'furniture');
insert into categories (institute_id,category_name) values (10030,'electronics');
insert into categories (institute_id,category_name) values (10030,'sports');



insert into colors (institute_id, color_name) values (10030,'maroon');
insert into colors (institute_id, color_name) values (10030,'black');
insert into colors (institute_id, color_name) values (10030,'brown');
insert into colors (institute_id, color_name) values (10030,'white');
insert into colors (institute_id, color_name) values (10030,'red');
insert into colors (institute_id, color_name) values (10030,'yellow');
insert into colors (institute_id, color_name) values (10030,'blue');
insert into colors (institute_id, color_name) values (10030,'navy blue');
insert into colors (institute_id, color_name) values (10030,'green');
insert into colors (institute_id, color_name) values (10030,'dark green');
insert into colors (institute_id, color_name) values (10030,'pink');
insert into colors (institute_id, color_name) values (10030,'purple');
insert into colors (institute_id, color_name) values (10030,'grey');
insert into colors (institute_id, color_name) values (10030,'olive');
insert into colors (institute_id, color_name) values (10030,'cyan');
insert into colors (institute_id, color_name) values (10030,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10030, 'FEE_INVOICE', 1, "");
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10030', 'meta_data', 'enable_permission', 'true');

insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10030', 'meta_data', 'audit_log_enabled', 'true');

insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10030, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10030, "Grade", "SYSTEM", "GRADE", 1);


-- Ensure all actions defined in com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction class are added to actions table 

insert into user_permissions select institute_id , role_id, 'ROLE' , module, action, 1 from actions join user_roles where role_name = 'ADMIN' and institute_id = 10030 order by institute_id, role_name, module, action ;
