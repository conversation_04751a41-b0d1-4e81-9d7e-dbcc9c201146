CREATE TABLE IF NOT EXISTS student_registration(
	institute_unique_code varchar(36) NOT NULL,
	registration_id varchar(36) PRIMARY KEY NOT NULL,
	institute_registration_id varchar(36) NOT NULL,
	registration_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	admission_academic_session int NOT NULL,
	registration_standard_id varchar(36) NOT NULL,
	status varchar(16),
	name varchar(225)NOT NULL,
	gender varchar(15),
	date_of_birth TIMESTAMP NULL DEFAULT NULL,
	birth_place varchar(225),
	category varchar(256) DEFAULT NULL,
  	religion varchar(225) DEFAULT NULL,
  	rte tinyint(1) DEFAULT '0',
  	aadhar_number varchar(225) DEFAULT NULL,
  	mother_tongue varchar(225) DEFAULT NULL,
  	area_type varchar(32) DEFAULT NULL,
  	specially_abled tinyint(1) DEFAULT NULL,
  	bpl tinyint(1) DEFAULT NULL,
	caste varchar(256),
	permanent_address text,
	city varchar(256),
	state varchar(256),
	zipcode varchar (15),
	primary_contact_number varchar(500) DEFAULT NULL,
    primary_email varchar(500) DEFAULT NULL,	
	father_name varchar(225),
	mother_name varchar(225),
	father_occupation varchar(225),
	mother_occupation varchar(225),
	father_contact_number varchar(225),
	mother_contact_number varchar(225),
	family_approx_income varchar(225),
	father_aadhar_number varchar(24) DEFAULT NULL,
  	mother_aadhar_number varchar(24) DEFAULT NULL,
	gardians_details text,
	previous_school_name varchar(225),
	class_passed varchar(225),
	previous_school_medium varchar(225),
	result varchar(225),
	percentage varchar(225),
	year_of_passing int,
	blood_group varchar(225),
	blood_pressure varchar(225),
	pulse varchar(225),
	height varchar(50),
	weight varchar(50),
	date_of_physical_examination TIMESTAMP NULL DEFAULT NULL,
	UNIQUE KEY (institute_unique_code, institute_registration_id),
	FOREIGN KEY (institute_unique_code) REFERENCES institute(institute_unique_code),
	FOREIGN KEY (registration_standard_id) REFERENCES standards(standard_id)
);


insert into counters (institute_id, counter_type, count, counter_prefix) values (702, 'ONLINE_REGISTRATION_NUMBER', 1, "");
