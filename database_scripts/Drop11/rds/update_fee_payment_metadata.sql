insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10045', 'meta_data', 'hide_current_date_on_fee_payment', 'true');

insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10001', 'meta_data', 'default_fee_payment_mode', 'CASH');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10005', 'meta_data', 'default_fee_payment_mode', 'CASH');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10010', 'meta_data', 'default_fee_payment_mode', 'CASH');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10020', 'meta_data', 'default_fee_payment_mode', 'CASH');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10025', 'meta_data', 'default_fee_payment_mode', 'CASH');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10030', 'meta_data', 'default_fee_payment_mode', 'CASH');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10031', 'meta_data', 'default_fee_payment_mode', 'CASH');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10040', 'meta_data', 'default_fee_payment_mode', 'CASH');
