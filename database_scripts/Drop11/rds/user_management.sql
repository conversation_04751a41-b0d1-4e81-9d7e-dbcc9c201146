CREATE TABLE IF NOT EXISTS user_role_mapping (
user_id  varchar(36) NOT NULL,
role_id varchar(36) NOT NULL,
status varchar(50) NOT NULL,
created_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
created_user_id varchar(36) NOT NULL,
updated_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
updated_user_id varchar(36),
PRIMARY KEY (user_id, role_id),
FOREIGN KEY (role_id) REFERENCES user_roles(role_id),
FOREIGN KEY (user_id) REFERENCES users(user_id)
);

ALTER TABLE `users` DROP FOREIGN KEY `fk_role_id`;
ALTER TABLE `users` DROP COLUMN `role_id`;
ALTER TABLE users MODIFY COLUMN last_name varchar(1024) NULL;
ALTER TABLE users MODIFY COLUMN gender varchar(15) NULL;
ALTER TABLE users MODIFY COLUMN birth_date timestamp NULL;
ALTER TABLE users MODIFY COLUMN enrollment_date timestamp NULL;
ALTER TABLE users DROP COLUMN register_roll_number;
ALTER TABLE users DROP COLUMN hostel;
