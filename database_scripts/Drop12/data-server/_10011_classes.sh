
INSTITUTE_STANDARDS_URL=$1/2.0/institute/standard

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10011",
  "standardName": "Nursery",
  "stream" : "NA",
  "level" : 1,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10011",
  "standardName": "L.K.G",
  "stream" : "NA",
  "level" : 2,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10011",
  "standardName": "U.K.G",
  "stream" : "NA",
  "level" : 3,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10011",
  "standardName": "1st",
  "stream" : "NA",
  "level" : 4,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10011",
  "standardName": "2nd",
  "stream" : "NA",
  "level" : 5,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10011",
  "standardName": "3rd",
  "stream" : "NA",
  "level" : 6,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10011",
  "standardName": "4th",
  "stream" : "NA",
  "level" : 7,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10011",
  "standardName": "5th",
  "stream" : "NA",
  "level" : 8,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10011",
  "standardName": "6th",
  "stream" : "NA",
  "level" : 9,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10011",
  "standardName": "7th",
  "stream" : "NA",
  "level" : 10,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10011",
  "standardName": "8th",
  "stream" : "NA",
  "level" : 11,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10011",
  "standardName": "9th",
  "stream" : "NA",
  "level" : 12,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL
