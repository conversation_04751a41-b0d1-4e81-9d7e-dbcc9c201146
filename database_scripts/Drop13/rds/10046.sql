use lernen

INSERT INTO organisation (organisation_id, name)
VALUES ("894628d3-e98b-4489-b87b-cf958c5c9712", "SREE JAIN SWETAMBER TERAPANTHI VIDYALAYA");

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code, organisation_id) 
VALUES (10046,'SREE JAIN SWETAMBER TERAPANTHI VIDYALAYA', '3, PORTUGUESE CHURCH STREET', null, 'KOLKATA', 'WEST BENGAL', 'India', '700001', 'BESIDE PORTUGUESE CHURCH / VERY NEAR OF BRABOURNE ROAD', '', '<EMAIL>', '033 - 2235 - 9336 (OFFICE) / 8100080878 (H.O.I)', '3, PORTUGUESE CHURCH STREET KOLKATA - 700001', '(AN ENGLISH MEDIUM SCHOOL RECOGNISED)', "69f52145-0d99-4c56-ad0a-c116ae01e5a6", "894628d3-e98b-4489-b87b-cf958c5c9712");

update institute set logo_url = "/static/core/images/10045_logo.png" where institute_id = 10046;

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10046, 2019, 2020, 7, 6);

insert into fee_category (institute_id, fee_category, description) values (10046, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10046, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10046, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10046, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10046, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10046, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10046, 'Other Fee' ,'Any Other Fees');

INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10046, (SELECT fee_category_id from fee_category where institute_id = 10046 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');

insert into categories (institute_id,category_name,genders) values (10046,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10046,'books',0);
insert into categories (institute_id,category_name) values (10046,'clothing');
insert into categories (institute_id,category_name) values (10046,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10046,'note book',0,0);
insert into categories (institute_id,category_name) values (10046,'art & craft');
insert into categories (institute_id,category_name) values (10046,'personal care');
insert into categories (institute_id,category_name) values (10046,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10046,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10046,'accessories');
insert into categories (institute_id,category_name) values (10046,'furniture');
insert into categories (institute_id,category_name) values (10046,'electronics');
insert into categories (institute_id,category_name) values (10046,'sports');


insert into colors (institute_id, color_name) values (10046,'maroon');
insert into colors (institute_id, color_name) values (10046,'black');
insert into colors (institute_id, color_name) values (10046,'brown');
insert into colors (institute_id, color_name) values (10046,'white');
insert into colors (institute_id, color_name) values (10046,'red');
insert into colors (institute_id, color_name) values (10046,'yellow');
insert into colors (institute_id, color_name) values (10046,'blue');
insert into colors (institute_id, color_name) values (10046,'navy blue');
insert into colors (institute_id, color_name) values (10046,'green');
insert into colors (institute_id, color_name) values (10046,'dark green');
insert into colors (institute_id, color_name) values (10046,'pink');
insert into colors (institute_id, color_name) values (10046,'purple');
insert into colors (institute_id, color_name) values (10046,'grey');
insert into colors (institute_id, color_name) values (10046,'olive');
insert into colors (institute_id, color_name) values (10046,'cyan');
insert into colors (institute_id, color_name) values (10046,'magenta');


insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10046', 'meta_data', 'registration_counter', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10046', 'meta_data', 'admission_counter', 'true');

insert into counters (institute_id, counter_type, count, counter_prefix) values (10046, 'FEE_INVOICE', 1, "HS-");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10046, 'REGISTRATION_NUMBER', 1, "HS-");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10046, 'ADMISSION_NUMBER', 1, "HS-");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10046, 'ONLINE_REGISTRATION_NUMBER', 1, "HS-");

-- insert into default_fee_assignment_structure_meta_data values(10050, "Monthly Fees", "fe712ded-1963-4b93-8d73-601f56163efc", "ENROLLMENT");
-- insert into default_fee_assignment_structure select "fe712ded-1963-4b93-8d73-601f56163efc", (select standard_id from standards where institute_id=10050 and standard_name = '2nd'), "CLASS", fee_id , fee_head_id, amount from fee_configuration where institute_id = 10050 and fee_type = "REGULAR";



insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10046, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10046, "Grade", "SYSTEM", "GRADE", 1);


