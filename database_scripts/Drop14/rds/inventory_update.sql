ALTER TABLE `lernen`.`product_transaction_metadata` ADD COLUMN `inventory_user_type` VARCHAR(64) NOT NULL AFTER `transaction_id`;
ALTER TABLE `lernen`.`product_transaction_metadata` ADD COLUMN `wallet_debit_amount` double NULL AFTER `transaction_status`;
ALTER TABLE `lernen`.`product_transaction_metadata` ADD COLUMN `wallet_based_credit_amount` double NULL AFTER `wallet_debit_amount`;
ALTER TABLE `lernen`.`product_transaction_metadata` ADD COLUMN `paid_amount` double NULL AFTER `wallet_based_credit_amount`;