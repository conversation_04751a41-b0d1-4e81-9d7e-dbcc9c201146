INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month) values(10030, 2020, 2021, 4, 3);

--select distinct CONSTRAINT_NAME from information_schema.TABLE_CONSTRAINTS where table_name = 'standard_section_mapping' and constraint_type = 'UNIQUE';
    
ALTER TABLE standard_section_mapping DROP INDEX standard_id, ADD UNIQUE KEY (standard_id, academic_session_id, section_name);



insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('e50da0af-e238-4e4e-bde0-15dc3b5143c8', 17, 'A');
insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('e50da0af-e238-4e4e-bde0-15dc3b5143c8', 17, 'B');
insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('50100091-2869-401b-ab46-2bb648b74b18', 17, 'A');
insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('50100091-2869-401b-ab46-2bb648b74b18', 17, 'B');
insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('14685dbe-4ee3-4c5b-9ee2-34becf24130f', 17, 'A');
insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('14685dbe-4ee3-4c5b-9ee2-34becf24130f', 17, 'B');
insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('167886df-2691-4b9c-849c-f751d9261115', 17, 'A');
insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('167886df-2691-4b9c-849c-f751d9261115', 17, 'B');
insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('3a684a07-f6b8-4750-9ef9-f2ec3e345939', 17, 'A');
insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('3a684a07-f6b8-4750-9ef9-f2ec3e345939', 17, 'B');
insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('21302af1-be37-49c9-bd9c-a0dfd55ee131', 17, 'A');
insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('21302af1-be37-49c9-bd9c-a0dfd55ee131', 17, 'B');
insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('05c5a900-5c54-4bea-b862-56a9a58f4d69', 17, 'A');
insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('05c5a900-5c54-4bea-b862-56a9a58f4d69', 17, 'B');