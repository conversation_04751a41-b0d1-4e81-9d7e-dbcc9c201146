ALTER TABLE `lernen`.`standard_section_mapping` ADD COLUMN `academic_session_id` int NOT NULL AFTER `standard_id`;

ALTER TABLE `standard_section_mapping` DROP FOREIGN KEY `standard_section_mapping_ibfk_1`;  
ALTER TABLE `standard_section_mapping` DROP INDEX `standard_id`;

ALTER TABLE `standard_section_mapping` ADD UNIQUE INDEX (`standard_id`, `academic_session_id`, `section_name`);

ALTER TABLE `standard_section_mapping`  ADD CONSTRAINT `standard_section_mapping_ibfk_1` FOREIGN KEY (`standard_id`) REFERENCES `standards` (`standard_id`);

-- below will be applied once all the sessions are updated properly
ALTER TABLE `standard_section_mapping`  ADD CONSTRAINT `standard_section_mapping_ibfk_2` FOREIGN KEY (`academic_session_id`) REFERENCES `academic_session` (`academic_session_id`);

