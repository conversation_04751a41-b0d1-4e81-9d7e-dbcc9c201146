use lernen

--INSERT INTO organisation (organisation_id, name)
--VALUES ("048a25e4-62e6-11ea-bc55-0242ac130003", "A.G. MISSION SCHOOL");

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code) 
VALUES (10065,'BRIGHT FUTURE SR. SEC. SCHOOL', 'New Hanuman Club, Ward No 22, Adsas Bass', 'Sri Dungargarh', 'Bikaner', 'Rajasthan', 'India', '331803', 'Hanuman Club', '/static/core/images/bright_future_logo.jpg', '<EMAIL>', '9413675541,8104515546,9602312650', '', '', "95515157-9d42-4d28-ae4b-c931e00fd412");

--update institute set branch_name = "A.G. MISSION SCHOOL", institute_name = "A.G. MISSION SCHOOL", logo_url="" where institute_id = 10065;

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10065, 2020, 2021, 7, 6);

insert into fee_category (institute_id, fee_category, description) values (10065, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10065, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10065, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10065, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10065, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10065, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10065, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10065, (SELECT fee_category_id from fee_category where institute_id = 10065 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10065,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10065,'books',0);
insert into categories (institute_id,category_name) values (10065,'clothing');
insert into categories (institute_id,category_name) values (10065,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10065,'note book',0,0);
insert into categories (institute_id,category_name) values (10065,'art & craft');
insert into categories (institute_id,category_name) values (10065,'personal care');
insert into categories (institute_id,category_name) values (10065,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10065,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10065,'accessories');
insert into categories (institute_id,category_name) values (10065,'furniture');
insert into categories (institute_id,category_name) values (10065,'electronics');
insert into categories (institute_id,category_name) values (10065,'sports');


insert into colors (institute_id, color_name) values (10065,'maroon');
insert into colors (institute_id, color_name) values (10065,'black');
insert into colors (institute_id, color_name) values (10065,'brown');
insert into colors (institute_id, color_name) values (10065,'white');
insert into colors (institute_id, color_name) values (10065,'red');
insert into colors (institute_id, color_name) values (10065,'yellow');
insert into colors (institute_id, color_name) values (10065,'blue');
insert into colors (institute_id, color_name) values (10065,'navy blue');
insert into colors (institute_id, color_name) values (10065,'green');
insert into colors (institute_id, color_name) values (10065,'dark green');
insert into colors (institute_id, color_name) values (10065,'pink');
insert into colors (institute_id, color_name) values (10065,'purple');
insert into colors (institute_id, color_name) values (10065,'grey');
insert into colors (institute_id, color_name) values (10065,'olive');
insert into colors (institute_id, color_name) values (10065,'cyan');
insert into colors (institute_id, color_name) values (10065,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10065, 'FEE_INVOICE', 1, "");
-- insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10065', 'meta_data', 'enable_permission', 'true');

--insert into default_fee_assignment_structure_meta_data values(10065, "Installments", "1aed4d2d-2d8e-4741-8b4c-6454126c1701", "ENROLLMENT");
insert into default_fee_assignment_structure select "1aed4d2d-2d8e-4741-8b4c-6454126c1701", (select standard_id from standards where institute_id=10065 and standard_name = '12th' and stream = 'COMMERCE'), "CLASS", fee_id , 80, 6900 from fee_configuration where institute_id = 10065 and fee_type = "REGULAR" and academic_session_id = 26;



insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10065, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10065, "Grade", "SYSTEM", "GRADE", 1);





New session setup 
INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month) values(10065, 2019, 2020, 7, 6);
insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('7da61a3c-bdb2-411b-a8f8-b48309e96fb2', 27, 'A');
insert into standard_section_mapping (standard_id, academic_session_id, section_name) values('7da61a3c-bdb2-411b-a8f8-b48309e96fb2', 27, 'B');



insert into configuration values ("INSTITUTE", "10065", "meta_data", "fees_sms_service_enabled", "true");
insert into configuration values ("INSTITUTE", "10065", "meta_data", "institute_name_in_sms", "BRIGHT FUTURE SCHOOL");
insert into configuration values ("INSTITUTE", "10065", "sms_preferences", "buffer_sms_count", "0");
insert into counters values (10065, "SMS_COUNTER", 100, "");
