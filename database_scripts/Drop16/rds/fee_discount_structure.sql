ALTER TABLE default_fee_assignment_structure_meta_data ADD academic_session_id int NOT NULL AFTER institute_id;
ALTER TABLE default_fee_assignment_structure_meta_data DROP INDEX institute_id;
ALTER TABLE default_fee_assignment_structure_meta_data ADD UNIQUE KEY (institute_id, academic_session_id, structure_name);

ALTER TABLE default_discount_assignment_structure_meta_data ADD academic_session_id int NOT NULL AFTER institute_id;
ALTER TABLE default_discount_assignment_structure_meta_data DROP INDEX institute_id;
ALTER TABLE default_discount_assignment_structure_meta_data ADD UNIQUE KEY (institute_id, academic_session_id, structure_name);