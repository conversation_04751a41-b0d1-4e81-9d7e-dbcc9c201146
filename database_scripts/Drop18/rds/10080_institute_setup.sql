use lernen

--INSERT INTO organisation (organisation_id, name)
--VALUES ("048a25e4-62e6-11ea-bc55-0242ac130003", "A.G. MISSION SCHOOL");

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code) 
VALUES (10080,'SANASKAR INNOVATIVE PUBLIC SCHOOL', 'Kalubass ward no 3 near Terapanth Bhawan', 'Sri Dungargarh', 'Bikaner', 'Rajasthan', 'India', '', '', '', '', '', '', '', "76030cba-088d-4260-817b-0468a1a76ce6");

--update institute set branch_name = "A.G. MISSION SCHOOL", institute_name = "A.G. MISSION SCHOOL", logo_url="" where institute_id = 10080;

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10080, 2020, 2021, 6, 5);

insert into fee_category (institute_id, fee_category, description) values (10080, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10080, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10080, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10080, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10080, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10080, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10080, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10080, (SELECT fee_category_id from fee_category where institute_id = 10080 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10080,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10080,'books',0);
insert into categories (institute_id,category_name) values (10080,'clothing');
insert into categories (institute_id,category_name) values (10080,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10080,'note book',0,0);
insert into categories (institute_id,category_name) values (10080,'art & craft');
insert into categories (institute_id,category_name) values (10080,'personal care');
insert into categories (institute_id,category_name) values (10080,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10080,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10080,'accessories');
insert into categories (institute_id,category_name) values (10080,'furniture');
insert into categories (institute_id,category_name) values (10080,'electronics');
insert into categories (institute_id,category_name) values (10080,'sports');


insert into colors (institute_id, color_name) values (10080,'maroon');
insert into colors (institute_id, color_name) values (10080,'black');
insert into colors (institute_id, color_name) values (10080,'brown');
insert into colors (institute_id, color_name) values (10080,'white');
insert into colors (institute_id, color_name) values (10080,'red');
insert into colors (institute_id, color_name) values (10080,'yellow');
insert into colors (institute_id, color_name) values (10080,'blue');
insert into colors (institute_id, color_name) values (10080,'navy blue');
insert into colors (institute_id, color_name) values (10080,'green');
insert into colors (institute_id, color_name) values (10080,'dark green');
insert into colors (institute_id, color_name) values (10080,'pink');
insert into colors (institute_id, color_name) values (10080,'purple');
insert into colors (institute_id, color_name) values (10080,'grey');
insert into colors (institute_id, color_name) values (10080,'olive');
insert into colors (institute_id, color_name) values (10080,'cyan');
insert into colors (institute_id, color_name) values (10080,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10080, 'FEE_INVOICE', 1, "");
-- insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10080', 'meta_data', 'enable_permission', 'true');

--insert into default_fee_assignment_structure_meta_data values(10080, "Installments", "1aed4d2d-2d8e-4741-8b4c-6454126c1701", "ENROLLMENT");
--insert into default_fee_assignment_structure select "1aed4d2d-2d8e-4741-8b4c-6454126c1701", (select standard_id from standards where institute_id=10080 and standard_name = '12th' and stream = 'COMMERCE'), "CLASS", fee_id , 80, 6900 from fee_configuration where institute_id = 10080 and fee_type = "REGULAR" and academic_session_id = 26;



insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10080, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10080, "Grade", "SYSTEM", "GRADE", 1);