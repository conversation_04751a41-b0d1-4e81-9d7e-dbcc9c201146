CREATE TABLE IF NOT EXISTS homework_details(
	institute_id int NOT NULL,
	homework_id varchar(36) NOT NULL,
	standard_id varchar(36) NOT NULL,
	course_id varchar(36) NOT NULL,
	chapter varchar(256),	
	title varchar(256) NOT NULL,
	created_user_id varchar(36) NOT NULL,
	created_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_user_id varchar(36),
	updated_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	description text,
	due_date TIMESTAMP NULL DEFAULT NULL,
	status varchar(100) NOT NULL,
	broadcasted_timestamp TIMESTAMP NULL,
	broadcasted_user_id varchar(36),
	faculty_user_id varchar(36),
	faculty_name varchar(256),
	allow_edit_after_submit tinyint,
	is_grade tinyint,
	max_marks int(6),
	scheduled_timestamp TIMESTAMP NULL,
	attachments text,
	primary key(homework_id),
	unique key (institute_id, standard_id, course_id, chapter,  title),
	FOREIGN KEY (standard_id) REFERENCES standards(standard_id),
	FOREIGN KEY (course_id) REFERENCES class_courses(course_id),
	FOREIGN KEY (faculty_user_id) REFERENCES staff_details(staff_id),
	FOREIGN KEY (created_user_id) REFERENCES users(user_id),
	FOREIGN KEY (updated_user_id) REFERENCES users(user_id),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);

CREATE TABLE IF NOT EXISTS homework_submission_details(
	homework_submission_id varchar(36) NOT NULL,
	homework_id varchar(36) NOT NULL,
	student_id varchar(36) NOT NULL,
	description text,
	attachments text,
	submission_status varchar(100) NOT NULL,
	submission_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	teachers_status varchar(100),
	teacher_status_user_id varchar(36),
	teachers_status_timestamp TIMESTAMP NULL,
	updated_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	result varchar(50),
	remarks text,
	primary key(homework_submission_id),
	unique key (homework_id, student_id),
	FOREIGN KEY (homework_id) REFERENCES homework_details(homework_id),
	FOREIGN KEY (student_id) REFERENCES students(student_id),
	FOREIGN KEY (teacher_status_user_id) REFERENCES users(user_id)
);