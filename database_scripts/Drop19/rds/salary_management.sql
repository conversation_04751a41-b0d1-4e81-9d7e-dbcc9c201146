CREATE TABLE IF NOT EXISTS pay_head_configuration(
	institute_id int NOT NULL,
	pay_head_id int PRIMARY KEY AUTO_INCREMENT,
	pay_head_name varchar(1024) NOT NULL,
	pay_head_type varchar(256) NOT NULL,
	description text,
	FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
	UNIQUE KEY(institute_id, pay_head_name)
);

CREATE TABLE IF NOT EXISTS staff_salary_structure_meta_data(
	institute_id int NOT NULL,
    structure_id varchar(36) NOT NULL,
    structure_name varchar(1024) NOT NULL,
    staff_id varchar(36) NOT NULL,
    status varchar(50) NOT NULL,
	primary key(structure_id),
	unique key(institute_id, staff_id, structure_name),
	FOREIGN KEY (staff_id) REFERENCES staff_details(staff_id),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);

CREATE TABLE IF NOT EXISTS staff_salary_structure_payhead_details(
	structure_id varchar(36) NOT NULL,
	salary_cycle_start int NOT NULL,
	pay_head_id int NOT NULL,
    amount double NOT NULL,
    primary key(pay_head_id, salary_cycle_start, structure_id),
    unique key(structure_id, salary_cycle_start, pay_head_id),
    FOREIGN KEY (structure_id) REFERENCES staff_salary_structure_meta_data(structure_id),
    FOREIGN KEY (pay_head_id) REFERENCES pay_head_configuration(pay_head_id)
);

CREATE TABLE IF NOT EXISTS salary_payslip_meta_data(
	institute_id int NOT NULL,
	payslip_id varchar(36) NOT NULL,
	staff_id varchar(256) NOT NULL,
	salary_cycle_start int NOT NULL,
	status varchar(50) NOT NULL,
	advance double,
	advance_transaction_id varchar(36),
	description text,
	primary key(payslip_id),
	FOREIGN KEY (staff_id) REFERENCES staff_details(staff_id),
	FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);

CREATE TABLE IF NOT EXISTS salary_payhead_details(
	payslip_id varchar(36) NOT NULL,
	pay_head_id int NOT NULL,
	amount double NOT NULL,
	primary key(payslip_id, pay_head_id),
	FOREIGN KEY (payslip_id) REFERENCES salary_payslip_meta_data(payslip_id),
	FOREIGN KEY (pay_head_id) REFERENCES pay_head_configuration(pay_head_id)
);

ALTER TABLE user_wallet_transaction_history ADD transaction_mode varchar(100) AFTER transaction_category;
ALTER TABLE user_wallet_transaction_history ADD transaction_date timestamp AFTER transaction_mode;
ALTER TABLE user_wallet_transaction_history ADD transaction_status varchar(50) AFTER transaction_date;

RENAME TABLE student_wallet TO user_wallet;
ALTER TABLE user_wallet Change student_id user_id varchar(36);
ALTER TABLE user_wallet ADD user_type varchar(256) AFTER user_id;
ALTER TABLE user_wallet DROP FOREIGN KEY user_wallet_ibfk_1;

update user_wallet set user_type = 'STUDENT';

CREATE TABLE IF NOT EXISTS staff_advance_transaction_history(
institute_id int NOT NULL,
transaction_id varchar(36) NOT NULL,
staff_id varchar(36) NOT NULL,
reason text,
transaction_category varchar(128) NOT NULL,
transaction_mode varchar(100),
transaction_date timestamp,
transaction_status varchar(50),
amount double NOT NULL,
transaction_by varchar(36) NOT NULL,
transaction_add_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
updated_by varchar(36),
updated_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
payslip_id varchar(36),
primary key(transaction_id),
FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
FOREIGN KEY (transaction_by) REFERENCES users(user_id)
);