
INSTITUTE_STANDARDS_URL=$1/2.0/institute/standard

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class I",
  "stream" : "NA",
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class II",
  "stream" : "NA",
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class III",
  "stream" : "NA",
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class IV",
  "stream" : "NA",
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class V",
  "stream" : "NA",
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class VI",
  "stream" : "NA",
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class VII",
  "stream" : "NA",
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class VIII",
  "stream" : "NA",
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class IX",
  "stream" : "NA",
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class X",
  "stream" : "NA",
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class XI",
  "stream" : "SCIENCE",
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class XI",
  "stream" : "COMMERCE",
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class XI",
  "stream" : "ARTS",
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class XII",
  "stream" : "SCIENCE",
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class XII",
  "stream" : "COMMERCE",
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class XII",
  "stream" : "ARTS",
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
}' $INSTITUTE_STANDARDS_URL