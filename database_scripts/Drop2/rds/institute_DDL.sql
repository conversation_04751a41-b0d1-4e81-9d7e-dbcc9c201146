CREATE TABLE IF NOT EXISTS academic_session(
	institute_id int NOT NULL,
	academic_session_id int PRIMARY KEY AUTO_INCREMENT NOT NULL,
	start_year int(4) NOT NULL,
	end_year int(4) NOT NULL,
	start_month int(2) NOT NULL,
	end_month int(2) NOT NULL,
	UNIQUE KEY (institute_id, start_year)
);


CREATE TABLE IF NOT EXISTS standards(
	institute_id int NOT NULL,
	standard_id varchar(36) PRIMARY KEY NOT NULL,
	standard_name varchar(255) NOT NULL,
	stream varchar(255) NOT NULL,
	level int,
	UNIQUE KEY (institute_id, standard_name, stream),
	FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);


CREATE TABLE IF NOT EXISTS standard_section_mapping(
	standard_id varchar(36) NOT NULL,
	section_id int AUTO_INCREMENT NOT NULL,
	section_name varchar(225) NOT NULL,
	primary key(section_id),
	UNIQUE KEY (standard_id, section_name),
	FOREIGN KEY (standard_id) REFERENCES standards(standard_id)
);


CREATE TABLE IF NOT EXISTS students(
	institute_id int NOT NULL,
	registration_number varchar(225) NOT NULL,
	student_id varchar(36) PRIMARY KEY NOT NULL,
	admission_date TIMESTAMP NULL DEFAULT NULL,
	name varchar(225)NOT NULL,
	date_of_birth TIMESTAMP NULL DEFAULT NULL,
	birth_place varchar(225),
	gender varchar(15),
	caste varchar(256),
	rte tinyint(1) default 0,
	aadhar_number varchar(225),
	permanent_address text,
	city varchar(256),
	state varchar(256),
	zipcode varchar (15),
	father_name varchar(225),
	mother_name varchar(225),
	father_occupation varchar(225),
	mother_occupation varchar(225),
	father_contact_number varchar(225),
	mother_contact_number varchar(225),
	family_approx_income varchar(225),
	gardians_details text,
	previous_school_name varchar(225),
	class_passed varchar(225),
	previous_school_medium varchar(225),
	result varchar(225),
	percentage varchar(225),
	year_of_passing int,
	blood_group varchar(225),
	blood_pressure varchar(225),
	pulse varchar(225),
	height varchar(50),
	weight varchar(50),
	date_of_physical_examination TIMESTAMP NULL DEFAULT NULL,
	admission_academic_session int,
	UNIQUE KEY (institute_id, registration_number),
	FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
	FOREIGN KEY (admission_academic_session) REFERENCES academic_session(academic_session_id)
);


CREATE TABLE IF NOT EXISTS student_academic_session_details(
	academic_session_id int NOT NULL,
	student_id varchar(36) NOT NULL,
	standard_id varchar(36) NOT NULL,
	section_id int,
	roll_number varchar(225),
	primary key(academic_session_id, student_id),
	FOREIGN KEY (standard_id) REFERENCES standards(standard_id),
	FOREIGN KEY (student_id) REFERENCES students(student_id),
	FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
);

-- FOREIGN KEY (section_id) REFERENCES standard_section_mapping(section_id)