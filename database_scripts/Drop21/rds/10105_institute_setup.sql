use lernen

--INSERT INTO organisation (organisation_id, name)
--VALUES ("048a25e4-62e6-11ea-bc55-0242ac130003", "A.G. MISSION SCHOOL");

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code) 
VALUES (10105,'MOTHERS PRIDE SEC. SCHOOL', 'A- 27, BHARDWAJ MARG, BARKAT NAGAR,', 'TONK PHATAK,', 'JA<PERSON><PERSON>', 'RAJASTHAN', 'India', '', '', '', '', '', '', '', UUID());

--update institute set branch_name = "A.G. MISSION SCHOOL", institute_name = "A.G. MISSION SCHOOL", logo_url="" where institute_id = 10100;

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10105, 2020, 2021, 4, 3);

insert into fee_category (institute_id, fee_category, description) values (10105, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10105, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10105, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10105, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10105, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10105, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10105, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag)
VALUES(10105, (SELECT fee_category_id from fee_category where institute_id = 10105 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10105,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10105,'books',0);
insert into categories (institute_id,category_name) values (10105,'clothing');
insert into categories (institute_id,category_name) values (10105,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10105,'note book',0,0);
insert into categories (institute_id,category_name) values (10105,'art & craft');
insert into categories (institute_id,category_name) values (10105,'personal care');
insert into categories (institute_id,category_name) values (10105,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10105,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10105,'accessories');
insert into categories (institute_id,category_name) values (10105,'furniture');
insert into categories (institute_id,category_name) values (10105,'electronics');
insert into categories (institute_id,category_name) values (10105,'sports');


insert into colors (institute_id, color_name) values (10105,'maroon');
insert into colors (institute_id, color_name) values (10105,'black');
insert into colors (institute_id, color_name) values (10105,'brown');
insert into colors (institute_id, color_name) values (10105,'white');
insert into colors (institute_id, color_name) values (10105,'red');
insert into colors (institute_id, color_name) values (10105,'yellow');
insert into colors (institute_id, color_name) values (10105,'blue');
insert into colors (institute_id, color_name) values (10105,'navy blue');
insert into colors (institute_id, color_name) values (10105,'green');
insert into colors (institute_id, color_name) values (10105,'dark green');
insert into colors (institute_id, color_name) values (10105,'pink');
insert into colors (institute_id, color_name) values (10105,'purple');
insert into colors (institute_id, color_name) values (10105,'grey');
insert into colors (institute_id, color_name) values (10105,'olive');
insert into colors (institute_id, color_name) values (10105,'cyan');
insert into colors (institute_id, color_name) values (10105,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10105, 'FEE_INVOICE', 1, "");
-- insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10105', 'meta_data', 'enable_permission', 'true');

--insert into default_fee_assignment_structure_meta_data values(10105, "Installments", "1aed4d2d-2d8e-4741-8b4c-6454126c1701", "ENROLLMENT");
--insert into default_fee_assignment_structure select "1aed4d2d-2d8e-4741-8b4c-6454126c1701", (select standard_id from standards where institute_id=10105 and standard_name = '12th' and stream = 'COMMERCE'), "CLASS", fee_id , 80, 6900 from fee_configuration where institute_id = 10105 and fee_type = "REGULAR" and academic_session_id = 26;



insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10105, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10105, "Grade", "SYSTEM", "GRADE", 1);



SET @institute_id := 10105;
SET @academic_session_id := 41;
SET @course_type := "SCHOLASTIC";

--0.0, 0.33  - E
--0.33, 0.405 - D
--0.405 , 0.505 - C
--0.505, 0.605 - C+
--0.605, 0.705  - B
--0.705, 0.805 - B+
--0.805, 0.905 - A
--0.905, 1.1  - A+

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end) 
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 10, 0.905, 1.1 from standards where institute_id = @institute_id;


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end) 
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 9, 0.805, 0.905 from standards where institute_id = @institute_id;


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end) 
select @institute_id, @academic_session_id, standard_id, @course_type, 'B+', 8, 0.705, 0.805 from standards where institute_id = @institute_id;


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end) 
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 7, 0.605, 0.705 from standards where institute_id = @institute_id;


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end) 
select @institute_id, @academic_session_id, standard_id, @course_type, 'C+', 6, 0.505 , 0.605 from standards where institute_id = @institute_id;


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end) 
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 5, 0.405 , 0.505 from standards where institute_id = @institute_id;


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end) 
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 4, 0.33 , 0.405 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end) 
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 3, 0.0 , 0.33 from standards where institute_id = @institute_id;

SET @institute_id := 10105;
SET @academic_session_id := 41;
SET @course_type := "COSCHOLASTIC";


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 10, 0.905, 1.1 from standards where institute_id = @institute_id;


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 9, 0.805, 0.905 from standards where institute_id = @institute_id;


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B+', 8, 0.705, 0.805 from standards where institute_id = @institute_id;


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 7, 0.605, 0.705 from standards where institute_id = @institute_id;


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C+', 6, 0.505 , 0.605 from standards where institute_id = @institute_id;


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 5, 0.405 , 0.505 from standards where institute_id = @institute_id;


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 4, 0.33 , 0.405 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 3, 0.0 , 0.33 from standards where institute_id = @institute_id;

insert into standards_metadata select 10105, 41, standard_id, 1, 0 from standards where institute_id = 10105;