
--create view admission_session  as select allsession.* from (select institute_id, student_id, academic_session.academic_session_id, end_year from  student_academic_session_details join
-- academic_session on student_academic_session_details.academic_session_id = academic_session.academic_session_id) allsession join
-- (select institute_id, student_id, min(end_year) min_end_year from  student_academic_session_details join
--academic_session on student_academic_session_details.academic_session_id = academic_session.academic_session_id
--group by institute_id, student_id) ms on allsession.institute_id = ms.institute_id and allsession.student_id = ms.student_id
--and allsession.end_year = ms.min_end_year;
--

--update students join admission_session on
--students.institute_id = admission_session.institute_id and students.student_id = admission_session.student_id
--set students.admission_academic_session = admission_session.academic_session_id;