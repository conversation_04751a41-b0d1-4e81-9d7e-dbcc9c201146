






select distinct student_fee_payments.institute_id, student_fee_payments.student_id, student_fee_payments.fee_id, student_fee_payments.fee_head_id, student_fee_payments.assigned_amount from student_fee_payments left join fee_assignment on student_fee_payments.student_id = fee_assignment.entity_id and student_fee_payments.fee_id = fee_assignment.fee_id and student_fee_payments.fee_head_id = fee_assignment.fee_head_id where fee_assignment.entity_id is null and student_fee_payments.institute_id = 10001;
select distinct student_fee_payments.institute_id, student_fee_payments.student_id from student_fee_payments left join fee_assignment on student_fee_payments.student_id = fee_assignment.entity_id and student_fee_payments.fee_id = fee_assignment.fee_id and student_fee_payments.fee_head_id = fee_assignment.fee_head_id where fee_assignment.entity_id is null and student_fee_payments.institute_id = 10001;


insert into fee_assignment select distinct student_fee_payments.institute_id, student_fee_payments.student_id, 'STUDENT', student_fee_payments.fee_id, student_fee_payments.fee_head_id, student_fee_payments.assigned_amount from student_fee_payments left join fee_assignment on student_fee_payments.student_id = fee_assignment.entity_id and student_fee_payments.fee_id = fee_assignment.fee_id and student_fee_payments.fee_head_id = fee_assignment.fee_head_id where fee_assignment.entity_id is null and student_fee_payments.institute_id = 10025;




select distinct a.student_id, a.fee_id, a.fee_head_id, a.assigned_amount from (select distinct student_fee_payments.institute_id, student_fee_payments.student_id, student_fee_payments.fee_id, student_fee_payments.fee_head_id, student_fee_payments.assigned_amount from student_fee_payments left join fee_assignment on student_fee_payments.student_id = fee_assignment.entity_id and student_fee_payments.fee_id = fee_assignment.fee_id and student_fee_payments.fee_head_id = fee_assignment.fee_head_id where fee_assignment.entity_id is null and student_fee_payments.institute_id = 10005) a left join fee_assignment b on a.institute_id = b.institute_id and a.fee_id = b.fee_id and a.fee_head_id = b.fee_head_id and b.entity_name = 'CLASS' where b.entity_id is null;


select distinct a.student_id, a.fee_id, a.fee_head_id, a.assigned_amount from (select distinct student_fee_payments.institute_id, student_fee_payments.student_id, student_fee_payments.fee_id, student_fee_payments.fee_head_id, student_fee_payments.assigned_amount from student_fee_payments left join fee_assignment on student_fee_payments.student_id = fee_assignment.entity_id and student_fee_payments.fee_id = fee_assignment.fee_id and student_fee_payments.fee_head_id = fee_assignment.fee_head_id where fee_assignment.entity_id is null and student_fee_payments.institute_id = 10005) a join fee_configuration on a.fee_id = fee_configuration.fee_id join student_academic_session_details c on a.student_id = c.student_id and fee_configuration.academic_session_id = c.academic_session_id join fee_assignment b on a.institute_id = b.institute_id and c.standard_id = b.entity_id and a.fee_id = b.fee_id and a.fee_head_id = b.fee_head_id and b.entity_name = 'CLASS';

-- For 10005 students where fee is not assigned at class level
select distinct a.student_id, a.fee_id, a.fee_head_id, a.assigned_amount from (select distinct student_fee_payments.institute_id, student_fee_payments.student_id, student_fee_payments.fee_id, student_fee_payments.fee_head_id, student_fee_payments.assigned_amount from student_fee_payments left join fee_assignment on student_fee_payments.student_id = fee_assignment.entity_id and student_fee_payments.fee_id = fee_assignment.fee_id and student_fee_payments.fee_head_id = fee_assignment.fee_head_id where fee_assignment.entity_id is null and student_fee_payments.institute_id = 10005) a join fee_configuration on a.fee_id = fee_configuration.fee_id join student_academic_session_details c on a.student_id = c.student_id and fee_configuration.academic_session_id = c.academic_session_id left join fee_assignment b on a.institute_id = b.institute_id and c.standard_id = b.entity_id and a.fee_id = b.fee_id and a.fee_head_id = b.fee_head_id and b.entity_name = 'CLASS' where b.entity_id is null;


insert into fee_assignment select distinct a.institute_id, a.student_id,  'STUDENT', a.fee_id, a.fee_head_id, a.assigned_amount from (select distinct student_fee_payments.institute_id, student_fee_payments.student_id, student_fee_payments.fee_id, student_fee_payments.fee_head_id, student_fee_payments.assigned_amount from student_fee_payments left join fee_assignment on student_fee_payments.student_id = fee_assignment.entity_id and student_fee_payments.fee_id = fee_assignment.fee_id and student_fee_payments.fee_head_id = fee_assignment.fee_head_id where fee_assignment.entity_id is null and student_fee_payments.institute_id = 10005) a join fee_configuration on a.fee_id = fee_configuration.fee_id join student_academic_session_details c on a.student_id = c.student_id and fee_configuration.academic_session_id = c.academic_session_id left join fee_assignment b on a.institute_id = b.institute_id and c.standard_id = b.entity_id and a.fee_id = b.fee_id and a.fee_head_id = b.fee_head_id and b.entity_name = 'CLASS' where b.entity_id is null;
