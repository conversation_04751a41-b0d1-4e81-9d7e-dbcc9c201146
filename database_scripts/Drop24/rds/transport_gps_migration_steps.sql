-- skip - only required to execute before to check if area and vehicle type are having unique amount
-- select transport_service_route.institute_id, transport_service_route.academic_session_id, transport_vehicle.vehicle_type, transport_service_route_stoppages.area_id, count(transport_service_route_stoppages.assigned_amount) c, sum(transport_service_route_stoppages.assigned_amount) s, min(transport_service_route_stoppages.assigned_amount) minn, max(transport_service_route_stoppages.assigned_amount) maxx from transport_service_route join transport_service_route_stoppages on transport_service_route.service_route_id = transport_service_route_stoppages.service_route_id join transport_vehicle on transport_service_route.vehicle_id = transport_vehicle.vehicle_id group by 1, 2, 3, 4;


ALTER TABLE transport_vehicle ADD vehicle_type_id int NOT NULL;

CREATE TABLE IF NOT EXISTS transport_vehicle_type(
	institute_id int NOT NULL,
	vehicle_type_id int PRIMARY KEY AUTO_INCREMENT NOT NULL,
	vehicle_type_name varchar(1024) NOT NULL,
	UNIQUE KEY (institute_id, vehicle_type_name)
);

---------------------------------
--insert into transport_vehicle_type(institute_id, vehicle_type_name) select institute_id, 'BUS' from institute;
--insert into transport_vehicle_type(institute_id, vehicle_type_name) select institute_id, 'VAN' from institute;
--insert into transport_vehicle_type(institute_id, vehicle_type_name) select institute_id, 'AUTO' from institute;


update transport_vehicle join transport_vehicle_type on transport_vehicle.institute_id = transport_vehicle_type.institute_id
and transport_vehicle.vehicle_type = transport_vehicle_type.vehicle_type_name set transport_vehicle.vehicle_type_id = transport_vehicle_type.vehicle_type_id;

ALTER TABLE transport_vehicle DROP COLUMN vehicle_type;

CREATE TABLE IF NOT EXISTS transport_area_amount(
	institute_id int NOT NULL,
	academic_session_id int NOT NULL,
	area_id int NOT NULL,
	vehicle_type_id int NOT NULL,
	amount double NOT NULL,
	PRIMARY KEY (institute_id, academic_session_id, area_id, vehicle_type_id),
	FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
	FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
	FOREIGN KEY (area_id) REFERENCES transport_area(area_id),
	FOREIGN KEY (vehicle_type_id) REFERENCES transport_vehicle_type(vehicle_type_id)
);

ALTER TABLE transport_service_route ADD default_driver varchar(36);
ALTER TABLE transport_service_route ADD default_conductor varchar(36);
ALTER TABLE transport_service_route ADD route_type varchar(255) NOT NULL;


SHOW INDEXES FROM transport_service_route;
ALTER TABLE transport_service_route DROP INDEX institute_id;
ALTER TABLE transport_service_route ADD CONSTRAINT UNIQUE_KEY UNIQUE(institute_id, academic_session_id, service_route_name, route_type);
SHOW INDEXES FROM transport_service_route;


update transport_service_route set route_type = "PICK_UP";

create table temp_uuids(uuid varchar(36));

--insert into temp_uuids (uuid) values ('3f60d025-9cbf-4cb3-8cad-1a34bf3cc5e5');
--insert into temp_uuids (uuid) values ('7aa6479a-e0ef-4217-92f4-83e59c20f744');

SET @route_row_number = 0;
SET @uuid_row_number = 0;

--select institute_id, academic_session_id, v_uuid.uuid, service_route_name, vehicle_id, "DROP_OFF" from (select (@route_row_number:=@route_row_number + 1) as row_num, transport_service_route.* from transport_service_route where route_type = "PICK_UP") v_route join (select (@uuid_row_number:=@uuid_row_number + 1) as row_num, temp_uuids.* from temp_uuids) v_uuid on v_route.row_num = v_uuid.row_num;

--insert into transport_service_route (institute_id, academic_session_id, service_route_id, service_route_name, vehicle_id, route_type) -- above select statement

ALTER TABLE transport_service_route_stoppages ADD time varchar(255) NOT NULL;
--update transport_service_route_stoppages set time = pickup_time;

--select drop_route.service_route_id, area_id, drop_time from transport_service_route_stoppages join transport_service_route pickup_route on pickup_route.service_route_id = transport_service_route_stoppages.service_route_id join transport_service_route drop_route on pickup_route.institute_id = drop_route.institute_id and pickup_route.academic_session_id = drop_route.academic_session_id and pickup_route.service_route_name = drop_route.service_route_name and drop_route.route_type = "DROP_OFF"

--insert into transport_service_route_stoppages (service_route_id, area_id, time) -- above select statement

ALTER TABLE transport_service_route_stoppages DROP COLUMN pickup_time;
ALTER TABLE transport_service_route_stoppages DROP COLUMN drop_time;


ALTER TABLE transport_history CHANGE `service_route_id` `pickup_service_route_id` varchar(36);
ALTER TABLE transport_history ADD drop_service_route_id varchar(36) after pickup_service_route_id;

--select drop_route.service_route_id from transport_history join transport_service_route pickup_route on pickup_route.service_route_id = transport_history.pickup_service_route_id join transport_service_route drop_route on pickup_route.institute_id = drop_route.institute_id and pickup_route.academic_session_id = drop_route.academic_session_id and pickup_route.service_route_name = drop_route.service_route_name and drop_route.route_type = "DROP_OFF"

--update transport_history join transport_service_route pickup_route on pickup_route.service_route_id = transport_history.pickup_service_route_id join transport_service_route drop_route on pickup_route.institute_id = drop_route.institute_id and pickup_route.academic_session_id = drop_route.academic_session_id and pickup_route.service_route_name = drop_route.service_route_name and drop_route.route_type = "DROP_OFF"
--set drop_service_route_id = drop_route.service_route_id;


-- select transport_vehicle.vehicle_type_id, transport_service_route.vehicle_id, transport_service_route_stoppages.area_id, transport_service_route_stoppages.assigned_amount from transport_service_route join transport_service_route_stoppages on transport_service_route.service_route_id = transport_service_route_stoppages.service_route_id join transport_vehicle on transport_service_route.vehicle_id = transport_vehicle.vehicle_id where  route_type = "PICK_UP";


-- 1 - select transport_service_route.institute_id, transport_service_route.academic_session_id, transport_service_route_stoppages.area_id, transport_vehicle.vehicle_type_id, count(transport_service_route_stoppages.assigned_amount) c, sum(transport_service_route_stoppages.assigned_amount) s, min(transport_service_route_stoppages.assigned_amount) minn, max(transport_service_route_stoppages.assigned_amount) maxx from transport_service_route join transport_service_route_stoppages on transport_service_route.service_route_id = transport_service_route_stoppages.service_route_id join transport_vehicle on transport_service_route.vehicle_id = transport_vehicle.vehicle_id where  route_type = "PICK_UP" group by 1, 2, 3, 4;
-- 2 - select transport_service_route.institute_id, transport_service_route.academic_session_id, transport_service_route_stoppages.area_id, transport_vehicle.vehicle_type_id, avg(transport_service_route_stoppages.assigned_amount) amount from transport_service_route join transport_service_route_stoppages on transport_service_route.service_route_id = transport_service_route_stoppages.service_route_id join transport_vehicle on transport_service_route.vehicle_id = transport_vehicle.vehicle_id where  route_type = "PICK_UP" and transport_service_route.academic_session_id > 0 group by 1, 2, 3, 4;

-- insert into transport_area_amount (institute_id, academic_session_id, area_id, vehicle_type_id, amount) -- above select statement 2

-- skip - only required to execute before to check if area and vehicle type are having unique amount
-- select transport_service_route.institute_id, transport_service_route.academic_session_id, transport_vehicle.vehicle_type, transport_service_route_stoppages.area_id, count(transport_service_route_stoppages.assigned_amount) c, sum(transport_service_route_stoppages.assigned_amount) s, min(transport_service_route_stoppages.assigned_amount) minn, max(transport_service_route_stoppages.assigned_amount) maxx from transport_service_route join transport_service_route_stoppages on transport_service_route.service_route_id = transport_service_route_stoppages.service_route_id join transport_vehicle on transport_service_route.vehicle_id = transport_vehicle.vehicle_id group by 1, 2, 3, 4;


ALTER TABLE transport_service_route_stoppages DROP COLUMN assigned_amount;