CREATE TABLE IF NOT EXISTS transport_tracking_details (
    institute_id int NOT NULL,
    transport_trip_id varchar(36) NOT NULL,
    service_route_id varchar(36) NOT NULL,
    trip_date int NOT NULL,
    driver_id varchar(36) NOT NULL,
    conductor_id varchar(36),
    trip_start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    trip_end_time TIMESTAMP NULL,
    trip_status varchar(255) NOT NULL,
    metadata text,
    description text,
    PRIMARY KEY (transport_trip_id),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);
