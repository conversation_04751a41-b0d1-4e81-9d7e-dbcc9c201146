use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code)
VALUES (702,'EMBRATE SCHOOL', ',', ',', 'JAIP<PERSON>', 'RAJASTHAN', 'INDIA', '', '', '', '', '', '', '', UUID());

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(702, 2021, 2022, 4, 3);

insert into fee_category (institute_id, fee_category, description) values (702, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (702, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (702, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (702, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (702, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (702, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (702, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag)
VALUES(702, (SELECT fee_category_id from fee_category where institute_id = 702 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (702,'stationery',0);
insert into categories (institute_id,category_name,genders) values (702,'books',0);
insert into categories (institute_id,category_name) values (702,'clothing');
insert into categories (institute_id,category_name) values (702,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (702,'note book',0,0);
insert into categories (institute_id,category_name) values (702,'art & craft');
insert into categories (institute_id,category_name) values (702,'personal care');
insert into categories (institute_id,category_name) values (702,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (702,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (702,'accessories');
insert into categories (institute_id,category_name) values (702,'furniture');
insert into categories (institute_id,category_name) values (702,'electronics');
insert into categories (institute_id,category_name) values (702,'sports');


insert into colors (institute_id, color_name) values (702,'maroon');
insert into colors (institute_id, color_name) values (702,'black');
insert into colors (institute_id, color_name) values (702,'brown');
insert into colors (institute_id, color_name) values (702,'white');
insert into colors (institute_id, color_name) values (702,'red');
insert into colors (institute_id, color_name) values (702,'yellow');
insert into colors (institute_id, color_name) values (702,'blue');
insert into colors (institute_id, color_name) values (702,'navy blue');
insert into colors (institute_id, color_name) values (702,'green');
insert into colors (institute_id, color_name) values (702,'dark green');
insert into colors (institute_id, color_name) values (702,'pink');
insert into colors (institute_id, color_name) values (702,'purple');
insert into colors (institute_id, color_name) values (702,'grey');
insert into colors (institute_id, color_name) values (702,'olive');
insert into colors (institute_id, color_name) values (702,'cyan');
insert into colors (institute_id, color_name) values (702,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (702, 'FEE_INVOICE', 1, "");
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '702', 'meta_data', 'enable_permission', 'true');

insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(702, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(702, "Grade", "SYSTEM", "GRADE", 1);

--insert into default_fee_assignment_structure_meta_data values(702, "Installments", "1aed4d2d-2d8e-4741-8b4c-6454126c1701", "ENROLLMENT");
--insert into default_fee_assignment_structure select "1aed4d2d-2d8e-4741-8b4c-6454126c1701", (select standard_id from standards where institute_id=702 and standard_name = '12th' and stream = 'COMMERCE'), "CLASS", fee_id , 80, 6900 from fee_configuration where institute_id = 10105 and fee_type = "REGULAR" and academic_session_id = 26;

SET @institute_id := 702;
SET @academic_session_id := 49;
SET @course_type := "SCHOLASTIC";


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A1', 9, 0.905, 1.1 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A2', 8, 0.805, 0.905 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B1', 7, 0.705, 0.805 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B2', 6, 0.605, 0.705 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C1', 5, 0.505 , 0.605 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C2', 4, 0.405 , 0.505 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 3, 0.33 , 0.405 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 0, 0.0 , 0.33 from standards where institute_id = @institute_id;

-- Setting COSCHOLASTIC Grades

SET @course_type := "COSCHOLASTIC";


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 10, 0.9, 1.1 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 9, 0.8, 0.9 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 8, 0.7, 0.8 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 7, 0.6, 0.7 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 6, 0.0, 0.6 from standards where institute_id = @institute_id;

insert into standards_metadata select 702, 49, standard_id, 1, 0 from standards where institute_id = 702;