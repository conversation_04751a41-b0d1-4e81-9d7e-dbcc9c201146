use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code)
VALUES (704,'St. Xavier\'s School', ',', ',', '<PERSON>ari', '<PERSON><PERSON><PERSON>o', 'INDIA', '', '', '', '', '', '', '', UUID());

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(704, 2022, 2023, 4, 3);

insert into fee_category (institute_id, fee_category, description) values (704, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (704, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (704, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (704, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (704, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (704, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (704, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag)
VALUES(704, (SELECT fee_category_id from fee_category where institute_id = 704 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (704,'stationery',0);
insert into categories (institute_id,category_name,genders) values (704,'books',0);
insert into categories (institute_id,category_name) values (704,'clothing');
insert into categories (institute_id,category_name) values (704,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (704,'note book',0,0);
insert into categories (institute_id,category_name) values (704,'art & craft');
insert into categories (institute_id,category_name) values (704,'personal care');
insert into categories (institute_id,category_name) values (704,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (704,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (704,'accessories');
insert into categories (institute_id,category_name) values (704,'furniture');
insert into categories (institute_id,category_name) values (704,'electronics');
insert into categories (institute_id,category_name) values (704,'sports');


insert into colors (institute_id, color_name) values (704,'maroon');
insert into colors (institute_id, color_name) values (704,'black');
insert into colors (institute_id, color_name) values (704,'brown');
insert into colors (institute_id, color_name) values (704,'white');
insert into colors (institute_id, color_name) values (704,'red');
insert into colors (institute_id, color_name) values (704,'yellow');
insert into colors (institute_id, color_name) values (704,'blue');
insert into colors (institute_id, color_name) values (704,'navy blue');
insert into colors (institute_id, color_name) values (704,'green');
insert into colors (institute_id, color_name) values (704,'dark green');
insert into colors (institute_id, color_name) values (704,'pink');
insert into colors (institute_id, color_name) values (704,'purple');
insert into colors (institute_id, color_name) values (704,'grey');
insert into colors (institute_id, color_name) values (704,'olive');
insert into colors (institute_id, color_name) values (704,'cyan');
insert into colors (institute_id, color_name) values (704,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (704, 'FEE_INVOICE', 1, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (704, 'AUDIO_VOICE_CALL_COUNTER', 0, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (704, 'SMS_COUNTER', 0, "");

insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '704', 'meta_data', 'enable_permission', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '704', 'exam_admit_card_preferences', 'include_class_roll_number', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '704', 'meta_data', 'institute_name_in_sms', 'Sadhu Ram Vidya Mandir');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '704', 'meta_data', 'institute_unique_code', 'srvm704');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '704', 'meta_data', 'sms_service_enabled', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '704', 'sms_preferences', 'buffer_sms_count', 0);

insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(704, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(704, "Grade", "SYSTEM", "GRADE", 1);

--insert into default_fee_assignment_structure_meta_data values(704, "Installments", "1aed4d2d-2d8e-4741-8b4c-6454126c1701", "ENROLLMENT");
--insert into default_fee_assignment_structure select "1aed4d2d-2d8e-4741-8b4c-6454126c1701", (select standard_id from standards where institute_id=704 and standard_name = '12th' and stream = 'COMMERCE'), "CLASS", fee_id , 80, 6900 from fee_configuration where institute_id = 10105 and fee_type = "REGULAR" and academic_session_id = 26;

insert into standards_metadata select 704, 41, standard_id, 1, 0 from standards where institute_id = 704;

SET @institute_id := 704;
SET @academic_session_id := 41;
SET @course_type := "SCHOLASTIC";


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A1', 9, 0.905, 1.1 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A2', 8, 0.805, 0.905 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B1', 7, 0.705, 0.805 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B2', 6, 0.605, 0.705 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C1', 5, 0.505 , 0.605 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C2', 4, 0.405 , 0.505 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 3, 0.33 , 0.405 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 0, 0.0 , 0.33 from standards where institute_id = @institute_id;

-- Setting COSCHOLASTIC Grades

SET @course_type := "COSCHOLASTIC";


insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 10, 0.9, 1.1 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 9, 0.8, 0.9 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 8, 0.7, 0.8 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 7, 0.6, 0.7 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 6, 0.0, 0.6 from standards where institute_id = @institute_id;


insert into configuration values('INSTITUTE', '704', 'meta_data', 'module_access', '["STORE","FEES","ADMISSION","TRANSPORT","COURSES","EXAMINATION","ATTENDANCE","STAFF_MANAGEMENT","INCOME_EXPENSE","USER_MANAGEMENT","LECTURE_MANAGEMENT","HOMEWORK_MANAGEMENT","SALARY_MANAGEMENT","NOTICE_BOARD_MANAGEMENT","STUDENT_MANAGEMENT","AUDIT_LOGS", "COMMUNICATION"]');