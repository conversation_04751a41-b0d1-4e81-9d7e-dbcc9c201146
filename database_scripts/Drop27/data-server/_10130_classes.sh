INSTITUTE_STANDARDS_URL=$1/2.0/institute/standard

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10130",
  "academicSessionId" : 61,
  "standardName": "Pre-Primary",
  "stream" : "NA",
  "level" : 1,
  "standardSectionList" : [{"sectionName" : "Toddlers"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10130",
  "academicSessionId" : 61,
  "standardName": "Nursery",
  "stream" : "NA",
  "level" : 2,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10130",
  "academicSessionId" : 61,
  "standardName": "Junior KG",
  "stream" : "NA",
  "level" : 3,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10130",
  "academicSessionId" : 61,
  "standardName": "Senior KG",
  "stream" : "NA",
  "level" : 4,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10130",
  "academicSessionId" : 61,
  "standardName": "I",
  "stream" : "NA",
  "level" : 5,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10130",
  "academicSessionId" : 61,
  "standardName": "II",
  "stream" : "NA",
  "level" : 6,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10130",
  "academicSessionId" : 61,
  "standardName": "III",
  "stream" : "NA",
  "level" : 7,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10130",
  "academicSessionId" : 61,
  "standardName": "IV",
  "stream" : "NA",
  "level" : 8,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10130",
  "academicSessionId" : 61,
  "standardName": "V",
  "stream" : "NA",
  "level" : 9,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10130",
  "academicSessionId" : 61,
  "standardName": "VI",
  "stream" : "NA",
  "level" : 10,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10130",
  "academicSessionId" : 61,
  "standardName": "VII",
  "stream" : "NA",
  "level" : 11,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10130",
  "academicSessionId" : 61,
  "standardName": "VIII",
  "stream" : "NA",
  "level" : 12,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10130",
  "academicSessionId" : 61,
  "standardName": "IX",
  "stream" : "NA",
  "level" : 13,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10130",
  "academicSessionId" : 61,
  "standardName": "X",
  "stream" : "NA",
  "level" : 14,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL