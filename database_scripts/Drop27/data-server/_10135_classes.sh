INSTITUTE_STANDARDS_URL=$1/2.0/institute/standard

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10135",
  "academicSessionId" : 63,
  "standardName": "NURSERY",
  "stream" : "NA",
  "level" : 1,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10135",
  "academicSessionId" : 63,
  "standardName": "LKG",
  "stream" : "NA",
  "level" : 2,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10135",
  "academicSessionId" : 63,
  "standardName": "UKG",
  "stream" : "NA",
  "level" : 3,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10135",
  "academicSessionId" : 63,
  "standardName": "I",
  "stream" : "NA",
  "level" : 4,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10135",
  "academicSessionId" : 63,
  "standardName": "II",
  "stream" : "NA",
  "level" : 5,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10135",
  "academicSessionId" : 63,
  "standardName": "III",
  "stream" : "NA",
  "level" : 6,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10135",
  "academicSessionId" : 63,
  "standardName": "IV",
  "stream" : "NA",
  "level" : 7,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10135",
  "academicSessionId" : 63,
  "standardName": "V",
  "stream" : "NA",
  "level" : 8,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10135",
  "academicSessionId" : 63,
  "standardName": "VI",
  "stream" : "NA",
  "level" : 9,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10135",
  "academicSessionId" : 63,
  "standardName": "VII",
  "stream" : "NA",
  "level" : 10,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10135",
  "academicSessionId" : 63,
  "standardName": "VIII",
  "stream" : "NA",
  "level" : 11,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
}' $INSTITUTE_STANDARDS_URL