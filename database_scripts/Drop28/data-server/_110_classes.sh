INSTITUTE_STANDARDS_URL=$1/2.0/institute/standard

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "110",
  "academicSessionId" : 70,
  "standardName": "NURSERY",
  "stream" : "NA",
  "level" : 1,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "110",
  "academicSessionId" : 70,
  "standardName": "LKG",
  "stream" : "NA",
  "level" : 2,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "110",
  "academicSessionId" : 70,
  "standardName": "UKG",
  "stream" : "NA",
  "level" : 3,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "110",
  "academicSessionId" : 70,
  "standardName": "I",
  "stream" : "NA",
  "level" : 4,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "110",
  "academicSessionId" : 70,
  "standardName": "II",
  "stream" : "NA",
  "level" : 5,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "110",
  "academicSessionId" : 70,
  "standardName": "III",
  "stream" : "NA",
  "level" : 6,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "110",
  "academicSessionId" : 70,
  "standardName": "IV",
  "stream" : "NA",
  "level" : 7,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "110",
  "academicSessionId" : 70,
  "standardName": "V",
  "stream" : "NA",
  "level" : 8,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "110",
  "academicSessionId" : 70,
  "standardName": "VI",
  "stream" : "NA",
  "level" : 9,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "110",
  "academicSessionId" : 70,
  "standardName": "VII",
  "stream" : "NA",
  "level" : 10,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "110",
  "academicSessionId" : 70,
  "standardName": "VIII",
  "stream" : "NA",
  "level" : 11,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "110",
  "academicSessionId" : 70,
  "standardName": "IX",
  "stream" : "NA",
  "level" : 12,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "110",
  "academicSessionId" : 70,
  "standardName": "X",
  "stream" : "NA",
  "level" : 13,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL