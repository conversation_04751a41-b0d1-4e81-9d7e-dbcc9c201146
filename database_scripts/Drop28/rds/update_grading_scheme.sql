SET @institute_id := 10005;
SET @academic_session_id := 46;
SET @course_type := "SCHOLASTIC";
insert into standards_metadata select @institute_id, @academic_session_id , standard_id, 1, 0 from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 9, 0.905, 1.1, "91-100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 8, 0.755, 0.905, "76-90" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 7, 0.605, 0.755, "61-75" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 6, 0.405, 0.605, "41-60" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 0, 0.0 , 0.405, "0-40" from standards where institute_id = @institute_id;
-- Setting COSCHOLASTIC Grades
SET @course_type := "COSCHOLASTIC";
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 9, 0.905, 1.1, "91-100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 8, 0.755, 0.905, "76-90" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 7, 0.605, 0.755, "61-75" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 6, 0.405, 0.605, "41-60" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 0, 0.0 , 0.405, "0-40" from standards where institute_id = @institute_id;


SET @institute_id := 10006;
SET @academic_session_id := 45;
SET @course_type := "SCHOLASTIC";
insert into standards_metadata select @institute_id, @academic_session_id , standard_id, 1, 0 from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 9, 0.905, 1.1, "91-100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 8, 0.755, 0.905, "76-90" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 7, 0.605, 0.755, "61-75" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 6, 0.405, 0.605, "41-60" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 0, 0.0 , 0.405, "0-40" from standards where institute_id = @institute_id;
-- Setting COSCHOLASTIC Grades
SET @course_type := "COSCHOLASTIC";
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 9, 0.905, 1.1, "91-100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 8, 0.755, 0.905, "76-90" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 7, 0.605, 0.755, "61-75" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 6, 0.405, 0.605, "41-60" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 0, 0.0 , 0.405, "0-40" from standards where institute_id = @institute_id;


SET @institute_id := 10070;
SET @academic_session_id := 58;
SET @course_type := "SCHOLASTIC";
insert into standards_metadata select @institute_id, @academic_session_id , standard_id, 1, 0 from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 9, 0.855, 1.1, "86 - 100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 8, 0.705, 0.855, "71 - 85" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 7, 0.505, 0.705, "51 - 70" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 6, 0.305, 0.505, "31 - 50" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 0, 0.0 , 0.305, "0 - 30" from standards where institute_id = @institute_id;
-- Setting COSCHOLASTIC Grades
SET @course_type := "COSCHOLASTIC";
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 9, 0.855, 1.1, "86 - 100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 8, 0.705, 0.855, "71 - 85" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 7, 0.505, 0.705, "51 - 70" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 6, 0.305, 0.505, "31 - 50" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 0, 0.0 , 0.305, "0 - 30" from standards where institute_id = @institute_id;


SET @institute_id := 10071;
SET @academic_session_id := 59;
SET @course_type := "SCHOLASTIC";
insert into standards_metadata select @institute_id, @academic_session_id , standard_id, 1, 0 from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 9, 0.855, 1.1, "86 - 100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 8, 0.705, 0.855, "71 - 85" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 7, 0.505, 0.705, "51 - 70" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 6, 0.305, 0.505, "31 - 50" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 0, 0.0 , 0.305, "0 - 30" from standards where institute_id = @institute_id;
-- Setting COSCHOLASTIC Grades
SET @course_type := "COSCHOLASTIC";
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 9, 0.855, 1.1, "86 - 100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 8, 0.705, 0.855, "71 - 85" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 7, 0.505, 0.705, "51 - 70" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 6, 0.305, 0.505, "31 - 50" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 0, 0.0 , 0.305, "0 - 30" from standards where institute_id = @institute_id;


SET @institute_id := 10075;
SET @academic_session_id := 51;
SET @course_type := "SCHOLASTIC";
insert into standards_metadata select @institute_id, @academic_session_id , standard_id, 1, 0 from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 9, 0.855, 1.1, "86 - 100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 8, 0.705, 0.855, "71 - 85" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 7, 0.505, 0.705, "51 - 70" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 6, 0.305, 0.505, "31 - 50" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 0, 0.0 , 0.305, "0 - 30" from standards where institute_id = @institute_id;
-- Setting COSCHOLASTIC Grades
SET @course_type := "COSCHOLASTIC";
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 9, 0.855, 1.1, "86 - 100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 8, 0.705, 0.855, "71 - 85" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 7, 0.505, 0.705, "51 - 70" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 6, 0.305, 0.505, "31 - 50" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 0, 0.0 , 0.305, "0 - 30" from standards where institute_id = @institute_id;


SET @institute_id := 10085;
SET @academic_session_id := 32;
SET @course_type := "SCHOLASTIC";
--insert into standards_metadata select @institute_id, @academic_session_id , standard_id, 1, 0 from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 9, 0.855, 1.1, "86 - 100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 8, 0.705, 0.855, "71 - 85" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 7, 0.505, 0.705, "51 - 70" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 6, 0.305, 0.505, "31 - 50" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 0, 0.0 , 0.305, "0 - 30" from standards where institute_id = @institute_id;
-- Setting COSCHOLASTIC Grades
SET @course_type := "COSCHOLASTIC";
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 9, 0.855, 1.1, "86 - 100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 8, 0.705, 0.855, "71 - 85" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 7, 0.505, 0.705, "51 - 70" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 6, 0.305, 0.505, "31 - 50" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 0, 0.0 , 0.305, "0 - 30" from standards where institute_id = @institute_id;