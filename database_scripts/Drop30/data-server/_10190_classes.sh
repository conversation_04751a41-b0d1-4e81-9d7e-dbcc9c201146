INSTITUTE_STANDARDS_URL=$1/2.0/institute/standard

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "NURSERY",
  "stream" : "NA",
  "level" : 1,
  "standardSectionList" : [{"sectionName" : "L"},{"sectionName" : "R"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "KG",
  "stream" : "NA",
  "level" : 2,
  "standardSectionList" : [{"sectionName" : "L"},{"sectionName" : "R"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "PREP",
  "stream" : "NA",
  "level" : 3,
  "standardSectionList" : [{"sectionName" : "L"},{"sectionName" : "R"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "I",
  "stream" : "NA",
  "level" : 4,
  "standardSectionList" : [{"sectionName" : "L"},{"sectionName" : "R"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "II",
  "stream" : "NA",
  "level" : 5,
  "standardSectionList" : [{"sectionName" : "L"},{"sectionName" : "R"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "III",
  "stream" : "NA",
  "level" : 6,
  "standardSectionList" : [{"sectionName" : "L"},{"sectionName" : "R"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "IV",
  "stream" : "NA",
  "level" : 7,
  "standardSectionList" : [{"sectionName" : "L"},{"sectionName" : "R"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "V",
  "stream" : "NA",
  "level" : 8,
  "standardSectionList" : [{"sectionName" : "L"},{"sectionName" : "R"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "VI",
  "stream" : "NA",
  "level" : 9,
  "standardSectionList" : [{"sectionName" : "L"},{"sectionName" : "R"},{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "VII",
  "stream" : "NA",
  "level" : 10,
  "standardSectionList" : [{"sectionName" : "L"},{"sectionName" : "R"},{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "VIII",
  "stream" : "NA",
  "level" : 11,
  "standardSectionList" : [{"sectionName" : "L"},{"sectionName" : "R"},{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "IX",
  "stream" : "NA",
  "level" : 12,
  "standardSectionList" : [{"sectionName" : "L"},{"sectionName" : "R"},{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "X",
  "stream" : "NA",
  "level" : 13,
  "standardSectionList" : [{"sectionName" : "L"},{"sectionName" : "R"},{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "XI",
  "stream" : "SCIENCE",
  "level" : 14,
  "standardSectionList" : [{"sectionName" : "L"},{"sectionName" : "R"},{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "XI",
  "stream" : "COMMERCE",
  "level" : 15,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "XI",
  "stream" : "ARTS",
  "level" : 16,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "XII",
  "stream" : "SCIENCE",
  "level" : 17,
  "standardSectionList" : [{"sectionName" : "L"},{"sectionName" : "R"},{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "XII",
  "stream" : "COMMERCE",
  "level" : 18,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10190",
  "academicSessionId" : 84,
  "standardName": "XII",
  "stream" : "ARTS",
  "level" : 19,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL