alter table students add column permanent_post_office varchar(500) after permanent_state;
alter table students add column permanent_police_station varchar(500) after permanent_post_office;
alter table students add column present_post_office varchar(500) after present_state;
alter table students add column present_police_station varchar(500) after present_post_office;
alter table students add column is_admission_tc_based tinyint(1) after gardians_details;
alter table students add column previous_school_tc_number varchar(500) after is_admission_tc_based;

alter table student_registration add column permanent_post_office varchar(500) after permanent_state;
alter table student_registration add column permanent_police_station varchar(500) after permanent_post_office;
alter table student_registration add column present_post_office varchar(500) after present_state;
alter table student_registration add column present_police_station varchar(500) after present_post_office;
alter table student_registration add column is_admission_tc_based tinyint(1) after guardians_details;
alter table student_registration add column previous_school_tc_number varchar(500) after is_admission_tc_based;