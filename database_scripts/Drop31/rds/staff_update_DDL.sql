alter table staff_details add column device_user_id varchar(64) after timing_details;
alter table staff_details add column rfid_card_data text after device_user_id;
alter table staff_details add column fingerprint_data text after rfid_card_data;
alter table staff_details add column face_data text after fingerprint_data;

CREATE INDEX device_user_id ON staff_details (device_user_id);
ALTER TABLE staff_details ADD CONSTRAINT UNIQUE_KEY UNIQUE(institute_id, device_user_id);



ALTER TABLE staff_details MODIFY face_data MEDIUMTEXT NULL;