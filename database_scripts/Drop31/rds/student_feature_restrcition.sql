insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GLOBAL', 'GLOBAL', 'meta_data', 'restriction_reason', 'Your institute have restrict this feature for you. Please contact school administration for further assistance.');

--for local data correction
select distinct(institute_id) from users where user_type = 'STUDENT';
select authorized_modules from users where institute_id = 10030 and user_type = 'STUDENT';
select config_value  from configuration where entity_id = 10030 and config_key = 'module_access';
update users set authorized_modules = (select config_value from configuration where entity_id = 10030 and config_key = 'module_access') where institute_id = 10030 and user_type = 'STUDENT';
select authorized_modules from users where institute_id = 10030 and user_type = 'STUDENT';

select REPLACE(authorized_modules, '"USER_MANAGEMENT",', '') from users where institute_id = 10030 and user_type = 'STUDENT';
update users set authorized_modules =  REPLACE(authorized_modules, '"USER_MANAGEMENT",', '') where user_type = 'STUDENT';

--for dev data correction
select distinct(institute_id) from users where user_type = 'STUDENT';
select authorized_modules from users where institute_id = 101 and user_type = 'STUDENT';
select config_value  from configuration where entity_id = 101 and config_key = 'module_access';
update users set authorized_modules = (select config_value from configuration where entity_id = 101 and config_key = 'module_access') where institute_id = 101 and user_type = 'STUDENT';
select authorized_modules from users where institute_id = 101 and user_type = 'STUDENT';

select authorized_modules from users where institute_id = 102 and user_type = 'STUDENT';
select config_value  from configuration where entity_id = 102 and config_key = 'module_access';
update users set authorized_modules = (select config_value from configuration where entity_id = 102 and config_key = 'module_access') where institute_id = 102 and user_type = 'STUDENT';
select authorized_modules from users where institute_id = 102 and user_type = 'STUDENT';

select authorized_modules from users where institute_id = 103 and user_type = 'STUDENT';
select config_value  from configuration where entity_id = 103 and config_key = 'module_access';
update users set authorized_modules = (select config_value from configuration where entity_id = 103 and config_key = 'module_access') where institute_id = 103 and user_type = 'STUDENT';
select authorized_modules from users where institute_id = 103 and user_type = 'STUDENT';

select authorized_modules from users where institute_id = 104 and user_type = 'STUDENT';
select config_value  from configuration where entity_id = 104 and config_key = 'module_access';
update users set authorized_modules = (select config_value from configuration where entity_id = 104 and config_key = 'module_access') where institute_id = 104 and user_type = 'STUDENT';
select authorized_modules from users where institute_id = 104 and user_type = 'STUDENT';

select authorized_modules from users where institute_id = 702 and user_type = 'STUDENT';
select config_value  from configuration where entity_id = 702 and config_key = 'module_access';
update users set authorized_modules = (select config_value from configuration where entity_id = 702 and config_key = 'module_access') where institute_id = 702 and user_type = 'STUDENT';
select authorized_modules from users where institute_id = 702 and user_type = 'STUDENT';

select authorized_modules from users where institute_id = 704 and user_type = 'STUDENT';
select config_value  from configuration where entity_id = 704 and config_key = 'module_access';
update users set authorized_modules = (select config_value from configuration where entity_id = 704 and config_key = 'module_access') where institute_id = 704 and user_type = 'STUDENT';
select authorized_modules from users where institute_id = 704 and user_type = 'STUDENT';

update users set authorized_modules =  REPLACE(authorized_modules, '"USER_MANAGEMENT",', '') where user_type = 'STUDENT';
update users set authorized_modules =  REPLACE(authorized_modules, ', "USER_MANAGEMENT"', '') where user_type = 'STUDENT';


select count(*) from users where authorized_modules like '%"USER_MANAGEMENT",%' and user_type = 'STUDENT';

select users.authorized_modules, configuration.config_value from  users join configuration on users.institute_id = configuration.entity_id and configuration.entity = "INSTITUTE" and configuration.config_key = 'module_access' where user_type = 'STUDENT';


update users join configuration on users.institute_id = configuration.entity_id and configuration.entity = "INSTITUTE" and configuration.config_key = 'module_access' set authorized_modules = configuration.config_value where user_type = 'STUDENT';


