select * from academic_session where institute_id = 10005;
select * from examination_grades where academic_session_id = 46;
select institute_id, 71, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 46;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 71, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 46;
select * from examination_grades where academic_session_id = 71;
select * from standards_metadata where academic_session_id = 46;
select institute_id, 71, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 46;
insert into standards_metadata select institute_id, 71, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 46;
select * from standards_metadata where academic_session_id = 71;


select * from academic_session where institute_id = 10006;
select * from examination_grades where academic_session_id = 45;
select institute_id, 72, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 45;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 72, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 45;
select * from examination_grades where academic_session_id = 72;
select * from standards_metadata where academic_session_id = 45;
select institute_id, 72, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 45;
insert into standards_metadata select institute_id, 72, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 45;
select * from standards_metadata where academic_session_id = 72;


select * from academic_session where institute_id = 10075;
select * from examination_grades where academic_session_id = 51;
select institute_id, 82, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 51;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 82, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 51;
select * from examination_grades where academic_session_id = 82;
select * from standards_metadata where academic_session_id = 51;
select institute_id, 82, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 51;
insert into standards_metadata select institute_id, 82, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 51;
select * from standards_metadata where academic_session_id = 82;


select * from academic_session where institute_id = 10135;
select * from examination_grades where academic_session_id = 63;
select institute_id, 93, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 63;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 93, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 63;
select * from examination_grades where academic_session_id = 93;
select * from standards_metadata where academic_session_id = 63;
select institute_id, 93, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 63;
insert into standards_metadata select institute_id, 93, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 63;
select * from standards_metadata where academic_session_id = 93;


select * from academic_session where institute_id = 10110;
select * from examination_grades where academic_session_id = 48;
select institute_id, 94, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 48;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 94, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 48;
select * from examination_grades where academic_session_id = 94;
select * from standards_metadata where academic_session_id = 48;
select institute_id, 94, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 48;
insert into standards_metadata select institute_id, 94, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 48;
select * from standards_metadata where academic_session_id = 94;


select * from academic_session where institute_id = 10070;
select * from examination_grades where academic_session_id = 58;
select institute_id, 95, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 58;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 95, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 58;
select * from examination_grades where academic_session_id = 95;
select * from standards_metadata where academic_session_id = 58;
select institute_id, 95, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 58;
insert into standards_metadata select institute_id, 95, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 58;
select * from standards_metadata where academic_session_id = 95;


select * from academic_session where institute_id = 10071;
select * from examination_grades where academic_session_id = 59;
select institute_id, 96, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 59;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 96, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 59;
select * from examination_grades where academic_session_id = 96;
select * from standards_metadata where academic_session_id = 59;
select institute_id, 96, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 59;
insert into standards_metadata select institute_id, 96, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 59;
select * from standards_metadata where academic_session_id = 96;

--'A+', 0.855, 1.1, "86-100"
--'A', 0.705, 0.855, "71-85"
--'B', 0.605, 0.705, "61-70"
--'C', 0.405, 0.605, "41-60"
--'D', 0.0 , 0.405, "0-40"
select * from examination_grades where academic_session_id = 71;
delete from examination_grades where academic_session_id = 71;

SET @institute_id := 10005;
SET @academic_session_id := 71;
SET @course_type := "SCHOLASTIC";
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 9, 0.855, 1.1, "86-100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 8, 0.705, 0.855, "71-85" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 7, 0.605, 0.705, "61-70" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 6, 0.405, 0.605, "41-60" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 0, 0.0 , 0.405, "0-40" from standards where institute_id = @institute_id;
-- Setting COSCHOLASTIC Grades
SET @course_type := "COSCHOLASTIC";
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 9, 0.855, 1.1, "86-100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 8, 0.705, 0.855, "71-85" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 7, 0.605, 0.705, "61-70" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 6, 0.405, 0.605, "41-60" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 0, 0.0 , 0.405, "0-40" from standards where institute_id = @institute_id;


select * from examination_grades where academic_session_id = 72;
delete from examination_grades where academic_session_id = 72;

SET @institute_id := 10006;
SET @academic_session_id := 72;
SET @course_type := "SCHOLASTIC";
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 9, 0.855, 1.1, "86-100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 8, 0.705, 0.855, "71-85" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 7, 0.605, 0.705, "61-70" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 6, 0.405, 0.605, "41-60" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 0, 0.0 , 0.405, "0-40" from standards where institute_id = @institute_id;
-- Setting COSCHOLASTIC Grades
SET @course_type := "COSCHOLASTIC";
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 9, 0.855, 1.1, "86-100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 8, 0.705, 0.855, "71-85" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 7, 0.605, 0.705, "61-70" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 6, 0.405, 0.605, "41-60" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 0, 0.0 , 0.405, "0-40" from standards where institute_id = @institute_id;
