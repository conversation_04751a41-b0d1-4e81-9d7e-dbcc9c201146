select * from academic_session where institute_id = 10145;
select * from standard_section_mapping where academic_session_id = 66;
--insert into standards_metadata select 10145, 66, standard_id, 1, 0 from standards where institute_id = 10145;
select * from standards_metadata where academic_session_id = 66;
select * from examination_grades where academic_session_id = 66;

insert into academic_session(institute_id, start_year, end_year, start_month, end_month, display_name) values(10145, 2023, 2024, 4, 3, NULL);

select standard_id, 140, section_name from standard_section_mapping where academic_session_id = 66;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 140, section_name from standard_section_mapping where academic_session_id = 66;

select institute_id, 140, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 66;
insert into standards_metadata select institute_id, 140, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 66;

select institute_id, 140, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 66;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 140, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 66;

select * from academic_session where institute_id = 10145;
select * from standard_section_mapping where academic_session_id = 140;
select * from standards_metadata where academic_session_id = 140;
select * from examination_grades where academic_session_id = 140;

--insert into default_fee_assignment_structure_meta_data values(10145, 140, "Monthly fees", UUID(), "ENROLLMENT");
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'NURSERY'), "CLASS", fee_id , 268, 600 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'LKG'), "CLASS", fee_id , 268, 600 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'UKG'), "CLASS", fee_id , 268, 600 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'I'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'II'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'III'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'IV'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'V'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'VI'), "CLASS", fee_id , 268, 800 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'VII'), "CLASS", fee_id , 268, 800 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'VIII'), "CLASS", fee_id , 268, 800 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'IX'), "CLASS", fee_id , 268, 900 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'X'), "CLASS", fee_id , 268, 900 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'XI' and stream = 'SCIENCE'), "CLASS", fee_id , 268, 1500 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'XI' and stream = 'ARTS'), "CLASS", fee_id , 268, 1000 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'XII' and stream = 'SCIENCE'), "CLASS", fee_id , 268, 1500 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10145 and standard_name = 'XII' and stream = 'ARTS'), "CLASS", fee_id , 268, 1000 from fee_configuration where institute_id = 10145 and fee_type = "REGULAR" and academic_session_id = 140;

--Add Default Exam Structure


select * from academic_session where institute_id = 10146;
select * from standard_section_mapping where academic_session_id = 67;
--insert into standards_metadata select 10146, 67, standard_id, 1, 0 from standards where institute_id = 10146;
select * from standards_metadata where academic_session_id = 67;
select * from examination_grades where academic_session_id = 67;

insert into academic_session(institute_id, start_year, end_year, start_month, end_month, display_name) values(10146, 2023, 2024, 4, 3, NULL);

select standard_id, 141, section_name from standard_section_mapping where academic_session_id = 67;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 141, section_name from standard_section_mapping where academic_session_id = 67;

select institute_id, 141, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 67;
insert into standards_metadata select institute_id, 141, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 67;

select institute_id, 141, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 67;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 141, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 67;

select * from academic_session where institute_id = 10146;
select * from standard_section_mapping where academic_session_id = 141;
select * from standards_metadata where academic_session_id = 141;
select * from examination_grades where academic_session_id = 141;

--insert into default_fee_assignment_structure_meta_data values(10146, 141, "Monthly fees", UUID(), "ENROLLMENT");
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'NURSERY'), "CLASS", fee_id , 268, 600 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'LKG'), "CLASS", fee_id , 268, 600 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'UKG'), "CLASS", fee_id , 268, 600 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'I'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'II'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'III'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'IV'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'V'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'VI'), "CLASS", fee_id , 268, 800 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'VII'), "CLASS", fee_id , 268, 800 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'VIII'), "CLASS", fee_id , 268, 800 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'IX'), "CLASS", fee_id , 268, 900 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'X'), "CLASS", fee_id , 268, 900 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'XI' and stream = 'SCIENCE'), "CLASS", fee_id , 268, 1500 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'XI' and stream = 'ARTS'), "CLASS", fee_id , 268, 1000 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'XII' and stream = 'SCIENCE'), "CLASS", fee_id , 268, 1500 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10146 and standard_name = 'XII' and stream = 'ARTS'), "CLASS", fee_id , 268, 1000 from fee_configuration where institute_id = 10146 and fee_type = "REGULAR" and academic_session_id = 141;

--Add Default Exam Structure

