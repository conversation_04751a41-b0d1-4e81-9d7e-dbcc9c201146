CREATE TABLE IF NOT EXISTS institute_houses(
	institute_id int NOT NULL,
	house_id varchar(36) PRIMARY KEY NOT NULL,
	name varchar(500) NOT NULL,
	created_by varchar(36) NOT NULL,
    created_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by varchar(36),
    updated_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    unique key (institute_id, name),
    FOREIGN KEY (created_by) REFERENCES users(user_id),
	FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);

ALTER TABLE students ADD COLUMN house_id varchar(36);
ALTER TABLE students ADD FOREIGN KEY (house_id) REFERENCES institute_houses(house_id);
ALTER TABLE students ADD COLUMN admission_class varchar(500);

ALTER TABLE students ADD COLUMN specially_abled_type varchar(500);
ALTER TABLE student_registration ADD COLUMN specially_abled_type varchar(500);

ALTER TABLE student_academic_session_details ADD medium varchar(500);