ALTER TABLE students add column relieved_metadata text;
ALTER TABLE students CHANGE status final_status VARCHAR(255) NOT NULL DEFAULT 'ENROLMENT_PENDING';
ALTER TABLE student_academic_session_details add column session_status VARCHAR(255) NOT NULL DEFAULT 'ENROLMENT_PENDING';

--Updating enroll student status
--select * from student_academic_session_details where student_id in (select student_id from students where status = 'ENROLLED');
--update student_academic_session_details set session_status = 'ENROLLED' where student_id in (select student_id from students where status = 'ENROLLED');



select user_id, user_status from users where user_id in (select staff_id from staff_details where staff_details.status = 'RELIEVED');
update users set user_status = 'DISABLED' where user_id in (select staff_id from staff_details where staff_details.status = 'RELIEVED');


select user_id, user_status from users where user_id in (select student_id from students where final_status in ('RELIEVED', 'NSO'));
update users set user_status = 'DISABLED' where user_id in (select student_id from students where final_status in ('RELIEVED', 'NSO'));