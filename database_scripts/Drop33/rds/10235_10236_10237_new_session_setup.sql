select * from academic_session where institute_id = 10235;
select * from standard_section_mapping where academic_session_id = 119;
--insert into standards_metadata select 10235, 119, standard_id, 1, 0 from standards where institute_id = 10235;
select * from standards_metadata where academic_session_id = 119;
select * from examination_grades where academic_session_id = 119;

--insert into academic_session(institute_id, start_year, end_year, start_month, end_month, display_name) values(10235, 2023, 2024, 4, 3, NULL);

select standard_id, 154, section_name from standard_section_mapping where academic_session_id = 119;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 154, section_name from standard_section_mapping where academic_session_id = 119;

select institute_id, 154, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 119;
insert into standards_metadata select institute_id, 154, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 119;

select institute_id, 154, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 119;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 154, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 119;

select * from academic_session where institute_id = 10235;
select * from standard_section_mapping where academic_session_id = 154;
select * from standards_metadata where academic_session_id = 154;
select * from examination_grades where academic_session_id = 154;

--insert into default_fee_assignment_structure_meta_data values(10235, 154, "Monthly fees", UUID(), "ENROLLMENT");
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'NURSERY'), "CLASS", fee_id , 268, 600 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'LKG'), "CLASS", fee_id , 268, 600 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'UKG'), "CLASS", fee_id , 268, 600 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'I'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'II'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'III'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'IV'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'V'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'VI'), "CLASS", fee_id , 268, 800 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'VII'), "CLASS", fee_id , 268, 800 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'VIII'), "CLASS", fee_id , 268, 800 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'IX'), "CLASS", fee_id , 268, 900 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'X'), "CLASS", fee_id , 268, 900 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'XI' and stream = 'SCIENCE'), "CLASS", fee_id , 268, 1500 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'XI' and stream = 'ARTS'), "CLASS", fee_id , 268, 1000 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'XII' and stream = 'SCIENCE'), "CLASS", fee_id , 268, 1500 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10235 and standard_name = 'XII' and stream = 'ARTS'), "CLASS", fee_id , 268, 1000 from fee_configuration where institute_id = 10235 and fee_type = "REGULAR" and academic_session_id = 154;

--Add Default Exam Structure


select * from academic_session where institute_id = 10236;
select * from standard_section_mapping where academic_session_id = 120;
--insert into standards_metadata select 10236, 120, standard_id, 1, 0 from standards where institute_id = 10236;
select * from standards_metadata where academic_session_id = 120;
select * from examination_grades where academic_session_id = 120;

--insert into academic_session(institute_id, start_year, end_year, start_month, end_month, display_name) values(10236, 2023, 2024, 4, 3, NULL);

select standard_id, 155, section_name from standard_section_mapping where academic_session_id = 120;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 155, section_name from standard_section_mapping where academic_session_id = 120;

select institute_id, 155, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 120;
insert into standards_metadata select institute_id, 155, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 120;

select institute_id, 155, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 120;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 155, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 120;

select * from academic_session where institute_id = 10236;
select * from standard_section_mapping where academic_session_id = 155;
select * from standards_metadata where academic_session_id = 155;
select * from examination_grades where academic_session_id = 155;

--insert into default_fee_assignment_structure_meta_data values(10236, 155, "Monthly fees", UUID(), "ENROLLMENT");
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'NURSERY'), "CLASS", fee_id , 268, 600 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'LKG'), "CLASS", fee_id , 268, 600 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'UKG'), "CLASS", fee_id , 268, 600 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'I'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'II'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'III'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'IV'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'V'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'VI'), "CLASS", fee_id , 268, 800 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'VII'), "CLASS", fee_id , 268, 800 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'VIII'), "CLASS", fee_id , 268, 800 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'IX'), "CLASS", fee_id , 268, 900 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'X'), "CLASS", fee_id , 268, 900 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'XI' and stream = 'SCIENCE'), "CLASS", fee_id , 268, 1500 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'XI' and stream = 'ARTS'), "CLASS", fee_id , 268, 1000 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'XII' and stream = 'SCIENCE'), "CLASS", fee_id , 268, 1500 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10236 and standard_name = 'XII' and stream = 'ARTS'), "CLASS", fee_id , 268, 1000 from fee_configuration where institute_id = 10236 and fee_type = "REGULAR" and academic_session_id = 155;

--Add Default Exam Structure


select * from academic_session where institute_id = 10237;
select * from standard_section_mapping where academic_session_id = 121;
--insert into standards_metadata select 10237, 121, standard_id, 1, 0 from standards where institute_id = 10237;
select * from standards_metadata where academic_session_id = 121;
select * from examination_grades where academic_session_id = 121;

--insert into academic_session(institute_id, start_year, end_year, start_month, end_month, display_name) values(10237, 2023, 2024, 4, 3, NULL);

select standard_id, 156, section_name from standard_section_mapping where academic_session_id = 121;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 156, section_name from standard_section_mapping where academic_session_id = 121;

select institute_id, 156, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 121;
insert into standards_metadata select institute_id, 156, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 121;

select institute_id, 156, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 121;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 156, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 121;

select * from academic_session where institute_id = 10237;
select * from standard_section_mapping where academic_session_id = 156;
select * from standards_metadata where academic_session_id = 156;
select * from examination_grades where academic_session_id = 156;

--insert into default_fee_assignment_structure_meta_data values(10237, 156, "Monthly fees", UUID(), "ENROLLMENT");
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'NURSERY'), "CLASS", fee_id , 268, 600 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'LKG'), "CLASS", fee_id , 268, 600 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'UKG'), "CLASS", fee_id , 268, 600 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'I'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'II'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'III'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'IV'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'V'), "CLASS", fee_id , 268, 700 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'VI'), "CLASS", fee_id , 268, 800 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'VII'), "CLASS", fee_id , 268, 800 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'VIII'), "CLASS", fee_id , 268, 800 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'IX'), "CLASS", fee_id , 268, 900 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'X'), "CLASS", fee_id , 268, 900 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'XI' and stream = 'SCIENCE'), "CLASS", fee_id , 268, 1500 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'XI' and stream = 'ARTS'), "CLASS", fee_id , 268, 1000 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'XII' and stream = 'SCIENCE'), "CLASS", fee_id , 268, 1500 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;
--insert into default_fee_assignment_structure select "117a2ed5-9329-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10237 and standard_name = 'XII' and stream = 'ARTS'), "CLASS", fee_id , 268, 1000 from fee_configuration where institute_id = 10237 and fee_type = "REGULAR" and academic_session_id = 156;

--Add Default Exam Structure