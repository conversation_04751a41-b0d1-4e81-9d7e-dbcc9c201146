CREATE TABLE IF NOT EXISTS homework_view_details(
    homework_id varchar(36) NOT NULL,
    student_id varchar(36)  NOT NULL,
    viewed_on TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY(homework_id, student_id),
    FOREI<PERSON><PERSON> KEY (homework_id) REFERENCES homework_details(homework_id),
    FOREIGN KEY (student_id) REFERENCES students(student_id)
);

ALTER TABLE homework_details MODIFY COLUMN course_id varchar(36) NULL;

ALTER TABLE homework_details ADD homework_type varchar(256) NOT NULL AFTER title;
UPDATE homework_details SET homework_type = 'HOMEWORK';

ALTER TABLE homework_details ADD academic_session_id int NOT NULL AFTER institute_id;
UPDATE homework_details a INNER JOIN class_courses b ON a.course_id = b.course_id SET a.academic_session_id = b.academic_session_id;

ALTER TABLE homework_submission_details ADD submitted_by varchar(36) NOT NULL AFTER student_id;
UPDATE homework_submission_details SET submitted_by = student_id;

ALTER TABLE homework_submission_details ADD status varchar(100) NOT NULL AFTER submission_status;
SELECT * from homework_submission_details WHERE teachers_status is null AND submission_status = 'SAVED';
UPDATE homework_submission_details SET status = 'SAVED'  WHERE  teachers_status is null AND submission_status = 'SAVED';

SELECT * from homework_submission_details WHERE teachers_status is null AND submission_status = 'SUBMITTED';
UPDATE homework_submission_details SET status = 'SUBMITTED' WHERE teachers_status is null AND submission_status = 'SUBMITTED';

SELECT * from homework_submission_details WHERE teachers_status = 'ACCEPTED';
UPDATE homework_submission_details SET status = 'ACCEPTED' WHERE teachers_status = 'ACCEPTED';

SELECT * from homework_submission_details  WHERE teachers_status = 'REDO';
UPDATE homework_submission_details SET status = 'REDO' WHERE teachers_status = 'REDO';

--Delete these columns after proper data migration and check
--ALTER TABLE homework_submission_details MODIFY COLUMN teachers_status varchar(100) NULL;
ALTER TABLE homework_submission_details MODIFY COLUMN submission_status varchar(100) NULL;
--ALTER TABLE homework_submission_details DROP COLUMN teachers_status;
--ALTER TABLE homework_submission_details DROP COLUMN submission_status;

--Run queries from database_scripts/Drop29/rds/remove_unique_key_homework_submission.sql