select * from academic_session where institute_id = 10135;
select * from standard_section_mapping where academic_session_id = 93;
--insert into standards_metadata select 10135, 93, standard_id, 1, 0 from standards where institute_id = 10135;
select * from standards_metadata where academic_session_id = 93;
select * from examination_grades where academic_session_id = 93;

insert into academic_session(institute_id, start_year, end_year, start_month, end_month, display_name, payroll_start_year, payroll_end_year, payroll_start_month, payroll_end_month, payroll_display_name) values(10135, 2024, 2025, 4, 3, NULL, 2024, 2025, 4, 3, NULL);

select standard_id, 169, section_name from standard_section_mapping where academic_session_id = 93;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 169, section_name from standard_section_mapping where academic_session_id = 93;

select institute_id, 169, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 93;
insert into standards_metadata select institute_id, 169, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 93;

select institute_id, 169, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 93;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 169, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 93;

select * from academic_session where institute_id = 10135;
select * from standard_section_mapping where academic_session_id = 169;
select * from standards_metadata where academic_session_id = 169;
select * from examination_grades where academic_session_id = 169;


insert into default_fee_assignment_structure_meta_data values(10135, 169, "Monthly Fees", "c0cb63d8-940d-11ee-83d3-bdccc474f77a", "ENROLLMENT");

select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'NURSERY'), "CLASS", fee_id , 174, 600 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;
insert into default_fee_assignment_structure select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'Nursery'), "CLASS", fee_id , 174, 600 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;

select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'LKG'), "CLASS", fee_id , 174, 600 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;
insert into default_fee_assignment_structure select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'LKG'), "CLASS", fee_id , 174, 600 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;

select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'UKG'), "CLASS", fee_id , 174, 600 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;
insert into default_fee_assignment_structure select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'UKG'), "CLASS", fee_id , 174, 600 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;



select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'I'), "CLASS", fee_id , 174, 750 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;
insert into default_fee_assignment_structure select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'I'), "CLASS", fee_id , 174, 750 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;

select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'II'), "CLASS", fee_id , 174, 750 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;
insert into default_fee_assignment_structure select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'II'), "CLASS", fee_id , 174, 750 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;

select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'III'), "CLASS", fee_id , 174, 750 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;
insert into default_fee_assignment_structure select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'III'), "CLASS", fee_id , 174, 750 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;



select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'IV'), "CLASS", fee_id , 174, 800 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;
insert into default_fee_assignment_structure select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'IV'), "CLASS", fee_id , 174, 800 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;

select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'V'), "CLASS", fee_id , 174, 800 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;
insert into default_fee_assignment_structure select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'V'), "CLASS", fee_id , 174, 800 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;


select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'VI'), "CLASS", fee_id , 174, 850 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;
insert into default_fee_assignment_structure select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'VI'), "CLASS", fee_id , 174, 850 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;

select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'VII'), "CLASS", fee_id , 174, 850 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;
insert into default_fee_assignment_structure select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'VII'), "CLASS", fee_id , 174, 850 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;

select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'VIII'), "CLASS", fee_id , 174, 850 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;
insert into default_fee_assignment_structure select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'VIII'), "CLASS", fee_id , 174, 850 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;



select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'IX'), "CLASS", fee_id , 174, 1000 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;
insert into default_fee_assignment_structure select "c0cb63d8-940d-11ee-83d3-bdccc474f77a", (select standard_id from standards where institute_id=10135 and standard_name = 'IX'), "CLASS", fee_id , 174, 1000 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 169;