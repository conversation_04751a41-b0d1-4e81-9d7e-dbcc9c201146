ALTER TABLE academic_session ADD payroll_start_year int(4) NOT NULL AFTER display_name;
ALTER TABLE academic_session ADD payroll_end_year int(4) NOT NULL AFTER payroll_start_year;
ALTER TABLE academic_session ADD payroll_start_month int(4) NOT NULL AFTER payroll_end_year;
ALTER TABLE academic_session ADD payroll_end_month int(4) NOT NULL AFTER payroll_start_month;
ALTER TABLE academic_session ADD payroll_display_name varchar(64) NULL AFTER payroll_end_month;



-- update academic_session set payroll_start_year = start_year, payroll_end_year = end_year, payroll_start_month = start_month, payroll_end_month = end_month  where institute_id <> 110;

--For local data migration run below query
--update academic_session set payroll_start_year = start_year, payroll_end_year = end_year, payroll_start_month = end_month, payroll_end_month = end_month, payroll_display_name = display_name;