ALTER TABLE homework_view_details DROP FOREIGN KEY homework_view_details_ibfk_1;
ALTER TABLE homework_view_details DROP FOREIGN KEY homework_view_details_ibfk_2;
ALTER TABLE homework_view_details DROP INDEX homework_id;

DROP INDEX leave_type_id ON user_leave_transaction_mapping;

ALTER TABLE homework_view_details ADD CONSTRAINT PRIMARY KEY (homework_id, student_id);
ALTER TABLE homework_view_details ADD CONSTRAINT homework_view_details_ibfk_1 FOREIGN KEY (homework_id) REFERENCES homework_details(homework_id);
ALTER TABLE homework_view_details ADD CONSTRAINT homework_view_details_ibfk_2 FOREIGN KEY (student_id) REFERENCES students(student_id);
