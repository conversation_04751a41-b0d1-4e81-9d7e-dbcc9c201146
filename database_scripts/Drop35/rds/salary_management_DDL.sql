--ALTER TABLE pay_head_configuration ADD pay_head_rule text AFTER pay_head_type;
ALTER TABLE pay_head_configuration ADD system boolean AFTER pay_head_type;
ALTER TABLE pay_head_configuration ADD pay_head_tag varchar(36) AFTER system;
ALTER TABLE pay_head_configuration ADD epf_calc boolean AFTER pay_head_tag;
ALTER TABLE pay_head_configuration ADD esic_calc boolean AFTER epf_calc;
ALTER TABLE pay_head_configuration ADD pro_rata boolean AFTER esic_calc;



CREATE TABLE IF NOT EXISTS salary_cycles(
	institute_id int NOT NULL,
	academic_session_id int NOT NULL,
	cycle_id int NOT NULL PRIMARY KEY AUTO_INCREMENT,
	cycle_start int NOT NULL,
	cycle_end int NOT NULL,
	name varchar(128) NOT NULL,
    unique key(institute_id, academic_session_id, cycle_start),
    unique key(institute_id, academic_session_id, name),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
);


CREATE TABLE IF NOT EXISTS salary_structure_template_metadata(
	institute_id int NOT NULL,
	academic_session_id int NOT NULL,
    structure_id varchar(36) NOT NULL,
    structure_name varchar(1024) NOT NULL,
    status varchar(50) NOT NULL,
    description text,
	primary key(structure_id),
	unique key(institute_id, academic_session_id, structure_name),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
);

CREATE TABLE IF NOT EXISTS salary_structure_template_mapping(
	institute_id int NOT NULL,
	structure_id varchar(36) NOT NULL,
	cycle_id int NOT NULL,
	pay_head_id int NOT NULL,
    amount double NOT NULL,
    primary key(structure_id, cycle_id, pay_head_id),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (structure_id) REFERENCES salary_structure_template_metadata(structure_id),
    FOREIGN KEY (cycle_id) REFERENCES salary_cycles(cycle_id),
    FOREIGN KEY (pay_head_id) REFERENCES pay_head_configuration(pay_head_id)
);

CREATE TABLE IF NOT EXISTS staff_salary_structure_metadata(
	institute_id int NOT NULL,
	academic_session_id int NOT NULL,
    structure_id varchar(36) NOT NULL,
    structure_name varchar(1024) NOT NULL,
    staff_id varchar(36) NOT NULL,
    status varchar(50) NOT NULL,
	primary key(structure_id),
	unique key(institute_id, academic_session_id, staff_id, structure_name),
	INDEX (staff_id),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
    FOREIGN KEY (staff_id) REFERENCES staff_details(staff_id)
);

CREATE TABLE IF NOT EXISTS staff_salary_structure_mapping(
	institute_id int NOT NULL,
	structure_id varchar(36) NOT NULL,
	cycle_id int NOT NULL,
	pay_head_id int NOT NULL,
    amount double NOT NULL,
    primary key(structure_id, cycle_id, pay_head_id),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (structure_id) REFERENCES staff_salary_structure_metadata(structure_id),
    FOREIGN KEY (cycle_id) REFERENCES salary_cycles(cycle_id),
    FOREIGN KEY (pay_head_id) REFERENCES pay_head_configuration(pay_head_id)
);




CREATE TABLE IF NOT EXISTS salary_payslip_metadata(
	institute_id int NOT NULL,
	academic_session_id int NOT NULL,
	payslip_id varchar(36) NOT NULL,
	staff_id varchar(256) NOT NULL,
	cycle_id int NOT NULL,
	status varchar(64) NOT NULL,
	payslip_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	advance_repayment double,
	advance_transaction_id varchar(36),
	total_days double,
	lop_days double,
	total_earnings double,
	total_deductions double,
	description text,
	added_by varchar(36) NOT NULL,
	updated_by varchar(36) NOT NULL,
	added_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata text,
	primary key(payslip_id),
	unique key(institute_id, academic_session_id, cycle_id, staff_id),
	INDEX (staff_id),
	FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
	FOREIGN KEY (staff_id) REFERENCES staff_details(staff_id),
	FOREIGN KEY (cycle_id) REFERENCES salary_cycles(cycle_id)
);

CREATE TABLE IF NOT EXISTS salary_payslip_mapping(
    institute_id int NOT NULL,
	payslip_id varchar(36) NOT NULL,
	pay_head_id int NOT NULL,
	pay_head_type varchar(36) NOT NULL,
	amount double NOT NULL,
	primary key(payslip_id, pay_head_id),
	FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
	FOREIGN KEY (payslip_id) REFERENCES salary_payslip_metadata(payslip_id),
	FOREIGN KEY (pay_head_id) REFERENCES pay_head_configuration(pay_head_id)
);



ALTER TABLE user_leave_transaction_mapping DROP FOREIGN KEY user_leave_transaction_mapping_ibfk_2;
ALTER TABLE user_leave_transaction_mapping DROP FOREIGN KEY user_leave_transaction_mapping_ibfk_3;
ALTER TABLE user_leave_transaction_mapping DROP PRIMARY KEY;
DROP INDEX leave_type_id ON user_leave_transaction_mapping;

ALTER TABLE user_leave_transaction_mapping ADD CONSTRAINT PRIMARY KEY (transaction_id, leave_type_id, start_date);
ALTER TABLE user_leave_transaction_mapping ADD CONSTRAINT user_leave_transaction_mapping_ibfk_2 FOREIGN KEY (transaction_id) REFERENCES user_leave_transaction_metadata(transaction_id);
ALTER TABLE user_leave_transaction_mapping ADD CONSTRAINT user_leave_transaction_mapping_ibfk_3 FOREIGN KEY (leave_type_id) REFERENCES leave_type(leave_type_id);

insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (110, 153, 1688149800, 1690828200, "July 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (110, 153, 1690828200, 1693506600, "August 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (110, 153, 1693506600, 1696098600, "September 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (110, 153, 1696098600, 1698777000, "October 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (110, 153, 1698777000, 1701369000, "November 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (110, 153, 1701369000, 1704047400, "December 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (110, 153, 1704047400, 1706725800, "January 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (110, 153, 1706725800, 1709231400, "February 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (110, 153, 1709231400, 1711909800, "March 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (110, 153, 1711909800, 1714501800, "April 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (110, 153, 1714501800, 1717180200, "May 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (110, 153, 1717180200, 1719772200, "June 2024");


insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10225, 110, 1688149800, 1690828200, "July 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10225, 110, 1690828200, 1693506600, "August 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10225, 110, 1693506600, 1696098600, "September 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10225, 110, 1696098600, 1698777000, "October 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10225, 110, 1698777000, 1701369000, "November 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10225, 110, 1701369000, 1704047400, "December 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10225, 110, 1704047400, 1706725800, "January 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10225, 110, 1706725800, 1709231400, "February 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10225, 110, 1709231400, 1711909800, "March 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10225, 110, 1711909800, 1714501800, "April 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10225, 110, 1714501800, 1717180200, "May 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10225, 110, 1717180200, 1719772200, "June 2024");


insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10226, 129, 1688149800, 1690828200, "July 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10226, 129, 1690828200, 1693506600, "August 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10226, 129, 1693506600, 1696098600, "September 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10226, 129, 1696098600, 1698777000, "October 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10226, 129, 1698777000, 1701369000, "November 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10226, 129, 1701369000, 1704047400, "December 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10226, 129, 1704047400, 1706725800, "January 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10226, 129, 1706725800, 1709231400, "February 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10226, 129, 1709231400, 1711909800, "March 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10226, 129, 1711909800, 1714501800, "April 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10226, 129, 1714501800, 1717180200, "May 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10226, 129, 1717180200, 1719772200, "June 2024");


insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10227, 130, 1688149800, 1690828200, "July 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10227, 130, 1690828200, 1693506600, "August 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10227, 130, 1693506600, 1696098600, "September 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10227, 130, 1696098600, 1698777000, "October 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10227, 130, 1698777000, 1701369000, "November 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10227, 130, 1701369000, 1704047400, "December 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10227, 130, 1704047400, 1706725800, "January 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10227, 130, 1706725800, 1709231400, "February 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10227, 130, 1709231400, 1711909800, "March 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10227, 130, 1711909800, 1714501800, "April 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10227, 130, 1714501800, 1717180200, "May 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10227, 130, 1717180200, 1719772200, "June 2024");

insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10228, 144, 1688149800, 1690828200, "July 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10228, 144, 1690828200, 1693506600, "August 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10228, 144, 1693506600, 1696098600, "September 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10228, 144, 1696098600, 1698777000, "October 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10228, 144, 1698777000, 1701369000, "November 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10228, 144, 1701369000, 1704047400, "December 2023");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10228, 144, 1704047400, 1706725800, "January 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10228, 144, 1706725800, 1709231400, "February 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10228, 144, 1709231400, 1711909800, "March 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10228, 144, 1711909800, 1714501800, "April 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10228, 144, 1714501800, 1717180200, "May 2024");
insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (10228, 144, 1717180200, 1719772200, "June 2024");


--update pay_head_configuration set system = true, pay_head_tag = 'EPF' where institute_id = 110 and pay_head_id = 12;


--ALTER TABLE staff_salary_structure_meta_data ADD CONSTRAINT institute_id_academic_session UNIQUE (institute_id, academic_session_id, staff_id, structure_name);
--ALTER TABLE staff_salary_structure_meta_data ADD CONSTRAINT FK_institute_id FOREIGN KEY (institute_id) REFERENCES institute(institute_id);
---- update historical data to use correct session id before adding this constraint
---- update staff_salary_structure_meta_data set academic_session_id = 92 where institute_id = 700;
--ALTER TABLE staff_salary_structure_meta_data ADD CONSTRAINT FK_academic_session_id FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id);
--
