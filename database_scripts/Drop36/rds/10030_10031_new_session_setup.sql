select * from academic_session where institute_id = 10030;
select * from standard_section_mapping where academic_session_id = 113;
select * from standards_metadata where academic_session_id = 113;
select * from examination_grades where academic_session_id = 113;

select standard_id, 191, section_name from standard_section_mapping where academic_session_id = 113;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 191, section_name from standard_section_mapping where academic_session_id = 113;

select institute_id, 191, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 113;
insert into standards_metadata select institute_id, 191, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 113;

select institute_id, 191, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 113;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 191, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 113;

select * from academic_session where institute_id = 10030;
select * from standard_section_mapping where academic_session_id = 191;
select * from standards_metadata where academic_session_id = 191;
select * from examination_grades where academic_session_id = 191;

--Add Default Exam Structure





select * from academic_session where institute_id = 10031;
select * from standard_section_mapping where academic_session_id = 114;
select * from standards_metadata where academic_session_id = 114;
select * from examination_grades where academic_session_id = 114;


select standard_id, 192, section_name from standard_section_mapping where academic_session_id = 114;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 192, section_name from standard_section_mapping where academic_session_id = 114;

select institute_id, 192, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 114;
insert into standards_metadata select institute_id, 192, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 114;

select institute_id, 192, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 114;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 192, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 114;

select * from academic_session where institute_id = 10031;
select * from standard_section_mapping where academic_session_id = 192;
select * from standards_metadata where academic_session_id = 192;
select * from examination_grades where academic_session_id = 192;

--Add Default Exam Structure