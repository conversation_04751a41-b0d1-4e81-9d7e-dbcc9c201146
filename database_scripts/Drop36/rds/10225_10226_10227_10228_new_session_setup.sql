--10225
select * from academic_session where institute_id = 10225;
select * from standard_section_mapping where academic_session_id = 110;
select * from standards_metadata where academic_session_id = 110;
select * from examination_grades where academic_session_id = 110;

select standard_id, 194, section_name from standard_section_mapping where academic_session_id = 110;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 194, section_name from standard_section_mapping where academic_session_id = 110;

select institute_id, 194, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 110;
insert into standards_metadata select institute_id, 194, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 110;

select institute_id, 194, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 110;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 194, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 110;

select * from academic_session where institute_id = 10225;
select * from standard_section_mapping where academic_session_id = 194;
select * from standards_metadata where academic_session_id = 194;
select * from examination_grades where academic_session_id = 194;


--10226
select * from academic_session where institute_id = 10226;
select * from standard_section_mapping where academic_session_id = 129;
select * from standards_metadata where academic_session_id = 129;
select * from examination_grades where academic_session_id = 129;

select standard_id, 195, section_name from standard_section_mapping where academic_session_id = 129;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 195, section_name from standard_section_mapping where academic_session_id = 129;

select institute_id, 195, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 129;
insert into standards_metadata select institute_id, 195, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 129;

select institute_id, 195, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 129;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 195, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 129;

select * from academic_session where institute_id = 10226;
select * from standard_section_mapping where academic_session_id = 195;
select * from standards_metadata where academic_session_id = 195;
select * from examination_grades where academic_session_id = 195;


--10227
select * from academic_session where institute_id = 10227;
select * from standard_section_mapping where academic_session_id = 130;
select * from standards_metadata where academic_session_id = 130;
select * from examination_grades where academic_session_id = 130;

select standard_id, 196, section_name from standard_section_mapping where academic_session_id = 130;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 196, section_name from standard_section_mapping where academic_session_id = 130;

select institute_id, 196, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 130;
insert into standards_metadata select institute_id, 196, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 130;

select institute_id, 196, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 130;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 196, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 130;

select * from academic_session where institute_id = 10227;
select * from standard_section_mapping where academic_session_id = 196;
select * from standards_metadata where academic_session_id = 196;
select * from examination_grades where academic_session_id = 196;


--10228
select * from academic_session where institute_id = 10228;
select * from standard_section_mapping where academic_session_id = 144;
select * from standards_metadata where academic_session_id = 144;
select * from examination_grades where academic_session_id = 144;

select standard_id, 197, section_name from standard_section_mapping where academic_session_id = 144;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 197, section_name from standard_section_mapping where academic_session_id = 144;

select institute_id, 197, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 144;
insert into standards_metadata select institute_id, 197, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 144;

select institute_id, 197, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 144;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 197, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 144;

select * from academic_session where institute_id = 10228;
select * from standard_section_mapping where academic_session_id = 197;
select * from standards_metadata where academic_session_id = 197;
select * from examination_grades where academic_session_id = 197;

