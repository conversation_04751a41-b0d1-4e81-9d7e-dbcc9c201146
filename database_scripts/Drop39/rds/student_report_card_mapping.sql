-- Step 1: Add the UUID column
ALTER TABLE exam_report_structure ADD COLUMN report_card_id VARCHAR(36) NOT NULL AFTER academic_session_id;

-- Step 2: Populate the UUID column
UPDATE exam_report_structure SET report_card_id = UUID();

-- Step 3: Drop the old composite primary key and Set UUID as the primary key
ALTER TABLE exam_report_structure DROP PRIMARY KEY, ADD PRIMARY KEY (report_card_id);

-- Step 5: Add a unique key constraint on the old columns
ALTER TABLE exam_report_structure ADD UNIQUE KEY unique_key (institute_id, academic_session_id, standard_id, report_type);

CREATE TABLE IF NOT EXISTS student_report_card_mapping (
institute_id int NOT NULL,
academic_session_id int NOT NULL,
report_card_id varchar(36) NOT NULL,
student_id varchar(36) NOT NULL,
status varchar(255) NOT NULL,
UNIQUE KEY (report_card_id, student_id),
FOREI<PERSON><PERSON> KEY (student_id) REFERENCES students(student_id),
<PERSON><PERSON><PERSON><PERSON><PERSON> KEY (report_card_id) REFERENCES exam_report_structure(report_card_id),
FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);
