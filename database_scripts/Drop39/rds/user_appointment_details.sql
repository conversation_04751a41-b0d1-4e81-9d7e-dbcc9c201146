CREATE TABLE IF NOT EXISTS user_appointment_details (
    institute_id INT NOT NULL,
    academic_session_id INT NOT NULL,
    appointment_id VARCHAR(36) NOT NULL PRIMARY KEY,
    raised_by_user_id VARCHAR(36) NOT NULL,
    raised_by_user_type VA<PERSON>HAR(255) NOT NULL,
    guardian_name VA<PERSON><PERSON><PERSON>(1024),
    guardian_contact_info VARCHAR(1024),
    raised_for_user_id VARCHAR(36) NOT NULL,
    raised_for_user_type VARCHAR(255) NOT NULL,
    appointment_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    description TEXT,
    status VARCHAR(100) NOT NULL,
    raised_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    accepted_date TIMESTAMP,
    accepted_by VARCHAR(36),
    accepted_reason VARCHAR(2048),
    rejected_date TIMESTAMP,
    rejected_by VARCHAR(36),
    rejected_reason VARCHAR(2048),
    completed_date TIMES<PERSON>MP,
    completed_by VARC<PERSON><PERSON>(36),
    outcome TEXT
);