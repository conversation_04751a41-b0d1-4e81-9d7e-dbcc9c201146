ALTER TABLE `lernen`.`institute` ADD COLUMN `letter_head_line1` varchar(1023);
ALTER TABLE `lernen`.`institute` ADD COLUMN `letter_head_line2` varchar(1023);

-- 2nd Jan, 2018
ALTER TABLE `lernen`.`students` 
CHANGE COLUMN `status` `status` VARCHAR(255) NOT NULL DEFAULT 'ENROLMENT_PENDING' ;

-- 3rd Jan, 2018
ALTER TABLE `lernen`.`students` 
ADD COLUMN `admission_number` VARCHAR(225) NOT NULL AFTER `registration_number`;

ALTER TABLE `lernen`.`students` 
ADD COLUMN `admission_number` VARCHAR(225) NOT NULL AFTER `registration_number`;

ALTER TABLE `lernen`.`students` 
ADD CONSTRAINT admission_number_unique UNIQUE KEY (institute_id, admission_number);

ALTER TABLE `lernen`.`students` 
ADD COLUMN `primary_contact_number` VARCHAR(500) NULL DEFAULT NULL AFTER `zipcode`,
ADD COLUMN `primary_email` VARCHAR(500) NULL DEFAULT NULL AFTER `primary_contact_number`,
ADD COLUMN `father_aadhar_number` VARCHAR(225) NULL DEFAULT NULL AFTER `mother_contact_number`,
ADD COLUMN `mother_aadhar_number` VARCHAR(225) NULL DEFAULT NULL AFTER `father_aadhar_number`,
ADD COLUMN `religion` VARCHAR(225) NULL DEFAULT NULL AFTER `caste`;



ALTER TABLE `lernen`.`students` 
ADD COLUMN `mother_tongue` VARCHAR(225) NULL DEFAULT NULL AFTER `aadhar_number`,
ADD COLUMN `area_type` VARCHAR(32) NULL DEFAULT NULL AFTER `mother_tongue`,
ADD COLUMN `specially_abled` TINYINT(1) NULL DEFAULT NULL AFTER `area_type`,
ADD COLUMN `bpl` TINYINT(1) NULL DEFAULT NULL AFTER `specially_abled`;

ALTER TABLE `lernen`.`students` 
CHANGE COLUMN `caste` `category` VARCHAR(256) NULL DEFAULT NULL ;