drop table book_tags;
CREATE TABLE IF NOT EXISTS publication(
	institute_id int NOT NULL,
	publication_id varchar(36) PRIMARY KEY NOT NULL,
	publication_name VARCHAR(255) NOT NULL,
    address VARCHAR(500),
    phone_number VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(255),
    Foreign Key (institute_id) REFERENCES institute(institute_id),
    UNIQUE (institute_id, publication_name)
);
CREATE TABLE IF NOT EXISTS publisher (
	institute_id int NOT NULL,
    publisher_id varchar(36) PRIMARY KEY NOT NULL,
    publisher_name VARCHAR(255) NOT NULL,
    contact_information VARCHAR(500),
    affiliation VARCHAR(255),
    Foreign Key (institute_id) REFERENCES institute(institute_id),
    UNIQUE (institute_id, publisher_name)
);
CREATE TABLE IF NOT EXISTS genre (
	entity_id VARCHAR(255) NOT NULL,
    entity_name VA<PERSON>HAR(255) NOT NULL,
    genre_id varchar(36) PRIMARY KEY NOT NULL,
    genre_name VARCHAR(255) NOT NULL,
    classification_number VARCHAR(50),
    UNIQUE (entity_id, genre_name)
    
);
CREATE TABLE IF NOT EXISTS vendor (
	institute_id int NOT NULL,
    vendor_id varchar(36) PRIMARY KEY NOT NULL,
    vendor_name VARCHAR(255) NOT NULL,
    address VARCHAR(500),
    phone_number VARCHAR(20),
    email VARCHAR(255),
    gst_number VARCHAR(20),
    account_type VARCHAR(100),
    bank_name VARCHAR(255),
    account_holder_name VARCHAR(500),
    account_number VARCHAR(100),
    ifsc_code VARCHAR(50),
    Foreign Key (institute_id) REFERENCES institute(institute_id),
    UNIQUE (institute_id, vendor_name)
);
CREATE TABLE IF NOT EXISTS author (
	institute_id int NOT NULL,
    author_id varchar(36) PRIMARY KEY NOT NULL,
    author_name VARCHAR(255) NOT NULL,
    nationality VARCHAR(100),
    date_of_birth TIMESTAMP,
    associated_genres VARCHAR(1024),
    short_biography VARCHAR(2048),
    Foreign Key (institute_id) REFERENCES institute(institute_id),
    UNIQUE (institute_id, author_name)
);
CREATE TABLE IF NOT EXISTS library_type (
	institute_id int NOT NULL,
    library_type_id varchar(36) PRIMARY KEY NOT NULL,
    library_type_name VARCHAR(100) NOT NULL,
    Foreign Key (institute_id) REFERENCES institute(institute_id),
    UNIQUE (institute_id, library_type_name)
);

CREATE TABLE IF NOT EXISTS individual_book_details (
    accession_id VARCHAR(36) PRIMARY KEY, 
    institute_id int NOT NULL,
    book_id VARCHAR(36) NOT NULL, 
    accession_number VARCHAR(50) NOT NULL, 
    rack VARCHAR(100), 
    price DECIMAL(10, 2), 
    bill_number VARCHAR(50), 
    date_of_purchase TIMESTAMP, 
    vendor_id VARCHAR(36),
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE', 
    volume VARCHAR(50), 
    library_type_id VARCHAR(36), 
    remark VARCHAR(500), 
    
	FOREIGN KEY (book_id) REFERENCES book_details(book_id),
    FOREIGN KEY (vendor_id) REFERENCES vendor(vendor_id),
    FOREIGN KEY (library_type_id) REFERENCES library_type(library_type_id),
    Foreign Key (institute_id) REFERENCES institute(institute_id),
    UNIQUE (institute_id, accession_number)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

ALTER TABLE publication CONVERT TO CHARACTER SET latin1 COLLATE latin1_swedish_ci;
ALTER TABLE publisher CONVERT TO CHARACTER SET latin1 COLLATE latin1_swedish_ci;
ALTER TABLE genre CONVERT TO CHARACTER SET latin1 COLLATE latin1_swedish_ci;
ALTER TABLE vendor CONVERT TO CHARACTER SET latin1 COLLATE latin1_swedish_ci;
ALTER TABLE author CONVERT TO CHARACTER SET latin1 COLLATE latin1_swedish_ci;
ALTER TABLE library_type CONVERT TO CHARACTER SET latin1 COLLATE latin1_swedish_ci;


-- Drop columns not in the new schema
ALTER TABLE book_details
DROP COLUMN genre,
DROP COLUMN author,
DROP COLUMN publication,
DROP COLUMN rack,
DROP COLUMN price_per_copy;

ALTER TABLE book_details
ADD book_no VARCHAR(255),
ADD book_tags varchar(1024) CHARACTER SET utf8,
ADD publication_id VARCHAR(36),
ADD publisher_id VARCHAR(36),
ADD genre_id VARCHAR(36),
ADD author_id VARCHAR(36),
ADD type_of_binding VARCHAR(50),
ADD number_of_pages INT;

ALTER TABLE book_details
ADD CONSTRAINT fk_publication_id FOREIGN KEY (publication_id) REFERENCES publication(publication_id),
ADD CONSTRAINT fk_publisher_id FOREIGN KEY (publisher_id) REFERENCES publisher(publisher_id),
ADD CONSTRAINT fk_genre_id FOREIGN KEY (genre_id) REFERENCES genre(genre_id),
ADD CONSTRAINT fk_author_id FOREIGN KEY (author_id) REFERENCES author(author_id);

ALTER TABLE library_ledger
DROP COLUMN book_no;

ALTER TABLE library_ledger
ADD accession_id VARCHAR(36) NOT NULL;

ALTER TABLE library_ledger
ADD CONSTRAINT fk_accession_id FOREIGN KEY (accession_id) REFERENCES individual_book_details(accession_id);

CREATE INDEX idx_book_details_book_tags ON book_details(book_tags);
CREATE INDEX idx_book_details_book_title ON book_details(book_title);
CREATE INDEX idx_book_details_book_no ON book_details(book_no);
CREATE INDEX idx_book_details_isbn_number ON book_details(isbn_number);
CREATE INDEX idx_individual_book_details_accession_number ON individual_book_details(accession_number);
CREATE INDEX idx_library_type_library_type_name ON library_type(library_type_name);
CREATE INDEX idx_author_author_name ON author(author_name);
CREATE INDEX idx_genre_genre_name ON genre(genre_name);
CREATE INDEX idx_genre_classification_number ON genre(classification_number);
CREATE INDEX idx_publisher_publisher_name ON publisher(publisher_name);
CREATE INDEX idx_publication_publication_name ON publication(publication_name);
