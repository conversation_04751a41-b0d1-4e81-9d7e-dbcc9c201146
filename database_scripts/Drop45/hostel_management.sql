CREATE TABLE IF NOT EXISTS hostel_management (
    institute_id INT NOT NULL,
    hostel_id VARCHAR(36) NOT NULL PRIMARY KEY,
    hostel_name VARCHAR(512) NOT NULL,
    address VARCHAR(1024),
    primary_contact_no VARCHAR(256),
    secondary_contact_no VARCHAR(256),
    email_id VARCHAR(256),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);

ALTER TABLE student_academic_session_details
ADD COLUMN hostel_id VARCHAR(36)
CHARACTER SET utf8mb4
COLLATE utf8mb4_0900_ai_ci;



ALTER TABLE student_academic_session_details
ADD CONSTRAINT fk_student_hostel
FOREIGN KEY (hostel_id) REFERENCES hostel_management(hostel_id);
