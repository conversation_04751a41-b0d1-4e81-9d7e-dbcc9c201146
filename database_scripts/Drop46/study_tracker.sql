CREATE TABLE IF NOT EXISTS study_tracker_type(
    institute_id int NOT NULL,
    academic_session_id int NOT NULL,
    type_id varchar(36) PRIMARY KEY,
    type_name varchar(255) NOT NULL,
    type_sequence int,
    Foreign Key (institute_id) REFERENCES institute(institute_id),
    Foreign Key (academic_session_id) REFERENCES academic_session(academic_session_id)
);


CREATE TABLE IF NOT EXISTS study_tracker (
    institute_id INT NOT NULL,
    academic_session_id INT NOT NULL,
    student_id VARCHAR(36) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
    entity_id VARCHAR(36) NOT NULL,
    entity_name VARCHAR(255) NOT NULL,
    type_id VARCHAR(36) NOT NULL,
    value DOUBLE,
    tracker_date TIMESTAMP NOT NULL,
    student_remark VARCHAR(255),
    faculty_remark VARCHAR(255),
    added_by VARCHAR(36) NOT NULL,
    added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(36) NOT NULL,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_study_tracker_entry UNIQUE (institute_id, academic_session_id, student_id, entity_id, entity_name, type_id, tracker_date),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
    FOREIGN KEY (student_id) REFERENCES students(student_id),
    FOREIGN KEY (type_id) REFERENCES study_tracker_type(type_id)
);