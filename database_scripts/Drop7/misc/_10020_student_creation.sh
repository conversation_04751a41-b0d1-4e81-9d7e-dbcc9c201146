sudo java -Dlernen_env={env} -cp dev-tools-1.0.1-SNAPSHOT.jar  com.lernen.cloud.dev.tools.ingest.student.IngestMPPSStudents -f /prefix/10020_students.csv -cs {session}

sudo java -Dlernen_env=prod -cp dev-tools-1.7.6.jar  com.lernen.cloud.dev.tools.ingest.student.IngestMPPSStudents -f /tmp/10020_students.csv -cs 4
sudo java -Dlernen_env=prod -cp dev-tools-1.0.1-SNAPSHOT.jar com.lernen.cloud.dev.tools.ingest.student.IngestMPPSStudents -f /tmp/10020_students_session18-19_12CLASS.csv -cs 5

sudo java -Dlernen_env=local -cp dev-tools-1.0.1-SNAPSHOT.jar com.lernen.cloud.dev.tools.ingest.student.IngestSesomuStudents -f /Users/<USER>/Lernen/lernen-backend/student_data/_10001_students_session_19-20.csv -cs 11
sudo java -Dlernen_env=prod -cp dev-tools-1.0.1-SNAPSHOT.jar com.lernen.cloud.dev.tools.ingest.student.IngestSesomuStudents -f /Users/<USER>/Lernen/lernen-backend/student_data/_10001_students_session_19-20.csv -cs 6

sudo java -Dlernen_env=local -cp dev-tools-1.0.1-SNAPSHOT.jar com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreator10020 -f /Users/<USER>/Lernen/lernen-backend/student_data/10020_transport_students.csv -cs 9

sudo java -Dlernen_env=local -cp dev-tools-1.0.1-SNAPSHOT.jar com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreator10001 -f /Users/<USER>/Lernen/lernen-backend/student_data/_10001_transport_students_19-20.csv -s 11 -i 10001 -h
sudo java -Dlernen_env=prod -cp dev-tools-1.0.1-SNAPSHOT.jar com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreator10001 -f /tmp/_10001_transport_students_19-20.csv -s 6 -i 10001 -h


sudo java -Dlernen_env=local -cp dev-tools-1.0.1-SNAPSHOT.jar com.lernen.cloud.dev.tools.discount.assignment.DiscountAssignmentCreator -f /Users/<USER>/Lernen/lernen-backend/student_data/_10001_discount_assignment_19-20.csv -s 11
