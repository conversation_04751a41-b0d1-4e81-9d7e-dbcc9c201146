INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10020, 2018, 2019, 5, 4);

insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10020', 'meta_data', 'only_student_copy_in_fee_receipts', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10020', 'meta_data', 'institute_name_in_sms', 'Maharana Prata<PERSON> School');

insert into default_fee_assignment_structure(institute_id,structure_name, structure_id,  entity_id, entity_name, fee_id, fee_head_id, amount) 
values(10020,"Admission Fees", "039b202c-e4f5-41f2-83c0-264b78db2c3c", "10020","INSTITUTE", "9ea21868-9175-41d3-8125-6f183183ef54",9,200);

insert into default_fee_assignment_structure(institute_id,structure_name, structure_id,  entity_id, entity_name, fee_id, fee_head_id, amount)
values(10020,"Caution Money", "83d2563a-5363-4e29-9788-2051fa91120f", "10020","INSTITUTE","27852fdc-e251-4d89-a5b2-6fb7cbe2a4df",10,150);

insert into default_fee_assignment_structure (institute_id,structure_name, structure_id,  entity_id, entity_name, fee_id, fee_head_id, amount)
values(10020,"Management Fees", "ca63196e-849e-4021-8626-2b528090f276", "10020","INSTITUTE","9389c37b-003e-4d95-834e-e81c24461be6",15,300);

insert into default_fee_assignment_structure (institute_id,structure_name, structure_id,  entity_id, entity_name, fee_id, fee_head_id, amount)
values(10020,"Registration Fees", "4a9642ef-9782-48e2-923c-a0883882029f", "10020","INSTITUTE","f149fe3c-9370-46d3-9336-cbf44b3d2ff5",16,200);
