CREATE TABLE IF NOT EXISTS default_fee_assignment_structure_meta_data(
institute_id int NOT NULL,
structure_name varchar(1024) NOT NULL, 
structure_id varchar(36) NOT NULL, 
structure_type varchar(256) NOT NULL,
primary key(structure_id),
unique key (institute_id, structure_name)
);

CREATE TABLE IF NOT EXISTS default_fee_assignment_structure(
structure_id varchar(36) NOT NULL, 
entity_id varchar(256) NOT NULL,
entity_name varchar(36) NOT NULL,
fee_id varchar(36) NOT NULL,
fee_head_id int NOT NULL,
amount double,
primary key(structure_id, entity_id, fee_id, fee_head_id),
FOREIGN KEY (structure_id) REFERENCES default_fee_assignment_structure_meta_data(structure_id),
FOREIGN KEY (fee_id) REFERENCES fee_configuration(fee_id),
FOREIGN KEY (fee_head_id) REFERENCES fee_head_configuration(fee_head_id)
);

-- Migration steps
--  rename table default_fee_assignment_structure to default_fee_assignment_structure1;
-- insert into default_fee_assignment_structure_meta_data(institute_id,structure_name,structure_id,structure_type) select institute_id,structure_name,structure_id, 'REGISTRATION' from default_fee_assignment_structure1  group by institute_id, structure_name, structure_id;
-- insert into default_fee_assignment_structure(structure_id, entity_id, entity_name, fee_id, fee_head_id, amount) select structure_id, entity_id, entity_name, fee_id, fee_head_id, amount  from default_fee_assignment_structure1;
