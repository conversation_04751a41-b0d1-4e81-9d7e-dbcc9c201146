ALTER TABLE exam_dimensions DROP INDEX institute_id, ADD UNIQUE KEY `institute_id` (institute_id, dimension_name,evaluation_type);


CREATE TABLE IF NOT EXISTS exam_report_structure (
institute_id int NOT NULL,
academic_session_id int NOT NULL,
standard_id varchar(36) NOT NULL,
report_type varchar(36) NOT NULL,
structure longtext,
PRIMARY KEY (institute_id, academic_session_id, standard_id, report_type),
FOREIGN KEY (standard_id) REFERENCES standards(standard_id),
FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
);

CREATE TABLE IF NOT EXISTS report_card_variables (
institute_id int NOT NULL,
academic_session_id int NOT NULL,
report_type varchar(225) NOT NULL,
student_id varchar(36) NOT NULL,
attended_days double,
working_days double,
remarks text,
PRIMARY KEY (institute_id, academic_session_id, report_type, student_id),
<PERSON>OREIG<PERSON>Y (student_id) REFERENCES students(student_id),
<PERSON><PERSON><PERSON><PERSON><PERSON>EY (academic_session_id) REFERENCES academic_session(academic_session_id),
FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);