CREATE TABLE IF NOT EXISTS attendance_types(
institute_id int NOT NULL,
academic_session_id int NOT NULL,
attendance_type_id int PRIMARY KEY AUTO_INCREMENT NOT NULL, 
name varchar(1024) NOT NULL,
description text,
unique key(institute_id, academic_session_id, name),
FOREIG<PERSON> KEY (institute_id) REFERENCES institute(institute_id),
FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
);

CREATE TABLE IF NOT EXISTS holiday_calendar(
institute_id int NOT NULL,
academic_session_id int NOT NULL,
holiday_type varchar(256),
entity_id varchar(256) NOT NULL,
entity_name varchar(256) NOT NULL,
holiday_date timestamp NOT NULL,
description text,
primary key(institute_id, entity_id, academic_session_id, holiday_type, holiday_date),
FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
FOREI<PERSON><PERSON> KEY (institute_id) REFERENCES institute(institute_id)
);


CREATE TABLE IF NOT EXISTS student_attendance_register(
institute_id int NOT NULL,
student_id varchar(36) NOT NULL,
academic_session_id int NOT NULL,
attendance_type int NOT NULL,	
attendance_date timestamp NOT NULL,
attendance_status varchar(256) NOT NULL,
created_by varchar(256),
updated_by varchar(256),
remarks text,
created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
primary key(student_id, academic_session_id, attendance_type, attendance_date),
FOREIGN KEY (student_id) REFERENCES students(student_id),
FOREIGN KEY (attendance_type) REFERENCES attendance_types(attendance_type_id)	
);

