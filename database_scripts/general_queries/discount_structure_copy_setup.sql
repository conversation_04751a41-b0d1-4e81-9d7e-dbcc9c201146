----- Add new discount structures metadata --------
--insert into fee_discount_structure_metadata values (10020, 69, "d14d20cd-a522-4097-b67c-8f95e902fd68", "3rd child onwards", null);
--insert into fee_discount_structure_metadata values (10020, 69, "ae1d58e3-f0e7-41e3-a7fd-8f88146d7e20", "CAUTION MONEY", null);
--insert into fee_discount_structure_metadata values (10020, 69, "6e4d6c6e-e55b-46ed-8c48-335dc2d05aae", "FINANCIAL WEAK SECTION", null);
--insert into fee_discount_structure_metadata values (10020, 69, "79751ad6-299d-450d-957e-49d3d8f03e96", "RTE CHILD", null);
--insert into fee_discount_structure_metadata values (10020, 69, "dd3daee7-26d4-497c-ae4f-0c9884a7597a", "Staff Discount", null);
--insert into fee_discount_structure_metadata values (10020, 69, "be41da03-db35-4766-a018-c00869ef5c7e", "Staff Discount From May to July", null);
--insert into fee_discount_structure_metadata values (10020, 69, "1b299387-3ba8-47ca-af17-3c137f9efc14", "VERY POOR", null);


----- Create view for fee mapping from past session to new session -----------
create view temp_fee_mapping as select b.academic_session_id as src_session, b.fee_id as src_fee_id, b.fee_name src_fee_name,
a.academic_session_id as dest_session, a.fee_id as dest_fee_id, a.fee_name as dest_fee_name
     from (select * from fee_configuration  where institute_id = 10020 and academic_session_id = 69) a join
    (select * from fee_configuration  where institute_id = 10020 and academic_session_id = 44) b on
     SUBSTRING_INDEX(a.fee_name, " ", 1) = SUBSTRING_INDEX(b.fee_name, " ", 1) and a.fee_type = b.fee_type and
      a.start_month <=> b.start_month and a.end_month <=> b.end_month;

----- Create view for discount structure mapping from past session to new session -----------

create view temp_discount_structure_mapping as select b.structure_name as src_structure_name, b.structure_id as src_structure_id, b.description as src_description,
 a.structure_name as dest_structure_name, a.structure_id as dest_structure_id, a.description as dest_description from (select * from fee_discount_structure_metadata where institute_id = 10020 and academic_session_id = 69) a
join (select * from fee_discount_structure_metadata where institute_id = 10020 and academic_session_id = 44) b on a.structure_name = b.structure_name;


------- Insert new entries in discount structure from past session from above views  ---------

insert into fee_discount_structure select dest_structure_id, entity_id, entity_name, dest_fee_id, fee_head_id, is_percent, amount from
fee_discount_structure a join temp_fee_mapping t on a.fee_id = t.src_fee_id join temp_discount_structure_mapping d on d.src_structure_id = a.structure_id;
