-- Tables to check for student complete delete

-- students
-- student_academic_session_details
-- user_wallet
-- fee_assignment
-- student_fee_payments
-- marks_feeding
-- student_attendance_register

select count(*) from students where institute_id = 10006 and status = 'RELIEVED';

select student_id, status, admission_number  from students where institute_id = 10006 and status = 'RELIEVED';

select user_wallet.* from user_wallet join students on user_wallet.user_id = students.student_id where students.institute_id = 10006;


select fee_assignment.* from fee_assignment join students on fee_assignment.entity_id = students.student_id where students.institute_id = 10006 and status = 'RELIEVED';

select student_fee_payments.* from student_fee_payments join students on student_fee_payments.student_id = students.student_id where students.institute_id = 10006 and status = 'RELIEVED';

select marks_feeding.* from marks_feeding join students on marks_feeding.student_id = students.student_id where students.institute_id = 10006 and status = 'RELIEVED';

select student_attendance_register.* from student_attendance_register join students on student_attendance_register.student_id = students.student_id where students.institute_id = 10006 and status = 'RELIEVED';

