use lernen

--INSERT INTO organisation (organisation_id, name)
--VALUES ("048a25e4-62e6-11ea-bc55-0242ac130003", "A.G. MISSION SCHOOL");

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code) 
VALUES (105,'Rajasthan Public School', 'V/p <PERSON>an<PERSON>', 'Jalore', '', 'Rajasthan', 'India', '', '', '', '<EMAIL>', '8890117618,9928948907', '', '', "46c67d0d-e716-426a-b1ad-67056cc4f5f2");

--update institute set branch_name = "A.G. MISSION SCHOOL", institute_name = "A.G. MISSION SCHOOL", logo_url="" where institute_id = 105;

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(105, 2020, 2021, 7, 6);

insert into fee_category (institute_id, fee_category, description) values (105, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (105, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (105, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (105, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (105, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (105, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (105, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag)
VALUES(105, (SELECT fee_category_id from fee_category where institute_id = 105 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (105,'stationery',0);
insert into categories (institute_id,category_name,genders) values (105,'books',0);
insert into categories (institute_id,category_name) values (105,'clothing');
insert into categories (institute_id,category_name) values (105,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (105,'note book',0,0);
insert into categories (institute_id,category_name) values (105,'art & craft');
insert into categories (institute_id,category_name) values (105,'personal care');
insert into categories (institute_id,category_name) values (105,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (105,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (105,'accessories');
insert into categories (institute_id,category_name) values (105,'furniture');
insert into categories (institute_id,category_name) values (105,'electronics');
insert into categories (institute_id,category_name) values (105,'sports');


insert into colors (institute_id, color_name) values (105,'maroon');
insert into colors (institute_id, color_name) values (105,'black');
insert into colors (institute_id, color_name) values (105,'brown');
insert into colors (institute_id, color_name) values (105,'white');
insert into colors (institute_id, color_name) values (105,'red');
insert into colors (institute_id, color_name) values (105,'yellow');
insert into colors (institute_id, color_name) values (105,'blue');
insert into colors (institute_id, color_name) values (105,'navy blue');
insert into colors (institute_id, color_name) values (105,'green');
insert into colors (institute_id, color_name) values (105,'dark green');
insert into colors (institute_id, color_name) values (105,'pink');
insert into colors (institute_id, color_name) values (105,'purple');
insert into colors (institute_id, color_name) values (105,'grey');
insert into colors (institute_id, color_name) values (105,'olive');
insert into colors (institute_id, color_name) values (105,'cyan');
insert into colors (institute_id, color_name) values (105,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (105, 'FEE_INVOICE', 1, "");
-- insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '105', 'meta_data', 'enable_permission', 'true');

--insert into default_fee_assignment_structure_meta_data values(105, "Installments", "1aed4d2d-2d8e-4741-8b4c-6454126c1701", "ENROLLMENT");
--insert into default_fee_assignment_structure select "1aed4d2d-2d8e-4741-8b4c-6454126c1701", (select standard_id from standards where institute_id=105 and standard_name = '12th' and stream = 'COMMERCE'), "CLASS", fee_id , 80, 6900 from fee_configuration where institute_id = 105 and fee_type = "REGULAR" and academic_session_id = 26;



insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(105, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(105, "Grade", "SYSTEM", "GRADE", 1);