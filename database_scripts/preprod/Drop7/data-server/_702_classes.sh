
INSTITUTE_STANDARDS_URL=$1/2.0/institute/standard

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "LKG",
  "stream" : "NA",
  "level" : 1,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "UKG",
  "stream" : "NA",
  "level" : 2,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "1",
  "stream" : "NA",
  "level" : 3,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "2",
  "stream" : "NA",
  "level" : 4,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "3",
  "stream" : "NA",
  "level" : 5,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "4",
  "stream" : "NA",
  "level" : 6,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "5",
  "stream" : "NA",
  "level" : 7,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "6",
  "stream" : "NA",
  "level" : 8,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "7",
  "stream" : "NA",
  "level" : 9,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "8",
  "stream" : "NA",
  "level" : 10,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "9",
  "stream" : "NA",
  "level" : 11,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "10",
  "stream" : "NA",
  "level" : 12,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "11",
  "stream" : "SCIENCE",
  "level" : 13,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "11",
  "stream" : "COMMERCE",
  "level" : 14,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "11",
  "stream" : "ARTS",
  "level" : 15,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "12",
  "stream" : "SCIENCE",
  "level" : 16,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "12",
  "stream" : "COMMERCE",
  "level" : 17,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "702",
  "standardName": "12",
  "stream" : "ARTS",
  "level" : 18,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL