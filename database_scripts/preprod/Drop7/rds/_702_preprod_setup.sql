INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number) 
VALUES (702,'Lernen Test 2', 'Station Road, Opp. Govt Hospital', 'Ratangarh', 'Churu', 'Rajasthan', 'India', '331803', null, '/static/core/images/raghunath_logo.png', '<EMAIL>', '1234567890');

update institute set letter_head_line1 = "(Co-Educational English-Hindi Medium School)" , letter_head_line2 = "Ratangarh-Churu-(Raj)" where institute_id = 702;

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(702, 2018, 2019, 5, 4);
INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(702, 2019, 2020, 5, 4);

insert into fee_category (institute_id, fee_category, description) values (702, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (702, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (702, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (702, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (702, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (702, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (702, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(702, (SELECT fee_category_id from fee_category where institute_id = 702 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');

insert into counters (institute_id, counter_type, count, counter_prefix) values (702, 'FEE_INVOICE', 1, "");

insert into categories (institute_id,category_name,genders) values (702,'stationery',0);
insert into categories (institute_id,category_name,genders) values (702,'books',0);
insert into categories (institute_id,category_name) values (702,'clothing');
insert into categories (institute_id,category_name) values (702,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (702,'note book',0,0);
insert into categories (institute_id,category_name) values (702,'art & craft');
insert into categories (institute_id,category_name) values (702,'personal care');
insert into categories (institute_id,category_name) values (702,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (702,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (702,'accessories');



insert into colors (institute_id, color_name) values (702,'maroon');
insert into colors (institute_id, color_name) values (702,'black');
insert into colors (institute_id, color_name) values (702,'brown');
insert into colors (institute_id, color_name) values (702,'white');
insert into colors (institute_id, color_name) values (702,'red');
insert into colors (institute_id, color_name) values (702,'yellow');
insert into colors (institute_id, color_name) values (702,'blue');
insert into colors (institute_id, color_name) values (702,'navy blue');
insert into colors (institute_id, color_name) values (702,'green');
insert into colors (institute_id, color_name) values (702,'dark green');
insert into colors (institute_id, color_name) values (702,'pink');
insert into colors (institute_id, color_name) values (702,'purple');
insert into colors (institute_id, color_name) values (702,'grey');
insert into colors (institute_id, color_name) values (702,'olive');
insert into colors (institute_id, color_name) values (702,'cyan');
insert into colors (institute_id, color_name) values (702,'magenta');




insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(702, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(702, "Grade", "SYSTEM", "GRADE", 1);


//
//-- Testing counter config --
//
//insert into counters (institute_id, counter_type, count, counter_prefix) values (702, 'REGISTRATION_NUMBER', 1, "");
//insert into counters (institute_id, counter_type, count, counter_prefix) values (702, 'ADMISSION_NUMBER', 1, "");
//
//
//insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '702', 'meta_data', 'registration_counter', 'true');
//insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '702', 'meta_data', 'admission_counter', 'true');


