package com.embrate.cloud.dev.tools.communication.handler;

import com.embrate.cloud.voicecall.report.handler.VoiceStatusUpdateHandler;
import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.ParseException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;

import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;
import com.lernen.cloud.sms.report.handler.SMSStatusUpdateHandler;

/**
 * 
 * https://hostadvice.com/how-to/how-to-setup-a-cron-job-on-ubuntu-18-04/
 * 
 * /etc/crontab
 * 
 * backslash after * is to escape forward slash. cron cmd :
 * 
 * *\/2 * * * * root java -Dlernen_env=qa -cp
 * /opt/embrate/cron/dev-tools-1.0.1-SNAPSHOT.jar
 * com.embrate.cloud.dev.tools.communication.handler.CommunicationStatusUpdater -pd 3
 * 
 * 
 * <AUTHOR>
 *
 */
public class CommunicationStatusUpdater {

	private static final Logger logger = LogManager.getLogger(CommunicationStatusUpdater.class);
	private static final String PAST_LOOKUP_DAYS = "pd";
	private final SMSStatusUpdateHandler smsStatusUpdateHandler;
	private final VoiceStatusUpdateHandler voiceStatusUpdateHandler;

	public CommunicationStatusUpdater(SMSStatusUpdateHandler smsStatusUpdateHandler, VoiceStatusUpdateHandler voiceStatusUpdateHandler) {
		this.smsStatusUpdateHandler = smsStatusUpdateHandler;
		this.voiceStatusUpdateHandler = voiceStatusUpdateHandler;
	}

	public static void main(String[] args) {
		final ApplicationContext ctx = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
		final SMSStatusUpdateHandler smsStatusUpdateHandler = ctx.getBean(SMSStatusUpdateHandler.class);
		final VoiceStatusUpdateHandler voiceStatusUpdateHandler = ctx.getBean(VoiceStatusUpdateHandler.class);
		final CommunicationStatusUpdater communicationStatusUpdater = new CommunicationStatusUpdater(smsStatusUpdateHandler, voiceStatusUpdateHandler);

		final Options options = buildOptions();

		CommandLine cmdLine = null;
		try {
			final CommandLineParser parser = new DefaultParser();
			cmdLine = parser.parse(options, args);
		} catch (final ParseException pex) {
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("SMSStatusUpdater", options);
			System.exit(2);
		}

		if (!cmdLine.hasOption(PAST_LOOKUP_DAYS)) {
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("Past lookup days is requried", options);
		}

		int pastLookupDays = Integer.parseInt(cmdLine.getOptionValue(PAST_LOOKUP_DAYS));

		logger.info("Running smsStatusUpdateHandlerfor past days {}, now at {}", pastLookupDays, DateUtils.now());
		communicationStatusUpdater.runSMSStatusUpdater(pastLookupDays);
		logger.info("Done updating SMS status at time {}", DateUtils.now());

		logger.info("Running voiceStatusUpdateHandler past days {}, now at {}", pastLookupDays, DateUtils.now());
		communicationStatusUpdater.runVoiceStatusUpdater(pastLookupDays);
		logger.info("Done updating Voice call status at time {}", DateUtils.now());
	}

	private void runSMSStatusUpdater(int pastLookupDays) {
		smsStatusUpdateHandler.updateSMSStatus(pastLookupDays);
	}
	private void runVoiceStatusUpdater(int pastLookupDays) {
		voiceStatusUpdateHandler.updateVoiceCallStatus(pastLookupDays);
	}

	private static Options buildOptions() {
		final Options options = new Options();
		options.addOption(new Option(PAST_LOOKUP_DAYS, true, "Past Lookup Days"));
		return options;
	}

}
