package com.embrate.cloud.dev.tools.fees.payment;

import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.fees.payment.*;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;
import org.apache.commons.cli.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.FileReader;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 *
 * <AUTHOR>
 * Header
 * Admission Number*,Transaction Mode *,Transaction Date * (dd-MMMM-yyyy),Fee Name * (Single Fee in one row),Fee Head Name * (Single Fee Head in one row),Paid Amount,Fine Amount,Instant Discount,Wallet Amount,Remarks,Reference
 */
public class FeePaymentTransactionCreator {

    private static final Logger logger = LogManager.getLogger(FeePaymentTransactionCreator.class);

    private static final String INSTITUTE_ID = "i";
    private static final String ACADEMIC_SESSION_ID = "s";
    private static final String USER = "user";
    private static final String FILE_PATH = "f";
    private static final String FILE_HEADER = "h";
    private static final String UPDATE = "u";
    private static final String FILE_DELIMITER = "\\|";

    private final StudentManager studentManager;
    private final FeePaymentManager feePaymentManager;

    public FeePaymentTransactionCreator(StudentManager studentManager, FeePaymentManager feePaymentManager) {
        this.studentManager = studentManager;
        this.feePaymentManager = feePaymentManager;
    }

    public static void main(String args[]) {
        final ApplicationContext context = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
        final StudentManager studentManager = context.getBean(StudentManager.class);
        final FeePaymentManager feePaymentManager = context.getBean(FeePaymentManager.class);
        FeePaymentTransactionCreator feePaymentTransactionCreator = new FeePaymentTransactionCreator(
                studentManager, feePaymentManager);
        Options options = buildOptions();
        CommandLine cmdLine = null;
        try {
            final CommandLineParser parser = new DefaultParser();
            cmdLine = parser.parse(options, args);
        } catch (final Exception pex) {
            System.err.print("Unable to parse arguments.");
            pex.printStackTrace();
            final HelpFormatter formatter = new HelpFormatter();
            formatter.printHelp("FeePaymentTransactionCreator ", options);
            return;
        }

        if (cmdLine == null) {
            logger.error("Unable to parse arguments from cmdline.");
            return;
        }

        int instituteId = 0;
        if (cmdLine.hasOption(INSTITUTE_ID)) {
            instituteId = Integer.parseInt(cmdLine.getOptionValue(INSTITUTE_ID));
        } else {
            logger.error("No institute id passed. Exitting");
            return;
        }
        int academicSessionId = 0;
        if (cmdLine.hasOption(ACADEMIC_SESSION_ID)) {
            academicSessionId = Integer.parseInt(cmdLine.getOptionValue(ACADEMIC_SESSION_ID));
        } else {
            logger.error("No academicSessionId passed. Exitting");
            return;
        }

        UUID userId = null;
        if (cmdLine.hasOption(USER)) {
            userId = UUID.fromString(cmdLine.getOptionValue(USER));
        } else {
            logger.error("No userId passed. Exitting");
            return;
        }

        String filePath = null;
        if (cmdLine.hasOption(FILE_PATH)) {
            filePath = cmdLine.getOptionValue(FILE_PATH);
        } else {
            logger.error("No file path passed. Exitting");
            return;
        }
        boolean fileHeader = false;
        if (cmdLine.hasOption(FILE_HEADER)) {
            fileHeader = true;
        }
        boolean updatePayment = false;
        if (cmdLine.hasOption(UPDATE)) {
            updatePayment = true;
        }
        logger.info("Running for institute = " + instituteId + " , session = " + academicSessionId + ", header = "
                + fileHeader + " , update2 = " + updatePayment);
        feePaymentTransactionCreator.run(instituteId, academicSessionId, filePath, fileHeader, updatePayment,
                userId);
    }

    public void run(int instituteId, int academicSessionId, String filePath, boolean fileHeader, boolean updatePayment,
                    UUID userId) {
        List<FeePaymentTransactionCreator.FileData> fileDataList = readPaymentFile(filePath, fileHeader);
        logger.info("Total records = {}", fileDataList.size());
        int success = 0;
        int failure = 0;
        int noTransactions = 0;
        int count = 1;

        Map<String, Student> studentMap = new HashMap<>();
        List<Student> enrolledStudents = studentManager.getStudentsInAcademicSession(instituteId, academicSessionId,
                Arrays.asList(StudentStatus.ENROLLED, StudentStatus.ENROLMENT_PENDING, StudentStatus.NSO, StudentStatus.RELIEVED));
        for (Student studentResponse : enrolledStudents) {
            studentMap.put(studentResponse.getStudentBasicInfo().getAdmissionNumber().toLowerCase(), studentResponse);
        }

        for (FeePaymentTransactionCreator.FileData fileData : fileDataList) {
            try {
                logger.info("{} Adding payment for entry {}", count++, fileData);

                Student student = studentMap.get(fileData.getAdmissionNumber().toLowerCase());
                if (student == null) {
                    logger.error("Student not found for entry {}", fileData);
                    failure++;
                    continue;
                }

                Integer transactionDate = fileData.getTransactionDate();
                if (transactionDate == null || transactionDate <= 0) {
                    logger.error("Invalid transaction Date {}", fileData);
                    failure++;
                    continue;
                }

                TransactionMode transactionMode = fileData.getTransactionMode();
                if (transactionMode == null) {
                    logger.error("Invalid transaction Mode {}", fileData);
                    failure++;
                    continue;
                }

                UUID feeId = fileData.getFeeId();
                if (feeId == null) {
                    logger.error("Invalid fee Id {}", fileData);
                    failure++;
                    continue;
                }

                int feeHeadId = fileData.getFeeHeadId();
                if (feeHeadId <= 0) {
                    logger.error("Invalid fee Head Id {}", fileData);
                    failure++;
                    continue;
                }

                double paidAmount = fileData.getPaidAmount();

                double fineAmount = fileData.getFineAmount();

                double instantDiscountAmount = fileData.getInstantDiscountAmount();

                double walletAmount = fileData.getWalletAmount();

                String reference = fileData.getReference();

                String remarks = fileData.getRemarks();

                List<FeeIdFeeHeadTransaction> feeIdFeeHeadTransactionList = new ArrayList<FeeIdFeeHeadTransaction>();

                FeeHeadTransactionAmounts feeHeadTransactionAmounts = new FeeHeadTransactionAmounts(
                        feeHeadId, paidAmount, instantDiscountAmount, fineAmount);

                feeIdFeeHeadTransactionList
                        .add(new FeeIdFeeHeadTransaction(feeId, Arrays.asList(feeHeadTransactionAmounts)));


                if (CollectionUtils.isEmpty(feeIdFeeHeadTransactionList)) {
                    logger.info("No fees transaction for student {}", student.getStudentId());
                    noTransactions++;
                    continue;
                }

                FeePaymentTransactionMetaData feePaymentTransactionMetaData = new FeePaymentTransactionMetaData();
                feePaymentTransactionMetaData.setInstituteId(instituteId);
                feePaymentTransactionMetaData.setAcademicSessionId(academicSessionId);
                feePaymentTransactionMetaData.setStudentId(student.getStudentId());
                feePaymentTransactionMetaData.setFeePaymentTransactionStatus(FeePaymentTransactionStatus.ACTIVE);
                feePaymentTransactionMetaData.setTransactionDate(transactionDate);
                feePaymentTransactionMetaData.setTransactionMode(transactionMode);
                feePaymentTransactionMetaData.setTransactionReference(reference);
                feePaymentTransactionMetaData.setRemark(remarks);

                FeePaymentPayload feePaymentPayload = new FeePaymentPayload(feePaymentTransactionMetaData,
                        feeIdFeeHeadTransactionList);
                logger.info("Fee payment payload {}", feePaymentPayload);

                if (!updatePayment) {
                    logger.info("Skipping update Fee payment payload {}", feePaymentPayload);
                    continue;
                }
                FeePaymentResponse feePaymentResponse = feePaymentManager.addFeePayment(feePaymentPayload, false,
                        userId, true);
                if (feePaymentResponse == null) {
                    logger.error("Unable to add payment for student {} ", student.getStudentId());
                    failure++;
                } else {
                    logger.info("Successfully added payment for student {} ", student.getStudentId());
                    success++;
                }
            } catch (Exception e) {
                logger.error("Error while adding payment for entry {}", fileData, e);
            }
        }
        logger.info("Total payment entries {}, success {}, failure {}, no transactions {}", count - 1,
                success, failure, noTransactions);

    }

    private List<FeePaymentTransactionCreator.FileData> readPaymentFile(String fileName, boolean header) {
        List<FeePaymentTransactionCreator.FileData> fileDataList = new ArrayList<>();
        try (final BufferedReader br = new BufferedReader(new FileReader(fileName))) {
            String line = null;
            while ((line = br.readLine()) != null) {
                if (header) {
                    header = false;
                    continue;
                }
                if (StringUtils.isBlank(line)) {
                    continue;
                }
                String[] columns = line.split(FILE_DELIMITER, 11);
                if (columns.length < 11) {
                    logger.error("Columns are not " + 11 + ". Skipping it as its not for payment assignment. Entry = " + line);
                    continue;
                }

                String admissionNumber = columns[0].trim();

                String transactionModeStr = columns[1].trim();
                TransactionMode transactionMode = null;
                if (!StringUtils.isEmpty(transactionModeStr)) {
                    transactionMode = TransactionMode.getTransactionMode(transactionModeStr);
                }

                String transactionDateStr = columns[2].trim();
                int transactionDate = 0;
                if (!StringUtils.isEmpty(transactionDateStr)) {
                    SimpleDateFormat df = new SimpleDateFormat("dd/MM/yyyy");
                    Date date = df.parse(transactionDateStr);
                    transactionDate = (int) (date.getTime() / 1000l);
                }

                String feeIdStr = columns[3].trim();
                UUID feeId = null;
                if (!StringUtils.isEmpty(feeIdStr)) {
                    feeId = UUID.fromString(feeIdStr);
                }

                String feeHeadIdStr = columns[4].trim();
                int feeHeadId = 0;
                if (!StringUtils.isEmpty(feeHeadIdStr)) {
                    feeHeadId = Integer.parseInt(feeHeadIdStr);
                }

                String paidAmountStr = columns[5].trim();
                double paidAmount = 0;
                if (!StringUtils.isEmpty(paidAmountStr)) {
                    paidAmount = Double.parseDouble(paidAmountStr);
                }

                String fineAmountStr = columns[6].trim();
                double fineAmount = 0;
                if (!StringUtils.isEmpty(fineAmountStr)) {
                    fineAmount = Double.parseDouble(fineAmountStr);
                }

                String instantDiscountAmountStr = columns[7].trim();
                double instantDiscountAmount = 0;
                if (!StringUtils.isEmpty(instantDiscountAmountStr)) {
                    instantDiscountAmount = Double.parseDouble(instantDiscountAmountStr);
                }

                String walletAmountStr = columns[8].trim();
                double walletAmount = 0;
                if (!StringUtils.isEmpty(walletAmountStr)) {
                    walletAmount = Double.parseDouble(walletAmountStr);
                }

                String remarks = columns[9].trim();

                String reference = columns[10].trim();

                fileDataList.add(new FeePaymentTransactionCreator.FileData(admissionNumber, transactionMode, transactionDate,
                        feeId, feeHeadId, paidAmount, fineAmount, instantDiscountAmount, walletAmount, remarks, reference));
            }
            return fileDataList;
        } catch (final Exception e) {
            e.printStackTrace();
            logger.error("Error while reading file", e);
        }
        return null;
    }

    private static Options buildOptions() {
        final Options options = new Options();
        options.addOption(INSTITUTE_ID, true, "specify the institute id");
        options.addOption(ACADEMIC_SESSION_ID, true, "Academic session id for institute");
        options.addOption(FILE_PATH, true, "Fee Payment Transaction assignment csv file path");
        options.addOption(FILE_HEADER, false, "File has header");
        options.addOption(UPDATE, false, "Create transaction in DB");
        options.addOption(USER, true, "User id for transaction");
        return options;
    }

    private class FileData {

        private final String admissionNumber;

        private final TransactionMode transactionMode;

        private final int transactionDate;

        private final UUID feeId;

        private final int feeHeadId;

        private final double paidAmount;

        private final double fineAmount;

        private final double instantDiscountAmount;

        private final double walletAmount;

        private final String remarks;

        private final String reference;

        public FileData(String admissionNumber, TransactionMode transactionMode, int transactionDate, UUID feeId, int feeHeadId, double paidAmount, double fineAmount, double instantDiscountAmount, double walletAmount, String remarks, String reference) {
            this.admissionNumber = admissionNumber;
            this.transactionMode = transactionMode;
            this.transactionDate = transactionDate;
            this.feeId = feeId;
            this.feeHeadId = feeHeadId;
            this.paidAmount = paidAmount;
            this.fineAmount = fineAmount;
            this.instantDiscountAmount = instantDiscountAmount;
            this.walletAmount = walletAmount;
            this.remarks = remarks;
            this.reference = reference;
        }

        public String getAdmissionNumber() {
            return admissionNumber;
        }

        public TransactionMode getTransactionMode() {
            return transactionMode;
        }

        public int getTransactionDate() {
            return transactionDate;
        }

        public UUID getFeeId() {
            return feeId;
        }

        public int getFeeHeadId() {
            return feeHeadId;
        }

        public double getPaidAmount() {
            return paidAmount;
        }

        public double getFineAmount() {
            return fineAmount;
        }

        public double getInstantDiscountAmount() {
            return instantDiscountAmount;
        }

        public double getWalletAmount() {
            return walletAmount;
        }

        public String getRemarks() {
            return remarks;
        }

        public String getReference() {
            return reference;
        }

        @Override
        public String toString() {
            return "FileData{" +
                    "admissionNumber='" + admissionNumber + '\'' +
                    ", transactionMode=" + transactionMode +
                    ", transactionDate=" + transactionDate +
                    ", feeId=" + feeId +
                    ", feeHeadId=" + feeHeadId +
                    ", paidAmount=" + paidAmount +
                    ", fineAmount=" + fineAmount +
                    ", instantDiscountAmount=" + instantDiscountAmount +
                    ", walletAmount=" + walletAmount +
                    ", remarks='" + remarks + '\'' +
                    ", reference=" + reference +
                    '}';
        }
    }
}
