/**
 * 
 */
package com.embrate.cloud.dev.tools.ingest.user;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Options;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;

import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserDesignation;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.student.StudentAdmissionManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;

/**
 * <AUTHOR>
 *
 */
public class IngestUsers10065 {
	private static final Logger logger = LogManager.getLogger(StudentAdmissionManager.class);

	private static final String INSTITUTE_ID = "i";
	private static final String ACADEMIC_SESSION_ID = "cs";
	private static final String USER_ID = "ui";
	private static final String STANDRAD_ID = "si";
	private static final String FILE_PATH = "/tmp/user_details.csv";
	private static final String USERNAME_PREFIX = "@bfs65";

	private final StudentManager studentManager;
	private final UserManager userManager;

	public IngestUsers10065(StudentManager studentManager, UserManager userManager) {
		this.studentManager = studentManager;
		this.userManager = userManager;
	}

	public static void main(String args[]) {
		final ApplicationContext context = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
		final IngestUsers10065 ingestUsers10065 = context.getBean(IngestUsers10065.class);
		Options options = buildOptions();
		CommandLine cmdLine = null;
		try {
			final CommandLineParser parser = new DefaultParser();
			cmdLine = parser.parse(options, args);
		} catch (final Exception pex) {
			logger.error("Unable to parse arguments.");
			pex.printStackTrace();
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("UserCreation ", options);
			return;
		}

		if (cmdLine == null) {
			logger.error("Unable to parse arguments from cmdline.");
			return;
		}

		int instituteId = 0;
		if (cmdLine.hasOption(INSTITUTE_ID)) {
			instituteId = Integer.parseInt(cmdLine.getOptionValue(INSTITUTE_ID));
		} else {
			logger.error("No institute id passed. Exitting");
			return;
		}
		int academicSessionId = 0;
		if (cmdLine.hasOption(ACADEMIC_SESSION_ID)) {
			academicSessionId = Integer.parseInt(cmdLine.getOptionValue(ACADEMIC_SESSION_ID));
		} else {
			logger.error("No academicSessionId passed. Exitting");
			return;
		}
		
		UUID userId = null;
		if (cmdLine.hasOption(USER_ID)) {
			 userId = UUID.fromString(cmdLine.getOptionValue(USER_ID));
		} else {
			logger.error("No userId passed. Exitting");
			return;
		}
		
		Set<UUID> standardIds = new HashSet<UUID>();
		if (cmdLine.hasOption(STANDRAD_ID)) {
			logger.info("User creation for specific standrads.");
			String[] standardIdsArr = cmdLine.getOptionValue(STANDRAD_ID).split(",");
			logger.info(standardIdsArr.length + " classes user needs to be created.");
			 for(int i = 0; i < standardIdsArr.length; i++) {
				 logger.info(standardIdsArr[i] + " users has to be created.");
				 standardIds.add(UUID.fromString(standardIdsArr[i].trim()));
				 logger.info(standardIdsArr[i] + " added to list.");
			 }
		} else {
			logger.info("User creation for all standrads.");
			standardIds = null;
		}

		try {
			ingestUsers10065.run(instituteId, academicSessionId, userId, standardIds);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	private void run(int instituteId, int academicSessionId, UUID userId, Set<UUID> standardIds) throws IOException {
		
		List<Student> instituteStudents = new ArrayList<Student>();
		if(standardIds == null || standardIds.size() <= 0) {
			instituteStudents = studentManager.getStudentsInAcademicSession(instituteId,
					academicSessionId);
		} else {
			instituteStudents = studentManager.getClassStudents(instituteId, academicSessionId, 
					standardIds);
		}
		
		System.out.println("instituteStudents.size() - " + instituteStudents.size());
		
        List<String[]> userData = new ArrayList<String[]>();
        userData.add(new String [] {"uuid", "admissionNumber", "registrationNumber", "name", "mobileNumber", "userName","password"});
		logger.info("Total students for institute {} = {}", instituteId, instituteStudents.size());
		int successCount = 0;
		int failureCount = 0;
		int exceptionCount = 0;
		int count = 0;
		for (Student student : instituteStudents) {
			logger.info("Count {}", ++count);
			try {
				logger.info("Creating users for student {} of class {}", student.getStudentId(),
						student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayName());
				
				String userName = student.getStudentBasicInfo().getAdmissionNumber().toLowerCase().replaceAll("\\s+","") + USERNAME_PREFIX;
				String password = RandomStringUtils.randomAlphanumeric(6).toLowerCase();
				List<Module> authorizedModules = new ArrayList<Module>();
				
				User user = new User(student.getStudentId(), student.getStudentBasicInfo().getAdmissionNumber(), instituteId, 
						student.getStudentBasicInfo().getName(), null, null, null, UserType.STUDENT, null, null, UserStatus.ENABLED, 
						userName, password, null, null, null, null, null, null, null, null, UserDesignation.STUDENT, null, null, null, 
						null, null, authorizedModules, null, null, null, null, false);
				
				if (userManager.createUser(user, userId) != null) {
			        
					userData.add(new String [] {student.getStudentId().toString(), student.getStudentBasicInfo().getAdmissionNumber(), 
			        		student.getStudentBasicInfo().getRegistrationNumber(), student.getStudentBasicInfo().getName(), 
			        		student.getStudentBasicInfo().getPrimaryContactNumber(), userName, password });
					
					logger.info("{} successfully created", student.getStudentId());
					successCount++;
					continue;
				}
				logger.error("{} failed in creation", student.getStudentId());
				failureCount++;
			} catch (Exception e) {
				logger.error("Error while creating user {}", student.getStudentId(), e);
				exceptionCount++;
			}
		}
		logger.info("Total success users for institute {} = {}", instituteId, successCount);
		logger.info("Total failure users for institute {} = {}", instituteId, failureCount);
		logger.info("Total exception users for institute {} = {}", instituteId, exceptionCount);
		
		createCSVofUserNamePassword(instituteId, userData);
	}
	
	private void createCSVofUserNamePassword(int instituteId, List<String[]> userData) throws IOException {
		File file = new File(FILE_PATH); 
		try {
			if (!file.exists()) {
				file.createNewFile();
			}
			FileWriter outputfile = new FileWriter(file);
		    BufferedWriter writer = new BufferedWriter(outputfile); 
		    for(String[] line : userData) {
		    	for(int i = 0; i < line.length; i++) {
		    		if(i == line.length - 1) {
		    			writer.write(line[i]);
		    		} else {
		    			writer.write(line[i] + "|");
		    		}
		    	}
		    	writer.newLine();
		     }
		     writer.close(); 
		  
		} catch (java.io.IOException e) {
			e.printStackTrace();
			logger.error("Error while inserting into csv for instittue {}", instituteId, e);
		}	
	}

	private static Options buildOptions() {
		final Options options = new Options();
		options.addOption(INSTITUTE_ID, true, "specify the institute id");
		options.addOption(ACADEMIC_SESSION_ID, true, "Current academic session id for institute");
		options.addOption(USER_ID, true, "user id of user for institute");
		options.addOption(STANDRAD_ID, true, "Standrad ids for institute");
		return options;
	}
}
