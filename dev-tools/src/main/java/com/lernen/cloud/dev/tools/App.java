package com.lernen.cloud.dev.tools;

import com.lernen.cloud.core.api.common.NameFormat;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.NameFormatUtils;
import com.lernen.cloud.core.utils.PasswordUtils;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;

import java.io.StringWriter;

/**
 * Hello world!
 *
 */
public class App {
	public static void main(String[] args) {

		int date = DateUtils.getTimestampFromDate("18-04-2023", DateUtils.DEFAULT_TIMEZONE, "dd-MM-yyyy");
		System.out.println(date);

//		String s = "24/340@sss77";
//		if (s.contains("/")) {
//			String[] tokens = s.split("/");
//			String finalUserName = "s";
//			for (String token : tokens) {
//				finalUserName += token.trim().toLowerCase();
//			}
//			s = finalUserName;
//		}
//		System.out.println(s);
//		String password = PasswordUtils.generateNumericPassword(6).toLowerCase();
//		System.out.println(password);
//
////    	final ApplicationContext context = new ClassPathXmlApplicationContext("dev-tools.xml");
////		final StudentPromoter studentPromoter = context.getBean(StudentPromoter.class);
//
////		Set<UUID> standardIds = new HashSet<UUID>();
////		standardIds.add(UUID.fromString("00a137b8-68e3-4004-81f1-8abf39354860"));
////		standardIds.add(UUID.fromString("a7030783-72fd-4a85-9462-2f730a4daeb0"));
////		standardIds.add(UUID.fromString("3d974988-2f39-403e-a0f8-fd83241a959d"));
////		studentPromoter.run(10030, 10, 24, true, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"), standardIds);
//
//
//		Velocity.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
//		Velocity.setProperty("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
//		Velocity.setProperty("runtime.log.logsystem.log4j.logger", "org.apache.velocity.runtime.log.Log4JLogChute");
//		Velocity.setProperty("runtime.references.strict",true);
//		Velocity.init();
//
//		final VelocityContext context = new VelocityContext();
//		context.put("institute_name", "bal");
//		context.put("name3", "");
//		context.put("name2", "");
//
//		String template = "hello ${name} , kye ah ${institute_name}";
//		final StringWriter smsContent = new StringWriter();
//		Velocity.evaluate(context, smsContent, template, template);
//		System.out.println(smsContent);

	}
}
