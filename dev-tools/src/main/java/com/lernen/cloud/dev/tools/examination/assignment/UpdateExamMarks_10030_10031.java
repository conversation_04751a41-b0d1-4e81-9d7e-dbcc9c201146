package com.lernen.cloud.dev.tools.examination.assignment;

import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.examination.*;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.embrate.cloud.core.api.institute.StandardMetadata;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.examination.ExaminationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;
import org.apache.commons.cli.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.*;

/**
 *
 * <AUTHOR>
 *
 * Sample data -
 * ,English,Maths
 * P-6370,17,19
 * P-6410,18,16
 * P-6404,9,17
 *
 *
 *  sudo java -Dlernen_env=local -cp dev-tools-1.0.1-SNAPSHOT.jar com.lernen.cloud.dev.tools.examination.assignment.UpdateExamMarks_10030_10031 -f /Users/<USER>/Desktop/student_sample_data.csv -um true -i 10030 -cs 10 -std a7030783-72fd-4a85-9462-2f730a4daeb0 -sec 5 -e 6a2c7a7c-2be4-4ed0-b4a6-a5ef9b6c1235 -d 5 -u 4cb09c60-eabc-4192-a9d4-e357cf773db3
 */
public class UpdateExamMarks_10030_10031 {

    private static final Logger logger = LogManager.getLogger(UpdateExamMarks_10030_10031.class);

    private static final String INSTITUTE_ID = "i";
    private static final String ACADEMIC_SESSION_ID = "cs";
    private static final String STANDARD_ID = "std";
    private static final String SECTION_ID = "sec";
    private static final String EXAM_ID = "e";
    private static final String DIMENSION_ID = "d";
    private static final String USER_ID = "u";

    private static final String FILE_PATH = "f";
    private static final String UPDATE = "um";
    private static final String FILE_DELIMITER = ",";

    private final ExaminationManager examinationManager;
    private final StudentManager studentManager;
    private final InstituteManager instituteManager;

    public UpdateExamMarks_10030_10031(ExaminationManager examinationManager, StudentManager studentManager,
                           InstituteManager instituteManager) {
        this.examinationManager = examinationManager;
        this.studentManager = studentManager;
        this.instituteManager = instituteManager;
    }

    private static Options buildOptions() {
        final Options options = new Options();
        options.addOption(INSTITUTE_ID, true, "specify the institute id");
        options.addOption(ACADEMIC_SESSION_ID, true, "Academic session id for institute");
        options.addOption(STANDARD_ID, true, "Standard Id");
        options.addOption(SECTION_ID, true, "Section Id");
        options.addOption(EXAM_ID, true, "Exam Id");
        options.addOption(DIMENSION_ID, true, "Dimension Id");
        options.addOption(USER_ID, true, "User Id");
        options.addOption(FILE_PATH, true, "Examination report card variables assignment csv file path");
        options.addOption(UPDATE, true, "Update the assignments");
        return options;
    }

    public static void main(String args[]) {
        final ApplicationContext context = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
        final UpdateExamMarks_10030_10031 updateExamMarks_10030_10031 = context.getBean(UpdateExamMarks_10030_10031.class);

        Options options = buildOptions();
        CommandLine cmdLine = null;
        try {
            final CommandLineParser parser = new DefaultParser();
            cmdLine = parser.parse(options, args);
        } catch (final Exception pex) {
            logger.error("Unable to parse arguments.", pex);
            final HelpFormatter formatter = new HelpFormatter();
            formatter.printHelp("ReportCardVariableAssignmentCreator ", options);
            return;
        }

        if (cmdLine == null) {
            logger.error("Unable to parse arguments from cmdline.");
            return;
        }

        int instituteId = 0;
        if (cmdLine.hasOption(INSTITUTE_ID)) {
            instituteId = Integer.parseInt(cmdLine.getOptionValue(INSTITUTE_ID));
        } else {
            logger.error("No institute id passed. Exitting");
            return;
        }
        int academicSessionId = 0;
        if (cmdLine.hasOption(ACADEMIC_SESSION_ID)) {
            academicSessionId = Integer.parseInt(cmdLine.getOptionValue(ACADEMIC_SESSION_ID));
        } else {
            logger.error("No academicSessionId passed. Exitting");
            return;
        }

        UUID standardId = null;
        if (cmdLine.hasOption(STANDARD_ID)) {
            standardId = UUID.fromString(cmdLine.getOptionValue(STANDARD_ID));
        } else {
            logger.error("No class passed. Exitting");
            return;
        }

        Integer sectionId = 0;
        if (cmdLine.hasOption(SECTION_ID)) {
            sectionId = Integer.parseInt(cmdLine.getOptionValue(SECTION_ID));
        }

        UUID examId = null;
        if (cmdLine.hasOption(EXAM_ID)) {
            examId = UUID.fromString(cmdLine.getOptionValue(EXAM_ID));
        } else {
            logger.error("No class passed. Exitting");
            return;
        }

        int dimensionId = 0;
        if (cmdLine.hasOption(DIMENSION_ID)) {
            dimensionId = Integer.parseInt(cmdLine.getOptionValue(DIMENSION_ID));
        } else {
            logger.error("No dimensionId passed. Exitting");
            return;
        }

        UUID userId = null;
        if (cmdLine.hasOption(USER_ID)) {
            userId = UUID.fromString(cmdLine.getOptionValue(USER_ID));
        } else {
            logger.error("No userId passed. Exitting");
            return;
        }

        String filePath = null;
        if (cmdLine.hasOption(FILE_PATH)) {
            filePath = cmdLine.getOptionValue(FILE_PATH);
        } else {
            logger.error("No file path passed. Exitting");
            return;
        }

        boolean updateAssignment = false;
        if (cmdLine.hasOption(UPDATE)) {
            updateAssignment = true;
        }

        logger.info(
                "Running for institute {}, academicSessionId {}, standardId {}, examId {}, dimensionId {}, " +
                        " userId {}, filePath {} , updateAssignment {}",
                instituteId, academicSessionId, standardId, examId, dimensionId, userId, filePath, updateAssignment);
        updateExamMarks_10030_10031.run(instituteId, academicSessionId, standardId, examId, dimensionId, userId,
                filePath, updateAssignment);
    }

    public void run(int instituteId, int academicSessionId, UUID standardId, UUID examId, int dimensionId, UUID userId,
                    String filePath, boolean updateAssignment) {

        try {
            List<MarksFeedData> marksFeedDataList = getPayloadFromFile(instituteId, academicSessionId, standardId,
                    examId, dimensionId, filePath);
            if (marksFeedDataList == null) {
                logger.error("Null list while getting exam marks payload");
                return;
            }
            logger.info("Total MarksFeedData in file {}", marksFeedDataList.size());
            Map<UUID, Map<UUID, List<MarksFeedData>>> examCourseMap = new HashMap<UUID, Map<UUID, List<MarksFeedData>>>();
            for (MarksFeedData marksFeedData : marksFeedDataList) {
                if (!examCourseMap.containsKey(marksFeedData.getExamId())) {
                    examCourseMap.put(marksFeedData.getExamId(), new HashMap<UUID, List<MarksFeedData>>());
                }
                if (!examCourseMap.get(marksFeedData.getExamId()).containsKey(marksFeedData.getCourseId())) {
                    examCourseMap.get(marksFeedData.getExamId()).put(marksFeedData.getCourseId(), new ArrayList<>());
                }
                examCourseMap.get(marksFeedData.getExamId()).get(marksFeedData.getCourseId()).add(marksFeedData);
            }
            if (updateAssignment) {
                boolean totalSuccess = true;
                for (Map.Entry<UUID, Map<UUID, List<MarksFeedData>>> entry : examCourseMap.entrySet()) {
                    for (Map.Entry<UUID, List<MarksFeedData>> courseEntry : entry.getValue().entrySet()) {
                        boolean success = examinationManager.feedMarks(instituteId, 0, courseEntry.getValue(),
                                MarksFeedStatus.SAVED, null, null, userId);
                        totalSuccess &= success;
                    }
                }

                if (totalSuccess) {
                    logger.info("Successfully updated all the exam marks");
                } else {
                    logger.error("Error while updating all the exam marks");
                }
            }

        } catch (Exception e) {
            logger.error("Error while getting exam marks payload", e);
        }
    }


    private List<MarksFeedData> getPayloadFromFile(int instituteId, int academicSessionId, UUID standardId, UUID examId,
                                                   int dimensionId, String fileName) {
        List<String> subjectList = new ArrayList<>();
        Map<String, String[]> studentMarks = new HashMap<>();
        int rowCount = -1;
        /**
         * Assuming first column is of admission number,
         * Then corresponding column are as per sequence provided from terminal
         */
        try (final BufferedReader br = new BufferedReader(new FileReader(fileName))) {
            String line = null;
            while ((line = br.readLine()) != null) {
                if (StringUtils.isBlank(line)) {
                    continue;
                }
                String[] columns = line.split(FILE_DELIMITER, -1);
                rowCount++;

                if(rowCount == 0) {
                    for (String col : columns) {
                        /**
                         * assuming no optional subjects are there,
                         * so every cell will have marks in it.
                         */
                        if(StringUtils.isEmpty(col)) {
                            continue;
                        }
                        subjectList.add(col.trim().toLowerCase());
                    }
                    continue;
                }

                String admissionNumber = columns[0].trim();
                if (StringUtils.isNotBlank(admissionNumber)) {
                    studentMarks.put(admissionNumber, columns);
                }
            }

        return getMarksFeedData(instituteId, academicSessionId, standardId, examId, dimensionId, subjectList,
                studentMarks);

        } catch (final Exception e) {
            logger.error("Error while reading file", e);
        }
        return null;
    }

    private List<MarksFeedData> getMarksFeedData(int instituteId, int academicSessionId, UUID standardId, UUID examId,
                                                 int dimensionId, List<String> subjectList, Map<String, String[]> studentMarks) {

        StandardMetadata standardMetaData = instituteManager.getStandardMetaData(instituteId, academicSessionId, standardId);
        if (standardMetaData == null) {
            logger.error("Invalid standard Id {}. Skipping.", standardId);
            return null;
        }
        ExamNode examNode = getExams(instituteId, standardMetaData.getStandardId(), academicSessionId).get(examId);
        if (examNode == null) {
            logger.error("Invalid exam {}. Skipping.", examId);
            throw new EmbrateRunTimeException("Invalid exam");
        }
        Map<UUID, Map<String, Course>> examCourseMap = getExamCourseMap(examNode);
        Map<String, Student> studentMap = getStudents(instituteId, academicSessionId, standardMetaData.getStandardId());
        logger.info("subjectList  {}", subjectList);

        List<MarksFeedData> marksFeedDatas = new ArrayList<MarksFeedData>();
        for (Map.Entry<String, String[]> studentEntry : studentMarks.entrySet()) {
            String admissionNumber = studentEntry.getKey();
            if (!studentMap.containsKey(admissionNumber)) {
                logger.error("Invalid admission number {}. Skipping.", admissionNumber);
                return null;
            }

            Student student = studentMap.get(admissionNumber);

            /**
             * starting from 1dt index as 0 will
             * contain admission number of student
             */
            for (int i = 1; i < studentEntry.getValue().length; i++) {
                String value = studentEntry.getValue()[i];
                String inputCourseName = subjectList.get(i - 1);

                if (StringUtils.isBlank(value)) {
                    continue;
                }

                try {
                    Double.parseDouble(value);
                } catch (Exception e) {
                    continue;
                }
                if (StringUtils.isNotBlank(inputCourseName)) {
                    String courseName = getCourseName(inputCourseName);
                    if (StringUtils.isBlank(courseName)) {
                        logger.error("Invalid course input {} . Skipping.", inputCourseName);
                        throw new EmbrateRunTimeException("Invalid course mapping");
                    }
                    Course course = examCourseMap.get(examNode.getExamMetaData().getExamId())
                            .get(courseName.toLowerCase());
                    if (course == null) {
                        logger.error("Invalid course input {}, mapped course {} . Skipping.", inputCourseName,
                                courseName);
                        throw new EmbrateRunTimeException("Invalid course");
                    }
                    MarksFeedData marksFeedData = new MarksFeedData();
                    marksFeedData.setStudentId(student.getStudentId());
                    marksFeedData.setExamId(examNode.getExamMetaData().getExamId());
                    marksFeedData.setCourseId(course.getCourseId());
                    /**
                     * Assuming we will always get dimension id > 0
                     */
                    ExamDimensionObtainedValues examDimensionObtainedValues = new ExamDimensionObtainedValues(
                                new ExamDimension(dimensionId, null, null, null, false));

                    examDimensionObtainedValues.setObtainedMarks(Double.parseDouble(value));
                    marksFeedData.setExamDimensionObtainedValues(Arrays.asList(examDimensionObtainedValues));

                    marksFeedDatas.add(marksFeedData);
                }

            }
        }
        return marksFeedDatas;
    }

    private String getCourseName(String inputCourseName) {
        return inputCourseName.toLowerCase();
    }

    private Map<String, Student> getStudents(int instituteId, int academicSessionId, UUID standardId) {
        List<Student> students = studentManager.getClassStudents(instituteId, academicSessionId, standardId);
        Map<String, Student> studentMap = new HashMap<String, Student>();
        for (Student student : students) {
            studentMap.put(student.getStudentBasicInfo().getAdmissionNumber(), student);
        }
        return studentMap;
    }

    private Map<UUID, ExamNode> getExams(int instituteId, UUID standardId, int academicSessionId) {
        List<ExamNode> examNodes = examinationManager.getClassExamsForest(standardId, academicSessionId, instituteId,
                true);
        List<ExamNode> allExams = new ArrayList<>();
        populateExams(examNodes.get(0), allExams);

        Map<UUID, ExamNode> examNameMap = new HashMap<UUID, ExamNode>();
        for (ExamNode examNode : allExams) {
            examNameMap.put(examNode.getExamMetaData().getExamId(), examNode);
        }

        return examNameMap;
    }

    private Map<UUID, Map<String, Course>> getExamCourseMap(ExamNode examNode) {
        Map<UUID, Map<String, Course>> examCourseMap = new HashMap<UUID, Map<String, Course>>();
            Map<String, Course> courseMap = new HashMap<String, Course>();
            for (Course course : examNode.getCourses()) {
                courseMap.put(course.getCourseName().trim().toLowerCase(), course);
            }
            examCourseMap.put(examNode.getExamMetaData().getExamId(), courseMap);
        return examCourseMap;
    }

    private void populateExams(ExamNode root, List<ExamNode> allExams) {
        allExams.add(root);
        if (CollectionUtils.isEmpty(root.getChildren())) {
            return;
        }
        for (ExamNode examNode : root.getChildren()) {
            populateExams(examNode, allExams);
        }
    }

}

