package com.lernen.cloud.dev.tools.fees.assignment;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.ParseException;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.fees.FeeStructureType;
import com.lernen.cloud.core.api.fees.ResolvedDefaultEntityFeeAssignmentStructure;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.fees.configuration.FeeConfigurationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentAdmissionManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;

/**
 * 
 * <AUTHOR>
 *
 */
public class AssignFeeStructure {
	private static final Logger logger = LogManager.getLogger(AssignFeeStructure.class);

	private static final String USER_ID = "ui";
	private static final String INSTITUTE_ID = "i";
	private static final String CURRENT_ACADEMIC_SESSION_ID = "cs";
	private static final String STANDARD_LIST = "standards";
	private static final String STUDENT_LIST = "students";
	private static final String FEE_STRUCTURE_LIST = "feestructures";

	private final InstituteManager instituteManager;
	private final StudentManager studentManager;
	private final StudentAdmissionManager studentAdmissionManager;
	private final FeeConfigurationManager feeConfigurationManager;

	public AssignFeeStructure(InstituteManager instituteManager, StudentManager studentManager,
			StudentAdmissionManager studentAdmissionManager, FeeConfigurationManager feeConfigurationManager) {
		this.instituteManager = instituteManager;
		this.studentManager = studentManager;
		this.studentAdmissionManager = studentAdmissionManager;
		this.feeConfigurationManager = feeConfigurationManager;
	}

	public static void main(String[] args) {
		final ApplicationContext ctx = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
		final InstituteManager instituteManager = ctx.getBean(InstituteManager.class);
		final StudentManager studentManager = ctx.getBean(StudentManager.class);
		final StudentAdmissionManager studentAdmissionManager = ctx.getBean(StudentAdmissionManager.class);
		final FeeConfigurationManager feeConfigurationManager = ctx.getBean(FeeConfigurationManager.class);
		final AssignFeeStructure assignFeeStructure = new AssignFeeStructure(instituteManager, studentManager,
				studentAdmissionManager, feeConfigurationManager);

		final Options options = buildOptions();

		CommandLine cmdLine = null;
		try {
			final CommandLineParser parser = new DefaultParser();
			cmdLine = parser.parse(options, args);
		} catch (final ParseException pex) {
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("Ingeststudents10050", options);
			System.exit(2);
		}

		Integer instituteId = null;
		if (cmdLine.hasOption(INSTITUTE_ID)) {
			instituteId = Integer.parseInt(cmdLine.getOptionValue(INSTITUTE_ID));
		} else {
			logger.error("No INSTITUTE_ID passed. Exitting");
			return;
		}
		UUID userId = null;
		if (cmdLine.hasOption(USER_ID)) {
			userId = UUID.fromString(cmdLine.getOptionValue(USER_ID));
		} else {
			logger.error("No userId passed. Exitting");
			return;
		}
		Set<UUID> feeStructureIds = new HashSet<>();
		if (cmdLine.hasOption(STUDENT_LIST)) {
			String feeStructureIdsCSV = cmdLine.getOptionValue(FEE_STRUCTURE_LIST);
			if (StringUtils.isBlank(feeStructureIdsCSV)) {
				logger.error("Invalid student list. Exitting");
				return;
			}
			String[] tokens = feeStructureIdsCSV.split(",");
			for (String token : tokens) {
				feeStructureIds.add(UUID.fromString(token));
			}
		}

		Set<UUID> studentIds = new HashSet<>();
		if (cmdLine.hasOption(STUDENT_LIST)) {
			String studentIdsCSV = cmdLine.getOptionValue(STUDENT_LIST);
			if (StringUtils.isBlank(studentIdsCSV)) {
				logger.error("Invalid student list. Exitting");
				return;
			}
			String[] tokens = studentIdsCSV.split(",");
			for (String token : tokens) {
				studentIds.add(UUID.fromString(token));
			}
		}

		Set<UUID> standardIds = new HashSet<>();

		if (cmdLine.hasOption(STANDARD_LIST)) {
			String standardsIdsCSV = cmdLine.getOptionValue(STANDARD_LIST);
			if (StringUtils.isBlank(standardsIdsCSV)) {
				logger.error("Invalid standard list. Exitting");
				return;
			}
			String[] tokens = standardsIdsCSV.split(",");
			for (String token : tokens) {
				standardIds.add(UUID.fromString(token));
			}
		}

		int currentAcademicSessionId = 0;
		if (cmdLine.hasOption(CURRENT_ACADEMIC_SESSION_ID)) {
			currentAcademicSessionId = Integer.parseInt(cmdLine.getOptionValue(CURRENT_ACADEMIC_SESSION_ID));
		} else {
			logger.error("No current academicSessionId passed. Exitting");
			return;
		}

		logger.info(
				"Running fee structure assignment instituteId {}, currentAcademicSessionId {}, feeStructureIds {}, studentIds {}, standardIds {}",
				instituteId, currentAcademicSessionId, feeStructureIds, studentIds, standardIds);
		assignFeeStructure.run(instituteId, currentAcademicSessionId, feeStructureIds, studentIds, standardIds, userId);
		logger.info("Assigned fee structures.");

	}

	private static Options buildOptions() {
		final Options options = new Options();
		options.addOption(new Option(USER_ID, true, "User id"));
		options.addOption(new Option(INSTITUTE_ID, true, "Institute Id"));
		options.addOption(new Option(STANDARD_LIST, true, "Standards List CSV"));
		options.addOption(new Option(STUDENT_LIST, true, "Students List CSV"));
		options.addOption(new Option(FEE_STRUCTURE_LIST, true, "Fee Structures List CSV"));
		options.addOption(CURRENT_ACADEMIC_SESSION_ID, true, "Current academic session id for institute");
		return options;
	}

	private boolean run(Integer instituteId, int academicSessionId, Set<UUID> feeStructureIds, Set<UUID> studentIds,
			Set<UUID> standardIds, UUID userId) {
		List<Student> students = null;
		if (!CollectionUtils.isEmpty(studentIds)) {
			logger.info("Student ids are present so skipping standards");
			students = studentManager.getStudentByAcademicSessionStudentIds(instituteId, academicSessionId,
					new ArrayList<>(studentIds));
		} else if (!CollectionUtils.isEmpty(standardIds)) {
			students = studentManager.getClassStudents(instituteId, academicSessionId, standardIds);
		} else {
			logger.error("No student or standard provided. Exiting");
		}

		for (Student student : students) {
			try {
				List<UUID> feeStructuresIds = getEntollStudentFeeStructure(instituteId, academicSessionId,
						student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId(), feeStructureIds);
				if (CollectionUtils.isEmpty(feeStructuresIds)) {
					logger.error("No fee structure found for assignment for student {}", student.getStudentId());
					continue;

				}

				if (studentAdmissionManager.assignFeeStructure(instituteId, academicSessionId, student.getStudentId(),
						student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId(), feeStructuresIds,
						Arrays.asList(FeeStructureType.ENROLLMENT), false, userId)) {
					logger.info("Fee structure assigned successfully {}", student.getStudentId());
				} else {
					logger.error("Failed to assigne fee structure for student {}", student.getStudentId());
				}
			} catch (Exception e) {
				logger.error("Error while assigning fees for student {}", student.getStudentId(), e);
			}
		}
		return true;
	}

	private List<UUID> getEntollStudentFeeStructure(int instituteId, int academicSessionId, UUID standardId,
			Set<UUID> feeStructureIds) {

		List<UUID> feeStructuresIds = new ArrayList<>();

		List<ResolvedDefaultEntityFeeAssignmentStructure> requiredResolvedDefaultEntityFeeAssignmentStructures = getDefaultFeeStructure(
				instituteId, academicSessionId, standardId);
		for (ResolvedDefaultEntityFeeAssignmentStructure resolvedDefaultEntityFeeAssignmentStructure : requiredResolvedDefaultEntityFeeAssignmentStructures) {
			if (CollectionUtils.isEmpty(feeStructureIds)) {
				feeStructuresIds.add(resolvedDefaultEntityFeeAssignmentStructure.getStructureId());
				logger.info("Fee structure found for assingment are {}",
						resolvedDefaultEntityFeeAssignmentStructure.getStructureName());
			} else {
				if (feeStructureIds.contains(resolvedDefaultEntityFeeAssignmentStructure.getStructureId())) {
					feeStructuresIds.add(resolvedDefaultEntityFeeAssignmentStructure.getStructureId());
					logger.info("Fee structure found for assingment are {}",
							resolvedDefaultEntityFeeAssignmentStructure.getStructureName());
				}
			}
		}
		return feeStructuresIds;
	}

	private List<ResolvedDefaultEntityFeeAssignmentStructure> getDefaultFeeStructure(int instituteId,
			int academicSessionId, UUID standardId) {

		List<ResolvedDefaultEntityFeeAssignmentStructure> requiredResolvedDefaultEntityFeeAssignmentStructures = new ArrayList<>();
		List<ResolvedDefaultEntityFeeAssignmentStructure> resolvedDefaultEntityFeeAssignmentStructures = feeConfigurationManager
				.getDefaultFeeAssignmentStructure(instituteId, academicSessionId, standardId,
						Arrays.asList(FeeStructureType.ENROLLMENT), false);
		return resolvedDefaultEntityFeeAssignmentStructures;

	}

}
