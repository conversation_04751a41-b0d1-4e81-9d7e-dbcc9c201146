package com.lernen.cloud.dev.tools.fees.assignment;

import com.lernen.cloud.core.api.fees.*;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.fees.configuration.FeeConfigurationManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;
import org.apache.commons.cli.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.*;
import java.util.Map.Entry;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeeAmountAssigner {
	private static final Logger logger = LogManager.getLogger(FeeAmountAssigner.class);

	private static final String INSTITUTE_ID = "i";
	private static final String ACADEMIC_SESSION_ID = "s";
	private static final String USER = "user";
	private static final String FILE_PATH = "f";
	private static final String FILE_HEADER = "h";
	private static final String UPDATE = "u";
	private static final String FILE_DELIMITER = ",";

	private final FeeConfigurationManager feeConfigurationManager;
	private final StudentManager studentManager;

	public FeeAmountAssigner(FeeConfigurationManager feeConfigurationManager, StudentManager studentManager) {
		this.feeConfigurationManager = feeConfigurationManager;
		this.studentManager = studentManager;
	}

	public static void main(String args[]) {
		final ApplicationContext context = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
		final StudentManager studentManager = context.getBean(StudentManager.class);
		final FeeConfigurationManager feeConfigurationManager = context.getBean(FeeConfigurationManager.class);
		FeeAmountAssigner feeAmountAssigner = new FeeAmountAssigner(feeConfigurationManager, studentManager);
		Options options = buildOptions();
		CommandLine cmdLine = null;
		try {
			final CommandLineParser parser = new DefaultParser();
			cmdLine = parser.parse(options, args);
		} catch (final Exception pex) {
			System.err.print("Unable to parse arguments.");
			pex.printStackTrace();
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("TransportAssignmentCreator ", options);
			return;
		}

		if (cmdLine == null) {
			logger.error("Unable to parse arguments from cmdline.");
			return;
		}

		int instituteId = 0;
		if (cmdLine.hasOption(INSTITUTE_ID)) {
			instituteId = Integer.parseInt(cmdLine.getOptionValue(INSTITUTE_ID));
		} else {
			logger.error("No institute id passed. Exitting");
			return;
		}
		int academicSessionId = 0;
		if (cmdLine.hasOption(ACADEMIC_SESSION_ID)) {
			academicSessionId = Integer.parseInt(cmdLine.getOptionValue(ACADEMIC_SESSION_ID));
		} else {
			logger.error("No academicSessionId passed. Exitting");
			return;
		}

		UUID userId = null;
		if (cmdLine.hasOption(USER)) {
			userId = UUID.fromString(cmdLine.getOptionValue(USER));
		} else {
			logger.error("No userId passed. Exitting");
			return;
		}

		String filePath = null;
		if (cmdLine.hasOption(FILE_PATH)) {
			filePath = cmdLine.getOptionValue(FILE_PATH);
		} else {
			logger.error("No file path passed. Exitting");
			return;
		}
		boolean fileHeader = false;
		if (cmdLine.hasOption(FILE_HEADER)) {
			fileHeader = true;
		}
		boolean updateAssignment = false;
		if (cmdLine.hasOption(UPDATE)) {
			updateAssignment = true;
		}
		logger.info("Running for institute = " + instituteId + " , session = " + academicSessionId + ", header = "
				+ fileHeader + " , assign = " + updateAssignment);
		feeAmountAssigner.assignFees(instituteId, academicSessionId, filePath, fileHeader, updateAssignment,
				userId);
	}

	public void assignFees(int instituteId, int academicSessionId, String filePath, boolean fileHeader,
			boolean updateAssignment, UUID userId) {
		List<FeeAssignmentFilePayload> feeAssignmentFilePayloads = readAssignmentFile(filePath, fileHeader);
		logger.info("Total records in file = " + feeAssignmentFilePayloads.size());

		for (FeeAssignmentFilePayload feeAssignmentFilePayload : feeAssignmentFilePayloads) {
			logger.info(feeAssignmentFilePayload.toString());
		}

		List<FeeAssignmentDetails> feeAssignmentDetailsList = getFeeAssignmentPayloads(instituteId, academicSessionId,
				feeAssignmentFilePayloads);

		boolean totalSuccess = true;
		int successCount = 0;
		int failCount = 0;
		logger.info("Assigning fees for total records = " + feeAssignmentFilePayloads.size());
		for (FeeAssignmentDetails feeAssignmentDetails : feeAssignmentDetailsList) {
			logger.info("Payload = " + feeAssignmentDetails.toString());
			if (!updateAssignment) {
				continue;
			}
			if (feeConfigurationManager.assignFees(feeAssignmentDetails, Module.FEES,
					FeeAssignmentState.COMPLETE_UPDATE,
					false, true, userId)) {
				logger.info("Assigned fee amount successfully.");
				successCount++;
			} else {
				logger.error("ERROR : Assigned transport amount not success.");
				totalSuccess = false;
				failCount++;
			}
		}
		if (totalSuccess) {
			logger.info("All records are assigned successfully.");
		} else {
			logger.info("ERROR : All records are not assigned  successfully.");
		}
		logger.info("Total = " + (successCount + failCount) + ", success = " + successCount + " , fail = " + failCount);
	}

	private final List<FeeAssignmentDetails> getFeeAssignmentPayloads(int instituteId, int academicSessionId,
			List<FeeAssignmentFilePayload> feeAssignmentFilePayloads) {
		List<FeeAssignmentDetails> feeAssignmentDetailsList = new ArrayList<>();
		Map<String, Student> studentMap = new HashMap<>();
		List<Student> enrolledStudents = studentManager.getStudentsInAcademicSession(instituteId, academicSessionId);
		for (Student studentResponse : enrolledStudents) {
			studentMap.put(studentResponse.getStudentBasicInfo().getAdmissionNumber().toLowerCase(), studentResponse);
		}

		List<FeeConfigurationResponse> feeConfigurationResponses = feeConfigurationManager
				.getFeeConfigurationByAcademicYear(instituteId, academicSessionId);
		Map<String, FeeConfigurationResponse> feeMap = new HashMap<>();

		for (FeeConfigurationResponse feeConfigurationResponse : feeConfigurationResponses) {
			feeMap.put(feeConfigurationResponse.getFeeConfigurationBasicInfo().getFeeName().toLowerCase(),
					feeConfigurationResponse);
		}

		List<FeeHeadConfigurationResponse> feeHeadConfigurationResponses = feeConfigurationManager
				.getFeeHeadConfiguration(instituteId);
		Map<String, FeeHeadConfigurationResponse> feeHeadMap = new HashMap<>();

		for (FeeHeadConfigurationResponse feeHeadConfigurationResponse : feeHeadConfigurationResponses) {
			feeHeadMap.put(feeHeadConfigurationResponse.getFeeHeadConfiguration().getFeeHead().toLowerCase(),
					feeHeadConfigurationResponse);
		}

		Map<UUID, Map<UUID, Map<Integer, Double>>> entityFeeMap = new HashMap<>();

		for (FeeAssignmentFilePayload feeAssignmentFilePayload : feeAssignmentFilePayloads) {
			Student student = studentMap.get(feeAssignmentFilePayload.getAdmissionNumber().toLowerCase());
			if (student == null) {
				logger.error("Invalid student {}", feeAssignmentFilePayload.getAdmissionNumber());
				continue;
			}
			FeeConfigurationResponse feeConfigurationResponse = feeMap
					.get(feeAssignmentFilePayload.getFeeName().toLowerCase());
			if (feeConfigurationResponse == null) {
				logger.error("Invalid fees name {}", feeAssignmentFilePayload.getFeeName());
				continue;
			}

			FeeHeadConfigurationResponse feeHeadConfigurationResponse = feeHeadMap
					.get(feeAssignmentFilePayload.getFeeHeadName().toLowerCase());
			if (feeHeadConfigurationResponse == null) {
				logger.error("Invalid fee head name {}", feeAssignmentFilePayload.getFeeHeadName());
				continue;
			}
			UUID feeId = feeConfigurationResponse.getFeeConfigurationBasicInfo().getFeeId();
			if (!entityFeeMap.containsKey(feeId)) {
				entityFeeMap.put(feeId, new HashMap<>());
			}
			UUID studentId = student.getStudentId();
			if (!entityFeeMap.get(feeId).containsKey(studentId)) {
				entityFeeMap.get(feeId).put(studentId, new HashMap<>());
			}
			entityFeeMap.get(feeId).get(studentId).put(
					feeHeadConfigurationResponse.getFeeHeadConfiguration().getFeeHeadId(),
					feeAssignmentFilePayload.getAmount());
		}

		for (Entry<UUID, Map<UUID, Map<Integer, Double>>> feeEntry : entityFeeMap.entrySet()) {
			UUID feeId = feeEntry.getKey();
			List<EntityFees> entityFees = new ArrayList<>();
			for (Entry<UUID, Map<Integer, Double>> studentEntry : feeEntry.getValue().entrySet()) {
				UUID studentId = studentEntry.getKey();
				List<FeeHeadAmount> feeHeadAmount = new ArrayList<>();
				for (Entry<Integer, Double> feeHeadEntry : studentEntry.getValue().entrySet()) {
					feeHeadAmount.add(new FeeHeadAmount(feeHeadEntry.getKey(), feeHeadEntry.getValue()));
				}
				entityFees.add(new EntityFees(studentId.toString(), FeeEntity.STUDENT, feeHeadAmount));
			}
			feeAssignmentDetailsList.add(new FeeAssignmentDetails(instituteId, feeId, entityFees));
		}
		return feeAssignmentDetailsList;
	}

	private List<FeeAssignmentFilePayload> readAssignmentFile(String fileName, boolean header) {
		List<FeeAssignmentFilePayload> feeAssignmentFilePayloads = new ArrayList<>();
		try (final BufferedReader br = new BufferedReader(new FileReader(fileName))) {
			String line = null;
			while ((line = br.readLine()) != null) {
				if (header) {
					header = false;
					continue;
				}
				if (StringUtils.isBlank(line)) {
					continue;
				}
				logger.info("line = " + line);
				String[] columns = line.split(FILE_DELIMITER);
				if (columns.length < 4) {
					logger.error("Columns are lesser then " + 4 + ". Skipping it as its not for assignment. Entry = "
							+ line);
					continue;
				}
				String admissionNumber = columns[0].trim();
				String feeName = columns[1].trim();
				String feeHeadName = columns[2].trim();
				Double amount = Double.parseDouble(columns[3].trim());
				if (StringUtils.isBlank(admissionNumber) || StringUtils.isBlank(feeName)
						|| StringUtils.isBlank(feeHeadName)) {
					logger.error("Not valid entry for assignment. Entry = " + line);
					continue;
				}

				feeAssignmentFilePayloads
						.add(new FeeAssignmentFilePayload(admissionNumber, feeName, feeHeadName, amount));
			}
			return feeAssignmentFilePayloads;
		} catch (final Exception e) {
			logger.error("Error while reading file", e);
		}
		return null;
	}

	private static Options buildOptions() {
		final Options options = new Options();
		options.addOption(INSTITUTE_ID, true, "specify the institute id");
		options.addOption(ACADEMIC_SESSION_ID, true, "Academic session id for institute");
		options.addOption(FILE_PATH, true, "Transport assignment csv file path");
		options.addOption(FILE_HEADER, false, "File has header");
		options.addOption(UPDATE, false, "Update the assignments");
		options.addOption(USER, true, "User id for transaction");
		return options;
	}

	private class FeeAssignmentFilePayload {
		private final String admissionNumber;
		private final String feeName;
		private final String feeHeadName;
		private final Double amount;

		public FeeAssignmentFilePayload(String admissionNumber, String feeName, String feeHeadName, Double amount) {
			this.admissionNumber = admissionNumber;
			this.feeName = feeName;
			this.feeHeadName = feeHeadName;
			this.amount = amount;
		}

		public String getAdmissionNumber() {
			return admissionNumber;
		}

		public String getFeeName() {
			return feeName;
		}

		public String getFeeHeadName() {
			return feeHeadName;
		}

		public Double getAmount() {
			return amount;
		}

		@Override
		public String toString() {
			return "FeeAssignmentFilePayload [admissionNumber=" + admissionNumber + ", feeName=" + feeName
					+ ", feeHeadName=" + feeHeadName + ", amount=" + amount + "]";
		}

	}
}
