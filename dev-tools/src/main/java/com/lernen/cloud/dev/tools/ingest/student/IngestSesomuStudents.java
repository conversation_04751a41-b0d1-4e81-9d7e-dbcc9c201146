package com.lernen.cloud.dev.tools.ingest.student;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.ParseException;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.fees.FeeStructureType;
import com.lernen.cloud.core.api.fees.ResolvedDefaultEntityFeeAssignmentStructure;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.institute.Stream;
import com.lernen.cloud.core.api.student.RegisterStudentPayload;
import com.lernen.cloud.core.api.student.StudentBasicInfo;
import com.lernen.cloud.core.api.student.StudentFamilyInfo;
import com.lernen.cloud.core.api.student.StudentPayload;
import com.lernen.cloud.core.api.student.StudentPreviousSchoolInfo;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.lib.fees.configuration.FeeConfigurationManager;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentAdmissionManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;

public class IngestSesomuStudents {
	private static final Logger logger = LogManager.getLogger(IngestSesomuStudents.class);
	private static final String FILE_PATH = "f";
	private static final String CURRENT_ACADEMIC_SESSION_ID = "cs";
	private static final String CSV_DELIMITER = "\\|";
	private static final String SPACE_DELIMITER = " ";
	public static final String DATE_FORMAT = "dd/MM/yyyy";
	private static final String TEMP_REGISTRATION_NUMBER_SUFFIX = "-TEMP";

	private static final int INSTITUTE_ID = 10001;

	public static final String PLAY_GROUP = "Play Group";
	public static final String NURSERY = "Nursery";
	public static final String LKG = "LKG";
	public static final String UKG = "UKG";
	public static final String FIRST = "Class I";
	public static final String SECOND = "Class II";
	public static final String THIRD = "Class III";
	public static final String FOURTH = "Class IV";
	public static final String FIFTH = "Class V";
	public static final String SIXTH = "Class VI";
	public static final String SEVENTH = "Class VII";
	public static final String EIGHTH = "Class VIII";
	public static final String NINTH = "Class IX";
	public static final String TENTH = "Class X";
	public static final String ELEVENTH = "Class XI";
	public static final String TWELFTH = "Class XII";

	public static final Map<String, List<String>> STUDENT_FEE_STRUCTURE_MAPPING = new HashMap<>();

	/**
	 * D => Day Scholar, H => Hostler , N => New Student , O => Old Students
	 */
	static {
		STUDENT_FEE_STRUCTURE_MAPPING.put("D-N", Arrays.asList("Day Scholar Fees Structure".trim().toLowerCase(),
				"Admission Fees".trim().toLowerCase()));
		STUDENT_FEE_STRUCTURE_MAPPING.put("D-O", Arrays.asList("Day Scholar Fees Structure".trim().toLowerCase()));
		STUDENT_FEE_STRUCTURE_MAPPING.put("H-N", Arrays.asList("Hostel Fees Structure".trim().toLowerCase(),
				"Admission Fees".trim().toLowerCase(), "Hostel One Time Fees".trim().toLowerCase()));
		STUDENT_FEE_STRUCTURE_MAPPING.put("H-O", Arrays.asList("Hostel Fees Structure".trim().toLowerCase()));
	}

	private final InstituteManager instituteManager;
	private final StudentManager studentManager;
	private final StudentAdmissionManager studentAdmissionManager;
	private final FeeConfigurationManager feeConfigurationManager;

	public IngestSesomuStudents(InstituteManager instituteManager, StudentManager studentManager,
			StudentAdmissionManager studentAdmissionManager, FeeConfigurationManager feeConfigurationManager) {
		this.instituteManager = instituteManager;
		this.studentManager = studentManager;
		this.studentAdmissionManager = studentAdmissionManager;
		this.feeConfigurationManager = feeConfigurationManager;
	}

	public static void main(String[] args) {
		final ApplicationContext ctx = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
		final IngestSesomuStudents ingestSesomuStudents = ctx.getBean(IngestSesomuStudents.class);
		final Options options = buildOptions();

		CommandLine cmdLine = null;
		try {
			final CommandLineParser parser = new DefaultParser();
			cmdLine = parser.parse(options, args);
		} catch (final ParseException pex) {
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("IngestSesomuStudents", options);
			System.exit(2);
		}

		if (!cmdLine.hasOption(FILE_PATH)) {
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("File path required", options);
		}

		int currentAcademicSessionId = 0;
		if (cmdLine.hasOption(CURRENT_ACADEMIC_SESSION_ID)) {
			currentAcademicSessionId = Integer.parseInt(cmdLine.getOptionValue(CURRENT_ACADEMIC_SESSION_ID));
		} else {
			logger.error("No current academicSessionId passed. Exitting");
			return;
		}

		String filePath = cmdLine.getOptionValue(FILE_PATH);
		logger.info("Reading student data from file: {} ", filePath);
		ingestSesomuStudents.ingestStudents(filePath, currentAcademicSessionId);
		logger.info("Ingested student data from file: {}", filePath);
	}

	private static Options buildOptions() {
		final Options options = new Options();
		options.addOption(new Option(FILE_PATH, true, "File Path"));
		options.addOption(CURRENT_ACADEMIC_SESSION_ID, true, "Current academic session id for institute");
		return options;
	}

	public boolean ingestStudents(String filePath, int academicSession) {
		Set<String> classes = new HashSet<>();
		Map<String, Integer> counts = new HashMap<>();
		try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
			String line = "";
			boolean header = true;
			int error = 0;
			int count = 0;
			while ((line = br.readLine()) != null) {
				if (header) {
					header = false;
					continue;
				}
				// System.out.println(line);
				String[] params = line.split(CSV_DELIMITER, -1);
				// System.out.println(params.length);
				classes.add(params[6].trim());
				if (counts.get(params[6].trim()) == null) {
					counts.put(params[6].trim(), 1);
				} else {
					counts.put(params[6].trim(), counts.get(params[6].trim()) + 1);
				}
				count++;
				RegisterStudentPayload registerStudentPaylaod = getStudentPayload(params, academicSession);
				if (registerStudentPaylaod == null) {
					logger.error("Invalid Student Info. Skipping...");
					error++;
					continue;
				}
				UUID studentId = studentAdmissionManager.registerStudent(registerStudentPaylaod, INSTITUTE_ID, null,
						null, Arrays.asList(FeeStructureType.REGISTRATION, FeeStructureType.ENROLLMENT), false, null);
				if (studentId == null) {
					logger.error("Unable to register Student. Skipping...");
					error++;
					continue;
				}
				if (!studentAdmissionManager.admitStudent(INSTITUTE_ID, studentId,
						registerStudentPaylaod.getStudentPayload().getStudentBasicInfo().getAdmissionNumber())) {
					logger.error("Unable to admit Student...");
					error++;
					continue;
				}

			}
			logger.info(counts);
			logger.info(classes);
			logger.info(count);
			logger.info(error);

		} catch (Exception e) {
			logger.error("Error occurred while adding students", e);
		}
		return false;
	}

	private List<ResolvedDefaultEntityFeeAssignmentStructure> getDefaultFeeStructure(int instituteId,
			int academicSessionId, UUID standardId, Set<String> structureNames) {
		List<ResolvedDefaultEntityFeeAssignmentStructure> requiredResolvedDefaultEntityFeeAssignmentStructures = new ArrayList<>();
		List<ResolvedDefaultEntityFeeAssignmentStructure> resolvedDefaultEntityFeeAssignmentStructures = feeConfigurationManager
				.getDefaultFeeAssignmentStructure(instituteId, academicSessionId, standardId,
						Arrays.asList(FeeStructureType.REGISTRATION, FeeStructureType.ENROLLMENT), false);
		for (ResolvedDefaultEntityFeeAssignmentStructure resolvedDefaultEntityFeeAssignmentStructure : resolvedDefaultEntityFeeAssignmentStructures) {
			if (structureNames
					.contains(resolvedDefaultEntityFeeAssignmentStructure.getStructureName().trim().toLowerCase())) {
				requiredResolvedDefaultEntityFeeAssignmentStructures.add(resolvedDefaultEntityFeeAssignmentStructure);
			}
		}
		return requiredResolvedDefaultEntityFeeAssignmentStructures;
	}

	private RegisterStudentPayload getStudentPayload(String[] params, int academicSession) {
		StudentPayload studentPayload = new StudentPayload();
		StudentBasicInfo studentBasicInfo = new StudentBasicInfo();
		StudentFamilyInfo studentFamilyInfo = new StudentFamilyInfo();
		StudentPreviousSchoolInfo studentPreviousSchoolInfo = new StudentPreviousSchoolInfo();
		String name = params[1];
		if (StringUtils.isBlank(name)) {
			logger.error("Invalid name");
			return null;
		}
		studentBasicInfo.setName(name.toString().trim());

		String registrationNumber = params[5];
		if (StringUtils.isBlank(registrationNumber)) {
			logger.error("Invalid registrationNumber for {}", name);
			return null;
		}
		studentBasicInfo.setRegistrationNumber(registrationNumber.trim() + TEMP_REGISTRATION_NUMBER_SUFFIX);
		studentBasicInfo.setAdmissionNumber(registrationNumber.trim());

		String inputCast = params[10];
		if (!StringUtils.isBlank(inputCast)) {
			String cast = inputCast;
			if (inputCast.equalsIgnoreCase("G")) {
				cast = "General";
			}
			studentBasicInfo.setUserCategory(UserCategory.getCategory(cast.trim()));
		}
		String gender = params[9];
		if (!StringUtils.isBlank(gender)) {
			if (gender.equalsIgnoreCase("M")) {
				studentBasicInfo.setGender(Gender.MALE);
			} else if (gender.equalsIgnoreCase("F")) {
				studentBasicInfo.setGender(Gender.FEMALE);
			}
		}
		String dob = params[4];
		if (!StringUtils.isBlank(dob)) {
			int dobTimestamp = DateUtils.getTimestampFromDate(dob.trim(), User.DFAULT_TIMEZONE, DATE_FORMAT);
			studentBasicInfo.setDateOfBirth(dobTimestamp);
		}

		String contactNumber = params[7];
		if (!StringUtils.isBlank(contactNumber)) {
			studentBasicInfo.setPrimaryContactNumber(contactNumber.trim());
			studentFamilyInfo.setFathersContactNumber(contactNumber.trim());
		}

		String admissionDate = params[8];
		if (!StringUtils.isBlank(admissionDate)) {
			int admissionTimestamp = DateUtils.getTimestampFromDate(admissionDate.trim(), User.DFAULT_TIMEZONE,
					DATE_FORMAT);
			studentBasicInfo.setAdmissionDate(admissionTimestamp);
		}

		String address = params[11];
		if (!StringUtils.isBlank(address)) {
			studentBasicInfo.setPermanentAddress(address.trim());
		}

		String city = params[12];
		if (!StringUtils.isBlank(city)) {
			studentBasicInfo.setPermanentCity(city.trim());
		}

		String state = params[13];
		if (!StringUtils.isBlank(state)) {
			studentBasicInfo.setPermanentState(state.trim());
		}

		String placeOfBirth = params[14];
		if (!StringUtils.isBlank(placeOfBirth)) {
			studentBasicInfo.setBirthPlace(placeOfBirth.trim());
		}

		String montherTongue = params[15];
		if (!StringUtils.isBlank(montherTongue)) {
			studentBasicInfo.setMotherTongue(montherTongue.trim());
		}

		String phyDisabled = params[21];
		if (!StringUtils.isBlank(phyDisabled)) {
			if (phyDisabled.equalsIgnoreCase("N")) {
				studentBasicInfo.setSpeciallyAbled(false);
			} else if (phyDisabled.equalsIgnoreCase("Y")) {
				studentBasicInfo.setSpeciallyAbled(true);
			}
		}

		String prevSchool = params[16];
		if (!StringUtils.isBlank(prevSchool)) {
			studentPreviousSchoolInfo.setSchoolName(prevSchool.trim());
		}

		String prevSchoolClassPassed = params[17];
		if (!StringUtils.isBlank(prevSchoolClassPassed)) {
			studentPreviousSchoolInfo.setClassPassed(prevSchoolClassPassed.trim());
		}

		String prevSchoolMedium = params[18];
		if (!StringUtils.isBlank(prevSchoolMedium)) {
			studentPreviousSchoolInfo.setMedium(prevSchoolMedium.trim());
		}

		String prevSchoolResult = params[19];
		if (!StringUtils.isBlank(prevSchoolResult)) {
			studentPreviousSchoolInfo.setResult(prevSchoolResult.trim());
		}

		String prevSchoolYear = params[20];
		if (!StringUtils.isBlank(prevSchoolYear)) {
			Integer year = Integer.parseInt(prevSchoolYear);
			if (year != null && year > 0) {
				studentPreviousSchoolInfo.setYearOfPassing(year);
			}
		}

		// String rte = params[9];
		// if (!StringUtils.isBlank(rte)) {
		// if (rte.equalsIgnoreCase("YES")) {
		// studentBasicInfo.setRTE(true);
		// } else {
		// studentBasicInfo.setRTE(false);
		// }
		// }

		String motherName = params[2];
		String fatherName = params[3];

		if (!StringUtils.isBlank(motherName)) {
			studentFamilyInfo.setMothersName(motherName);
		}

		if (!StringUtils.isBlank(fatherName)) {
			studentFamilyInfo.setFathersName(fatherName);
		}

		String fatherProfession = params[22];
		if (!StringUtils.isBlank(fatherProfession)) {
			studentFamilyInfo.setFathersOccupation(fatherProfession.trim());
		}

		String montherProfession = params[23];
		if (!StringUtils.isBlank(montherProfession)) {
			studentFamilyInfo.setMothersOccupation(montherProfession.trim());
		}

		String className = params[6];
		if (StringUtils.isBlank(className)) {
			logger.error("Invalid standard for {}", name);
			return null;
		}
		Pair<Standard, String> standardPair = getStandardId(className.trim(), academicSession);
		if (standardPair == null) {
			logger.error("No standard with given name {} , for {}", className.trim(), name);
			return null;
		}
		Integer sectionId = null;
		if (StringUtils.isNotBlank(standardPair.getSecond())) {
			for (StandardSections standardSection : standardPair.getFirst().getStandardSectionList()) {
				if (standardSection.getSectionName().equals(standardPair.getSecond())) {
					sectionId = standardSection.getSectionId();
					break;
				}
			}
		}

		studentPayload.setStudentBasicInfo(studentBasicInfo);
		studentPayload.setStudentFamilyInfo(studentFamilyInfo);
		studentPayload.setStudentPreviousSchoolInfo(studentPreviousSchoolInfo);
		studentPayload.setStandardId(standardPair.getFirst().getStandardId());
		studentPayload.setSectionId(sectionId);
		studentPayload.setAdmissionAcademicSession(academicSession);
		studentPayload.setInstituteId(INSTITUTE_ID);

		boolean newAdmission = StringUtils.isBlank(params[0]) ? false : params[0].trim().equalsIgnoreCase("new");
		boolean hostler = registrationNumber.trim().split("/")[3].trim().equalsIgnoreCase("H");

		List<UUID> feeStructureIds = getStudentFeeStructure(studentPayload, newAdmission, hostler);
		RegisterStudentPayload registerStudentPayload = new RegisterStudentPayload();
		registerStudentPayload.setStudentPayload(studentPayload);
		registerStudentPayload.setFeeStructureIds(feeStructureIds);
		return registerStudentPayload;
	}

	private List<UUID> getStudentFeeStructure(StudentPayload studentPayload, boolean newAdmission, boolean hostler) {
		String key = "";
		if (hostler) {
			key = "H";
		} else {
			key = "D";
		}

		if (newAdmission) {
			key += "-N";
		} else {
			key += "-O";
		}
		List<UUID> feeStructuresIds = new ArrayList<>();

		List<String> feeStructures = STUDENT_FEE_STRUCTURE_MAPPING.get(key);
		logger.info("Fee structure being assinged to student {} for key {} are {}",
				studentPayload.getStudentBasicInfo().getName(), key, feeStructures);
		List<ResolvedDefaultEntityFeeAssignmentStructure> requiredResolvedDefaultEntityFeeAssignmentStructures = getDefaultFeeStructure(
				studentPayload.getInstituteId(), studentPayload.getAdmissionAcademicSession(),
				studentPayload.getStandardId(), new HashSet<>(feeStructures));
		for (ResolvedDefaultEntityFeeAssignmentStructure resolvedDefaultEntityFeeAssignmentStructure : requiredResolvedDefaultEntityFeeAssignmentStructures) {
			feeStructuresIds.add(resolvedDefaultEntityFeeAssignmentStructure.getStructureId());
			logger.info("Fee structure found for assingment to student {} for key {} are {}",
					studentPayload.getStudentBasicInfo().getName(), key,
					resolvedDefaultEntityFeeAssignmentStructure.getStructureName());
		}
		return feeStructuresIds;
	}

	private Pair<Standard, String> getStandardId(String className, int academicSession) {
		List<Standard> standards = instituteManager.getInstituteStandardList(INSTITUTE_ID, academicSession);
		if (CollectionUtils.isEmpty(standards)) {
			System.out.println("Invalid standards configured");
		}

		Map<String, Standard> standardNameMap = new HashMap<>();
		for (Standard standard : standards) {
			Stream stream = standard.getStream() == null ? Stream.NA : standard.getStream();
			if (stream == Stream.NA) {
				standardNameMap.put(standard.getStandardName().trim(), standard);
			} else {
				standardNameMap.put(standard.getStandardName().trim() + "|" + stream, standard);
			}
		}

		String[] str = className.split(" ");
		String classId = null;
		String section = null;
		if (className.equalsIgnoreCase("play group")) {
			classId = className;
		} else {
			if (str.length == 1) {
				classId = str[0].trim();
			} else {
				classId = str[0].trim();
				section = str[1].trim();
			}
		}

		if (classId.equalsIgnoreCase("LKG")) {
			if (!standardNameMap.containsKey(LKG)) {
				System.out.println("No " + LKG + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(LKG), section);
		} else if (classId.equalsIgnoreCase("UKG")) {
			if (!standardNameMap.containsKey(UKG)) {
				System.out.println("No " + UKG + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(UKG), section);
		} else if (classId.equalsIgnoreCase("NURSERY")) {
			if (!standardNameMap.containsKey(NURSERY)) {
				System.out.println("No " + NURSERY + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(NURSERY), section);
		} else if (classId.equalsIgnoreCase("PG")) {
			if (!standardNameMap.containsKey(PLAY_GROUP)) {
				System.out.println("No " + PLAY_GROUP + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(PLAY_GROUP), section);
		} else if (classId.equalsIgnoreCase("FIRST")) {
			if (!standardNameMap.containsKey(FIRST)) {
				System.out.println("No " + FIRST + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(FIRST), section);
		} else if (classId.equalsIgnoreCase("SECOND")) {
			if (!standardNameMap.containsKey(SECOND)) {
				System.out.println("No " + SECOND + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(SECOND), section);
		} else if (classId.equalsIgnoreCase("THIRD")) {
			if (!standardNameMap.containsKey(THIRD)) {
				System.out.println("No " + THIRD + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(THIRD), section);
		} else if (classId.equalsIgnoreCase("FOURTH")) {
			if (!standardNameMap.containsKey(FOURTH)) {
				System.out.println("No " + FOURTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(FOURTH), section);
		} else if (classId.equalsIgnoreCase("FIFTH")) {
			if (!standardNameMap.containsKey(FIFTH)) {
				System.out.println("No " + FIFTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(FIFTH), section);
		} else if (classId.equalsIgnoreCase("SIXTH")) {
			if (!standardNameMap.containsKey(SIXTH)) {
				System.out.println("No " + SIXTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(SIXTH), section);
		} else if (classId.equalsIgnoreCase("SEVENTH")) {
			if (!standardNameMap.containsKey(SEVENTH)) {
				System.out.println("No " + SEVENTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(SEVENTH), section);
		} else if (classId.equalsIgnoreCase("EIGHTH")) {
			if (!standardNameMap.containsKey(EIGHTH)) {
				System.out.println("No " + EIGHTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(EIGHTH), section);
		} else if (classId.equalsIgnoreCase("NINTH")) {
			if (!standardNameMap.containsKey(NINTH)) {
				System.out.println("No " + NINTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(NINTH), section);
		} else if (classId.equalsIgnoreCase("TENTH")) {
			if (!standardNameMap.containsKey(TENTH)) {
				System.out.println("No " + TENTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(TENTH), section);
		} else if (classId.equalsIgnoreCase("ELEVENTH")) {
			String standardName = ELEVENTH;
			if ("A".equalsIgnoreCase(section)) {
				standardName = ELEVENTH + "|" + "SCIENCE";
			} else if ("B".equalsIgnoreCase(section)) {
				standardName = ELEVENTH + "|" + "COMMERCE";
			}
			if (!standardNameMap.containsKey(standardName)) {
				System.out.println("No " + standardName + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(standardName), null);
		} else if (classId.equalsIgnoreCase("TWELFTH")) {
			String standardName = TWELFTH;
			if ("A".equalsIgnoreCase(section)) {
				standardName = TWELFTH + "|" + "SCIENCE";
			} else if ("B".equalsIgnoreCase(section)) {
				standardName = TWELFTH + "|" + "COMMERCE";
			}
			if (!standardNameMap.containsKey(standardName)) {
				System.out.println("No " + standardName + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(standardName), null);
		}
		return null;
	}
}
