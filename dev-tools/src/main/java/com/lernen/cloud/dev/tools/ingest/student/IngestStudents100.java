/**
 * 
 */
package com.lernen.cloud.dev.tools.ingest.student;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.ParseException;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.fees.FeeStructureType;
import com.lernen.cloud.core.api.fees.ResolvedDefaultEntityFeeAssignmentStructure;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.institute.Stream;
import com.lernen.cloud.core.api.student.EnrollStudentPayload;
import com.lernen.cloud.core.api.student.StudentBasicInfo;
import com.lernen.cloud.core.api.student.StudentFamilyInfo;
import com.lernen.cloud.core.api.student.StudentPayload;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.lib.fees.configuration.FeeConfigurationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentAdmissionManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;

/**
 * <AUTHOR>
 *
 */
public class IngestStudents100 {
	private static final Logger logger = LogManager.getLogger(IngestStudents100.class);

	private static final String FILE_PATH = "f";
	private static final String INSTITUTE_ID = "i";
	private static final String CURRENT_ACADEMIC_SESSION_ID = "cs";
	private static final String DELIMITER = "\\|";
	public static final String DATE_FORMAT = "dd/MM/yyyy";
	private static final String TEMP_REGISTRATION_NUMBER_SUFFIX = "-TEMP";
	
	public static final String FIRST = "Class I";
	public static final String SECOND = "Class II";
	public static final String THIRD = "Class III";
	public static final String FOURTH = "Class IV";
	public static final String FIFTH = "Class V";
	public static final String SIXTH = "Class VI";
	public static final String SEVENTH = "Class VII";
	public static final String EIGHTH = "Class VIII";
	public static final String NINTH = "Class IX";
	public static final String TENTH = "Class X";

	private final InstituteManager instituteManager;
	private final StudentManager studentManager;
	private final StudentAdmissionManager studentAdmissionManager;
	private final FeeConfigurationManager feeConfigurationManager;

	public IngestStudents100(InstituteManager instituteManager, StudentManager studentManager,
			StudentAdmissionManager studentAdmissionManager, FeeConfigurationManager feeConfigurationManager) {
		this.instituteManager = instituteManager;
		this.studentManager = studentManager;
		this.studentAdmissionManager = studentAdmissionManager;
		this.feeConfigurationManager = feeConfigurationManager;
	}

	public static void main(String[] args) {
		final ApplicationContext ctx = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
		final InstituteManager instituteManager = ctx.getBean(InstituteManager.class);
		final StudentManager studentManager = ctx.getBean(StudentManager.class);
		final StudentAdmissionManager studentAdmissionManager = ctx.getBean(StudentAdmissionManager.class);
		final FeeConfigurationManager feeConfigurationManager = ctx.getBean(FeeConfigurationManager.class);
		final IngestStudents100 ingestStudents100 = new IngestStudents100(instituteManager, studentManager,
				studentAdmissionManager, feeConfigurationManager);

		final Options options = buildOptions();

		CommandLine cmdLine = null;
		System.out.println("---------");
		try {
			final CommandLineParser parser = new DefaultParser();
			cmdLine = parser.parse(options, args);
		} catch (final ParseException pex) {
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("IngestStudents10080", options);
			System.exit(2);
		}

		if (!cmdLine.hasOption(FILE_PATH)) {
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("File path required", options);
		}
		String filePath = cmdLine.getOptionValue(FILE_PATH);

		Integer instituteId = null;
		if (cmdLine.hasOption(INSTITUTE_ID)) {
			instituteId = Integer.parseInt(cmdLine.getOptionValue(INSTITUTE_ID));
		} else {
			logger.error("No INSTITUTE_ID passed. Exitting");
			return;
		}

//		String inputMedium = null;
//		if (cmdLine.hasOption(MEDIUM)) {
//			inputMedium = cmdLine.getOptionValue(MEDIUM);
//		} else {
//			logger.error("No MEDIUM passed. Exitting");
//			return;
//		}

		int currentAcademicSessionId = 0;
		if (cmdLine.hasOption(CURRENT_ACADEMIC_SESSION_ID)) {
			currentAcademicSessionId = Integer.parseInt(cmdLine.getOptionValue(CURRENT_ACADEMIC_SESSION_ID));
		} else {
			logger.error("No current academicSessionId passed. Exitting");
			return;
		}
		System.out.println("---------");
		logger.info("Reading student data from file: {}", filePath);
		ingestStudents100.ingestStudents(filePath, instituteId, currentAcademicSessionId);
		logger.info("Ingested student data from file: {}", filePath);

	}

	private static Options buildOptions() {
		final Options options = new Options();
		options.addOption(new Option(FILE_PATH, true, "File Path"));
		options.addOption(new Option(INSTITUTE_ID, true, "Institute Id"));
		options.addOption(CURRENT_ACADEMIC_SESSION_ID, true, "Current academic session id for institute");
		return options;
	}

	private boolean ingestStudents(String filePath, Integer instituteId, int academicSession) {
		System.out.println(filePath);
		try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
			String line = "";
			boolean header = true;
			int count = 0;
			int error = 0;
			while ((line = br.readLine()) != null) {
				if (header) {
					header = false;
					continue;
				}
				count++;
				String[] params = line.split(DELIMITER, -1);
				try {
					
//					String medium = params[20];
//					
//					if (StringUtils.isBlank(medium)) {
//						logger.error("Invalid Student Info. Blank Medium {}. Skipping...", line);
//						error++;
//						continue;
//					}

//					if (!inputMedium.equalsIgnoreCase(medium)) {
//						logger.error("Medium is {}. Skipping as input is {} ", medium, inputMedium);
//						error++;
//						continue;
//					}

					StudentPayload studentPayload = getStudentPayload(params, instituteId, academicSession);
					
					if (studentPayload == null) {
						logger.error("Invalid Student Info {}. Skipping...", line);
						error++;
						continue;
					}
					UUID studentId = studentManager.addStudent(studentPayload);
					if (studentId == null) {
						logger.error("Unable to create Student {}. Skipping...", line);
						error++;
						continue;
					}

					EnrollStudentPayload enrollStudentPayload = new EnrollStudentPayload();
					enrollStudentPayload.setStudentId(studentId);
					enrollStudentPayload.setAdmissionNumber(studentPayload.getStudentBasicInfo().getAdmissionNumber());
					enrollStudentPayload.setFeeStructureIds(getEntollStudentFeeStructure(studentPayload));

					if (!studentAdmissionManager.admitStudent(instituteId, enrollStudentPayload, null).isSuccess()) {
						logger.error("Unable to admit Student {}", line);
						error++;
						continue;
					}
				} catch (Exception e) {
					logger.error("Exception while creating user line {}", line, e);
					error++;
					continue;
				}

			}
			logger.info("Total {}, error {}", count, error);
		} catch (Exception e) {
			logger.error("Exception ", e);
		}
		return false;
	}

	private StudentPayload getStudentPayload(String[] params, int instituteId, int academicSession) {
		StudentPayload studentPayload = new StudentPayload();
		StudentBasicInfo studentBasicInfo = new StudentBasicInfo();
		String admissionNumber = params[6];
		if (StringUtils.isBlank(admissionNumber)) {
			logger.error("Invalid admission number {}", admissionNumber);
			return null;
		}
		studentBasicInfo.setAdmissionNumber(admissionNumber.toString().trim());
		studentBasicInfo.setRegistrationNumber(admissionNumber.trim() + TEMP_REGISTRATION_NUMBER_SUFFIX);
		studentBasicInfo.setRegistrationRequestNumber(studentBasicInfo.getRegistrationNumber());

		String studentName = params[1];
		if (StringUtils.isBlank(studentName)) {
			logger.error("Invalid student name");
			return null;
		}
		studentBasicInfo.setName(StringUtils.capitalize(studentName.trim()));

		logger.info(studentName);

		String dob = params[9];
		if (!StringUtils.isBlank(dob)) {
			int dobTimestamp = DateUtils.getTimestampFromDate(dob.trim(), User.DFAULT_TIMEZONE, DATE_FORMAT);
			studentBasicInfo.setDateOfBirth(dobTimestamp);
		}

		String gender = params[8];
		if (!StringUtils.isBlank(gender)) {
			if (gender.trim().equalsIgnoreCase("Boy")) {
				studentBasicInfo.setGender(Gender.MALE);
			} else if (gender.trim().equalsIgnoreCase("Girl")) {
				studentBasicInfo.setGender(Gender.FEMALE);
			}
		}

		String category = params[7];
		if (!StringUtils.isBlank(category)) {
			if (category.equalsIgnoreCase("GENERAL")) {
				studentBasicInfo.setUserCategory(UserCategory.GENERAL);
			} else if (category.equalsIgnoreCase("OBC")) {
				studentBasicInfo.setUserCategory(UserCategory.OBC);

			} else if (category.equalsIgnoreCase("SC")) {
				studentBasicInfo.setUserCategory(UserCategory.SC);

			} else if (category.equalsIgnoreCase("ST")) {
				studentBasicInfo.setUserCategory(UserCategory.ST);

			} else if (category.equalsIgnoreCase("SBC")) {
				studentBasicInfo.setUserCategory(UserCategory.SBC);

			} else {
				studentBasicInfo.setUserCategory(UserCategory.OTHER);

			}
		}

//		String religion = params[7];
//		if (!StringUtils.isBlank(religion)) {
//			studentBasicInfo.setReligion(StringUtils.capitalize(religion.trim()));
//		}
		
		String rte = params[10];
		if (!StringUtils.isBlank(rte)) {
			if (rte.equalsIgnoreCase("yes")) {
				studentBasicInfo.setRte(true);
			} else {
				studentBasicInfo.setRte(false);
			}
		}

//		 String address = params[9];
//		 if (!StringUtils.isBlank(address)) {
//		 studentBasicInfo.setPermanentAddress(StringUtils.capitalize(address));
//		 }

//
//		String motherTongue = params[8];
//		if (!StringUtils.isBlank(motherTongue)) {
//			studentBasicInfo.setMotherTongue(StringUtils.capitalize(motherTongue.trim()));
//		}

//		String areaType = params[9];
//		if (!StringUtils.isBlank(areaType)) {
//			studentBasicInfo.setAreaType(AreaType.getAreaType(areaType.trim()));
//
//		}

//		String city = params[10];
//		if (!StringUtils.isBlank(city)) {
//			studentBasicInfo.setPermanentCity(StringUtils.capitalize(city));
//		}

//		String admissionDate = params[11];
//		if (!StringUtils.isBlank(admissionDate)) {
//			int admissionDateTimestamp = DateUtils.getTimestampFromDate(admissionDate.trim(), User.DFAULT_TIMEZONE,
//					DATE_FORMAT);
//			studentBasicInfo.setAdmissionDate(admissionDateTimestamp);
//		}
//		String bpl = params[13];
//		if (!StringUtils.isBlank(bpl)) {
//			if (bpl.equalsIgnoreCase("yes")) {
//				studentBasicInfo.setBpl(true);
//			} else {
//				studentBasicInfo.setBpl(false);
//			}
//		}

//		String specialAbility = params[14];
//		if (!StringUtils.isBlank(specialAbility)) {
//			if (specialAbility.equalsIgnoreCase("yes")) {
//				studentBasicInfo.setSpeciallyAbled(true);
//			} else {
//				studentBasicInfo.setSpeciallyAbled(false);
//			}
//		}
		
//		 String state = params[11];
//		 if (!StringUtils.isBlank(state)) {
//			 studentBasicInfo.setPermanentState(state);
//		 }

//		 String zipcode = params[12];
//		 	if (!StringUtils.isBlank(zipcode)) {
//		 		studentBasicInfo.setPermanentZipcode(zipcode);
//		 }

		StudentFamilyInfo studentFamilyInfo = new StudentFamilyInfo();
		String fatherName = params[2];
		String motherName = params[3];
		
		if (!StringUtils.isBlank(motherName)) {
			studentFamilyInfo.setMothersName(StringUtils.capitalize(motherName));
		}

		if (!StringUtils.isBlank(fatherName)) {
			studentFamilyInfo.setFathersName(StringUtils.capitalize(fatherName));
		}

//		String contactNumbers = params[4];
//		if (!StringUtils.isBlank(contactNumbers)) {
//			studentBasicInfo.setPrimaryContactNumber(contactNumbers);
//		}
		
//		String alternateContactNumbers = params[4];
//		if (!StringUtils.isBlank(alternateContactNumbers)) {
//			studentFamilyInfo.setFathersContactNumber(alternateContactNumbers);
//		}
		
//		StudentPreviousSchoolInfo studentPreviousSchoolInfo = new StudentPreviousSchoolInfo();
//		String prevClass = params[17];
//		if (!StringUtils.isBlank(prevClass) && !prevClass.equalsIgnoreCase("none")) {
//			studentPreviousSchoolInfo.setClassPassed(prevClass);
//		}
//
//		String prevSchool = params[18];
//		if (!StringUtils.isBlank(prevSchool) && !prevClass.equalsIgnoreCase("NOT APPLICABLE")) {
//			studentPreviousSchoolInfo.setSchoolName(prevSchool);
//		}

		String className = params[4];
		if (StringUtils.isBlank(className)) {
			logger.error("Invalid standard");
			return null;
		}

//		String section = params[5];
//		if (StringUtils.isBlank(section)) {
//			logger.error("Invalid section");
//			section = null;
//		}
		
//		String stream = params[6];
//		if (StringUtils.isBlank(stream)) {
//			logger.error("Invalid stream");
//			stream = null;
//		}

		Pair<Standard, String> standardPair = getStandardId(instituteId, className.trim(), null, null, academicSession);
		if (standardPair == null) {
			logger.error("No standard with given name");
			return null;
		}
		Integer sectionId = null;
		if (StringUtils.isNotBlank(standardPair.getSecond())) {
			for (StandardSections standardSection : standardPair.getFirst().getStandardSectionList()) {
				if (standardSection.getSectionName().equals(standardPair.getSecond())) {
					sectionId = standardSection.getSectionId();
					break;
				}
			}
		}

		studentPayload.setStudentBasicInfo(studentBasicInfo);
		studentPayload.setStudentFamilyInfo(studentFamilyInfo);
		studentPayload.setStandardId(standardPair.getFirst().getStandardId());
		studentPayload.setSectionId(sectionId);
		studentPayload.setAdmissionAcademicSession(academicSession);
		studentPayload.setInstituteId(instituteId);
		return studentPayload;
	}

	private List<UUID> getEntollStudentFeeStructure(StudentPayload studentPayload) {

		List<UUID> feeStructuresIds = new ArrayList<>();

		List<ResolvedDefaultEntityFeeAssignmentStructure> requiredResolvedDefaultEntityFeeAssignmentStructures = getDefaultFeeStructure(
				studentPayload.getInstituteId(), studentPayload.getAdmissionAcademicSession(),
				studentPayload.getStandardId());
		for (ResolvedDefaultEntityFeeAssignmentStructure resolvedDefaultEntityFeeAssignmentStructure : requiredResolvedDefaultEntityFeeAssignmentStructures) {
			feeStructuresIds.add(resolvedDefaultEntityFeeAssignmentStructure.getStructureId());
			logger.info("Fee structure found for assingment to student {} are {}",
					studentPayload.getStudentBasicInfo().getName(),
					resolvedDefaultEntityFeeAssignmentStructure.getStructureName());
		}
		return feeStructuresIds;
	}

	private List<ResolvedDefaultEntityFeeAssignmentStructure> getDefaultFeeStructure(int instituteId,
			int academicSessionId, UUID standardId) {
		List<ResolvedDefaultEntityFeeAssignmentStructure> resolvedDefaultEntityFeeAssignmentStructures = feeConfigurationManager
				.getDefaultFeeAssignmentStructure(instituteId, academicSessionId, standardId,
						Arrays.asList(FeeStructureType.ENROLLMENT), false);
		return resolvedDefaultEntityFeeAssignmentStructures;

	}

	private Pair<Standard, String> getStandardId(int instituteId, String classId, String streamFinal, String section, int academicSession) {
		List<Standard> standards = instituteManager.getInstituteStandardList(instituteId, academicSession);
		if (CollectionUtils.isEmpty(standards)) {
			logger.error("Invalid standards configured");
			return null;
		}

		Map<String, Standard> standardNameMap = new HashMap<>();
		for (Standard standard : standards) {
			Stream stream = standard.getStream() == null ? Stream.NA : standard.getStream();
			if (stream == Stream.NA) {
				standardNameMap.put(standard.getStandardName().trim(), standard);
			} else {
				standardNameMap.put(standard.getStandardName().trim() + "|" + stream, standard);
			}
		}

		logger.info("Class = " + classId + ", Stream = " + streamFinal + ", Section = " + section);
		//excel compare
		if (classId.equalsIgnoreCase("First")) {
			if (!standardNameMap.containsKey(FIRST)) {
				logger.info("No " + FIRST + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(FIRST), section);
		} else if (classId.equalsIgnoreCase("Second")) {
			if (!standardNameMap.containsKey(SECOND)) {
				logger.info("No " + SECOND + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(SECOND), section);
		} else if (classId.equalsIgnoreCase("THIRD")) {
			if (!standardNameMap.containsKey(THIRD)) {
				logger.info("No " + THIRD + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(THIRD), section);
		} else if (classId.equalsIgnoreCase("FOURTH")) {
			if (!standardNameMap.containsKey(FOURTH)) {
				logger.info("No " + FOURTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(FOURTH), section);
		} else if (classId.equalsIgnoreCase("FIFTH")) {
			if (!standardNameMap.containsKey(FIFTH)) {
				logger.info("No " + FIFTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(FIFTH), section);
		} else if (classId.equalsIgnoreCase("SIXTH")) {
			if (!standardNameMap.containsKey(SIXTH)) {
				logger.info("No " + SIXTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(SIXTH), section);
		} else if (classId.equalsIgnoreCase("SEVENTH")) {
			if (!standardNameMap.containsKey(SEVENTH)) {
				logger.info("No " + SEVENTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(SEVENTH), section);
		} else if (classId.equalsIgnoreCase("Eigth")) {
			if (!standardNameMap.containsKey(EIGHTH)) {
				logger.info("No " + EIGHTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(EIGHTH), section);
		}else if (classId.equalsIgnoreCase("Ninth")) {
			if (!standardNameMap.containsKey(NINTH)) {
				logger.info("No " + NINTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(NINTH), section);
		} else if (classId.equalsIgnoreCase("Tenth")) {
			if (!standardNameMap.containsKey(TENTH)) {
				logger.info("No " + TENTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(TENTH), section);
		}
		return null;
	}
}
