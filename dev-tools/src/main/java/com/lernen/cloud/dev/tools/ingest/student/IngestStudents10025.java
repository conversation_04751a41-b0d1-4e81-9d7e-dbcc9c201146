package com.lernen.cloud.dev.tools.ingest.student;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.ParseException;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.fees.FeeStructureType;
import com.lernen.cloud.core.api.fees.ResolvedDefaultEntityFeeAssignmentStructure;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.institute.Stream;
import com.lernen.cloud.core.api.student.EnrollStudentPayload;
import com.lernen.cloud.core.api.student.StudentBasicInfo;
import com.lernen.cloud.core.api.student.StudentFamilyInfo;
import com.lernen.cloud.core.api.student.StudentPayload;
import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.fees.configuration.FeeConfigurationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentAdmissionManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;

/**
 * 
 * <AUTHOR>
 *
 */
public class IngestStudents10025 {
	private static final Logger logger = LogManager.getLogger(IngestStudents10025.class);

	private static final String FILE_PATH = "f";
	private static final String CURRENT_ACADEMIC_SESSION_ID = "cs";
	private static final String DELIMITER = "\\|";
	public static final String DATE_FORMAT = "dd/MM/yyyy";
	private static final String TEMP_REGISTRATION_NUMBER_SUFFIX = "-TEMP";

	private static final int INSTITUTE_ID = 10025;

	public static final String LKG = "LKG";
	public static final String UKG = "UKG";
	public static final String FIRST = "First";
	public static final String SECOND = "Second";
	public static final String THIRD = "Third";
	public static final String FOURTH = "Fourth";
	public static final String FIFTH = "Fifth";
	public static final String SIXTH = "Sixth";
	public static final String SEVENTH = "Seventh";
	public static final String EIGHTH = "Eigth";
	public static final String NINTH = "Ninth";
	public static final String TENTH = "Tenth";

	private final InstituteManager instituteManager;
	private final StudentManager studentManager;
	private final StudentAdmissionManager studentAdmissionManager;
	private final FeeConfigurationManager feeConfigurationManager;

	public IngestStudents10025(InstituteManager instituteManager, StudentManager studentManager,
			StudentAdmissionManager studentAdmissionManager, FeeConfigurationManager feeConfigurationManager) {
		this.instituteManager = instituteManager;
		this.studentManager = studentManager;
		this.studentAdmissionManager = studentAdmissionManager;
		this.feeConfigurationManager = feeConfigurationManager;
	}

	public static void main(String[] args) {
		final ApplicationContext ctx = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
		final InstituteManager instituteManager = ctx.getBean(InstituteManager.class);
		final StudentManager studentManager = ctx.getBean(StudentManager.class);
		final StudentAdmissionManager studentAdmissionManager = ctx.getBean(StudentAdmissionManager.class);
		final FeeConfigurationManager feeConfigurationManager = ctx.getBean(FeeConfigurationManager.class);
		final IngestStudents10025 ingestStudents10025 = new IngestStudents10025(instituteManager, studentManager,
				studentAdmissionManager, feeConfigurationManager);

		final Options options = buildOptions();

		CommandLine cmdLine = null;
		try {
			final CommandLineParser parser = new DefaultParser();
			cmdLine = parser.parse(options, args);
		} catch (final ParseException pex) {
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("Ingeststudents10025", options);
			System.exit(2);
		}

		if (!cmdLine.hasOption(FILE_PATH)) {
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("File path required", options);
		}
		String filePath = cmdLine.getOptionValue(FILE_PATH);

		int currentAcademicSessionId = 0;
		if (cmdLine.hasOption(CURRENT_ACADEMIC_SESSION_ID)) {
			currentAcademicSessionId = Integer.parseInt(cmdLine.getOptionValue(CURRENT_ACADEMIC_SESSION_ID));
		} else {
			logger.error("No current academicSessionId passed. Exitting");
			return;
		}
		logger.info("Reading student data from file: {}", filePath);
		ingestStudents10025.ingestStudents(filePath, currentAcademicSessionId);
		logger.info("Ingested student data from file: {}", filePath);

	}

	private static Options buildOptions() {
		final Options options = new Options();
		options.addOption(new Option(FILE_PATH, true, "File Path"));
		options.addOption(CURRENT_ACADEMIC_SESSION_ID, true, "Current academic session id for institute");
		return options;
	}

	private boolean ingestStudents(String filePath, int academicSession) {
		try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
			String line = "";
			boolean header = true;
			int count = 0;
			int error = 0;
			while ((line = br.readLine()) != null) {
				if (header) {
					header = false;
					continue;
				}
				count++;
				String[] params = line.split(DELIMITER, -1);
				try {
					StudentPayload studentPayload = getStudentPayload(params, academicSession);
					if (studentPayload == null) {
						logger.error("Invalid Student Info {}. Skipping...", line);
						error++;
						continue;
					}
					UUID studentId = studentManager.addStudent(studentPayload);

					if (studentId == null) {
						logger.error("Unable to create Student {}. Skipping...", line);
						error++;
						continue;
					}

					EnrollStudentPayload enrollStudentPayload = new EnrollStudentPayload();
					enrollStudentPayload.setStudentId(studentId);
					enrollStudentPayload.setAdmissionNumber(studentPayload.getStudentBasicInfo().getAdmissionNumber());
					enrollStudentPayload.setFeeStructureIds(getEntollStudentFeeStructure(studentPayload));

					if (!studentAdmissionManager.admitStudent(INSTITUTE_ID, enrollStudentPayload, null).isSuccess()) {
						logger.error("Unable to admit Student {}",line);
						error++;
						continue;
					}
				}catch(Exception e) {
					logger.error("Exception while creating user line {}", line, e);
					error++;
					continue;
				}
				
			}
			logger.info("Total {}, error {}", count, error);
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	private StudentPayload getStudentPayload(String[] params, int academicSession) {
		StudentPayload studentPayload = new StudentPayload();
		StudentBasicInfo studentBasicInfo = new StudentBasicInfo();
		String admissionNumber = params[6];
		if (StringUtils.isBlank(admissionNumber)) {
			logger.error("Invalid admission number {}", admissionNumber);
			return null;
		}
		studentBasicInfo.setAdmissionNumber(admissionNumber.toString().trim());
		studentBasicInfo.setRegistrationNumber(admissionNumber.trim() + TEMP_REGISTRATION_NUMBER_SUFFIX);

		String studentName = params[1];
		if (StringUtils.isBlank(studentName)) {
			logger.error("Invalid student name");
			return null;
		}
		studentBasicInfo.setName(StringUtils.capitalize(studentName.trim()));

		logger.info(studentName);

//		String admissionDate = params[2];
//		if (!StringUtils.isBlank(admissionDate)) {
//			int admissionDateTimestamp = DateUtils.getTimestampFromDate(admissionDate.trim(), User.DFAULT_TIMEZONE,
//					DATE_FORMAT);
//			studentBasicInfo.setAdmissionDate(admissionDateTimestamp);
//		}

		String dob = params[9];
		if (!StringUtils.isBlank(dob)) {
			int dobTimestamp = DateUtils.getTimestampFromDate(dob.trim(), User.DFAULT_TIMEZONE, DATE_FORMAT);
			studentBasicInfo.setDateOfBirth(dobTimestamp);
		}

		String gender = params[8];
		if (!StringUtils.isBlank(gender)) {
			if (gender.equalsIgnoreCase("boy")) {
				studentBasicInfo.setGender(Gender.MALE);
			} else if (gender.equalsIgnoreCase("girl")) {
				studentBasicInfo.setGender(Gender.FEMALE);
			}
		}

		String category = params[7];
		if (!StringUtils.isBlank(category)) {
			if (category.equalsIgnoreCase("GENERAL")) {
				studentBasicInfo.setUserCategory(UserCategory.GENERAL);
			} else if (category.equalsIgnoreCase("obc")) {
				studentBasicInfo.setUserCategory(UserCategory.OBC);

			} else if (category.equalsIgnoreCase("sc")) {
				studentBasicInfo.setUserCategory(UserCategory.SC);

			} else if (category.equalsIgnoreCase("st")) {
				studentBasicInfo.setUserCategory(UserCategory.ST);

			} else {
				studentBasicInfo.setUserCategory(UserCategory.OTHER);

			}
		}

		// String religion = params[9];
		// if (!StringUtils.isBlank(religion)) {
		// studentBasicInfo.setReligion(StringUtils.capitalize(religion.trim()));
		// }

		String rte = params[10];
		if (!StringUtils.isBlank(rte)) {
			if (rte.equalsIgnoreCase("yes")) {
				studentBasicInfo.setRte(true);
			} else {
				studentBasicInfo.setRte(false);
			}
		}

		// String address = params[11];
		// if (!StringUtils.isBlank(address)) {
		// studentBasicInfo.setPermanentAddress(StringUtils.capitalize(address));
		// }

		// String city = params[12];
		// if (!StringUtils.isBlank(city)) {
		// studentBasicInfo.setCity(StringUtils.capitalize(city));
		// }

		studentBasicInfo.setPermanentState("Rajasthan");
		// String state = params[13];
		// if (!StringUtils.isBlank(state)) {
		// if (state.toLowerCase().contains("raj")) {
		// studentBasicInfo.setState("Rajasthan");
		// }
		// }

		// String zipcode = params[14];
		// if (!StringUtils.isBlank(zipcode)) {
		// studentBasicInfo.setZipcode(zipcode);
		// }

		StudentFamilyInfo studentFamilyInfo = new StudentFamilyInfo();
		String motherName = params[3];
		String fatherName = params[2];

		if (!StringUtils.isBlank(motherName)) {
			studentFamilyInfo.setMothersName(StringUtils.capitalize(motherName));
		}

		if (!StringUtils.isBlank(fatherName)) {
			studentFamilyInfo.setFathersName(StringUtils.capitalize(fatherName));
		}

//		String contactNumbers = params[7];
//		if (!StringUtils.isBlank(contactNumbers)) {
//			String[] numbers = contactNumbers.split("/");
//			String fatherContactNumber = numbers[0];
//			String motherContactNumber = null;
//			if (numbers.length >= 2) {
//				motherContactNumber = numbers[1];
//			}
//
//			if (!StringUtils.isBlank(motherContactNumber)) {
//				studentFamilyInfo.setMothersContactNumber(motherContactNumber);
//			}
//
//			if (!StringUtils.isBlank(fatherContactNumber)) {
//				studentBasicInfo.setPrimaryContactNumber(fatherContactNumber);
//				studentFamilyInfo.setFathersContactNumber(fatherContactNumber);
//			}
//
//		}

		String className = params[4];
		if (StringUtils.isBlank(className)) {
			logger.error("Invalid standard");
			return null;
		}

		Pair<Standard, String> standardPair = getStandardId(className.trim(), academicSession);
		if (standardPair == null) {
			logger.error("No standard with given name");
			return null;
		}
		Integer sectionId = null;
		if (StringUtils.isNotBlank(standardPair.getSecond())) {
			for (StandardSections standardSection : standardPair.getFirst().getStandardSectionList()) {
				if (standardSection.getSectionName().equals(standardPair.getSecond())) {
					sectionId = standardSection.getSectionId();
					break;
				}
			}
		}

		studentPayload.setStudentBasicInfo(studentBasicInfo);
		studentPayload.setStudentFamilyInfo(studentFamilyInfo);
		studentPayload.setStandardId(standardPair.getFirst().getStandardId());
		studentPayload.setSectionId(sectionId);
		studentPayload.setAdmissionAcademicSession(academicSession);
		studentPayload.setInstituteId(INSTITUTE_ID);
		return studentPayload;
	}

	private List<UUID> getEntollStudentFeeStructure(StudentPayload studentPayload) {

		List<UUID> feeStructuresIds = new ArrayList<>();

		List<ResolvedDefaultEntityFeeAssignmentStructure> requiredResolvedDefaultEntityFeeAssignmentStructures = getDefaultFeeStructure(
				studentPayload.getInstituteId(), studentPayload.getAdmissionAcademicSession(),
				studentPayload.getStandardId());
		for (ResolvedDefaultEntityFeeAssignmentStructure resolvedDefaultEntityFeeAssignmentStructure : requiredResolvedDefaultEntityFeeAssignmentStructures) {
			feeStructuresIds.add(resolvedDefaultEntityFeeAssignmentStructure.getStructureId());
			logger.info("Fee structure found for assingment to student {} are {}",
					studentPayload.getStudentBasicInfo().getName(),
					resolvedDefaultEntityFeeAssignmentStructure.getStructureName());
		}
		return feeStructuresIds;
	}

	private List<ResolvedDefaultEntityFeeAssignmentStructure> getDefaultFeeStructure(int instituteId,
			int academicSessionId, UUID standardId) {

		List<ResolvedDefaultEntityFeeAssignmentStructure> requiredResolvedDefaultEntityFeeAssignmentStructures = new ArrayList<>();
		List<ResolvedDefaultEntityFeeAssignmentStructure> resolvedDefaultEntityFeeAssignmentStructures = feeConfigurationManager
				.getDefaultFeeAssignmentStructure(instituteId, academicSessionId, standardId,
						Arrays.asList(FeeStructureType.ENROLLMENT), false);
		return resolvedDefaultEntityFeeAssignmentStructures;

	}

	private Pair<Standard, String> getStandardId(String className, int academicSession) {
		List<Standard> standards = instituteManager.getInstituteStandardList(INSTITUTE_ID, academicSession);
		if (CollectionUtils.isEmpty(standards)) {
			logger.error("Invalid standards configured");
		}

		Map<String, Standard> standardNameMap = new HashMap<>();
		for (Standard standard : standards) {
			Stream stream = standard.getStream() == null ? Stream.NA : standard.getStream();
			if (stream == Stream.NA) {
				standardNameMap.put(standard.getStandardName().trim(), standard);
			} else {
				standardNameMap.put(standard.getStandardName().trim() + "|" + stream, standard);
			}
		}

		String[] str = className.split("-", -1);
		String classId = null;
		String stream = null;
		String section = null;

		if (str.length == 1) {
			classId = str[0].trim();
		} else if (str.length == 2) {
			classId = str[0].trim();
			section = str[1].trim();
		} else {
			classId = str[0].trim();
			stream = str[1].trim();
			section = str[2].trim();
		}

		logger.info("Class = " + classId + ", Stream = " + stream + ", Section = " + section);
		if (classId.equalsIgnoreCase("LKG")) {
			if (!standardNameMap.containsKey(LKG)) {
				logger.info("No " + LKG + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(LKG), section);
		} else if (classId.equalsIgnoreCase("UKG")) {
			if (!standardNameMap.containsKey(UKG)) {
				logger.info("No " + UKG + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(UKG), section);
		} else if (classId.equalsIgnoreCase(FIRST)) {
			if (!standardNameMap.containsKey(FIRST)) {
				logger.info("No " + FIRST + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(FIRST), section);
		} else if (classId.equalsIgnoreCase(SECOND)) {
			if (!standardNameMap.containsKey(SECOND)) {
				logger.info("No " + SECOND + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(SECOND), section);
		} else if (classId.equalsIgnoreCase(THIRD)) {
			if (!standardNameMap.containsKey(THIRD)) {
				logger.info("No " + THIRD + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(THIRD), section);
		} else if (classId.equalsIgnoreCase(FOURTH)) {
			if (!standardNameMap.containsKey(FOURTH)) {
				logger.info("No " + FOURTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(FOURTH), section);
		} else if (classId.equalsIgnoreCase(FIFTH)) {
			if (!standardNameMap.containsKey(FIFTH)) {
				logger.info("No " + FIFTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(FIFTH), section);
		} else if (classId.equalsIgnoreCase(SIXTH)) {
			if (!standardNameMap.containsKey(SIXTH)) {
				logger.info("No " + SIXTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(SIXTH), section);
		} else if (classId.equalsIgnoreCase(SEVENTH)) {
			if (!standardNameMap.containsKey(SEVENTH)) {
				logger.info("No " + SEVENTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(SEVENTH), section);
		} else if (classId.equalsIgnoreCase(EIGHTH)) {
			if (!standardNameMap.containsKey(EIGHTH)) {
				logger.info("No " + EIGHTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(EIGHTH), section);
		} else if (classId.equalsIgnoreCase(NINTH)) {
			if (!standardNameMap.containsKey(NINTH)) {
				logger.info("No " + NINTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(NINTH), section);
		} else if (classId.equalsIgnoreCase(TENTH)) {
			if (!standardNameMap.containsKey(TENTH)) {
				logger.info("No " + TENTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(TENTH), section);
		}

		return null;
	}
}
