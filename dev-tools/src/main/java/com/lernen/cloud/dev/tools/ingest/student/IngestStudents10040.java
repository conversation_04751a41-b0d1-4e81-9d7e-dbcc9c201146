package com.lernen.cloud.dev.tools.ingest.student;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.ParseException;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.fees.FeeStructureType;
import com.lernen.cloud.core.api.fees.ResolvedDefaultEntityFeeAssignmentStructure;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.institute.Stream;
import com.lernen.cloud.core.api.student.EnrollStudentPayload;
import com.lernen.cloud.core.api.student.StudentBasicInfo;
import com.lernen.cloud.core.api.student.StudentFamilyInfo;
import com.lernen.cloud.core.api.student.StudentPayload;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.lib.fees.configuration.FeeConfigurationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentAdmissionManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;

/**
 * 
 * <AUTHOR>
 *
 */
public class IngestStudents10040 {
	private static final Logger logger = LogManager.getLogger(IngestStudents10040.class);

	private static final String FILE_PATH = "f";
	private static final String CURRENT_ACADEMIC_SESSION_ID = "cs";
	private static final String DELIMITER = "\\|";
	public static final String DATE_FORMAT = "dd/MM/yyyy";
	private static final String TEMP_REGISTRATION_NUMBER_SUFFIX = "-TEMP";

	private static final String INSTITUTE_ID = "i";

	public static final String PLAY = "play";
	public static final String NURSERY = "nursery";
	public static final String LKG = "lkg";
	public static final String UKG = "ukg";
	public static final String FIRST = "1st";
	public static final String SECOND = "2nd";
	public static final String THIRD = "3rd";
	public static final String FOURTH = "4th";
	public static final String FIFTH = "5th";
	public static final String SIXTH = "6th";
	public static final String SEVENTH = "7th";
	public static final String EIGHTH = "8th";

	private final InstituteManager instituteManager;
	private final StudentManager studentManager;
	private final StudentAdmissionManager studentAdmissionManager;
	private final FeeConfigurationManager feeConfigurationManager;

	public IngestStudents10040(InstituteManager instituteManager, StudentManager studentManager,
			StudentAdmissionManager studentAdmissionManager, FeeConfigurationManager feeConfigurationManager) {
		this.instituteManager = instituteManager;
		this.studentManager = studentManager;
		this.studentAdmissionManager = studentAdmissionManager;
		this.feeConfigurationManager = feeConfigurationManager;
	}

	public static void main(String[] args) {
		final ApplicationContext ctx = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
		final InstituteManager instituteManager = ctx.getBean(InstituteManager.class);
		final StudentManager studentManager = ctx.getBean(StudentManager.class);
		final StudentAdmissionManager studentAdmissionManager = ctx.getBean(StudentAdmissionManager.class);
		final FeeConfigurationManager feeConfigurationManager = ctx.getBean(FeeConfigurationManager.class);
		final IngestStudents10040 ingestStudents10040 = new IngestStudents10040(instituteManager, studentManager,
				studentAdmissionManager, feeConfigurationManager);

		final Options options = buildOptions();

		CommandLine cmdLine = null;
		try {
			final CommandLineParser parser = new DefaultParser();
			cmdLine = parser.parse(options, args);
		} catch (final ParseException pex) {
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("IngestStudents10040", options);
			System.exit(2);
		}

		if (!cmdLine.hasOption(FILE_PATH)) {
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("File path required", options);
		}
		String filePath = cmdLine.getOptionValue(FILE_PATH);

		int currentAcademicSessionId = 0;
		if (cmdLine.hasOption(CURRENT_ACADEMIC_SESSION_ID)) {
			currentAcademicSessionId = Integer.parseInt(cmdLine.getOptionValue(CURRENT_ACADEMIC_SESSION_ID));
		} else {
			logger.error("No current academicSessionId passed. Exitting");
			return;
		}
		int instituteId = Integer.parseInt(cmdLine.getOptionValue(INSTITUTE_ID));
		logger.info("Reading student data from file: {}", filePath);
		ingestStudents10040.ingestStudents(filePath, currentAcademicSessionId, instituteId);
		logger.info("Ingested student data from file: {}", filePath);

	}

	private static Options buildOptions() {
		final Options options = new Options();
		options.addOption(new Option(FILE_PATH, true, "File Path"));
		options.addOption(CURRENT_ACADEMIC_SESSION_ID, true, "Current academic session id for institute");
		options.addOption(INSTITUTE_ID, true, "Current institute");
		return options;
	}

	private boolean ingestStudents(String filePath, int academicSession, int instituteId) {
		try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
			String line = "";
			boolean header = true;
			int count = 0;
			int error = 0;
			while ((line = br.readLine()) != null) {
				if (header) {
					header = false;
					continue;
				}
				count++;
				String[] params = line.split(DELIMITER, -1);
				try {
					StudentPayload studentPayload = getStudentPayload(params, academicSession, instituteId);
					if (studentPayload == null) {
						logger.error("Invalid Student Info {}. Skipping...", line);
						error++;
						continue;
					}
					UUID studentId = studentManager.addStudent(studentPayload);

					if (studentId == null) {
						logger.error("Unable to create Student {}. Skipping...", line);
						error++;
						continue;
					}

					EnrollStudentPayload enrollStudentPayload = new EnrollStudentPayload();
					enrollStudentPayload.setStudentId(studentId);
					enrollStudentPayload.setAdmissionNumber(studentPayload.getStudentBasicInfo().getAdmissionNumber());
					enrollStudentPayload.setFeeStructureIds(getEntollStudentFeeStructure(studentPayload));

					if (!studentAdmissionManager.admitStudent(instituteId, enrollStudentPayload, null).isSuccess()) {
						logger.error("Unable to admit Student {}", line);
						error++;
						continue;
					}
				} catch (Exception e) {
					logger.error("Exception while creating user line {}", line, e);
					error++;
					continue;
				}

			}
			logger.info("Total {}, error {}", count, error);
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	private StudentPayload getStudentPayload(String[] params, int academicSession, int instituteId) {
		StudentPayload studentPayload = new StudentPayload();
		StudentBasicInfo studentBasicInfo = new StudentBasicInfo();
		String admissionNumber = params[0];
		if (StringUtils.isBlank(admissionNumber)) {
			logger.error("Invalid admission number {}", admissionNumber);
			return null;
		}
		studentBasicInfo.setAdmissionNumber(admissionNumber.toString().trim());
		studentBasicInfo.setRegistrationNumber(admissionNumber.trim() + TEMP_REGISTRATION_NUMBER_SUFFIX);

		String studentName = params[1];
		if (StringUtils.isBlank(studentName)) {
			logger.error("Invalid student name");
			return null;
		}
		studentBasicInfo.setName(StringUtils.capitalize(studentName.trim()));

		logger.info(studentName);

		String admissionDate = params[2];
		if (!StringUtils.isBlank(admissionDate)) {
			int admissionDateTimestamp = DateUtils.getTimestampFromDate(admissionDate.trim(), User.DFAULT_TIMEZONE,
					DATE_FORMAT);
			studentBasicInfo.setAdmissionDate(admissionDateTimestamp);
		}

		String dob = params[3];
		if (!StringUtils.isBlank(dob)) {
			int dobTimestamp = DateUtils.getTimestampFromDate(dob.trim(), User.DFAULT_TIMEZONE, DATE_FORMAT);
			studentBasicInfo.setDateOfBirth(dobTimestamp);
		}

		String gender = params[4];
		if (!StringUtils.isBlank(gender)) {
			if (gender.equalsIgnoreCase("MALE")) {
				studentBasicInfo.setGender(Gender.MALE);
			} else if (gender.equalsIgnoreCase("FEMALE")) {
				studentBasicInfo.setGender(Gender.FEMALE);
			}
		}

		String religion = params[9];
		if (!StringUtils.isBlank(religion)) {
			studentBasicInfo.setReligion(religion.trim());
		}

//		String aadharNumber = params[8];
//		studentBasicInfo.setAadharNumber(aadharNumber);

		String category = params[7];
		if (!StringUtils.isBlank(category)) {
			if (category.equalsIgnoreCase("GEN")) {
				studentBasicInfo.setUserCategory(UserCategory.GENERAL);
			} else if (category.equalsIgnoreCase("obc")) {
				studentBasicInfo.setUserCategory(UserCategory.OBC);

			} else if (category.equalsIgnoreCase("sc")) {
				studentBasicInfo.setUserCategory(UserCategory.SC);

			} else if (category.equalsIgnoreCase("st")) {
				studentBasicInfo.setUserCategory(UserCategory.ST);

			} else {
				studentBasicInfo.setUserCategory(UserCategory.OTHER);

			}
		}

//		String rte = params[10];
//		if (!StringUtils.isBlank(rte)) {
//			if (rte.equalsIgnoreCase("yes")) {
//				studentBasicInfo.setRte(true);
//			} else {
//				studentBasicInfo.setRte(false);
//			}
//		}

		String address = params[10];
		if (!StringUtils.isBlank(address)) {
			studentBasicInfo.setPermanentAddress(StringUtils.capitalize(address));
		}

		String city = params[11];
		if (!StringUtils.isBlank(city)) {
			studentBasicInfo.setPermanentCity(StringUtils.capitalize(city));
		}

		studentBasicInfo.setPermanentState("Uttar Pradesh");
		// String state = params[13];
		// if (!StringUtils.isBlank(state)) {
		// if (state.toLowerCase().contains("raj")) {
		// studentBasicInfo.setState("Rajasthan");
		// }
		// }

		String zipcode = params[13];
		if (!StringUtils.isBlank(zipcode)) {
			studentBasicInfo.setPermanentZipcode(zipcode);
		}

		StudentFamilyInfo studentFamilyInfo = new StudentFamilyInfo();
		String motherName = params[14];
		String fatherName = params[15];

		if (!StringUtils.isBlank(motherName)) {
			studentFamilyInfo.setMothersName(StringUtils.capitalize(motherName));
		}

		if (!StringUtils.isBlank(fatherName)) {
			studentFamilyInfo.setFathersName(StringUtils.capitalize(fatherName));
		}

//		String contactNumbers = params[12];
//		if (!StringUtils.isBlank(contactNumbers)) {
//			String[] numbers = contactNumbers.split("/");
//			String fatherContactNumber = numbers[0];
//			String motherContactNumber = null;
//			if (numbers.length >= 2) {
//				motherContactNumber = numbers[1];
//			}
//
//			if (!StringUtils.isBlank(motherContactNumber)) {
//				studentFamilyInfo.setMothersContactNumber(motherContactNumber);
//			}
//
//			if (!StringUtils.isBlank(fatherContactNumber)) {
//				studentBasicInfo.setPrimaryContactNumber(fatherContactNumber);
//				studentFamilyInfo.setFathersContactNumber(fatherContactNumber);
//			}
//
//		}
//		
		String motherNumber = params[16];
		if (!StringUtils.isBlank(motherNumber)) {
			studentFamilyInfo.setMothersContactNumber(motherNumber);
		}

		String fatherContactNumber = params[17];
		if (!StringUtils.isBlank(fatherContactNumber)) {
			studentFamilyInfo.setFathersContactNumber(fatherContactNumber);
		}

		String contactNumbers = params[20];
		if (!StringUtils.isBlank(contactNumbers)) {
			studentBasicInfo.setPrimaryContactNumber(contactNumbers);
		}

		String className = params[5];
		if (StringUtils.isBlank(className)) {
			logger.error("Invalid standard");
			return null;
		}

		String section = params[6];
		if (StringUtils.isBlank(section)) {
			logger.error("Invalid section");
			section = "";
		}else {
			section = "-"+section;
		}
		Pair<Standard, String> standardPair = getStandardId(className.trim() + section, instituteId, academicSession);
		if (standardPair == null) {
			logger.error("No standard with given name");
			return null;
		}
		Integer sectionId = null;
		if (StringUtils.isNotBlank(standardPair.getSecond())) {
			for (StandardSections standardSection : standardPair.getFirst().getStandardSectionList()) {
				if (standardSection.getSectionName().equalsIgnoreCase(standardPair.getSecond())) {
					sectionId = standardSection.getSectionId();
					break;
				}
			}
		}

		studentPayload.setStudentBasicInfo(studentBasicInfo);
		studentPayload.setStudentFamilyInfo(studentFamilyInfo);
		studentPayload.setStandardId(standardPair.getFirst().getStandardId());
		studentPayload.setSectionId(sectionId);
		studentPayload.setAdmissionAcademicSession(academicSession);
		studentPayload.setInstituteId(instituteId);
		return studentPayload;
	}

	private List<UUID> getEntollStudentFeeStructure(StudentPayload studentPayload) {

		List<UUID> feeStructuresIds = new ArrayList<>();

		List<ResolvedDefaultEntityFeeAssignmentStructure> requiredResolvedDefaultEntityFeeAssignmentStructures = getDefaultFeeStructure(
				studentPayload.getInstituteId(), studentPayload.getAdmissionAcademicSession(),
				studentPayload.getStandardId());
		for (ResolvedDefaultEntityFeeAssignmentStructure resolvedDefaultEntityFeeAssignmentStructure : requiredResolvedDefaultEntityFeeAssignmentStructures) {
			feeStructuresIds.add(resolvedDefaultEntityFeeAssignmentStructure.getStructureId());
			logger.info("Fee structure found for assingment to student {} are {}",
					studentPayload.getStudentBasicInfo().getName(),
					resolvedDefaultEntityFeeAssignmentStructure.getStructureName());
		}
		return feeStructuresIds;
	}

	private List<ResolvedDefaultEntityFeeAssignmentStructure> getDefaultFeeStructure(int instituteId,
			int academicSessionId, UUID standardId) {

		List<ResolvedDefaultEntityFeeAssignmentStructure> requiredResolvedDefaultEntityFeeAssignmentStructures = new ArrayList<>();
		List<ResolvedDefaultEntityFeeAssignmentStructure> resolvedDefaultEntityFeeAssignmentStructures = feeConfigurationManager
				.getDefaultFeeAssignmentStructure(instituteId, academicSessionId, standardId,
						Arrays.asList(FeeStructureType.ENROLLMENT), false);
		return resolvedDefaultEntityFeeAssignmentStructures;

	}

	private Pair<Standard, String> getStandardId(String className, int instituteId, int academicSession) {
		List<Standard> standards = instituteManager.getInstituteStandardList(instituteId, academicSession);
		if (CollectionUtils.isEmpty(standards)) {
			logger.error("Invalid standards configured");
		}

		Map<String, Standard> standardNameMap = new HashMap<>();
		for (Standard standard : standards) {
			Stream stream = standard.getStream() == null ? Stream.NA : standard.getStream();
			if (stream == Stream.NA) {
				standardNameMap.put(standard.getStandardName().trim().toLowerCase(), standard);
			} else {
				standardNameMap.put(standard.getStandardName().trim().toLowerCase() + "|" + stream, standard);
			}
		}

		String[] str = className.toLowerCase().split("-", -1);
		String classId = null;
		String stream = null;
		String section = null;

		if (str.length == 1) {
			classId = str[0].trim();
		} else if (str.length == 2) {
			classId = str[0].trim();
			section = str[1].trim();
		} else {
			classId = str[0].trim();
			stream = str[1].trim();
			section = str[2].trim();
		}

		logger.info("Class = " + classId + ", Stream = " + stream + ", Section = " + section);
		if (classId.equalsIgnoreCase(PLAY)) {
			if (!standardNameMap.containsKey(PLAY)) {
				logger.info("No " + PLAY + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(PLAY), section);
		} else if (classId.equalsIgnoreCase(NURSERY)) {
			if (!standardNameMap.containsKey(NURSERY)) {
				logger.info("No " + NURSERY + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(NURSERY), section);
		} else if (classId.equalsIgnoreCase(LKG)) {
			if (!standardNameMap.containsKey(LKG)) {
				logger.info("No " + LKG + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(LKG), section);
		} else if (classId.equalsIgnoreCase(UKG)) {
			if (!standardNameMap.containsKey(UKG)) {
				logger.info("No " + UKG + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(UKG), section);
		} else if (classId.equalsIgnoreCase(FIRST)) {
			if (!standardNameMap.containsKey(FIRST)) {
				logger.info("No " + FIRST + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(FIRST), section);
		} else if (classId.equalsIgnoreCase(SECOND)) {
			if (!standardNameMap.containsKey(SECOND)) {
				logger.info("No " + SECOND + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(SECOND), section);
		} else if (classId.equalsIgnoreCase(THIRD)) {
			if (!standardNameMap.containsKey(THIRD)) {
				logger.info("No " + THIRD + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(THIRD), section);
		} else if (classId.equalsIgnoreCase(FOURTH)) {
			if (!standardNameMap.containsKey(FOURTH)) {
				logger.info("No " + FOURTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(FOURTH), section);
		} else if (classId.equalsIgnoreCase(FIFTH)) {
			if (!standardNameMap.containsKey(FIFTH)) {
				logger.info("No " + FIFTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(FIFTH), section);
		} else if (classId.equalsIgnoreCase(SIXTH)) {
			if (!standardNameMap.containsKey(SIXTH)) {
				logger.info("No " + SIXTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(SIXTH), section);
		} else if (classId.equalsIgnoreCase(SEVENTH)) {
			if (!standardNameMap.containsKey(SEVENTH)) {
				logger.info("No " + SEVENTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(SEVENTH), section);
		} else if (classId.equalsIgnoreCase(EIGHTH)) {
			if (!standardNameMap.containsKey(EIGHTH)) {
				logger.info("No " + EIGHTH + " standard configured");
				return null;
			}
			return new Pair<Standard, String>(standardNameMap.get(EIGHTH), section);
		} 

		return null;
	}
}
