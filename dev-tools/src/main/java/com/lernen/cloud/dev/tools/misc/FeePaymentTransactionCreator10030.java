package com.lernen.cloud.dev.tools.misc;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Options;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.fees.FeeType;
import com.lernen.cloud.core.api.fees.payment.FeeHeadTransactionAmounts;
import com.lernen.cloud.core.api.fees.payment.FeeIdFeeHeadTransaction;
import com.lernen.cloud.core.api.fees.payment.FeePaymentDetails;
import com.lernen.cloud.core.api.fees.payment.FeePaymentPayload;
import com.lernen.cloud.core.api.fees.payment.FeePaymentResponse;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionMetaData;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.fees.payment.StudentFeesDetails;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeePaymentTransactionCreator10030 {

	private static final Logger logger = LogManager.getLogger(FeePaymentTransactionCreator10030.class);

	private static final String INSTITUTE_ID = "i";
	private static final String ACADEMIC_SESSION_ID = "s";
	private static final String USER = "user";
	private static final String FILE_PATH = "f";
	private static final String FILE_HEADER = "h";
	private static final String UPDATE = "u";
	private static final String FILE_DELIMITER = ",";

	private final StudentManager studentManager;
	private final FeePaymentManager feePaymentManager;

	public FeePaymentTransactionCreator10030(StudentManager studentManager, FeePaymentManager feePaymentManager) {
		this.studentManager = studentManager;
		this.feePaymentManager = feePaymentManager;
	}

	public static void main(String args[]) {
		final ApplicationContext context = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
		final StudentManager studentManager = context.getBean(StudentManager.class);
		final FeePaymentManager feePaymentManager = context.getBean(FeePaymentManager.class);
		FeePaymentTransactionCreator10030 feePaymentTransactionCreator10030 = new FeePaymentTransactionCreator10030(
				studentManager, feePaymentManager);
		Options options = buildOptions();
		CommandLine cmdLine = null;
		try {
			final CommandLineParser parser = new DefaultParser();
			cmdLine = parser.parse(options, args);
		} catch (final Exception pex) {
			System.err.print("Unable to parse arguments.");
			pex.printStackTrace();
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("TransportAssignmentCreator ", options);
			return;
		}

		if (cmdLine == null) {
			logger.error("Unable to parse arguments from cmdline.");
			return;
		}

		int instituteId = 0;
		if (cmdLine.hasOption(INSTITUTE_ID)) {
			instituteId = Integer.parseInt(cmdLine.getOptionValue(INSTITUTE_ID));
		} else {
			logger.error("No institute id passed. Exitting");
			return;
		}
		int academicSessionId = 0;
		if (cmdLine.hasOption(ACADEMIC_SESSION_ID)) {
			academicSessionId = Integer.parseInt(cmdLine.getOptionValue(ACADEMIC_SESSION_ID));
		} else {
			logger.error("No academicSessionId passed. Exitting");
			return;
		}

		UUID userId = null;
		if (cmdLine.hasOption(USER)) {
			userId = UUID.fromString(cmdLine.getOptionValue(USER));
		} else {
			logger.error("No userId passed. Exitting");
			return;
		}

		String filePath = null;
		if (cmdLine.hasOption(FILE_PATH)) {
			filePath = cmdLine.getOptionValue(FILE_PATH);
		} else {
			logger.error("No file path passed. Exitting");
			return;
		}
		boolean fileHeader = false;
		if (cmdLine.hasOption(FILE_HEADER)) {
			fileHeader = true;
		}
		boolean updatePayment = false;
		if (cmdLine.hasOption(UPDATE)) {
			updatePayment = true;
		}
		logger.info("Running for institute = " + instituteId + " , session = " + academicSessionId + ", header = "
				+ fileHeader + " , update2 = " + updatePayment);
		feePaymentTransactionCreator10030.run(instituteId, academicSessionId, filePath, fileHeader, updatePayment,
				userId);
	}

	public void run(int instituteId, int academicSessionId, String filePath, boolean fileHeader, boolean updatePayment,
			UUID userId) {
		List<FileData> fileDataList = readPaymentFile(filePath, fileHeader);
		logger.info("Total records = {}", fileDataList.size());
		int success = 0;
		int failure = 0;
		int noTransactions = 0;
		int noAssignment = 0;
		int count = 1;

		Map<String, Student> studentMap = new HashMap<>();
		List<Student> enrolledStudents = studentManager.getStudentsInAcademicSession(instituteId, academicSessionId);
		for (Student studentResponse : enrolledStudents) {
			studentMap.put(studentResponse.getStudentBasicInfo().getAdmissionNumber().toLowerCase(), studentResponse);
		}

		for (FileData fileData : fileDataList) {
			try {
				logger.info("{} Adding payment for entry {}", count++, fileData);
				Student student = studentMap.get(fileData.getAdmissionNumber().toLowerCase());
				if (student == null) {
					logger.error("Student not found for entry {}", fileData);
					failure++;
					continue;
				}
				Double discountAmount = fileData.getDiscountAmountPerFees();
				if (discountAmount == null || discountAmount <= 0) {
					logger.error("Invalid discount {}", fileData);
					noAssignment++;
					continue;
				}

				logger.info("{} Adding payment for student {}, entry {}", count++, student.getStudentId(), fileData);
				StudentFeesDetails studentFeesDetails = feePaymentManager.getPaymentDetails(instituteId,
						academicSessionId, student.getStudentId());

				List<FeePaymentDetails> paymentFeeDetails = new ArrayList<>();
				for (FeePaymentDetails feePaymentDetails : studentFeesDetails.getFeePaymentDetailsList()) {
					if (feePaymentDetails.getFeeConfigurationBasicInfo().getFeeType() != FeeType.REGULAR) {
						continue;
					}
					if (feePaymentDetails.getFeeConfigurationBasicInfo().getFeeName().contains("APRIL")) {
						continue;
					}
					paymentFeeDetails.add(feePaymentDetails);
				}

				logger.info("Fee payment size {}", paymentFeeDetails.size());
				if (paymentFeeDetails.size() != 11) {
					logger.error("Skipping fee payment as fee payment size is {}", paymentFeeDetails.size());
					failure++;
					continue;
				}

				List<FeeIdFeeHeadTransaction> feeIdFeeHeadTransactionList = new ArrayList<FeeIdFeeHeadTransaction>();

				for (FeePaymentDetails feePaymentDetails : paymentFeeDetails) {
					UUID feeId = feePaymentDetails.getFeeConfigurationBasicInfo().getFeeId();

					if (feePaymentDetails.getFeeHeadPaymentDetails().size() != 1) {
						logger.error("Expected only 1 fee head for details {}", feePaymentDetails);
						feeIdFeeHeadTransactionList.clear();
						break;
					}
					discountAmount = feePaymentDetails.getTotalAssignedAmount() < discountAmount
							? feePaymentDetails.getTotalAssignedAmount()
							: discountAmount;

					FeeHeadTransactionAmounts feeHeadTransactionAmounts = new FeeHeadTransactionAmounts(
							feePaymentDetails.getFeeHeadPaymentDetails().get(0).getFeeHeadConfiguration()
									.getFeeHeadId(),
							0d, discountAmount, 0d);
					feeIdFeeHeadTransactionList
							.add(new FeeIdFeeHeadTransaction(feeId, Arrays.asList(feeHeadTransactionAmounts)));
				}

				if (CollectionUtils.isEmpty(feeIdFeeHeadTransactionList)) {
					logger.info("No fees transaction for student {}", student.getStudentId());
					noTransactions++;
					continue;
				}

				FeePaymentTransactionMetaData feePaymentTransactionMetaData = new FeePaymentTransactionMetaData();
				feePaymentTransactionMetaData.setInstituteId(instituteId);
				feePaymentTransactionMetaData.setAcademicSessionId(academicSessionId);
				feePaymentTransactionMetaData.setStudentId(student.getStudentId());
				feePaymentTransactionMetaData.setFeePaymentTransactionStatus(FeePaymentTransactionStatus.ACTIVE);
				feePaymentTransactionMetaData.setTransactionDate((int) (System.currentTimeMillis() / 1000l));
				feePaymentTransactionMetaData.setTransactionMode(TransactionMode.CASH);

				FeePaymentPayload feePaymentPayload = new FeePaymentPayload(feePaymentTransactionMetaData,
						feeIdFeeHeadTransactionList);
				logger.info("Fee payment payload {}", feePaymentPayload);

				if (!updatePayment) {
					logger.info("Skipping update Fee payment payload {}", feePaymentPayload);
					continue;
				}
				FeePaymentResponse feePaymentResponse = feePaymentManager.addFeePayment(feePaymentPayload, false,
						userId, true);
				if (feePaymentResponse == null) {
					logger.error("Unable to add payment for student {} ", student.getStudentId());
					failure++;
				} else {
					logger.info("Successfully added payment for student {} ", student.getStudentId());
					success++;
				}
			} catch (Exception e) {
				logger.error("Error while adding payment for entry {}", fileData, e);
			}
		}
		logger.info("Total payment entries {}, success {}, failure {}, no transactions {}, noAssignment {}", count - 1,
				success, failure, noTransactions, noAssignment);

	}

	private List<FileData> readPaymentFile(String fileName, boolean header) {
		List<FileData> fileDataList = new ArrayList<>();
		try (final BufferedReader br = new BufferedReader(new FileReader(fileName))) {
			String line = null;
			while ((line = br.readLine()) != null) {
				if (header) {
					header = false;
					continue;
				}
				if (StringUtils.isBlank(line)) {
					continue;
				}
				String[] columns = line.split(FILE_DELIMITER, 5);
				if (columns.length < 5) {
					logger.error("Columns are not " + 5 + ". Skipping it as its not for assignment. Entry = " + line);
					continue;
				}
				String admissionNumber = columns[1].trim();

				String discountAmountStr = columns[4].trim();
				Double discountAmount = null;
				if (!StringUtils.isEmpty(discountAmountStr)) {
					discountAmount = Double.parseDouble(discountAmountStr);
				}

				fileDataList.add(new FileData(admissionNumber, discountAmount));
			}
			return fileDataList;
		} catch (final Exception e) {
			logger.error("Error while reading file", e);
		}
		return null;
	}

	private static Options buildOptions() {
		final Options options = new Options();
		options.addOption(INSTITUTE_ID, true, "specify the institute id");
		options.addOption(ACADEMIC_SESSION_ID, true, "Academic session id for institute");
		options.addOption(FILE_PATH, true, "Transport assignment csv file path");
		options.addOption(FILE_HEADER, false, "File has header");
		options.addOption(UPDATE, false, "Create transaction in DB");
		options.addOption(USER, true, "User id for transaction");
		return options;
	}

	private class FileData {
		private final String admissionNumber;

		private final Double discountAmountPerFees;

		public FileData(String admissionNumber, Double discountAmountPerFees) {
			this.admissionNumber = admissionNumber;
			this.discountAmountPerFees = discountAmountPerFees;
		}

		public String getAdmissionNumber() {
			return admissionNumber;
		}

		public Double getDiscountAmountPerFees() {
			return discountAmountPerFees;
		}

		@Override
		public String toString() {
			return "FileData [admissionNumber=" + admissionNumber + ", discountAmountPerFees=" + discountAmountPerFees
					+ "]";
		}

	}
}
