package com.lernen.cloud.dev.tools.misc;

/**
 * This script assigns transport based on area, updates due fee amount , update
 * student info and creates payment transactions with given amount and discount
 * 
 * <AUTHOR>
 *
 */
public class MultipleDataUpdateCreator10050 {
//	private static final Logger logger = LogManager.getLogger(MultipleDataUpdateCreator10050.class);
//
//	private static final String INSTITUTE_ID = "i";
//	private static final String ACADEMIC_SESSION_ID = "s";
//	private static final String FILE_PATH = "f";
//	private static final String FILE_HEADER = "h";
//	private static final String UPDATE = "u";
//	private static final String ACTION = "a";
//	private static final String USER = "user";
//	private static final String FILE_DELIMITER = ",";
//
//	private static final Map<String, String> AREA_ROUTE_MAP = new HashMap<String, String>();
//
//	static {
//		AREA_ROUTE_MAP.put("Chainpura".toLowerCase(), "Chainpura".toLowerCase());
//		AREA_ROUTE_MAP.put("Gogasar".toLowerCase(), "Gogasar".toLowerCase());
//		AREA_ROUTE_MAP.put("Kangar".toLowerCase(), "Kangar".toLowerCase());
//		AREA_ROUTE_MAP.put("Ladhasar".toLowerCase(), "Ladhasar".toLowerCase());
//		AREA_ROUTE_MAP.put("Malasar".toLowerCase(), "Malasar".toLowerCase());
//		AREA_ROUTE_MAP.put("Melusar".toLowerCase(), "Melusar-Pabusar".toLowerCase());
//		AREA_ROUTE_MAP.put("Pabusar".toLowerCase(), "Melusar-Pabusar".toLowerCase());
//
//	}
//	private final TransportAssignmentManager transportAssignmentManager;
//	private final TransportConfigurationManager transportConfigurationManager;
//	private final TransportFeeConfigurationManager transportFeeConfigurationManager;
//	private final StudentManager studentManager;
//	private final FeePaymentManager feePaymentManager;
//	private final InstituteManager instituteManager;
//	private final FeeConfigurationManager feeConfigurationManager;
//
//	public MultipleDataUpdateCreator10050(TransportAssignmentManager transportAssignmentManager,
//			TransportConfigurationManager transportConfigurationManager,
//			TransportFeeConfigurationManager transportFeeConfigurationManager, StudentManager studentManager,
//			FeePaymentManager feePaymentManager, InstituteManager instituteManager,
//			FeeConfigurationManager feeConfigurationManager) {
//		this.transportAssignmentManager = transportAssignmentManager;
//		this.transportConfigurationManager = transportConfigurationManager;
//		this.transportFeeConfigurationManager = transportFeeConfigurationManager;
//		this.studentManager = studentManager;
//		this.feePaymentManager = feePaymentManager;
//		this.instituteManager = instituteManager;
//		this.feeConfigurationManager = feeConfigurationManager;
//	}
//
//	public static void main(String args[]) {
//		final ApplicationContext context = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
//		final MultipleDataUpdateCreator10050 multipleDataUpdateCreator10050 = context
//				.getBean(MultipleDataUpdateCreator10050.class);
//		Options options = buildOptions();
//		CommandLine cmdLine = null;
//		try {
//			final CommandLineParser parser = new DefaultParser();
//			cmdLine = parser.parse(options, args);
//		} catch (final Exception pex) {
//			System.err.print("Unable to parse arguments.");
//			pex.printStackTrace();
//			final HelpFormatter formatter = new HelpFormatter();
//			formatter.printHelp("TransportAssignmentCreator ", options);
//			return;
//		}
//
//		if (cmdLine == null) {
//			logger.error("Unable to parse arguments from cmdline.");
//			return;
//		}
//
//		int instituteId = 0;
//		if (cmdLine.hasOption(INSTITUTE_ID)) {
//			instituteId = Integer.parseInt(cmdLine.getOptionValue(INSTITUTE_ID));
//		} else {
//			logger.error("No institute id passed. Exitting");
//			return;
//		}
//		int academicSessionId = 0;
//		if (cmdLine.hasOption(ACADEMIC_SESSION_ID)) {
//			academicSessionId = Integer.parseInt(cmdLine.getOptionValue(ACADEMIC_SESSION_ID));
//		} else {
//			logger.error("No academicSessionId passed. Exitting");
//			return;
//		}
//
//		String filePath = null;
//		if (cmdLine.hasOption(FILE_PATH)) {
//			filePath = cmdLine.getOptionValue(FILE_PATH);
//		} else {
//			logger.error("No file path passed. Exitting");
//			return;
//		}
//		String action = null;
//		if (cmdLine.hasOption(ACTION)) {
//			action = cmdLine.getOptionValue(ACTION);
//		} else {
//			logger.error("No Action passed. Exitting");
//			return;
//		}
//
//		UUID userId = null;
//		if (cmdLine.hasOption(USER)) {
//			userId = UUID.fromString(cmdLine.getOptionValue(USER));
//		} else {
//			logger.error("No userId passed. Exitting");
//			return;
//		}
//
//		boolean fileHeader = false;
//		if (cmdLine.hasOption(FILE_HEADER)) {
//			fileHeader = true;
//		}
//		boolean updateAssignment = false;
//		if (cmdLine.hasOption(UPDATE)) {
//			updateAssignment = true;
//		}
//		logger.info("Running for institute = " + instituteId + " , session = " + academicSessionId + ", header = "
//				+ fileHeader + " , assign = " + updateAssignment + " , action = " + action);
//		if (StringUtils.isEmpty(action)) {
//			logger.error("Invalid Action passed. Exitting");
//			return;
//		}
//		switch (action) {
//		case "assign_transport":
//			multipleDataUpdateCreator10050.assignTransportFromFile(instituteId, academicSessionId, filePath, fileHeader,
//					updateAssignment, userId);
//			return;
//		case "due_fee_assign":
//			multipleDataUpdateCreator10050.assignFees(instituteId, academicSessionId, filePath, fileHeader,
//					updateAssignment, userId);
//			return;
//		case "make_payment":
//			multipleDataUpdateCreator10050.addFeePayment(instituteId, academicSessionId, filePath, fileHeader,
//					updateAssignment, userId);
//			return;
//		case "update_student":
//			multipleDataUpdateCreator10050.updateStudent(instituteId, academicSessionId, filePath, fileHeader,
//					updateAssignment, userId);
//			return;
//		default:
//			break;
//		}
//
//	}
//
//	private void assignTransportFromFile(int instituteId, int academicSessionId, String filePath, boolean fileHeader,
//			boolean updateAssignment, UUID userId) {
//		List<TransportAssignmentFilePayload> transportAssignmentFilePayloads = readAssignmentFile(filePath, fileHeader);
//		logger.info("Total students in file = " + transportAssignmentFilePayloads.size());
//
//		for (TransportAssignmentFilePayload transportAssignmentFilePayload : transportAssignmentFilePayloads) {
//			logger.info(transportAssignmentFilePayload.toString());
//		}
//
//		Map<String, Student> studentMap = new HashMap<>();
//		List<Student> enrolledStudents = studentManager.getStudentsInAcademicSesison(instituteId, academicSessionId);
//		for (Student studentResponse : enrolledStudents) {
//			studentMap.put(studentResponse.getStudentBasicInfo().getAdmissionNumber().toLowerCase(), studentResponse);
//		}
//
//		List<TransportAssignmentPayload> transportAssignmentPayloads = getTransportAssignmentPayload(instituteId,
//				academicSessionId, transportAssignmentFilePayloads, studentMap);
//		boolean totalSuccess = true;
//		int successCount = 0;
//		int failCount = 0;
//		logger.info("Assigning transport for total records = " + transportAssignmentPayloads.size());
//		for (TransportAssignmentPayload transportAssignmentPayload : transportAssignmentPayloads) {
//			logger.info("Dry run payload = " + transportAssignmentPayload.toString());
//			if (!updateAssignment) {
//				continue;
//			}
//			if (transportAssignmentManager.addTransport(transportAssignmentPayload, userId)) {
//				logger.info("Assigned transport ammount successfully.");
//				successCount++;
//			} else {
//				logger.error("ERROR : Assigned transport ammount not success.");
//				totalSuccess = false;
//				failCount++;
//			}
//		}
//		if (totalSuccess) {
//			logger.info("All records are assigned successfully.");
//		} else {
//			logger.info("ERROR : All records are not assigned  successfully.");
//		}
//		logger.info("Total = " + (successCount + failCount) + ", success = " + successCount + " , fail = " + failCount);
//
//		logger.info("\n---------------------------------------------------------------\n");
//
//	}
//
//	private void addFeePayment(int instituteId, int academicSessionId, String filePath, boolean fileHeader,
//			boolean updateAssignment, UUID userId) {
//
//		List<FeePaymentFilePayload> feePaymentFilePayloads = readFeePaymentFile(filePath, fileHeader);
//		logger.info("Total Entries in fee payments file = " + feePaymentFilePayloads.size());
//
//		Map<String, Student> studentMap = new HashMap<>();
//		List<Student> enrolledStudents = studentManager.getStudentsInAcademicSesison(instituteId, academicSessionId);
//		for (Student studentResponse : enrolledStudents) {
//			studentMap.put(studentResponse.getStudentBasicInfo().getAdmissionNumber().toLowerCase(), studentResponse);
//		}
//
//		int success = 0;
//		int failure = 0;
//		int noTransactions = 0;
//		int noAssignment = 0;
//		int lessTotalAssignedAmount = 0;
//		int count = 1;
//		for (FeePaymentFilePayload feePaymentFilePayload : feePaymentFilePayloads) {
//
//			Student student = studentMap.get(feePaymentFilePayload.getAdmissionNumber().toLowerCase());
//			if (student == null) {
//				logger.error("Student not found for entry {}", feePaymentFilePayload);
//				failure++;
//				continue;
//			}
//			logger.info("{} Adding payment for student {}, entry {}", count++, student.getStudentId(),
//					feePaymentFilePayload);
//
//			if ((feePaymentFilePayload.getPaidAmount() == null || feePaymentFilePayload.getPaidAmount() < 0d)
//					&& (feePaymentFilePayload.getDiscount() == null || feePaymentFilePayload.getDiscount() < 0d)) {
//				logger.info("Total amount and balance amount not provided. Skipping student {}",
//						student.getStudentId());
//				noAssignment++;
//				continue;
//			}
//
//			StudentFeesDetails studentFeesDetails = feePaymentManager.getPaymentDetails(instituteId, academicSessionId,
//					student.getStudentId());
//
//			Double totalPaidAmount = feePaymentFilePayload.getPaidAmount() == null
//					|| feePaymentFilePayload.getPaidAmount() < 0d ? 0d : feePaymentFilePayload.getPaidAmount();
//
//			Double totalDiscountAmount = feePaymentFilePayload.getDiscount() == null
//					|| feePaymentFilePayload.getDiscount() < 0d ? 0d : feePaymentFilePayload.getDiscount();
//
//			List<FeePaymentDetails> allFeeDetails = new ArrayList<>();
//			double totalAssignedAmount = 0d;
//			for (FeePaymentDetails feePaymentDetails : studentFeesDetails.getFeePaymentDetailsList()) {
//				allFeeDetails.add(feePaymentDetails);
//				totalAssignedAmount += feePaymentDetails.getTotalAssignedAmount();
//
//			}
//
//			logger.info("Total fees count {}, and total assigned amount {}", allFeeDetails.size(), totalAssignedAmount);
//
//			if (totalAssignedAmount < totalPaidAmount + totalDiscountAmount) {
//				logger.error("total assigned amount {} is lesser then total paid + discount {}", totalAssignedAmount,
//						totalPaidAmount + totalDiscountAmount);
//				lessTotalAssignedAmount++;
//				continue;
//			}
//
//			List<FeeIdFeeHeadTransaction> feeIdFeeHeadTransactionList = new ArrayList<>();
//
//			Pair<Double, Double> finalAmounts = new Pair<Double, Double>(totalPaidAmount, totalDiscountAmount);
//			for (FeePaymentDetails feePaymentDetails : studentFeesDetails.getFeePaymentDetailsList()) {
//				if (feePaymentDetails.getFeeConfigurationBasicInfo().getFeeName().trim()
//						.equalsIgnoreCase("Due Fees 2018-19")) {
//					finalAmounts = addFeePaymentAmount(feePaymentDetails, feeIdFeeHeadTransactionList, finalAmounts);
//					break;
//				}
//
//			}
//
//			for (FeePaymentDetails feePaymentDetails : studentFeesDetails.getFeePaymentDetailsList()) {
//				if (feePaymentDetails.getFeeConfigurationBasicInfo().getFeeName().trim()
//						.equalsIgnoreCase("Admission Fees")) {
//					finalAmounts = addFeePaymentAmount(feePaymentDetails, feeIdFeeHeadTransactionList, finalAmounts);
//					break;
//				}
//
//			}
//
//			for (FeePaymentDetails feePaymentDetails : studentFeesDetails.getFeePaymentDetailsList()) {
//				if (feePaymentDetails.getFeeConfigurationBasicInfo().getFeeName().trim()
//						.equalsIgnoreCase("Due Fees 2018-19")
//						|| feePaymentDetails.getFeeConfigurationBasicInfo().getFeeName().trim()
//								.equalsIgnoreCase("Admission Fees")) {
//					continue;
//				}
//				finalAmounts = addFeePaymentAmount(feePaymentDetails, feeIdFeeHeadTransactionList, finalAmounts);
//			}
//
//			if (CollectionUtils.isEmpty(feeIdFeeHeadTransactionList)) {
//				logger.info("No fees transaction for student {}", student.getStudentId());
//				noTransactions++;
//				continue;
//			}
//
//			FeePaymentTransactionMetaData feePaymentTransactionMetaData = new FeePaymentTransactionMetaData();
//			feePaymentTransactionMetaData.setInstituteId(instituteId);
//			feePaymentTransactionMetaData.setAcademicSessionId(academicSessionId);
//			feePaymentTransactionMetaData.setStudentId(student.getStudentId());
//			feePaymentTransactionMetaData.setFeePaymentTransactionStatus(FeePaymentTransactionStatus.ACTIVE);
//			feePaymentTransactionMetaData.setTransactionDate((int) (System.currentTimeMillis() / 1000l));
//			feePaymentTransactionMetaData.setTransactionMode(TransactionMode.CASH);
//
//			FeePaymentPayload feePaymentPayload = new FeePaymentPayload(feePaymentTransactionMetaData,
//					feeIdFeeHeadTransactionList);
//			logger.info("Fee payment payload {}", feePaymentPayload);
//
//			FeePaymentResponse feePaymentResponse = feePaymentManager.addFeePayment(feePaymentPayload, false, userId);
//			if (feePaymentResponse == null) {
//				logger.error("Unable to add payment for student {} ", student.getStudentId());
//				failure++;
//			} else {
//				logger.info("Successfully added payment for student {} ", student.getStudentId());
//				success++;
//			}
//			try {
//				Thread.sleep(1000l);
//			} catch (InterruptedException e) {
//				// TODO Auto-generated catch block
//				e.printStackTrace();
//			}
//		}
//
//		logger.info(
//				"Total payment entries {}, success {}, failure {}, no transactions {}, noAssignment {}, lessTotalAssignedAmount {}",
//				count - 1, success, failure, noTransactions, noAssignment, lessTotalAssignedAmount);
//
//	}
//
//	private Pair<Double, Double> addFeePaymentAmount(FeePaymentDetails feePaymentDetails,
//			List<FeeIdFeeHeadTransaction> feeIdFeeHeadTransactionList, Pair<Double, Double> totalAmount) {
//		Double totalPaidAmount = totalAmount.getFirst();
//
//		Double totalDiscountAmount = totalAmount.getSecond();
//
//		UUID feeId = feePaymentDetails.getFeeConfigurationBasicInfo().getFeeId();
//		List<FeeHeadTransactionAmounts> feeHeadTransactionAmountsList = new ArrayList<FeeHeadTransactionAmounts>();
//		for (FeeHeadPaymentDetails feeHeadPaymentDetails : feePaymentDetails.getFeeHeadPaymentDetails()) {
//			double balanceAmount = feeHeadPaymentDetails.getBalanceAmount();
//			if (totalPaidAmount <= 0d && totalDiscountAmount <= 0d) {
//				break;
//			}
//			if (Double.compare(totalDiscountAmount, 0d) > 0) {
//				double appliedDiscountAmount = balanceAmount < totalDiscountAmount ? balanceAmount
//						: totalDiscountAmount;
//				double paidAmount = balanceAmount - appliedDiscountAmount < totalPaidAmount
//						? balanceAmount - appliedDiscountAmount
//						: totalPaidAmount;
//				FeeHeadTransactionAmounts feeHeadTransactionAmounts = new FeeHeadTransactionAmounts(
//						feeHeadPaymentDetails.getFeeHeadConfiguration().getFeeHeadId(), paidAmount,
//						appliedDiscountAmount, 0d);
//
//				totalDiscountAmount -= appliedDiscountAmount;
//				totalPaidAmount -= paidAmount;
//
//				feeHeadTransactionAmountsList.add(feeHeadTransactionAmounts);
//			} else {
//
//				double paidAmount = balanceAmount < totalPaidAmount ? balanceAmount : totalPaidAmount;
//				if (Double.compare(paidAmount, 0d) <= 0) {
//					continue;
//				}
//
//				FeeHeadTransactionAmounts feeHeadTransactionAmounts = new FeeHeadTransactionAmounts(
//						feeHeadPaymentDetails.getFeeHeadConfiguration().getFeeHeadId(), paidAmount, 0d, 0d);
//
//				totalPaidAmount -= paidAmount;
//
//				feeHeadTransactionAmountsList.add(feeHeadTransactionAmounts);
//
//			}
//		}
//		if (!CollectionUtils.isEmpty(feeHeadTransactionAmountsList)) {
//			feeIdFeeHeadTransactionList.add(new FeeIdFeeHeadTransaction(feeId, feeHeadTransactionAmountsList));
//		}
//
//		return new Pair<Double, Double>(totalPaidAmount, totalDiscountAmount);
//	}
//
//	private final List<TransportAssignmentPayload> getTransportAssignmentPayload(int instituteId, int academicSessionId,
//			List<TransportAssignmentFilePayload> transportAssignmentFilePayloads, Map<String, Student> studentMap) {
//		List<TransportAssignmentPayload> transportAssignmentPayloads = new ArrayList<>();
//		List<TransportArea> transportAreas = transportConfigurationManager.getTransportAreas(instituteId);
//		List<TransportServiceRouteResponse> transportServiceRouteResponses = transportConfigurationManager
//				.getTransportServiceRoutes(instituteId, academicSessionId);
//
//		Map<String, TransportArea> transportAreaMap = new HashMap<>();
//		Map<String, TransportServiceRouteResponse> transportServiceRouteResponseMap = new HashMap<>();
//
//		for (TransportArea transportArea : transportAreas) {
//			transportAreaMap.put(transportArea.getAreaKey().toLowerCase(), transportArea);
//		}
//
//		for (TransportServiceRouteResponse transportServiceRouteResponse : transportServiceRouteResponses) {
//			transportServiceRouteResponseMap.put(transportServiceRouteResponse.getServiceRouteName().toLowerCase(),
//					transportServiceRouteResponse);
//		}
//
//		TransportFeeConfigData transportFeeConfigData = transportFeeConfigurationManager
//				.getTransportFeeConfigData(instituteId, academicSessionId);
//		logger.info(transportFeeConfigData);
//		List<TransportConfiguredFeeData> transportConfiguredFeeDatas1 = transportFeeConfigData
//				.getTransportConfiguredFeeDatas();
//		logger.info("Is exam configured = " + transportFeeConfigData.isConfigured());
//		if (CollectionUtils.isEmpty(transportConfiguredFeeDatas1)) {
//			logger.error("Invalid fee configured. Exitting");
//			return null;
//		}
//		for (TransportAssignmentFilePayload transportAssignmentFilePayload : transportAssignmentFilePayloads) {
//			logger.info(transportAssignmentFilePayload);
//			Student studentResponse = studentMap.get(transportAssignmentFilePayload.getAdmissionNumber().toLowerCase());
//			TransportArea transportArea = transportAreaMap
//					.get(transportAssignmentFilePayload.getAreaKey().toLowerCase());
//			TransportServiceRouteResponse transportServiceRouteResponse = transportServiceRouteResponseMap
//					.get(transportAssignmentFilePayload.getRouteName().toLowerCase());
//
//			if (studentResponse == null || transportArea == null || transportServiceRouteResponse == null) {
//				logger.error("Invalid student, or transport area or route. Exitting "
//						+ transportAssignmentFilePayload.toString());
//				continue;
//			}
//			// System.out.println(studentResponse);
//			// System.out.println(transportArea);
//			// System.out.println(transportServiceRouteResponse);
//			//
//			TransportAssignmentPayload transportAssignmentPayload = new TransportAssignmentPayload();
//			transportAssignmentPayload.setInstituteId(instituteId);
//			transportAssignmentPayload.setStudentId(studentResponse.getStudentId());
//			transportAssignmentPayload.setAcademicSessionId(academicSessionId);
//			transportAssignmentPayload.setAreaId(transportArea.getAreaId());
//			transportAssignmentPayload.setServiceRouteId(transportServiceRouteResponse.getServiceRouteId());
//			transportAssignmentPayload.setCompleteSession(true);
//
//			List<TransportHistoryFeeIdAmount> transportHistoryFeeIdAmountList = new ArrayList<>();
//			Double totalAmount = null;
//			for (TransportServiceRouteStoppagesResponse transportServiceRouteStoppagesResponse : transportServiceRouteResponse
//					.getStoppagesList()) {
//				if (transportServiceRouteStoppagesResponse.getTransportArea().getAreaId() == transportArea
//						.getAreaId()) {
//					totalAmount = transportServiceRouteStoppagesResponse.getAssignedAmount();
//					break;
//				}
//			}
//			if (totalAmount == null) {
//				logger.error("Invalid total assigned amount for fees. Exitting");
//				return null;
//			}
//			for (TransportConfiguredFeeData transportConfiguredFeeData : transportConfiguredFeeDatas1) {
//				TransportHistoryFeeIdAmount transportHistoryFeeIdAmount = new TransportHistoryFeeIdAmount();
//				transportHistoryFeeIdAmount.setFeeId(transportConfiguredFeeData.getFeeConfigurationResponse()
//						.getFeeConfigurationBasicInfo().getFeeId());
//				transportHistoryFeeIdAmount.setAmount(
//						totalAmount * transportConfiguredFeeData.getModuleFeeProportion().getFeeProportion());
//				transportHistoryFeeIdAmountList.add(transportHistoryFeeIdAmount);
//			}
//			transportAssignmentPayload.setTransportHistoryFeeIdAmountList(transportHistoryFeeIdAmountList);
//			transportAssignmentPayloads.add(transportAssignmentPayload);
//		}
//		return transportAssignmentPayloads;
//	}
//
//	////////////////////////////////// Fee Assign
//	////////////////////////////////// /////////////////////////////////////////////////////
//	public void assignFees(int instituteId, int academicSessionId, String filePath, boolean fileHeader,
//			boolean updateAssignment, UUID userId) {
//		List<FeeAssignmentFilePayload> feeAssignmentFilePayloads = readFeeAssignmentFile(filePath, fileHeader);
//		logger.info("Total records in file = " + feeAssignmentFilePayloads.size());
//
//		for (FeeAssignmentFilePayload feeAssignmentFilePayload : feeAssignmentFilePayloads) {
//			logger.info(feeAssignmentFilePayload.toString());
//		}
//
//		List<FeeAssignmentDetails> feeAssignmentDetailsList = getFeeAssignmentPayloads(instituteId, academicSessionId,
//				feeAssignmentFilePayloads);
//
//		boolean totalSuccess = true;
//		int successCount = 0;
//		int failCount = 0;
//		logger.info("Assigning fees for total records = " + feeAssignmentFilePayloads.size());
//		for (FeeAssignmentDetails feeAssignmentDetails : feeAssignmentDetailsList) {
//			logger.info("Payload = " + feeAssignmentDetails.toString());
//			if (!updateAssignment) {
//				continue;
//			}
//			if (feeConfigurationManager.assignFees(feeAssignmentDetails, Module.FEES,
//					FeeAssignmentState.COMPLETE_UPDATE, false, true, userId)) {
//				logger.info("Assigned fee ammount successfully.");
//				successCount++;
//			} else {
//				logger.error("ERROR : Assigned transport ammount not success.");
//				totalSuccess = false;
//				failCount++;
//			}
//		}
//		if (totalSuccess) {
//			logger.info("All records are assigned successfully.");
//		} else {
//			logger.info("ERROR : All records are not assigned  successfully.");
//		}
//		logger.info("Total = " + (successCount + failCount) + ", success = " + successCount + " , fail = " + failCount);
//	}
//
//	private final List<FeeAssignmentDetails> getFeeAssignmentPayloads(int instituteId, int academicSessionId,
//			List<FeeAssignmentFilePayload> feeAssignmentFilePayloads) {
//		List<FeeAssignmentDetails> feeAssignmentDetailsList = new ArrayList<>();
//		Map<String, Student> studentMap = new HashMap<>();
//		List<Student> enrolledStudents = studentManager.getStudentsInAcademicSesison(instituteId, academicSessionId);
//		for (Student studentResponse : enrolledStudents) {
//			studentMap.put(studentResponse.getStudentBasicInfo().getAdmissionNumber().toLowerCase(), studentResponse);
//		}
//
//		List<FeeConfigurationResponse> feeConfigurationResponses = feeConfigurationManager
//				.getFeeConfigurationByAcademicYear(instituteId, academicSessionId);
//		Map<String, FeeConfigurationResponse> feeMap = new HashMap<>();
//
//		for (FeeConfigurationResponse feeConfigurationResponse : feeConfigurationResponses) {
//			feeMap.put(feeConfigurationResponse.getFeeConfigurationBasicInfo().getFeeName().toLowerCase(),
//					feeConfigurationResponse);
//		}
//
//		List<FeeHeadConfigurationResponse> feeHeadConfigurationResponses = feeConfigurationManager
//				.getFeeHeadConfiguration(instituteId);
//		Map<String, FeeHeadConfigurationResponse> feeHeadMap = new HashMap<>();
//
//		for (FeeHeadConfigurationResponse feeHeadConfigurationResponse : feeHeadConfigurationResponses) {
//			feeHeadMap.put(feeHeadConfigurationResponse.getFeeHeadConfiguration().getFeeHead().toLowerCase(),
//					feeHeadConfigurationResponse);
//		}
//
//		Map<UUID, Map<UUID, Map<Integer, Double>>> entityFeeMap = new HashMap<>();
//
//		for (FeeAssignmentFilePayload feeAssignmentFilePayload : feeAssignmentFilePayloads) {
//			Student student = studentMap.get(feeAssignmentFilePayload.getAdmissionNumber().toLowerCase());
//			if (student == null) {
//				logger.error("Invalid student {}", feeAssignmentFilePayload.getAdmissionNumber());
//				continue;
//			}
//			FeeConfigurationResponse feeConfigurationResponse = feeMap
//					.get(feeAssignmentFilePayload.getFeeName().toLowerCase());
//			if (feeConfigurationResponse == null) {
//				logger.error("Invalid fees name {}", feeAssignmentFilePayload.getFeeName());
//				continue;
//			}
//
//			FeeHeadConfigurationResponse feeHeadConfigurationResponse = feeHeadMap
//					.get(feeAssignmentFilePayload.getFeeHeadName().toLowerCase());
//			if (feeHeadConfigurationResponse == null) {
//				logger.error("Invalid fee head name {}", feeAssignmentFilePayload.getFeeHeadName());
//				continue;
//			}
//			UUID feeId = feeConfigurationResponse.getFeeConfigurationBasicInfo().getFeeId();
//			if (!entityFeeMap.containsKey(feeId)) {
//				entityFeeMap.put(feeId, new HashMap<>());
//			}
//			UUID studentId = student.getStudentId();
//			if (!entityFeeMap.get(feeId).containsKey(studentId)) {
//				entityFeeMap.get(feeId).put(studentId, new HashMap<>());
//			}
//			entityFeeMap.get(feeId).get(studentId).put(
//					feeHeadConfigurationResponse.getFeeHeadConfiguration().getFeeHeadId(),
//					feeAssignmentFilePayload.getAmount());
//		}
//
//		for (Entry<UUID, Map<UUID, Map<Integer, Double>>> feeEntry : entityFeeMap.entrySet()) {
//			UUID feeId = feeEntry.getKey();
//			List<EntityFees> entityFees = new ArrayList<>();
//			for (Entry<UUID, Map<Integer, Double>> studentEntry : feeEntry.getValue().entrySet()) {
//				UUID studentId = studentEntry.getKey();
//				List<FeeHeadAmount> feeHeadAmount = new ArrayList<>();
//				for (Entry<Integer, Double> feeHeadEntry : studentEntry.getValue().entrySet()) {
//					feeHeadAmount.add(new FeeHeadAmount(feeHeadEntry.getKey(), feeHeadEntry.getValue()));
//				}
//				entityFees.add(new EntityFees(studentId.toString(), FeeEntity.STUDENT, feeHeadAmount));
//			}
//			feeAssignmentDetailsList.add(new FeeAssignmentDetails(instituteId, feeId, entityFees));
//		}
//		return feeAssignmentDetailsList;
//	}
//////////////////////////////////// End Fee Assign /////////////////////////////////////////////////////
//
//	private void updateStudent(int instituteId, int academicSessionId, String filePath, boolean fileHeader,
//			boolean updateAssignment, UUID userId) {
//		List<StudentDetailFilePayload> studentDetailFilePayloads = readStudentDetailFile(filePath, fileHeader);
//		logger.info("Total students in file = " + studentDetailFilePayloads.size());
//
//		for (StudentDetailFilePayload studentDetailFilePayload : studentDetailFilePayloads) {
//			logger.info(studentDetailFilePayload.toString());
//		}
//
//		Map<String, Student> studentMap = new HashMap<>();
//		List<Student> enrolledStudents = studentManager.getStudentsInAcademicSesison(instituteId, academicSessionId);
//		for (Student studentResponse : enrolledStudents) {
//			studentMap.put(studentResponse.getStudentBasicInfo().getAdmissionNumber().toLowerCase(), studentResponse);
//		}
//
//		boolean totalSuccess = true;
//		int successCount = 0;
//		int failCount = 0;
//		logger.info("Updating student for total records = " + studentDetailFilePayloads.size());
//		for (StudentDetailFilePayload studentDetailFilePayload : studentDetailFilePayloads) {
//			logger.info("Dry run payload = " + studentDetailFilePayload.toString());
//			if (!updateAssignment) {
//				continue;
//			}
//			StudentPayload studentPayload = new StudentPayload();
//
//			Student student = studentMap.get(studentDetailFilePayload.getAdmissionNumber().toLowerCase());
//
//			if (student == null) {
//				logger.error("Student not found with admission number {}.",
//						studentDetailFilePayload.getAdmissionNumber());
//				failCount++;
//				continue;
//			}
//			StudentBasicInfo studentBasicInfo = student.getStudentBasicInfo();
//			studentBasicInfo.setPrimaryContactNumber(studentDetailFilePayload.getPrimaryContact());
//
//			studentPayload.setStudentId(student.getStudentId());
//			studentPayload.setStudentBasicInfo(studentBasicInfo);
//			studentPayload.setStandardId(student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId());
//			if (!CollectionUtils
//					.isEmpty(student.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList())) {
//				studentPayload.setSectionId(student.getStudentAcademicSessionInfoResponse().getStandard()
//						.getStandardSectionList().get(0).getSectionId());
//			}
//			studentPayload.setAdmissionAcademicSession(
//					student.getStudentAcademicSessionInfoResponse().getAcademicSession().getAcademicSessionId());
//			studentPayload.setInstituteId(instituteId);
//
//			UUID studentId = studentManager.updateStudent(studentPayload, academicSessionId, instituteId, null, null,
//					userId);
//			if (studentId == student.getStudentId()) {
//				logger.info("Updated student successfully.");
//				successCount++;
//			} else {
//				logger.error("ERROR : Updating student not success.");
//				totalSuccess = false;
//				failCount++;
//			}
//		}
//		if (totalSuccess) {
//			logger.info("All records are assigned successfully.");
//		} else {
//			logger.info("ERROR : All records are not assigned  successfully.");
//		}
//		logger.info("Total = " + (successCount + failCount) + ", success = " + successCount + " , fail = " + failCount);
//
//		logger.info("\n---------------------------------------------------------------\n");
//
//	}
//
//	private List<TransportAssignmentFilePayload> readAssignmentFile(String fileName, boolean header) {
//		List<TransportAssignmentFilePayload> transportAssignmentFilePayloads = new ArrayList<>();
//		try (final BufferedReader br = new BufferedReader(new FileReader(fileName))) {
//			String line = null;
//			while ((line = br.readLine()) != null) {
//				if (header) {
//					header = false;
//					continue;
//				}
//				if (StringUtils.isBlank(line)) {
//					continue;
//				}
//				String[] columns = line.split(FILE_DELIMITER, 12);
//
//				String admissionNumber = columns[1].trim();
//				String areaName = columns[8].trim();
//
//				if (StringUtils.isBlank(admissionNumber)) {
//					logger.error("Not valid entry for assignment. Entry = " + line);
//					continue;
//				}
//
//				if (StringUtils.isBlank(areaName)) {
//					logger.error("Not valid entry for assignment. Entry = " + line);
//					continue;
//				}
//
//				String routeName = AREA_ROUTE_MAP.get(areaName.toLowerCase());
//				if (routeName == null) {
//					logger.error("Not found any route for assignment. Entry = " + line);
//					continue;
//				}
//
//				transportAssignmentFilePayloads
//						.add(new TransportAssignmentFilePayload(admissionNumber, routeName, areaName));
//			}
//			return transportAssignmentFilePayloads;
//		} catch (final Exception e) {
//			logger.error("Error while reading file", e);
//		}
//		return null;
//	}
//
//	private List<FeeAssignmentFilePayload> readFeeAssignmentFile(String fileName, boolean header) {
//		List<FeeAssignmentFilePayload> feeAssignmentFilePayloads = new ArrayList<>();
//		try (final BufferedReader br = new BufferedReader(new FileReader(fileName))) {
//			String line = null;
//			while ((line = br.readLine()) != null) {
//				if (header) {
//					header = false;
//					continue;
//				}
//				if (StringUtils.isBlank(line)) {
//					continue;
//				}
//				String[] columns = line.split(FILE_DELIMITER, 12);
//
//				String admissionNumber = columns[1].trim();
//				String feeName = "Due Fees 2018-19".toLowerCase();
//				String feeHeadName = "Due Fees".toLowerCase();
//				String dueAmountString = columns[6].trim();
//				String admissionAmountString = columns[10].trim();
//
//				if (StringUtils.isBlank(admissionNumber)) {
//					logger.error("Not valid entry for assignment. Entry = " + line);
//					continue;
//				}
//
//				Double dueAmount = null;
//				if (StringUtils.isNotBlank(dueAmountString)) {
//					dueAmount = Double.parseDouble(dueAmountString);
//				}
//
//				Double admissionAmount = null;
//				if (StringUtils.isNotBlank(admissionAmountString)) {
//					admissionAmount = Double.parseDouble(admissionAmountString);
//				}
//
//				if (StringUtils.isBlank(admissionNumber) || StringUtils.isBlank(feeName)
//						|| StringUtils.isBlank(feeHeadName)) {
//					logger.error("Not valid entry for assignment. Entry = " + line);
//					continue;
//				}
//
//				if (dueAmount != null && dueAmount > 0) {
//					feeAssignmentFilePayloads
//							.add(new FeeAssignmentFilePayload(admissionNumber, feeName, feeHeadName, dueAmount));
//				}
//
//				if (admissionAmount != null && admissionAmount > 0) {
//					feeAssignmentFilePayloads.add(new FeeAssignmentFilePayload(admissionNumber,
//							"Admission Fees".toLowerCase(), "Admission Fees".toLowerCase(), admissionAmount));
//				}
//
//			}
//			return feeAssignmentFilePayloads;
//		} catch (final Exception e) {
//			logger.error("Error while reading file", e);
//		}
//		return null;
//	}
//
//	private List<FeePaymentFilePayload> readFeePaymentFile(String fileName, boolean header) {
//		List<FeePaymentFilePayload> feePaymentFilePayloads = new ArrayList<>();
//		try (final BufferedReader br = new BufferedReader(new FileReader(fileName))) {
//			String line = null;
//			while ((line = br.readLine()) != null) {
//				if (header) {
//					header = false;
//					continue;
//				}
//				if (StringUtils.isBlank(line)) {
//					continue;
//				}
//				String[] columns = line.split(FILE_DELIMITER, 12);
//
//				String admissionNumber = columns[1].trim();
//
//				String paidAmountStr = columns[7].trim();
//				Double paidAmount = null;
//				if (StringUtils.isBlank(admissionNumber)) {
//					logger.error("Not valid entry for assignment. Entry = " + line);
//					continue;
//				}
//
//				if (!StringUtils.isEmpty(paidAmountStr)) {
//					paidAmount = Double.parseDouble(paidAmountStr);
//				}
//
//				String discountAmountStr = columns[11].trim();
//				Double discountAmount = null;
//				if (!StringUtils.isEmpty(discountAmountStr)) {
//					discountAmount = Double.parseDouble(discountAmountStr);
//				}
//				feePaymentFilePayloads.add(new FeePaymentFilePayload(admissionNumber, paidAmount, discountAmount));
//			}
//			return feePaymentFilePayloads;
//		} catch (final Exception e) {
//			logger.error("Error while reading file", e);
//		}
//		return null;
//	}
//
//	private List<StudentDetailFilePayload> readStudentDetailFile(String fileName, boolean header) {
//		List<StudentDetailFilePayload> studentDetailFilePayloads = new ArrayList<>();
//		try (final BufferedReader br = new BufferedReader(new FileReader(fileName))) {
//			String line = null;
//			while ((line = br.readLine()) != null) {
//				if (header) {
//					header = false;
//					continue;
//				}
//				if (StringUtils.isBlank(line)) {
//					continue;
//				}
//				String[] columns = line.split(FILE_DELIMITER, 12);
//
//				String admissionNumber = columns[1].trim();
//
//				String primaryContact = columns[9].trim();
//
//				if (StringUtils.isBlank(admissionNumber)) {
//					logger.error("Not valid entry for assignment. Entry = " + line);
//					continue;
//				}
//
//				if (StringUtils.isEmpty(primaryContact)) {
//					logger.info("Invalid contanct valid entry for assignment. Entry = " + line);
//					continue;
//				}
//
//				studentDetailFilePayloads.add(new StudentDetailFilePayload(admissionNumber, primaryContact));
//			}
//			return studentDetailFilePayloads;
//		} catch (final Exception e) {
//			logger.error("Error while reading file", e);
//		}
//		return null;
//	}
//
//	private static Options buildOptions() {
//		final Options options = new Options();
//		options.addOption(INSTITUTE_ID, true, "specify the institute id");
//		options.addOption(ACADEMIC_SESSION_ID, true, "Academic session id for institute");
//		options.addOption(FILE_PATH, true, "Transport assignment csv file path");
//		options.addOption(FILE_HEADER, false, "File has header");
//		options.addOption(UPDATE, false, "Update the assignments");
//		options.addOption(ACTION, true, "Action to perform");
//		options.addOption(USER, true, "User id for transaction");
//		return options;
//	}
//
//	private class TransportAssignmentFilePayload {
//		private final String admissionNumber;
//		private final String routeName;
//		private final String areaKey;
//
//		public TransportAssignmentFilePayload(String admissionNumber, String routeName, String areaKey) {
//			this.admissionNumber = admissionNumber;
//			this.routeName = routeName;
//			this.areaKey = areaKey;
//		}
//
//		public String getAdmissionNumber() {
//			return admissionNumber;
//		}
//
//		public String getRouteName() {
//			return routeName;
//		}
//
//		public String getAreaKey() {
//			return areaKey;
//		}
//
//		@Override
//		public String toString() {
//			return "TransportAssignmentFilePayload [admissionNumber=" + admissionNumber + ", routeName=" + routeName
//					+ ", areaKey=" + areaKey + "]";
//		}
//
//	}
//
//	private class FeeAssignmentFilePayload {
//		private final String admissionNumber;
//		private final String feeName;
//		private final String feeHeadName;
//		private final Double amount;
//
//		public FeeAssignmentFilePayload(String admissionNumber, String feeName, String feeHeadName, Double amount) {
//			this.admissionNumber = admissionNumber;
//			this.feeName = feeName;
//			this.feeHeadName = feeHeadName;
//			this.amount = amount;
//		}
//
//		public String getAdmissionNumber() {
//			return admissionNumber;
//		}
//
//		public String getFeeName() {
//			return feeName;
//		}
//
//		public String getFeeHeadName() {
//			return feeHeadName;
//		}
//
//		public Double getAmount() {
//			return amount;
//		}
//
//		@Override
//		public String toString() {
//			return "FeeAssignmentFilePayload [admissionNumber=" + admissionNumber + ", feeName=" + feeName
//					+ ", feeHeadName=" + feeHeadName + ", amount=" + amount + "]";
//		}
//
//	}
//
//	private class FeePaymentFilePayload {
//		private final String admissionNumber;
//		private final Double paidAmount;
//		private final Double discount;
//
//		public FeePaymentFilePayload(String admissionNumber, Double paidAmount, Double discount) {
//			this.admissionNumber = admissionNumber;
//			this.paidAmount = paidAmount;
//			this.discount = discount;
//		}
//
//		public String getAdmissionNumber() {
//			return admissionNumber;
//		}
//
//		public Double getPaidAmount() {
//			return paidAmount;
//		}
//
//		public Double getDiscount() {
//			return discount;
//		}
//
//		@Override
//		public String toString() {
//			return "FeePaymentFilePayload [admissionNumber=" + admissionNumber + ", paidAmount=" + paidAmount
//					+ ", discount=" + discount + "]";
//		}
//
//	}
//
//	private class StudentDetailFilePayload {
//		private final String admissionNumber;
//		private final String primaryContact;
//
//		public StudentDetailFilePayload(String admissionNumber, String primaryContact) {
//			this.admissionNumber = admissionNumber;
//			this.primaryContact = primaryContact;
//		}
//
//		public String getAdmissionNumber() {
//			return admissionNumber;
//		}
//
//		public String getPrimaryContact() {
//			return primaryContact;
//		}
//
//		@Override
//		public String toString() {
//			return "StudentDetailFilePayload [admissionNumber=" + admissionNumber + ", primaryContact=" + primaryContact
//					+ "]";
//		}
//
//	}
}
