package com.lernen.cloud.dev.tools.transport.assignment;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Options;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.examination.StudentExamMarksDetails;
import com.lernen.cloud.core.api.fees.FeeHeadConfigurationResponse;
import com.lernen.cloud.core.api.fees.payment.FeeHeadPaymentDetails;
import com.lernen.cloud.core.api.fees.payment.FeeHeadTransactionAmounts;
import com.lernen.cloud.core.api.fees.payment.FeeIdFeeHeadTransaction;
import com.lernen.cloud.core.api.fees.payment.FeePaymentDetails;
import com.lernen.cloud.core.api.fees.payment.FeePaymentPayload;
import com.lernen.cloud.core.api.fees.payment.FeePaymentResponse;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionMetaData;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.fees.payment.StudentFeesDetails;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.transport.TransportArea;
import com.lernen.cloud.core.api.transport.TransportAssignmentPayload;
import com.lernen.cloud.core.api.transport.TransportConfiguredFeeData;
import com.lernen.cloud.core.api.transport.TransportFeeConfigData;
import com.lernen.cloud.core.api.transport.TransportHistoryFeeIdAmount;
import com.lernen.cloud.core.api.transport.TransportServiceRouteResponse;
import com.lernen.cloud.core.api.transport.TransportServiceRouteStoppagesResponse;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.lib.transport.configuration.TransportAssignmentManager;
import com.lernen.cloud.core.lib.transport.configuration.TransportConfigurationManager;
import com.lernen.cloud.core.lib.transport.configuration.TransportFeeConfigurationManager;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;
import com.lernen.cloud.dev.tools.ingest.student.IngestMPPSStudents;

/**
 * 
 * <AUTHOR>
 *
 */
public class TransportAssignmentCreator10030 {
//	private static final Logger logger = LogManager.getLogger(TransportAssignmentCreator10030.class);
//
//	private static final String INSTITUTE_ID = "i";
//	private static final String ACADEMIC_SESSION_ID = "s";
//	private static final String FILE_PATH = "f";
//	private static final String FILE_HEADER = "h";
//	private static final String UPDATE = "u";
//	private static final String FILE_DELIMITER = ",";
//
//	private static final Map<String, String> AREA_ROUTE_MAP = new HashMap<String, String>();
//
//	static {
//		AREA_ROUTE_MAP.put("chouna", "Khangora-Uncha Amipur-Chouna");
//		AREA_ROUTE_MAP.put("dhokalpur", "Rasoolpur-Dhokalpur");
//		AREA_ROUTE_MAP.put("khangora", "Khangora-Uncha Amipur-Chouna");
//		AREA_ROUTE_MAP.put("n.t.p.c", "N.T.P.C-Tatarpur-Patadi");
//		AREA_ROUTE_MAP.put("patadi", "N.T.P.C-Tatarpur-Patadi");
//		AREA_ROUTE_MAP.put("piyawali", "Rasoolpur-Patadi-Piyawali");
//		AREA_ROUTE_MAP.put("rasoolpur", "Rasoolpur-Dhokalpur");
//		AREA_ROUTE_MAP.put("tatarpur", "N.T.P.C-Tatarpur-Patadi");
//		AREA_ROUTE_MAP.put("uncha amipur", "Khangora-Uncha Amipur-Chouna");
////		AREA_ROUTE_MAP.put("t-gate","Khangora-Uncha Amipur-Chouna");
//
//	}
//	private final TransportAssignmentManager transportAssignmentManager;
//	private final TransportConfigurationManager transportConfigurationManager;
//	private final TransportFeeConfigurationManager transportFeeConfigurationManager;
//	private final StudentManager studentManager;
//	private final FeePaymentManager feePaymentManager;
//	private final InstituteManager instituteManager;
//
//	public TransportAssignmentCreator10030(TransportAssignmentManager transportAssignmentManager,
//			TransportConfigurationManager transportConfigurationManager,
//			TransportFeeConfigurationManager transportFeeConfigurationManager, StudentManager studentManager,
//			FeePaymentManager feePaymentManager, InstituteManager instituteManager) {
//		this.transportAssignmentManager = transportAssignmentManager;
//		this.transportConfigurationManager = transportConfigurationManager;
//		this.transportFeeConfigurationManager = transportFeeConfigurationManager;
//		this.studentManager = studentManager;
//		this.feePaymentManager = feePaymentManager;
//		this.instituteManager = instituteManager;
//	}
//
//	public static void main(String args[]) {
//		final ApplicationContext context = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
//		final TransportAssignmentCreator10030 transportAssignmentCreator = context
//				.getBean(TransportAssignmentCreator10030.class);
//		Options options = buildOptions();
//		CommandLine cmdLine = null;
//		try {
//			final CommandLineParser parser = new DefaultParser();
//			cmdLine = parser.parse(options, args);
//		} catch (final Exception pex) {
//			System.err.print("Unable to parse arguments.");
//			pex.printStackTrace();
//			final HelpFormatter formatter = new HelpFormatter();
//			formatter.printHelp("TransportAssignmentCreator ", options);
//			return;
//		}
//
//		if (cmdLine == null) {
//			logger.error("Unable to parse arguments from cmdline.");
//			return;
//		}
//
//		int instituteId = 0;
//		if (cmdLine.hasOption(INSTITUTE_ID)) {
//			instituteId = Integer.parseInt(cmdLine.getOptionValue(INSTITUTE_ID));
//		} else {
//			logger.error("No institute id passed. Exitting");
//			return;
//		}
//		int academicSessionId = 0;
//		if (cmdLine.hasOption(ACADEMIC_SESSION_ID)) {
//			academicSessionId = Integer.parseInt(cmdLine.getOptionValue(ACADEMIC_SESSION_ID));
//		} else {
//			logger.error("No academicSessionId passed. Exitting");
//			return;
//		}
//
//		String filePath = null;
//		if (cmdLine.hasOption(FILE_PATH)) {
//			filePath = cmdLine.getOptionValue(FILE_PATH);
//		} else {
//			logger.error("No file path passed. Exitting");
//			return;
//		}
//		boolean fileHeader = false;
//		if (cmdLine.hasOption(FILE_HEADER)) {
//			fileHeader = true;
//		}
//		boolean updateAssignment = false;
//		if (cmdLine.hasOption(UPDATE)) {
//			updateAssignment = true;
//		}
//		logger.info("Running for institute = " + instituteId + " , session = " + academicSessionId + ", header = "
//				+ fileHeader + " , assign = " + updateAssignment);
//		transportAssignmentCreator.assignTransportFromFile(instituteId, academicSessionId, filePath, fileHeader,
//				updateAssignment);
//	}
//
//	public void assignTransportFromFile(int instituteId, int academicSessionId, String filePath, boolean fileHeader,
//			boolean updateAssignment) {
//		List<TransportAssignmentFilePayload> transportAssignmentFilePayloads = readAssignmentFile(filePath, fileHeader);
//		logger.info("Total students in file = " + transportAssignmentFilePayloads.size());
//
//		for (TransportAssignmentFilePayload transportAssignmentFilePayload : transportAssignmentFilePayloads) {
//			logger.info(transportAssignmentFilePayload.toString());
//		}
//		Map<String, Student> studentMap = new HashMap<>();
//		List<Student> enrolledStudents = studentManager.getStudentsInAcademicSesison(instituteId, academicSessionId);
//		for (Student studentResponse : enrolledStudents) {
//			studentMap.put(studentResponse.getStudentBasicInfo().getAdmissionNumber().toLowerCase(), studentResponse);
//		}
//
//		List<TransportAssignmentPayload> transportAssignmentPayloads = getTransportAssignmentPayload(instituteId,
//				academicSessionId, transportAssignmentFilePayloads, studentMap);
//		boolean totalSuccess = true;
//		int successCount = 0;
//		int failCount = 0;
//		logger.info("Assigning transport for total records = " + transportAssignmentPayloads.size());
//		for (TransportAssignmentPayload transportAssignmentPayload : transportAssignmentPayloads) {
//			logger.info("Dry run payload = " + transportAssignmentPayload.toString());
//			if (!updateAssignment) {
//				continue;
//			}
//			if (transportAssignmentManager.addTransport(transportAssignmentPayload, null)) {
//				logger.info("Assigned transport ammount successfully.");
//				successCount++;
//			} else {
//				logger.error("ERROR : Assigned transport ammount not success.");
//				totalSuccess = false;
//				failCount++;
//			}
//		}
//		if (totalSuccess) {
//			logger.info("All records are assigned successfully.");
//		} else {
//			logger.info("ERROR : All records are not assigned  successfully.");
//		}
//		logger.info("Total = " + (successCount + failCount) + ", success = " + successCount + " , fail = " + failCount);
//
//		logger.info("\n---------------------------------------------------------------\n");
//
//		if (updateAssignment) {
//			logger.info("Adding transport payment");
//			assignPaidAmount(instituteId, academicSessionId, transportAssignmentFilePayloads, studentMap);
//		}
//	}
//
//	private void assignPaidAmount(int instituteId, int academicSessionId,
//			List<TransportAssignmentFilePayload> transportAssignmentFilePayloads, Map<String, Student> studentMap) {
//
//		int success = 0;
//		int failure = 0;
//		int noTransactions = 0;
//		int noAssignment = 0;
//		int count = 1;
//		for (TransportAssignmentFilePayload transportAssignmentFilePayload : transportAssignmentFilePayloads) {
//
//			final FeeHeadConfigurationResponse transportFeeHead = instituteManager.getTransportFeeHead(instituteId);
//			int transportFeeHeadId = transportFeeHead.getFeeHeadConfiguration().getFeeHeadId();
//			Student student = studentMap.get(transportAssignmentFilePayload.getAdmissionNumber().toLowerCase());
//			if (student == null) {
//				logger.error("Student not found for entry {}", transportAssignmentFilePayload);
//				failure++;
//				continue;
//			}
//			logger.info("{} Adding payment for student {}, entry {}", count++, student.getStudentId(),
//					transportAssignmentFilePayload);
//
//			if (transportAssignmentFilePayload.getPaidAmount() == null
//					|| transportAssignmentFilePayload.getPaidAmount() < 0d
//					|| transportAssignmentFilePayload.getBalance() == null
//					|| transportAssignmentFilePayload.getBalance() < 0d) {
//				logger.info("Total amount and balance amount not provided. Skipping student {}",
//						student.getStudentId());
//				noAssignment++;
//				continue;
//			}
//
//			StudentFeesDetails studentFeesDetails = feePaymentManager.getPaymentDetails(instituteId, academicSessionId,
//					student.getStudentId());
//
//			List<FeePaymentDetails> transportFeeDetails = new ArrayList<>();
//			double totalAssignedAmount = 0d;
//			for (FeePaymentDetails feePaymentDetails : studentFeesDetails.getFeePaymentDetailsList()) {
//				if (feePaymentDetails.getFeeHeadPaymentDetails().size() == 1
//						&& feePaymentDetails.getFeeHeadPaymentDetails().get(0).getFeeHeadConfiguration()
//								.getFeeHeadId() == transportFeeHeadId) {
//					transportFeeDetails.add(feePaymentDetails);
//					totalAssignedAmount += feePaymentDetails.getTotalAssignedAmount();
//				}
//			}
//
//			logger.info("Total transport fees count {}", transportFeeDetails.size());
//
//			Collections.sort(transportFeeDetails);
//			double discountAmount = totalAssignedAmount
//					- (transportAssignmentFilePayload.getPaidAmount() + transportAssignmentFilePayload.getBalance());
//			double totalPaidAmount = transportAssignmentFilePayload.getPaidAmount();
//			List<FeeIdFeeHeadTransaction> feeIdFeeHeadTransactionList = new ArrayList<FeeIdFeeHeadTransaction>();
//
//			for (FeePaymentDetails feePaymentDetails : transportFeeDetails) {
//				UUID feeId = feePaymentDetails.getFeeConfigurationBasicInfo().getFeeId();
//				double assignedAmount = feePaymentDetails.getTotalAssignedAmount();
//				if (Double.compare(discountAmount, 0d) > 0) {
//					double appliedDiscountAmount = assignedAmount < discountAmount ? assignedAmount : discountAmount;
//					double paidAmount = assignedAmount - appliedDiscountAmount < totalPaidAmount
//							? assignedAmount - appliedDiscountAmount
//							: totalPaidAmount;
//					FeeHeadTransactionAmounts feeHeadTransactionAmounts = new FeeHeadTransactionAmounts(
//							transportFeeHeadId, paidAmount, appliedDiscountAmount, 0d);
//
//					discountAmount -= appliedDiscountAmount;
//					totalPaidAmount -= paidAmount;
//
//					feeIdFeeHeadTransactionList
//							.add(new FeeIdFeeHeadTransaction(feeId, Arrays.asList(feeHeadTransactionAmounts)));
//				} else {
//
//					double paidAmount = assignedAmount < totalPaidAmount ? assignedAmount : totalPaidAmount;
//					if (Double.compare(paidAmount, 0d) <= 0) {
//						break;
//					}
//
//					FeeHeadTransactionAmounts feeHeadTransactionAmounts = new FeeHeadTransactionAmounts(
//							transportFeeHeadId, paidAmount, 0d, 0d);
//
//					totalPaidAmount -= paidAmount;
//
//					feeIdFeeHeadTransactionList
//							.add(new FeeIdFeeHeadTransaction(feeId, Arrays.asList(feeHeadTransactionAmounts)));
//				}
//			}
//
//			if (CollectionUtils.isEmpty(feeIdFeeHeadTransactionList)) {
//				logger.info("No fees transaction for student {}", student.getStudentId());
//				noTransactions++;
//				continue;
//			}
//
//			FeePaymentTransactionMetaData feePaymentTransactionMetaData = new FeePaymentTransactionMetaData();
//			feePaymentTransactionMetaData.setInstituteId(instituteId);
//			feePaymentTransactionMetaData.setAcademicSessionId(academicSessionId);
//			feePaymentTransactionMetaData.setStudentId(student.getStudentId());
//			feePaymentTransactionMetaData.setFeePaymentTransactionStatus(FeePaymentTransactionStatus.ACTIVE);
//			feePaymentTransactionMetaData.setTransactionDate((int) (System.currentTimeMillis() / 1000l));
//			feePaymentTransactionMetaData.setTransactionMode(TransactionMode.CASH);
//
//			FeePaymentPayload feePaymentPayload = new FeePaymentPayload(feePaymentTransactionMetaData,
//					feeIdFeeHeadTransactionList);
//			logger.info("Fee payment payload {}", feePaymentPayload);
//
//			FeePaymentResponse feePaymentResponse = feePaymentManager.addFeePayment(feePaymentPayload, false, null);
//			if (feePaymentResponse == null) {
//				logger.error("Unable to add payment for student {} ", student.getStudentId());
//				failure++;
//			} else {
//				logger.info("Successfully added payment for student {} ", student.getStudentId());
//				success++;
//			}
//		}
//
//		logger.info("Total payment entries {}, success {}, failure {}, no transactions {}, noAssignment {}", count - 1,
//				success, failure, noTransactions, noAssignment);
//
//	}
//
//	private final List<TransportAssignmentPayload> getTransportAssignmentPayload(int instituteId, int academicSessionId,
//			List<TransportAssignmentFilePayload> transportAssignmentFilePayloads, Map<String, Student> studentMap) {
//		List<TransportAssignmentPayload> transportAssignmentPayloads = new ArrayList<>();
//		List<TransportArea> transportAreas = transportConfigurationManager.getTransportAreas(instituteId);
//		List<TransportServiceRouteResponse> transportServiceRouteResponses = transportConfigurationManager
//				.getTransportServiceRoutes(instituteId, academicSessionId);
//
//		Map<String, TransportArea> transportAreaMap = new HashMap<>();
//		Map<String, TransportServiceRouteResponse> transportServiceRouteResponseMap = new HashMap<>();
//
//		for (TransportArea transportArea : transportAreas) {
//			transportAreaMap.put(transportArea.getAreaKey().toLowerCase(), transportArea);
//		}
//
//		for (TransportServiceRouteResponse transportServiceRouteResponse : transportServiceRouteResponses) {
//			transportServiceRouteResponseMap.put(transportServiceRouteResponse.getServiceRouteName().toLowerCase(),
//					transportServiceRouteResponse);
//		}
//
//		TransportFeeConfigData transportFeeConfigData = transportFeeConfigurationManager
//				.getTransportFeeConfigData(instituteId, academicSessionId);
//		logger.info(transportFeeConfigData);
//		List<TransportConfiguredFeeData> transportConfiguredFeeDatas1 = transportFeeConfigData
//				.getTransportConfiguredFeeDatas();
//		logger.info("Is exam configured = " + transportFeeConfigData.isConfigured());
//		if (CollectionUtils.isEmpty(transportConfiguredFeeDatas1)) {
//			logger.error("Invalid fee configured. Exitting");
//			return null;
//		}
//		for (TransportAssignmentFilePayload transportAssignmentFilePayload : transportAssignmentFilePayloads) {
//			logger.info(transportAssignmentFilePayload);
//			Student studentResponse = studentMap.get(transportAssignmentFilePayload.getAdmissionNumber().toLowerCase());
//			TransportArea transportArea = transportAreaMap
//					.get(transportAssignmentFilePayload.getAreaKey().toLowerCase());
//			TransportServiceRouteResponse transportServiceRouteResponse = transportServiceRouteResponseMap
//					.get(transportAssignmentFilePayload.getRouteName().toLowerCase());
//
//			if (studentResponse == null || transportArea == null || transportServiceRouteResponse == null) {
//				logger.error("Invalid student, or transport area or route. Exitting "
//						+ transportAssignmentFilePayload.toString());
//				continue;
//			}
//			// System.out.println(studentResponse);
//			// System.out.println(transportArea);
//			// System.out.println(transportServiceRouteResponse);
//			//
//			TransportAssignmentPayload transportAssignmentPayload = new TransportAssignmentPayload();
//			transportAssignmentPayload.setInstituteId(instituteId);
//			transportAssignmentPayload.setStudentId(studentResponse.getStudentId());
//			transportAssignmentPayload.setAcademicSessionId(academicSessionId);
//			transportAssignmentPayload.setAreaId(transportArea.getAreaId());
//			transportAssignmentPayload.setServiceRouteId(transportServiceRouteResponse.getServiceRouteId());
//			transportAssignmentPayload.setCompleteSession(true);
//
//			List<TransportHistoryFeeIdAmount> transportHistoryFeeIdAmountList = new ArrayList<>();
//			Double totalAmount = null;
//			for (TransportServiceRouteStoppagesResponse transportServiceRouteStoppagesResponse : transportServiceRouteResponse
//					.getStoppagesList()) {
//				if (transportServiceRouteStoppagesResponse.getTransportArea().getAreaId() == transportArea
//						.getAreaId()) {
//					totalAmount = transportServiceRouteStoppagesResponse.getAssignedAmount();
//					break;
//				}
//			}
//			if (totalAmount == null) {
//				logger.error("Invalid total assigned amount for fees. Exitting");
//				return null;
//			}
//			for (TransportConfiguredFeeData transportConfiguredFeeData : transportConfiguredFeeDatas1) {
//				TransportHistoryFeeIdAmount transportHistoryFeeIdAmount = new TransportHistoryFeeIdAmount();
//				transportHistoryFeeIdAmount.setFeeId(transportConfiguredFeeData.getFeeConfigurationResponse()
//						.getFeeConfigurationBasicInfo().getFeeId());
//				transportHistoryFeeIdAmount.setAmount(
//						totalAmount * transportConfiguredFeeData.getModuleFeeProportion().getFeeProportion());
//				transportHistoryFeeIdAmountList.add(transportHistoryFeeIdAmount);
//			}
//			transportAssignmentPayload.setTransportHistoryFeeIdAmountList(transportHistoryFeeIdAmountList);
//			transportAssignmentPayloads.add(transportAssignmentPayload);
//		}
//		return transportAssignmentPayloads;
//	}
//
//	private List<TransportAssignmentFilePayload> readAssignmentFile(String fileName, boolean header) {
//		List<TransportAssignmentFilePayload> transportAssignmentFilePayloads = new ArrayList<>();
//		try (final BufferedReader br = new BufferedReader(new FileReader(fileName))) {
//			String line = null;
//			while ((line = br.readLine()) != null) {
//				if (header) {
//					header = false;
//					continue;
//				}
//				if (StringUtils.isBlank(line)) {
//					continue;
//				}
//				String[] columns = line.split(FILE_DELIMITER, 9);
//				if (columns.length < 9) {
//					logger.error("Columns are not " + 9 + ". Skipping it as its not for assignment. Entry = " + line);
//					continue;
//				}
//				String admissionNumber = columns[1].trim();
//				String areaName = columns[6].trim();
//				if (StringUtils.isBlank(areaName)) {
//					logger.error("Not valid entry for assignment. Entry = " + line);
//					continue;
//				}
//				String routeName = columns[5].trim();
//				if (StringUtils.isBlank(routeName)) {
//					logger.error("Blank route name. Not valid entry for assignment. Entry = " + line);
//					continue;
//				}
//
////				String routeName = AREA_ROUTE_MAP.get(areaName.toLowerCase());
////				if (routeName == null) {
////					logger.error("Not found any route for assignment. Entry = " + line);
////					continue;
////				}
//				String paidAmountStr = columns[7].trim();
//				Double paidAmount = null;
//				if (!StringUtils.isEmpty(paidAmountStr)) {
//					paidAmount = Double.parseDouble(paidAmountStr);
//				}
//
//				String balanceAmountStr = columns[8].trim();
//				Double balanceAmount = null;
//				if (!StringUtils.isEmpty(balanceAmountStr)) {
//					balanceAmount = Double.parseDouble(balanceAmountStr);
//				}
//				transportAssignmentFilePayloads.add(new TransportAssignmentFilePayload(admissionNumber, routeName,
//						areaName, paidAmount, balanceAmount));
//			}
//			return transportAssignmentFilePayloads;
//		} catch (final Exception e) {
//			logger.error("Error while reading file", e);
//		}
//		return null;
//	}
//
//	private static Options buildOptions() {
//		final Options options = new Options();
//		options.addOption(INSTITUTE_ID, true, "specify the institute id");
//		options.addOption(ACADEMIC_SESSION_ID, true, "Academic session id for institute");
//		options.addOption(FILE_PATH, true, "Transport assignment csv file path");
//		options.addOption(FILE_HEADER, false, "File has header");
//		options.addOption(UPDATE, false, "Update the assignments");
//		return options;
//	}
//
//	private class TransportAssignmentFilePayload {
//		private final String admissionNumber;
//		private final String routeName;
//		private final String areaKey;
//		private final Double paidAmount;
//		private final Double balance;
//
//		public TransportAssignmentFilePayload(String admissionNumber, String routeName, String areaKey,
//				Double paidAmount, Double balance) {
//			this.admissionNumber = admissionNumber;
//			this.routeName = routeName;
//			this.areaKey = areaKey;
//			this.paidAmount = paidAmount;
//			this.balance = balance;
//		}
//
//		public String getAdmissionNumber() {
//			return admissionNumber;
//		}
//
//		public String getRouteName() {
//			return routeName;
//		}
//
//		public String getAreaKey() {
//			return areaKey;
//		}
//
//		public Double getPaidAmount() {
//			return paidAmount;
//		}
//
//		public Double getBalance() {
//			return balance;
//		}
//
//		@Override
//		public String toString() {
//			return "TransportAssignmentFilePayload [admissionNumber=" + admissionNumber + ", routeName=" + routeName
//					+ ", areaKey=" + areaKey + ", paidAmount=" + paidAmount + ", balance=" + balance + "]";
//		}
//
//	}
}
