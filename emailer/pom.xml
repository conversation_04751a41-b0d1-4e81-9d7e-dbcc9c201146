<?xml version="1.0"?>
<project
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
        xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.embrate.cloud</groupId>
        <artifactId>embrate-backend</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <groupId>com.lernen.cloud.emailer</groupId>
    <artifactId>emailer</artifactId>
    <name>emailer</name>
    <packaging>jar</packaging>
    <url>https://www.embrate.com</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.lernen.cloud.core.api</groupId>
            <artifactId>core-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.lernen.cloud.core.lib</groupId>
            <artifactId>core-lib</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sparkpost</groupId>
            <artifactId>sparkpost-lib</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.sendgrid/sendgrid-java -->
        <dependency>
            <groupId>com.sendgrid</groupId>
            <artifactId>sendgrid-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-ses</artifactId>
        </dependency>
    </dependencies>
</project>
