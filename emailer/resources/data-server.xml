<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-3.0.xsd 
	http://www.springframework.org/schema/context
	http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<!-- <context:property-placeholder location="classpath:utils-${my.env}.properties, 
		classpath:core-pii-${my.env}.properties, classpath:core-cass-${my.env}.properties, 
		classpath:core-iam-${my.env}.properties,classpath:core-lib-${my.env}.properties, 
		classpath:core-emails-${my.env}.properties, classpath:emails.properties,classpath:event-messages.properties,classpath:core-event.properties, 
		/WEB-INF/${my.env}.properties,classpath:sms-global.properties,classpath:sms-${my.env}.properties" 
		system-properties-mode="OVERRIDE" /> -->


	<import resource="classpath:core-lib.xml" />


	<context:component-scan base-package="com.lernen.cloud.data.server" />

	<bean id="testEndPoint" class="com.lernen.cloud.data.server.TestEndPoint">
		<constructor-arg name="helloWorld" ref="helloWorld" />
		<constructor-arg name="testDao" ref="testDao" />
	</bean>

	<bean id="userEndPoint" class="com.lernen.cloud.data.server.user.UserEndPoint">
		<constructor-arg name="userManager" ref="userManager" />
	</bean>

	<bean id="libraryEndpoint" class="com.lernen.cloud.data.server.library.LibraryEndpoint">
		<constructor-arg name="libraryManager" ref="libraryManager" />
	</bean>

	<bean id="storeInventoryEndpoint"
		class="com.lernen.cloud.data.server.inventory.StoreInventoryEndPoint">
		<constructor-arg name="storeInventoryManager" ref="storeInventoryManager" />
	</bean>

	<bean id="productEndpoint" class="com.lernen.cloud.data.server.inventory.ProductEndpoint">
		<constructor-arg name="storeInventoryManager" ref="storeInventoryManager" />
	</bean>

	<bean id="productTransactionsEndpoint"
		class="com.lernen.cloud.data.server.inventory.ProductTransactionsEndpoint">
		<constructor-arg name="productTransactionsManager"
			ref="productTransactionsManager" />
	</bean>

	<bean id="reportGenerationEndPoint"
		class="com.lernen.cloud.data.server.inventory.ReportGenerationEndPoint">
		<constructor-arg name="reportGenerationManager" ref="reportGenerationManager" />
	</bean>

	<!-- <bean id="ingestionEndpoint" class="com.lernen.cloud.data.server.inventory.IngestionEndpoint"> 
		<constructor-arg name="stockIngester" ref="stockIngester" /> </bean> -->

	<bean id="instituteEndPoint" class="com.lernen.cloud.data.server.institute.InstitueEndPoint">
		<constructor-arg name="instituteManager" ref="instituteManager" />
	</bean>

	<bean id="productGroupEndPoint"
		class="com.lernen.cloud.data.server.inventory.ProductGroupEndPoint">
		<constructor-arg name="productGroupManager" ref="productGroupManager" />
	</bean>

	<bean id="feeConfigurationEndPoint"
		class="com.lernen.cloud.data.server.fees.FeeConfigurationEndPoint">
		<constructor-arg name="feeConfigurationManager" ref="feeConfigurationManager" />
	</bean>

	<bean id="feeDiscountConfigurationEndPoint"
		class="com.lernen.cloud.data.server.fees.FeeDiscountConfigurationEndPoint">
		<constructor-arg name="feeDiscountConfigurationManager"
			ref="feeDiscountConfigurationManager" />
	</bean>

	<bean id="studentEndPoint" class="com.lernen.cloud.data.server.student.StudentEndPoint">
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="documentManager" ref="documentManager" />
	</bean>

	<bean id="feePaymentEndPoint" class="com.lernen.cloud.data.server.fees.FeePaymentEndPoint">
		<constructor-arg name="feePaymentManager" ref="feePaymentManager" />
	</bean>

</beans>
