package com.embrate.cloud.lambda.utils.handler.impl;

import com.amazonaws.services.lambda.runtime.LambdaLogger;
import com.embrate.cloud.core.api.hpc.utils.HPCExamType;
import com.embrate.cloud.lambda.utils.api.AWSLambdaRequestResponse;
import com.embrate.cloud.pdf.hpc.HPCPdfServiceProvider;
import com.google.common.base.Joiner;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */

public class StudentHPCReportCardsRequestHandler extends AWSLambdaRequestHandler {

    public static String ID = "StudentHPCReportCards";

    @Override
    public String getId() {
        return ID;
    }

    private final ClassLoader classLoader = HPCPdfServiceProvider.class.getClassLoader();

    @Override
    public AWSLambdaRequestResponse handle(Map<String, String> requestData, LambdaLogger logger) {
        String templateClass = "";
        try {
            int instituteId = getInstituteId(requestData);
            int academicSessionId = getAcademicSessionId(requestData);
            UUID standardId = getStandardId(requestData);
            Set<Integer> sectionIds = getSectionIds(requestData);
            Integer sectionId = null;
            if (!CollectionUtils.isEmpty(sectionIds)) {
                sectionId = sectionIds.iterator().next();
            }
            HPCExamType hpcExamType = getHPCExamType(requestData);
            UUID userId = getUserId(requestData);
            String reportCardFor = getReportCardFor(requestData);
            List<UUID> studentIds = getStudentIdList(requestData);


            if (instituteId <= 0 || hpcExamType == null || academicSessionId <= 0 || standardId == null) {
                logger.log("Invalid arguments " + Joiner.on(",").join(instituteId, hpcExamType, academicSessionId, standardId));
                return new AWSLambdaRequestResponse(false, null, null, null);
            }

            ApplicationContext applicationContext = getContext();
            HPCPdfServiceProvider hpcPdfServiceProvider = applicationContext.getBean(HPCPdfServiceProvider.class);

            logger.log("Generating hpc exam report for instituteId " + instituteId + " academicSessionId " + academicSessionId +
                    ", standardId " + standardId + ", reportType " + hpcExamType);

            if(reportCardFor.equalsIgnoreCase("BULK")) {
                DocumentOutput documentOutput = hpcPdfServiceProvider.getBulkStudentHPC(instituteId, academicSessionId, studentIds, hpcExamType, userId);
                return new AWSLambdaRequestResponse(true, documentOutput == null ? null : documentOutput.getName(),
                        documentOutput == null ? null : documentOutput.getContent(), applicationContext);
            }

            DocumentOutput documentOutput = hpcPdfServiceProvider.getClassHPC(instituteId, academicSessionId, standardId,
                sectionId == null ? null : new HashSet<>(Arrays.asList(sectionId)), hpcExamType, userId);
            return new AWSLambdaRequestResponse(true, documentOutput == null ? null : documentOutput.getName(),
                    documentOutput == null ? null : documentOutput.getContent(), applicationContext);

        } catch (Exception e) {
            e.printStackTrace();
            logger.log("-------------------- Exception during Document Output ----------- \n " + e.getMessage() + " \n " + e + " \n");
        }

        return new AWSLambdaRequestResponse(false, null, null, null);
    }
}
