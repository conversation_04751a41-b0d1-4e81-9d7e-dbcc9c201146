package com.embrate.cloud.lambda.utils.router;


import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.LambdaLogger;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.embrate.cloud.core.lib.filesystem.S3FileSystem;
import com.embrate.cloud.lambda.utils.handler.impl.AWSLambdaRequestHandler;
import com.embrate.cloud.lambda.utils.handler.AWSLambdaRequestHandlerRegistry;
import com.embrate.cloud.lambda.utils.api.AWSLambdaRequestResponse;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.lernen.cloud.core.api.common.FileData;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;

import java.io.ByteArrayOutputStream;
import java.util.Map;
import java.util.UUID;

import static com.embrate.cloud.lambda.utils.api.AWSLambdaRequestConstants.HANDLER_ID;

/**
 * <AUTHOR>
 */

public class AWSLambdaRequestRouter implements RequestHandler<Map<String, String>, String> {
    public static String SUCCESS_CODE = "200";
    public static String FAILURE_CODE = "400";

    public static String RESULT_S3_BUCKET = "lernen-artifacts-v5";
    public static String RESULT_S3_PATH = "tmp";
    public static String SLASH = "/";

    public static String PIPE = "|";

    Gson gson = new GsonBuilder().setPrettyPrinting().create();

    @Override
    public String handleRequest(Map<String, String> event, Context context) {
        LambdaLogger logger = context.getLogger();
        if (MapUtils.isEmpty(event) || StringUtils.isBlank(event.get(HANDLER_ID.getId()))) {
            logger.log("Invalid event map, skipping execution");
            return FAILURE_CODE;
        }
        String handlerId = event.get(HANDLER_ID.getId());
        AWSLambdaRequestHandlerRegistry awsLambdaRequestHandlerRegistry = new AWSLambdaRequestHandlerRegistry();
        AWSLambdaRequestHandler awsLambdaRequestHandler = awsLambdaRequestHandlerRegistry.get(handlerId);
        if (awsLambdaRequestHandler == null) {
            logger.log("No event handler found for " + handlerId + ", skipping execution");
            return FAILURE_CODE;
        }
        AWSLambdaRequestResponse awsLambdaRequestResponse = awsLambdaRequestHandler.handle(event, logger);
        if (awsLambdaRequestResponse == null || !awsLambdaRequestResponse.isSuccess()) {
            logger.log("Failure during event handling");
            return FAILURE_CODE;
        }
        ByteArrayOutputStream content = awsLambdaRequestResponse.getContent();
        ApplicationContext applicationContext = awsLambdaRequestHandler.getContext();
        if (content == null || applicationContext == null) {
            logger.log("No document content found, skipping s3 upload");
            return SUCCESS_CODE;
        }

        try {
            S3FileSystem s3FileSystem = applicationContext.getBean(S3FileSystem.class);
            String bucketPrefix = "s3://" + RESULT_S3_BUCKET + SLASH;
            String pathKey = RESULT_S3_PATH + SLASH + UUID.randomUUID() + "."+FilenameUtils.getExtension(awsLambdaRequestResponse.getFileName());
            String s3Path = bucketPrefix + pathKey;
            if (s3FileSystem.writeFile(RESULT_S3_BUCKET, pathKey, new FileData(content.toByteArray(), awsLambdaRequestResponse.getFileName()))) {
                logger.log("Result written to s3 path = " + s3Path);
                return SUCCESS_CODE + PIPE + s3Path + PIPE + awsLambdaRequestResponse.getFileName();
            } else {
                logger.log("Failed to write result to s3 path = " + s3Path);
                return FAILURE_CODE;
            }
        } catch (Exception e) {
            logger.log("Failed to write result to s3 path. Exception = " + e.getMessage() + " , " + e);
            return FAILURE_CODE;
        }
    }
}