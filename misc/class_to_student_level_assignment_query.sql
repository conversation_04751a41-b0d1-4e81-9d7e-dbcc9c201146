 select * from fee_assignment where institute_id = 10020 and entity_name = 'CLASS';    

 

 select 10020, student_academic_session_details.student_id, 'STUDENT', fee_assignment.fee_id, fee_assignment.fee_head_id , fee_assignment.amount   from fee_assignment join student_academic_session_details on fee_assignment.entity_id = student_academic_session_details.standard_id  where institute_id = 10020 and entity_name = 'CLASS' and academic_session_id = 4;


insert into fee_assignment select 10005, student_academic_session_details.student_id, 'STUDENT', fee_assignment.fee_id, fee_assignment.fee_head_id , fee_assignment.amount   from fee_assignment join student_academic_session_details on fee_assignment.entity_id = student_academic_session_details.standard_id  where institute_id = 10005 and entity_name = 'CLASS' and fee_assignment.entity_id='32fca99a-e375-45bd-9449-80d908fd27a8' and academic_session_id = 4;



insert into fee_assignment select 10020, student_academic_session_details.student_id, 'STUDENT', fee_assignment.fee_id, fee_assignment.fee_head_id , fee_assignment.amount   from fee_assignment join student_academic_session_details on fee_assignment.entity_id = student_academic_session_details.standard_id  where institute_id = 10020 and entity_name = 'CLASS' and academic_session_id = 4;


-- 

insert into default_fee_assignment_structure_meta_data values(10020, "Student Fee Structure", "d88082a2-ec69-46b9-8153-fa7f0d409910", "ENROLLMENT");


select "d88082a2-ec69-46b9-8153-fa7f0d409910", fee_assignment.entity_id, fee_assignment.entity_name, fee_assignment.fee_id, fee_assignment.fee_head_id, fee_assignment.amount from fee_assignment where institute_id = 10020 and entity_name = 'CLASS';     


insert into default_fee_assignment_structure select "d88082a2-ec69-46b9-8153-fa7f0d409910", fee_assignment.entity_id, fee_assignment.entity_name, fee_assignment.fee_id, fee_assignment.fee_head_id, fee_assignment.amount from fee_assignment where institute_id = 10020 and entity_name = 'CLASS';     



--------- 10010


 select * from fee_assignment where institute_id = 10010 and entity_name = 'CLASS';    

 delete from fee_assignment where institute_id = 10010 and entity_name = 'CLASS';    


 select 10010, student_academic_session_details.student_id, 'STUDENT', fee_assignment.fee_id, fee_assignment.fee_head_id , fee_assignment.amount   from fee_assignment join student_academic_session_details on fee_assignment.entity_id = student_academic_session_details.standard_id  where institute_id = 10010 and entity_name = 'CLASS' and academic_session_id = 3;
 
 insert into fee_assignment select 10010, student_academic_session_details.student_id, 'STUDENT', fee_assignment.fee_id, fee_assignment.fee_head_id , fee_assignment.amount   from fee_assignment join student_academic_session_details on fee_assignment.entity_id = student_academic_session_details.standard_id  where institute_id = 10010 and entity_name = 'CLASS' and academic_session_id = 3;


insert into default_fee_assignment_structure_meta_data values(10010, "Student Fee Structure", "8738f2db-39b2-4ffd-becf-5436b49f913d", "ENROLLMENT");

select "8738f2db-39b2-4ffd-becf-5436b49f913d", fee_assignment.entity_id, fee_assignment.entity_name, fee_assignment.fee_id, fee_assignment.fee_head_id, fee_assignment.amount from fee_assignment where institute_id = 10010 and entity_name = 'CLASS';     

insert into default_fee_assignment_structure select "8738f2db-39b2-4ffd-becf-5436b49f913d", fee_assignment.entity_id, fee_assignment.entity_name, fee_assignment.fee_id, fee_assignment.fee_head_id, fee_assignment.amount from fee_assignment where institute_id = 10010 and entity_name = 'CLASS';     

