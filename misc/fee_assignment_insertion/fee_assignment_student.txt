// 10228
// get fee id & fee head id
select * from fee_configuration where institute_id = 10228 and academic_session_id = ;
select * from fee_head_configuration where institute_id = 10228;


select 10228, student_id, "STUDENT", "48ce91be-b27e-4fe0-bffe-882aac6fb1f7", 394, 200 from students where institute_id = 10228 and admission_number in ("BPSB641", "BPSB643", "BPSB647", "BPSB666", "BPSB640", "BPSB644", "BPSB661", "BPSB663", "BPSB648", "BPSB654", "BPSB639", "BPSB658", "BPSB645", "BPSB662", "BPSB664", "BPSB651", "BPSB655", "BPSB642", "BPSB646", "BPSB665", "BPSB652", "BPSB656", "BPSB660", "BPSB649", "BPSB638", "BPSB653", "BPSB657", "BPSB659", "BPSB650", "BPSB2823", "BPSB2808", "BPSB2827", "BPSB2829", "BPSB2814", "BPSB2816", "BPSB2818", "BPSB2826", "BPSB2828", "BPSB2815", "BPSB2834", "BPSB2819", "BPSB2831", "BPSB2820", "BPSB2832", "BPSB2778", "BPSB2833", "BPSB2830", "BPSB2817", "BPSB2821", "BPSB2825", "BPSB2810", "BPSB2822", "BPSB2824", "BPSB2811", "BPSB2813", "BPSB2812", "BPSB2809", "BPSB2518", "BPSB2515", "BPSB2522", "BPSB2509", "BPSB2523", "BPSB2525", "BPSB2514", "BPSB2520", "BPSB2524", "BPSB2516", "BPSB2517", "BPSB2519", "BPSB2521", "BPSB2526");


select 10228, student_id, "STUDENT", "cbccfa3e-244e-4805-9559-5ce29a4f5941", 408, 2300 from students where institute_id = 10228 and admission_number in ("BPSB641", "BPSB643", "BPSB647", "BPSB666", "BPSB640", "BPSB644", "BPSB661", "BPSB663", "BPSB648", "BPSB654", "BPSB639", "BPSB658", "BPSB645", "BPSB662", "BPSB664", "BPSB651", "BPSB655", "BPSB642", "BPSB646", "BPSB665", "BPSB652", "BPSB656", "BPSB660", "BPSB649", "BPSB638", "BPSB653", "BPSB657", "BPSB659", "BPSB650", "BPSB2823", "BPSB2808", "BPSB2827", "BPSB2829", "BPSB2814", "BPSB2816", "BPSB2818", "BPSB2826", "BPSB2828", "BPSB2815", "BPSB2834", "BPSB2819", "BPSB2831", "BPSB2820", "BPSB2832", "BPSB2778", "BPSB2833", "BPSB2830", "BPSB2817", "BPSB2821", "BPSB2825", "BPSB2810", "BPSB2822", "BPSB2824", "BPSB2811", "BPSB2813", "BPSB2812", "BPSB2809", "BPSB2518", "BPSB2515", "BPSB2522", "BPSB2509", "BPSB2523", "BPSB2525", "BPSB2514", "BPSB2520", "BPSB2524", "BPSB2516", "BPSB2517", "BPSB2519", "BPSB2521", "BPSB2526");


select 10228, student_id, "STUDENT", "2ae2bd4d-f2f9-4bf0-bab9-fd7575ead45d", 409, 1000 from students where institute_id = 10228 and admission_number in ("BPSB641", "BPSB643", "BPSB647", "BPSB666", "BPSB640", "BPSB644", "BPSB661", "BPSB663", "BPSB648", "BPSB654", "BPSB639", "BPSB658", "BPSB645", "BPSB662", "BPSB664", "BPSB651", "BPSB655", "BPSB642", "BPSB646", "BPSB665", "BPSB652", "BPSB656", "BPSB660", "BPSB649", "BPSB638", "BPSB653", "BPSB657", "BPSB659", "BPSB650", "BPSB2823", "BPSB2808", "BPSB2827", "BPSB2829", "BPSB2814", "BPSB2816", "BPSB2818", "BPSB2826", "BPSB2828", "BPSB2815", "BPSB2834", "BPSB2819", "BPSB2831", "BPSB2820", "BPSB2832", "BPSB2778", "BPSB2833", "BPSB2830", "BPSB2817", "BPSB2821", "BPSB2825", "BPSB2810", "BPSB2822", "BPSB2824", "BPSB2811", "BPSB2813", "BPSB2812", "BPSB2809", "BPSB2518", "BPSB2515", "BPSB2522", "BPSB2509", "BPSB2523", "BPSB2525", "BPSB2514", "BPSB2520", "BPSB2524", "BPSB2516", "BPSB2517", "BPSB2519", "BPSB2521", "BPSB2526");



insert into fee_assignment (institute_id, entity_id, entity_name, fee_id, fee_head_id, amount) select 10228, student_id, "STUDENT", "2ae2bd4d-f2f9-4bf0-bab9-fd7575ead45d", 409, 1000 from students where institute_id = 10228 and admission_number in ("BPSB641", "BPSB643", "BPSB647", "BPSB666", "BPSB640", "BPSB644", "BPSB661", "BPSB663", "BPSB648", "BPSB654", "BPSB639", "BPSB658", "BPSB645", "BPSB662", "BPSB664", "BPSB651", "BPSB655", "BPSB642", "BPSB646", "BPSB665", "BPSB652", "BPSB656", "BPSB660", "BPSB649", "BPSB638", "BPSB653", "BPSB657", "BPSB659", "BPSB650", "BPSB2823", "BPSB2808", "BPSB2827", "BPSB2829", "BPSB2814", "BPSB2816", "BPSB2818", "BPSB2826", "BPSB2828", "BPSB2815", "BPSB2834", "BPSB2819", "BPSB2831", "BPSB2820", "BPSB2832", "BPSB2778", "BPSB2833", "BPSB2830", "BPSB2817", "BPSB2821", "BPSB2825", "BPSB2810", "BPSB2822", "BPSB2824", "BPSB2811", "BPSB2813", "BPSB2812", "BPSB2809", "BPSB2518", "BPSB2515", "BPSB2522", "BPSB2509", "BPSB2523", "BPSB2525", "BPSB2514", "BPSB2520", "BPSB2524", "BPSB2516", "BPSB2517", "BPSB2519", "BPSB2521", "BPSB2526");



insert into fee_assignment (institute_id, entity_id, entity_name, fee_id, fee_head_id, amount) select 10228, student_id, "STUDENT", "e5e83f4e-d337-4c3d-87de-6b842ff7b901", 410, 100 from students where institute_id = 10228 and admission_number in  ("BPSB599", "BPSB613", "BPSB638", "BPSB595", "BPSB617", "BPSB542", "BPSB615", "BPSB619", "BPSB620", "BPSB650", "BPSB649", "BPSB602", "BPSB616", "BPSB614", "BPSB635", "BPSB606", "BPSB656", "BPSB660", "BPSB659", "BPSB2785", "BPSB2814", "BPSB2788", "BPSB2758", "BPSB2816", "BPSB2819", "BPSB2818", "BPSB2797", "BPSB2791", "BPSB2779", "BPSB2823", "BPSB2789", "BPSB2780", "BPSB2781", "BPSB2783", "BPSB2746", "BPSB2730", "BPSB2745", "BPSB2771", "BPSB2832", "BPSB2744", "BPSB2738", "BPSB2740", "BPSB2693", "BPSB2663", "BPSB2778", "BPSB2690", "BPSB2775", "BPSB2687", "BPSB2669", "BPSB2686", "BPSB2698", "BPSB2830", "BPSB2817", "BPSB2778", "BPSB2821", "BPSB2665", "BPSB2689", "BPSB2684", "BPSB2833", "BPSB2692", "BPSB2704", "BPSB2604", "BPSB2751", "BPSB2644", "BPSB2597", "BPSB2576", "BPSB2591", "BPSB2810", "BPSB2825", "BPSB2494", "BPSB2496", "BPSB2493", "BPSB2529", "BPSB2761", "BPSB2549", "BPSB2527", "BPSB2757", "BPSB2709", "BPSB2510", "BPSB2482", "BPSB2485", "BPSB2423", "BPSB2449");



// 10226
// get fee id & fee head id
select * from fee_configuration where institute_id = 10226 and academic_session_id = 127;
select * from fee_head_configuration where institute_id = 10226;


select 10226, student_id, "STUDENT", "cb2249d7-e049-4a68-be90-ea18b26628e7", 415, 200 from students where institute_id = 10226 and admission_number in ("JKMN797", "JKMN782", "JKMN799", "JKMN784", "JKMN816", "JKMN786", "JKMN818", "JKMN803", "JKMN820", "JKMN745", "JKMN773", "JKMN750", "JKMN790", "JKMN822", "JKMN775", "JKMN807", "JKMN752", "JKMN769", "JKMN749", "JKMN777", "JKMN754", "JKMN794", "JKMN826", "JKMN779", "JKMN811", "JKMN781", "JKMN798", "JKMN783", "JKMN815", "JKMN785", "JKMN817", "JKMN764", "JKMN787", "JKMN819", "JKMN804", "JKMN821", "JKMN746", "JKMN774", "JKMN806", "JKMN791", "JKMN823", "JKMN748", "JKMN793", "JKMN825", "JKMN770", "JKMN755", "JKMN827", "JKMN780", "JKMN812", "JKMN759", "JKMN761", "JKMN788", "JKMN805", "JKMN767", "JKMN824", "JKMN771", "JKMN756", "JKMN796", "JKMN758", "JKMN762", "JKMN802", "JKMN768", "JKMN776", "JKMN808", "JKMN753", "JKMN778", "JKMN810", "JKMN795", "JKMN757", "JKMN814", "JKMN809", "JKMN760", "JKMN766", "JAMN968", "JAMN950", "JAMN952", "JAMN983", "JAMN943", "JAMN985", "JAMN945", "JAMN955", "JAMN947", "JAMN949", "JAMN951", "JAMN982", "JAMN967", "JAMN984", "JAMN944", "JAMN986", "JAMN946", "JAMN948", "JAMN962", "JAMN966", "JAMN991", "JAMN963", "JAMN965", "JAMN975", "JAMN972", "JAMN964", "JAMN989", "JAMN969", "JAMN961", "JAMN971", "JAMN956", "JAMN976", "JAMN957", "JAMN954", "JAMN960", "JAMN973", "JAMN990", "JAMN970", "JAMN979", "JAMN959", "JAMN958");

select 10226, student_id, "STUDENT", "66d1e060-cc41-4304-b2ca-94c33e42569f", 414, 1300 from students where institute_id = 10226 and admission_number in ("JKMN797", "JKMN782", "JKMN799", "JKMN784", "JKMN816", "JKMN786", "JKMN818", "JKMN803", "JKMN820", "JKMN745", "JKMN773", "JKMN750", "JKMN790", "JKMN822", "JKMN775", "JKMN807", "JKMN752", "JKMN769", "JKMN749", "JKMN777", "JKMN754", "JKMN794", "JKMN826", "JKMN779", "JKMN811", "JKMN781", "JKMN798", "JKMN783", "JKMN815", "JKMN785", "JKMN817", "JKMN764", "JKMN787", "JKMN819", "JKMN804", "JKMN821", "JKMN746", "JKMN774", "JKMN806", "JKMN791", "JKMN823", "JKMN748", "JKMN793", "JKMN825", "JKMN770", "JKMN755", "JKMN827", "JKMN780", "JKMN812", "JKMN759", "JKMN761", "JKMN788", "JKMN805", "JKMN767", "JKMN824", "JKMN771", "JKMN756", "JKMN796", "JKMN758", "JKMN762", "JKMN802", "JKMN768", "JKMN776", "JKMN808", "JKMN753", "JKMN778", "JKMN810", "JKMN795", "JKMN757", "JKMN814", "JKMN809", "JKMN760", "JKMN766", "JAMN968", "JAMN950", "JAMN952", "JAMN983", "JAMN943", "JAMN985", "JAMN945", "JAMN955", "JAMN947", "JAMN949", "JAMN951", "JAMN982", "JAMN967", "JAMN984", "JAMN944", "JAMN986", "JAMN946", "JAMN948", "JAMN962", "JAMN966", "JAMN991", "JAMN963", "JAMN965", "JAMN975", "JAMN972", "JAMN964", "JAMN989", "JAMN969", "JAMN961", "JAMN971", "JAMN956", "JAMN976", "JAMN957", "JAMN954", "JAMN960", "JAMN973", "JAMN990", "JAMN970", "JAMN979", "JAMN959", "JAMN958");

select 10226, student_id, "STUDENT", "2a0cec83-ab8f-4c6e-8c0d-0dded5fafff0", 413, 1000 from students where institute_id = 10226 and admission_number in ("JKMN797", "JKMN782", "JKMN799", "JKMN784", "JKMN816", "JKMN786", "JKMN818", "JKMN803", "JKMN820", "JKMN745", "JKMN773", "JKMN750", "JKMN790", "JKMN822", "JKMN775", "JKMN807", "JKMN752", "JKMN769", "JKMN749", "JKMN777", "JKMN754", "JKMN794", "JKMN826", "JKMN779", "JKMN811", "JKMN781", "JKMN798", "JKMN783", "JKMN815", "JKMN785", "JKMN817", "JKMN764", "JKMN787", "JKMN819", "JKMN804", "JKMN821", "JKMN746", "JKMN774", "JKMN806", "JKMN791", "JKMN823", "JKMN748", "JKMN793", "JKMN825", "JKMN770", "JKMN755", "JKMN827", "JKMN780", "JKMN812", "JKMN759", "JKMN761", "JKMN788", "JKMN805", "JKMN767", "JKMN824", "JKMN771", "JKMN756", "JKMN796", "JKMN758", "JKMN762", "JKMN802", "JKMN768", "JKMN776", "JKMN808", "JKMN753", "JKMN778", "JKMN810", "JKMN795", "JKMN757", "JKMN814", "JKMN809", "JKMN760", "JKMN766", "JAMN968", "JAMN950", "JAMN952", "JAMN983", "JAMN943", "JAMN985", "JAMN945", "JAMN955", "JAMN947", "JAMN949", "JAMN951", "JAMN982", "JAMN967", "JAMN984", "JAMN944", "JAMN986", "JAMN946", "JAMN948", "JAMN962", "JAMN966", "JAMN991", "JAMN963", "JAMN965", "JAMN975", "JAMN972", "JAMN964", "JAMN989", "JAMN969", "JAMN961", "JAMN971", "JAMN956", "JAMN976", "JAMN957", "JAMN954", "JAMN960", "JAMN973", "JAMN990", "JAMN970", "JAMN979", "JAMN959", "JAMN958");



insert into fee_assignment (institute_id, entity_id, entity_name, fee_id, fee_head_id, amount) select 10226, student_id, "STUDENT", "2a0cec83-ab8f-4c6e-8c0d-0dded5fafff0", 413, 1000 from students where institute_id = 10226 and admission_number in ("JKMN797", "JKMN782", "JKMN799", "JKMN784", "JKMN816", "JKMN786", "JKMN818", "JKMN803", "JKMN820", "JKMN745", "JKMN773", "JKMN750", "JKMN790", "JKMN822", "JKMN775", "JKMN807", "JKMN752", "JKMN769", "JKMN749", "JKMN777", "JKMN754", "JKMN794", "JKMN826", "JKMN779", "JKMN811", "JKMN781", "JKMN798", "JKMN783", "JKMN815", "JKMN785", "JKMN817", "JKMN764", "JKMN787", "JKMN819", "JKMN804", "JKMN821", "JKMN746", "JKMN774", "JKMN806", "JKMN791", "JKMN823", "JKMN748", "JKMN793", "JKMN825", "JKMN770", "JKMN755", "JKMN827", "JKMN780", "JKMN812", "JKMN759", "JKMN761", "JKMN788", "JKMN805", "JKMN767", "JKMN824", "JKMN771", "JKMN756", "JKMN796", "JKMN758", "JKMN762", "JKMN802", "JKMN768", "JKMN776", "JKMN808", "JKMN753", "JKMN778", "JKMN810", "JKMN795", "JKMN757", "JKMN814", "JKMN809", "JKMN760", "JKMN766", "JAMN968", "JAMN950", "JAMN952", "JAMN983", "JAMN943", "JAMN985", "JAMN945", "JAMN955", "JAMN947", "JAMN949", "JAMN951", "JAMN982", "JAMN967", "JAMN984", "JAMN944", "JAMN986", "JAMN946", "JAMN948", "JAMN962", "JAMN966", "JAMN991", "JAMN963", "JAMN965", "JAMN975", "JAMN972", "JAMN964", "JAMN989", "JAMN969", "JAMN961", "JAMN971", "JAMN956", "JAMN976", "JAMN957", "JAMN954", "JAMN960", "JAMN973", "JAMN990", "JAMN970", "JAMN979", "JAMN959", "JAMN958");


// 10227
// get fee id & fee head id
select * from fee_configuration where institute_id = 10227 and academic_session_id = 128;
select * from fee_head_configuration where institute_id = 10227;


select 10227, student_id, "STUDENT", "1d94c8e5-051f-4b07-9caf-639af8242f0b", 362, 200 from students where institute_id = 10227 and admission_number in ("JAGS11808", "JAGS11595", "JAGS11618", "JAGS11589", "JAGS11580", "JAGS11611", "JAGS11755", "JAGS11789", "JAGS11791", "JAGS11612", "JAGS11599", "JAGS11616", "JAGS11578", "JAGS11593", "JAGS11598", "JAGS11690", "JAGS11761", "JAGS11731", "JAGS11579", "JAGS11842", "JAGS11701", "JAGS11575", "JAGS11724", "JAGS11771", "JAGS11760", "JAGS11807", "JAGS11809", "JAGS11841", "JAGS11698", "JAGS11686", "JAGS11796", "JAGS11763", "JAGS11733", "JAGS11703", "JAGS11704", "JAGS11847", "JAGS11804", "JAGS11678", "JAGS11722", "JAGS11848", "JAGS11839", "JAGS11613", "JAGS11584", "JAGS11770", "JAGS11819", "JAGS11587", "JAGS11695", "JAGS11734", "JAGS11802", "JAGS11710", "JAGS11718", "JAGS11585", "JAGS11794", "JAGS11744", "JAGS11825", "JAGS11814", "JAGS11769", "JAGS11754", "JAGS11739", "JAGS11743", "JAGS11805", "JAGS11730", "JAGS11762", "JAGS11811", "JAGS11766", "JAGS11813", "JAGS11736", "JAGS11785", "JAGS11706", "JAGS11628", "JAGS11873", "JAGS11586", "JAGS11630", "JAGS11746", "JAGS11581", "JAGS11816", "JAGS11745", "JAGS11836", "JAGS11759", "JAGS11853", "JAGS11765", "JAGS11669", "JAGS11801", "JAGS11833", "JAGS11629", "JAGS11803", "JAGS11726", "JAGS11857", "JAGS11822", "JAGS11861", "JAGS11702", "JAGS11687", "JAGS11834", "JAGS11725", "JAGS11778", "JAGS11573", "JAGS11784", "JAGS11614", "JAGS11709", "JAGS11758", "JAGS11775", "JAGS11620", "JAGS11576", "JAGS11574", "JAGS11717", "JAGS11596", "JAGS11768", "JAGS11615", "JAGS11817", "JAGS11774", "JAGS11588", "JAGS11590", "JAGS11823", "JAGS11782", "JAGS11741", "JAGS11773", "JAGS11631", "JAGS11790", "JAGS11792", "JAGS11607", "JAGS11700", "JAGS11594", "JAGS11783", "JAGS11708", "JAGS11878", "JAGS11795", "JAGS11716", "JAGS11810", "JAGS11812", "JAGS11788", "JAGS11715", "JAGS11779", "JAGS11696", "JAGS11781", "JAGS11821", "JAGS11838", "JAGS11798", "JAGS11800", "JAGS11671", "JAGS11756", "JAGS11818", "JAGS11837", "JAGS11824", "JAGS11799", "JAGS11849", "JAGS11617", "JAGS11661", "JAGS11735", "JAGS11637", "JAGS11850", "JAGS11856", "JAGS11851", "JAGS11752", "JAGS11719", "JAGS11862", "JAGS11767", "JAGS11843", "JAGS11787", "JAGS11772", "JAGS11764", "JAGS11753", "JAGS11714", "JAGS11829", "JAGS11737", "JAGS11583", "JAGS11751", "JAGS11738", "JAGS11577", "JAGS11748", "JAGS11864", "JAGS11866", "JAGS11835", "JAGS11820", "JAGS11859", "JAGS11727", "JAGS11604", "JAGS11699", "JAGS11750", "JAGS11597", "JAGS11877", "JAGS11683", "JAGS11640", "JAGS11601", "JAGS11728", "JAGS11747", "JAGS11865", "JAGS11797", "JAGS11600", "JAGS11602", "JAGS11713", "JAGS11684", "JAGS11732", "JAGS11876", "JAGS11694", "JAGS11891", "JAGS11711", "JAGS11777", "JAGS11649", "JAGS11885", "JAGS11892", "JAGS11863", "JAGS11826", "JAGS11828", "JAGS11830", "JAGS11858", "JAGS11623", "JAGS11705", "JAGS11673", "JAGS11622", "JAGS11646", "JAGS11663", "JAGS11647", "JAGS11668", "JAGS11882", "JAGS11650", "JAGS11852", "JAGS11662", "JAGS11721", "JAGS11676", "JAGS11603", "JAGS11890", "JAGS11881", "JAGS11893", "JAGS11894", "JAGS11884", "JAGS11883");

select 10227, student_id, "STUDENT", "b3479e8a-a41c-4072-89ea-ad7dd91ece06", 363, 1300 from students where institute_id = 10227 and admission_number in ("JAGS11808", "JAGS11595", "JAGS11618", "JAGS11589", "JAGS11580", "JAGS11611", "JAGS11755", "JAGS11789", "JAGS11791", "JAGS11612", "JAGS11599", "JAGS11616", "JAGS11578", "JAGS11593", "JAGS11598", "JAGS11690", "JAGS11761", "JAGS11731", "JAGS11579", "JAGS11842", "JAGS11701", "JAGS11575", "JAGS11724", "JAGS11771", "JAGS11760", "JAGS11807", "JAGS11809", "JAGS11841", "JAGS11698", "JAGS11686", "JAGS11796", "JAGS11763", "JAGS11733", "JAGS11703", "JAGS11704", "JAGS11847", "JAGS11804", "JAGS11678", "JAGS11722", "JAGS11848", "JAGS11839", "JAGS11613", "JAGS11584", "JAGS11770", "JAGS11819", "JAGS11587", "JAGS11695", "JAGS11734", "JAGS11802", "JAGS11710", "JAGS11718", "JAGS11585", "JAGS11794", "JAGS11744", "JAGS11825", "JAGS11814", "JAGS11769", "JAGS11754", "JAGS11739", "JAGS11743", "JAGS11805", "JAGS11730", "JAGS11762", "JAGS11811", "JAGS11766", "JAGS11813", "JAGS11736", "JAGS11785", "JAGS11706", "JAGS11628", "JAGS11873", "JAGS11586", "JAGS11630", "JAGS11746", "JAGS11581", "JAGS11816", "JAGS11745", "JAGS11836", "JAGS11759", "JAGS11853", "JAGS11765", "JAGS11669", "JAGS11801", "JAGS11833", "JAGS11629", "JAGS11803", "JAGS11726", "JAGS11857", "JAGS11822", "JAGS11861", "JAGS11702", "JAGS11687", "JAGS11834", "JAGS11725", "JAGS11778", "JAGS11573", "JAGS11784", "JAGS11614", "JAGS11709", "JAGS11758", "JAGS11775", "JAGS11620", "JAGS11576", "JAGS11574", "JAGS11717", "JAGS11596", "JAGS11768", "JAGS11615", "JAGS11817", "JAGS11774", "JAGS11588", "JAGS11590", "JAGS11823", "JAGS11782", "JAGS11741", "JAGS11773", "JAGS11631", "JAGS11790", "JAGS11792", "JAGS11607", "JAGS11700", "JAGS11594", "JAGS11783", "JAGS11708", "JAGS11878", "JAGS11795", "JAGS11716", "JAGS11810", "JAGS11812", "JAGS11788", "JAGS11715", "JAGS11779", "JAGS11696", "JAGS11781", "JAGS11821", "JAGS11838", "JAGS11798", "JAGS11800", "JAGS11671", "JAGS11756", "JAGS11818", "JAGS11837", "JAGS11824", "JAGS11799", "JAGS11849", "JAGS11617", "JAGS11661", "JAGS11735", "JAGS11637", "JAGS11850", "JAGS11856", "JAGS11851", "JAGS11752", "JAGS11719", "JAGS11862", "JAGS11767", "JAGS11843", "JAGS11787", "JAGS11772", "JAGS11764", "JAGS11753", "JAGS11714", "JAGS11829", "JAGS11737", "JAGS11583", "JAGS11751", "JAGS11738", "JAGS11577", "JAGS11748", "JAGS11864", "JAGS11866", "JAGS11835", "JAGS11820", "JAGS11859", "JAGS11727", "JAGS11604", "JAGS11699", "JAGS11750", "JAGS11597", "JAGS11877", "JAGS11683", "JAGS11640", "JAGS11601", "JAGS11728", "JAGS11747", "JAGS11865", "JAGS11797", "JAGS11600", "JAGS11602", "JAGS11713", "JAGS11684", "JAGS11732", "JAGS11876", "JAGS11694", "JAGS11891", "JAGS11711", "JAGS11777", "JAGS11649", "JAGS11885", "JAGS11892", "JAGS11863", "JAGS11826", "JAGS11828", "JAGS11830", "JAGS11858", "JAGS11623", "JAGS11705", "JAGS11673", "JAGS11622", "JAGS11646", "JAGS11663", "JAGS11647", "JAGS11668", "JAGS11882", "JAGS11650", "JAGS11852", "JAGS11662", "JAGS11721", "JAGS11676", "JAGS11603", "JAGS11890", "JAGS11881", "JAGS11893", "JAGS11894", "JAGS11884", "JAGS11883");

select 10227, student_id, "STUDENT", "7e420f08-c686-4465-a266-82910d8e292b", 364, 1000 from students where institute_id = 10227 and admission_number in ("JAGS11808", "JAGS11595", "JAGS11618", "JAGS11589", "JAGS11580", "JAGS11611", "JAGS11755", "JAGS11789", "JAGS11791", "JAGS11612", "JAGS11599", "JAGS11616", "JAGS11578", "JAGS11593", "JAGS11598", "JAGS11690", "JAGS11761", "JAGS11731", "JAGS11579", "JAGS11842", "JAGS11701", "JAGS11575", "JAGS11724", "JAGS11771", "JAGS11760", "JAGS11807", "JAGS11809", "JAGS11841", "JAGS11698", "JAGS11686", "JAGS11796", "JAGS11763", "JAGS11733", "JAGS11703", "JAGS11704", "JAGS11847", "JAGS11804", "JAGS11678", "JAGS11722", "JAGS11848", "JAGS11839", "JAGS11613", "JAGS11584", "JAGS11770", "JAGS11819", "JAGS11587", "JAGS11695", "JAGS11734", "JAGS11802", "JAGS11710", "JAGS11718", "JAGS11585", "JAGS11794", "JAGS11744", "JAGS11825", "JAGS11814", "JAGS11769", "JAGS11754", "JAGS11739", "JAGS11743", "JAGS11805", "JAGS11730", "JAGS11762", "JAGS11811", "JAGS11766", "JAGS11813", "JAGS11736", "JAGS11785", "JAGS11706", "JAGS11628", "JAGS11873", "JAGS11586", "JAGS11630", "JAGS11746", "JAGS11581", "JAGS11816", "JAGS11745", "JAGS11836", "JAGS11759", "JAGS11853", "JAGS11765", "JAGS11669", "JAGS11801", "JAGS11833", "JAGS11629", "JAGS11803", "JAGS11726", "JAGS11857", "JAGS11822", "JAGS11861", "JAGS11702", "JAGS11687", "JAGS11834", "JAGS11725", "JAGS11778", "JAGS11573", "JAGS11784", "JAGS11614", "JAGS11709", "JAGS11758", "JAGS11775", "JAGS11620", "JAGS11576", "JAGS11574", "JAGS11717", "JAGS11596", "JAGS11768", "JAGS11615", "JAGS11817", "JAGS11774", "JAGS11588", "JAGS11590", "JAGS11823", "JAGS11782", "JAGS11741", "JAGS11773", "JAGS11631", "JAGS11790", "JAGS11792", "JAGS11607", "JAGS11700", "JAGS11594", "JAGS11783", "JAGS11708", "JAGS11878", "JAGS11795", "JAGS11716", "JAGS11810", "JAGS11812", "JAGS11788", "JAGS11715", "JAGS11779", "JAGS11696", "JAGS11781", "JAGS11821", "JAGS11838", "JAGS11798", "JAGS11800", "JAGS11671", "JAGS11756", "JAGS11818", "JAGS11837", "JAGS11824", "JAGS11799", "JAGS11849", "JAGS11617", "JAGS11661", "JAGS11735", "JAGS11637", "JAGS11850", "JAGS11856", "JAGS11851", "JAGS11752", "JAGS11719", "JAGS11862", "JAGS11767", "JAGS11843", "JAGS11787", "JAGS11772", "JAGS11764", "JAGS11753", "JAGS11714", "JAGS11829", "JAGS11737", "JAGS11583", "JAGS11751", "JAGS11738", "JAGS11577", "JAGS11748", "JAGS11864", "JAGS11866", "JAGS11835", "JAGS11820", "JAGS11859", "JAGS11727", "JAGS11604", "JAGS11699", "JAGS11750", "JAGS11597", "JAGS11877", "JAGS11683", "JAGS11640", "JAGS11601", "JAGS11728", "JAGS11747", "JAGS11865", "JAGS11797", "JAGS11600", "JAGS11602", "JAGS11713", "JAGS11684", "JAGS11732", "JAGS11876", "JAGS11694", "JAGS11891", "JAGS11711", "JAGS11777", "JAGS11649", "JAGS11885", "JAGS11892", "JAGS11863", "JAGS11826", "JAGS11828", "JAGS11830", "JAGS11858", "JAGS11623", "JAGS11705", "JAGS11673", "JAGS11622", "JAGS11646", "JAGS11663", "JAGS11647", "JAGS11668", "JAGS11882", "JAGS11650", "JAGS11852", "JAGS11662", "JAGS11721", "JAGS11676", "JAGS11603", "JAGS11890", "JAGS11881", "JAGS11893", "JAGS11894", "JAGS11884", "JAGS11883");


select 10227, student_id, "STUDENT", "3a35351a-c6c3-44ce-af46-ae77b88bb177", 367, 10000 from students  where institute_id = 10227 and admission_number in ("JAGS11771", "JAGS11847", "JAGS11578", "JAGS11848", "JAGS11785", "JAGS11696", "JAGS11812", "JAGS11719", "JAGS11750", "JAGS11669", "JAGS11686", "JAGS11883", "JAGS11884", "JAGS11804", "JAGS11802", "JAGS11637", "JAGS11661", "JAGS11735", "JAGS11850", "JAGS11851", "JAGS11856", "JAGS11752", "JAGS11713", "JAGS11694", "JAGS11711", "JAGS11777", "JAGS11892", "JAGS11858", "JAGS11663", "JAGS11668", "JAGS11676", "JAGS11649", "JAGS11885", "JAGS11622", "JAGS11623", "JAGS11673", "JAGS11646", "JAGS11647", "JAGS11882", "JAGS11650", "JAGS11662", "JAGS11721", "JAGS11852", "JAGS11881", "JAGS11890", "JAGS11876", "JAGS11891", "JAGS11705", "JAGS11603")

select 10227, student_id, "STUDENT", "82a5f827-332a-4cc8-84af-38c56343f0af", 366, 500 from students  where institute_id = 10227 and admission_number in ("JAGS11771", "JAGS11847", "JAGS11578", "JAGS11848", "JAGS11785", "JAGS11696", "JAGS11812", "JAGS11719", "JAGS11750", "JAGS11669", "JAGS11686", "JAGS11883", "JAGS11884", "JAGS11804", "JAGS11802", "JAGS11637", "JAGS11661", "JAGS11735", "JAGS11850", "JAGS11851", "JAGS11856", "JAGS11752", "JAGS11713", "JAGS11694", "JAGS11711", "JAGS11777", "JAGS11892", "JAGS11858", "JAGS11663", "JAGS11668", "JAGS11676", "JAGS11649", "JAGS11885", "JAGS11622", "JAGS11623", "JAGS11673", "JAGS11646", "JAGS11647", "JAGS11882", "JAGS11650", "JAGS11662", "JAGS11721", "JAGS11852", "JAGS11881", "JAGS11890", "JAGS11876", "JAGS11891", "JAGS11705", "JAGS11603");

insert into fee_assignment (institute_id, entity_id, entity_name, fee_id, fee_head_id, amount) select 10227, student_id, "STUDENT", "82a5f827-332a-4cc8-84af-38c56343f0af", 366, 500 from students  where institute_id = 10227 and admission_number in ("JAGS11771", "JAGS11847", "JAGS11578", "JAGS11848", "JAGS11785", "JAGS11696", "JAGS11812", "JAGS11719", "JAGS11750", "JAGS11669", "JAGS11686", "JAGS11883", "JAGS11884", "JAGS11804", "JAGS11802", "JAGS11637", "JAGS11661", "JAGS11735", "JAGS11850", "JAGS11851", "JAGS11856", "JAGS11752", "JAGS11713", "JAGS11694", "JAGS11711", "JAGS11777", "JAGS11892", "JAGS11858", "JAGS11663", "JAGS11668", "JAGS11676", "JAGS11649", "JAGS11885", "JAGS11622", "JAGS11623", "JAGS11673", "JAGS11646", "JAGS11647", "JAGS11882", "JAGS11650", "JAGS11662", "JAGS11721", "JAGS11852", "JAGS11881", "JAGS11890", "JAGS11876", "JAGS11891", "JAGS11705", "JAGS11603");