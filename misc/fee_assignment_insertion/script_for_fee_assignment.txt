Take the build - mvn clean install -T 4C


copy jar file to specific path in VM -
scp -i ~/.ssh/lernen-be-2021.cer /Users/<USER>/Lernen/lernen-backend/dev-tools/target/dev-tools-1.0.1-SNAPSHOT.jar  ubuntu@************:/tmp/

login to server (prod)
ssh -i ~/.ssh/lernen-be-2021.cer ubuntu@************

check the file in server -
cd /tmp/
ls -lrt
File should be there

final script
scp -i ~/.ssh/lernen-be-2021.cer /Users/<USER>/Lernen/lernen-backend/student_data/FeeDataInsertion/FeeAssignment/10190_fee_assignment_single_data.csv  ubuntu@************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/10190_fee_assignment_single_data.csv -i 10190 -s 84 -user 3a51b85d-8458-4024-b180-97a4270ba879 -h true -u false
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/10190_fee_assignment_single_data.csv -i 10190 -s 84 -user 3a51b85d-8458-4024-b180-97a4270ba879 -h true -u true


scp -i ~/.ssh/lernen-be-2021.cer /Users/<USER>/Lernen/lernen-backend/student_data/FeeDataInsertion/FeeAssignment/10190_fee_assignment_data.csv  ubuntu@************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/10190_fee_assignment_data.csv -i 10190 -s 84 -user 3a51b85d-8458-4024-b180-97a4270ba879 -h true -u false
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/10190_fee_assignment_data.csv -i 10190 -s 84 -user 3a51b85d-8458-4024-b180-97a4270ba879 -h true -u true


scp -i ~/.ssh/lernen-be-2021.cer /Users/<USER>/Lernen/lernen-backend/student_data/FeeDataInsertion/FeeAssignment/10190_fee_assignment_prev_session_fees.csv  ubuntu@************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/10190_fee_assignment_prev_session_fees.csv -i 10190 -s 84 -user 3a51b85d-8458-4024-b180-97a4270ba879 -h true -u false
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/10190_fee_assignment_prev_session_fees.csv -i 10190 -s 84 -user 3a51b85d-8458-4024-b180-97a4270ba879 -h true -u true

Check logs in dev tools and normal server -
login to server -
ssh -i ~/.ssh/lernen-be-2021.cer ubuntu@************

devtools logs -
then go to path -
cd /var/log/lernen/devTools/
then run -
tail -100f devtools.log


--in local
aws s3 cp dev-tools/target/dev-tools-1.0.1-SNAPSHOT.jar s3://lernen-artifacts-v5/ --profile aws_prod_admin_2025
aws s3 cp /Users/<USER>/Desktop/fee_assignment_data_output.csv s3://lernen-artifacts-v5/ --profile aws_prod_admin_2025

--in server
aws s3 cp s3://lernen-artifacts-v5/dev-tools-1.0.1-SNAPSHOT.jar *********************************
aws s3 cp s3://lernen-artifacts-v5/fee_assignment_data_output.csv /tmp/fee_assignment_data_output.csv


scp -i ~/.ssh/lernen-be-2023.cer /Users/<USER>/Desktop/fee_assignment_data_output.csv  ubuntu@44.221.237.152:/tmp/

sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod-v2.c5m2qecqy0qe.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="************************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -Doauth_defaultReadOnlyClientId=read_only_embrate_client_id -Doauth_defaultReadOnlyClientSecret=FdJdqWJ3rxWVBddeB5tX -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/fee_assignment_data_output.csv -i 10226 -s 300 -user f0484d4b-9608-4d5a-a97b-6ef919022b10 -h
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod-v2.c5m2qecqy0qe.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="************************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -Doauth_defaultReadOnlyClientId=read_only_embrate_client_id -Doauth_defaultReadOnlyClientSecret=FdJdqWJ3rxWVBddeB5tX -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/fee_assignment_data_output.csv -i 10226 -s 300 -user f0484d4b-9608-4d5a-a97b-6ef919022b10 -h -u



--in local
aws s3 cp dev-tools/target/dev-tools-1.0.1-SNAPSHOT.jar s3://lernen-artifacts-v5/ --profile aws_prod_admin_2025
aws s3 cp /Users/<USER>/Desktop/fee_assignment_data_output.csv s3://lernen-artifacts-v5/ --profile aws_prod_admin_2025

--in server
aws s3 cp s3://lernen-artifacts-v5/dev-tools-1.0.1-SNAPSHOT.jar *********************************
aws s3 cp s3://lernen-artifacts-v5/fee_assignment_data_output.csv /tmp/fee_assignment_data_output.csv


scp -i ~/.ssh/lernen-be-2023.cer /Users/<USER>/Desktop/fee_assignment_data_output.csv  ubuntu@44.221.237.152:/tmp/

sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod-v2.c5m2qecqy0qe.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="************************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -Doauth_defaultReadOnlyClientId=read_only_embrate_client_id -Doauth_defaultReadOnlyClientSecret=FdJdqWJ3rxWVBddeB5tX -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/fee_assignment_data_output.csv -i 10227 -s 364 -user f0484d4b-9608-4d5a-a97b-6ef919022b10 -h
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod-v2.c5m2qecqy0qe.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="************************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -Doauth_defaultReadOnlyClientId=read_only_embrate_client_id -Doauth_defaultReadOnlyClientSecret=FdJdqWJ3rxWVBddeB5tX -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/fee_assignment_data_output.csv -i 10227 -s 364 -user f0484d4b-9608-4d5a-a97b-6ef919022b10 -h -u

