sudo java -Dlernen_env=prod -Dsecurity_mysql_username= -Dsecurity_mysql_password= -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username= -Dmysql_password= -Doauth_defaultClientId= -Doauth_defaultClientSecret= -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator10225 -i 10228 -s 143 -user f0484d4b-9608-4d5a-a97b-6ef919022b10 -f /tmp/fee_payment_10228_2223.csv -h -fidp -u




sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod.c5m2qecqy0qe.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -Doauth_defaultReadOnlyClientId=read_only_embrate_client_id -Doauth_defaultReadOnlyClientSecret=FdJdqWJ3rxWVBddeB5tX -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator10225 -f /tmp/fee_collection_data_output_adding_last_two_columns_and_formatting_date.csv -i 10225 -s 281 -user f0484d4b-9608-4d5a-a97b-6ef919022b10 -h

sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod.c5m2qecqy0qe.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -Doauth_defaultReadOnlyClientId=read_only_embrate_client_id -Doauth_defaultReadOnlyClientSecret=FdJdqWJ3rxWVBddeB5tX -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator10225 -f /tmp/fee_collection_data_output_adding_last_two_columns_and_formatting_date.csv -i 10225 -s 281 -user f0484d4b-9608-4d5a-a97b-6ef919022b10 -h -u

-i 10225 -s 281 -user f0484d4b-9608-4d5a-a97b-6ef919022b10 -h true



then go to path -
cd /var/log/lernen/devTools/
then run -
tail -100f devtools.log
