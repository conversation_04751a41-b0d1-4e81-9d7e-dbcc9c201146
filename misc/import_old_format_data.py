import sys
import json
import requests

file = open('old_format_data_sesomu.csv','r')
categories = {'stationery': 1,'art-&-craft': 6,'personal-care': 7,'notebook': 5,'clothing': 3,'footwear':  4}
brands = {'classmate' :'13527df5-96d4-4cb6-a82d-fa49f5f365d5' ,'camel' :'cb7eb2c8-b59e-4eaa-a5cb-2b62f23023d5' ,'ajay' :'401a5e06-e936-4cdf-9f2f-1eb49455f998' ,'vaseline' :'4066dba8-0a39-4503-9924-b1bdf4fa1c54' ,'nilkamal' :'c426250b-0312-4887-b6c4-15192c92253f' ,'cherry' :'2aced808-f37b-484c-9fa7-86ff689f9aa8' ,'shree' :'843436b7-1515-410f-b6b2-ee1c3d6bc830' ,'patanjli' :'6ac4d84b-5cbe-4b56-8936-b6d4261f7a62' ,'unknown' :'a0c56a89-b39a-49cb-9943-3a16ee8a2d64' ,'cello' :'40f56c38-8146-4ddb-810a-3607e786e974' ,'rin' :'1d96e56c-9fae-441e-967c-4d8f1f2cb7ab' ,'syria' :'72085f50-fab7-43fb-8714-516b603c59f1' ,'dettol' :'bf0fa687-ebe5-40c4-bc36-7e3516861b75' ,'dabur' :'a06a7cdf-fb5d-4a15-9698-e14f8ce31ff3' ,'fevicol' :'3a447e3f-31f5-45ba-ad22-95f33e24d29e'}
colors = {'black' : 2,'blue' : 7,'brown' : 3,'cyan' :15,'darkgreen' :10,'green' : 9,'grey' :13,'magenta' :16,'maroon' : 1,'navy blue' : 8,'olive' :14,'pink' :11,'purple' :12,'red' : 5,'white' : 4,'yellow' : 6}

url = "http://127.0.0.1:8080/data-server/2.0/inventory/product"
# url = "http://**************:8080/data-server/2.0/inventory/product"

product_groups = {}
skus = {}
count = 0

def load_file():
	global product_groups,skus,count

	for line in file:
		attrs = line.split(",")
		group_id = attrs[0]
		group_name = attrs[5]
		category = categories[attrs[6].strip()]
		brand = brands[attrs[7].strip()]
		size = attrs[8]
		color = attrs[9]
		if( color != "NULL" ):
			color = colors[attrs[9]]
		user_group = attrs[10].upper()
		gender = attrs[11].upper()
		total_quantity = attrs[12]
		initial_quantity = attrs[13]
		selling_price = attrs[14]
		discount = attrs[15]
		in_use = attrs[16]
		description = attrs[17]

		if(group_id == "4283a1fc-ed05-4fb3-8a0c-2259ea1f5d46"):
			continue
		count += 1

		if( group_id not in product_groups):
			product_groups[group_id] = {'name' : group_name, 'category' : category ,'brand':brand,'size' : set(),'color' : set(),'user_group':set(),'gender':set(),'description':description}

		products = product_groups[group_id]
		# print(products)
		if(size != "NULL"):
			products['size'].add(size)
		if(color != "NULL"):
			products['color'].add(color)
		if(user_group != "NULL"):
			products['user_group'].add(user_group)
		if(gender != "NULL"):
			products['gender'].add(gender)

		sku = str(category)+"|"+group_name+"|"+brand+"|"+user_group+"|"+gender+"|"+size+"|"+str(color)
		if(sku in skus):
			print(sku)
		skus[sku] = {'initialQuantity':initial_quantity, 'sellingPrice' : selling_price, 'discount' : discount}









print(count)

def create_new_product(data):
	headers={"Content-Type": "application/json"}
	payload = {'instituteId' : 10001, 'productVariationsGroup' : {'name': data['name'], 'categoryId' : data['category'], 'brandId' : data['brand'], 'sizes' : list(data['size']), 'colors' : list(data['color']) ,'userGroups' : list(data['user_group']),'genders' : list(data['gender']) ,'description' : data['description']}}
	response = requests.post(url, data=json.dumps(payload), headers=headers)
	if response.status_code != 200:
		print("failure")
		return 0
	return 1

def update_product(data):
	headers={"Content-Type": "application/json"}
	payload = {'instituteId' : 10001, 'generalName': data['generalName'], 'skuId': data['skuId'], 'sellingPrice' : data['sellingPrice'], 'discount' : data['discount'], 'initialQuantity' : data['initialQuantity'], 'initialQuantityAsOf' : data['initialQuantityAsOf']}
	print(json.dumps(payload))
	response = requests.post(url+"/update", data=json.dumps(payload), headers=headers)
	if response.status_code != 200:
		print(response.text)
		return 0
	return 1


def run():
	load_file()
	errors = 0
	success = 0
	for key in product_groups:
		print(key)
		status = create_new_product(product_groups[key])
		if(status == 0):
			errors+=1
		else:
			success+=1

	print(errors)
	print(success)
	get_products()


def populate_data():
	print(len(skus))
	for key in skus:
		print(key)
		print(skus[key])

def get_products():
	headers={"Content-Type": "application/json"}
	response = requests.get(url+"/10001",  headers=headers)
	if response.status_code != 200:
		print("failure")
		return 0

	data = json.loads(response.text)

	c = 0
	for product in data:
		userGroup = product['userGroup']
		if userGroup is None:
			 userGroup = "NULL"

		gender = product['gender']
		if gender is None:
			 gender = "NULL"

		size = product['size']
		if size is None:
			 size = "NULL"

		color = product['color']
		if color is None:
			 color = "NULL"
		else:
			color = str(color['colorId'])

		new_sku = str(product['category']['categoryId'])+"|"+product['generalName']+"|"+product['brand']['brandId']+"|"+userGroup+"|"+gender+"|"+size+"|"+color
		if new_sku in skus:
			sku_data = skus[new_sku]
			sellingPrice = sku_data['sellingPrice']
			if(sellingPrice == "NULL"):
				sellingPrice = None

			initialQuantity = sku_data['initialQuantity']
			initialQuantityAsOf = 1522521000
			if(initialQuantity == "0"):
				initialQuantity = None
				initialQuantityAsOf = None
			status = update_product({'generalName': product['generalName'],'skuId':product['skuId'], 'sellingPrice' : sellingPrice, 'discount' : sku_data['discount'], 'initialQuantity' : initialQuantity, 'initialQuantityAsOf' : initialQuantityAsOf})
			if(status == 1):		
				c+=1
	print(c)



run()



