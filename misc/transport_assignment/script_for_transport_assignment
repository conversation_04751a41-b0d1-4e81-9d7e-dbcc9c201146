Take the build - mvn clean install -T 4C


copy jar file to specific path in VM -
scp -i ~/.ssh/lernen-be-2021.cer /Users/<USER>/Lernen/lernen-backend/dev-tools/target/dev-tools-1.0.1-SNAPSHOT.jar  ubuntu@**************:/tmp/

login to server (prod)
ssh -i ~/.ssh/lernen-be-2021.cer ubuntu@**************

check the file in server -
cd /tmp/
ls -lrt
File should be there



final script
scp -i ~/.ssh/lernen-be-2021.cer /Users/<USER>/Lernen/lernen-backend/student_data/TransportAssignment/10190_single_transport_assignment.csv  ubuntu@**************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreatorV2 -i 10190 -s 84 -f /tmp/10190_single_transport_assignment.csv -h -u 3a51b85d-8458-4024-b180-97a4270ba879
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreatorV2 -i 10190 -s 84 -f /tmp/10190_single_transport_assignment.csv -h -u 3a51b85d-8458-4024-b180-97a4270ba879 -update true

scp -i ~/.ssh/lernen-be-2021.cer /Users/<USER>/Lernen/lernen-backend/student_data/TransportAssignment/10190_transport_assignment.csv  ubuntu@**************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreatorV2 -i 10190 -s 84 -f /tmp/10190_transport_assignment.csv -h -u 3a51b85d-8458-4024-b180-97a4270ba879
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreatorV2 -i 10190 -s 84 -f /tmp/10190_transport_assignment.csv -h -u 3a51b85d-8458-4024-b180-97a4270ba879 -update true



Check logs in dev tools and normal server -
login to server -
ssh -i ~/.ssh/lernen-be-2021.cer ubuntu@**************

devtools logs -
then go to path -
cd /var/log/lernen/devTools/
then run -
tail -100f devtools.log
