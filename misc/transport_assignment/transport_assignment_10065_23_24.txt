Take the build - mvn clean install -T 4C


copy jar file to specific path in VM -
scp -i ~/.ssh/lernen-be-2021.cer /Users/<USER>/Lernen/lernen-backend/dev-tools/target/dev-tools-1.0.1-SNAPSHOT.jar  ubuntu@***********:/tmp/

login to server (prod)
ssh -i ~/.ssh/lernen-be-2021.cer ubuntu@***********

check the file in server -
cd /tmp/
ls -lrt
File should be there



final script
scp -i ~/.ssh/lernen-be-2021.cer /Users/<USER>/Lernen/lernen-backend/student_data/TransportAssignment/10065_23_24_single_transport_assignment_data.csv  ubuntu@***********:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreatorV2 -i 10065 -s 163 -f /tmp/10065_23_24_single_transport_assignment_data.csv -h -u 47e21660-ce8e-477c-a8e3-effdb3c6c464
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreatorV2 -i 10065 -s 163 -f /tmp/10065_23_24_single_transport_assignment_data.csv -h -u 47e21660-ce8e-477c-a8e3-effdb3c6c464 -update

scp -i ~/.ssh/lernen-be-2021.cer /Users/<USER>/Lernen/lernen-backend/student_data/TransportAssignment/10065_23_24_transport_assignment_data.csv  ubuntu@***********:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreatorV2 -i 10065 -s 163 -f /tmp/10065_23_24_transport_assignment_data.csv -h -u 47e21660-ce8e-477c-a8e3-effdb3c6c464
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreatorV2 -i 10065 -s 163 -f /tmp/10065_23_24_transport_assignment_data.csv -h -u 47e21660-ce8e-477c-a8e3-effdb3c6c464 -update



Check logs in dev tools and normal server -
login to server -
ssh -i ~/.ssh/lernen-be-2021.cer ubuntu@***********

devtools logs -
then go to path -
cd /var/log/lernen/devTools/
then run -
tail -100f devtools.log
