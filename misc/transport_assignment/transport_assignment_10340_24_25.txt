sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod.cjvqf7murjkc.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreatorV2 -i 10395 -s 265 -f /tmp/10395_transport_assignment_data.csv -h -u 616727cb-5a8a-4464-acfe-c7b7435d7cbf
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod.cjvqf7murjkc.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreatorV2 -i 10395 -s 265 -f /tmp/10395_transport_assignment_data.csv -h -u 616727cb-5a8a-4464-acfe-c7b7435d7cbf -update true



Take the build - mvn clean install -T 4C


copy jar file to specific path in VM -
scp -i ~/.ssh/lernen-be-2023.cer /Users/<USER>/Lernen/lernen-backend/dev-tools/target/dev-tools-1.0.1-SNAPSHOT.jar  ubuntu@*************:/tmp/

login to server (prod)
ssh -i ~/.ssh/lernen-be-2023.cer ubuntu@*************

check the file in server -
cd /tmp/
ls -lrt
File should be there



final script
scp -i ~/.ssh/lernen-be-2023.cer /Users/<USER>/Lernen/lernen-backend/student_data/TransportAssignment/10340_single_transport_assignment.csv  ubuntu@*************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod.cjvqf7murjkc.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreatorV2 -i 10340 -s 230 -f /tmp/10340_single_transport_assignment.csv -h -u 89535643-c277-4e3f-b086-564cfad9e6e4
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod.cjvqf7murjkc.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreatorV2 -i 10340 -s 230 -f /tmp/10340_single_transport_assignment.csv -h -u 89535643-c277-4e3f-b086-564cfad9e6e4 -update true

scp -i ~/.ssh/lernen-be-2023.cer /Users/<USER>/Lernen/lernen-backend/student_data/TransportAssignment/10340_transport_assignment.csv  ubuntu@*************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod.cjvqf7murjkc.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreatorV2 -i 10340 -s 230 -f /tmp/10340_transport_assignment.csv -h -u 89535643-c277-4e3f-b086-564cfad9e6e4
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod.cjvqf7murjkc.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.transport.assignment.TransportAssignmentCreatorV2 -i 10340 -s 230 -f /tmp/10340_transport_assignment.csv -h -u 89535643-c277-4e3f-b086-564cfad9e6e4 -update true



Check logs in dev tools and normal server -
login to server -
ssh -i ~/.ssh/lernen-be-2023.cer ubuntu@*************

devtools logs -
then go to path -
cd /var/log/lernen/devTools/
then run -
tail -100f devtools.log
