select * from fee_payment_transaction_amounts join where fee_payment_transaction_amounts.transaction_id in (select transaction_id from temp_issue_trans)


create view temp_i1 as select distinct a.transaction_id  from (select institute_id, t.transaction_id, fee_id, fee_head_id from temp_issue_trans t join fee_payment_transaction_amounts fta on t.transaction_id = fta.transaction_id) a join temp_trans_head h on a.institute_id = h.institute_id and a.fee_head_id = h.fee_head_id;


select transaction_id, count(distinct fee_head_id) as c from fee_payment_transaction_amounts where transaction_id in (select transaction_id from temp_i1)  group by 1 order by c;



create view temp_i2 as SELECT
cancelled.institute_id,
cancelled.academic_session_id,
cancelled.transaction_id AS cancelled_transaction_id,
cancelled.updated_at AS cancelled_updated_at,
active.transaction_id AS active_transaction_id,
active.transaction_added_at AS active_added_at
        FROM
fee_payment_transactions cancelled
JOIN
fee_payment_transactions active
ON cancelled.institute_id = active.institute_id
AND cancelled.student_id = active.student_id
AND cancelled.academic_session_id = active.academic_session_id
AND cancelled.transaction_id <> active.transaction_id
AND cancelled.updated_at BETWEEN DATE_SUB(active.transaction_added_at, INTERVAL 5 SECOND)
AND DATE_ADD(active.transaction_added_at, INTERVAL 5 SECOND)
WHERE
cancelled.status = 'CANCELLED'
AND active.status = 'ACTIVE'
and cancelled.transaction_id in (select transaction_id from temp_i1);


select admission_number from students where student_id in (select student_id from fee_payment_transactions where institute_id = 10205 and transaction_id in (select cancelled_transaction_id from temp_i2));

| institute_id | academic_session_id | cancelled_transaction_id             | cancelled_updated_at | active_transaction_id                | active_added_at     |
|        10205 |                 203 | 1530965e-86ee-4060-8541-3a544e2b9736 | 2024-11-16 05:35:05  | 4f52e71e-434e-4cd3-aeb7-9201db3e4383 | 2024-11-16 05:35:06 |
|        10205 |                 299 | 1ec43a59-8a7f-4743-9a3c-36d836ab13fa | 2025-04-15 06:55:38  | b3454048-5cb4-454e-8f3c-152ab8c0374a | 2025-04-15 06:55:38 |
|        10205 |                 203 | 3e956a74-89a9-4104-bc57-6fa8a6b92f2f | 2025-04-28 06:53:33  | 528dfdf0-aafc-4a72-b162-c9c2db0bc1d8 | 2025-04-28 06:53:34 |
|        10205 |                 203 | 3e956a74-89a9-4104-bc57-6fa8a6b92f2f | 2025-04-28 06:53:33  | 5735a135-3742-4d18-93ec-205ccca5582b | 2025-04-28 06:53:34 |
|        10205 |                 299 | 58b12551-b8c2-4bef-8d10-86c78d31b6d2 | 2025-04-15 06:56:27  | a44b2ed9-4c4d-4eb0-a93a-5ec105bef6da | 2025-04-15 06:56:28 |
|        10205 |                 203 | f32762ef-b197-47b2-98cd-8b3b504ba09e | 2025-04-28 06:53:33  | 528dfdf0-aafc-4a72-b162-c9c2db0bc1d8 | 2025-04-28 06:53:34 |
|        10205 |                 203 | f32762ef-b197-47b2-98cd-8b3b504ba09e | 2025-04-28 06:53:33  | 5735a135-3742-4d18-93ec-205ccca5582b | 2025-04-28 06:53:34 |
