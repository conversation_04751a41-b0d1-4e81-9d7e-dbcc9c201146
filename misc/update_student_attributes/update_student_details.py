import sys
import requests
import json

file_name = "/Users/<USER>/Desktop/reg_number.csv"
institute_id = "institute_id"
academic_session_id = "academic_session_id"
standard_id = "standard_id"
user_id = "user_id"
access_token = "access_token"
from_script = True

def get_student(admission_number):
	r = requests.get("http://127.0.0.1:8080/data-server/2.0/student/search?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&admission_number="+admission_number+"&access_token="+access_token)
	response = r.json()
	if('studentId' in response):
		return response
	else:
		return None

def create_payload(student, field_data):
	student_id = student['studentId']
	return {"studentId" : student_id, "registrationNumber" : field_data}


def update_student_board_num(all_student_list):

#     payload = {"instituteId" : institute_id, "academicSessionId" : academic_session_id,"standard_id" : standard_id, "studentAcademisSessionParametersList" : ["ROLL_NUMBER"], "studentAcademicDetailsList" : all_student_list}
#     print(payload)
#
#     r = requests.post("https://api.embrate.com/2.0/student/update-session-details?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+user_id+"&from_script="+str(from_script)+"&access_token=" + access_token, data=json.dumps(payload), headers={"Content-Type": "application/json"})
#     print(r)

	payload = {"instituteId" : institute_id, "academicSessionId" : academic_session_id, "studentParametersSet" : ["REGISTRATION_NUMBER"], "bulkStudentInfoList" : all_student_list}
    # payload = {"instituteId" : institute_id, "academicSessionId" : academic_session_id, "studentParametersSet" : ["PEN"], "bulkStudentInfoList" : all_student_list}
	print(payload)
	r = requests.post("http://127.0.0.1:8080/data-server/2.0/student/update-bulk-student-details?institute_id="+str(institute_id)+"&user_id="+user_id+"&from_script="+str(from_script)+"&access_token=" + access_token, data=json.dumps(payload), headers={"Content-Type": "application/json"})
	print(r)


def read_file():
	file = open(file_name, 'r')
	all_student_list= []
	for line in file:
		print(line)
		tokens = line.strip().split(",")
		admission_number = tokens[0].strip()
		field_data = tokens[1].strip()
		student = get_student(admission_number)
		if(student is None):
			print("Invalid user " + str(admission_number))
		else:
			print("found student for " + str(admission_number))
			student_payload = create_payload(student, field_data)
			all_student_list.append(student_payload)

	return all_student_list


all_student_list = read_file()
for user in all_student_list:
	print(user)

update_student_board_num(all_student_list)
