select student_fee_payments.institute_id, student_fee_payments.student_id , student_fee_payments.fee_id, student_fee_payments.fee_head_id , student_fee_payments.assigned_amount, student_fee_payments.paid_amount, student_fee_payments.instant_discount_amount, student_fee_payments.fine_amount, fee_discount_structure_metadata.structure_name, fee_discount_structure.structure_id, fee_discount_structure.is_percent, fee_discount_structure.amount, if(fee_discount_structure.is_percent, student_fee_payments.assigned_amount * fee_discount_structure.amount/100, fee_discount_structure.amount) as final_discount from student_fee_payments join student_fee_discount_assignment on student_fee_payments.student_id  = student_fee_discount_assignment.student_id join fee_discount_structure_metadata on fee_discount_structure_metadata.structure_id = student_fee_discount_assignment.structure_id join fee_discount_structure on fee_discount_structure_metadata.structure_id = fee_discount_structure.structure_id and student_fee_payments.fee_id = fee_discount_structure.fee_id and student_fee_payments.fee_head_id = fee_discount_structure.fee_head_id;



select b.*,  aa - (pa + ida + ad) as diff from (select institute_id, student_id, fee_id, fee_head_id, avg(assigned_amount) aa, avg(paid_amount) pa, avg(instant_discount_amount) ida, avg(fine_amount) fa, sum(final_discount) ad from (select student_fee_payments.institute_id, student_fee_payments.student_id , student_fee_payments.fee_id, student_fee_payments.fee_head_id , student_fee_payments.assigned_amount, student_fee_payments.paid_amount, student_fee_payments.instant_discount_amount, student_fee_payments.fine_amount, fee_discount_structure_metadata.structure_name, fee_discount_structure.structure_id, fee_discount_structure.is_percent, fee_discount_structure.amount, if(fee_discount_structure.is_percent, student_fee_payments.assigned_amount * fee_discount_structure.amount/100, fee_discount_structure.amount) as final_discount from student_fee_payments join student_fee_discount_assignment on student_fee_payments.student_id  = student_fee_discount_assignment.student_id join fee_discount_structure_metadata on fee_discount_structure_metadata.structure_id = student_fee_discount_assignment.structure_id join fee_discount_structure on fee_discount_structure_metadata.structure_id = fee_discount_structure.structure_id and student_fee_payments.fee_id = fee_discount_structure.fee_id and student_fee_payments.fee_head_id = fee_discount_structure.fee_head_id) a group by 1,2,3,4) b where aa - (pa + ida + ad) < 0;


create view nd1 as select institute_id, student_id, fee_id, fee_head_id, avg(assigned_amount) aa, avg(paid_amount) pa, avg(instant_discount_amount) ida, avg(fine_amount) fa, sum(final_discount) ad from (select student_fee_payments.institute_id, student_fee_payments.student_id , student_fee_payments.fee_id, student_fee_payments.fee_head_id , student_fee_payments.assigned_amount, student_fee_payments.paid_amount, student_fee_payments.instant_discount_amount, student_fee_payments.fine_amount, fee_discount_structure_metadata.structure_name, fee_discount_structure.structure_id, fee_discount_structure.is_percent, fee_discount_structure.amount, if(fee_discount_structure.is_percent, student_fee_payments.assigned_amount * fee_discount_structure.amount/100, fee_discount_structure.amount) as final_discount from student_fee_payments join student_fee_discount_assignment on student_fee_payments.student_id  = student_fee_discount_assignment.student_id join fee_discount_structure_metadata on fee_discount_structure_metadata.structure_id = student_fee_discount_assignment.structure_id join fee_discount_structure on fee_discount_structure_metadata.structure_id = fee_discount_structure.structure_id and student_fee_payments.fee_id = fee_discount_structure.fee_id and student_fee_payments.fee_head_id = fee_discount_structure.fee_head_id) a group by 1,2,3,4;




select b.*,  aa - (pa + ida + ad) as diff from (select institute_id, student_id, fee_id, fee_head_id, avg(assigned_amount) aa, avg(paid_amount) pa, avg(instant_discount_amount) ida, avg(fine_amount) fa, sum(final_discount) ad from (select student_fee_payments.institute_id, student_fee_payments.student_id , student_fee_payments.fee_id, student_fee_payments.fee_head_id , student_fee_payments.assigned_amount, student_fee_payments.paid_amount, student_fee_payments.instant_discount_amount, student_fee_payments.fine_amount, student_fee_payment_discounts.discount_group_id, student_fee_payment_discounts.discount_id, student_fee_payment_discounts.discount_amount as final_discount from student_fee_payments join student_fee_payment_discounts on student_fee_payments.discount_group_id  = student_fee_payment_discounts.discount_group_id) a group by 1,2,3,4) b where aa - (pa + ida + ad) < 0;

create view od1 as select institute_id, student_id, fee_id, fee_head_id, avg(assigned_amount) aa, avg(paid_amount) pa, avg(instant_discount_amount) ida, avg(fine_amount) fa, sum(final_discount) ad from (select student_fee_payments.institute_id, student_fee_payments.student_id , student_fee_payments.fee_id, student_fee_payments.fee_head_id , student_fee_payments.assigned_amount, student_fee_payments.paid_amount, student_fee_payments.instant_discount_amount, student_fee_payments.fine_amount, student_fee_payment_discounts.discount_group_id, student_fee_payment_discounts.discount_id, student_fee_payment_discounts.discount_amount as final_discount from student_fee_payments join student_fee_payment_discounts on student_fee_payments.discount_group_id  = student_fee_payment_discounts.discount_group_id) a group by 1,2,3,4;


 select nd1.institute_id, nd1.student_id, nd1.fee_id, nd1.fee_head_id from nd1 join od1 on nd1.institute_id = od1.institute_id and nd1.student_id = od1.student_id and nd1.fee_id = od1.fee_id and nd1.fee_head_id = od1.fee_head_id and nd1.aa = od1.aa and nd1.pa = od1.pa and nd1.ida = od1.ida and nd1.fa = od1.fa and nd1.ad = od1.ad ;


  select nd1.institute_id, nd1.student_id, nd1.fee_id, nd1.fee_head_id from nd1  join od1 on nd1.institute_id = od1.institute_id and nd1.student_id = od1.student_id and nd1.fee_id = od1.fee_id and nd1.fee_head_id = od1.fee_head_id;


nd1 - 9668
od1 - 9114

  // Discount deleted

 select nd1.institute_id, nd1.student_id, nd1.fee_id, nd1.fee_head_id, od1.institute_id, od1.student_id, od1.fee_id, od1.fee_head_id from nd1 right join od1 on nd1.institute_id = od1.institute_id and nd1.student_id = od1.student_id and nd1.fee_id = od1.fee_id and nd1.fee_head_id = od1.fee_head_id where nd1.student_id is null;

  select nd1.institute_id, nd1.student_id, nd1.fee_id, nd1.fee_head_id, od1.institute_id, od1.student_id, od1.fee_id, od1.fee_head_id, od1.aa, od1.pa, od1.ida, od1.ad, od1.aa - (od1.pa + od1.ida + od1.ad) as odue from nd1 right join od1 on nd1.institute_id = od1.institute_id and nd1.student_id = od1.student_id and nd1.fee_id = od1.fee_id and nd1.fee_head_id = od1.fee_head_id where nd1.student_id is null;

27
10/08/2023 - 33

  // Discount amount changed

  select nd1.institute_id, nd1.student_id, nd1.fee_id, nd1.fee_head_id, nd1.aa, nd1.pa, nd1.ida, nd1.ad, od1.aa, od1.pa, od1.ida, od1.ad,  nd1.aa - (nd1.pa + nd1.ida + nd1.ad) as ndue,  od1.aa - (od1.pa + od1.ida + od1.ad) as odue from nd1  join od1 on nd1.institute_id = od1.institute_id and nd1.student_id = od1.student_id and nd1.fee_id = od1.fee_id and nd1.fee_head_id = od1.fee_head_id where nd1.aa - (nd1.pa + nd1.ida + nd1.ad) != od1.aa - (od1.pa + od1.ida + od1.ad);

32
10/08/2023 - 33
  // Discount addition

   select nd1.institute_id, nd1.student_id, nd1.fee_id, nd1.fee_head_id,  nd1.aa, nd1.pa, nd1.ida, nd1.ad, nd1.aa - (nd1.pa + nd1.ida + nd1.ad), od1.institute_id, od1.student_id, od1.fee_id, od1.fee_head_id from nd1 left join od1 on nd1.institute_id = od1.institute_id and nd1.student_id = od1.student_id and nd1.fee_id = od1.fee_id and nd1.fee_head_id = od1.fee_head_id where od1.student_id is null and nd1.aa - (nd1.pa + nd1.ida + nd1.ad) < 0;

   581
   10/08/2023 - 679

select institute_id, academic_session_id from academic_session where academic_session_id in (select academic_session_id from fee_configuration where fee_id in (select distinct a3 from (select nd1.institute_id as a1, nd1.student_id as a2, nd1.fee_id as a3, nd1.fee_head_id as a4,  nd1.aa, nd1.pa, nd1.ida, nd1.ad, nd1.aa - (nd1.pa + nd1.ida + nd1.ad), od1.institute_id, od1.student_id, od1.fee_id, od1.fee_head_id from nd1 left join od1 on nd1.institute_id = od1.institute_id and nd1.student_id = od1.student_id and nd1.fee_id = od1.fee_id and nd1.fee_head_id = od1.fee_head_id where od1.student_id is null and nd1.aa - (nd1.pa + nd1.ida + nd1.ad) < 0) as t));
