package com.embrate.cloud.pdf;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.LambdaLogger;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.embrate.cloud.core.lib.filesystem.S3FileSystem;
import com.embrate.cloud.pdf.admission.form.AdmissionFormHandler;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;
import com.lernen.cloud.pdf.identitycard.student.StudentIdentityCardHandler;
import org.springframework.context.ApplicationContext;

import java.io.ByteArrayOutputStream;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class AWSLambdaTestHandler implements RequestHandler<Map<String, String>, String> {

    Gson gson = new GsonBuilder().setPrettyPrinting().create();

    @Override
    public String handleRequest(Map<String, String> event, Context context) {
        long start = System.currentTimeMillis();
        LambdaLogger logger = context.getLogger();
        String response = new String("200 OK Yup Working!!!");
        // log execution details
        logger.log("ENVIRONMENT VARIABLES: " + gson.toJson(System.getenv()));
        logger.log("CONTEXT: " + gson.toJson(context));
        // process event
        logger.log("EVENT: " + gson.toJson(event));
        logger.log("EVENT TYPE: " + event.getClass().toString());

//        try{
//            logger.log("-------------------- Starting Bean Loading ------------");
//            ApplicationContext applicationContext = SpringAppContextProvider.getApplicationContext("pdf.xml");
//            logger.log("-------------------- Bean Loading Done ------------");
//            AdmissionFormHandler admissionFormHandler = applicationContext.getBean(AdmissionFormHandler.class);
//            logger.log("-------------------- Got AdmissionFormHandler Bean ------------");
//            final DocumentOutput documentOutput = admissionFormHandler.generateAdmissionForm(110, UUID.fromString("51d8a38b-8a3b-4711-b3c4-06ac3dcfbb29"));
//            logger.log("-------------------- Done getting document --------");
//            if(documentOutput == null){
//                logger.log("-------------------- Got null Document Output --------");
//            }else{
//                logger.log("-------------------- Document Output --------"  + documentOutput.getName());
//            }
//        }catch (Exception e){
//            logger.log("-------------------- Exception during Document Output --------" + e.getMessage() + " " + e.toString());
//        }
        String s3Path = null;
        try{
            long diff = System.currentTimeMillis() - start;
            logger.log("-------------------- Starting Bean Loading after ------------ : " + diff + " \n");
            start =  System.currentTimeMillis();
            ApplicationContext applicationContext = SpringAppContextProvider.getApplicationContext("pdf.xml");
            logger.log("-------------------- Bean Loading Done ------------ \n");
            StudentIdentityCardHandler studentIdentityCardHandler = applicationContext.getBean(StudentIdentityCardHandler.class);
            S3FileSystem s3FileSystem = applicationContext.getBean(S3FileSystem.class);
            diff = System.currentTimeMillis() - start;
            logger.log("-------------------- Got AdmissionFormHandler Bean ------------ \n after : " + diff);
            start =  System.currentTimeMillis();
            final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCards(10171, 78,  UUID.fromString("153b7ad9-1052-4580-a52b-f315e693eda1"), null, UUID.fromString("81e57091-81df-416c-9aea-10e7f0b4f3d0"));
            diff = System.currentTimeMillis() - start;
            logger.log("-------------------- Done getting document -------- \n " + diff);
            start =  System.currentTimeMillis();
            if(documentOutput == null){
                logger.log("-------------------- Got null Document Output -------- \n");
            }else{
                s3Path = "s3://lernen-artifacts-v5/test_output"+documentOutput.getName();
                s3FileSystem.writeFile("lernen-artifacts-v5", "test_output/"+documentOutput.getName(), new FileData(documentOutput.getContent().toByteArray(), documentOutput.getName()));
                diff = System.currentTimeMillis() - start;
                logger.log("-------------------- Document Output --------"  + documentOutput.getName() + " after : "  + diff + " \n");
            }
        }catch (Exception e){
            logger.log("-------------------- Exception during Document Output --------" + e.getMessage() + " " + e.toString() + " \n");
        }


        return s3Path;
    }
}