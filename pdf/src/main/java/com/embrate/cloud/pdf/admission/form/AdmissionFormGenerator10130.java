package com.embrate.cloud.pdf.admission.form;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.properties.AreaBreakType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.lib.student.StudentManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class AdmissionFormGenerator10130 extends AdmissionFormGenerator {

    public AdmissionFormGenerator10130(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    private static final Logger logger = LogManager.getLogger(AdmissionFormGenerator10130.class);

    @Override
    public DocumentOutput generateAdmissionForm(StudentManager studentManager, Institute institute, AcademicSession academicSession, StudentTransportDetails studentTransportDetails, boolean publishStudentData, Student student,
                                                String documentName) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData admissionFormLayoutData = generateAdmissionFormLayoutData(institute, documentOutput, 70f,
                    70f);

            generateStudentAdmissionForm(admissionFormLayoutData, studentManager, institute, academicSession, studentTransportDetails, publishStudentData, student, 1);

            admissionFormLayoutData.getDocument().close();

            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating admission form for institute {}, student id {}",
                    institute.getInstituteId(), student.getStudentId(), e);
        }
        return null;
    }

    private void generateStudentAdmissionForm(DocumentLayoutData admissionFormLayoutData, StudentManager studentManager, Institute institute, AcademicSession academicSession, StudentTransportDetails studentTransportDetails, boolean publishStudentData, Student student, int pageNumber) throws IOException {

        Document document = admissionFormLayoutData.getDocument();
        int index = 1;
        float squareBorderMargin = 8f;
        float borderInnerGap = 2f;


        addBlankLine(document, false, 1);
        generatePageHeader(admissionFormLayoutData, institute,
                student.getStudentAcademicSessionInfoResponse().getAcademicSession(), 20f,
                admissionFormLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.90f,
                15.5f);
        generateMetadata(admissionFormLayoutData, squareBorderMargin, borderInnerGap, pageNumber);

        addBlankLine(document, true, 1);

        index = generateBasicInformationPage(admissionFormLayoutData, studentManager, studentTransportDetails, institute, student, true,
                index);

        addBlankLine(document, true, 1);

        index = generateFamilyInformationPage(admissionFormLayoutData, institute, student, index, false);

        addBlankLine(document, true, 1);

        index = generatePrevSchoolInformationPage(admissionFormLayoutData, institute, student, index);

        addBlankLine(document, true, 1);

        index = generateMedicalInformationPage(admissionFormLayoutData, institute, student, index);

        generateSignatureBox(admissionFormLayoutData, squareBorderMargin, borderInnerGap);

    }

    @Override
    public DocumentOutput generateBulkAdmissionForm(StudentManager studentManager, Institute institute, AcademicSession academicSession, Map<UUID, StudentTransportDetails> studentTransportDetailsMap, boolean publishStudentData, List<Student> studentList, String documentName) {

        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData admissionFormLayoutData = generateAdmissionFormLayoutData(institute, documentOutput, 70f,
                    70f);
            int pageNumber = 1;
            for (Student student : studentList) {
                generateStudentAdmissionForm(admissionFormLayoutData, studentManager, institute, academicSession,
                        studentTransportDetailsMap == null || CollectionUtils.isEmpty(studentTransportDetailsMap.entrySet()) ? null :
                                studentTransportDetailsMap.get(student.getStudentId()), publishStudentData, student, pageNumber);

                if (pageNumber != studentList.size()) {
                    admissionFormLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;
            }

            admissionFormLayoutData.getDocument().close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating admission form for institute {}, academic session id {}",
                    institute.getInstituteId(), academicSession.getAcademicSessionId(), e);
        }
        return null;
    }
}
