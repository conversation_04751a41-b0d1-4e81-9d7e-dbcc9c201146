package com.embrate.cloud.pdf.admission.form;

import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.institute.AcademicSession;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.layout.Document;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.lib.student.StudentManager;

/**
 * 
 * <AUTHOR>
 *
 */
public class GlobalAdmissionFormGenerator extends AdmissionFormGenerator {

	public GlobalAdmissionFormGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(GlobalAdmissionFormGenerator.class);

	@Override
	public DocumentOutput generateAdmissionForm(StudentManager studentManager, Institute institute, AcademicSession academicSession, StudentTransportDetails studentTransportDetails,boolean publishStudentData, Student student,
												String documentName) {
		try {
			float squareBorderMargin = 8f;
			float borderInnerGap = 2f;
			
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			DocumentLayoutData admissionFormLayoutData = generateAdmissionFormLayoutData(institute, documentOutput, 70f,
					70f);
			Document document = admissionFormLayoutData.getDocument();
			int index = 1;
			
			
			addBlankLine(document, false, 1);
			generatePageHeader(admissionFormLayoutData, institute,
					student.getStudentAcademicSessionInfoResponse().getAcademicSession());
			generateMetadata(admissionFormLayoutData, squareBorderMargin, borderInnerGap);
			
			addBlankLine(document, true, 1);

			index = generateBasicInformationPage(admissionFormLayoutData, studentManager, studentTransportDetails ,institute, student, true,
					index);

			addBlankLine(document, true, 1);

			index = generateFamilyInformationPage(admissionFormLayoutData, institute, student, index, false);

			addBlankLine(document, true, 1);

			index = generatePrevSchoolInformationPage(admissionFormLayoutData, institute, student, index);

			addBlankLine(document, true, 1);

			index = generateMedicalInformationPage(admissionFormLayoutData, institute, student, index);

			generateSignatureBox(admissionFormLayoutData, squareBorderMargin, borderInnerGap);

			document.close();

			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating admission form for institute {}, student id {}",
					institute.getInstituteId(), student.getStudentId(), e);
		}
		return null;
	}

	@Override
	public DocumentOutput generateBulkAdmissionForm(StudentManager studentManager, Institute institute, AcademicSession academicSession, Map<UUID, StudentTransportDetails> studentTransportDetailsMap, boolean publishStudentData, List<Student> studentList, String documentName) {
		return null;
	}

}
