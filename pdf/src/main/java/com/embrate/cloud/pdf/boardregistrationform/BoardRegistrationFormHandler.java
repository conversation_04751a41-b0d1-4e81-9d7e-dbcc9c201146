package com.embrate.cloud.pdf.boardregistrationform;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.UUID;

public class BoardRegistrationFormHandler {

    private static final Logger logger = LogManager.getLogger(BoardRegistrationFormHandler.class);

	private final BoardRegistrationFormGeneratorFactory registrationFormGeneratorFactory;
	private final InstituteManager instituteManager;
	private final StudentManager studentManager;

	public BoardRegistrationFormHandler(InstituteManager instituteManager, StudentManager studentManager, AssetProvider assetProvider) {

		this.registrationFormGeneratorFactory = new BoardRegistrationFormGeneratorFactory(assetProvider);
		this.instituteManager = instituteManager;
		this.studentManager = studentManager;
	}

	public DocumentOutput generateBoardRegistrationForm(int instituteId, int academicSessionId , UUID studentId) {

		if (instituteId <= 0 || academicSessionId <= 0 || studentId == null) {
			logger.error("Invalid institute id or academicSession id or student id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid institute id or student id"));
		}
      
		// List<Student> studentInAllSessions = studentManager.descendingSortedStudentDetailsInAllSession(instituteId, studentId);

		// if (CollectionUtils.isEmpty(studentInAllSessions)) {
		// 	logger.error("No student found in institute {} with student id {}", instituteId, studentId);
		// 	return null;
		// }

		
		final Student student = studentManager.getStudentByAcademicSessionStudentId(instituteId, academicSessionId, studentId);
		logger.info("Generating  Registration form for instituteId {}, student id {} ", instituteId, studentId);
		
		BoardRegistrationFormGenerator registrationFormGenerator = registrationFormGeneratorFactory
				.getRegistrationFormGenerator(instituteId);
				String fileName = student.getStudentBasicInfo().getName() + "_BoardRegistrationForm.pdf";
	
		return registrationFormGenerator.generateBoardRegistrationForm(studentManager, instituteManager.getInstitute(instituteId),
				student.getStudentAcademicSessionInfoResponse().getAcademicSession() , student, fileName);
	}
}






