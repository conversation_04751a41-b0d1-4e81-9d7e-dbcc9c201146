package com.embrate.cloud.pdf.boardregistrationform;

import java.io.ByteArrayOutputStream;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.layout.Document;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;

public class GlobalBoardRegistrationFormGenerator extends BoardRegistrationFormGenerator {

    public GlobalBoardRegistrationFormGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(GlobalBoardRegistrationFormGenerator.class);

	@Override
	public DocumentOutput generateBoardRegistrationForm(StudentManager studentManager, Institute institute, AcademicSession academicSession, Student student,
												String documentName) {
		try {
			float squareBorderMargin = 8f;
			float borderInnerGap = 3f;
			
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			DocumentLayoutData registrationFormLayoutData = generateBoardRegistrationFormLayoutData(institute, documentOutput, 70f, 70f);
			Document document = registrationFormLayoutData.getDocument();
			int index = 1;
				
			addBlankLine(document, false, 1);

			generatePageHeader(registrationFormLayoutData, institute, student,
					student.getStudentAcademicSessionInfoResponse().getAcademicSession());

			generateMetadata(registrationFormLayoutData, squareBorderMargin, borderInnerGap);

			index = generateBasicInformationPage(registrationFormLayoutData, studentManager, institute, student, true,
					index, registrationFormLayoutData.getBoldFont(),registrationFormLayoutData.getRegularFont());

			addBlankLine(document, true, 1);

			generateSignatureBox(registrationFormLayoutData, squareBorderMargin, borderInnerGap);

			addBlankLine(document, true, 1);

			addNotes(registrationFormLayoutData, registrationFormLayoutData.getRegularFont(), registrationFormLayoutData.getBoldFont());

			document.close();

			return documentOutput;

		} catch (Exception e) {

			logger.error("Error while generating admission form for institute {}, student id {}",
					institute.getInstituteId(), student.getStudentId(), e);

		}

		return null;

	}

}
