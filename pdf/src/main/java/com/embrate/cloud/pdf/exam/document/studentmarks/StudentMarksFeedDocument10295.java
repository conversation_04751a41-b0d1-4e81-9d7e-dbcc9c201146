package com.embrate.cloud.pdf.exam.document.studentmarks;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.google.common.collect.Lists;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.*;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.base.PDFGenerator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.Map.Entry;

public class StudentMarksFeedDocument10295 extends StudentMarksFeedDocument {
    
    public StudentMarksFeedDocument10295(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    private static final Logger logger = LogManager.getLogger(StudentMarksFeedDocument10295.class);
    public static final float DEFAULT_LOGO_WIDTH = 40f;
    public static final float DEFAULT_LOGO_HEIGHT = 40f;

    public DocumentOutput generateStudentMarksFeedDocument(Institute institute, Standard standard, Integer sectionId,
                                                           String documentName, ExamMetaData examMetaData, List<StudentExamMarksDetails> studentExamMarksDetailsList,
                                                           int singleColumnStudentCount, Set<UUID> userAccessCourses, Map<CourseType, List<ExamGrade>> examGradeMap) {
        int instituteId = institute.getInstituteId();
        try {
            DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(true);

            documentLayoutSetup.setNoDoubleContentBorder(true);
            float contentFontSize = 8f;
            float headerFontSize = 8f;
            float defaultBorderWidth = 0.1f;
            Document document = initDocument(invoice.getContent(), documentLayoutSetup, false);
            DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
                    defaultBorderWidth, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), null, null);

            float offsetX = documentLayoutSetup.getSideMargin()
                    + (documentLayoutSetup.isOfficeCopy() ? DEFAULT_LOGO_DOUBLE_CONTENT_SIDE_MARGIN
                    : DEFAULT_LOGO_SINGLE_CONTENT_SIDE_MARGIN);
            float offsetY = documentLayoutSetup.getPageSize().getHeight() - DEFAULT_LOGO_HEIGHT
                    - documentLayoutSetup.getTopMargin() + 5f;

            LinkedHashMap<UUID, ExamCourseMarks> examCourseMarksMap = new LinkedHashMap<>();
            //StudentId, CourseId, DimensionId, ExamDimensionObtainedValues
            Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> studentExamCourseMarksMap = new HashMap<>();
            for (StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList) {
                if(!studentExamCourseMarksMap.containsKey(studentExamMarksDetails.getStudent().getStudentId())) {
                    studentExamCourseMarksMap.put(studentExamMarksDetails.getStudent().getStudentId(), new HashMap<>());
                }
                Map<UUID, Map<Integer, ExamDimensionObtainedValues>> courseDimensionObtainedValuesMap = studentExamCourseMarksMap.get(studentExamMarksDetails.getStudent().getStudentId());
                for (ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesAllDimensionsMarks()) {
                    if(!courseDimensionObtainedValuesMap.containsKey(examCourseMarks.getCourse().getCourseId())) {
                        courseDimensionObtainedValuesMap.put(examCourseMarks.getCourse().getCourseId(), new HashMap<>());
                    }
                    if (!examCourseMarksMap.containsKey(examCourseMarks.getCourse().getCourseId())) {
                        examCourseMarksMap.put(examCourseMarks.getCourse().getCourseId(), examCourseMarks);
                    }
                    Map<Integer, ExamDimensionObtainedValues> dimensionObtainedValuesMap = courseDimensionObtainedValuesMap.get(examCourseMarks.getCourse().getCourseId());
                    for(ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks.getExamDimensionObtainedValues()) {
                        if(!dimensionObtainedValuesMap.containsKey(examDimensionObtainedValues.getExamDimension().getDimensionId())) {
                            dimensionObtainedValuesMap.put(examDimensionObtainedValues.getExamDimension().getDimensionId(), examDimensionObtainedValues);
                        }
                    }
                }

            }

            int count = 0;

            List<List<StudentExamMarksDetails>> studentRowList = Lists.partition(studentExamMarksDetailsList,
                    singleColumnStudentCount);
            int lastPageIndex = examCourseMarksMap.keySet().size() * studentRowList.size();
            for (Entry<UUID, ExamCourseMarks> examCourseMarksMapEntry : examCourseMarksMap.entrySet()) {
                ExamCourseMarks examCourseMarks = examCourseMarksMapEntry.getValue();
                /**
                 * filtering courses of which user don't have access to generate report
                 */
                UUID courseId = examCourseMarks.getCourse().getCourseId();
                if(!CollectionUtils.isEmpty(userAccessCourses) && !userAccessCourses.contains(courseId)) {
                    continue;
                }
                int studentIndex = 1;
                for (List<StudentExamMarksDetails> pageStudents : studentRowList) {
                    generateDynamicImageProvider(documentLayoutData, offsetX, offsetY, instituteId, InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

                    generateHeader(document, documentLayoutSetup, standard, sectionId, institute, headerFontSize,
                            defaultBorderWidth, examMetaData, examCourseMarks);
                    generateStudentInformation(document, documentLayoutSetup, contentFontSize, defaultBorderWidth,
                            pageStudents, singleColumnStudentCount, studentIndex, examCourseMarks, studentExamCourseMarksMap);
                    studentIndex += singleColumnStudentCount;
                    generateSignatureBox(document, documentLayoutSetup, contentFontSize, count + 1);
                    if (count != lastPageIndex - 1) {
                        document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                    }
                    count++;
                }
            }

            document.close();
            return invoice;
        } catch (Exception e) {
            logger.error("Error while crating exam marks feed sheet for institute {}, standard {} ",
                    institute.getInstituteId(), standard.getStandardId(), e);
        }
        return null;
    }

    public Table getPDFTable(DocumentLayoutSetup documentLayoutSetup, int singleCopyColumnCount) {
        return getPDFTable(documentLayoutSetup, documentLayoutSetup.getPageSize().getWidth(), singleCopyColumnCount);
    }

    public Table getPDFTable(DocumentLayoutSetup documentLayoutSetup, float[] singleCopyColumnWidthPercents) {
        return getPDFTable(documentLayoutSetup, documentLayoutSetup.getPageSize().getWidth(),
                singleCopyColumnWidthPercents);
    }

    public void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, Standard standard,
                               Integer sectionId, Institute institute, float headerFontSize, float defaultBorderWidth,
                               ExamMetaData examMetaData, ExamCourseMarks examCourseMarks) throws IOException {
        Double totalMarks = null;
        for (ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
                .getExamDimensionObtainedValues()) {
            if (examDimensionObtainedValues.getExamDimension().isTotal()) {
                totalMarks = examDimensionObtainedValues.getMaxMarks();
                break;
            }
        }
        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(getRegularBoldFont()).setTextAlignment(TextAlignment.CENTER);

        addRow(table, documentLayoutSetup, Collections.singletonList(getParagraph(institute.getInstituteName())),
                cellLayoutSetup.setFontSize(headerFontSize));
        addRow(table, documentLayoutSetup, Collections.singletonList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);
        addRow(table, documentLayoutSetup, Collections.singletonList(getParagraph("Exam Result Marks List")), cellLayoutSetup);

        document.add(table);

        // Student copy section
        table = getPDFTable(documentLayoutSetup, new float[] {0.4f, 0.6f});

        Paragraph exam = getKeyValueParagraph("Exam : ", examMetaData.getExamName());
        String standardDisplayName = standard.getDisplayName();
        if(sectionId != null && !CollectionUtils.isEmpty(standard.getStandardSectionList())) {
            for(StandardSections standardSections : standard.getStandardSectionList()) {
                if(standardSections.getSectionId() == sectionId) {
                    standardDisplayName += "-" + standardSections.getSectionName();
                    break;
                }
            }
        }

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(getKeyValueParagraph("Subject : ", examCourseMarks.getCourse().getCourseName()),
                        cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
                new CellData(exam.setMultipliedLeading(0.95f), cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));

        addRow(table, documentLayoutSetup, Arrays.asList(

                new CellData(getKeyValueParagraph("Class : ", standardDisplayName),
                        cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
                new CellData(
                        getKeyValueParagraph("Total Marks : ",
                                totalMarks == null ? EMPTY_TEXT : String.valueOf(Math.round(totalMarks))),
                        cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));

        document.add(table);

    }

    public void generateStudentInformation(Document document, DocumentLayoutSetup documentLayoutSetup,
                                           float contentFontSize, float defaultBorderWidth, List<StudentExamMarksDetails> studentExamMarksDetails,
                                           int singleColumnStudentCount, int studentIndex, ExamCourseMarks examCourseMarks,
                                           Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> studentExamCourseMarksMap) throws IOException {

        int dimensionSize = 0;

        for (ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
                .getExamDimensionObtainedValues()) {
            if(examDimensionObtainedValues.getMaxMarks() == null) {
                continue;
            }
            dimensionSize++;
        }

        float[] singleContentColumnWidths = new float[3 + dimensionSize];
        singleContentColumnWidths[0] = 0.11f;
        singleContentColumnWidths[1] = 0.13f;
        singleContentColumnWidths[2] = 0.33f;
        for (int i = 0; i < dimensionSize; i++) {
            singleContentColumnWidths[i + 3] = 0.43f / dimensionSize;
        }

        Table table = getPDFTable(documentLayoutSetup, singleContentColumnWidths);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize);

        CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)
                .setBorder(new SolidBorder(defaultBorderWidth)).setPaddingTop(3.5f).setPaddingBottom(3.5f);

        List<CellData> headerCellDataList = new ArrayList<CellData>();
        headerCellDataList.add(new CellData(getParagraph("Roll No.").setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setFontSize(contentFontSize - 1), 2, 1));
        headerCellDataList.add(new CellData(getParagraph("Ad. No.").setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setFontSize(contentFontSize - 1), 2, 1));
        headerCellDataList.add(new CellData(getParagraph("Student Name").setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setFontSize(contentFontSize - 1), 2, 1));

        List<CellData> rightHeaderCellDataList = new ArrayList<CellData>(headerCellDataList);

        List<CellData> leftDimensionMarks = new ArrayList<>();

        for (ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
                .getExamDimensionObtainedValues()) {

            if(examDimensionObtainedValues.getMaxMarks() == null) {
                continue;
            }

            headerCellDataList.add(new CellData(getParagraph(examDimensionObtainedValues.getExamDimension().getDimensionName()).setMultipliedLeading(0.95f),
                    firstCellLayoutSetup));
            rightHeaderCellDataList.add(new CellData(getParagraph(examDimensionObtainedValues.getExamDimension().getDimensionName()).setMultipliedLeading(0.95f),
                    firstCellLayoutSetup));

            leftDimensionMarks.add(
                    new CellData(getParagraph(examDimensionObtainedValues.getMaxMarks() == null ? EMPTY_TEXT :
                            String.valueOf(Math.round(examDimensionObtainedValues.getMaxMarks()))).setMultipliedLeading(0.95f),
                            firstCellLayoutSetup));
        }
        List<CellData> rightDimensionMarks = new ArrayList<>(leftDimensionMarks);

        addRow(table, documentLayoutSetup, headerCellDataList, rightHeaderCellDataList);
        addRow(table, documentLayoutSetup, leftDimensionMarks, rightDimensionMarks);

        for (int i = 0; i < singleColumnStudentCount; i++) {
            Student leftStudent = null;
            if (i < studentExamMarksDetails.size()) {
                leftStudent = studentExamMarksDetails.get(i).getStudent();
            }
            List<CellData> leftStudentRow = new ArrayList<CellData>();

            Map<Integer, ExamDimensionObtainedValues> examDimensionObtainedValuesMap = new HashMap<>();
            if (leftStudent != null) {
                examDimensionObtainedValuesMap = studentExamCourseMarksMap.get(leftStudent.getStudentId()).get(examCourseMarks.getCourse().getCourseId());
                String rollNumber = leftStudent.getStudentAcademicSessionInfoResponse() == null ?
                        EMPTY_TEXT : leftStudent.getStudentAcademicSessionInfoResponse().getRollNumber() == null ?
                        EMPTY_TEXT : leftStudent.getStudentAcademicSessionInfoResponse().getRollNumber();
                leftStudentRow.add(new CellData(getParagraph(rollNumber).setMultipliedLeading(0.95f), firstCellLayoutSetup));
                leftStudentRow.add(
                        new CellData(getParagraph(leftStudent.getStudentBasicInfo().getAdmissionNumber()).setMultipliedLeading(0.95f), firstCellLayoutSetup));
                leftStudentRow.add(new CellData(getParagraph(leftStudent.getStudentBasicInfo().getName()).setMultipliedLeading(0.95f), firstCellLayoutSetup));
            } else {
                leftStudentRow.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setBorder(null)));
                leftStudentRow.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setBorder(null)));
                leftStudentRow.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setBorder(null)));
                leftStudentRow.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setBorder(null)));
            }

            for (ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
                    .getExamDimensionObtainedValues()) {
                if(examDimensionObtainedValues.getMaxMarks() == null) {
                    continue;
                }

                ExamDimensionObtainedValues studentExamDimensionObtainedValues = examDimensionObtainedValuesMap.get(examDimensionObtainedValues.getExamDimension().getDimensionId());

                String obtainedMarksValue = EMPTY_TEXT;
                if(studentExamDimensionObtainedValues != null) {
                    if (studentExamDimensionObtainedValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER) {
                        obtainedMarksValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(
                                studentExamDimensionObtainedValues.getObtainedMarks() == null ? null : NumberUtils.getRoundValueString(studentExamDimensionObtainedValues.getObtainedMarks()),
                                studentExamDimensionObtainedValues.getAttendanceStatus(), studentExamDimensionObtainedValues.getMinMarks(), false,
                                EMPTY_TEXT, studentExamDimensionObtainedValues.getMaxMarks(), EMPTY_TEXT, true);
                    } else if (studentExamDimensionObtainedValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.GRADE) {
                        obtainedMarksValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(studentExamDimensionObtainedValues.getObtainedGrade(),
                                studentExamDimensionObtainedValues.getAttendanceStatus(), studentExamDimensionObtainedValues.getMinMarks(), false,
                                EMPTY_TEXT, studentExamDimensionObtainedValues.getMaxMarks(), EMPTY_TEXT, true);
                    }
                }

                if (leftStudent != null) {
                    leftStudentRow.add(new CellData(getParagraph(obtainedMarksValue).setMultipliedLeading(0.95f), firstCellLayoutSetup));
                } else {
                    leftStudentRow.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setBorder(null)));
                }
            }
            addRow(table, documentLayoutSetup, leftStudentRow, leftStudentRow);
        }
        document.add(table);
    }

    protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
                                        float contentFontSize, int pageNumber) throws IOException {
        int singleContentColumn = 3;

        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getKeyValueParagraph("Present Student : ", EMPTY_TEXT),
                        getKeyValueParagraph("Passed Student : ", EMPTY_TEXT),
                        getKeyValueParagraph("Failed Student : ", EMPTY_TEXT)),
                signatureCellLayoutSetup);
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
                signatureCellLayoutSetup);
        addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("Exam Result : ", EMPTY_TEXT),
                        getKeyValueParagraph("Date : ", EMPTY_TEXT), getKeyValueParagraph("Signature Examiner: ", EMPTY_TEXT)),
                signatureCellLayoutSetup);

        float contentWidth = documentLayoutSetup.getPageSize().getWidth() - 2 * documentLayoutSetup.getSideMargin();
        table.setFixedPosition(pageNumber, documentLayoutSetup.getSideMargin(), 8, contentWidth);
        document.add(table);
    }

}