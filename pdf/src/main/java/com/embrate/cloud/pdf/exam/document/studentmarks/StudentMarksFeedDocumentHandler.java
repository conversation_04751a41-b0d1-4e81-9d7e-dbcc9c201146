package com.embrate.cloud.pdf.exam.document.studentmarks;

import com.embrate.cloud.core.lib.timetable.TimetableManager;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamDetails;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.StudentExamMarksDetails;
import com.lernen.cloud.core.api.examination.StudentMarksDetails;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.student.StudentSortingParameters;
import com.lernen.cloud.core.lib.examination.ExaminationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.student.StudentSorter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentMarksFeedDocumentHandler {

	private static final Logger logger = LogManager.getLogger(StudentMarksFeedDocumentHandler.class);

	private final StudentMarksFeedDocumentFactory studentMarksFeedDocumentFactory;
	private final ExaminationManager examinationManager;
	private final InstituteManager instituteManager;
	private final UserPermissionManager userPermissionManager;
	private final TimetableManager timetableManager;

	public StudentMarksFeedDocumentHandler(ExaminationManager examinationManager, InstituteManager instituteManager, AssetProvider assetProvider,
										   UserPermissionManager userPermissionManager, TimetableManager timetableManager) {
		this.studentMarksFeedDocumentFactory = new StudentMarksFeedDocumentFactory(assetProvider);
		this.examinationManager = examinationManager;
		this.instituteManager = instituteManager;
		this.userPermissionManager = userPermissionManager;
		this.timetableManager = timetableManager;
	}

	public DocumentOutput generateStudentMarksFeedDocument(int instituteId, int academicSessionId, UUID examId, UUID courseId,
														   UUID standardId, Integer sectionId, UUID userId, int singleColumnStudentCount, boolean addRelievedStudents,
														   StudentSortingParameters studentSortingParameters) {
		if (instituteId <= 0 || examId == null) {
			logger.error("Invalid institute id or exam id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid institute id or exam id"));
		}

		ExamDetails examDetails = examinationManager.getExamDetails(examId, instituteId);
		if (examDetails == null) {
			logger.error("Invalid examDetails for exam id {}, instituteId {} ", examId, instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Invalid exam"));
		}
		/**
		 * passing userId as null, as we are not applying filtering for reports currently.
		 */
		final List<StudentExamMarksDetails> studentExamMarksDetailsList = examinationManager
				.getResolvedClassMarks(instituteId, academicSessionId, examId, courseId, standardId, sectionId, addRelievedStudents, null);

		if (CollectionUtils.isEmpty(studentExamMarksDetailsList)) {
			logger.error("Student exam marks details not present for exam id {}, courseId {}, instituteId {} ", examId,
					courseId, instituteId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Student exam marks details not present"));
		}


		System.out.println(studentExamMarksDetailsList.size());
		studentSortingParameters = studentSortingParameters == null ? StudentSortingParameters.STUDENT_NAME : studentSortingParameters;
		StudentSorter.sortStudents(
				studentExamMarksDetailsList,
				studentSortingParameters,
				new StudentSorter.StudentKeyExtractor<StudentExamMarksDetails>() {
					@Override
					public String getAdmissionNumber(StudentExamMarksDetails studentExamMarksDetails) {
						return studentExamMarksDetails.getStudent().getStudentBasicInfo().getAdmissionNumber();
					}

					@Override
					public String getName(StudentExamMarksDetails studentExamMarksDetails) {
						return studentExamMarksDetails.getStudent().getStudentBasicInfo().getName();
					}

					@Override
					public String getRollNumber(StudentExamMarksDetails studentExamMarksDetails) {
						return studentExamMarksDetails.getStudent().getStudentAcademicSessionInfoResponse().getRollNumber();
					}

					@Override
					public String getSectionName(StudentExamMarksDetails studentExamMarksDetails) {
						List<StandardSections> sections = studentExamMarksDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList();
						return (sections != null && !sections.isEmpty()) ? sections.get(0).getSectionName() : null;
					}
				}
		);

		Map<CourseType, List<ExamGrade>> examGradeMap = examinationManager.getExamGrades(instituteId, academicSessionId, standardId);

		Set<UUID> userAccessCourses = new HashSet<>();
		if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ACCESS_ALL_CLASSES_EXAMINATION_MARKS_DATA, false)) {
			String standardSectionIdStr = standardId + (sectionId == null ? "" : ":" + sectionId);
			Map<String, Set<UUID>> staffStandardSectionStrCourseDetailsMap = timetableManager.getStaffStandardSectionIdEntityDetails(
					instituteId, academicSessionId, userId);
			userAccessCourses = staffStandardSectionStrCourseDetailsMap.get(standardSectionIdStr);
			if(CollectionUtils.isEmpty(userAccessCourses)) {
				logger.error("Staff don't have access to download report for this particular standard id {}, exam_id {}, instituteId {} ", standardId, examId, instituteId);
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
						"Staff don't have access to download report for this particular class."));
			}
		}

		String fileName = examDetails.getStandard().getStandardName() + " "
				+ examDetails.getExamMetaData().getExamName() + " StudentMarks.pdf";
		singleColumnStudentCount = singleColumnStudentCount == 0 ?  40 : singleColumnStudentCount;

		StudentMarksFeedDocument studentMarksFeedDocument = studentMarksFeedDocumentFactory
				.getStudentMarksFeedDocumentGenerator(instituteId);

		return studentMarksFeedDocument.generateStudentMarksFeedDocument(instituteManager.getInstitute(instituteId),
				examDetails.getStandard(), sectionId, fileName, examDetails.getExamMetaData(), studentExamMarksDetailsList,
				singleColumnStudentCount, userAccessCourses, examGradeMap);
	}

}
