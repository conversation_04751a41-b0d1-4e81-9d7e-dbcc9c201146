package com.embrate.cloud.pdf.exam.reports._10225;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportCourseMarksRow;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridHeaderConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridTotalMarksRowConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportGeneratorLandscape10228 extends ExamReportGenerator implements IExamReportCardGenerator {

	public ExamReportGeneratorLandscape10228(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;
	public static final float LOGO_WIDTH = 65f;
	public static final float LOGO_HEIGHT = 65f;

	protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";
	protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";
	private static final String GRADING_SCHEME = "Grading Scheme : 91 - 100 (A1), 81 - 90 (A2), 71 - 80 (B1), 61 - 70 (B2), 51 - 60 (C1), 41 - 50 (C2), 33 - 40 (D), 32 & Below (E(Needs Improvement))";
	private static final String SCHOLASTIC_EXAM_DESCRIPTION = "PA : Periodic Assessment, SA : Summative Assessment, SEA : Subject Enrichment Activity, FA : Formative Assessment, NB : Notebook";
	private static final Logger logger = LogManager.getLogger(ExamReportGeneratorLandscape10228.class);

	private static final Map<String, String> MARKS_GRADE_MAP = new LinkedHashMap<>();
	static {
		MARKS_GRADE_MAP.put("91 - 100", "A1");
		MARKS_GRADE_MAP.put("81 - 90", "A2");
		MARKS_GRADE_MAP.put("71 - 80", "B1");
		MARKS_GRADE_MAP.put("61 - 70", "B2");
		MARKS_GRADE_MAP.put("51 - 60", "C1");
		MARKS_GRADE_MAP.put("41 - 50", "C2");
		MARKS_GRADE_MAP.put("33 - 40", "D");
		MARKS_GRADE_MAP.put("32 & Below", "E");
	}

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
			ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			Document document = examReportCardLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
			generateStudentReport(document, documentLayoutSetup, institute,
					examReportData.getStudentLite(), reportType, examReportData, examReportCardLayoutData, 1, studentManager);
			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}
	private void generateBackgroundImage(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
										 float height, float offsetX, float offsetY) {
		try {
			generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
		} catch (Exception e) {
			logger.error("Exception while adding background image", e);
		}

	}

	private void generateStudentReport(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute, StudentLite studentLite, String reportType,
			ExamReportData examReportData, ExamReportCardLayoutData examReportCardLayoutData, int pageNumber, StudentManager studentManager)
			throws IOException {

		float bgImageHeightWidth = 400f;

        generateWatermark(examReportCardLayoutData, institute.getInstituteId(), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 + 20, bgImageHeightWidth / 2);
		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.86f, studentManager);
		addBlankLine(examReportCardLayoutData, false, 1);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData);
		generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType,
				new GridConfigs(new GridHeaderConfigs(false), new GridTotalMarksRowConfigs(true, false, true, true, false)),
				"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType));
		generateScholasticExamDescription(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examReportData, examReportCardLayoutData.getRegularFont(),
				examReportCardLayoutData.getBoldFont(), SCHOLASTIC_EXAM_DESCRIPTION);
		generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 0.5f,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "Co-Scholastic Subjects",
				getCoScholasticMarksGridSubjectWidth(reportType));
		generateScholasticExamDescription(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examReportData, examReportCardLayoutData.getRegularFont(),
				examReportCardLayoutData.getBoldFont(), GRADING_SCHEME);
		generateResultSummary(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examReportData, reportType);
		generateRemarksSection(examReportCardLayoutData, examReportData);
		generateSignatureBox(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examReportCardLayoutData.getDefaultBorderWidth(), pageNumber);
		generateBorderLayout(examReportCardLayoutData, pageNumber);
	}

	protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
			ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont, String descriptionText) throws IOException {
		Text descText = new Text(descriptionText).setFontSize(contentFontSize - 2).setFont(regularFont);
		Paragraph desc = new Paragraph();
		desc.add(descText);
		document.add(desc);
	}
	private void generateCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
			ExamReportData examReportData, String reportType, GridConfigs gridConfigs, String subjectColumnTitle,
			float subjectColumnWidth) throws IOException {
		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 0.5f,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, nonAdditionalSubjects, null);

	}

	protected float getScholasticMarksGridSubjectWidth(String reportType) {
		return 0.15f;
	}

	protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.5f;
		}
		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			return 0.4f;
		}
		return 0.3f;
	}

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4.rotate());
		float contentFontSize = 10f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

		ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup,
				getRegularFont(), getRegularBoldFont(), contentFontSize, defaultBorderWidth, LOGO_WIDTH, LOGO_HEIGHT,
				LogoProvider.INSTANCE.getLogo(institute.getInstituteId()));
		examReportCardLayoutData.setBuildingImage(ImageProvider.INSTANCE.getImage(ImageProvider._10030_BUILDING));
		return examReportCardLayoutData;
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
								  Institute institute, String reportType, float offsetX, float offsetY, StudentManager studentManager) throws IOException {

		generateDynamicImageProvider(examReportCardLayoutData,  offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		byte[] studentImage = getStudentImage(institute.getInstituteId(), studentLite.getStudentId(), studentManager);
		if (studentImage != null) {
			generateImage(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
					studentImage, LOGO_WIDTH, LOGO_HEIGHT, examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - offsetX - LOGO_WIDTH,
					offsetY);
		}

		int singleContentColumn = 1;
		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName())
						.setFontColor(Color.convertRgbToCmyk(new DeviceRgb(EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB))).setMultipliedLeading(1f)),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 8));
		String letterHead1 = institute.getLetterHeadLine1();
		String letterHead2 = institute.getLetterHeadLine2();
		String letterHead = letterHead1;
		if(StringUtils.isBlank(letterHead)) {
			letterHead = letterHead2;
		} else {
			letterHead += ", " + letterHead2;
		}

		int textColorR = EColorUtils._10228IntR;
		int textColorG = EColorUtils._10228IntG;
		int textColorB = EColorUtils._10228IntB;
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(letterHead)
						.setFontColor(Color.convertRgbToCmyk(new DeviceRgb(textColorR, textColorG, textColorB)))),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 1));

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		String headerExamTitle = "PROGRESS REPORT ";
		headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 1).setPdfFont(getRegularBoldFont()));
		examReportCardLayoutData.getDocument().add(table);

	}

	protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
											  StudentLite studentLite, ExamReportData examReportData) throws IOException {

		PdfFont boldFont = getRegularBoldFont();
		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize() - 0.5f;

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.24f, 0.01f, 0.24f, 0.01f, 0.24f, 0.01f, 0.24f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup fourthCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		int textColorR = EColorUtils._10228IntR;
		int textColorG = EColorUtils._10228IntG;
		int textColorB = EColorUtils._10228IntB;

		Paragraph studentName = getKeyValueParagraph("Student's Name : ", studentLite.getName(), textColorR, textColorG, textColorB, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph fatherName = getKeyValueParagraph("Father's Name : ", studentLite.getFathersName(), textColorR, textColorG, textColorB, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph dob = getKeyValueParagraph("Birth Date : ",
				studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
						: DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE), textColorR, textColorG, textColorB, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph motherName = getKeyValueParagraph("Mother's Name : ", studentLite.getMothersName(), textColorR, textColorG, textColorB, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph admissionNumber = getKeyValueParagraph("Reg No. : ", studentLite.getAdmissionNumber(), textColorR, textColorG, textColorB, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph classValue = getKeyValueParagraph("Class : ",
				studentLite.getStudentSessionData().getStandardNameWithSection(), textColorR, textColorG, textColorB, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph rollNumber = getKeyValueParagraph("Roll No. : ", studentLite.getStudentSessionData().getRollNumber(), textColorR, textColorG, textColorB, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph dateOfResultDeclaration = getKeyValueParagraph("House : ", examReportData.getStudentLite().getInstituteHouse() == null ? ""
						: examReportData.getStudentLite().getInstituteHouse().getHouseName(),
				textColorR, textColorG, textColorB, boldFont, boldFont).setMultipliedLeading(1f);


		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(admissionNumber, secondCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(classValue, thirdCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(rollNumber, fourthCellLayoutSetup)));

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(fatherName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(motherName, secondCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(dob, thirdCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(dateOfResultDeclaration, fourthCellLayoutSetup)));

		document.add(table);

	}

	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
				documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
	}

	protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
										 float contentFontSize, ExamReportData examReportData, String reportType) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.33f, 0.01f, 0.32f, 0.01f, 0.33f });

		PdfFont boldFont = getRegularBoldFont();
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup fourthCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

		Paragraph noOfMeetings = getKeyValueParagraph("Number of Meetings : ", examReportData.getTotalWorkingDays() == null ? ""
				: String.valueOf(examReportData.getTotalWorkingDays()), 1, 155, 248, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph noOfPresent = getKeyValueParagraph("Number of presents : ", examReportData.getTotalAttendedDays() == null ? ""
				: String.valueOf(examReportData.getTotalAttendedDays()), 1, 155, 248, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph result = getKeyValueParagraph("Result : ", "-", boldFont).setMultipliedLeading(1f);
		if(examReportData.getExamResultStatus() != null){
			switch (examReportData.getExamResultStatus()){
				case PASS:
				case PASS_WITH_GRACE:
					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
							76, 142, 48, boldFont, boldFont);
					break;
				case SUPPLEMENTARY:
					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
							255, 165, 0, boldFont, boldFont);
					break;
				case FAIL:
					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
							255, 0, 0, boldFont, boldFont);
					break;
			}
		}

		int textColorR = EColorUtils._10228IntR;
		int textColorG = EColorUtils._10228IntG;
		int textColorB = EColorUtils._10228IntB;

		Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
				examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName(), textColorR, textColorG, textColorB, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
				examReportData.getTotalObtainedMarks() == null ? "-"
						: examReportData.getTotalObtainedMarks() * 10 % 10 == 0
						? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
						: String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d), textColorR, textColorG, textColorB, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
				: String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10), textColorR, textColorG, textColorB, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
				: String.valueOf(Math.round(examReportData.getPercentage() * 100) / 100d) + "%", textColorR, textColorG, textColorB, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph grade = getKeyValueParagraph("Grade : ",
				examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(), textColorR, textColorG, textColorB, boldFont, boldFont).setMultipliedLeading(1f);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(obtainedMarks, thirdCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(totalMarks, fourthCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(result, firstCellLayoutSetup)));

		if (reportType.equalsIgnoreCase("ANNUAL")) {
			addRow(table, documentLayoutSetup,
					Arrays.asList(new CellData(promotedClass, secondCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
							new CellData(percentage, thirdCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
							new CellData(grade, fourthCellLayoutSetup)));
		} else {
			addRow(table, documentLayoutSetup,
					Arrays.asList(new CellData(percentage, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
							new CellData(grade, secondCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
							new CellData(EMPTY_TEXT, thirdCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
							new CellData(EMPTY_TEXT, fourthCellLayoutSetup)));
		}

		document.add(table);

	}

	protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
										  ExamReportData examReportData) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(examReportCardLayoutData.getContentFontSize())
				.setTextAlignment(TextAlignment.LEFT);

		Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 1);

		Paragraph remarks = getKeyValueParagraph("Remarks: ", examReportData.getRemarks());

		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(remarks),
				cellLayoutSetup);
		examReportCardLayoutData.getDocument().add(remarksTable);
	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
										float contentFontSize, float defaultBorderWidth, int pageNumber) throws IOException {
		int singleContentColumn = 3;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Class Teacher"), getParagraph("Exam Incharge"),
				getParagraph("Principal")), signatureCellLayoutSetup);
		table.setFixedPosition(30f, 10f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);
	}

	protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
											  List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			Document document = examReportCardLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReport(document, documentLayoutSetup, institute, examReportData.getStudentLite(), reportType, examReportData,
						examReportCardLayoutData, pageNumber, studentManager);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}
			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;

	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			PdfFont boldFont = getRegularBoldFont();
			PdfFont regularFont = getRegularFont();
			int instituteId = institute.getInstituteId();

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateScholasticMarksGrid(document,
						documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
						"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
						null, "#39419e0");
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}

}
