package com.embrate.cloud.pdf.hpc;

import com.embrate.cloud.core.api.hpc.layout.HPCElementAlignment;
import com.embrate.cloud.core.api.hpc.payload.HPCDocumentType;
import com.embrate.cloud.core.api.hpc.utils.HPCExamImageValue;
import com.embrate.cloud.core.api.hpc.utils.HPCExamType;
import com.embrate.cloud.core.api.hpc.utils.HPCExamValue;
import com.embrate.cloud.core.api.hpc.generator.HPCDocumentLayout;
import com.embrate.cloud.core.api.hpc.generator.HPCDocument;
import com.embrate.cloud.core.api.hpc.layout.*;
import com.embrate.cloud.core.api.hpc.utils.HPCUserType;
import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.hpc.HPCFormManager;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.*;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.pdf.base.PDFGenerator;
import org.apache.commons.collections4.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class BaseHPCGenerator extends PDFGenerator implements IHPCGenerator {

	private static final float DEFAULT_IMAGE_HEIGHT = 250f;

	private static final float DEFAULT_IMAGE_WIDTH = 500f;

	public BaseHPCGenerator(AssetProvider assetProvider) {
		super(assetProvider);
	}

	public HPCDocumentLayout getDocumentLayout() throws IOException {
		PdfFont defaultRegularFont = getComicSansMSFont();
		PdfFont defaultBoldFont = getComicSansBoldFont();
		return new HPCDocumentLayout(PageSize.A4, 50f, 50f, 40f, 30f, 15f, defaultRegularFont, defaultBoldFont, HPCHorizontalTextAlignment.LEFT, HPCVerticalTextAlignment.CENTER, 0f);
	}

	public HPCDocument initHPCDocument(String studentName) throws IOException {

		DocumentOutput documentOutput = new DocumentOutput("HPC-" + studentName+".pdf", new ByteArrayOutputStream());
		HPCDocumentLayout hpcDocumentLayout = getDocumentLayout();
		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false, hpcDocumentLayout.getPageSize(), hpcDocumentLayout.getTopMargin(), hpcDocumentLayout.getBottomMargin(), hpcDocumentLayout.getSideMargin(), 0f);
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

		return new HPCDocument(documentOutput, document, documentLayoutSetup, hpcDocumentLayout);
	}

	public void generateHPCSections(Student student, HPCForm hpcForm, HPCDocument hpcDocument, Set<HPCExamType> requiredExamTypes, UUID principalStaffId, UUID classTeacherStaffId, HPCFormManager hpcFormManager, StudentManager studentManager, StaffManager staffManager) throws IOException {
		Document document = hpcDocument.getDocument();
		for (HPCSection section : hpcForm.getSections()) {
			Table sectionTable = getTable(1);
			if (section.getHeading() != null) {
				add(sectionTable, section.getHeading(), hpcDocument.getHpcDocumentLayout());
			}
			if (section.getSubHeading() != null) {
				add(sectionTable, section.getSubHeading(), hpcDocument.getHpcDocumentLayout());
			}
			renderContainer(student.getInstituteId(), student.getStudentAcademicSessionInfoResponse().getAcademicSession().getAcademicSessionId(),
					student.getStudentId(), section.getAssociatedUserType(), sectionTable, section.getContainer(), hpcDocument.getHpcDocumentLayout(), requiredExamTypes, principalStaffId, classTeacherStaffId, hpcFormManager, studentManager, staffManager);
			document.add(sectionTable);
			if (section.isPageBreak()) {
				document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
			}
			if (section.getBlankSpaceLineCount() > 0) {
				addBlankLine(document, section.getBlankSpaceLineCount());
			}
		}
	}

	private void renderContainer(int instituteId, int academicSessionId, UUID studentId, HPCUserType formUserType, Table parentContainerTable, HPCContainer container, HPCDocumentLayout documentLayout, Set<HPCExamType> requiredExamTypes, UUID principalStaffId, UUID classTeacherStaffId,
								 HPCFormManager hpcFormManager, StudentManager studentManager, StaffManager staffManager) throws IOException {
		// Skip the containers if they are not in required exam type if they are associated with some hpc exam
		if (container.getAssociatedExamType() != null && !requiredExamTypes.contains(container.getAssociatedExamType())) {
			return;
		}

		// Check child containers
		if (CollectionUtils.isNotEmpty(container.getChildContainers())) {
			int childContainerCount = getChildContainerCount(container.getChildAlignment(), container.getChildContainers(), requiredExamTypes);
			Table containerTable = getTable(childContainerCount);
			for (HPCContainer childContainer : container.getChildContainers()) {
				renderContainer(instituteId, academicSessionId, studentId, formUserType, containerTable, childContainer, documentLayout, requiredExamTypes, principalStaffId, classTeacherStaffId, hpcFormManager, studentManager, staffManager);
			}
			addContainer(parentContainerTable, containerTable, container.getPdfAttributes(), documentLayout);
//			parentContainerTable.addCell(containerTable);
			return;
		}

		// Check image section
		if (container.getImageContainer() != null) {
			Table containerTable = getTable(1);
			HPCImageContainer imageContainer = container.getImageContainer();
			renderImageElement(instituteId, academicSessionId, studentId, formUserType, requiredExamTypes, containerTable, imageContainer, hpcFormManager, studentManager);
			addContainer(parentContainerTable, containerTable, container.getPdfAttributes(), documentLayout);
			return;
		}

		// Check text element section
		if (CollectionUtils.isNotEmpty(container.getTextElements())) {
			int childContainerCount = getTextChildCount(container.getChildAlignment(), container.getTextElements().size());
			Table containerTable = getTable(childContainerCount);
			for (HPCTextElement textElement : container.getTextElements()) {
				System.out.println(textElement);
				renderTextElement(containerTable, textElement, documentLayout, requiredExamTypes, instituteId, principalStaffId, classTeacherStaffId, staffManager);
			}
			addContainer(parentContainerTable, containerTable, container.getPdfAttributes(), documentLayout);
			return;
		}

		// Check table section
		if (container.getTable() != null) {
			Table containerTable = getTable(1);
			renderTable(containerTable, container.getTable(), documentLayout, requiredExamTypes, instituteId, principalStaffId, classTeacherStaffId, staffManager);
			addContainer(parentContainerTable, containerTable, container.getPdfAttributes(), documentLayout);
//			parentContainerTable.addCell(containerTable);
			return;
		}
	}

	private void renderTextElement(Table parentContainerTable, HPCTextElement textElement, HPCDocumentLayout documentLayout, Set<HPCExamType> requiredExamTypes,
								   int instituteId, UUID principalStaffId, UUID classTeacherStaffId, StaffManager staffManager) throws IOException {
		switch (textElement.getCategory()) {
			case EXAM:
				add(parentContainerTable, textElement, documentLayout);
				if(textElement.isImageComponent()) {
					for (HPCExamImageValue examValue : textElement.getExamImageValues()) {
						if (requiredExamTypes.contains(examValue.getHpcExamType())) {
//							add(parentContainerTable, getDisplayValue(examValue.getValue()), hpcPdfAttributes, documentLayout);
//							byte[] imageByteArray = getHPCDocumentImage(instituteId, academicSessionId, studentId,
//									examType, formUserType, imageContainer.getId(), imageContainer.getImageType(), hpcFormManager, studentManager);
//							if(imageByteArray != null) {
//								Image image = new Image(ImageDataFactory.create(imageByteArray));
//								add(parentContainerTable, image, imageContainer.getImageHeight(), imageContainer.getImageWidth());
//							}
							if(examValue.getImageType() == HPCDocumentType.PRINCIPAL_SIGNATURE) {
								byte[] imageByteArray = getStaffSignature(instituteId, principalStaffId, staffManager);
								Image image = null;
								if (imageByteArray != null) {
									image = new Image(ImageDataFactory.create(imageByteArray));
								}
								add(parentContainerTable, image, examValue.getImageHeight(), examValue.getImageWidth(), true);
							}

							if(examValue.getImageType() == HPCDocumentType.CLASS_TEACHER_SIGNATURE) {
								byte[] imageByteArray = getStaffSignature(instituteId, classTeacherStaffId, staffManager);
								Image image = null;
								if (imageByteArray != null) {
									image = new Image(ImageDataFactory.create(imageByteArray));
								}
								add(parentContainerTable, image, examValue.getImageHeight(), examValue.getImageWidth(), true);
							}
						}
					}
				} else {
					for (HPCExamValue examValue : textElement.getExamValues()) {
						if (requiredExamTypes.contains(examValue.getHpcExamType())) {
							HPCPDFAttributes hpcPdfAttributes = examValue.getPdfAttributes() == null ? textElement.getPdfAttributes() : examValue.getPdfAttributes();
							add(parentContainerTable, getDisplayValue(examValue.getValue()), hpcPdfAttributes, documentLayout);
						}
					}
				}
				break;
			case GENERAL:
			default:
				String keyText = textElement.getText();
				String valueText = getDisplayValue(textElement.getGeneralInputValue());
				HPCPDFAttributes keyHpcPdfAttributes = textElement.getPdfAttributes();
				HPCPDFAttributes valueHpcPdfAttributes = textElement.getValuePdfAttributes() == null ? textElement.getPdfAttributes() : textElement.getValuePdfAttributes();
				add(parentContainerTable, keyText, valueText, keyHpcPdfAttributes, valueHpcPdfAttributes, documentLayout);
				break;
		}
	}

	private void renderImageElement(int instituteId, int academicSessionId, UUID studentId, HPCUserType formUserType, Set<HPCExamType> requiredExamTypes, Table parentContainerTable, HPCImageContainer imageContainer, HPCFormManager hpcFormManager,
									StudentManager studentManager) throws IOException {
		//Fetching first element here as in this list the first exam is for which report card is getting generated.
		HPCExamType examType = requiredExamTypes.contains(HPCExamType.TERM2) ? HPCExamType.TERM2 : HPCExamType.TERM1;
		byte[] imageByteArray = getHPCDocumentImage(instituteId, academicSessionId, studentId,
				examType, formUserType, imageContainer.getId(), imageContainer.getImageType(), hpcFormManager, studentManager);
		if(imageByteArray != null) {
			Image image = new Image(ImageDataFactory.create(imageByteArray));
			add(parentContainerTable, image, imageContainer.getImageHeight(), imageContainer.getImageWidth(), false);
		}
		if(imageContainer.getImageType() != HPCDocumentType.MYSELF) {
			return;
		}
		imageByteArray = getStudentImage(instituteId, studentId, studentManager);
		if(imageByteArray == null) {
			return;
		}
		Image image = new Image(ImageDataFactory.create(imageByteArray));
		add(parentContainerTable, image, imageContainer.getImageHeight(), imageContainer.getImageWidth(), false);
	}

	private String getDisplayValue(String value) {
		return value == null ? "" : value.trim();
	}

	private void renderTable(Table parentContainerTable, HPCTable hpcTable, HPCDocumentLayout documentLayout, Set<HPCExamType> requiredExamTypes,
							 int instituteId, UUID principalStaffId, UUID classTeacherStaffId, StaffManager staffManager) throws IOException {
		List<HPCTableHeader> finalHeaders = new ArrayList<>();
		int examColumnCount = 0;
		List<Float> headerWidths = new ArrayList<>();
		float missingRatioSum = 0f;
		for (HPCTableHeader header : hpcTable.getHeaderRow()) {
			if (header.getHeaderType() == HPCTableHeaderType.EXAM) {
				if (requiredExamTypes.contains(header.getExamType())) {
					finalHeaders.add(header);
					examColumnCount++;
					headerWidths.add(header.getWidthRatio());
				} else {
					missingRatioSum += header.getWidthRatio();
				}
			} else {
				finalHeaders.add(header);
				headerWidths.add(header.getWidthRatio());
			}
		}
		float equalMissingRation = missingRatioSum / headerWidths.size();
		float[] widths = new float[headerWidths.size()];
		int i = 0;
		for (Float headerWidth : headerWidths) {
			widths[i++] = headerWidth.floatValue() + equalMissingRation;
		}

		Table containerTable = getTable(widths, null);

		for (HPCTableHeader headerValue : finalHeaders) {
			add(containerTable, headerValue.getText(), headerValue.getPdfAttributes(), documentLayout);
		}

		for (HPCCell cell : hpcTable.getCells()) {
			renderTableCell(containerTable, hpcTable.getChildAlignment(), cell, documentLayout, requiredExamTypes, examColumnCount,
			instituteId, principalStaffId, classTeacherStaffId, staffManager);
		}

		addContainer(parentContainerTable, containerTable, hpcTable.getPdfAttributes(), documentLayout);
//		parentContainerTable.addCell(containerTable);
	}

	private void renderTableCell(Table parentContainerTable, HPCElementAlignment alignment, HPCCell cell, HPCDocumentLayout documentLayout, Set<HPCExamType> requiredExamTypes, int examColumnCount,
								 int instituteId, UUID principalStaffId, UUID classTeacherStaffId, StaffManager staffManager) throws IOException {
		if (CollectionUtils.isEmpty(cell.getChildCells())) {
			add(parentContainerTable, cell.getTextElement(), documentLayout);
			// Leaf node is always assumed as exam values in table
			if(cell.getTextElement().isImageComponent()) {
				for (HPCExamImageValue examValue : cell.getTextElement().getExamImageValues()) {
					if (requiredExamTypes.contains(examValue.getHpcExamType())) {
//							add(parentContainerTable, getDisplayValue(examValue.getValue()), hpcPdfAttributes, documentLayout);
//							byte[] imageByteArray = getHPCDocumentImage(instituteId, academicSessionId, studentId,
//									examType, formUserType, imageContainer.getId(), imageContainer.getImageType(), hpcFormManager, studentManager);
//							if(imageByteArray != null) {
//								Image image = new Image(ImageDataFactory.create(imageByteArray));
//								add(parentContainerTable, image, imageContainer.getImageHeight(), imageContainer.getImageWidth());
//							}
						if(examValue.getImageType() == HPCDocumentType.PRINCIPAL_SIGNATURE) {
							byte[] imageByteArray = getStaffSignature(instituteId, principalStaffId, staffManager);
							Image image = null;
							if (imageByteArray != null) {
								image = new Image(ImageDataFactory.create(imageByteArray));
							}
							add(parentContainerTable, image, examValue.getImageHeight(), examValue.getImageWidth(), true);
						}

						if(examValue.getImageType() == HPCDocumentType.CLASS_TEACHER_SIGNATURE) {
							byte[] imageByteArray = getStaffSignature(instituteId, classTeacherStaffId, staffManager);
							Image image = null;
							if (imageByteArray != null) {
								image = new Image(ImageDataFactory.create(imageByteArray));
							}
							add(parentContainerTable, image, examValue.getImageHeight(), examValue.getImageWidth(), true);
						}

					}
				}
			} else {
				for (HPCExamValue examValue : cell.getTextElement().getExamValues()) {
					if (requiredExamTypes.contains(examValue.getHpcExamType())) {
						HPCPDFAttributes hpcPdfAttributes = examValue.getPdfAttributes() == null ? cell.getTextElement().getPdfAttributes() : examValue.getPdfAttributes();
						add(parentContainerTable, getDisplayValue(examValue.getValue()), hpcPdfAttributes, documentLayout);
					}
				}
			}
			return;
		}

		// Container cell case
		int cellChildCount = getCellTreeCount(cell);
//		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		if (alignment == HPCElementAlignment.VERTICAL) {
//			parentContainerTable.addCell(getCell(new CellData(cell.getTextElement().getText(), cellLayoutSetup, cellChildCount == 0 ? 1 : cellChildCount, 1)));
			add(parentContainerTable, cell.getTextElement(), documentLayout, cellChildCount == 0 ? 1 : cellChildCount, 1);
//			parentContainerTable.addCell(getCell(new CellData(, cellLayoutSetup, cellChildCount == 0 ? 1 : cellChildCount, 1)));
		} else {
			// For horizontal alignment exam columns will always be merged. This implementation does not account for
			// multiple children as of now
//			parentContainerTable.addCell(getCell(new CellData(cell.getTextElement().getText(), cellLayoutSetup, 1, 1 + examColumnCount)));
			add(parentContainerTable, cell.getTextElement(), documentLayout, 1, 1 + examColumnCount);
		}
		for (HPCCell childCell : cell.getChildCells()) {
			renderTableCell(parentContainerTable, cell.getChildAlignment(), childCell, documentLayout, requiredExamTypes, examColumnCount,
			instituteId, principalStaffId, classTeacherStaffId, staffManager);
		}
	}

	private int getCellTreeCount(HPCCell cell) {
		if (CollectionUtils.isEmpty(cell.getChildCells())) {
			return 0;
		}
		int totalChildCount = 0;
		for (HPCCell childCell : cell.getChildCells()) {
			totalChildCount += (1 + getCellTreeCount(childCell));
		}
		return totalChildCount;
	}

	private int getChildContainerCount(HPCElementAlignment alignment, List<HPCContainer> childContainers, Set<HPCExamType> requiredExamTypes) {
		int validChildCount = 0;
		for (HPCContainer container : childContainers) {
			if (container.getAssociatedExamType() == null || requiredExamTypes.contains(container.getAssociatedExamType())) {
				validChildCount++;
			}
		}
		return alignment == HPCElementAlignment.HORIZONTAL ? validChildCount : 1;
	}

	private int getTextChildCount(HPCElementAlignment alignment, int childCount) {
		return alignment == HPCElementAlignment.HORIZONTAL ? childCount : 1;
	}

	private void addContainer(Table parentTable, Table cellTable, HPCPDFAttributes pdfAttributes, HPCDocumentLayout documentLayout) throws IOException {
		CellLayoutSetup cellLayoutSetup = getHPCCellLayoutSetup(pdfAttributes, documentLayout);
		Cell c = getCell(cellTable, cellLayoutSetup, 1, 1);
		if(pdfAttributes != null && pdfAttributes.isUnderline()) {
			c.setUnderline();
		}
		parentTable.addCell(c);
	}

	private CellLayoutSetup getHPCCellLayoutSetup(HPCPDFAttributes pdfAttributes, HPCDocumentLayout documentLayout) throws IOException {
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		float fontSize = documentLayout.getDefaultTextFontSize();
		PdfFont font = documentLayout.getDefaultRegularFont();
		TextAlignment textAlignment = getTextAlignment(documentLayout.getDefaultHorizontalTextAlignment());
		VerticalAlignment verticalAlignment = getVerticalTextAlignment(documentLayout.getDefaultVerticalTextAlignment());
		Border leftBorder = null;
		Border rightBorder = null;
		Border topBorder = null;
		Border bottonBorder = null;
		float rotationAngle = 0f;
		float leftPadding = documentLayout.getDefaultPadding();
		float rightPadding = documentLayout.getDefaultPadding();
		float topPadding = documentLayout.getDefaultPadding();
		float bottomPadding = documentLayout.getDefaultPadding();
		Float height = null;
		if (pdfAttributes != null) {
			HPCPDFAttributes attributes = pdfAttributes;
			height = attributes.getOverrideHeight();
			rotationAngle = (float) (Math.toRadians(attributes.getRotationAngleInDegree()));
			font = attributes.isBold() ? getComicSansBoldFont() : font;
			fontSize = attributes.getFontSize() != null ? attributes.getFontSize() : fontSize;
			textAlignment = getTextAlignment(attributes.getHorizontalTextAlignment());
			verticalAlignment = getVerticalTextAlignment(attributes.getVerticalTextAlignment());
			if (attributes.getBorder() != null) {
				if (attributes.getBorder().getAllSideBorderWidth() == null || attributes.getBorder().getAllSideBorderWidth() <= 0f) {
					leftBorder = getBorder(attributes.getBorder().getLeftBorderWidth());
					rightBorder = getBorder(attributes.getBorder().getRightBorderWidth());
					topBorder = getBorder(attributes.getBorder().getTopBorderWidth());
					bottonBorder = getBorder(attributes.getBorder().getBottomBorderWidth());
				} else {
					Border allBorder = getBorder(attributes.getBorder().getAllSideBorderWidth());
					leftBorder = allBorder;
					rightBorder = allBorder;
					topBorder = allBorder;
					bottonBorder = allBorder;
				}
			}
			if (attributes.getPadding() != null) {
				HPCPadding padding = attributes.getPadding();
				if (padding.getAllSidePadding() == null || padding.getAllSidePadding() < 0f) {
					leftPadding = getPadding(padding.getLeftPadding());
					rightPadding = getPadding(padding.getRightPadding());
					topPadding = getPadding(padding.getTopPadding());
					bottomPadding = getPadding(padding.getBottomPadding());

				} else {
					leftPadding = padding.getAllSidePadding();
					rightPadding = padding.getAllSidePadding();
					topPadding = padding.getAllSidePadding();
					bottomPadding = padding.getAllSidePadding();
				}

			}
		}

		return cellLayoutSetup.setFontSize(fontSize)
				.setHeight(height)
				.setPdfFont(font)
				.setTextAlignment(textAlignment)
				.setVerticalAlignment(verticalAlignment)
				.setRotationAngle(rotationAngle)
				.setBorderLeft(leftBorder)
				.setBorderRight(rightBorder)
				.setBorderTop(topBorder)
				.setBorderBottom(bottonBorder)
				.setPaddingLeft(leftPadding)
				.setPaddingRight(rightPadding)
				.setPaddingTop(topPadding)
				.setPaddingBottom(bottomPadding);
	}

	public Text applyStyles(Text text, HPCPDFAttributes hpcPdfAttributes, HPCDocumentLayout documentLayout) throws IOException {
		float fontSize = documentLayout.getDefaultTextFontSize();
		PdfFont font = documentLayout.getDefaultRegularFont();
		TextAlignment textAlignment = getTextAlignment(documentLayout.getDefaultHorizontalTextAlignment());
		VerticalAlignment verticalAlignment = getVerticalTextAlignment(documentLayout.getDefaultVerticalTextAlignment());
		boolean isUnderline = false;
		Border leftBorder = null;
		Border rightBorder = null;
		Border topBorder = null;
		Border bottonBorder = null;
		float rotationAngle = 0f;
		float leftPadding = documentLayout.getDefaultPadding();
		float rightPadding = documentLayout.getDefaultPadding();
		float topPadding = documentLayout.getDefaultPadding();
		float bottomPadding = documentLayout.getDefaultPadding();
		Float height = null;
		if (hpcPdfAttributes != null) {
			HPCPDFAttributes attributes = hpcPdfAttributes;
			height = attributes.getOverrideHeight();
			rotationAngle = (float) (Math.toRadians(attributes.getRotationAngleInDegree()));
			font = attributes.isBold() ? getComicSansBoldFont() : font;
			isUnderline = attributes.isUnderline();
			fontSize = attributes.getFontSize() != null ? attributes.getFontSize() : fontSize;
			textAlignment = getTextAlignment(attributes.getHorizontalTextAlignment());
			verticalAlignment = getVerticalTextAlignment(attributes.getVerticalTextAlignment());
			if (attributes.getBorder() != null) {
				if (attributes.getBorder().getAllSideBorderWidth() == null || attributes.getBorder().getAllSideBorderWidth() <= 0f) {
					leftBorder = getBorder(attributes.getBorder().getLeftBorderWidth());
					rightBorder = getBorder(attributes.getBorder().getRightBorderWidth());
					topBorder = getBorder(attributes.getBorder().getTopBorderWidth());
					bottonBorder = getBorder(attributes.getBorder().getBottomBorderWidth());
				} else {
					Border allBorder = getBorder(attributes.getBorder().getAllSideBorderWidth());
					leftBorder = allBorder;
					rightBorder = allBorder;
					topBorder = allBorder;
					bottonBorder = allBorder;
				}
			}
			if (attributes.getPadding() != null) {
				HPCPadding padding = attributes.getPadding();
				if (padding.getAllSidePadding() == null || padding.getAllSidePadding() < 0f) {
					leftPadding = getPadding(padding.getLeftPadding());
					rightPadding = getPadding(padding.getRightPadding());
					topPadding = getPadding(padding.getTopPadding());
					bottomPadding = getPadding(padding.getBottomPadding());

				} else {
					leftPadding = padding.getAllSidePadding();
					rightPadding = padding.getAllSidePadding();
					topPadding = padding.getAllSidePadding();
					bottomPadding = padding.getAllSidePadding();
				}

			}
		}

		if(isUnderline) {
			text.setUnderline();
		}
		return text.setFontSize(fontSize)
				.setFont(font)
				.setTextAlignment(textAlignment)
				.setBorderLeft(leftBorder)
				.setBorderRight(rightBorder)
				.setBorderTop(topBorder)
				.setBorderBottom(bottonBorder);
	}

	private float getPadding(Float padding) {
		return padding == null || padding < 0f ? 0f : padding.floatValue();
	}

	private Border getBorder(Float borderWidth) {
		if (borderWidth == null || borderWidth <= 0f) {
			return null;
		}
		return new SolidBorder(borderWidth);
	}

	private TextAlignment getTextAlignment(HPCHorizontalTextAlignment hpcHorizontalTextAlignment) {
		if (hpcHorizontalTextAlignment == null) {
			return TextAlignment.LEFT;
		}
		switch (hpcHorizontalTextAlignment) {
			case LEFT:
				return TextAlignment.LEFT;
			case RIGHT:
				return TextAlignment.RIGHT;
			case CENTER:
				return TextAlignment.CENTER;
			case JUSTIFIED:
				return TextAlignment.JUSTIFIED;
			default:
				return null;
		}
	}

	private VerticalAlignment getVerticalTextAlignment(HPCVerticalTextAlignment hpcVerticalTextAlignment) {
		if (hpcVerticalTextAlignment == null) {
			return VerticalAlignment.MIDDLE;
		}
		switch (hpcVerticalTextAlignment) {
			case TOP:
				return VerticalAlignment.TOP;
			case BOTTOM:
				return VerticalAlignment.BOTTOM;
			case CENTER:
				return VerticalAlignment.MIDDLE;
			default:
				return null;
		}
	}

	private void add(Table t, HPCTextElement textElement, HPCDocumentLayout documentLayout) throws IOException {
		add(t, textElement, documentLayout, 1, 1);
	}

//	private void add(Table t, HPCTextElement textElement, HPCDocumentLayout documentLayout, int rowSpan, int colSpan) throws IOException {
//		CellLayoutSetup cellLayoutSetup = getHPCCellLayoutSetup(textElement.getPdfAttributes(), documentLayout);
//		Paragraph p = getText(textElement.getText(), documentLayout);
//		if (textElement.getTextAlignment() == HPCElementAlignment.VERTICAL) {
//			p.setRotationAngle(Math.PI / 2);
//		}
//		c.add(p);
//		t.addCell(c);
//	}

//	private void add(Table t, String text, HPCDocumentLayout documentLayout) {
//		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup()
//				.setBorder(new SolidBorder(1f))
//				.setFontSize(documentLayout.getDefaultTextFontSize())
//				.setPdfFont(documentLayout.getDefaultRegularFont());
//		CellData cellData = new CellData(text, cellLayoutSetup, 1, 1);
//		Cell c = getCell(cellData);
////		c.add(getText(text, documentLayout));
//		t.addCell(c);
//	}

	private void addImageSection(Table t, float imageHeight) {
		Cell c = new Cell().setHeight(imageHeight);
		t.addCell(c);
	}

	public void add(Table parentTable, HPCTextElement textElement, HPCDocumentLayout documentLayout, int rowSpan, int colSpan) throws IOException {
		add(parentTable, textElement.getText(), textElement.getPdfAttributes(), documentLayout, rowSpan, colSpan);
	}

	public void add(Table parentTable, String text, HPCPDFAttributes pdfAttributes, HPCDocumentLayout documentLayout) throws IOException {
		add(parentTable, text, pdfAttributes, documentLayout, 1, 1);
	}

	public void add(Table parentTable, String keyStr, String valueStr, HPCPDFAttributes keyPdfAttributes, HPCPDFAttributes valuePdfAttributes, HPCDocumentLayout documentLayout) throws IOException {
		add(parentTable, keyStr, valueStr, keyPdfAttributes, valuePdfAttributes, documentLayout, 1, 1);
	}

	public void add(Table parentTable, String keyStr, String valueStr, HPCPDFAttributes keyPdfAttributes, HPCPDFAttributes valuePdfAttributes, HPCDocumentLayout documentLayout, int rowSpan, int colSpan) throws IOException {

		// Split template into parts around %s
		String[] parts = keyStr.split("%s");

		// Create Text objects for each part
		Text keyPart1 = parts.length > 0 ? applyStyles(new Text(parts[0]), keyPdfAttributes, documentLayout) : new Text("");
		Text valueText = valueStr != null ? applyStyles(new Text(valueStr), valuePdfAttributes, documentLayout) : new Text("");
		Text keyPart2 = parts.length > 1 ? applyStyles(new Text(parts[1]), keyPdfAttributes, documentLayout) : new Text("");

		// Combine all parts into a Paragraph
		CellLayoutSetup cellLayoutSetup = getHPCCellLayoutSetup(keyPdfAttributes, documentLayout);
		Paragraph paragraph = new Paragraph().add(keyPart1).add(valueText).add(keyPart2);

		if(keyPdfAttributes.isUnderline()) {
			paragraph.setUnderline();
		}

		Cell c = getCell(new CellData(paragraph, cellLayoutSetup, rowSpan, colSpan));

		parentTable.addCell(c);
	}

	public void add(Table parentTable, String text, HPCPDFAttributes pdfAttributes, HPCDocumentLayout documentLayout, int rowSpan, int colSpan) throws IOException {
		CellLayoutSetup cellLayoutSetup = getHPCCellLayoutSetup(pdfAttributes, documentLayout);
		Cell c = getCell(new CellData(text, cellLayoutSetup, rowSpan, colSpan));
		if(pdfAttributes != null && pdfAttributes.isUnderline()) {
			c.setUnderline();
		}
		parentTable.addCell(c);
	}

	public void add(Table parentTable, Image image, Float height, Float width, boolean hasBorder) throws IOException {

		Cell cell = new Cell();
		if(image != null) {
			image.setHeight(height == null ? DEFAULT_IMAGE_HEIGHT : height);
			image.setWidth(width == null ? DEFAULT_IMAGE_WIDTH : width);
			image.setHorizontalAlignment(HorizontalAlignment.CENTER);
			cell.add(image);
		}

		cell.setBorder(hasBorder ? new SolidBorder(1) : null);
		parentTable.addCell(cell);
	}

	public Table getTable(int columnCount, Float totalWidth) {
		float[] columnWidths = new float[columnCount];
		for (int i = 0; i < columnCount; i++) {
			columnWidths[i] = 1f / columnCount;
		}
		return getTable(columnWidths, totalWidth);
	}

	public Table getTable(float[] widths, Float totalWidth) {
		if (totalWidth != null && totalWidth > 0f) {
			return new Table(widths).setWidth(totalWidth.floatValue()).setFixedLayout();
		}

		return new Table(widths).setWidth(UnitValue.createPercentValue(100)).setFixedLayout();
	}

	public Table getTable(int columnCount) {
		return getTable(columnCount, null);
	}


	protected void generateMetaDataLayout(Document document, HPCDocument hpcDocument, DocumentLayoutData documentLayoutData, Institute institute, Student student, StudentManager studentManager, String hPCTitle1, String hPCTitle2, String hPCTitle3) throws IOException {
		generateFrontPage(hpcDocument, documentLayoutData, institute, student, studentManager, 170f,
				hpcDocument.getDocumentLayoutSetup().getPageSize().getHeight() * 0.53f, hPCTitle1, hPCTitle2, hPCTitle3);
	}

	protected void generateFrontPage(HPCDocument hpcDocument, DocumentLayoutData documentLayoutData, Institute institute, Student student, StudentManager studentManager, float offsetX, float offsetY, String hPCTitle1, String hPCTitle2, String hPCTitle3) throws IOException {

		generateDynamicImageProvider(documentLayoutData, hpcDocument.getDocumentLayoutSetup().getPageSize().getWidth() / 2 - documentLayoutData.getLogoWidth() / 2, offsetY - 150, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
		int singleContentColumn = 1;
		float contentFontSize = documentLayoutData.getContentFontSize();
		PdfFont boldFont = getCambriaBoldFont();
		PdfFont regularFont = getRegularFont();
		Table table = getPDFTable(hpcDocument.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize + 2f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, hpcDocument.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase())),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 25f));
		addRow(table, hpcDocument.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 6f).setPdfFont(boldFont));
		addRow(table, hpcDocument.getDocumentLayoutSetup(), Arrays.asList(getKeyValueParagraph("Phn No. - " , institute.getPhoneNumber(), boldFont, boldFont)),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 6f));

		hpcDocument.getDocument().add(table);
		addBlankLine(hpcDocument.getDocument(), false, 55);
		table = getPDFTable(hpcDocument.getDocumentLayoutSetup(), singleContentColumn);

		addRow(table, hpcDocument.getDocumentLayoutSetup(), Arrays.asList(getParagraph(hPCTitle1)),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 12f).setPdfFont(boldFont));
		addRow(table, hpcDocument.getDocumentLayoutSetup(), Arrays.asList(getParagraph(hPCTitle2)),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 12f).setPdfFont(boldFont));
		addRow(table, hpcDocument.getDocumentLayoutSetup(), Arrays.asList(getParagraph(hPCTitle3)),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 12f).setPdfFont(boldFont));
		hpcDocument.getDocument().add(table);
		addBlankLine(hpcDocument.getDocument(), false, 5);

		PdfDocument pdfDocument = hpcDocument.getDocument().getPdfDocument();
		if (pdfDocument.getNumberOfPages() > 0) {
			PdfPage page = pdfDocument.getLastPage();
			PdfCanvas canvas = new PdfCanvas(page);
			canvas.moveTo(40, 40);
			canvas.lineTo(555, 40);
			canvas.lineTo(555, 800);
			canvas.lineTo(40, 800);

			canvas.setLineWidth(1.75f);
			canvas.closePathStroke();
		}
	}

}
