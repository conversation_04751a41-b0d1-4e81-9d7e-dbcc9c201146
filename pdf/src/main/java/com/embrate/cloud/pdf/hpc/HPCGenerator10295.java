package com.embrate.cloud.pdf.hpc;

import com.embrate.cloud.core.api.hpc.generator.HPCDocument;
import com.embrate.cloud.core.api.hpc.layout.HPCForm;
import com.embrate.cloud.core.api.hpc.utils.HPCExamType;
import com.embrate.cloud.core.api.hpc.utils.HPCFormWithStudent;
import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.hpc.HPCFormManager;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.util.*;

public class HPCGenerator10295 extends GlobalHPCGenerator{
	private static final Logger logger = LogManager.getLogger(HPCGenerator10295.class);

	public HPCGenerator10295(AssetProvider assetProvider) {
		super(assetProvider);
	}

	@Override
	public DocumentOutput generateIndividual(Institute institute, Student student, HPCExamType examType, HPCForm hpcForm,
											 UUID principalStaffId, UUID classTeacherStaffId,
											 HPCFormManager hpcFormManager, StudentManager studentManager, StaffManager staffManager) {
		try {
			HPCDocument hpcDocument = initHPCDocument(student.getStudentBasicInfo().getName());
			Set<HPCExamType> requiredExamTypes = new HashSet<>(examType.getApplicableExamTypes());
			DocumentLayoutData documentLayoutData = generateHPCDocumentLayoutData(institute, hpcDocument);
			generateStudentHPCReportCard(documentLayoutData, institute, student, hpcForm, hpcDocument, requiredExamTypes,
					principalStaffId, classTeacherStaffId, hpcFormManager, studentManager, staffManager);
			hpcDocument.getDocument().close();
			return hpcDocument.getOutput();
		} catch (Exception e) {
			logger.error("Exception in generating hpc document", e);
		}
		return null;

	}

	private void generateStudentHPCReportCard(DocumentLayoutData documentLayoutData, Institute institute, Student student, HPCForm hpcForm, HPCDocument hpcDocument, Set<HPCExamType> requiredExamTypes,
											  UUID principalStaffId, UUID classTeacherStaffId, HPCFormManager hpcFormManager,
											  StudentManager studentManager, StaffManager staffManager) throws IOException {
		Document document = hpcDocument.getDocument();
		List<UUID> standardIdNurToUKG = new ArrayList<>();
		standardIdNurToUKG.add(UUID.fromString("10523d7c-2c13-4d56-8dc8-187c0a3f5b1f")); //class Nur
		standardIdNurToUKG.add(UUID.fromString("a0fa4a29-6248-4b48-ab5f-699a35fb9b4c")); //class Nur
		standardIdNurToUKG.add(UUID.fromString("7ff462c9-68bb-46b0-b6de-860802eb3c99")); // class LKG
		standardIdNurToUKG.add(UUID.fromString("3778aae8-6b96-4c8a-9c14-cf2cffb33741")); // class UKG

		String hPCTitle1 = "HOLISTIC";
		String hPCTitle2 = "PROGRESS CARD";
		String hPCTitle3 = "Foundational Stage 1-3";
		String hPCTitle4 = "(" + student.getStudentAcademicSessionInfoResponse().getAcademicSession().getShortYearDisplayName() + ")";

		if(standardIdNurToUKG.contains(student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId())) {
			generateMetaDataLayout(document, hpcDocument, documentLayoutData, institute, student, studentManager, hPCTitle1, hPCTitle2, hPCTitle3, hPCTitle4);
		}
		generateHPCSections(student, hpcForm, hpcDocument, requiredExamTypes, principalStaffId, classTeacherStaffId, hpcFormManager, studentManager, staffManager);

	}

	protected ExamReportCardLayoutData generateHPCDocumentLayoutData(Institute institute, HPCDocument hpcDocument)
			throws IOException {
		float contentFontSize = 12f;
		float defaultBorderWidth = 0.5f;
		float logoWidth = 350f;
		float logoHeight = 350f;

		int instituteId = institute.getInstituteId();
		return new ExamReportCardLayoutData(hpcDocument.getDocument(), hpcDocument.getDocumentLayoutSetup(), null, null, contentFontSize,
				defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
	}


	@Override
	public DocumentOutput generateBulk(Institute institute, HPCExamType examType, List<HPCFormWithStudent> studentHPCFormList,
									   UUID principalStaffId, UUID classTeacherStaffId,
									   HPCFormManager hpcFormManager, StudentManager studentManager, StaffManager staffManager) {
		try {
			HPCDocument hpcDocument = initHPCDocument(examType.getDisplayName());
			DocumentLayoutData documentLayoutData = generateHPCDocumentLayoutData(institute, hpcDocument);
			Set<HPCExamType> requiredExamTypes = new HashSet<>(examType.getApplicableExamTypes());

			int pageNumber = 1;
			for (HPCFormWithStudent hpcFormWithStudent : studentHPCFormList) {

				generateStudentHPCReportCard(documentLayoutData, institute, hpcFormWithStudent.getStudent(), hpcFormWithStudent.getForm(),
						hpcDocument, requiredExamTypes, principalStaffId, classTeacherStaffId, hpcFormManager, studentManager, staffManager);

				if (pageNumber != studentHPCFormList.size()) {
					hpcDocument.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}

				pageNumber++;
			}

			hpcDocument.getDocument().close();
			return hpcDocument.getOutput();

		} catch (IOException e) {
			e.printStackTrace();
		}

		return null;
	}

	protected void generateMetaDataLayout(Document document, HPCDocument hpcDocument, DocumentLayoutData documentLayoutData, Institute institute, Student student, StudentManager studentManager, String hPCTitle1, String hPCTitle2, String hPCTitle3, String hPCTitle4) throws IOException {
		generateFrontPage(hpcDocument, documentLayoutData, institute, student, studentManager, 170f,
				hpcDocument.getDocumentLayoutSetup().getPageSize().getHeight() * 0.53f, hPCTitle1, hPCTitle2, hPCTitle3, hPCTitle4);
	}

	protected void generateFrontPage(HPCDocument hpcDocument, DocumentLayoutData documentLayoutData, Institute institute, Student student, StudentManager studentManager, float offsetX, float offsetY, String hPCTitle1, String hPCTitle2, String hPCTitle3, String hPCTitle4) throws IOException {

		generateDynamicImageProvider(documentLayoutData, hpcDocument.getDocumentLayoutSetup().getPageSize().getWidth() / 2 - documentLayoutData.getLogoWidth() / 2, offsetY - 150, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		generateLogo(hpcDocument.getDocument(), hpcDocument.getDocumentLayoutSetup(), ImageProvider.INSTANCE.getImage(ImageProvider._CBSE_LOGO_2),
				-10, 95, 1.5f, 1.5f);

		generateDynamicImageProvider(documentLayoutData, -420, -210, 0.25f, 0.20f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_SECONDARY_LOGO);


		int singleContentColumn = 1;
		float contentFontSize = documentLayoutData.getContentFontSize();
		PdfFont boldFont = getCambriaBoldFont();
		PdfFont regularFont = getRegularFont();
		Table table = getPDFTable(hpcDocument.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize + 2f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, hpcDocument.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase()).setFontColor(new DeviceRgb(0, 32, 96))),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 25f));
		addRow(table, hpcDocument.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1()).setFontColor(new DeviceRgb(0, 32, 96))),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 6f).setPdfFont(boldFont));
		addRow(table, hpcDocument.getDocumentLayoutSetup(), Arrays.asList(getKeyValueParagraph("Phn No. - " , institute.getPhoneNumber(), boldFont, boldFont).setFontColor(new DeviceRgb(0, 32, 96))),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 6f));

		hpcDocument.getDocument().add(table);
		addBlankLine(hpcDocument.getDocument(), false, 58);
		table = getPDFTable(hpcDocument.getDocumentLayoutSetup(), singleContentColumn);

		addRow(table, hpcDocument.getDocumentLayoutSetup(), Arrays.asList(getParagraph(hPCTitle1).setMultipliedLeading(0.95f).setFontColor(new DeviceRgb(238, 87, 62))),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 15f).setPdfFont(boldFont));
		addRow(table, hpcDocument.getDocumentLayoutSetup(), Arrays.asList(getParagraph(hPCTitle2).setMultipliedLeading(0.95f).setFontColor(new DeviceRgb(0, 32, 96))),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 15f).setPdfFont(boldFont));
		addRow(table, hpcDocument.getDocumentLayoutSetup(), Arrays.asList(getParagraph(hPCTitle3).setMultipliedLeading(0.95f).setFontColor(new DeviceRgb(0, 32, 96))),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 12f).setPdfFont(boldFont));
		addRow(table, hpcDocument.getDocumentLayoutSetup(), Arrays.asList(getParagraph(hPCTitle4).setMultipliedLeading(0.95f).setFontColor(new DeviceRgb(0, 32, 96))),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 12f).setPdfFont(boldFont));
		hpcDocument.getDocument().add(table);

		PdfDocument pdfDocument = hpcDocument.getDocument().getPdfDocument();
		if (pdfDocument.getNumberOfPages() > 0) {
			PdfPage page = pdfDocument.getLastPage();
			PdfCanvas canvas = new PdfCanvas(page);
			canvas.moveTo(40, 40);
			canvas.lineTo(555, 40);
			canvas.lineTo(555, 800);
			canvas.lineTo(40, 800);

			canvas.setLineWidth(1.75f);
			canvas.closePathStroke();
		}
	}
}
