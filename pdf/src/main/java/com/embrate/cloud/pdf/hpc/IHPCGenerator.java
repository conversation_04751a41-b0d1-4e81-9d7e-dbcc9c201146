package com.embrate.cloud.pdf.hpc;

import com.embrate.cloud.core.api.hpc.layout.HPCForm;
import com.embrate.cloud.core.api.hpc.utils.HPCExamType;
import com.embrate.cloud.core.api.hpc.utils.HPCFormWithStudent;
import com.embrate.cloud.core.lib.hpc.HPCFormManager;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public interface IHPCGenerator {

	DocumentOutput generateIndividual(Institute institute, Student student, HPCExamType examType, HPCForm hpcForm,
									  UUID principalStaffId, UUID class<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>d,
									  HPCFormManager hpcFormManager, StudentManager student<PERSON>anager, StaffManager staffManager);

	DocumentOutput generateBulk(Institute institute, HPCExamType examType, List<HPCFormWithStudent> studentHPCFormList,
								UUID principalStaffId, UUID classTeacherStaffId,
								HPCFormManager hpcFormManager, StudentManager studentManager, StaffManager staffManager);
}
