package com.embrate.cloud.pdf.inventory.store;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.inventory.InventoryTransactionType;
import com.lernen.cloud.core.api.inventory.InventoryUserType;
import com.lernen.cloud.core.api.inventory.PurchasedProduct;
import com.lernen.cloud.core.api.inventory.TransactionSummary;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.StringHelper;
import com.lernen.cloud.core.utils.images.ImageProvider;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;

import static com.lernen.cloud.core.utils.NumberUtils.addValues;
import static com.lernen.cloud.core.utils.NumberUtils.subtractValues;

public class StoreInventoryInvoiceGenerator10030 extends StoreInventoryInvoiceGenerator {
	public StoreInventoryInvoiceGenerator10030(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(StoreInventoryInvoiceGenerator10030.class);

	public static final float DEFAULT_LOGO_WIDTH = 40f;
	public static final float DEFAULT_LOGO_HEIGHT = 40f;

	@Override
	public DocumentOutput generateInvoice(Institute institute, TransactionSummary transactionSummary,
			String documentName, boolean officeCopy) {
		int instituteId = institute.getInstituteId();
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			/**
			 * Office copy not required by 10030/31
			 */
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
			float contentFontSize = 8f;
			float headerFontSize = 11f;
			float defaultBorderWidth = 0.1f;
			Document document = initDocument(invoice.getContent(), documentLayoutSetup);

			return generateInvoice(institute, transactionSummary, invoice, documentLayoutSetup, contentFontSize, headerFontSize, defaultBorderWidth, document, false);
		} catch (Exception e) {
			logger.error("Unable to create invoice for transaction {}", transactionSummary.getTransactionId(), e);
		}
		return null;
	}

}
