package com.embrate.cloud.pdf.inventory.store.v2;

import com.embrate.cloud.core.api.inventory.v2.TradeProductBatchSummary;
import com.embrate.cloud.core.api.inventory.v2.TradeProductSummary;
import com.embrate.cloud.core.api.inventory.v2.transaction.InventoryTransactionSummary;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.inventory.InventoryTransactionStatus;
import com.lernen.cloud.core.api.inventory.InventoryTransactionType;
import com.lernen.cloud.core.api.inventory.InventoryUserType;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.StringHelper;
import com.lernen.cloud.core.utils.images.ImageProvider;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.lernen.cloud.core.utils.NumberUtils.*;

public class StoreInventoryInvoiceGenerator10190 extends StoreInventoryInvoiceGenerator {
	public StoreInventoryInvoiceGenerator10190(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(StoreInventoryInvoiceGenerator10190.class);

//	public static final float DEFAULT_LOGO_WIDTH = 75f;
//	public static final float DEFAULT_LOGO_HEIGHT = 45f;

	@Override
	public DocumentOutput generateInvoice(Institute institute, InventoryTransactionSummary transactionSummary,
			String documentName, boolean officeCopy) {
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(officeCopy);
			float contentFontSize = 8f;
			float headerFontSize = 10f;
			float defaultBorderWidth = 0.1f;
			Document document = initDocument(invoice.getContent(), documentLayoutSetup);

			return generateInvoice(institute, transactionSummary, invoice, documentLayoutSetup, contentFontSize, headerFontSize, defaultBorderWidth, document, true);
		} catch (Exception e) {
			logger.error("Unable to create invoice for transaction {}", transactionSummary.getTransactionId(), e);
		}
		return null;
	}

	public DocumentOutput generateInvoice(Institute institute, InventoryTransactionSummary transactionSummary, DocumentOutput invoice, DocumentLayoutSetup documentLayoutSetup, float contentFontSize, float headerFontSize, float defaultBorderWidth, Document document, boolean includeInstituteName) throws IOException {
		switch (transactionSummary.getTransactionType()) {
			case SALE:
			case SALES_RETURN:
				generateSalesInvoice(document, documentLayoutSetup, institute, headerFontSize, contentFontSize, transactionSummary,
						defaultBorderWidth, includeInstituteName);
				break;
			case PURCHASE:
			case RETURN:
				generatePurchaseInvoice(document, documentLayoutSetup, institute, headerFontSize, contentFontSize, transactionSummary,
						defaultBorderWidth, includeInstituteName);
				break;
			case ISSUE:
				generateIssueInvoice(document, documentLayoutSetup, institute, headerFontSize, contentFontSize, transactionSummary,
						defaultBorderWidth, includeInstituteName);
				break;
			default:
				break;
		}
		if (transactionSummary.getInventoryTransactionStatus() == InventoryTransactionStatus.CANCELLED) {
			PdfPage pdfPage = document.getPdfDocument().getPage(1);
			Rectangle pagesize = pdfPage.getPageSizeWithRotation();

			float x = documentLayoutSetup.isOfficeCopy() ? pagesize.getWidth() / 8 - 50 : pagesize.getWidth() / 4 + 30;
			float y = documentLayoutSetup.isOfficeCopy() ? pagesize.getHeight() * 0.75f : pagesize.getHeight() * 0.70f;
			addWaterMark(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(ImageProvider.CANCELLED_TEXT_IMAGE2), x, y);

		}
		else if (transactionSummary.getTransactionType() == InventoryTransactionType.SALES_RETURN) {
			addWaterMark(document, documentLayoutSetup,
					ImageProvider.INSTANCE.getImage(ImageProvider.RETURNED_TEXT_IMAGE));
		}
		document.close();
		return invoice;
	}

	public void generateSalesInvoice(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute, float headerFontSize,
									 float contentFontSize, InventoryTransactionSummary transactionSummary, float defaultBorderWidth, boolean includeInstituteName)
			throws IOException {
		generateSalesInvoiceHeader(document, documentLayoutSetup, transactionSummary, institute, headerFontSize, contentFontSize,
				defaultBorderWidth, includeInstituteName);
//		generateLogo(document, documentLayoutSetup, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), DEFAULT_LOGO_WIDTH,
//				DEFAULT_LOGO_HEIGHT);
		generateItemsContent(document, documentLayoutSetup, contentFontSize, transactionSummary,
				defaultBorderWidth);
		generatePaymentSummary(document, documentLayoutSetup, contentFontSize, transactionSummary,
				defaultBorderWidth);
	}

	public void generateIssueInvoice(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute, float headerFontSize,
									 float contentFontSize, InventoryTransactionSummary transactionSummary, float defaultBorderWidth, boolean includeInstituteName)
			throws IOException {
		generateSalesInvoiceHeader(document, documentLayoutSetup, transactionSummary, institute, headerFontSize, contentFontSize,
				defaultBorderWidth, includeInstituteName);
//		generateLogo(document, documentLayoutSetup, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), DEFAULT_LOGO_WIDTH,
//				DEFAULT_LOGO_HEIGHT);
		generateItemsContent(document, documentLayoutSetup, contentFontSize, transactionSummary,
				defaultBorderWidth);
		generatePaymentSummary(document, documentLayoutSetup, contentFontSize, transactionSummary,
				defaultBorderWidth);
	}

	public void generatePurchaseInvoice(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute, float headerFontSize,
										float contentFontSize, InventoryTransactionSummary transactionSummary, float defaultBorderWidth, boolean includeInstituteName)
			throws IOException {
		generatePurchaseInvoiceHeader(document, documentLayoutSetup, transactionSummary, institute, headerFontSize, contentFontSize,
				defaultBorderWidth, includeInstituteName);
//		generateLogo(document, documentLayoutSetup, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), DEFAULT_LOGO_WIDTH,
//				DEFAULT_LOGO_HEIGHT);
		generateItemsContent(document, documentLayoutSetup, contentFontSize, transactionSummary,
				defaultBorderWidth);
		generatePaymentSummary(document, documentLayoutSetup, contentFontSize, transactionSummary,
				defaultBorderWidth);
	}

	public void generateSalesInvoiceHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
										   InventoryTransactionSummary transactionSummary, Institute institute, float headerFontSize,
										   float contentFontSize, float defaultBorderWidth, boolean includeInstituteName) throws IOException {

//		Table table = getPDFTable(documentLayoutSetup, new float[] {0.32f, 0.68f});
//
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setTextAlignment(TextAlignment.LEFT);
//
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""), getParagraph(institute.getInstituteName())),
//				cellLayoutSetup.setFontSize(headerFontSize));
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""), getParagraph(institute.getLetterHeadLine1())),
//				cellLayoutSetup.setFontSize(8f));
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""), getParagraph(institute.getLetterHeadLine2())),
//				cellLayoutSetup);
//
//		document.add(table);

		int singleContentColumn = 1;
		cellLayoutSetup.setTextAlignment(TextAlignment.CENTER);
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);

		if (transactionSummary.getTransactionType() == InventoryTransactionType.ISSUE) {
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Issue Receipt")), cellLayoutSetup);
		} else {
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Sales Order Receipt")), cellLayoutSetup);
		}

		document.add(table);

		// Student copy section
		singleContentColumn = 1;
		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData("(Customer Copy)", cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER))),
				Arrays.asList(new CellData("(Office Copy)", cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER))));
		document.add(table);

		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		cellLayoutSetup.setTextAlignment(TextAlignment.LEFT);
		String customerName = transactionSummary.getInventoryUserType() == InventoryUserType.STUDENT ?
				transactionSummary.getStudentLite().getName() + " (" + transactionSummary.getStudentLite().getAdmissionNumber() + ")"
				: transactionSummary.getBuyerName();

		addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("Invoice: ", transactionSummary.getInvoiceId())),
				cellLayoutSetup.setFontSize(contentFontSize));
		addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("Customer Name: ", customerName)),
				cellLayoutSetup.setFontSize(contentFontSize));
		addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("Date: ", DateUtils.getFormattedDate((int) (transactionSummary.getTransactionDate() / 1000l)))),
				cellLayoutSetup.setFontSize(contentFontSize));

		document.add(table);

	}

	public void generatePurchaseInvoiceHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
											  InventoryTransactionSummary transactionSummary, Institute institute, float headerFontSize,
											  float contentFontSize, float defaultBorderWidth, boolean includeInstituteName) throws IOException {

//		float instituteFontSize = headerFontSize + 1;
//		int singleContentColumn = 1;
//		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
//
//		CellLayoutSetup headerCellLayoutSetup = new CellLayoutSetup();
//		headerCellLayoutSetup.setPdfFont(getRegularBoldFont()).setTextAlignment(TextAlignment.CENTER);
//
//
//		if (includeInstituteName) {
//			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName())),
//					headerCellLayoutSetup.setFontSize(instituteFontSize));
//		}

//		Table table = getPDFTable(documentLayoutSetup, new float[] {0.32f, 0.68f});

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setTextAlignment(TextAlignment.LEFT);

//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""), getParagraph(institute.getInstituteName())),
//				cellLayoutSetup.setFontSize(headerFontSize));
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""), getParagraph(institute.getLetterHeadLine1())),
//				cellLayoutSetup.setFontSize(8f));
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""), getParagraph(institute.getLetterHeadLine2())),
//				cellLayoutSetup);
//
//		document.add(table);

		int singleContentColumn = 1;
		cellLayoutSetup.setTextAlignment(TextAlignment.CENTER);
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);

		String purchaseReturnKey = "";
		if (transactionSummary.getTransactionType() == InventoryTransactionType.RETURN) {
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Seller Return Order Receipt")),
					cellLayoutSetup);
			purchaseReturnKey = "Returned To";
		} else {
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Purchase Order Receipt")),
					cellLayoutSetup);
			purchaseReturnKey = "Purchased From";

		}

		document.add(table);

		// Student copy section
		singleContentColumn = 1;
		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData("(Customer Copy)", cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER))),
				Arrays.asList(new CellData("(Office Copy)", cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER))));
		document.add(table);

		singleContentColumn = 2;
		table = getPDFTable(documentLayoutSetup, singleContentColumn);

		cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(purchaseReturnKey, firstCellLayoutSetup.copy().setPdfFont(getRegularBoldFont())),
				new CellData(getKeyValueParagraph("Invoice: ", transactionSummary.getInvoiceId()), thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(transactionSummary.getSupplier().getSupplierName(), firstCellLayoutSetup),
				new CellData(getKeyValueParagraph("Payment Mode: ", transactionSummary.getTransactionMode().getDisplayName()), thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(transactionSummary.getSupplier().getAddress(), firstCellLayoutSetup),
				new CellData(getKeyValueParagraph("Date: ", DateUtils.getFormattedDate((int) (transactionSummary.getTransactionDate() / 1000l))), thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(StringHelper.getAddress(transactionSummary.getSupplier().getCity(), transactionSummary.getSupplier().getState(), transactionSummary.getSupplier().getZipcode()), firstCellLayoutSetup),
				new CellData(EMPTY_TEXT, thirdCellLayoutSetup)));

		document.add(table);

	}


	/**
	 * If there is any difference in sales return invoice
	 *
	 * @param document
	 * @param documentLayoutSetup
	 * @param contentFontSize
	 * @param transactionSummary
	 * @param defaultBorderWidth
	 * @throws IOException
	 */
	public void generateItemsContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
									 InventoryTransactionSummary transactionSummary, float defaultBorderWidth) throws IOException {

		switch (transactionSummary.getTransactionType()) {
			case ISSUE:
				generateIssueItemsContent(document, documentLayoutSetup, contentFontSize, transactionSummary,
						defaultBorderWidth);
				break;
			default:
				generateSalesItemsContent(document, documentLayoutSetup, contentFontSize, transactionSummary,
						defaultBorderWidth);
				break;
		}

	}

	private StoreInventoryInvoiceGenerator.PurchaseItemHeaderState getPurchaseItemHeaderState(boolean noDiscountItem, boolean noTaxItem) {
		if (noDiscountItem && noTaxItem) {
			return StoreInventoryInvoiceGenerator.PurchaseItemHeaderState.WITHOUT_DISCOUNT_AND_TAX;
		}
		if (!noDiscountItem && !noTaxItem) {
			return StoreInventoryInvoiceGenerator.PurchaseItemHeaderState.WITH_DISCOUNT_AND_TAX;
		}
		if (noDiscountItem) {
			return StoreInventoryInvoiceGenerator.PurchaseItemHeaderState.WITH_TAX_ONLY;
		}
		return StoreInventoryInvoiceGenerator.PurchaseItemHeaderState.WITH_DISCOUNT_ONLY;
	}

	public void generateSalesItemsContent(Document document, DocumentLayoutSetup documentLayoutSetup,
										  float contentFontSize, InventoryTransactionSummary transactionSummary, float defaultBorderWidth)
			throws IOException {

		if (CollectionUtils.isEmpty(transactionSummary.getTradeProductSummaryList())) {
			return;
		}

		boolean noDiscountItem = true;
		boolean noTaxItem = true;
		for (TradeProductSummary tradeProductSummary : transactionSummary.getTradeProductSummaryList()) {
			for (TradeProductBatchSummary productBatchSummary : tradeProductSummary.getTradeProductBatchSummaryList()) {
				if (!lteZeroOrNull(productBatchSummary.getTotalDiscount())) {
					noDiscountItem = false;
				}
				if (!lteZeroOrNull(productBatchSummary.getTotalTax())) {
					noTaxItem = false;
				}
			}
		}

		CellLayoutSetup itemCellLayoutSetup = new CellLayoutSetup();
		itemCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth))
				.setBorderRight(new SolidBorder(defaultBorderWidth));


		CellLayoutSetup purchaseItemsHeaderCellLayoutSetup = new CellLayoutSetup();
		purchaseItemsHeaderCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.LEFT);

		CellLayoutSetup purchaseItemsCellLayoutSetup = new CellLayoutSetup();
		purchaseItemsCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.LEFT);

		StoreInventoryInvoiceGenerator.PurchaseItemHeaderState purchaseItemHeaderState = getPurchaseItemHeaderState(noDiscountItem, noTaxItem);
		float[] headerWidthCellArray = new float[]{};
		List<CellData> headerCellArray = new ArrayList<>();
		switch (purchaseItemHeaderState) {
			case WITH_DISCOUNT_AND_TAX:
				headerWidthCellArray = DEFAULT_PURCHASE_ITEMS_HEADER_WIDTH;
				headerCellArray = Arrays.asList(new CellData("#", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Particulars", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Qty", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Unit Price", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Amt.", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Disc.", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Tax", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Total", purchaseItemsHeaderCellLayoutSetup));
				break;
			case WITHOUT_DISCOUNT_AND_TAX:
				headerWidthCellArray = PURCHASE_ITEMS_WITHOUT_DISCOUNT_TAX_HEADER_WIDTH;
				headerCellArray = Arrays.asList(new CellData("#", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Particulars", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Qty", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Unit Price", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Net Amt", purchaseItemsHeaderCellLayoutSetup));
				break;
			case WITH_TAX_ONLY:
				headerWidthCellArray = PURCHASE_ITEMS_WITH_DISCOUNT_OR_TAX_HEADER_WIDTH;
				headerCellArray = Arrays.asList(new CellData("#", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Particulars", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Qty", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Unit Price", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Total", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Tax", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Net Amt", purchaseItemsHeaderCellLayoutSetup));
				break;
			case WITH_DISCOUNT_ONLY:
				headerWidthCellArray = PURCHASE_ITEMS_WITH_DISCOUNT_OR_TAX_HEADER_WIDTH;
				headerCellArray = Arrays.asList(new CellData("#", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Particulars", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Qty", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Unit Price", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Total", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Disc.", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Net Amt", purchaseItemsHeaderCellLayoutSetup));
				break;
			default:
				break;
		}

		Table purchaseItemsTable = getPDFTable(documentLayoutSetup, headerWidthCellArray);
		addRow(purchaseItemsTable, documentLayoutSetup, headerCellArray);

		int count = 0;
		for (TradeProductSummary tradeProductSummary : transactionSummary.getTradeProductSummaryList()) {
			for (TradeProductBatchSummary productBatchSummary : tradeProductSummary.getTradeProductBatchSummaryList()) {
				count++;
				String productName = tradeProductSummary.getProductName() + " (" + productBatchSummary.getBatchName() + ")";
				switch (purchaseItemHeaderState) {
					case WITH_DISCOUNT_AND_TAX:
						addRow(purchaseItemsTable, documentLayoutSetup,
								Arrays.asList(new CellData(String.valueOf(count), purchaseItemsCellLayoutSetup),
										new CellData(productName, purchaseItemsCellLayoutSetup),
										new CellData(String.valueOf((int) productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
										new CellData(formatDouble(productBatchSummary.getTotalPrice() / productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
										new CellData(formatDouble(productBatchSummary.getTotalPrice()), purchaseItemsCellLayoutSetup),
										new CellData(formatDouble(productBatchSummary.getTotalDiscount()), purchaseItemsCellLayoutSetup),
										new CellData(formatDouble(productBatchSummary.getTotalTax()), purchaseItemsCellLayoutSetup),
										new CellData(formatDouble(productBatchSummary.getTotalPrice() + productBatchSummary.getTotalTax() - productBatchSummary.getTotalDiscount()), purchaseItemsCellLayoutSetup)));
						break;
					case WITHOUT_DISCOUNT_AND_TAX:
						addRow(purchaseItemsTable, documentLayoutSetup,
								Arrays.asList(new CellData(String.valueOf(count), purchaseItemsCellLayoutSetup),
										new CellData(productName, purchaseItemsCellLayoutSetup),
										new CellData(String.valueOf((int) productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
										new CellData(formatDouble(productBatchSummary.getTotalPrice() / productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
										new CellData(formatDouble(productBatchSummary.getTotalPrice()), purchaseItemsCellLayoutSetup)));
						break;
					case WITH_TAX_ONLY:
						addRow(purchaseItemsTable, documentLayoutSetup,
								Arrays.asList(new CellData(String.valueOf(count), purchaseItemsCellLayoutSetup),
										new CellData(productName, purchaseItemsCellLayoutSetup),
										new CellData(String.valueOf((int) productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
										new CellData(formatDouble(productBatchSummary.getTotalPrice() / productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
										new CellData(formatDouble(productBatchSummary.getTotalPrice()), purchaseItemsCellLayoutSetup),
										new CellData(formatDouble(productBatchSummary.getTotalTax()), purchaseItemsCellLayoutSetup),
										new CellData(formatDouble(productBatchSummary.getTotalPrice() + productBatchSummary.getTotalTax() - productBatchSummary.getTotalDiscount()), purchaseItemsCellLayoutSetup)));
						break;
					case WITH_DISCOUNT_ONLY:
						addRow(purchaseItemsTable, documentLayoutSetup,
								Arrays.asList(new CellData(String.valueOf(count), purchaseItemsCellLayoutSetup),
										new CellData(productName, purchaseItemsCellLayoutSetup),
										new CellData(String.valueOf((int) productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
										new CellData(formatDouble(productBatchSummary.getTotalPrice() / productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
										new CellData(formatDouble(productBatchSummary.getTotalPrice()), purchaseItemsCellLayoutSetup),
										new CellData(formatDouble(productBatchSummary.getTotalDiscount()), purchaseItemsCellLayoutSetup),
										new CellData(formatDouble(productBatchSummary.getTotalPrice() + productBatchSummary.getTotalTax() - productBatchSummary.getTotalDiscount()), purchaseItemsCellLayoutSetup)));
						break;
					default:
						break;
				}
			}

		}

		document.add(purchaseItemsTable);
	}

	public void generateIssueItemsContent(Document document, DocumentLayoutSetup documentLayoutSetup,
										  float contentFontSize, InventoryTransactionSummary transactionSummary, float defaultBorderWidth)
			throws IOException {

		if (CollectionUtils.isEmpty(transactionSummary.getTradeProductSummaryList())) {
			return;
		}

		CellLayoutSetup itemCellLayoutSetup = new CellLayoutSetup();
		itemCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth))
				.setBorderRight(new SolidBorder(defaultBorderWidth));

		Table purchaseItemsTable = getPDFTable(documentLayoutSetup, DEFAULT_ISSUE_ITEMS_HEADER_WIDTH);
		CellLayoutSetup purchaseItemsHeaderCellLayoutSetup = new CellLayoutSetup();
		purchaseItemsHeaderCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.LEFT);

		CellLayoutSetup purchaseItemsCellLayoutSetup = new CellLayoutSetup();
		purchaseItemsCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.LEFT);

		addRow(purchaseItemsTable, documentLayoutSetup,
				Arrays.asList(new CellData("#", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Particulars", purchaseItemsHeaderCellLayoutSetup),
						new CellData("Qty", purchaseItemsHeaderCellLayoutSetup)));

		int count = 0;
		for (TradeProductSummary tradeProductSummary : transactionSummary.getTradeProductSummaryList()) {
			for (TradeProductBatchSummary productBatchSummary : tradeProductSummary.getTradeProductBatchSummaryList()) {
				count++;
				String productName = tradeProductSummary.getProductName() + " (" + productBatchSummary.getBatchName() + ")";
				addRow(purchaseItemsTable, documentLayoutSetup,
						Arrays.asList(new CellData(String.valueOf(count), purchaseItemsCellLayoutSetup),
								new CellData(productName, purchaseItemsCellLayoutSetup),
								new CellData(String.valueOf((int) productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup)));
			}
		}

		document.add(purchaseItemsTable);
	}

	public void generatePaymentSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
									   float contentFontSize, InventoryTransactionSummary transactionSummary, float defaultBorderWidth)
			throws IOException {
		InventoryUserType userType = transactionSummary.getInventoryUserType();
		int singleContentColumn = 2;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup keyCellLayoutSetup = new CellLayoutSetup();
		keyCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth));

		CellLayoutSetup valueCellLayoutSetup = keyCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT)
				.setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderLeft(null);

		CellLayoutSetup keyBoldCellLayoutSetup = keyCellLayoutSetup.copy().setPdfFont(getRegularBoldFont());
		CellLayoutSetup valueBoldCellLayoutSetup = valueCellLayoutSetup.copy().setPdfFont(getRegularBoldFont());

		int totalUnits = 0;
		double totalPrice = 0;
		double totalDiscount = 0;
		double totalTax = 0;
		for (TradeProductSummary tradeProductSummary : transactionSummary.getTradeProductSummaryList()) {
			for (TradeProductBatchSummary productBatchSummary : tradeProductSummary.getTradeProductBatchSummaryList()) {
				totalUnits += productBatchSummary.getQuantity();
				totalPrice += productBatchSummary.getTotalPrice();
				totalDiscount += productBatchSummary.getTotalDiscount();
				totalTax += productBatchSummary.getTotalTax();
			}
		}

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Total Units:", keyCellLayoutSetup),
				new CellData(String.valueOf(totalUnits), valueCellLayoutSetup)));

		if (transactionSummary.getTransactionType() == InventoryTransactionType.ISSUE) {
			document.add(table);
			addRemarkSection(document, documentLayoutSetup, contentFontSize, transactionSummary, defaultBorderWidth);
			return;
		}

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData("Sub Total:", keyCellLayoutSetup), new CellData(
						formatToRupees(totalPrice), valueCellLayoutSetup)));
		if (Double.compare(totalDiscount, 0d) > 0) {
			addRow(table, documentLayoutSetup,
					Arrays.asList(new CellData("Total Discount:", keyCellLayoutSetup), new CellData(
							formatToRupees(totalDiscount), valueCellLayoutSetup)));
		}

		if (Double.compare(totalTax, 0d) > 0) {
			addRow(table, documentLayoutSetup,
					Arrays.asList(new CellData("Total Tax:", keyCellLayoutSetup), new CellData(
							formatToRupees(totalTax), valueCellLayoutSetup)));
		}


		if (Double.compare(transactionSummary.getAdditionalCost(), 0d) > 0) {
			addRow(table, documentLayoutSetup,
					Arrays.asList(new CellData("Additional Cost:", keyCellLayoutSetup), new CellData(
							formatToRupees(transactionSummary.getAdditionalCost()), valueCellLayoutSetup)));
		}

		if (Double.compare(transactionSummary.getAdditionalDiscount(), 0d) > 0) {
			addRow(table, documentLayoutSetup,
					Arrays.asList(new CellData("Additional Discount:", keyCellLayoutSetup), new CellData(
							formatToRupees(transactionSummary.getAdditionalDiscount()), valueCellLayoutSetup)));
		}

		double netAdditionalAmount = subtractValues(transactionSummary.getAdditionalCost(), transactionSummary.getAdditionalDiscount());
		double netAmount = addValues(subtractValues(addValues(totalPrice, totalTax), totalDiscount), netAdditionalAmount);
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData("Net Amount:", keyBoldCellLayoutSetup),
						new CellData(formatToRupees(netAmount),
								valueBoldCellLayoutSetup)));

		if (transactionSummary.getTransactionType() == InventoryTransactionType.SALES_RETURN) {
			if (userType == InventoryUserType.STUDENT) {
				if (transactionSummary.getWalletCreditAmount() != null && transactionSummary.getWalletCreditAmount() > 0) {
					addRow(table, documentLayoutSetup,
							Arrays.asList(new CellData("Refund To Wallet:", keyBoldCellLayoutSetup), new CellData(
									formatToRupees(transactionSummary.getWalletCreditAmount()), valueBoldCellLayoutSetup)));
				}
			}
			if (lteZeroOrNull(transactionSummary.getPaidAmount())) {
				addRow(table, documentLayoutSetup,
						Arrays.asList(new CellData("Refund (" + transactionSummary.getTransactionMode().getDisplayName() + "):", keyBoldCellLayoutSetup),
								new CellData(formatToRupees(transactionSummary.getPaidAmount()),
										valueBoldCellLayoutSetup)));
			}
		} else {
			if (userType == InventoryUserType.STUDENT) {
				if (transactionSummary.getUsedWalletAmount() != null && transactionSummary.getUsedWalletAmount() > 0) {
					addRow(table, documentLayoutSetup,
							Arrays.asList(new CellData("Paid From Wallet:", keyBoldCellLayoutSetup), new CellData(
									formatToRupees(transactionSummary.getUsedWalletAmount()), valueBoldCellLayoutSetup)));
				}
				if (transactionSummary.getWalletCreditAmount() != null && transactionSummary.getWalletCreditAmount() > 0) {
					addRow(table, documentLayoutSetup,
							Arrays.asList(new CellData("Credit Amount:", keyBoldCellLayoutSetup), new CellData(
									formatToRupees(transactionSummary.getWalletCreditAmount()), valueBoldCellLayoutSetup)));
				}
			}

			addRow(table, documentLayoutSetup,
					Arrays.asList(new CellData("Paid (" + transactionSummary.getTransactionMode().getDisplayName() + "):", keyBoldCellLayoutSetup),
							new CellData(formatToRupees(transactionSummary.getPaidAmount()),
									valueBoldCellLayoutSetup)));
		}

		document.add(table);

		addRemarkSection(document, documentLayoutSetup, contentFontSize, transactionSummary, defaultBorderWidth);

	}

	private void addRemarkSection(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize, InventoryTransactionSummary transactionSummary, float defaultBorderWidth) throws IOException {
		int singleContentColumn;
		Table table;
		singleContentColumn = 1;
		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		Paragraph remarks = getKeyValueParagraph("Remarks: ",
				transactionSummary.getDescription());

		CellLayoutSetup remarksCellLayoutSetup = new CellLayoutSetup();
		remarksCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(defaultBorderWidth));


		if (StringUtils.isNotBlank(transactionSummary.getReference())) {
			CellLayoutSetup refCellLayoutSetup = new CellLayoutSetup();
			refCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
					.setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(defaultBorderWidth)).setBorderBottom(null);
			addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("Reference: ",
					transactionSummary.getReference())), refCellLayoutSetup);

			remarksCellLayoutSetup.setBorderTop(null);
		}
		addRow(table, documentLayoutSetup, Arrays.asList(remarks), remarksCellLayoutSetup);
		document.add(table);
	}

	enum PurchaseItemHeaderState {
		WITH_DISCOUNT_AND_TAX,
		WITHOUT_DISCOUNT_AND_TAX,
		WITH_DISCOUNT_ONLY,
		WITH_TAX_ONLY;
	}
}
