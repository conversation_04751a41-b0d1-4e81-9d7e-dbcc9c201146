package com.embrate.cloud.pdf.inventory.store.v2;

import com.embrate.cloud.core.api.inventory.v2.transaction.InventoryTransactionSummary;
import com.embrate.cloud.core.lib.inventory.v2.InventoryTransactionsManager;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.inventory.TransactionSummary;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.inventory.ProductTransactionsManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class StoreInventoryPDFInvoiceHandler {

	private static final Logger logger = LogManager.getLogger(StoreInventoryPDFInvoiceHandler.class);

	private final StoreInventoryInvoiceGeneratorFactory storeInventoryInvoiceGeneratorFactoryV2;
	private final InventoryTransactionsManager inventoryTransactionsManager;
	private final InstituteManager instituteManager;
	private final UserPreferenceSettings userPreferenceSettings;
	private final UserPermissionManager userPermissionManager;

	public StoreInventoryPDFInvoiceHandler(InventoryTransactionsManager inventoryTransactionsManager, InstituteManager instituteManager, UserPreferenceSettings userPreferenceSettings, UserPermissionManager userPermissionManager, AssetProvider assetProvider) {
		this.storeInventoryInvoiceGeneratorFactoryV2 = new StoreInventoryInvoiceGeneratorFactory(assetProvider);
		this.inventoryTransactionsManager = inventoryTransactionsManager;
		this.instituteManager = instituteManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.userPermissionManager = userPermissionManager;
	}

	public DocumentOutput generateInvoice(int instituteId,
													UUID transactionId, boolean storeCopy, UUID userId) {

//		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.DEMAND_NOTICE);

		if(transactionId == null || instituteId <= 0){
			logger.error("Invalid payload for generating invoice {} , instituteId {}", transactionId, instituteId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Invalid invoice request"));
		}

		InventoryTransactionSummary transactionSummary = inventoryTransactionsManager.getTransactionDetails(instituteId, transactionId);
		if (transactionSummary == null) {
			logger.error("No transaction found for invoice {} , instituteId {}", transactionId, instituteId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Invalid invoice request"));

		}

		StoreInventoryInvoiceGenerator storeInventoryInvoiceGenerator = storeInventoryInvoiceGeneratorFactoryV2.getStoreInventoryInvoiceGenerator(instituteId);
		Institute institute = instituteManager.getInstitute(instituteId);
		return storeInventoryInvoiceGenerator.generateInvoice(institute, transactionSummary, transactionSummary.getTransactionId().toString() + ".pdf", storeCopy);
	}

}
