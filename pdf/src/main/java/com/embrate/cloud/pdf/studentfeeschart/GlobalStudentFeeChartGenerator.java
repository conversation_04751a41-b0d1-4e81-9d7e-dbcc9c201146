package com.embrate.cloud.pdf.studentfeeschart;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.layout.LayoutArea;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.StudentFeePaymentData;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

public class GlobalStudentFeeChartGenerator extends StudentFeesChartGenerator {

    public GlobalStudentFeeChartGenerator(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    public static final float DEFAULT_PAGE_SIDE_MARGIN = 30f;
    public static final float DEFAULT_PAGE_TOP_MARGIN = 20f;
    public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 20f;
    public static final float LOGO_WIDTH = 75f;
    public static final float LOGO_HEIGHT = 75f;
    public static final float DEFAULT_TABLE_BORDER_WIDTH = 0.5f;
    public static final TimeZone DFAULT_TIMEZONE = DateUtils.DEFAULT_TIMEZONE;
    private static final Logger logger = LogManager.getLogger(GlobalStudentFeeChartGenerator.class);

    @Override

    public DocumentOutput generateStudentFeeChart(Institute institute, AcademicSession academicSession, StudentManager studentManager,
                                                  Map<UUID, List<StudentFeePaymentData>> studentFeePaymentDataMap, int studentPerPageValue,
                                                  boolean includePaidAmount, boolean includeDiscountedAmount, boolean includeDueAmount, boolean includeDueAmountTillToday) {


        try {

            String documentName = "FEE CHART";
            PdfFont cambriaBoldFont = getCambriaBoldFont();
            PdfFont cambriaFont = getCambriaFont();
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4, DEFAULT_PAGE_TOP_MARGIN,
                    DEFAULT_PAGE_BOTTOM_MARGIN, DEFAULT_PAGE_SIDE_MARGIN);
            float contentFontSize = 12f;
            float defaultBorderWidth = DEFAULT_TABLE_BORDER_WIDTH;
            int instituteId = institute.getInstituteId();
            Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
            DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup,
                    null, null, contentFontSize, defaultBorderWidth,
                    LOGO_WIDTH, LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(instituteId),
                    null, null);

            int pageNumber = 1;
            int studentCount = 0;
            for (Map.Entry<UUID, List<StudentFeePaymentData>> studentFeePaymentDataList : studentFeePaymentDataMap.entrySet()) {
                UUID studentId = studentFeePaymentDataList.getKey();
                List<StudentFeePaymentData> studentFeePaymentData = studentFeePaymentDataList.getValue();
                generateStudentFeeCharts(document, documentLayoutSetup, documentLayoutData, studentFeePaymentData, institute, academicSession, studentId, studentManager,
                        cambriaBoldFont, cambriaFont, contentFontSize, includePaidAmount, includeDiscountedAmount, includeDueAmount, includeDueAmountTillToday, pageNumber);

                studentCount++;
                if (studentCount < studentFeePaymentDataMap.size() && (studentCount % studentPerPageValue) == 0) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                    pageNumber++;
                } else {
                    LayoutArea currentArea = document.getRenderer().getCurrentArea();
                    Rectangle rectangle = currentArea.getBBox();
                    while (rectangle.getHeight() > ((documentLayoutSetup.getPageSize().getHeight() / 2) - 20)) {
                        addBlankLine(document, false, 1);
                        currentArea = document.getRenderer().getCurrentArea();
                        rectangle = currentArea.getBBox();
                    }
                }
            }

            documentLayoutData.getDocument().close();

            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating fee charts", e);
        }


        return null;

    }

    private void generateStudentFeeCharts(Document document, DocumentLayoutSetup documentLayoutSetup, DocumentLayoutData documentLayoutData,
                                          List<StudentFeePaymentData> studentFeePaymentData, Institute institute, AcademicSession academicSession, UUID studentId, StudentManager studentManager,
                                          PdfFont cambriaBoldFont, PdfFont cambriaFont, float contentFontSize, boolean includePaidAmount, boolean includeDiscountedAmount,
                                          boolean includeDueAmount, boolean includeDueAmountTillToday, int pageNumber) throws IOException {

        /**
         * Watermark
         */
        // float bgImageHeightWidth = 400f;
        int instituteId = institute.getInstituteId();
        // generateBackgroundImage(document, documentLayoutSetup, WatermarkProvider.INSTANCE.getWatermark(instituteId),
        // 		bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 + 20, bgImageHeightWidth / 2);


        // byte[] image = getStudentImage(instituteId, studentId, studentManager);
        // if (image != null) {
        // 	generateImage(document, documentLayoutSetup, image, 80, 90,
        // 			documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 120f,
        // 			documentLayoutSetup.getPageSize().getHeight() - LOGO_HEIGHT - 25f);
        // }

        /**
         * Header
         */
        generateHeader(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                documentLayoutSetup, institute);

        /**
         * Student Details
         */
        generateStudentDetails(document, documentLayoutData, documentLayoutSetup, cambriaFont, cambriaBoldFont, studentFeePaymentData.get(0), academicSession, contentFontSize);

        /**
         * Feesheet Table
         */
        generateFeesheetTable(document, documentLayoutSetup, documentLayoutData, cambriaFont, cambriaBoldFont, institute,
                studentFeePaymentData, includePaidAmount, includeDiscountedAmount, includeDueAmount, includeDueAmountTillToday, pageNumber);

        /**
         * Signature Details
         */
        generateSignatureDetails(document, documentLayoutSetup, documentLayoutData, cambriaBoldFont, institute, contentFontSize,
                "Class Teacher's Signature", "Principal's Signature");

    }

    protected void generateBackgroundImage(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
                                           float height, float offsetX, float offsetY) {
        try {
            generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
        } catch (Exception e) {
            logger.error("Exception while adding background image", e);
        }

    }

    protected void generateHeader(Document document, DocumentLayoutData documentLayoutData,
                                  PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
                                  Institute institute) throws IOException {

        // generateLogo(document, documentLayoutSetup, documentLayoutData, institute, documentLayoutData.getLogo(), 30f,
        // 		documentLayoutSetup.getPageSize().getHeight() - LOGO_HEIGHT - 25f);

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setTextAlignment(TextAlignment.CENTER).setPaddingLeft(25f);

        float instituteNameFontSize = 17f;
        List<Integer> instituteNameFontColor = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase(), instituteNameFontColor)),
                cellLayoutSetup.copy().setFontSize(instituteNameFontSize).setPdfFont(cambriaBoldFont));

        List<Integer> letterHead1FontColor = EColorUtils.hex2Rgb(EColorUtils.themeColorHexCode);
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1(), letterHead1FontColor)),
                cellLayoutSetup.copy().setFontSize(documentLayoutData.getContentFontSize() - 2).setPdfFont(cambriaFont));

        List<Integer> letterHead2FontColor = EColorUtils.hex2Rgb(EColorUtils.themeColorHexCode);
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2(), letterHead2FontColor)),
                cellLayoutSetup.copy().setFontSize(documentLayoutData.getContentFontSize() - 2).setPdfFont(cambriaFont));

        documentLayoutData.getDocument().add(table);
    }

    protected void generateStudentDetails(Document document, DocumentLayoutData documentLayoutData,
                                          DocumentLayoutSetup documentLayoutSetup, PdfFont cambriaFont,
                                          PdfFont cambriaBoldFont, StudentFeePaymentData studentFeePaymentData, AcademicSession academicSession,
                                          float contentFontSize) {

        Table table = getPDFTable(documentLayoutSetup, 1);

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.CENTER);
        addRow(table, documentLayoutSetup,
                Arrays.asList(getKeyValueParagraph("Student Fee Chart (Session : ", academicSession.getShortYearDisplayName() + ")",
                        cambriaBoldFont, cambriaBoldFont).setUnderline()),
                singleCellLayoutSetup);

        document.add(table);

        table = getPDFTable(documentLayoutSetup, new float[]{0.48f, 0.04f, 0.48f});

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(contentFontSize);

        CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
        CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
        CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);

        List<Integer> keyFontColor = EColorUtils.hex2Rgb("#434343");
        List<Integer> valueFontColor = EColorUtils.hex2Rgb(EColorUtils.themeColorHexCode);

        Paragraph studentName = getKeyValueParagraph("Student's Name : ", studentFeePaymentData.getStudentFullName(),
                keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont);
        Paragraph fatherName = getKeyValueParagraph("Father's Name : ", StringUtils.isEmpty(studentFeePaymentData.getFatherName()) ?
                        EMPTY_TEXT : studentFeePaymentData.getFatherName(),
                keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont);
        Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ", studentFeePaymentData.getAdmissionNumber(),
                keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont);
        Paragraph className = getKeyValueParagraph("Class : ", studentFeePaymentData.getStandardName(), keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont);

        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
                        new CellData(admissionNumber, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(fatherName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
                        new CellData(className, thirdCellLayoutSetup)));

        document.add(table);
    }

    protected void generateFeesheetTable(Document document, DocumentLayoutSetup documentLayoutSetup,
                                         DocumentLayoutData documentLayoutData, PdfFont cambriaFont,
                                         PdfFont cambriaBoldFont, Institute institute,
                                         List<StudentFeePaymentData> studentFeePaymentData,
                                         boolean includePaidAmount, boolean includeDiscountedAmount, boolean includeDueAmount,
                                         boolean includeDueAmountTillToday,
                                         int pageNumber) {

        float contentFontSize = documentLayoutData.getContentFontSize();

        int singleCopyColoumCount = 3;

        List<Integer> tableHeaderFontColor = EColorUtils.hex2Rgb(EColorUtils.WHITE_COLOR_HEX_CODE);
        Paragraph subjectText = getParagraph("Fee Name", tableHeaderFontColor, cambriaBoldFont);
        Paragraph dueDateText = getParagraph("Due Date", tableHeaderFontColor, cambriaBoldFont);
        Paragraph assignedAmountText = getParagraph("Assigned", tableHeaderFontColor, cambriaBoldFont);
        Paragraph paidAmountText = null, givenDiscountText = null, dueAmountText = null, dueAmountTillTodayText = null;
        if (includePaidAmount) {
            paidAmountText = getParagraph("Collected", tableHeaderFontColor, cambriaBoldFont);
            singleCopyColoumCount++;
        }
        if (includeDiscountedAmount) {
            givenDiscountText = getParagraph("Discount", tableHeaderFontColor, cambriaBoldFont);
            singleCopyColoumCount++;
        }
        if (includeDueAmount) {
            dueAmountText = getParagraph("Due", tableHeaderFontColor, cambriaBoldFont);
            singleCopyColoumCount++;
        }
        if (includeDueAmountTillToday) {
            dueAmountTillTodayText = getParagraph("Due (Till Today)", tableHeaderFontColor, cambriaBoldFont);
            singleCopyColoumCount++;
        }

        Table table = getPDFTable(documentLayoutSetup, singleCopyColoumCount);
        Border tableBorder = new SolidBorder(Color.convertRgbToCmyk(
                new DeviceRgb(158, 158, 158)), DEFAULT_TABLE_BORDER_WIDTH);
        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(contentFontSize).setPaddingLeft(10f);
        CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy()
                .setTextAlignment(TextAlignment.CENTER).setPdfFont(cambriaFont).setBorder(tableBorder);
        CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy()
                .setTextAlignment(TextAlignment.CENTER).setPdfFont(cambriaFont).setBorder(tableBorder);
        CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy()
                .setTextAlignment(TextAlignment.CENTER).setPdfFont(cambriaFont).setBorder(tableBorder);
        String tableHeaderBackgroundColor = EColorUtils.themeColorHexCode;

        List<CellData> cellDataList = new ArrayList<>();

        cellDataList.add(new CellData(subjectText, firstCellLayoutSetup.copy().setBackgroundColor(tableHeaderBackgroundColor)));
        cellDataList.add(new CellData(dueDateText, secondCellLayoutSetup.copy().setBackgroundColor(tableHeaderBackgroundColor)));
        cellDataList.add(new CellData(assignedAmountText, thirdCellLayoutSetup.copy().setBackgroundColor(tableHeaderBackgroundColor)));
        if (includePaidAmount) {
            cellDataList.add(new CellData(paidAmountText, firstCellLayoutSetup.copy().setBackgroundColor(tableHeaderBackgroundColor)));
        }
        if (includeDiscountedAmount) {
            cellDataList.add(new CellData(givenDiscountText, firstCellLayoutSetup.copy().setBackgroundColor(tableHeaderBackgroundColor)));
        }
        if (includeDueAmount) {
            cellDataList.add(new CellData(dueAmountText, firstCellLayoutSetup.copy().setBackgroundColor(tableHeaderBackgroundColor)));
        }
        if (includeDueAmountTillToday) {
            cellDataList.add(new CellData(dueAmountTillTodayText, firstCellLayoutSetup.copy().setBackgroundColor(tableHeaderBackgroundColor)));
        }

        addRow(table, documentLayoutSetup, cellDataList);
        document.add(table);
        table = getPDFTable(documentLayoutSetup, singleCopyColoumCount);

        List<Integer> tableContentFontColor = EColorUtils.hex2Rgb(EColorUtils.BLACK_COLOR_HEX_CODE);

        double totalAssignedAmount = 0;
        double totalPaidAmount = 0;
        double totalDiscountAmount = 0;
        double totalDueAmount = 0;
        double totalDueAmountTillToday = 0;
        CellLayoutSetup amountCellLayoutSetup = firstCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER);
        for (StudentFeePaymentData studentFeeData : studentFeePaymentData) {
            if (studentFeeData == null) {
                continue;
            }
            List<CellData> cellDataContentList = new ArrayList<>();
            String feeName = studentFeeData.getFeeName();
            String dueDate = DateUtils.getFormattedDate(studentFeeData.getDueDate());
            double assignedAmount = studentFeeData.getAssignedAmount();
            totalAssignedAmount += assignedAmount;
            Paragraph feeNameVal = getParagraph(feeName, tableContentFontColor, cambriaFont);
            Paragraph dueDateVal = getParagraph(dueDate, tableContentFontColor, cambriaFont);
            Paragraph assignedAmountVal = getParagraph(NumberUtils.formatDouble(assignedAmount), tableContentFontColor, cambriaFont);
            cellDataContentList.add(new CellData(feeNameVal, firstCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)));
            cellDataContentList.add(new CellData(dueDateVal, secondCellLayoutSetup));
            cellDataContentList.add(new CellData(assignedAmountVal, amountCellLayoutSetup));
            if (includePaidAmount) {
                double paidAmount = studentFeeData.getAmountCollected();
                totalPaidAmount += paidAmount;
                Paragraph paidAmountVal = getParagraph(NumberUtils.formatDouble(paidAmount), tableContentFontColor, cambriaFont);
                cellDataContentList.add(new CellData(paidAmountVal, amountCellLayoutSetup));
            }
            if (includeDiscountedAmount) {
                double discountedAmount = studentFeeData.getGivenDiscount() + studentFeeData.getRemainingDiscountToBeGiven();
                totalDiscountAmount += discountedAmount;
                Paragraph discountedAmountVal = getParagraph(NumberUtils.formatDouble(discountedAmount), tableContentFontColor, cambriaFont);
                cellDataContentList.add(new CellData(discountedAmountVal, amountCellLayoutSetup));
            }
            if (includeDueAmount) {
                double dueAmount = studentFeeData.getDueAmount();
                totalDueAmount += dueAmount;
                Paragraph dueAmountVal = getParagraph(NumberUtils.formatDouble(dueAmount), tableContentFontColor, cambriaFont);
                cellDataContentList.add(new CellData(dueAmountVal, amountCellLayoutSetup));
            }
            if (includeDueAmountTillToday) {
                double dueAmountTillToday = studentFeeData.getDueAmountTillToday();
                totalDueAmountTillToday += dueAmountTillToday;
                Paragraph dueAmountTillTodayVal = getParagraph(NumberUtils.formatDouble(dueAmountTillToday), tableContentFontColor, cambriaFont);
                cellDataContentList.add(new CellData(dueAmountTillTodayVal, amountCellLayoutSetup));
            }
            addRow(table, documentLayoutSetup, cellDataContentList);

        }

        List<CellData> totalCellDataContentList = new ArrayList<>();
        Paragraph feeNameVal = getParagraph("Total", tableContentFontColor, cambriaBoldFont);
        Paragraph totalAssignedAmountVal = getParagraph(NumberUtils.formatDouble(totalAssignedAmount), tableContentFontColor, cambriaBoldFont);
        totalCellDataContentList.add(new CellData(feeNameVal, firstCellLayoutSetup, 1, 2));
        totalCellDataContentList.add(new CellData(totalAssignedAmountVal, amountCellLayoutSetup));
        if (includePaidAmount) {
            Paragraph totalPaidAmountVal = getParagraph(NumberUtils.formatDouble(totalPaidAmount), tableContentFontColor, cambriaBoldFont);
            totalCellDataContentList.add(new CellData(totalPaidAmountVal, amountCellLayoutSetup));
        }
        if (includeDiscountedAmount) {
            Paragraph totalDiscountedAmountVal = getParagraph(NumberUtils.formatDouble(totalDiscountAmount), tableContentFontColor, cambriaBoldFont);
            totalCellDataContentList.add(new CellData(totalDiscountedAmountVal, amountCellLayoutSetup));
        }
        if (includeDueAmount) {
            Paragraph totalDueAmountVal = getParagraph(NumberUtils.formatDouble(totalDueAmount), tableContentFontColor, cambriaBoldFont);
            totalCellDataContentList.add(new CellData(totalDueAmountVal, amountCellLayoutSetup));
        }
        if (includeDueAmountTillToday) {
            Paragraph totalDueAmountTillTodayVal = getParagraph(NumberUtils.formatDouble(totalDueAmountTillToday), tableContentFontColor, cambriaBoldFont);
            totalCellDataContentList.add(new CellData(totalDueAmountTillTodayVal, amountCellLayoutSetup));
        }
        addRow(table, documentLayoutSetup, totalCellDataContentList);

        document.add(table);
        addBlankLine(document, true, 2);
    }

    protected void generateSignatureDetails(Document document, DocumentLayoutSetup documentLayoutSetup,
                                            DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont,
                                            Institute institute, float contentFontSize,
                                            String leftSideSignatureText,
                                            String rightSideSignatureText) throws IOException {

        Table table = getPDFTable(documentLayoutSetup, new float[]{0.4f, 0.2f, 0.4f});

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(contentFontSize);

        CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER);
        CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER);
        CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER);

        List<Integer> signatureFontColor = EColorUtils.hex2Rgb("#474747");
        Paragraph classTeacherSignatureText = getParagraph(leftSideSignatureText, signatureFontColor, cambriaBoldFont);
        Paragraph principalSignatureText = getParagraph(rightSideSignatureText, signatureFontColor, cambriaBoldFont);

        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(classTeacherSignatureText, firstCellLayoutSetup),
                        new CellData(EMPTY_TEXT, secondCellLayoutSetup),
                        new CellData(principalSignatureText, thirdCellLayoutSetup)));
        document.add(table);
        addBlankLine(document, true, 1);
    }

    // protected void generateLogo(Document document, DocumentLayoutSetup documentLayoutSetup, DocumentLayoutData documentLayoutData, Institute institute, byte[] logo,
    // 							float offsetX, float offsetY) throws MalformedURLException, IOException {

    // 	generateImage(document, documentLayoutSetup, logo,
    // 			documentLayoutData.getLogoWidth(), documentLayoutData.getLogoHeight(), offsetX, offsetY);
    // }

}
