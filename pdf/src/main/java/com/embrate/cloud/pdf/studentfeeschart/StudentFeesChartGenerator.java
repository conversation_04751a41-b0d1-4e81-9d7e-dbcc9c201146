package com.embrate.cloud.pdf.studentfeeschart;

import java.util.*;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.StudentFeePaymentData;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.pdf.base.PDFGenerator;

public abstract class StudentFeesChartGenerator  extends PDFGenerator{
    public StudentFeesChartGenerator(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    public abstract DocumentOutput generateStudentFeeChart(Institute institute,AcademicSession academicSession, StudentManager studentManager, 
                    Map<UUID, List<StudentFeePaymentData>> studentFeePaymentDataMap,  int studentPerPageValue, 
                    boolean includeCollectedAmount, boolean includeDiscountedAmount, boolean includeDueAmount, boolean includeDueAmountTillToday);
    
}
