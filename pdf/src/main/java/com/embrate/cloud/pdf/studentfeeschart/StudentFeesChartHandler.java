package com.embrate.cloud.pdf.studentfeeschart;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.lib.fees.payment.FeePaymentInsightManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fees.payment.StudentFeePaymentData;
import com.lernen.cloud.core.api.institute.AcademicSession;

import java.util.*;


public class StudentFeesChartHandler {
    private static final Logger logger = LogManager.getLogger(StudentFeesChartHandler.class);

    private final FeePaymentInsightManager feePaymentInsightManager;
    private final StudentFeesChartGeneratorFactory studentFeesChartGeneratorFactory;
    private final StudentManager studentManager;
    private final InstituteManager instituteManager;
    private final AssetProvider assetProvider;

    public StudentFeesChartHandler(FeePaymentInsightManager feePaymentInsightManager, StudentManager studentManager, InstituteManager instituteManager, AssetProvider assetProvider) {
            this.feePaymentInsightManager = feePaymentInsightManager;
            this.studentFeesChartGeneratorFactory = new StudentFeesChartGeneratorFactory();
            this.studentManager = studentManager;
            this.instituteManager = instituteManager;
            this.assetProvider = assetProvider;
    }

    public DocumentOutput generateStudentFeesChart(int instituteId, int academicSessionId, Set<UUID> studentUUIDs, int studentPerPageValue,
            boolean includeCollectedAmount,boolean includeDueAmount, boolean includeDiscountedAmount, boolean includeDueAmountTillToday) {

        if (instituteId <= 0 || academicSessionId <= 0) {
            logger.error("Invalid institute id or academic session id");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid institute id or academic session id"));
        }
        
        final List<StudentFeePaymentData> studentFeePaymentDataList = feePaymentInsightManager.getStudentFeePaymentsData(instituteId, academicSessionId, null, null, null,studentUUIDs);

        if(CollectionUtils.isEmpty(studentFeePaymentDataList)) {
            logger.error("Invalid Details studentFeePaymentDataList {} ", studentFeePaymentDataList);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE__STRUCTURE_CONFIGURATION, "Can not generate Fee Chart As No Fee is Assigned to the Student"));
        }

        StudentFeesChartGenerator studentFeesChartGenerator = studentFeesChartGeneratorFactory.getStudentFeesChartGenerator(instituteId);

        final Map<UUID, List<StudentFeePaymentData>> studentFeePaymentDataMap = getStudentIdFeesDetailsMap(studentFeePaymentDataList);
        AcademicSession academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
        return studentFeesChartGenerator.generateStudentFeeChart(instituteManager.getInstitute(instituteId),academicSession, studentManager, studentFeePaymentDataMap, studentPerPageValue, includeCollectedAmount, includeDiscountedAmount, includeDueAmount, includeDueAmountTillToday);
    }

    private Map<UUID, List<StudentFeePaymentData>> getStudentIdFeesDetailsMap(final List<StudentFeePaymentData> studentFeePaymentDataList) {

		final Map<UUID, List<StudentFeePaymentData>> studentFeePaymentDataMap = new LinkedHashMap<>();
		for (final StudentFeePaymentData studentFeePaymentData : studentFeePaymentDataList) {
			final UUID studentId = studentFeePaymentData.getStudentId();
			if (!studentFeePaymentDataMap.containsKey(studentId)) {
				studentFeePaymentDataMap.put(studentId, new ArrayList<>());
			}
			studentFeePaymentDataMap.get(studentId).add(studentFeePaymentData);
		}
		return studentFeePaymentDataMap;
	}

}
