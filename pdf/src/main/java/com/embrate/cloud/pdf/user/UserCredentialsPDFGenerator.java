package com.embrate.cloud.pdf.user;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.api.onboarding.institute.setup.UserCredentials;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.base.PDFGenerator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

public class UserCredentialsPDFGenerator extends PDFGenerator {
	public static final float LOGO_WIDTH = 70f;
	public static final float LOGO_HEIGHT = 70f;
	private static final Logger logger = LogManager.getLogger(UserCredentialsPDFGenerator.class);
	private final StudentManager studentManager;
	private final InstituteManager instituteManager;
	private final StaffManager staffManager;
	private final UserManager userManager;
	private final AssetProvider assetProvider;

	public UserCredentialsPDFGenerator(StudentManager studentManager, InstituteManager instituteManager, StaffManager staffManager, UserManager userManager, AssetProvider assetProvider) {
		super(assetProvider);
		this.studentManager = studentManager;
		this.instituteManager = instituteManager;
		this.staffManager = staffManager;
		this.userManager = userManager;
		this.assetProvider = assetProvider;
	}

	public DocumentOutput generateUserCredentialsPDF(int instituteId, int academicSessionId, UUID userId, String documentName, UserType userType) {
		if (!(userType.equals(UserType.STUDENT)) && !(userType.equals(UserType.STAFF))) {
			logger.error("Invalid userType detected institute {}: ", instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER_TYPE, "User type can only be either STUDENT or STAFF."));
		}

		final Institute institute = instituteManager.getInstitute(instituteId);
		if (institute == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to get institute details"));
		}
		HashMap<UUID, UserCredentials> userCredentialMap = userManager.generateUserCredentialsPdf(instituteId, academicSessionId, userId, userType);

		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutData documentLayoutData = generateUserCredentialsLayoutData(institute, documentOutput, assetProvider);
			Document document = documentLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
			PdfFont boldFont = getCambriaBoldFont();
			PdfFont regularFont = getCambriaFont();
			float contentFontSize = documentLayoutData.getContentFontSize();

			generateDynamicImageProvider(documentLayoutData, 0, 20, instituteId, InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
			generateHeader(document, documentLayoutSetup, contentFontSize, boldFont, regularFont, institute);
			generateContent(document, documentLayoutSetup, contentFontSize, userCredentialMap, boldFont, regularFont, instituteId, academicSessionId, userType);
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating user credentials PDF for institute {}: ", instituteId, e);
		}
		return null;
	}

	protected DocumentLayoutData generateUserCredentialsLayoutData(Institute institute, DocumentOutput documentOutput,
																   AssetProvider assetProvider) throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false, PageSize.A4.rotate(),
				DEFAULT_PAGE_TOP_MARGIN, DEFAULT_PAGE_BOTTOM_MARGIN, 30f, 0f);

		float contentFontSize = 12f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

		//TODO: NEED TO ADD ASSET PROVIDER SUPPORT HERE
		DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(), getRegularBoldFont(),
				contentFontSize, defaultBorderWidth, LOGO_WIDTH, LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), null, null);
		return documentLayoutData;
	}

	protected void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								  PdfFont boldFont, PdfFont regularFont, Institute institute) throws IOException {
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setTextAlignment(TextAlignment.CENTER).setFontSize(contentFontSize);

		Table table = getPDFTable(documentLayoutSetup, 1).setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Collections.singletonList(getParagraph(institute.getInstituteName())),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 4));

		cellLayoutSetup.setPdfFont(regularFont);

		addRow(table, documentLayoutSetup, Collections.singletonList(getParagraph(institute.getLetterHeadLine1())),
				cellLayoutSetup.copy().setFontSize(contentFontSize - 1));
		addRow(table, documentLayoutSetup, Collections.singletonList(getParagraph(institute.getLetterHeadLine2())),
				cellLayoutSetup.setFontSize(contentFontSize - 1));
		document.add(table);

		addBlankLine(document, true, 1);
	}

	protected void generateContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								   HashMap<UUID, UserCredentials> userCredentialMap, PdfFont boldFont, PdfFont regularFont, int instituteId, int academicSessionId, UserType userType) throws IOException {

		Table table = new Table(new float[]{50f, 100f, 150f, 150f, 50f, 150f, 150f});
		table.setWidth(100);

		if (userType.equals(UserType.STUDENT)) {
			table.addHeaderCell(new Cell().add(new Paragraph("SR#").setFont(boldFont).setFontSize(contentFontSize)));
			table.addHeaderCell(new Cell().add(new Paragraph("Admission Number").setFont(boldFont).setFontSize(contentFontSize)));
			table.addHeaderCell(new Cell().add(new Paragraph("Student Name").setFont(boldFont).setFontSize(contentFontSize)));
			table.addHeaderCell(new Cell().add(new Paragraph("Father Name").setFont(boldFont).setFontSize(contentFontSize)));
			table.addHeaderCell(new Cell().add(new Paragraph("Class").setFont(boldFont).setFontSize(contentFontSize)));
			table.addHeaderCell(new Cell().add(new Paragraph("Student Username").setFont(boldFont).setFontSize(contentFontSize)));
			table.addHeaderCell(new Cell().add(new Paragraph("Password").setFont(boldFont).setFontSize(contentFontSize)));

			int srNo = 1;
			List<UUID> studentIdsList = new ArrayList<>(userCredentialMap.keySet());
			List<Student> studentList = studentManager.getStudentByAcademicSessionStudentIds(instituteId, academicSessionId, studentIdsList, StudentStatus.ENROLLED);
			if (studentList == null) {
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to get student details"));
			}
			for (Student student : studentList) {
				UserCredentials credentials = userCredentialMap.get(student.getStudentId());

				table.addCell(new Cell().add(new Paragraph(String.valueOf(srNo++)).setFont(regularFont).setFontSize(contentFontSize)));
				table.addCell(new Cell().add(new Paragraph(student.getStudentBasicInfo().getAdmissionNumber()).setFont(regularFont).setFontSize(contentFontSize)));
				table.addCell(new Cell().add(new Paragraph(student.getStudentBasicInfo().getName()).setFont(regularFont).setFontSize(contentFontSize)));
				table.addCell(new Cell().add(new Paragraph(student.getStudentFamilyInfo().getFathersName() == null ? EMPTY_TEXT : student.getStudentFamilyInfo().getFathersName()).setFont(regularFont).setFontSize(contentFontSize)));
				table.addCell(new Cell().add(new Paragraph(student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayName()).setFont(regularFont).setFontSize(contentFontSize)));
				table.addCell(new Cell().add(new Paragraph(credentials.getUserName() == null ? EMPTY_TEXT : credentials.getUserName()).setFont(regularFont).setFontSize(contentFontSize)));
				table.addCell(new Cell().add(new Paragraph(credentials.getPassword() == null ? EMPTY_TEXT : credentials.getPassword()).setFont(regularFont).setFontSize(contentFontSize)));
			}
		} else if (userType.equals(UserType.STAFF)) {
			table.addHeaderCell(new Cell().add(new Paragraph("SR#").setFont(boldFont).setFontSize(contentFontSize)));
			table.addHeaderCell(new Cell().add(new Paragraph("Enrollment Number").setFont(boldFont).setFontSize(contentFontSize)));
			table.addHeaderCell(new Cell().add(new Paragraph("Staff Name").setFont(boldFont).setFontSize(contentFontSize)));
			table.addHeaderCell(new Cell().add(new Paragraph("Primary Number").setFont(boldFont).setFontSize(contentFontSize)));
			table.addHeaderCell(new Cell().add(new Paragraph("Email").setFont(boldFont).setFontSize(contentFontSize)));
			table.addHeaderCell(new Cell().add(new Paragraph("Staff Username").setFont(boldFont).setFontSize(contentFontSize)));
			table.addHeaderCell(new Cell().add(new Paragraph("Password").setFont(boldFont).setFontSize(contentFontSize)));

			int srNo = 1;
			List<Staff> staffList = staffManager.getStaffs(instituteId, userCredentialMap.keySet());
			if (staffList == null) {
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to get staff details"));
			}
			for (Staff staff : staffList) {
				UserCredentials credentials = userCredentialMap.get(staff.getStaffId());

				table.addCell(new Cell().add(new Paragraph(String.valueOf(srNo++)).setFont(regularFont).setFontSize(contentFontSize)));
				table.addCell(new Cell().add(new Paragraph(staff.getStaffBasicInfo().getStaffInstituteId()).setFont(regularFont).setFontSize(contentFontSize)));
				table.addCell(new Cell().add(new Paragraph(staff.getStaffBasicInfo().getName()).setFont(regularFont).setFontSize(contentFontSize)));
				table.addCell(new Cell().add(new Paragraph(staff.getStaffBasicInfo().getPrimaryContactNumber()).setFont(regularFont).setFontSize(contentFontSize)));
				table.addCell(new Cell().add(new Paragraph(staff.getStaffBasicInfo().getPrimaryEmail()).setFont(regularFont).setFontSize(contentFontSize)));
				table.addCell(new Cell().add(new Paragraph(credentials.getUserName() == null ? EMPTY_TEXT : credentials.getUserName()).setFont(regularFont).setFontSize(contentFontSize)));
				table.addCell(new Cell().add(new Paragraph(credentials.getPassword() == null ? EMPTY_TEXT : credentials.getPassword()).setFont(regularFont).setFontSize(contentFontSize)));
			}
		}
		document.add(table);
	}
}
