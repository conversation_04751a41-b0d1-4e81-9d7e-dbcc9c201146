package com.lernen.cloud.pdf;

import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.ExamReportCardConfiguration;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.examination.ExamReportCardManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.pdf.exam.reports.ExamReportSerivceProvider;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.io.*;
import java.util.UUID;

/**
 * Hello world!
 */
public class App {
	public static void main(String[] args) {
		App app = new App();
		final ApplicationContext context = new ClassPathXmlApplicationContext("pdf.xml");
		ExamReportCardManager examReportCardManager = context.getBean(ExamReportCardManager.class);
		ExamReportSerivceProvider examReportSerivceProvider = context.getBean(ExamReportSerivceProvider.class);
		StudentManager studentManager = context.getBean(StudentManager.class);
		Student student = studentManager.getStudentByAcademicSessionStudentId(10225, 110, UUID.fromString("c48de24e-c368-48a6-a5f2-36ca3da4a99b"));
		ExamReportCardConfiguration examReportCardConfiguration = examReportCardManager
				.getExamReportCardConfiguration(10225, 110, UUID.fromString("1ce27b21-5d0e-4aa9-9f18-5c5629e451ca"), "ANNUAL");

		ExamReportData examReportData = examReportCardManager.getExamReportData(10225, student,
				examReportCardConfiguration.getExamReportStructure(), "ANNUAL", true);
		System.out.println(examReportData);


		DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10225, 110, UUID.fromString("c48de24e-c368-48a6-a5f2-36ca3da4a99b"), "ANNUAL", true, UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"));
		ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
		try (OutputStream outputStream = new FileOutputStream( "/Users/<USER>/Embrate/outputPdfHTML/report12.pdf")) {
			byteArrayOutputStream.writeTo(outputStream);
		} catch (FileNotFoundException e) {
			throw new RuntimeException(e);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}

	}
}
