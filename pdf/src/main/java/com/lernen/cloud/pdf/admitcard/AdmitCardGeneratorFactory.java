package com.lernen.cloud.pdf.admitcard;

import com.lernen.cloud.core.api.admitcard.AdmitCardType;
import com.embrate.cloud.core.lib.utility.AssetProvider;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class AdmitCardGeneratorFactory {

	private static final Map<Integer, AdmitCardGenerator> INSTITUTE_ADMIT_CARD_WITHOUT_DATESHEET_GENERATOR = new HashMap<>();

	private static final Map<Integer, AdmitCardGenerator> INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR = new HashMap<>();

	private final AssetProvider assetProvider;

	public  AdmitCardGeneratorFactory(AssetProvider assetProvider) {
		this.assetProvider = assetProvider;
		initializeGenerators();
	}

	private void initializeGenerators(){
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(705, new GlobalAdmitCardTwoInAPageWithDatesheetGenerator(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10110, new AdmitCardWithDatesheetGenerator10110(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10115, new AdmitCardWithDatesheetGenerator10115(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10130, new AdmitCardWithDatesheetGenerator10130_10131(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10131, new AdmitCardWithDatesheetGenerator10130_10131(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10135, new AdmitCardWithDatesheetGenerator10135(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10160, new AdmitCardWithDatesheetGenerator10160(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10170, new AdmitCardWithDatesheetGenerator10170_10171(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10171, new AdmitCardWithDatesheetGenerator10170_10171(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10200, new AdmitCardWithDatesheetGenerator10200(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10205, new GlobalAdmitCardTwoInAPageWithDatesheetGenerator(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10225, new AdmitCardWithDatesheetGenerator10225_10226_10227_10228(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10226, new AdmitCardWithDatesheetGenerator10225_10226_10227_10228(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10227, new AdmitCardWithDatesheetGenerator10225_10226_10227_10228(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10228, new AdmitCardWithDatesheetGenerator10225_10226_10227_10228(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10230, new AdmitCardWithDatesheetGenerator10230_10231_10232(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10231, new AdmitCardWithDatesheetGenerator10230_10231_10232(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10232, new AdmitCardWithDatesheetGenerator10230_10231_10232(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10255, new AdmitCardWithDatesheetGenerator10255(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10275, new GlobalAdmitCardTwoInAPageWithDatesheetGenerator(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10295, new AdmitCardWithDatesheetGenerator10295(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10325, new GlobalAdmitCardTwoInAPageWithDatesheetGenerator(assetProvider));
		INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.put(10380, new GlobalAdmitCardTwoInAPageWithDatesheetGenerator(assetProvider));


		INSTITUTE_ADMIT_CARD_WITHOUT_DATESHEET_GENERATOR.put(10130, new AdmitCardWithoutDatesheetGenerator10130(assetProvider));
		INSTITUTE_ADMIT_CARD_WITHOUT_DATESHEET_GENERATOR.put(10190, new AdmitCardWithoutDatesheetGenerator10190(assetProvider));
		INSTITUTE_ADMIT_CARD_WITHOUT_DATESHEET_GENERATOR.put(10205, new AdmitCardWithoutDatesheetGenerator10205(assetProvider));
		INSTITUTE_ADMIT_CARD_WITHOUT_DATESHEET_GENERATOR.put(10265, new AdmitCardWithoutDatesheetGenerator10265(assetProvider));

	}

	
	public AdmitCardGenerator getAdmitCardGenerator(int instituteId, AdmitCardType admitCardType) {
		if(admitCardType == AdmitCardType.ADMIT_CARD_WITHOUT_DATESHEET) {
			if (!INSTITUTE_ADMIT_CARD_WITHOUT_DATESHEET_GENERATOR.containsKey(instituteId)) {
				return new GlobalAdmitCardWithoutDatesheetGenerator(assetProvider);
			}
			return INSTITUTE_ADMIT_CARD_WITHOUT_DATESHEET_GENERATOR.get(instituteId);
		} else if(admitCardType == AdmitCardType.ADMIT_CARD_WITH_DATESHEET) {
			if(instituteId >= 10395) {
				return new GlobalAdmitCardTwoInAPageWithDatesheetGenerator(assetProvider);
			}
			if (!INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.containsKey(instituteId)) {
				return new GlobalAdmitCardWithDatesheetGenerator(assetProvider);
			}
			return INSTITUTE_ADMIT_CARD_WITH_DATESHEET_GENERATOR.get(instituteId);
		}
		return null;
	}
}
