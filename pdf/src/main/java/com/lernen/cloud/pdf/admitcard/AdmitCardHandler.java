package com.lernen.cloud.pdf.admitcard;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.admitcard.AdmitCardType;
import com.lernen.cloud.core.api.configurations.ExamAdmitCardPreferences;
import com.lernen.cloud.core.api.course.CourseStudents;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.DatesheetDetailRow;
import com.lernen.cloud.core.api.examination.DatesheetDetails;
import com.lernen.cloud.core.api.examination.ExamDetails;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.embrate.cloud.core.lib.courses.CourseManager;
import com.lernen.cloud.core.lib.examination.ExaminationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
public class AdmitCardHandler {

    private static final Logger logger = LogManager.getLogger(AdmitCardHandler.class);

    private final AdmitCardGeneratorFactory admitCardGeneratorFactory;
    private final ExaminationManager examinationManager;
    private final InstituteManager instituteManager;
    private final StudentManager studentManager;
    private final UserPreferenceSettings userPreferenceSettings;
    private final CourseManager courseManager;

    public AdmitCardHandler(ExaminationManager examinationManager, InstituteManager instituteManager,
                            StudentManager studentManager, UserPreferenceSettings userPreferenceSettings,
                            CourseManager courseManager, AssetProvider assetProvider) {
        this.admitCardGeneratorFactory = new AdmitCardGeneratorFactory(assetProvider);
        this.examinationManager = examinationManager;
        this.instituteManager = instituteManager;
        this.studentManager = studentManager;
        this.userPreferenceSettings = userPreferenceSettings;
        this.courseManager = courseManager;
    }

    public DocumentOutput generateAdmitCards(int instituteId, UUID examId, UUID userId, AdmitCardType admitCardType,
                                             List<UUID> studentUUIDs, int countPerPage) {
        if (instituteId <= 0 || examId == null) {
            logger.error("Invalid institute id or exam id");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid institute id or exam id"));
        }

        if (countPerPage == 0) {
            countPerPage = 10;
        }

        ExamAdmitCardPreferences examAdmitCardPreferences = userPreferenceSettings
                .getExamAdmitCardPreferences(instituteId);

        if(admitCardType == AdmitCardType.ADMIT_CARD_WITHOUT_DATESHEET){
            examAdmitCardPreferences.setItemsPerRow(2);
            examAdmitCardPreferences.setRowsPerPage(countPerPage/2);
        }

        ExamDetails examDetails = examinationManager.getExamDetails(examId, instituteId);
        if (examDetails == null) {
            logger.error("Invalid examDetails for exam id {}, instituteId {} ", examId, instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Invalid exam"));
        }

        Map<Integer, List<DatesheetDetailRow>> datesheetDetailsMap = null;
        Map<UUID, Set<UUID>> studentCourseMap = null;

        if (admitCardType == AdmitCardType.ADMIT_CARD_WITH_DATESHEET) {
            datesheetDetailsMap = examinationManager.getSortedDatesheetDetailRow(instituteId, examDetails.getAcademicSession().getAcademicSessionId(),
                    examDetails.getStandard().getStandardId(), examId, null);

            //Map of student UUID and course UUID set
            studentCourseMap = getStudentCoursesMap(
                    courseManager.getStudentsCoursesAssignmentDetailsByStandardId(examDetails.getAcademicSession().getAcademicSessionId(),
                            examDetails.getStandard().getStandardId()));
        }

        List<Student> studentList = studentManager.getClassStudents(instituteId,
                examDetails.getAcademicSession().getAcademicSessionId(), examDetails.getStandard().getStandardId());

        List<Student> students = new ArrayList<>();
        if (!CollectionUtils.isEmpty(studentUUIDs)) {
            for (Student student : studentList) {
                if (studentUUIDs.contains(student.getStudentId())) {
                    students.add(student);
                }
            }
        } else {
            students = studentList;
        }

        logger.info("Generating admit cards for instituteId {}, exam id {} , student count {}", instituteId, examId,
                students.size());

        DatesheetDetails datesheetDetails = examinationManager.getDatesheetDetailsByExamId(
                instituteId, examDetails.getAcademicSession().getAcademicSessionId(), examDetails.getStandard().getStandardId(),
                examId);

        AdmitCardGenerator admitCardGenerator = admitCardGeneratorFactory.getAdmitCardGenerator(instituteId, admitCardType);
        String fileName = examDetails.getStandard().getStandardName() + " "
                + examDetails.getExamMetaData().getExamName() + ".pdf";

        return admitCardGenerator.generateAdmitCard(instituteManager.getInstitute(instituteId), students, examDetails,
                datesheetDetailsMap, studentCourseMap, fileName, examAdmitCardPreferences, studentManager, datesheetDetails);
    }

    private Map<UUID, Set<UUID>> getStudentCoursesMap(List<CourseStudents> courseStudentsList) {
        if (CollectionUtils.isEmpty(courseStudentsList)) {
            return null;
        }
        Map<UUID, Set<UUID>> studentCourseMap = new HashMap<>();
        for (CourseStudents courseStudents : courseStudentsList) {
            for (UUID studentId : courseStudents.getStudents()) {
                if (studentCourseMap.containsKey(studentId)) {
                    if (!studentCourseMap.get(studentId).contains(courseStudents.getCourse().getCourseId())) {
                        studentCourseMap.get(studentId).add(courseStudents.getCourse().getCourseId());
                    }
                } else {
                    Set<UUID> courseIds = new HashSet<UUID>();
                    courseIds.add(courseStudents.getCourse().getCourseId());
                    studentCourseMap.put(studentId, courseIds);
                }
            }
        }
        return CollectionUtils.isEmpty(studentCourseMap) ? null : studentCourseMap;
    }

}
