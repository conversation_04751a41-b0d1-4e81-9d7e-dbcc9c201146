package com.lernen.cloud.pdf.admitcard;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.layout.LayoutArea;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.ExamAdmitCardPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.DatesheetDetailRow;
import com.lernen.cloud.core.api.examination.DatesheetDetails;
import com.lernen.cloud.core.api.examination.ExamDetails;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.*;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class AdmitCardWithDatesheetGenerator10225_10226_10227_10228 extends GlobalAdmitCardWithDatesheetGenerator {

	public AdmitCardWithDatesheetGenerator10225_10226_10227_10228(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}


	private static final Logger logger = LogManager.getLogger(AdmitCardWithDatesheetGenerator10225_10226_10227_10228.class);

	public static final float DEFAULT_PAGE_SIDE_MARGIN = 30f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 20f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 20f;
	public static final float LOGO_WIDTH = 75f;
	public static final float LOGO_HEIGHT = 75f;
	public static final float DEFAULT_TABLE_BORDER_WIDTH = 0.5f;

	@Override
	public DocumentOutput generateAdmitCard(Institute institute, List<Student> students, ExamDetails examDetails,
											Map<Integer, List<DatesheetDetailRow>> datesheetDetailsMap,
											Map<UUID, Set<UUID>> studentCourseMap, String documentName,
											ExamAdmitCardPreferences examAdmitCardPreferences,
											StudentManager studentManager, DatesheetDetails datesheetDetails) {

		try {

			String notes = datesheetDetails == null ? null : datesheetDetails.getNotes();
			int noOfCourses = 0;
			if(!CollectionUtils.isEmpty(datesheetDetailsMap)) {
				for (Map.Entry<Integer, List<DatesheetDetailRow>> datesheetDetailRowMap : datesheetDetailsMap.entrySet()) {
					if (!CollectionUtils.isEmpty(datesheetDetailRowMap.getValue())) {
						noOfCourses += datesheetDetailRowMap.getValue().size();
					}
				}
			}

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4, DEFAULT_PAGE_TOP_MARGIN,
					DEFAULT_PAGE_BOTTOM_MARGIN, DEFAULT_PAGE_SIDE_MARGIN);
			float contentFontSize = noOfCourses > 14 ? 7f : noOfCourses > 10 ? 7.5f : 8f;
			float defaultBorderWidth = DEFAULT_TABLE_BORDER_WIDTH;
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			int instituteId = institute.getInstituteId();
			DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup,
					null, null, contentFontSize, defaultBorderWidth,
					LOGO_WIDTH, LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(instituteId),
					null, null);

			PdfFont cambriaBoldFont = getCambriaBoldFont();
			PdfFont cambriaFont = getCambriaFont();

			int studentCount = 0;
			int pageNumber = 1;
			boolean addWaterMark = true;
			for (Student student : students) {
				Set<UUID> assignCoursesUUIDs = studentCourseMap.get(student.getStudentId());
				generateAdmitCards(document, documentLayoutData, cambriaBoldFont, cambriaFont,
						documentLayoutSetup, institute, examDetails, datesheetDetailsMap, assignCoursesUUIDs, student,
						pageNumber, studentManager, contentFontSize, examAdmitCardPreferences, notes, addWaterMark);
				/**
				 * if no of courses is greater than 16, then one page has one student datesheet ow 2
				 */
				if(noOfCourses > 16) {
					if (pageNumber != students.size()) {
						documentLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					}
					pageNumber++;
				} else {
					studentCount++;
					addWaterMark = false;
					if (studentCount < students.size() && studentCount % 2 == 0) {
						document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
						addWaterMark = true;
						pageNumber++;
					} else {
						LayoutArea currentArea = document.getRenderer().getCurrentArea();
						Rectangle rectangle = currentArea.getBBox();
						while (rectangle.getHeight() > ((documentLayoutSetup.getPageSize().getHeight() / 2) - 20)) {
							addBlankLine(document, false, 1);
							currentArea = document.getRenderer().getCurrentArea();
							rectangle = currentArea.getBBox();
						}
					}
				}

			}

			documentLayoutData.getDocument().close();

			return documentOutput;

		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating admit cards for institute {}, exam id {}", institute.getInstituteId(),
					examDetails.getExamMetaData().getExamId(), e);
		}
		return null;
	}

	private void generateAdmitCards(Document document, DocumentLayoutData documentLayoutData,
									PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
									Institute institute, ExamDetails examDetails,
									Map<Integer, List<DatesheetDetailRow>> datesheetDetails,
									Set<UUID> assignCoursesUUIDs, Student student, int pageNumber,
									StudentManager studentManager, float contentFontSize,
									ExamAdmitCardPreferences examAdmitCardPreferences, String notes,
									boolean addWaterMark) throws IOException {

		/**
		 * Watermark
		 */
		float watermarkImageHeightWidth = 400f;
		generateWatermark(documentLayoutData, institute.getInstituteId(),
				watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20, watermarkImageHeightWidth / 2);


		byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
		if (image != null) {

			float yOffSet = documentLayoutSetup.getPageSize().getHeight() - LOGO_HEIGHT - 20f;
			/**
			 * We are not adding WaterMark for second student in a page,
			 * so changing yOffset for those
			 */
			if(!addWaterMark) {
				yOffSet = (documentLayoutSetup.getPageSize().getHeight() / 2) - LOGO_HEIGHT;
			}

			generateImage(document, documentLayoutSetup, image, 55, 65,
					documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 80f, yOffSet);
		}

		/**
		 * Header
		 */
		generateHeader(document, documentLayoutData, cambriaBoldFont, cambriaFont, documentLayoutSetup,
				institute, addWaterMark, contentFontSize, examDetails);

		/**
		 * Student Details
		 */
		generateStudentDetails(document, documentLayoutData, documentLayoutSetup, cambriaFont, cambriaBoldFont, institute, student,
				contentFontSize);

		/**
		 * Datesheet Table
		 */
		generateDatesheetTable(document, documentLayoutSetup, documentLayoutData, cambriaFont, cambriaBoldFont, institute,
				student, datesheetDetails, assignCoursesUUIDs, examAdmitCardPreferences);

		/**
		 * Notes
		 */
		if(!StringUtils.isBlank(notes)) {
			generateNoteDetails(document, documentLayoutSetup, documentLayoutData, cambriaFont, cambriaBoldFont, notes);
		}

		addBlankLine(document, false, 2);
		/**
		 * Signature Details
		 */
		generateSignatureDetails(document, documentLayoutSetup, documentLayoutData, cambriaBoldFont, institute, contentFontSize,
				"Class Teacher's Signature", "Principal's Signature");

	}

	public void generateNoteDetails(Document document, DocumentLayoutSetup documentLayoutSetup, DocumentLayoutData documentLayoutData, PdfFont cambriaFont,
									PdfFont boldFont, String notes) {
		float contentFontSize = documentLayoutData.getContentFontSize();
		List<Integer> color = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);
		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NOTE_POINT_HEADING, color).setUnderline()),
				cellLayoutSetup.copy().setPdfFont(boldFont));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(notes, color)),
				cellLayoutSetup.copy().setFontSize(contentFontSize - 1));
		document.add(table);
	}

	protected void generateSignatureDetails(Document document, DocumentLayoutSetup documentLayoutSetup,
											DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont,
											Institute institute, float contentFontSize,
											String leftSideSignatureText,
											String rightSideSignatureText) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);

		List<Integer> signatureFontColor = EColorUtils.hex2Rgb("#474747");

		Paragraph classTeacherSignatureText = getParagraph(leftSideSignatureText, signatureFontColor, cambriaBoldFont);
		Paragraph principalSignatureText = getParagraph(rightSideSignatureText, signatureFontColor, cambriaBoldFont);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(classTeacherSignatureText, firstCellLayoutSetup),
						new CellData("", secondCellLayoutSetup),
						new CellData(principalSignatureText, thirdCellLayoutSetup)));

		document.add(table);

	}

	protected void generateDatesheetTable(Document document, DocumentLayoutSetup documentLayoutSetup,
										  DocumentLayoutData documentLayoutData, PdfFont cambriaFont,
										  PdfFont cambriaBoldFont, Institute institute,
										  Student student, Map<Integer, List<DatesheetDetailRow>> datesheetDetailsMap,
										  Set<UUID> assignCoursesUUIDs, ExamAdmitCardPreferences examAdmitCardPreferences) {

		if(datesheetDetailsMap == null || CollectionUtils.isEmpty(datesheetDetailsMap.entrySet()) ||
				CollectionUtils.isEmpty(assignCoursesUUIDs)) {
			return;
		}

		float contentFontSize = documentLayoutData.getContentFontSize();

//		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.32f, 0.22f, 0.22f, 0.24f });
		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.27f, 0.24f, 0.24f, 0.25f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(contentFontSize).setPaddingLeft(10f);

		Border tableBorder = new SolidBorder(Color.convertRgbToCmyk(
				new DeviceRgb(158, 158, 158)), DEFAULT_TABLE_BORDER_WIDTH);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy()
				.setTextAlignment(TextAlignment.LEFT).setPdfFont(cambriaFont).setBorder(tableBorder);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy()
				.setTextAlignment(TextAlignment.LEFT).setPdfFont(cambriaFont).setBorder(tableBorder);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy()
				.setTextAlignment(TextAlignment.LEFT).setPdfFont(cambriaFont).setBorder(tableBorder);
		CellLayoutSetup fourthCellLayoutSetup = cellLayoutSetup.copy()
				.setTextAlignment(TextAlignment.LEFT).setPdfFont(cambriaFont).setBorder(tableBorder);

		List<Integer> tableHeaderFontColor = EColorUtils.hex2Rgb(EColorUtils.WHITE_COLOR_HEX_CODE);
		Paragraph subjectText = getParagraph("Subject", tableHeaderFontColor, cambriaBoldFont);
		Paragraph examDateText = getParagraph("Exam Date", tableHeaderFontColor, cambriaBoldFont);
		Paragraph dayText = getParagraph("Day", tableHeaderFontColor, cambriaBoldFont);
		Paragraph timingsText = getParagraph("Timings", tableHeaderFontColor, cambriaBoldFont);

		String tableHeaderBackgroundColor = "#52626e";
		switch (institute.getInstituteId()) {
			case 10225:
				tableHeaderBackgroundColor = "#52626e";
				break;
			case 10226:
				tableHeaderBackgroundColor = EColorUtils._10226ColorHexCode;
				break;
			case 10227:
				tableHeaderBackgroundColor = EColorUtils._10227ColorHexCode;
				break;
			case 10228:
				tableHeaderBackgroundColor = EColorUtils._10228ColorHexCode;
				break;
			default:
		}
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(subjectText, firstCellLayoutSetup.copy().setBackgroundColor(tableHeaderBackgroundColor)),
				new CellData(examDateText, secondCellLayoutSetup.copy().setBackgroundColor(tableHeaderBackgroundColor)),
				new CellData(dayText, thirdCellLayoutSetup.copy().setBackgroundColor(tableHeaderBackgroundColor)),
				new CellData(timingsText, fourthCellLayoutSetup.copy().setBackgroundColor(tableHeaderBackgroundColor))));

		List<Integer> tableContentFontColor = EColorUtils.hex2Rgb(EColorUtils.BLACK_COLOR_HEX_CODE);

		for(Map.Entry<Integer, List<DatesheetDetailRow>> dateWiseDatesheetDetailRowEntry : datesheetDetailsMap.entrySet()) {
			if(dateWiseDatesheetDetailRowEntry == null || CollectionUtils.isEmpty(dateWiseDatesheetDetailRowEntry.getValue())) {
				continue;
			}
			for(DatesheetDetailRow datesheetDetailRow : dateWiseDatesheetDetailRowEntry.getValue()) {
				if(datesheetDetailRow == null || datesheetDetailRow.getDatesheetID() == null) {
					continue;
				}
				//Removing optional subjects that are not assigned to student
				if(!assignCoursesUUIDs.contains(datesheetDetailRow.getCourse().getCourseId())) {
					continue;
				}
				String dimensionName = examAdmitCardPreferences.isIncludeDimensionInAdmitCard() ? " ("
						+ datesheetDetailRow.getExamDimension().getDimensionName() + ") " : EMPTY_TEXT;
				String courseName = datesheetDetailRow.getCourse().getCourseName() + dimensionName;
				Paragraph subjectVal = getParagraph(courseName, tableContentFontColor, cambriaFont);

				String examDateName = datesheetDetailRow.getCourseExamDate() == null ||
						datesheetDetailRow.getCourseExamDate() <= 0 ?
						EMPTY_TEXT : DateUtils.getFormattedDate(datesheetDetailRow.getCourseExamDate(),
						"dd-MMM-yyyy", DFAULT_TIMEZONE);
				Paragraph examDateVal = getParagraph(examDateName, tableContentFontColor, cambriaFont);

				String dayOfWeekName = datesheetDetailRow.getCourseExamDate() == null ||
						datesheetDetailRow.getCourseExamDate() <= 0 ?
						EMPTY_TEXT : DateUtils.getDayOfWeek(datesheetDetailRow.getCourseExamDate());
				Paragraph dayVal = getParagraph(dayOfWeekName, tableContentFontColor, cambriaFont);

				String timingName = DateUtils.getFormattedAmPmTime(datesheetDetailRow.getStartTime(), false) + " - " +
						DateUtils.getFormattedAmPmTime(datesheetDetailRow.getEndTime(), false);
				Paragraph timingsVal = getParagraph(timingName, tableContentFontColor, cambriaFont);

				addRow(table, documentLayoutSetup, Arrays.asList(
						new CellData(subjectVal, firstCellLayoutSetup),
						new CellData(examDateVal, secondCellLayoutSetup),
						new CellData(dayVal, thirdCellLayoutSetup),
						new CellData(timingsVal, fourthCellLayoutSetup)));
			}
		}
		document.add(table);
	}

	protected void generateStudentDetails(Document document, DocumentLayoutData documentLayoutData,
										  DocumentLayoutSetup documentLayoutSetup, PdfFont cambriaFont,
										  PdfFont cambriaBoldFont, Institute institute, Student student,
										  float contentFontSize) {

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.34f, 0.33f, 0.33f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);

		List<Integer> keyFontColor = EColorUtils.hex2Rgb("#434343");
		String valueFontHexColor = "#52626e";
		switch (institute.getInstituteId()) {
			case 10225:
				valueFontHexColor = "#52626e";
				break;
			case 10226:
				valueFontHexColor = EColorUtils._10226ColorHexCode;
				break;
			case 10227:
				valueFontHexColor = EColorUtils._10227ColorHexCode;
				break;
			case 10228:
				valueFontHexColor = EColorUtils._10228ColorHexCode;
				break;
			default:
		}

		List<Integer> valueFontColor = EColorUtils.hex2Rgb(valueFontHexColor);
		Paragraph sessionDetails = getKeyValueParagraph("Session : ", student.getStudentAcademicSessionInfoResponse()
				.getAcademicSession().getShortYearDisplayName(), keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont).setMultipliedLeading(1f);
		Paragraph studentName = getKeyValueParagraph("Student's Name : ", student.getStudentBasicInfo().getName(),
				keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont).setMultipliedLeading(1f);
		Paragraph fatherName = getKeyValueParagraph("Father's Name : ", student.getStudentFamilyInfo() == null ?
						EMPTY_TEXT : StringUtils.isEmpty(student.getStudentFamilyInfo().getFathersName()) ?
						EMPTY_TEXT : student.getStudentFamilyInfo().getFathersName(),
				keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont).setMultipliedLeading(1f);
		Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ", student.getStudentBasicInfo().getAdmissionNumber(),
				keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont).setMultipliedLeading(1f);
		Paragraph className = getKeyValueParagraph("Class. : ", student.getStudentAcademicSessionInfoResponse().getStandard()
				.getDisplayNameWithSection(), keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont).setMultipliedLeading(1f);
		String rollNumberText = StringUtils.isBlank(student.getStudentAcademicSessionInfoResponse().getRollNumber()) ?
				"   " : student.getStudentAcademicSessionInfoResponse().getRollNumber();
		Paragraph rollNumber = getKeyValueParagraph("Roll Number : ", rollNumberText, keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont).setMultipliedLeading(1f);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(sessionDetails, firstCellLayoutSetup), new CellData(studentName, secondCellLayoutSetup),
						new CellData(className, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData(fatherName, secondCellLayoutSetup),
						new CellData(rollNumber, thirdCellLayoutSetup)));

		document.add(table);
	}

	protected void generateHeader(Document document, DocumentLayoutData documentLayoutData,
								  PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
								  Institute institute, boolean addWaterMark,
								  float contentFontSize, ExamDetails examDetails) throws IOException {

		float yOffSet = documentLayoutSetup.getPageSize().getHeight() - LOGO_HEIGHT - 25f;
		/**
		 * We are not adding WaterMark for second student in a page,
		 * so changing yOffset for those
		 */
		if(!addWaterMark) {
			yOffSet = (documentLayoutSetup.getPageSize().getHeight() / 2) - LOGO_HEIGHT - 5f;
		}
		generateDynamicImageProvider(documentLayoutData, 30f, yOffSet, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setTextAlignment(TextAlignment.CENTER).setPaddingLeft(25f);

		List<Integer> instituteNameFontColor = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase(), instituteNameFontColor)),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 4).setPdfFont(cambriaBoldFont));

		String letterHead1FontHexColor = "#52626e";
		switch (institute.getInstituteId()) {
			case 10225:
				letterHead1FontHexColor = "#52626e";
				break;
			case 10226:
				letterHead1FontHexColor = EColorUtils._10226ColorHexCode;
				break;
			case 10227:
				letterHead1FontHexColor = EColorUtils._10227ColorHexCode;
				break;
			case 10228:
				letterHead1FontHexColor = EColorUtils._10228ColorHexCode;
				break;
			default:
		}
		List<Integer> letterHead1FontColor = EColorUtils.hex2Rgb(letterHead1FontHexColor);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1(), letterHead1FontColor)),
				cellLayoutSetup.copy().setFontSize(contentFontSize - 1).setPdfFont(cambriaFont));

		String letterHead2FontHexColor = "#52626e";
		switch (institute.getInstituteId()) {
			case 10225:
				letterHead1FontHexColor = "#52626e";
				break;
			case 10226:
				letterHead1FontHexColor = EColorUtils._10226ColorHexCode;
				break;
			case 10227:
				letterHead1FontHexColor = EColorUtils._10227ColorHexCode;
				break;
			case 10228:
				letterHead1FontHexColor = EColorUtils._10228ColorHexCode;
				break;
			default:
		}
		List<Integer> letterHead2FontColor = EColorUtils.hex2Rgb(letterHead1FontHexColor);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2(), letterHead2FontColor)),
				cellLayoutSetup.copy().setFontSize(contentFontSize - 1).setPdfFont(cambriaFont));

		documentLayoutData.getDocument().add(table);

		generateAdmitCardText(document, documentLayoutData, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute, examDetails);
	}

	protected void generateAdmitCardText(Document document, DocumentLayoutData documentLayoutData,
										 PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
										 Institute institute, ExamDetails examDetails) throws IOException {

		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setTextAlignment(TextAlignment.CENTER);

		List<Integer> admitCardTextFontColor = EColorUtils.hex2Rgb("#474747");
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph( "ADMIT CARD (" + examDetails.getExamMetaData().getExamName() + ")", admitCardTextFontColor).setUnderline()),
				cellLayoutSetup.copy().setFontSize(9f).setPdfFont(cambriaBoldFont));

		documentLayoutData.getDocument().add(table);


		addBlankLine(document, false, 1);

	}

}
