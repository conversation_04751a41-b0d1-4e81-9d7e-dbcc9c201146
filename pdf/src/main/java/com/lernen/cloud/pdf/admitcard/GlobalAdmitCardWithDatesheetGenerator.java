package com.lernen.cloud.pdf.admitcard;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.ExamAdmitCardPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.DatesheetDetailRow;
import com.lernen.cloud.core.api.examination.DatesheetDetails;
import com.lernen.cloud.core.api.examination.ExamDetails;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.*;

/**
 * 
 * <AUTHOR>
 *
 */
public class GlobalAdmitCardWithDatesheetGenerator extends AdmitCardGenerator {

	public GlobalAdmitCardWithDatesheetGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}


	private static final Logger logger = LogManager.getLogger(GlobalAdmitCardWithDatesheetGenerator.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 30f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 20f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 20f;
	public static final float LOGO_WIDTH = 75f;
	public static final float LOGO_HEIGHT = 75f;
	public static final float DEFAULT_TABLE_BORDER_WIDTH = 0.5f;
	public static final TimeZone DFAULT_TIMEZONE = TimeZone.getTimeZone("Asia/Kolkata");
	public static final String NOTE_POINT_HEADING = "INSTRUCTIONS:";

	@Override
	public DocumentOutput generateAdmitCard(Institute institute, List<Student> students, ExamDetails examDetails,
											Map<Integer, List<DatesheetDetailRow>> datesheetDetailsMap,
											Map<UUID, Set<UUID>> studentCourseMap,
											String documentName, ExamAdmitCardPreferences examAdmitCardPreferences,
											StudentManager studentManager, DatesheetDetails datesheetDetails) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4, DEFAULT_PAGE_TOP_MARGIN,
					DEFAULT_PAGE_BOTTOM_MARGIN, DEFAULT_PAGE_SIDE_MARGIN);
			float contentFontSize = 12f;
			float defaultBorderWidth = DEFAULT_TABLE_BORDER_WIDTH;
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			int instituteId = institute.getInstituteId();
			float logoWidth = examAdmitCardPreferences == null ? LOGO_WIDTH :
					examAdmitCardPreferences.getLogoWidth()  == null ? LOGO_WIDTH : examAdmitCardPreferences.getLogoWidth();
			float logoHeight = examAdmitCardPreferences == null ? LOGO_HEIGHT :
					examAdmitCardPreferences.getLogoHeight()  == null ? LOGO_HEIGHT : examAdmitCardPreferences.getLogoHeight();

			DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup,
					null, null, contentFontSize, defaultBorderWidth,
					logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId),
					null, null);

			PdfFont cambriaBoldFont = getCambriaBoldFont();
			PdfFont cambriaFont = getCambriaFont();

			String notes = datesheetDetails == null ? null : datesheetDetails.getNotes();

			int pageNumber = 1;
			for (Student student : students) {

				Set<UUID> assignCoursesUUIDs = studentCourseMap.get(student.getStudentId());
				generateAdmitCards(document, documentLayoutData, cambriaBoldFont, cambriaFont,
						documentLayoutSetup, institute, examDetails, datesheetDetailsMap, assignCoursesUUIDs, student, pageNumber,
						studentManager, contentFontSize, examAdmitCardPreferences, notes);

				if (pageNumber != students.size()) {
					documentLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}

			documentLayoutData.getDocument().close();

			return documentOutput;
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating admit cards for institute {}, exam id {}", institute.getInstituteId(),
					examDetails.getExamMetaData().getExamId(), e);
		}
		return null;
	}

	private void generateAdmitCards(Document document, DocumentLayoutData documentLayoutData,
									PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
									Institute institute, ExamDetails examDetails,
									Map<Integer, List<DatesheetDetailRow>> datesheetDetails,
									Set<UUID> assignCoursesUUIDs, Student student, int pageNumber,
									StudentManager studentManager, float contentFontSize,
									ExamAdmitCardPreferences examAdmitCardPreferences, String notes) throws IOException {

		/**
		 * Watermark
		 */
		float watermarkImageHeightWidth = 400f;
		generateWatermark(documentLayoutData, institute.getInstituteId(),
				watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20, watermarkImageHeightWidth / 2);


		byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
		if (image != null) {
			generateImage(document, documentLayoutSetup, image, 80, 90,
					documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 120f,
					documentLayoutSetup.getPageSize().getHeight() * 0.80f + 10f);
		}

		/**
		 * Header
		 */
		generateHeader(document, documentLayoutData, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute);

		/**
		 * Student Details
		 */
		generateStudentDetails(document, documentLayoutData, documentLayoutSetup, cambriaFont, cambriaBoldFont, student, examDetails,
				contentFontSize);

		/**
		 * Datesheet Table
		 */
		generateDatesheetTable(document, documentLayoutSetup, documentLayoutData, cambriaFont, cambriaBoldFont, institute,
				student, datesheetDetails, assignCoursesUUIDs, examAdmitCardPreferences);

		/**
		 * Notes
		 */
		if(!StringUtils.isBlank(notes)) {
			generateNoteDetails(document, documentLayoutSetup, documentLayoutData, cambriaFont, cambriaBoldFont, notes);
		}

		/**
		 * Signature Details
		 */
		generateSignatureDetails(document, documentLayoutSetup, documentLayoutData, cambriaBoldFont, institute, contentFontSize,
				"Class Teacher's Signature", EMPTY_TEXT, "Principal's Signature");

	}

	public void generateNoteDetails(Document document, DocumentLayoutSetup documentLayoutSetup, DocumentLayoutData documentLayoutData, PdfFont cambriaFont,
									 PdfFont boldFont, String notes) {
		float contentFontSize = documentLayoutData.getContentFontSize();
		addBlankLine(document, true, 1);
		List<Integer> color = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);
		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NOTE_POINT_HEADING, color).setUnderline()),
				cellLayoutSetup.copy().setPdfFont(boldFont));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(notes, color)),
				cellLayoutSetup.copy().setFontSize(contentFontSize - 1));
		document.add(table);
	}

	protected void generateSignatureDetails(Document document, DocumentLayoutSetup documentLayoutSetup,
									  DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont,
									  Institute institute, float contentFontSize,
										String leftSideSignatureText,
											String centerSideSignatureText,
										String rightSideSignatureText) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.34f, 0.33f, 0.33f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);

		List<Integer> signatureFontColor = EColorUtils.hex2Rgb("#474747");
		Paragraph leftSideSignatureParagraph = getParagraph(leftSideSignatureText, signatureFontColor, cambriaBoldFont);
		Paragraph centerSideSignatureParagraph = getParagraph(centerSideSignatureText, signatureFontColor, cambriaBoldFont);
		Paragraph rightSideSignatureParagraph = getParagraph(rightSideSignatureText, signatureFontColor, cambriaBoldFont);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(leftSideSignatureParagraph, firstCellLayoutSetup),
						new CellData(centerSideSignatureParagraph, secondCellLayoutSetup),
						new CellData(rightSideSignatureParagraph, thirdCellLayoutSetup)));

		table.setFixedPosition(30f, 20f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);

	}

	protected void generateDatesheetTable(Document document, DocumentLayoutSetup documentLayoutSetup,
										DocumentLayoutData documentLayoutData, PdfFont cambriaFont,
										PdfFont cambriaBoldFont, Institute institute,
										Student student, Map<Integer, List<DatesheetDetailRow>> datesheetDetailsMap,
										Set<UUID> assignCoursesUUIDs, ExamAdmitCardPreferences examAdmitCardPreferences) {

		addBlankLine(document, true, 1);

		if(datesheetDetailsMap == null || CollectionUtils.isEmpty(datesheetDetailsMap.entrySet()) ||
				CollectionUtils.isEmpty(assignCoursesUUIDs)) {
			return;
		}

		float contentFontSize = documentLayoutData.getContentFontSize();

//		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.32f, 0.22f, 0.22f, 0.24f });
		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.25f, 0.25f, 0.25f, 0.25f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(contentFontSize).setPaddingLeft(10f);

		Border tableBorder = new SolidBorder(Color.convertRgbToCmyk(
				new DeviceRgb(158, 158, 158)), DEFAULT_TABLE_BORDER_WIDTH);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy()
				.setTextAlignment(TextAlignment.LEFT).setPdfFont(cambriaFont).setBorder(tableBorder);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy()
				.setTextAlignment(TextAlignment.LEFT).setPdfFont(cambriaFont).setBorder(tableBorder);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy()
				.setTextAlignment(TextAlignment.LEFT).setPdfFont(cambriaFont).setBorder(tableBorder);
		CellLayoutSetup fourthCellLayoutSetup = cellLayoutSetup.copy()
				.setTextAlignment(TextAlignment.LEFT).setPdfFont(cambriaFont).setBorder(tableBorder);

		List<Integer> tableHeaderFontColor = EColorUtils.hex2Rgb(EColorUtils.WHITE_COLOR_HEX_CODE);
		Paragraph subjectText = getParagraph("Subject", tableHeaderFontColor, cambriaBoldFont);
		Paragraph examDateText = getParagraph("Exam Date", tableHeaderFontColor, cambriaBoldFont);
		Paragraph dayText = getParagraph("Day", tableHeaderFontColor, cambriaBoldFont);
		Paragraph timingsText = getParagraph("Timings", tableHeaderFontColor, cambriaBoldFont);

		String tableHeaderBackgroundColor = EColorUtils.themeColorHexCode;
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(subjectText, firstCellLayoutSetup.copy().setBackgroundColor(tableHeaderBackgroundColor)),
				new CellData(examDateText, secondCellLayoutSetup.copy().setBackgroundColor(tableHeaderBackgroundColor)),
				new CellData(dayText, thirdCellLayoutSetup.copy().setBackgroundColor(tableHeaderBackgroundColor)),
				new CellData(timingsText, fourthCellLayoutSetup.copy().setBackgroundColor(tableHeaderBackgroundColor))));

		List<Integer> tableContentFontColor = EColorUtils.hex2Rgb(EColorUtils.BLACK_COLOR_HEX_CODE);

		for(Map.Entry<Integer, List<DatesheetDetailRow>> dateWiseDatesheetDetailRowEntry : datesheetDetailsMap.entrySet()) {
			if(dateWiseDatesheetDetailRowEntry == null || CollectionUtils.isEmpty(dateWiseDatesheetDetailRowEntry.getValue())) {
				continue;
			}
			for(DatesheetDetailRow datesheetDetailRow : dateWiseDatesheetDetailRowEntry.getValue()) {
				if(datesheetDetailRow == null || datesheetDetailRow.getDatesheetID() == null) {
					continue;
				}
				//Removing optional subjects that are not assigned to student
				if(!assignCoursesUUIDs.contains(datesheetDetailRow.getCourse().getCourseId())) {
					continue;
				}
				String dimensionName = examAdmitCardPreferences.isIncludeDimensionInAdmitCard() ? " ("
						+ datesheetDetailRow.getExamDimension().getDimensionName() + ") " : EMPTY_TEXT;
				String courseName = datesheetDetailRow.getCourse().getCourseName() + dimensionName;
				Paragraph subjectVal = getParagraph(courseName, tableContentFontColor, cambriaFont);

				String examDateName = datesheetDetailRow.getCourseExamDate() == null ||
						datesheetDetailRow.getCourseExamDate() <= 0 ?
						EMPTY_TEXT : DateUtils.getFormattedDate(datesheetDetailRow.getCourseExamDate(),
						"dd-MMM-yyyy", DFAULT_TIMEZONE);
				Paragraph examDateVal = getParagraph(examDateName, tableContentFontColor, cambriaFont);

				String dayOfWeekName = datesheetDetailRow.getCourseExamDate() == null ||
						datesheetDetailRow.getCourseExamDate() <= 0 ?
						EMPTY_TEXT : DateUtils.getDayOfWeek(datesheetDetailRow.getCourseExamDate());
				Paragraph dayVal = getParagraph(dayOfWeekName, tableContentFontColor, cambriaFont);

				String timingName = DateUtils.getFormattedAmPmTime(datesheetDetailRow.getStartTime(), false) + " - " +
						DateUtils.getFormattedAmPmTime(datesheetDetailRow.getEndTime(), false);
				Paragraph timingsVal = getParagraph(timingName, tableContentFontColor, cambriaFont);

				addRow(table, documentLayoutSetup, Arrays.asList(
						new CellData(subjectVal, firstCellLayoutSetup),
						new CellData(examDateVal, secondCellLayoutSetup),
						new CellData(dayVal, thirdCellLayoutSetup),
						new CellData(timingsVal, fourthCellLayoutSetup)));
			}
		}
		document.add(table);
	}

	protected void generateStudentDetails(Document document, DocumentLayoutData documentLayoutData,
										DocumentLayoutSetup documentLayoutSetup, PdfFont cambriaFont,
										PdfFont cambriaBoldFont, Student student, ExamDetails examDetails,
										  float contentFontSize) {

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.48f, 0.04f, 0.48f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);

		List<Integer> keyFontColor = EColorUtils.hex2Rgb("#434343");
		List<Integer> valueFontColor = EColorUtils.hex2Rgb(EColorUtils.themeColorHexCode);

		Paragraph examName = getKeyValueParagraph("Exam : ", examDetails.getExamMetaData().getExamName(),
				keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont);
		Paragraph sessionDetails = getKeyValueParagraph("Session : ", student.getStudentAcademicSessionInfoResponse()
				.getAcademicSession().getShortYearDisplayName(), keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont);
		Paragraph studentName = getKeyValueParagraph("Student's Name : ", student.getStudentBasicInfo().getName(),
				keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont);
		Paragraph fatherName = getKeyValueParagraph("Father's Name : ", student.getStudentFamilyInfo() == null ?
				EMPTY_TEXT : StringUtils.isEmpty(student.getStudentFamilyInfo().getFathersName()) ?
				EMPTY_TEXT : student.getStudentFamilyInfo().getFathersName(),
				keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont);
		Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ", student.getStudentBasicInfo().getAdmissionNumber(),
				keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont);
		Paragraph className = getKeyValueParagraph("Class. : ", student.getStudentAcademicSessionInfoResponse().getStandard()
				.getDisplayNameWithSection(), keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont);
		String rollNumberText = StringUtils.isBlank(student.getStudentAcademicSessionInfoResponse().getRollNumber()) ?
				"   " : student.getStudentAcademicSessionInfoResponse().getRollNumber();
		Paragraph rollNumber = getKeyValueParagraph("Roll Number : ", rollNumberText, keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(examName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(sessionDetails, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(admissionNumber, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(fatherName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(className, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(rollNumber, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(EMPTY_TEXT, thirdCellLayoutSetup)));

		document.add(table);
	}

	protected void generateHeader(Document document, DocumentLayoutData documentLayoutData,
										 PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
										 Institute institute) throws IOException {

		generateDynamicImageProvider(documentLayoutData, 30f, documentLayoutSetup.getPageSize().getHeight() - LOGO_HEIGHT - 25f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setTextAlignment(TextAlignment.CENTER).setPaddingLeft(25f);

		float instituteNameFontSize = 17f;
		List<Integer> instituteNameFontColor = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);
//		if(institute.getInstituteId() == 10200) {
//			instituteNameFontSize = 24f;
//			instituteNameFontColor = EColorUtils.hex2Rgb(EColorUtils.purpleHexCode);
//		}
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase(), instituteNameFontColor)),
				cellLayoutSetup.copy().setFontSize(instituteNameFontSize).setPdfFont(cambriaBoldFont));

		List<Integer> letterHead1FontColor = EColorUtils.hex2Rgb(EColorUtils.themeColorHexCode);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1(), letterHead1FontColor)),
				cellLayoutSetup.copy().setFontSize(12f).setPdfFont(cambriaFont));

		List<Integer> letterHead2FontColor = EColorUtils.hex2Rgb(EColorUtils.themeColorHexCode);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2(), letterHead2FontColor)),
				cellLayoutSetup.copy().setFontSize(12f).setPdfFont(cambriaFont));

		documentLayoutData.getDocument().add(table);

		addBlankLine(document, true, 1);

		generateAdmitCardText(document, documentLayoutData, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute);
	}

	protected void generateAdmitCardText(Document document, DocumentLayoutData documentLayoutData,
								PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
								Institute institute) throws IOException {

		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setTextAlignment(TextAlignment.CENTER);

		List<Integer> admitCardTextFontColor = EColorUtils.hex2Rgb("#474747");
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("ADMIT CARD", admitCardTextFontColor).setUnderline()),
				cellLayoutSetup.copy().setFontSize(15f).setPdfFont(cambriaBoldFont));

		documentLayoutData.getDocument().add(table);

		addBlankLine(document, true, 1);
	}

}
