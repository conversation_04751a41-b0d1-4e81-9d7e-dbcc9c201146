package com.lernen.cloud.pdf.certificates.birthday;

import java.util.UUID;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentManager;


public class BirthdayCertificateDocumentHandler {
    private static final Logger logger = LogManager.getLogger(BirthdayCertificateDocumentHandler.class);

    private final StudentManager studentManager;
	private final InstituteManager instituteManager;
	private final BirthdayCertificateGeneratorFactory birthdayCertificateGeneratorFactory;


	public BirthdayCertificateDocumentHandler(StudentManager studentManager, InstituteManager instituteManager, AssetProvider assetProvider) {
		this.birthdayCertificateGeneratorFactory = new BirthdayCertificateGeneratorFactory(assetProvider);
		this.studentManager = studentManager;
		this.instituteManager = instituteManager;
	}

	public DocumentOutput generateBirthdayCertificate(int instituteId, int academicSessionId, UUID studentId) {
		final Institute institute = instituteManager.getInstitute(instituteId);
		if (institute == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to get institute"));
		}
		
		final Student student = studentManager.getStudentByAcademicSessionStudentId(instituteId, academicSessionId, studentId);

		if (student == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to get student details"));
		}
		

		BirthdayCertificateGenerator birthdayCertificateGenerator = birthdayCertificateGeneratorFactory.getBirthdayCertificateGenerator(instituteId);
		String fileName = student.getStudentBasicInfo().getAdmissionNumber() + "_Birthday_Certificate.pdf";
		
		return birthdayCertificateGenerator.generateBirthdayCertificate(institute, student, fileName, studentManager);
	}
}
