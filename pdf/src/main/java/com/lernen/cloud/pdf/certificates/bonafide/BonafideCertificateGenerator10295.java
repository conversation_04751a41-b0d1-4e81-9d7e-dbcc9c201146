package com.lernen.cloud.pdf.certificates.bonafide;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.DocumentPropertiesPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.InstituteMetadataVariables;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.PronounUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;

public class BonafideCertificateGenerator10295 extends GlobalBonafideCertificateGenerator {

    public BonafideCertificateGenerator10295(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }
    private static final Logger logger = LogManager.getLogger(BonafideCertificateGenerator10295.class);

        private static final String CONTENT_1 = "This is to certify that ";
        private static final String CONTENT_2 = "%s ";//STUDENT NAME
        private static final String CONTENT_21 = "admission No. ";
        private static final String CONTENT_22 = "%s ";
        private static final String CONTENT_3 = "ward of ";
        private static final String CONTENT_4 = "Mr. %s ";//FATHER NAME
        private static final String CONTENT_5 = "and";
        private static final String CONTENT_6 = " Mrs. %s, ";//MOTHER NAME
        private static final String CONTENT_7 = "is currently enrolled in ";
        private static final String CONTENT_8 = "%s ";//Class with section
        private static final String CONTENT_9 = "at our school. According to our records, the Date Of Birth is ";
        private static final String CONTENT_10 = "%s ";//DOB
        private static final String CONTENT_11 = "( in fig.) ";
        private static final String CONTENT_12 = "%s ";// DOB in fig.
        private static final String CONTENT_13 = "and %s Permanent Education Number (PEN) is ";
        private static final String CONTENT_14 = "%s. ";//PEN Number
        private static final String CONTENT_15 = "Our school is affilated by ";
        private static final String CONTENT_16 = "CBSE ";
        private static final String CONTENT_17 = "with affiliation No. ";
        private static final String CONTENT_23 = "2130978 ";
        private static final String CONTENT_24 = "and school UDISE No. ";
        private static final String CONTENT_18 = "%s"; // udice no
        private static final String CONTENT_19 = "Date : ";
        private static final String CONTENT_20 = "%s ";//current date
        private static final String CONTENT_HEADER_LINE1 ="TO WHOM SO EVER IT MAY CONCERN";
        private static final String CONTENT_HEADER_LINE2 ="BONAFIDE CERTIFICATE";
        private static final String CITY="";

    @Override
    public DocumentOutput generateBonafideCertificate(Institute institute, Student student, String documentName,
            StudentManager studentManager, DocumentPropertiesPreferences documentPropertiesPreferences) {
                try {
                    float squareBorderMargin = 8f;
                    float borderInnerGap = 2f;
                    DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
        
                    DocumentLayoutData documentLayoutData = generateBonafiedCertificateLayoutData(institute, documentOutput, 48f,
                            48f);
                    Float secondaryLogoWidth = 75f;
                    DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
                    Document document = documentLayoutData.getDocument();
                    PdfFont boldFont = documentLayoutData.getBoldFont();
                    PdfFont regularFont = documentLayoutData.getRegularFont();
                    float contentFontSize = documentLayoutData.getContentFontSize();
                    float bgImageHeightWidth = 300f;
                    
                    generateImages(document, documentLayoutSetup, documentLayoutData, institute, student, studentManager, secondaryLogoWidth, bgImageHeightWidth);
                    generateHeader(document, documentLayoutSetup, contentFontSize, boldFont, regularFont, student, institute, CONTENT_HEADER_LINE1, CONTENT_HEADER_LINE2, CITY, documentPropertiesPreferences);
                    generateMetadata(documentLayoutData, squareBorderMargin, borderInnerGap);
                    generateContent(document, documentLayoutSetup, institute, contentFontSize, student, boldFont, regularFont);
                    generateSystemMessage(document, documentLayoutSetup, contentFontSize, boldFont, regularFont);
                    document.close();
                    return documentOutput;
                } catch (Exception e) {
                    logger.error("Error while generate study certificate for student {} ", student.getStudentBasicInfo().getAdmissionNumber(), e);
                }
                return null;
            }
            protected DocumentLayoutData generateBonafiedCertificateLayoutData(Institute institute, DocumentOutput documentOutput,
                                                                               float logoWidth, float logoHeight) throws IOException {
        
        
                DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false,  PageSize.A5, DEFAULT_PAGE_TOP_MARGIN, DEFAULT_PAGE_BOTTOM_MARGIN, DEFAULT_PAGE_SIDE_MARGIN,
                        0f);
                float contentFontSize = 12f;
                float defaultBorderWidth = 0.5f;
                Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        
                DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getCambriaFont(),
                        getCambriaBoldFont(), contentFontSize, defaultBorderWidth, logoWidth, logoHeight,
                        LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), null, null);
                documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
                return documentLayoutData;
            }
            public void generateSystemMessage(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
            PdfFont cambriaBoldFont, PdfFont cambriaFont) {
                CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
                cellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER).setFontSize(contentFontSize-2f);
        
                Table table = getPDFTable(documentLayoutSetup, 1).setTextAlignment(TextAlignment.CENTER);
                addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SYSTEM_GENERATED_MESSAGE).setItalic()), cellLayoutSetup);
			table.setFixedPosition(0f, 15f,documentLayoutSetup.getPageSize().getWidth());
		document.add(table);
        
        }
        protected void generateImages(Document document, DocumentLayoutSetup documentLayoutSetup,
            DocumentLayoutData documentLayoutData, Institute institute, Student student,
            StudentManager studentManager, Float secondaryWidth, Float bgImageHeightWidth) throws IOException {
            int instituteId = institute.getInstituteId();

            //Watermark
            generateWatermark(documentLayoutData, instituteId, bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5, bgImageHeightWidth / 2);
            //
            //LOGO
            generateDynamicImageProvider(documentLayoutData, 20f,documentLayoutSetup.getPageSize().getHeight() * 0.87f, 1.4f, 1.4f, instituteId, InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
            documentLayoutData.setLogoWidth(secondaryWidth);
            //Second Logo
            generateDynamicImageProvider(documentLayoutData, documentLayoutSetup.getPageSize().getWidth() - 100f, documentLayoutSetup.getPageSize().getHeight() * 0.88f, 1.1f, 1.1f, instituteId, InstituteDocumentType.INSTITUTE_SECONDARY_LOGO);
        }
        protected void generateContent(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute, float contentFontSize,
                                         Student student, PdfFont cambriaBoldFont, PdfFont cambriaFont) throws IOException {
        
                Table table = getPDFTable(documentLayoutSetup, 1);
        
                CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
                cellLayoutSetup.setFontSize(contentFontSize - 1).setTextAlignment(TextAlignment.JUSTIFIED);

		String diseCode = null;

		if(institute != null && !CollectionUtils.isEmpty(institute.getInstituteMetadataVariablesMap())) {
			Map<InstituteMetadataVariables, String> instituteMetadataVariablesMap = institute.getInstituteMetadataVariablesMap();
			diseCode = instituteMetadataVariablesMap.get(InstituteMetadataVariables.DISE_CODE);
		}
                String studentName = student.getStudentBasicInfo().getName().toUpperCase();
                String admissionNo = student.getStudentBasicInfo().getAdmissionNumber();
                String sonDaughter = PronounUtils.getSonDaughter(student.getStudentBasicInfo().getGender(),true);
                String hisHer = PronounUtils.getHisHer(student.getStudentBasicInfo().getGender(),false);
                String fatherName = student.getStudentFamilyInfo() == null ? NA :
                        StringUtils.isBlank(student.getStudentFamilyInfo().getFathersName()) ? NA :
                        student.getStudentFamilyInfo().getFathersName().toUpperCase();
                String motherName = student.getStudentFamilyInfo() == null ? NA :
                        StringUtils.isBlank(student.getStudentFamilyInfo().getMothersName()) ? NA :
                        student.getStudentFamilyInfo().getMothersName().toUpperCase();
                String dob = student.getStudentBasicInfo().getDateOfBirth() == null ? NA :
                        DateUtils.getFormattedDate(student.getStudentBasicInfo().getDateOfBirth());
                String dobwords = student.getStudentBasicInfo().getDateOfBirth() == null ? NA :
                        DateUtils.getDateInWords(student.getStudentBasicInfo().getDateOfBirth());
                String displayClassName = student.getStudentAcademicSessionInfoResponse() == null ? NA :
                        student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection();
                String currentDate = DateUtils.getFormattedDate(DateUtils.now());
                String penNumber = student.getStudentBasicInfo().getPenNumber();

        
                Text t1 = new Text(String.format(CONTENT_1)).setFont(cambriaFont);
                Text t2 = new Text(String.format(CONTENT_2, studentName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t21 = new Text(String.format(CONTENT_21)).setFont(cambriaFont);
                Text t22 = new Text(String.format(CONTENT_22, admissionNo)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t3 = new Text(String.format(CONTENT_3, sonDaughter)).setFont(cambriaFont);
                Text t4 = new Text(String.format(CONTENT_4, fatherName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t5 = new Text(String.format(CONTENT_5)).setFont(cambriaFont);
                Text t6 = new Text(String.format(CONTENT_6, motherName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t7 = new Text(String.format(CONTENT_7)).setFont(cambriaFont);
                Text t8 = new Text(String.format(CONTENT_8, displayClassName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t9 = new Text(String.format(CONTENT_9)).setFont(cambriaFont);
                Text t10 = new Text(String.format(CONTENT_10, dob)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t11 = new Text(String.format(CONTENT_11)).setFont(cambriaFont);
                Text t12 = new Text(String.format(CONTENT_12, dobwords)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t13 = new Text(String.format(CONTENT_13, hisHer)).setFont(cambriaFont);
                Text t14 = new Text(String.format(CONTENT_14, penNumber)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t15 = new Text(String.format(CONTENT_15)).setFont(cambriaFont);
                Text t16 = new Text(String.format(CONTENT_16)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t17 = new Text(String.format(CONTENT_17)).setFont(cambriaFont);
                Text t23 = new Text(String.format(CONTENT_23)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t24 = new Text(String.format(CONTENT_24)).setFont(cambriaFont);
                Text t18 = new Text(String.format(CONTENT_18, diseCode)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t19 = new Text(String.format(CONTENT_19)).setFont(cambriaFont);
                Text t20 = new Text(String.format(CONTENT_20, currentDate)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
        
                Paragraph p1 = new Paragraph();
                p1.add(t1).add(t2).add(t21).add(t22).add(t3).add(t4).add(t5).add(t6).add(t7).add(t8).add(t9).add(t10).add(t11).add(t12)
                        .add(t13).add(t14).add(t15).add(t16).add(t17).add(t23).add(t24).add(t18).add(". ");
                addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p1, cellLayoutSetup)));
                Paragraph p2 = new Paragraph();
                p2.add(t19).add(t20);
                addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p2, cellLayoutSetup)));
                document.add(table);
        
                addBlankLine(document, true, 2);
            }
}
