package com.lernen.cloud.pdf.certificates.bonafide;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.DocumentPropertiesPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.InstituteMetadataVariables;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.PronounUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


public class GlobalBonafideCertificateGenerator extends BonafideCertificateGenerator {

    public GlobalBonafideCertificateGenerator(AssetProvider assetProvider){
        super(assetProvider);
    }

    private static final Logger logger = LogManager.getLogger(GlobalBonafideCertificateGenerator.class);

	public static final float DEFAULT_PAGE_SIDE_MARGIN = 50f;
	public static final float DEFAULT_TABLE_BORDER_WIDTH = 0.5f;
	public static final float LOGO_WIDTH = 75f;
	public static final float LOGO_HEIGHT = 75f;
	public static final String NA = "NA";

	private static final String CONTENT_1 = "This is to certify that ";
	private static final String CONTENT_2 = "%s ";//STUDENT NAME
	private static final String CONTENT_3 = "%s";//(S/D/)
	private static final String CONTENT_4 = "o ";
	private static final String CONTENT_5 = "Shri %s ";//FATHER NAME
	private static final String CONTENT_6_7 = "&";
	private static final String CONTENT_8 = "Smt %s ";//MOTHER NAME
	private static final String CONTENT_9 = "bearing PEN : ";
	private static final String CONTENT_10 = "%s ";//PEN Number
	private static final String CONTENT_11 = "is a student of class : ";
	private static final String CONTENT_12 = "%s ";//Class with section
	private static final String CONTENT_13 = "under Scholar No.(Admission No. ) : ";
	private static final String CONTENT_14 = "%s ";//Scholer No.(Admission No. )
	private static final String CONTENT_15 = "Date Of Birth : ";
	private static final String CONTENT_16 = "%s ";//DOB
	private static final String CONTENT_17 = "for the Academic year ";
	private static final String CONTENT_18 = "%s ";//Academic year
	private static final String CONTENT_19 = "%s"; // He/She
	private static final String CONTENT_20 = " is bonafied student of ";
	private static final String CONTENT_21 = "%s";//School Name & Address
        private static final String CONTENT_22 = "bearing UDISE No.  ";
        private static final String CONTENT_23 = "%s";// udice no.
	private static final String CONTENT_24 = "Date : ";
	private static final String CONTENT_25 = "%s ";//current date


	private static final String SIGNATURE_1 = "";
	private static final String SIGNATURE_2 = "";
	private static final String SIGNATURE_3 = "Principal";
	private static final String SIGNATURE_4 = "(Seal & Signature)";

        private static final String CONTENT_HEADER_LINE1 ="TO WHOM SO EVER IT MAY CONCERN";
        private static final String CONTENT_HEADER_LINE2 ="BONAFIDE CERTIFICATE";
        private static final String CITY = " ";

        static final String SYSTEM_GENERATED_MESSAGE = "This is System Generated // Hence, it doesn't require any signature.";

    @Override
    public DocumentOutput generateBonafideCertificate(Institute institute, Student student, String documentName,
            StudentManager studentManager, DocumentPropertiesPreferences documentPropertiesPreferences) {
                try {
                    float squareBorderMargin = 8f;
                    float borderInnerGap = 2f;
                    DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
        
                    DocumentLayoutData documentLayoutData = generateBonafiedCertificateLayoutData(institute, documentOutput, documentPropertiesPreferences);

                    DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
                    Document document = documentLayoutData.getDocument();
                    PdfFont boldFont = documentLayoutData.getBoldFont();
                    PdfFont regularFont = documentLayoutData.getRegularFont();
                    float contentFontSize = documentLayoutData.getContentFontSize();
                    Float secondaryLogoWidth = documentPropertiesPreferences != null && documentPropertiesPreferences.getSecondaryLogoWidth() != null ? documentPropertiesPreferences.getSecondaryLogoWidth() : 60f;
                    float bgImageHeightWidth = documentPropertiesPreferences != null && documentPropertiesPreferences.getWatermarkLogoHeight() != null ? documentPropertiesPreferences.getWatermarkLogoHeight() : 500f;
        
                    generateImages(document, documentLayoutSetup, documentLayoutData, institute, student, studentManager, secondaryLogoWidth, bgImageHeightWidth);
                    generateHeader(document, documentLayoutSetup, contentFontSize, boldFont, regularFont, student, institute, CONTENT_HEADER_LINE1, CONTENT_HEADER_LINE2, CITY , documentPropertiesPreferences);
                    generateMetadata(documentLayoutData, squareBorderMargin, borderInnerGap);
                    generateContent(document, documentLayoutSetup, institute, contentFontSize, student, boldFont, regularFont);
                    generateSignatureBox(document, documentLayoutSetup, contentFontSize, boldFont, regularFont);
                    
                    document.close();
                    return documentOutput;
                } catch (Exception e) {
                    logger.error("Error while generate study certificate for student {} ", student.getStudentBasicInfo().getAdmissionNumber(), e);
                }
                return null;
            }

        @Override
            public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
                return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE : DEFAULT_PAGE_SIZE,
                        officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
                        officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
                        officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
                        officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
            }
        
            protected void generateImages(Document document, DocumentLayoutSetup documentLayoutSetup,
                                        DocumentLayoutData documentLayoutData, Institute institute, Student student,
                                        StudentManager studentManager, Float secondaryWidth, Float bgImageHeightWidth) throws IOException {
                int instituteId = institute.getInstituteId();
        
                //Watermark
                generateWatermark(documentLayoutData, instituteId, bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 - 50, bgImageHeightWidth / 2 - 80);
        //
                //LOGO
                generateDynamicImageProvider(documentLayoutData, 20f,documentLayoutSetup.getPageSize().getHeight() * 0.87f, 1.4f, 1.4f, instituteId, InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
                documentLayoutData.setLogoWidth(secondaryWidth);
                //Second Logo
                generateDynamicImageProvider(documentLayoutData, documentLayoutSetup.getPageSize().getWidth() - 130f, documentLayoutSetup.getPageSize().getHeight() * 0.885f, 1.2f, 1.2f, instituteId, InstituteDocumentType.INSTITUTE_SECONDARY_LOGO);
        
            }
        
            public void generateSystemMessage(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
            PdfFont cambriaBoldFont, PdfFont cambriaFont) {
                CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
                cellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER).setFontSize(contentFontSize);
        
                Table table = getPDFTable(documentLayoutSetup, 1).setTextAlignment(TextAlignment.CENTER);
                addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SYSTEM_GENERATED_MESSAGE).setItalic()), cellLayoutSetup);
			table.setFixedPosition(0f, 55f,documentLayoutSetup.getPageSize().getWidth());
		document.add(table);
        
        }
        
            protected DocumentLayoutData generateBonafiedCertificateLayoutData(Institute institute, DocumentOutput documentOutput,
                                                                               DocumentPropertiesPreferences documentPropertiesPreferences) throws IOException {
        
                float logoHeight = documentPropertiesPreferences != null && documentPropertiesPreferences.getPrimaryLogoHeight() != null ? documentPropertiesPreferences.getPrimaryLogoHeight() : 60f;
                float logoWidth = documentPropertiesPreferences != null && documentPropertiesPreferences.getPrimaryLogoWidth() != null ? documentPropertiesPreferences.getPrimaryLogoWidth() : 60f;
                DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false,  DEFAULT_PAGE_SIZE, DEFAULT_PAGE_TOP_MARGIN, DEFAULT_PAGE_BOTTOM_MARGIN, DEFAULT_PAGE_SIDE_MARGIN,
                        0f);
                float contentFontSize = documentPropertiesPreferences != null && documentPropertiesPreferences.getDocumentContentFontSize() != null ? documentPropertiesPreferences.getDocumentContentFontSize() : 14f;
                float defaultBorderWidth = 0.5f;
                Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        
                DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getCambriaFont(),
                        getCambriaBoldFont(), contentFontSize, defaultBorderWidth, logoWidth, logoHeight,
                        LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), null, null);
                documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
                return documentLayoutData;
            }
        
            protected void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                        PdfFont cambriaBoldFont, PdfFont cambriaFont, Student student, Institute institute, String contentHeaderLine1, String contentHeaderLine2, String city,
                                        DocumentPropertiesPreferences documentPropertiesPreferences) throws IOException {
                
                float instituteNameFontSize = documentPropertiesPreferences != null && documentPropertiesPreferences.getInstituteNameFontSize() != null ? documentPropertiesPreferences.getInstituteNameFontSize() : contentFontSize + 6f;
                float letterHeadLine1FontSize =  documentPropertiesPreferences != null && documentPropertiesPreferences.getLetterHead1FontSize() != null ? documentPropertiesPreferences.getLetterHead1FontSize() : contentFontSize - 2f;
                float letterHeadLine2Fontsize = documentPropertiesPreferences != null && documentPropertiesPreferences.getLetterHead2FontSize() != null ?  documentPropertiesPreferences.getLetterHead2FontSize() : contentFontSize - 2f;
                float documentHeaderFontSize = documentPropertiesPreferences != null && documentPropertiesPreferences.getDocumentHeadingFontSize() != null ? documentPropertiesPreferences.getDocumentHeadingFontSize() : contentFontSize + 2f;
                List<Integer> instituteNameFontColor = documentPropertiesPreferences != null && !StringUtils.isEmpty(documentPropertiesPreferences.getInstituteNameColor()) ? EColorUtils.hex2Rgb(documentPropertiesPreferences.getInstituteNameColor()) : EColorUtils.hex2Rgb("#39419E");
                List<Integer> letterHeadLine1FontColor = documentPropertiesPreferences != null && !StringUtils.isEmpty(documentPropertiesPreferences.getLetterHead1Color()) ? EColorUtils.hex2Rgb(documentPropertiesPreferences.getLetterHead1Color()) : EColorUtils.hex2Rgb("#212121");
                List<Integer> letterHeadLine2FontColor = documentPropertiesPreferences != null && !StringUtils.isEmpty(documentPropertiesPreferences.getLetterHead2Color()) ? EColorUtils.hex2Rgb(documentPropertiesPreferences.getLetterHead2Color()) : EColorUtils.hex2Rgb("#212121");

                CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
                cellLayoutSetup.setPdfFont(cambriaFont).setTextAlignment(TextAlignment.CENTER).setFontSize(contentFontSize);
        
                Table table = getPDFTable(documentLayoutSetup, 1).setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(
						getParagraph(institute.getInstituteName().toUpperCase()+city, instituteNameFontColor).setMultipliedLeading(1.0f)),
				cellLayoutSetup.copy().setFontSize(instituteNameFontSize).setPdfFont(cambriaBoldFont));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1(), letterHeadLine1FontColor).setMultipliedLeading(.95f)),
				cellLayoutSetup.copy().setFontSize(letterHeadLine1FontSize));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2(), letterHeadLine2FontColor).setMultipliedLeading(.95f)),
				cellLayoutSetup.setFontSize(letterHeadLine2Fontsize));
		document.add(table);

		addBlankLine(document, true, 1);
        
                table = getPDFTable(documentLayoutSetup, 1).setUnderline().setTextAlignment(TextAlignment.CENTER);
                if(!StringUtils.isBlank(contentHeaderLine1)){
                        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(contentHeaderLine1)),
                        cellLayoutSetup.copy().setFontSize(documentHeaderFontSize).setPdfFont(cambriaBoldFont));
                }
                if(!StringUtils.isBlank(contentHeaderLine2)){
                        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(contentHeaderLine2)),
                        cellLayoutSetup.copy().setFontSize(documentHeaderFontSize).setPdfFont(cambriaBoldFont));
                }
                document.add(table);
        
                addBlankLine(document, true, 1);

            }
        
            protected void generateContent(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute, float contentFontSize,
                                         Student student, PdfFont cambriaBoldFont, PdfFont cambriaFont) throws IOException {
        
                Table table = getPDFTable(documentLayoutSetup, 1);
        
                CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
                cellLayoutSetup.setFontSize(contentFontSize - 1).setTextAlignment(TextAlignment.JUSTIFIED);

		String diseCode = null;

		if(institute != null && !CollectionUtils.isEmpty(institute.getInstituteMetadataVariablesMap())) {
			Map<InstituteMetadataVariables, String> instituteMetadataVariablesMap = institute.getInstituteMetadataVariablesMap();
			diseCode = instituteMetadataVariablesMap.get(InstituteMetadataVariables.DISE_CODE);
		}
                String studentName = student.getStudentBasicInfo().getName().toUpperCase();
                String sonDaughter = PronounUtils.getSonDaughter(student.getStudentBasicInfo().getGender(),true);
                String heShe = PronounUtils.getHeShe(student.getStudentBasicInfo().getGender(),true);
                String fatherName = student.getStudentFamilyInfo() == null ? NA :
                        StringUtils.isBlank(student.getStudentFamilyInfo().getFathersName()) ? NA :
                        student.getStudentFamilyInfo().getFathersName().toUpperCase();
                String motherName = student.getStudentFamilyInfo() == null ? NA :
                        StringUtils.isBlank(student.getStudentFamilyInfo().getMothersName()) ? NA :
                        student.getStudentFamilyInfo().getMothersName().toUpperCase();
                String dob = student.getStudentBasicInfo().getDateOfBirth() == null ? NA :
                        DateUtils.getFormattedDate(student.getStudentBasicInfo().getDateOfBirth());
                String AcademicSession = student.getStudentAcademicSessionInfoResponse().getAcademicSession().getShortYearDisplayName();
                String srNumber = student.getStudentBasicInfo().getAdmissionNumber();
                String displayClassName = student.getStudentAcademicSessionInfoResponse() == null ? NA :
                        student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection();
                String currentDate = DateUtils.getFormattedDate(DateUtils.now());
                String schoolNameAndAddress = (institute != null && institute.getInstituteName() != null) ? (institute.getAddressLine1() != null ? institute.getInstituteName() + "," + institute.getAddressLine1() : institute.getInstituteName()) : NA;
                String penNumber = student.getStudentBasicInfo().getPenNumber();

        
                Text t1 = new Text(String.format(CONTENT_1)).setFont(cambriaFont);
                Text t2 = new Text(String.format(CONTENT_2, studentName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t3 = new Text(String.format(CONTENT_3, sonDaughter)).setFont(cambriaFont);
                Text t4 = new Text(String.format(CONTENT_4)).setFont(cambriaFont);
                Text t5 = new Text(String.format(CONTENT_5, fatherName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t6_7 = new Text(String.format(CONTENT_6_7)).setFont(cambriaFont);
                Text t8 = new Text(String.format(CONTENT_8, motherName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t9 = new Text(String.format(CONTENT_9)).setFont(cambriaFont);
                Text t10 = new Text(String.format(CONTENT_10, penNumber)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t11 = new Text(String.format(CONTENT_11)).setFont(cambriaFont);
                Text t12 = new Text(String.format(CONTENT_12, displayClassName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t13 = new Text(String.format(CONTENT_13)).setFont(cambriaFont);
                Text t14 = new Text(String.format(CONTENT_14, srNumber)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t15 = new Text(String.format(CONTENT_15)).setFont(cambriaFont);
                Text t16 = new Text(String.format(CONTENT_16, dob)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t17 = new Text(String.format(CONTENT_17)).setFont(cambriaFont);
                Text t18 = new Text(String.format(CONTENT_18, AcademicSession)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t19 = new Text(String.format(CONTENT_19, heShe)).setFont(cambriaFont);
                Text t20 = new Text(String.format(CONTENT_20)).setFont(cambriaFont);
                Text t21 = new Text(String.format(CONTENT_21, schoolNameAndAddress)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t22 = new Text(String.format(CONTENT_22)).setFont(cambriaFont);
                Text t23 = new Text(String.format(CONTENT_23, diseCode)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
                Text t24 = new Text(String.format(CONTENT_24)).setFont(cambriaFont);
                Text t25 = new Text(String.format(CONTENT_25, currentDate)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
        
                Paragraph p1 = new Paragraph();
                p1.add(t1).add(t2).add(t3).add(t4).add(t5).add(t6_7).add(t8).add(t9).add(t10).add(t11).add(t12)
                        .add(t13).add(t14).add(t15).add(t16).add(t17).add(t18).add(". ").add(t19).add(t20).add(t21).add(" ").add(t22).add(t23);
                addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p1, cellLayoutSetup)));
                Paragraph p2 = new Paragraph();
                p2.add(t24).add(t25);
                addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p2, cellLayoutSetup)));
                document.add(table);
        
                addBlankLine(document, true, 3);
            }
        
            protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
										PdfFont cambriaBoldFont, PdfFont cambriaFont) throws IOException {
		Table table = getPDFTable(documentLayoutSetup, 2);
		CellLayoutSetup signatureLeftCellLayoutSetup = new CellLayoutSetup();
		signatureLeftCellLayoutSetup.setPdfFont(cambriaFont).setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT);
		addBlankLine(document, true, 2);
		CellLayoutSetup signatureRightCellLayoutSetup = new CellLayoutSetup();
		signatureRightCellLayoutSetup.setTextAlignment(TextAlignment.RIGHT).setPdfFont(cambriaFont).setFontSize(contentFontSize);

		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(SIGNATURE_1, signatureLeftCellLayoutSetup),
				new CellData(SIGNATURE_3, signatureRightCellLayoutSetup)));
                addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(SIGNATURE_2, signatureLeftCellLayoutSetup),
				new CellData(SIGNATURE_4, signatureRightCellLayoutSetup)));
		document.add(table);
	}
}
