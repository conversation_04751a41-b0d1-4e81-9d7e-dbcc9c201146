/**
 * 
 */
package com.lernen.cloud.pdf.certificates.character;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class CharacterCertificateDocumentHandler {
	
	private static final Logger logger = LogManager.getLogger(CharacterCertificateDocumentHandler.class);

	private final StudentManager studentManager;
	private final InstituteManager instituteManager;
	private final CharacterCertificateGeneratorFactory characterCertificateGeneratorFactory;


	public CharacterCertificateDocumentHandler(StudentManager studentManager, InstituteManager instituteManager, AssetProvider assetProvider) {
		this.characterCertificateGeneratorFactory = new CharacterCertificateGeneratorFactory(assetProvider);
		this.studentManager = studentManager;
		this.instituteManager = instituteManager;
	}

	public DocumentOutput generateCharacterCertificate(int instituteId, UUID studentId, int academicSessionId) {
		final Institute institute = instituteManager.getInstitute(instituteId);
		if (institute == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to get institute"));
		}
		
		final Student student = studentManager.getStudentByAcademicSessionStudentId(instituteId, academicSessionId, studentId);

		if (student == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to get student details"));
		}
		

		CharacterCertificateGenerator characterCertificateGenerator = characterCertificateGeneratorFactory.getCharacterCertificateGenerator(instituteId);
		String fileName = student.getStudentBasicInfo().getAdmissionNumber() + "_Character_Certificate.pdf";
		
		return characterCertificateGenerator.generateCharacterCertificate(institute, student, fileName, studentManager);
	}

}
