/**
 * 
 */
package com.lernen.cloud.pdf.certificates.character;

import com.embrate.cloud.core.lib.utility.AssetProvider;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class CharacterCertificateGeneratorFactory {

	private static final Map<Integer, CharacterCertificateGenerator> CHARACTER_CERTIFICATE_GENERATOR = new HashMap<>();
	private final AssetProvider assetProvider;

	public  CharacterCertificateGeneratorFactory(AssetProvider assetProvider) {
		this.assetProvider = assetProvider;
		initializeGenerators();
	}

	private void initializeGenerators(){
		CHARACTER_CERTIFICATE_GENERATOR.put(10085, new CharacterCertificateGenerator10085(assetProvider));
		CHARACTER_CERTIFICATE_GENERATOR.put(10130, new CharacterCertificateGenerator10130(assetProvider));
		CHARACTER_CERTIFICATE_GENERATOR.put(10190, new CharacterCertificateGenerator10190(assetProvider));
		CHARACTER_CERTIFICATE_GENERATOR.put(10295, new CharacterCertificateGenerator10295(assetProvider));
		CHARACTER_CERTIFICATE_GENERATOR.put(10225, new CharacterCertificateGenerator10225_10226_10227_10228(assetProvider));
		CHARACTER_CERTIFICATE_GENERATOR.put(10226, new CharacterCertificateGenerator10225_10226_10227_10228(assetProvider));
		CHARACTER_CERTIFICATE_GENERATOR.put(10227, new CharacterCertificateGenerator10225_10226_10227_10228(assetProvider));
		CHARACTER_CERTIFICATE_GENERATOR.put(10228, new CharacterCertificateGenerator10225_10226_10227_10228(assetProvider));
	}

	public CharacterCertificateGenerator getCharacterCertificateGenerator(int instituteId) {
		if (!CHARACTER_CERTIFICATE_GENERATOR.containsKey(instituteId)) {
			return new GlobalCharacterCertificateGenerator(assetProvider);
		}
		return CHARACTER_CERTIFICATE_GENERATOR.get(instituteId);
	}

}
