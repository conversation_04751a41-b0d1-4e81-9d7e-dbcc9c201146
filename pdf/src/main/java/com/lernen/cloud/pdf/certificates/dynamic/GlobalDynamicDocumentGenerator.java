/**
 * 
 */
package com.lernen.cloud.pdf.certificates.dynamic;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.AddressUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.pdf.identitycard.student.StudentIdentityCardGenerator10170_10171;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class GlobalDynamicDocumentGenerator extends DynamicDocumentGenerator {

	public GlobalDynamicDocumentGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	public static final float DEFAULT_LOGO_WIDTH = 75f;
	public static final float DEFAULT_LOGO_HEIGHT = 35f;

	public static final String BLACK_COLOR_HEX_CODE = EColorUtils.lightBlackHexCode;
	public static final String WHITE_COLOR_HEX_CODE = EColorUtils.WHITE_COLOR_HEX_CODE;
	public static final String GREEN_COLOR_HEX_CODE = "#418e45";

	private static final Logger logger = LogManager.getLogger(StudentIdentityCardGenerator10170_10171.class);

	@Override
	public DocumentOutput generateDynamicDocument(StudentManager studentManager, Institute institute,
											   Student student, String documentName, String documentHeader) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute,
					documentOutput);
			Document document = documentLayoutData.getDocument();

			PdfFont cambriaBoldFont = getCambriaBoldFont();
			PdfFont cambriaFont = getCambriaFont();

			generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
					documentLayoutData.getDocumentLayoutSetup(), institute, student, studentManager, 1, documentHeader);
			document.close();
			return documentOutput;
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating idenity cards for institute {}, student {}",
					institute.getInstituteId(), student.getStudentId(), e);
		}
		return null;
	}

	private void generateIdentityCardDetails(Document document, DocumentLayoutData documentLayoutData,
											 PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup, Institute institute, Student student,
											 StudentManager studentManager, int pageNumber, String documentHeader) throws IOException {

		/**
		 * Institute Header
		 */
		generateInstituteHeader(document, documentLayoutData, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute, student, documentHeader, pageNumber);

		/**
		 * Student Details
		 */
		generateStudentDetails(document, cambriaFont, cambriaBoldFont, documentLayoutSetup, student);

		/**
		 * Student Image
		 */
		generateStudentImage(document, documentLayoutSetup, documentLayoutData, institute, studentManager, student);


		/**
		 * Principal Signature
		 */
		generateImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(ImageProvider._10190_PRINCIPAL_SIGNATURE),
				40f, 25f, 75f, 18f);

		/**
		 * Bottom bar
		 */
		generateBottomBar(document, cambriaBoldFont, documentLayoutSetup, cambriaFont, student);
	}

	private void generateStudentImage(Document document, DocumentLayoutSetup documentLayoutSetup, DocumentLayoutData documentLayoutData,
									  Institute institute, StudentManager studentManager, Student student) throws MalformedURLException {

		byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
		float width = 50f;
		float height = 60f;
		if (image != null) {
			generateImage(document, documentLayoutSetup, image, width, height, documentLayoutSetup.getPageSize().getWidth() - 63f,
					30f);
		}
	}

	private void generateStudentDetails(Document document, PdfFont cambriaFont, PdfFont cambriaBoldFont, DocumentLayoutSetup documentLayoutSetup,
										Student student) {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(7f)
				.setTextAlignment(TextAlignment.LEFT).setPaddingLeft(10f).setPaddingTop(0f).setPaddingBottom(0f);

		CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();

		List<Integer> rgb = EColorUtils.hex2Rgb(BLACK_COLOR_HEX_CODE);

		List<Integer> greenRgb = EColorUtils.hex2Rgb(GREEN_COLOR_HEX_CODE);

		centerCellLayoutSetup.setPdfFont(cambriaFont).setTextAlignment(TextAlignment.RIGHT).setPaddingRight(12f).setFontSize(7f);

		Table table = getPDFTable(documentLayoutSetup, new float [] { 0.7f, 0.3f });

		Paragraph classRollNumber = getKeyValueParagraph("Reg No : ",
				student.getStudentBasicInfo().getAdmissionNumber(),
				greenRgb, rgb, cambriaBoldFont, cambriaFont);
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(classRollNumber, cellLayoutSetup.copy().setPaddingTop(10f)),
						new CellData(new Paragraph(""), cellLayoutSetup.copy().setPaddingTop(10f))));

		Paragraph studentName = getKeyValueParagraph("Student Name : ",
				student.getStudentBasicInfo().getName().toUpperCase(), greenRgb, rgb, cambriaBoldFont, cambriaFont);
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, cellLayoutSetup.copy()),
						new CellData(new Paragraph(""), cellLayoutSetup.copy())));

		Paragraph fatherName = getKeyValueParagraph("Father Name : ", student.getStudentFamilyInfo().getFathersName(),
				greenRgb, rgb, cambriaBoldFont, cambriaFont);
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(fatherName, cellLayoutSetup.copy()),
						new CellData(new Paragraph(""), cellLayoutSetup.copy())));

		Paragraph className = getKeyValueParagraph("Class : ",
				student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection() + " (Dance)",
				greenRgb, rgb, cambriaBoldFont, cambriaFont);
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(className, cellLayoutSetup.copy()),
						new CellData(new Paragraph(""), cellLayoutSetup.copy())));

//		Paragraph details = getKeyValueParagraph("", ,
//				greenRgb, rgb, cambriaBoldFont, cambriaFont);
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(new CellData(details, cellLayoutSetup.copy()),
//						new CellData(new Paragraph(""), cellLayoutSetup.copy())));

		Paragraph address = getKeyValueParagraph("Address : ", AddressUtils.getStudentAddress(student),
				greenRgb, rgb, cambriaBoldFont, cambriaFont);
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(address, cellLayoutSetup.copy()),
						new CellData("", cellLayoutSetup.copy())));

		Paragraph contactNumber = getKeyValueParagraph("Mobile : ",
				student.getStudentBasicInfo().getPrimaryContactNumber(),
				greenRgb, rgb, cambriaBoldFont, cambriaFont);
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(contactNumber, cellLayoutSetup.copy()),
						new CellData(new Paragraph(""), cellLayoutSetup.copy())));

		Paragraph principalSign = getKeyValueParagraph("PRINCIPAL SIGN : ","",
				greenRgb, rgb, cambriaBoldFont, cambriaFont);
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(principalSign, cellLayoutSetup.copy().setPaddingTop(6f)),
						new CellData(new Paragraph(""), cellLayoutSetup.copy().setPaddingTop(6f))));

		document.add(table);
	}

	private void generateBottomBar(Document document, PdfFont cambriaBoldFont, DocumentLayoutSetup documentLayoutSetup,
								   PdfFont cambriaFont, Student student) {

		List<Integer> rgbSessionColor = EColorUtils.hex2Rgb(WHITE_COLOR_HEX_CODE);

		Float rgbSessionFontSize = 7f;
		String sessionBackgroundColor = GREEN_COLOR_HEX_CODE;

		Table table = getPDFTable(documentLayoutSetup, 1);
		table.setFixedPosition(0f, 0f, documentLayoutSetup.getPageSize().getWidth());
		CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();
		centerCellLayoutSetup.setPdfFont(cambriaFont).setTextAlignment(TextAlignment.CENTER)
				.setFontSize(rgbSessionFontSize).setPaddingBottom(0f).setPaddingTop(0f);
		centerCellLayoutSetup.setBackgroundColor(sessionBackgroundColor);

		Paragraph session = getParagraph("SESSION : " + student.getStudentAcademicSessionInfoResponse()
				.getAcademicSession().getYearDisplayName(), rgbSessionColor);

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(session,
				centerCellLayoutSetup.copy())));

		document.add(table);

	}

	private void generateInstituteHeader(Document document,
										 DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
										 DocumentLayoutSetup documentLayoutSetup, Institute institute,
										 Student student, String documentHeader, int pageNumber) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();
		centerCellLayoutSetup.setPdfFont(cambriaFont).setTextAlignment(TextAlignment.RIGHT)
				.setPaddingRight(20f);

		List<Integer> documentHeaderColor = EColorUtils.hex2Rgb(BLACK_COLOR_HEX_CODE);

		Float rgbInstituteNameFontSize = 11f;
		addRow(table, documentLayoutSetup, Arrays.asList(
						getParagraph(documentHeader, documentHeaderColor)),
				centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize).setPaddingTop(15f).setPaddingBottom(0f));

		document.add(table);

		generateDynamicImageProvider(documentLayoutData, -8,2, 1f, 1f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);


		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		canvas.moveTo(0, documentLayoutSetup.getPageSize().getHeight() - 38f);
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth(), documentLayoutSetup.getPageSize().getHeight() - 38f);
		canvas.setLineWidth(.5f);
		canvas.closePathStroke();
	}

	protected DocumentLayoutData generateIdentityCardLayoutData(Institute institute,
																DocumentOutput documentOutput)
			throws IOException {

		/**
		 * 	aadhar card size - 8.5cmx5.5cm
		 * 	in inches - 3.34646x2.16535
		 * 	1 inch  = 72 points
		 * 	3.34646*72 & 2.16535*72
		 * 	240.94512f X 155.9052f
		 */
		PageSize pageSize = new PageSize(241f, 156f);
		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize);
		float contentFontSize = 9f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

		float logoWidth = DEFAULT_LOGO_WIDTH;
		float logoHeight = DEFAULT_LOGO_HEIGHT;

//		int instituteId = institute.getInstituteId();
//		instituteId = 10190;
		DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(),
				getRegularBoldFont(),
				contentFontSize, 0f, logoWidth, logoHeight,
				ImageProvider.INSTANCE.getImage(ImageProvider._10190_LOGO_HEADER),
//				LogoProvider.INSTANCE.getLogo(instituteId),
				null, null);

		documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
		return documentLayoutData;
	}

	public DocumentLayoutSetup initDocumentLayoutSetup(PageSize defaultPageSize) {
		return initDocumentLayoutSetup(false, defaultPageSize,
				0f, 0f, 0f, 0f);
	}

	@Override
	public DocumentOutput generateDynamicDocuments(StudentManager studentManager, Institute institute,
												List<Student> students,
												String documentName, String documentHeader) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute,
					documentOutput);
			Document document = documentLayoutData.getDocument();

			PdfFont cambriaBoldFont = getCambriaBoldFont();
			PdfFont cambriaFont = getCambriaFont();

			int pageNumber = 1;
			for (Student student : students) {

				generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
						documentLayoutData.getDocumentLayoutSetup(), institute, student, studentManager,
						pageNumber, documentHeader);

				if (pageNumber != students.size()) {
					documentLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}

			documentLayoutData.getDocument().close();

			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating student identity card institute {}", institute.getInstituteId(), e);
		}
		return null;
	}


}
