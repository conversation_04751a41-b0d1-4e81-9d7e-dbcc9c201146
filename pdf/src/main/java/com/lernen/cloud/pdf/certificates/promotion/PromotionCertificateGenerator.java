/**
 * 
 */
package com.lernen.cloud.pdf.certificates.promotion;

import java.util.List;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.pdf.base.PDFGenerator;

/**
 * <AUTHOR>
 *
 */
public abstract class PromotionCertificateGenerator extends PDFGenerator {

	public PromotionCertificateGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	public abstract DocumentOutput generatePromotionCertificate(Institute institute, Student student,
			String documentName, Standard promotedStandard);

	public abstract DocumentOutput generateStandardPromotionCertificate(Institute institute, List<Student> studentList,
			String documentName, Standard promotedStandard);

	protected void generateMetadata(DocumentLayoutData documentLayoutData, float squareBorderMargin, float innerGap) {
		generateMetadata(documentLayoutData,squareBorderMargin, innerGap,1);
	}

	protected void generateMetadata(DocumentLayoutData documentLayoutData, float squareBorderMargin, float innerGap, int pageNumber) {
		generateBorderLayout(documentLayoutData.getDocument(), documentLayoutData.getDocumentLayoutSetup(), pageNumber,
				squareBorderMargin, innerGap);
	}
}
