/**
 * 
 */
package com.lernen.cloud.pdf.certificates.promotion;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;

/**
 * <AUTHOR>
 *
 */
public class PromotionCertificateGenerator_10190 extends GlobalPromotionCertificateGenerator {
	
	public PromotionCertificateGenerator_10190(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(PromotionCertificateGenerator_10190.class);
	
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 40f;
	
	@Override
	public DocumentOutput generatePromotionCertificate(Institute institute, Student student, String documentName,
			Standard promotedStandard) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			DocumentLayoutData documentLayoutData = generatePromotionCertificateLayoutData(institute, documentOutput, 160f,
					80f);
			DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
			Document document = documentLayoutData.getDocument();
			PdfFont boldFont = getCambriaBoldFont();
			PdfFont regularFont = getCambriaFont();
			float contentFontSize = documentLayoutData.getContentFontSize();

			generateImages(document, documentLayoutSetup, documentLayoutData, institute, student);
			generateHeader(document, documentLayoutSetup, contentFontSize, boldFont, regularFont, student, institute);
			generateContent(document, documentLayoutSetup, contentFontSize, student, institute, promotedStandard, regularFont, boldFont);
			generateSignatureBox(document, documentLayoutSetup, contentFontSize, boldFont, regularFont);

			document.close();
			return documentOutput;

		} catch (Exception e) {
			logger.error("Error while generate promotion certificate for student {} ", student.getStudentBasicInfo().getAdmissionNumber(), e);
		}
		return null;
	}

	protected void generateImages(Document document, DocumentLayoutSetup documentLayoutSetup,
								  DocumentLayoutData documentLayoutData, Institute institute, Student student) throws IOException {
		int instituteId = institute.getInstituteId();
		float bgImageHeightWidth = 500f;

		//Watermark
		generateWatermark(documentLayoutData, institute.getInstituteId(), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 - 50, bgImageHeightWidth / 2 - 80);

		//LOGO
		generateImage(documentLayoutData.getDocument(), documentLayoutData.getDocumentLayoutSetup(),
				ImageProvider.INSTANCE.getImage(ImageProvider._10190_LOGO_HEADER), 350, 130,
				(documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() / 2) - 165,
				documentLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.84f);
	}

	protected void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								  PdfFont cambriaBoldFont, PdfFont cambriaFont, Student student, Institute institute) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER).setFontSize(contentFontSize);

		Table table = getPDFTable(documentLayoutSetup, 1).setTextAlignment(TextAlignment.LEFT);
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Affiliated to CBSE - Delhi")),
//				cellLayoutSetup.copy().setFontSize(contentFontSize - 5).setPdfFont(cambriaFont).setTextAlignment(TextAlignment.LEFT));
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Affiliation No. : 1730661")),
//				cellLayoutSetup.copy().setFontSize(contentFontSize - 5).setPdfFont(cambriaFont).setTextAlignment(TextAlignment.LEFT));
//		document.add(table);

		addBlankLine(document, true, 4);
		addBlankLine(document, false, 2);

		table = getPDFTable(documentLayoutSetup, 1).setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Session : " +
						student.getStudentAcademicSessionInfoResponse().getAcademicSession().getShortYearDisplayName())),
				cellLayoutSetup.copy().setFontSize(contentFontSize));
		document.add(table);
		table = getPDFTable(documentLayoutSetup, 1).setUnderline().setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("PROMOTION CERTIFICATE")),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 2));
		document.add(table);

		addBlankLine(document, true, 3);

	}

	private void generateStandardPromotionCertificate(Institute institute, Student student, String documentName,
            DocumentLayoutData documentLayoutData, Standard promotedStandard, Document document,
            DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
			PdfFont regularFont, PdfFont boldFont) throws IOException {

        generateImages(document, documentLayoutSetup, documentLayoutData, institute, student);
        generateHeader(document, documentLayoutSetup, contentFontSize, boldFont, regularFont, student, institute);
        generateContent(document, documentLayoutSetup, contentFontSize, student, institute, promotedStandard, regularFont, boldFont);
        generateSignatureBox(document, documentLayoutSetup, contentFontSize, boldFont, regularFont);

	}
}
