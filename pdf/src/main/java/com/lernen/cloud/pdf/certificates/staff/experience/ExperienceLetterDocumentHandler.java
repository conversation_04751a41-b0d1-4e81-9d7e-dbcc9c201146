package com.lernen.cloud.pdf.certificates.staff.experience;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.staff.FullStaffDetails;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.staff.StaffManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.UUID;


public class ExperienceLetterDocumentHandler {
    private static final Logger logger = LogManager.getLogger(ExperienceLetterDocumentHandler.class);
    private final StaffManager staffManager;
	private final InstituteManager instituteManager;
	private final ExperienceLetterGeneratorFactory experienceLetterGeneratorFactory;


	public ExperienceLetterDocumentHandler(StaffManager staffManager, InstituteManager instituteManager, AssetProvider assetProvider) {
		this.experienceLetterGeneratorFactory = new ExperienceLetterGeneratorFactory(assetProvider);
		this.staffManager = staffManager;
		this.instituteManager = instituteManager;
	}

	public DocumentOutput generateExperienceLetter(int instituteId, UUID staffId) {
		if (instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}

		final Institute institute = instituteManager.getInstitute(instituteId);
		if (institute == null) {
			logger.error("Invalid institute");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to get institute"));
		}

		if (staffId == null) {
			logger.error("Invalid staff id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid staff id"));
		}

		final FullStaffDetails fullStaffDetails = staffManager.getFullStaffDetails(instituteId, staffId);

		if (fullStaffDetails == null) {
			logger.error("Invalid staff");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Unable to get staff details"));
		}

//		if (fullStaffDetails.getStaffStatus() != StaffStatus.RELIEVED) {
//			logger.error("Experience letter can only be generated for relieved staff.");
//			throw new ApplicationException(
//					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Experience letter can only be generated for relieved staff."));
//		}

		ExperienceLetterGenerator experienceLetterGenerator = experienceLetterGeneratorFactory
				.getExperienceLetterGenerator(instituteId);
		String fileName = fullStaffDetails.getStaffBasicDetailsWithCategoryDepartDesignation()
				.getStaffInstituteId() + "_Experience_Letter.pdf";
		
		return experienceLetterGenerator.generateExperienceLetter(institute, fullStaffDetails, fileName, staffManager);
	}
}
