/**
 * 
 */
package com.lernen.cloud.pdf.certificates.study;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.DocumentPropertiesPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;

/**
 * <AUTHOR>
 *
 */
public class StudyCertificateGenerator10130 extends GlobalStudyCertificateGenerator {
	
	public StudyCertificateGenerator10130(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(StudyCertificateGenerator10130.class);

	public static final float DEFAULT_PAGE_SIDE_MARGIN = 50f;
	public static final float DEFAULT_TABLE_BORDER_WIDTH = 0.5f;
	public static final float LOGO_WIDTH = 75f;
	public static final float LOGO_HEIGHT = 75f;
	public static final String NA = "NA";

	private static final String CONTENT_1 = "This is to certify that ";
	private static final String CONTENT_2 = "%s ";//STUDENT NAME
	private static final String CONTENT_3 = "%s";//(S/D/)
	private static final String CONTENT_4 = "o ";
	private static final String CONTENT_5 = "Shri %s ";//FATHER NAME
	private static final String CONTENT_6 = "%s";//(S/D/)
	private static final String CONTENT_7 = "o ";
	private static final String CONTENT_8 = "Smt %s ";//MOTHER NAME
	private static final String CONTENT_9 = "Date of Birth : ";
	private static final String CONTENT_10 = "%s ";//Date of birth
	private static final String CONTENT_9_1 = "Date of Birth (In Words) : ";
	private static final String CONTENT_10_1 = "%s ";//Date of birth in words
	private static final String CONTENT_11 = "Category : ";
	private static final String CONTENT_12 = "%s ";//Category
	private static final String CONTENT_13 = "Gender : ";
	private static final String CONTENT_14 = "%s ";//Gender
	private static final String CONTENT_15 = "Admission Number : ";
	private static final String CONTENT_16 = "%s ";//S.R.No.
	private static final String CONTENT_17 = "Class : ";
	private static final String CONTENT_18 = "%s ";//Class with section
	private static final String CONTENT_19 = "R/o ";
	private static final String CONTENT_20 = "%s";//address
//	private static final String CONTENT_21 = ", is currently studying in this school as a regular student.\nThis certificate is signed and the photo of the student is attested by the principal and marked with the seal of the school.\n\n";
	private static final String CONTENT_21 = ", is currently studying in this school as a regular student.";
//	private static final String CONTENT_22 = "Date : ";
//	private static final String CONTENT_23 = "%s ";//current date

	//	private static final String SIGNATURE_1 = "Signature of Student";
//	private static final String SIGNATURE_2 = "Signature of Class Teacher";
//	private static final String SIGNATURE_1 = "";
//	private static final String SIGNATURE_2 = "";
//	private static final String SIGNATURE_3 = "HeadMaster/Principal";
//	private static final String SIGNATURE_4 = "(Seal & Signature)";

	private static final String SIGNATURE_1 = "Date : ";
	private static final String SIGNATURE_2 = "%s ";//current date
	private static final String SIGNATURE_3 = "Authorised Signatory";

	@Override
	public DocumentOutput generateStudyCertificate(Institute institute, Student student,
												   String documentName, StudentManager studentManager, DocumentPropertiesPreferences documentPropertiesPreferences) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			DocumentLayoutData documentLayoutData = generateStudyCertificateLayoutData(institute, documentOutput, 70f,
					70f, documentPropertiesPreferences);
			DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
			Document document = documentLayoutData.getDocument();
			PdfFont boldFont = getCambriaBoldFont();
			PdfFont regularFont = getCambriaFont();
			float contentFontSize = documentLayoutData.getContentFontSize();

			generateImages(document, documentLayoutSetup, documentLayoutData, institute, student, studentManager, documentPropertiesPreferences);
			generateHeader(document, documentLayoutSetup, contentFontSize, boldFont, regularFont, student, institute, documentPropertiesPreferences);
			generateContent(document, documentLayoutSetup, contentFontSize, student, boldFont, regularFont);
			generateSignatureBox(document, documentLayoutSetup, contentFontSize, boldFont, regularFont);

			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generate study certificate for student {} ", student.getStudentBasicInfo().getAdmissionNumber(), e);
		}
		return null;
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

//	protected void generateImages(Document document, DocumentLayoutSetup documentLayoutSetup,
//								  DocumentLayoutData documentLayoutData, Institute institute, Student student,
//								  StudentManager studentManager) throws IOException {
//		int instituteId = institute.getInstituteId();
//		float bgImageHeightWidth = 500f;
//
//		//Watermark
//		generateWatermark(documentLayoutData, institute.getInstituteId(), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 - 50, bgImageHeightWidth / 2 - 80);
//
//		//LOGO
//		generateDynamicImageProvider(documentLayoutData, 30f, documentLayoutSetup.getPageSize().getHeight() * 0.75f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
//
//		//Student Image
//		byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
//		if (image == null) {
//			image = documentLayoutData.getImageFrame();
//		}
//		generateImage(document, documentLayoutSetup, image, 80, 90,
//				documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 120f,
//				documentLayoutSetup.getPageSize().getHeight() * 0.75f);
//	}

//	protected void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
//								  PdfFont cambriaBoldFont, PdfFont cambriaFont, Student student, Institute institute) throws IOException {
//
//		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
//		cellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER).setFontSize(contentFontSize);
//
//		Table table = getPDFTable(documentLayoutSetup, 1).setTextAlignment(TextAlignment.CENTER);
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName())),
//				cellLayoutSetup.copy().setFontSize(contentFontSize + 2));
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
//				cellLayoutSetup.copy().setFontSize(contentFontSize - 3));
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
//				cellLayoutSetup.setFontSize(contentFontSize - 3));
//		document.add(table);
//
//		addBlankLine(document, true, 2);
//
//		table = getPDFTable(documentLayoutSetup, 1).setUnderline().setTextAlignment(TextAlignment.CENTER);
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Student Certificate")),
//				cellLayoutSetup.copy().setFontSize(contentFontSize + 2));
//		document.add(table);
//
//		addBlankLine(document, true, 3);
//
//
//	}

	protected void generateContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								   Student student, PdfFont cambriaBoldFont, PdfFont cambriaFont) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, 1);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setFontSize(contentFontSize - 1).setTextAlignment(TextAlignment.JUSTIFIED);

		String studentName = student.getStudentBasicInfo().getName().toUpperCase();
		String sonDaughter = getSonDaughter(student.getStudentBasicInfo().getGender());
		String fatherName = student.getStudentFamilyInfo() == null ? NA :
				StringUtils.isBlank(student.getStudentFamilyInfo().getFathersName()) ? NA :
						student.getStudentFamilyInfo().getFathersName().toUpperCase();
		String motherName = student.getStudentFamilyInfo() == null ? NA :
				StringUtils.isBlank(student.getStudentFamilyInfo().getMothersName()) ? NA :
						student.getStudentFamilyInfo().getMothersName().toUpperCase();
		String dob = student.getStudentBasicInfo().getDateOfBirth() == null ? NA :
				DateUtils.getFormattedDate(student.getStudentBasicInfo().getDateOfBirth());
		String dobInWords = student.getStudentBasicInfo().getDateOfBirth() == null ? NA :
				DateUtils.getDateInWords(student.getStudentBasicInfo().getDateOfBirth());
		String category = student.getStudentBasicInfo().getUserCategory() == null ? NA :
				student.getStudentBasicInfo().getUserCategory().name().toUpperCase();
		String gender = getGender(student.getStudentBasicInfo().getGender());
		String srNumber = student.getStudentBasicInfo().getAdmissionNumber();
		String displayClassName = student.getStudentAcademicSessionInfoResponse() == null ? NA :
				student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection();
		String address = student.getStudentBasicInfo().getStudentFullAddress();
		String currentDate = DateUtils.getFormattedDate(DateUtils.now());

		Text t1 = new Text(String.format(CONTENT_1)).setFont(cambriaFont);
		Text t2 = new Text(String.format(CONTENT_2, studentName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t3 = new Text(String.format(CONTENT_3, sonDaughter)).setFont(cambriaFont);
		Text t4 = new Text(String.format(CONTENT_4)).setFont(cambriaFont);
		Text t5 = new Text(String.format(CONTENT_5, fatherName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t6 = new Text(String.format(CONTENT_6, sonDaughter)).setFont(cambriaFont);
		Text t7 = new Text(String.format(CONTENT_7)).setFont(cambriaFont);
		Text t8 = new Text(String.format(CONTENT_8, motherName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t9 = new Text(String.format(CONTENT_9)).setFont(cambriaFont);
		Text t10 = new Text(String.format(CONTENT_10, dob)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t9_1 = new Text(String.format(CONTENT_9_1)).setFont(cambriaFont);
		Text t10_1 = new Text(String.format(CONTENT_10_1, dobInWords)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t11 = new Text(String.format(CONTENT_11)).setFont(cambriaFont);
		Text t12 = new Text(String.format(CONTENT_12, category)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t13 = new Text(String.format(CONTENT_13)).setFont(cambriaFont);
		Text t14 = new Text(String.format(CONTENT_14, gender)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t15 = new Text(String.format(CONTENT_15)).setFont(cambriaFont);
		Text t16 = new Text(String.format(CONTENT_16, srNumber)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t17 = new Text(String.format(CONTENT_17)).setFont(cambriaFont);
		Text t18 = new Text(String.format(CONTENT_18, displayClassName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t19 = new Text(String.format(CONTENT_19)).setFont(cambriaFont);
		Text t20 = new Text(String.format(CONTENT_20, address)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t21 = new Text(String.format(CONTENT_21)).setFont(cambriaFont);
//		Text t22 = new Text(String.format(CONTENT_22)).setFont(cambriaFont);
//		Text t23 = new Text(String.format(CONTENT_23, currentDate)).setFont(cambriaBoldFont).setFontSize(contentFontSize);


		Paragraph p1 = new Paragraph();
		p1.add(t1).add(t2).add(t3).add(t4).add(t5).add(t6).add(t7).add(t8).add(t9).add(t10).add(t9_1).add(t10_1).add(t11).add(t12)
				.add(t13).add(t14).add(t15).add(t16).add(t17).add(t18).add(t19).add(t20).add(t21);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p1, cellLayoutSetup)));
		document.add(table);

		addBlankLine(document, true, 3);
	}

	protected String getSonDaughter(Gender gender) {
		if (gender == Gender.FEMALE) {
			return "D/";
		} else if (gender == Gender.MALE) {
			return "S/";
		}
		return "S/D/";

	}

	protected String getGender(Gender gender) {
		if (gender == Gender.FEMALE) {
			return "GIRL";
		} else if (gender == Gender.MALE) {
			return "BOY";
		}
		return "BOY/GIRL";

	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
										PdfFont cambriaBoldFont, PdfFont cambriaFont) throws IOException {
		Table table = getPDFTable(documentLayoutSetup, 2);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(cambriaFont).setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph(""), signatureCellLayoutSetup),
				new CellData(getParagraph(""), signatureCellLayoutSetup)));
		document.add(table);
		addBlankLine(document, true, 2);
		table = getPDFTable(documentLayoutSetup, 2);
		signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup signatureRightCellLayoutSetup = new CellLayoutSetup();
		signatureRightCellLayoutSetup.setTextAlignment(TextAlignment.RIGHT);
		String currentDate = DateUtils.getFormattedDate(DateUtils.now());
		Text t1 = new Text(SIGNATURE_1).setFont(cambriaFont).setFontSize(contentFontSize);
		Text t2 = new Text(String.format(SIGNATURE_2, currentDate)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t3 = new Text(SIGNATURE_3).setFont(cambriaFont).setFontSize(contentFontSize);
		Paragraph paragraph1 = new Paragraph();
		paragraph1.add(t1).add(t2);
		Paragraph paragraph2 = new Paragraph();
		paragraph2.add(t3);
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(paragraph1, signatureCellLayoutSetup),
				new CellData(paragraph2, signatureRightCellLayoutSetup)));
		document.add(table);
	}

}
