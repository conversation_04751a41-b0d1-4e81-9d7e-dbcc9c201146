/**
 * 
 */
package com.lernen.cloud.pdf.certificates.study;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.DocumentPropertiesPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;

/**
 * <AUTHOR>
 *
 */
public class StudyCertificateGenerator10190 extends GlobalStudyCertificateGenerator {
	
	public StudyCertificateGenerator10190(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(StudyCertificateGenerator10190.class);

	public static final float DEFAULT_PAGE_SIDE_MARGIN = 40f;
	public static final float LOGO_WIDTH = 160f;
	public static final float LOGO_HEIGHT = 80f;

	@Override
	public DocumentOutput generateStudyCertificate(Institute institute, Student student,
												   String documentName, StudentManager studentManager, DocumentPropertiesPreferences documentPropertiesPreferences) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			DocumentLayoutData documentLayoutData = generateStudyCertificateLayoutData(institute, documentOutput, LOGO_WIDTH,
					LOGO_HEIGHT, documentPropertiesPreferences);
			DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
			Document document = documentLayoutData.getDocument();
			PdfFont boldFont = getCambriaBoldFont();
			PdfFont regularFont = getCambriaFont();
			float contentFontSize = documentLayoutData.getContentFontSize();

			generateImages(document, documentLayoutSetup, documentLayoutData, institute, student, studentManager);
			generateHeader(document, documentLayoutSetup, contentFontSize, boldFont, regularFont, student, institute);
			generateContent(document, documentLayoutSetup, contentFontSize, student, boldFont, regularFont);
			generateSignatureBox(document, documentLayoutSetup, contentFontSize, boldFont, regularFont);

			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generate study certificate for student {} ", student.getStudentBasicInfo().getAdmissionNumber(), e);
		}
		return null;
	}


	protected void generateImages(Document document, DocumentLayoutSetup documentLayoutSetup,
								  DocumentLayoutData documentLayoutData, Institute institute, Student student,
								  StudentManager studentManager) throws IOException {
		int instituteId = institute.getInstituteId();
		float bgImageHeightWidth = 500f;

		//Watermark
		generateWatermark(documentLayoutData, institute.getInstituteId(), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 - 50, bgImageHeightWidth / 2 - 80);

		//LOGO
		generateImage(documentLayoutData.getDocument(), documentLayoutData.getDocumentLayoutSetup(),
				ImageProvider.INSTANCE.getImage(ImageProvider._10190_LOGO_HEADER), 350, 130,
				(documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() / 2) - 165,
				documentLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.84f);

//		generateLogo(document, documentLayoutSetup, documentLayoutData, institute,
//				ImageProvider.INSTANCE.getImage(ImageProvider._10190_LOGO_HEADER),
//		(documentLayoutSetup.getPageSize().getWidth() / 2) - (documentLayoutData.getLogoWidth() / 2) + 20,
//		documentLayoutSetup.getPageSize().getHeight() * 0.88f);

		//Student Image
		byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
		if (image == null) {
			image = documentLayoutData.getImageFrame();
		}
		generateImage(document, documentLayoutSetup, image, 80, 90,
				documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 120f,
				documentLayoutSetup.getPageSize().getHeight() * 0.75f);

	}

	protected void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								  PdfFont cambriaBoldFont, PdfFont cambriaFont, Student student, Institute institute) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER).setFontSize(contentFontSize);

		Table table = getPDFTable(documentLayoutSetup, 1).setTextAlignment(TextAlignment.LEFT);
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Affiliated to CBSE - Delhi")),
//				cellLayoutSetup.copy().setFontSize(contentFontSize - 5).setPdfFont(cambriaFont).setTextAlignment(TextAlignment.LEFT));
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Affiliation No. : 1730661")),
//				cellLayoutSetup.copy().setFontSize(contentFontSize - 5).setPdfFont(cambriaFont).setTextAlignment(TextAlignment.LEFT));
//		document.add(table);

		addBlankLine(document, true, 4);
		addBlankLine(document, false, 2);

		table = getPDFTable(documentLayoutSetup, 1).setUnderline().setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("STUDY CERTIFICATE")),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 2));
		document.add(table);

		addBlankLine(document, true, 3);

	}
}
