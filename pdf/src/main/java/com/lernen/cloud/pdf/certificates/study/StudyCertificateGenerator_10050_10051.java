/**
 * 
 */
package com.lernen.cloud.pdf.certificates.study;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.DocumentPropertiesPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

/**
 * <AUTHOR>
 *
 */
public class StudyCertificateGenerator_10050_10051 extends GlobalStudyCertificateGenerator {

	public StudyCertificateGenerator_10050_10051(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(StudyCertificateGenerator_10050_10051.class);
	
	public static final float HEADER_HEIGHT = 150f;
	public static final float FOOTER_HEIGHT = 100f;
	public static final float HEADER_Y_POSITION = 692;
	public static final float FOOTER_Y_POSITION = 0;
	public static final float X_POSITIONS = 0;

	private static final String STUDY_CERTIFICATE_CONTENT_PARAGRAPH_1_1 = "This is to certify that ";
	private static final String STUDENT_NAME = "%s ";
	private static final String SON_DAUGHTER = "%s ";
	private static final String STUDY_CERTIFICATE_CONTENT_PARAGRAPH_1_2 = "of ";
	private static final String FATHER_NAME = "Shri %s ";
	private static final String STUDY_CERTIFICATE_CONTENT_PARAGRAPH_1_3 = "and ";
	private static final String MOTHER_NAME = "Smt. %s ";
	private static final String STUDY_CERTIFICATE_CONTENT_PARAGRAPH_1_4 = "whose Admission No. is ";
	private static final String ADMISSION_NUMBER = "%s ";
	private static final String STUDY_CERTIFICATE_CONTENT_PARAGRAPH_1_5 = "and Date of Birth is (In Figures) ";
	private static final String DOB_FIGURES = "%s ";
	private static final String STUDY_CERTIFICATE_CONTENT_PARAGRAPH_1_6 = "(In words) ";
	private static final String DOB_WORDS =  "%s ";
	private static final String STUDY_CERTIFICATE_CONTENT_PARAGRAPH_1_7 = "has been the student of school ";

	private static final String STUDY_CERTIFICATE_CONTENT_PARAGRAPH_2_1 = "%s %s in Class ";
	private static final String CLASS = "%s ";
	private static final String STUDY_CERTIFICATE_CONTENT_PARAGRAPH_2_2 = "in Session ";
	private static final String SESSION = "%s.";

	private static final String STUDY_CERTIFICATE_CONTENT_PARAGRAPH_3 = "%s is a hard-working and diligent student with a great sense of respect for teachers and elders. %s %s good moral character to the best of our knowledge. ";
	private static final String STUDY_CERTIFICATE_CONTENT_PARAGRAPH_4 = "We wish %s a bright future ahead.";

	@Override
	public DocumentOutput generateStudyCertificate(Institute institute, Student student, String documentName, StudentManager studentManager, DocumentPropertiesPreferences documentPropertiesPreferences) {
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
			float contentFontSize = 12f;

			Document document = initDocument(invoice.getContent(), documentLayoutSetup);

			generateImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(ImageProvider._10050_10051_HEADER),
					documentLayoutSetup.getPageSize().getWidth(), HEADER_HEIGHT, X_POSITIONS, HEADER_Y_POSITION);
			
			generateContent(document, documentLayoutSetup, contentFontSize, student, institute);
			
			generateSignatureBox(document, documentLayoutSetup, contentFontSize);
			
			generateImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(ImageProvider._10050_10051_FOOTER),
					documentLayoutSetup.getPageSize().getWidth(), FOOTER_HEIGHT, X_POSITIONS, FOOTER_Y_POSITION);

			document.close();
			return invoice;
		} catch (Exception e) {
			logger.error("Error while generate study certificate for student {} ", student.getStudentBasicInfo().getAdmissionNumber(), e);
		}
		return null;
	}

	public void generateContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
			Student student, Institute institute) throws IOException {

		addBlankLine(document, false, 1);
		addBlankLine(document, true, 5);

		int singleContentColumn = 1;
		Table table4 = new Table(singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		float contentWidth = getContentWidth(documentLayoutSetup);
		signatureCellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(getRegularBoldFont())
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.CENTER);

		addRow(table4, documentLayoutSetup, Arrays.asList(getParagraph("STUDY CERTIFICATE")), signatureCellLayoutSetup.setFontSize(18f));
		addBlankLine(document, true, 1);
		addBlankLine(document, false, 1);
		document.add(table4);

		addBlankLine(document, true, 1);
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		Table table = new Table(columnCount);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setWidth(contentWidth / singleContentColumn);
		Paragraph date = getKeyValueParagraph("Date : ", new SimpleDateFormat("dd-MM-yyyy").format(new Date())).setFontSize(14f);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(date, cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);

		document.add(table);

		columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		table = new Table(columnCount);

		cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setWidth(contentWidth / singleContentColumn)
				.setFontSize(contentFontSize + 4).setTextAlignment(TextAlignment.JUSTIFIED);

		String heShe = "He/She";
		String himHer = "Him/Her";
		if (student.getStudentBasicInfo().getGender() == Gender.FEMALE) {
			heShe = "She";
			himHer = "Her";
		} else if (student.getStudentBasicInfo().getGender() == Gender.MALE) {
			heShe = "He";
			himHer = "Him";
		}

		String studyingStudied = "Studying/Studied";
		String bearsBeared = "Bears/Beared";

		if(student.getStudentStatus() == StudentStatus.RELIEVED) {
			studyingStudied = "had studied";
			bearsBeared = "beared";
		}
		else {
			studyingStudied = "is studying";
			bearsBeared = "bears";
		}

		String admissionDate = student.getStudentBasicInfo().getAdmissionDate() == null ? "" :
				DateUtils.getFormattedDate(student.getStudentBasicInfo().getAdmissionDate());
		String startYear = "";
		String endYear = "";

		DateTimeFormatter formatter = DateTimeFormat.forPattern("dd/MMM/yyyy");

		if(!StringUtils.isEmpty(admissionDate)) {
			LocalDate dateTime = formatter.parseLocalDate(admissionDate);
			if(dateTime.getMonthOfYear() >= 4) {
				startYear += dateTime.getYear();
				endYear += Integer.valueOf(startYear) + 1;
			}
			else {
				endYear += dateTime.getYear();
				startYear += Integer.valueOf(startYear) - 1;
			}
		}

		Text t1 = new Text(String.format(STUDY_CERTIFICATE_CONTENT_PARAGRAPH_1_1));
		Text t2 = new Text(String.format(STUDENT_NAME, student.getStudentBasicInfo().getName())).setFont(getRegularBoldFont());
		Text t3 = new Text(String.format(SON_DAUGHTER, getSonDaughter(student.getStudentBasicInfo().getGender())));
		Text t4 = new Text(String.format(STUDY_CERTIFICATE_CONTENT_PARAGRAPH_1_2));
		Text t5 = new Text(String.format(FATHER_NAME, student.getStudentFamilyInfo() == null ? "" :
				StringUtils.isEmpty(student.getStudentFamilyInfo().getFathersName()) ? "" :
						student.getStudentFamilyInfo().getFathersName())).setFont(getRegularBoldFont());
		Text t6 = new Text(String.format(STUDY_CERTIFICATE_CONTENT_PARAGRAPH_1_3));
		Text t7 = new Text(String.format(MOTHER_NAME, student.getStudentFamilyInfo() == null ? "" :
				StringUtils.isEmpty(student.getStudentFamilyInfo().getMothersName()) ? "" :
						student.getStudentFamilyInfo().getMothersName())).setFont(getRegularBoldFont());
		Text t8 = new Text(String.format(STUDY_CERTIFICATE_CONTENT_PARAGRAPH_1_4));
		Text t9 = new Text(String.format(ADMISSION_NUMBER, student.getStudentBasicInfo().getAdmissionNumber())).setFont(getRegularBoldFont());
		Text t10 = new Text(String.format(STUDY_CERTIFICATE_CONTENT_PARAGRAPH_1_5));
		Text t11 = new Text(String.format(DOB_FIGURES, student.getStudentBasicInfo().getDateOfBirth() == null ? "" :
				DateUtils.getFormattedDate(student.getStudentBasicInfo().getDateOfBirth(), DATE_FORMAT,
						User.DFAULT_TIMEZONE))).setFont(getRegularBoldFont());
		Text t12 = new Text(String.format(STUDY_CERTIFICATE_CONTENT_PARAGRAPH_1_6));
		Text t13= new Text(String.format(DOB_WORDS, student.getStudentBasicInfo().getDateOfBirth() == null ? "" :
				DateUtils.getDateInWords(student.getStudentBasicInfo().getDateOfBirth()))).setFont(getRegularBoldFont());
		Text t14 = new Text(String.format(STUDY_CERTIFICATE_CONTENT_PARAGRAPH_1_7));

		Text t15 = new Text("");
		Text t16 = new Text("");
		Text t17 = new Text("");
		Text t18 = new Text("");
		Text t19 = new Text("");
		if(startYear.equalsIgnoreCase(String.valueOf(student.getStudentAcademicSessionInfoResponse().getAcademicSession().getStartYear()))
				|| endYear.equalsIgnoreCase(String.valueOf(student.getStudentAcademicSessionInfoResponse().getAcademicSession().getEndYear()))) {
			t15 = new Text("in ");
			t16 = new Text(startYear + "-" + endYear).setFont(getRegularBoldFont());
			t17 = new Text(".");
		}
		else {
			t15 = new Text("from ");
			t16 = new Text(startYear + "-" + endYear).setFont(getRegularBoldFont());
			t17 = new Text(" to ");
			t18 = new Text(student.getStudentAcademicSessionInfoResponse().getAcademicSession().getYearDisplayName()).setFont(getRegularBoldFont());
			t19 = new Text(".");
		}
		Paragraph p1 = new Paragraph().setFixedLeading(25f);
		p1.add(t1).add(t2).add(t3).add(t4).add(t5).add(t6).add(t7).add(t8).add(t9).add(t10).add(t11).add(t12).add(t13).add(t14).add(t15).add(t16).add(t17).add(t18).add(t19);


		Text t21 = new Text(String.format(STUDY_CERTIFICATE_CONTENT_PARAGRAPH_2_1, heShe, studyingStudied));
		Text t22 = new Text(String.format(CLASS, student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayName())).setFont(getRegularBoldFont());
		Text t23 = new Text(String.format(STUDY_CERTIFICATE_CONTENT_PARAGRAPH_2_2));
		Text t24 = new Text(String.format(SESSION, student.getStudentAcademicSessionInfoResponse().getAcademicSession().getYearDisplayName())).setFont(getRegularBoldFont());
		Paragraph p2 = new Paragraph().setFixedLeading(25f);
		p2.add(t21).add(t22).add(t23).add(t24);


		Text t31 = new Text(String.format(STUDY_CERTIFICATE_CONTENT_PARAGRAPH_3, heShe, heShe, bearsBeared));
		Paragraph p3 = new Paragraph().setFixedLeading(25f);
		p3.add(t31);


		Text t41 = new Text(String.format(STUDY_CERTIFICATE_CONTENT_PARAGRAPH_4, himHer.toLowerCase()));
		Paragraph p4 = new Paragraph().setFixedLeading(25f);
		p4.add(t41);

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p1, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p2, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p3, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p4, cellLayoutSetup)));

		document.add(table);
	}

	protected String getSonDaughter(Gender gender) {
		if (gender == Gender.FEMALE) {
			return "daughter";
		} else if (gender == Gender.MALE) {
			return "son";
		}
		return "son/daughter";

	}

	public void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize)
			throws IOException {
		int singleContentColumn = 1;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		Table table = new Table(columnCount);

		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(getRegularBoldFont())
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT);

		addBlankLine(document, true, 1);
		addBlankLine(document, true, 1);
		addBlankLine(document, true, 1);
		addBlankLine(document, true, 1);
		addBlankLine(document, true, 1);

		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData("HEADMASTER", signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT).setFontSize(16f))));
		document.add(table);
	}


}
