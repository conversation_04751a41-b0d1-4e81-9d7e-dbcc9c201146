package com.lernen.cloud.pdf.certificates.transfer;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.*;
import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;

public class CBSETransferCertificateGenerator extends GlobalTransferCertificateGenerator {

    public CBSETransferCertificateGenerator(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final Logger logger = LogManager.getLogger(CBSETransferCertificateGenerator.class);

    public static final float SQUARE_BORDER_MARGIN = 12f;

    @Override
    public DocumentOutput generateTransferCertificate(StudentManager studentManager, Institute institute, Student student,
                                                      String documentName) {
        try {
            float squareBorderMargin = 8f;
            float borderInnerGap = 2f;

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            DocumentLayoutData admissionFormLayoutData = generateTransferCertificateLayoutData(institute, documentOutput,
                    60f, 60f);
            Document document = admissionFormLayoutData.getDocument();
            int index = 1;

            addBlankLine(document, false, 1);
            StudentTransferCertificateDetails studentTransferCertificateDetails = student.getStudentTransferCertificateDetails();
            generatePageHeader(admissionFormLayoutData, institute, studentTransferCertificateDetails);

            addBlankLine(document, false, 1);

            index = generateBasicInformationPage(admissionFormLayoutData, studentManager, institute,
                    studentTransferCertificateDetails, index, true, student);

            generateSignatureBox(admissionFormLayoutData, squareBorderMargin, borderInnerGap);

            generateMetadata(admissionFormLayoutData, squareBorderMargin, borderInnerGap);

            document.close();

            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating admission form for institute {}, student id {}",
                    institute.getInstituteId(), student.getStudentId(), e);
        }
        return null;
    }

    protected DocumentLayoutData generateTransferCertificateLayoutData(Institute institute, DocumentOutput documentOutput,
                                                                       float logoWidth, float logoHeight) throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false, PageSize.A4, 5f, 5f,
                20f, 0f);
        float contentFontSize = 12f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        int instituteId = institute.getInstituteId();
        DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getCambriaFont(),
                getCambriaBoldFont(), contentFontSize, defaultBorderWidth, logoWidth, logoHeight,
                LogoProvider.INSTANCE.getLogo(instituteId), null, null);
        documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
        return documentLayoutData;
    }
    protected void generatePageHeader(DocumentLayoutData documentLayoutData, Institute institute,
                                      StudentTransferCertificateDetails studentTransferCertificateDetails, float schoolNameFontSize) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();
        PdfFont regularFont = documentLayoutData.getRegularFont();

        generateDynamicImageProvider(documentLayoutData, 20f, documentLayoutSetup.getPageSize().getHeight() * 0.87f,
                1.5f, 1.5f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

        float bgImageHeightWidth = 500f;
        //Institute watermark
        int instituteId = institute.getInstituteId();
        generateWatermark(documentLayoutData, instituteId, bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 - 50, bgImageHeightWidth / 2 - 80);

//        generateImage(document, documentLayoutSetup, documentLayoutData.getLogo(), documentLayoutData.getLogoWidth(),
//                documentLayoutData.getLogoHeight(), 20f,
//                documentLayoutSetup.getPageSize().getHeight() * 0.87f, 1.5f, 1.5f);

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(regularFont).setFontSize(
                documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.CENTER);


        addRow(table, documentLayoutSetup, Arrays.asList(
            getParagraph(institute.getInstituteName().toUpperCase(), EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB)),
                cellLayoutSetup.copy().setFontSize(schoolNameFontSize).setPdfFont(boldFont));

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
                cellLayoutSetup.copy().setFontSize(documentLayoutData.getContentFontSize()-2f));

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
                cellLayoutSetup.copy().setFontSize(documentLayoutData.getContentFontSize()-2f));

        document.add(table);

        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("TRANSFER CERTIFICATE").setUnderline()),
                cellLayoutSetup.copy().setPdfFont(boldFont));

        document.add(table);

    }
    protected void generatePageHeader(DocumentLayoutData documentLayoutData, Institute institute,
                                      StudentTransferCertificateDetails studentTransferCertificateDetails) throws IOException {
        generatePageHeader(documentLayoutData, institute, studentTransferCertificateDetails, documentLayoutData.getContentFontSize() + 2);
    }

    protected int generateBasicInformationPage(DocumentLayoutData documentLayoutData, StudentManager studentManager,
            Institute institute, StudentTransferCertificateDetails studentTransferCertificateDetails, int index,
                                               boolean addSchoolInfoData, Student student) throws IOException {

        PdfFont boldFont = documentLayoutData.getBoldFont();
        PdfFont regularFont = documentLayoutData.getRegularFont();

        int headlineContentColumn = 3;
        Table headlineTable = getPDFTable(documentLayoutData.getDocumentLayoutSetup(), headlineContentColumn);
        CellLayoutSetup headlineCellLayoutSetup = new CellLayoutSetup();
        headlineCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize() - 1)
                .setTextAlignment(TextAlignment.LEFT).setPdfFont(regularFont);

        String penNumber = studentTransferCertificateDetails.getTcSchoolDetails() == null ? student.getStudentBasicInfo().getPenNumber() :
                studentTransferCertificateDetails.getTcSchoolDetails().getPrimaryEducationAffiliationNumber() == null ? student.getStudentBasicInfo().getPenNumber(): studentTransferCertificateDetails.getTcSchoolDetails().getPrimaryEducationAffiliationNumber();

        if(addSchoolInfoData) {
            TCSchoolDetails tcSchoolDetails = studentTransferCertificateDetails.getTcSchoolDetails();
            addRow(headlineTable, documentLayoutData.getDocumentLayoutSetup(), Arrays.asList(
                    new CellData(getKeyValueParagraph("School Code: ", tcSchoolDetails.getSchoolCode(), regularFont, regularFont),
                            headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
                    new CellData(getKeyValueParagraph("PEN Number : ", penNumber, regularFont, regularFont),
                            headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
                    new CellData(getKeyValueParagraph("Affiliation No: ", tcSchoolDetails.getAffiliationNumber(), regularFont, regularFont),
                            headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));
            documentLayoutData.getDocument().add(headlineTable);
        }

        headlineContentColumn = 3;
        headlineTable = getPDFTable(documentLayoutData.getDocumentLayoutSetup(), headlineContentColumn);

        headlineCellLayoutSetup = new CellLayoutSetup();
        headlineCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize() - 1).setTextAlignment(TextAlignment.LEFT).setPdfFont(regularFont);

        TCStudentDetails tcStudentDetails = studentTransferCertificateDetails.getTcStudentDetails();
        addRow(headlineTable, documentLayoutData.getDocumentLayoutSetup(), Arrays.asList(
                new CellData(getKeyValueParagraph("TC Number: " , studentTransferCertificateDetails.getTcNumber(), regularFont, regularFont), headlineCellLayoutSetup),
                new CellData(getKeyValueParagraph("", "", regularFont, regularFont), headlineCellLayoutSetup),
                new CellData(getKeyValueParagraph("S.R. No: ", tcStudentDetails.getAdmissionNumber(), regularFont, regularFont),
                        headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))
        ));
        documentLayoutData.getDocument().add(headlineTable);

        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();

        int singleContentColumn = 1;

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Name of Pupil: ", tcStudentDetails.getStudentName(), false, regularFont, regularFont, false, true)),
                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Father's/Guardian's Name: ", tcStudentDetails.getFatherGuardianName(), false, regularFont, regularFont, false, true)),
                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Mother's Name: ", tcStudentDetails.getMotherName(), false, regularFont, regularFont, false, true)),
                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                        index++, "Nationality: ", tcStudentDetails.getNationality(), false, regularFont, regularFont, false, true)),
                singleCellLayoutSetup);

        boolean isScST = false;
        if(tcStudentDetails.getCategory() != null &&
                (tcStudentDetails.getCategory() == UserCategory.SC || tcStudentDetails.getCategory() == UserCategory.ST)) {
            isScST = true;
        }
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Whether the candidate belongs to Schedule Caste or Schedule Tribe: ",
                isScST ? "Yes" : "No", false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        document.add(table);

        table = getPDFTable(documentLayoutSetup, 1);
        String dobInFigures = tcStudentDetails.getDob() == null || tcStudentDetails.getDob() <= 0
                ? EMPTY_VALUE : DateUtils.getFormattedDate(tcStudentDetails.getDob());
        String dobInWords = tcStudentDetails.getDob() == null || tcStudentDetails.getDob() <= 0
                ? EMPTY_VALUE : DateUtils.getDateInWords(tcStudentDetails.getDob());
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Date Of Birth (in figures): ", dobInFigures, false, regularFont, regularFont, false, true)), singleCellLayoutSetup);
        addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("(in words) ", dobInWords, regularFont, regularFont, false, true).setPaddingLeft(20f)), singleCellLayoutSetup);
        document.add(table);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);

//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, 2);
//        String dobInFigures = tcStudentDetails.getDob() == null || tcStudentDetails.getDob() <= 0
//                ? EMPTY_VALUE : DateUtils.getFormattedDate(tcStudentDetails.getDob());
//        String dobInWords = tcStudentDetails.getDob() == null || tcStudentDetails.getDob() <= 0
//                ? EMPTY_VALUE : DateUtils.getDateInWords(tcStudentDetails.getDob());
//        addRow(table, documentLayoutSetup, Arrays.asList(
//                getTransferCertificateKeyValueParagraph(index++, "Date Of Birth (in figures): ", dobInFigures),
//                getKeyValueParagraph("(in words): ", dobInWords)),
//                singleCellLayoutSetup);
//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        String dateOfFirstAdmissionWithClass = tcStudentDetails.getAdmissionDate() == null || tcStudentDetails.getAdmissionDate() <= 0
                ? EMPTY_VALUE : DateUtils.getFormattedDate(tcStudentDetails.getAdmissionDate())
                + (StringUtils.isBlank(tcStudentDetails.getAdmissionClass()) ? EMPTY_VALUE : ", Class: " + tcStudentDetails.getAdmissionClass());

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                        index++, "Date of first admission in school with class: ", dateOfFirstAdmissionWithClass, false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        TCStudentLastActiveSessionDetails tcStudentLastActiveSessionDetails =
                studentTransferCertificateDetails.getTcStudentLastActiveSessionDetails();
        document.add(table);

        table = getPDFTable(documentLayoutSetup, 2);
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Class in which pupil last studied: ",
                tcStudentLastActiveSessionDetails.getLastActiveSessionClass(), false, regularFont, regularFont, false, true)),
                singleCellLayoutSetup);
        document.add(table);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "School/Board Annual examination last taken with result: ",
                tcStudentLastActiveSessionDetails.getLastExamTakenWithResult(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Whether failed, if so once/twice in the same class: ",
                tcStudentLastActiveSessionDetails.getNumberOfTimeExamFailed(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        String subjects = CollectionUtils.isEmpty(tcStudentLastActiveSessionDetails.getScholasticCoursesLastActiveSession()) ? EMPTY_TEXT :
                String.join(", " , tcStudentLastActiveSessionDetails.getScholasticCoursesLastActiveSession());
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Subjects Studied: ", subjects, false, regularFont, regularFont, false, true)), singleCellLayoutSetup);
//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, 2);
//
//        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
//                index++, "Whether qualified for promotion in higher class: ",
//                tcStudentLastActiveSessionDetails.getPromotionToHigherClass()),
//                getTransferCertificateKeyValueParagraph(index++, "If so in which class: ",
//                tcStudentLastActiveSessionDetails.getPromotingClassName())),
//                singleCellLayoutSetup);
//
//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, singleContentColumn);

//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, 1);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Whether qualified for promotion in higher class: ",
                tcStudentLastActiveSessionDetails.getPromotionToHigherClass(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("If so in which class: ",
                tcStudentLastActiveSessionDetails.getPromotingClassName(), regularFont, regularFont, false, true).setPaddingLeft(20f)), singleCellLayoutSetup);

//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Month up to which (pupil had paid) school dues paid: ",
                tcStudentLastActiveSessionDetails.getLastFeesPaid(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Any fees concession availed of: If so, the nature of such concession: ",
                tcStudentLastActiveSessionDetails.getDiscountWithNature(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Total No. of working days: ",
                tcStudentLastActiveSessionDetails.getTotalWorkingDays(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Total No. of working days present: ",
                tcStudentLastActiveSessionDetails.getTotalAttendedDays(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        TCOtherDetails tcOtherDetails = studentTransferCertificateDetails.getTcOtherDetails();

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Whether NCC Cadet/Boy Scout/Girl Guide (details may be given): ",
                tcOtherDetails.getNccCadetBoyScoutGirlGuideWithDetails(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Games played extra curricular activities in which the pupil usually took part (mention achievement level therein): ",
                tcOtherDetails.getCoCurricularActivities(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);


        TCStudentRelievingDetails tcStudentRelievingDetails = studentTransferCertificateDetails.getTcStudentRelievingDetails();

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "General Conduct: ", tcStudentRelievingDetails.getCodeOfConduct(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        String relieveDate = tcStudentRelievingDetails.getRelieveDate() == null || tcStudentRelievingDetails.getRelieveDate() <= 0 ?
                EMPTY_VALUE : DateUtils.getFormattedDate(tcStudentRelievingDetails.getRelieveDate());
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Date of application of Certificate: ", relieveDate, false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        String tcGenerationDate = studentTransferCertificateDetails.getTcGenerationDate() == null || studentTransferCertificateDetails.getTcGenerationDate() <= 0 ?
                EMPTY_VALUE : DateUtils.getFormattedDate(studentTransferCertificateDetails.getTcGenerationDate());
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Date of issue of Certificate: ", tcGenerationDate, false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Reason for leaving the school: ", tcStudentRelievingDetails.getRelieveReason(), false, regularFont, regularFont, false, true)),
                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Any other remarks: ", tcStudentRelievingDetails.getRemarks(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        document.add(table);

        return index;
    }

    protected void generateSignatureBox(DocumentLayoutData documentLayoutData, float squareBorderMargin, float innerGap)
            throws IOException {

        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PageSize pageSize = documentLayoutSetup.getPageSize();

        int singleContentColumn = 3;

        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(documentLayoutData.getBoldFont())
                .setFontSize(documentLayoutData.getContentFontSize() - 1)
                .setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData("Signature of class in charge", signatureCellLayoutSetup),
                new CellData("Admin", signatureCellLayoutSetup.setTextAlignment(TextAlignment.CENTER)),
                new CellData("Principal", signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));

        table.setFixedPosition(document.getLeftMargin(), document.getBottomMargin(), pageSize.getWidth() - document.getLeftMargin() - document.getRightMargin());

        document.add(table);
    }
}
