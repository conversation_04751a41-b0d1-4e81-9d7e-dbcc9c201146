package com.lernen.cloud.pdf.certificates.transfer;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.*;
import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;

public class TransferCertificateGenerator10295 extends CBSETransferCertificateGenerator {

    public TransferCertificateGenerator10295(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final Logger logger = LogManager.getLogger(TransferCertificateGenerator10295.class);

    private static final String AFFILIATION_NUMBER = "2130978";
    private static final String SCHOOL_NUMBER = "60441";

    @Override
    public DocumentOutput generateTransferCertificate(StudentManager studentManager, Institute institute, Student student,
                                                      String documentName) {
        try {
            float squareBorderMargin = 20f;
            float borderInnerGap = 2f;

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            DocumentLayoutData admissionFormLayoutData = generateTransferCertificateLayoutData(institute, documentOutput,
                    60f, 60f);
            Document document = admissionFormLayoutData.getDocument();
            int index = 1;

            addBlankLine(document, false, 1);
            StudentTransferCertificateDetails studentTransferCertificateDetails = student.getStudentTransferCertificateDetails();
            generatePageHeader(admissionFormLayoutData, institute, studentTransferCertificateDetails);

            addBlankLine(document, false, 1);

            index = generateBasicInformationPage(admissionFormLayoutData, studentManager, institute,
                    studentTransferCertificateDetails, index, true, student.getStudentBasicInfo());

            generateDeclarationPage(admissionFormLayoutData, document);

            generateSignatureBox(admissionFormLayoutData);

            generateMetadata(admissionFormLayoutData, squareBorderMargin, borderInnerGap);

            document.close();

            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating admission form for institute {}, student id {}",
                    institute.getInstituteId(), student.getStudentId(), e);
        }
        return null;
    }

    private void generateDeclarationPage(DocumentLayoutData documentLayoutData, Document document) {
        PdfFont boldFont = documentLayoutData.getBoldFont();
        PdfFont regularFont = documentLayoutData.getRegularFont();

        addBlankLine(document, false, 1);

        Table table = getPDFTable(documentLayoutData.getDocumentLayoutSetup(), 1);
        CellLayoutSetup headlineCellLayoutSetup = new CellLayoutSetup();
        headlineCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize())
                .setTextAlignment(TextAlignment.LEFT).setPdfFont(regularFont);
        addRow(table, documentLayoutData.getDocumentLayoutSetup(), Collections.singletonList(
                new CellData(getParagraph("I hereby declare that the information provided above by me, including the candidate's name, father's name, mother's name, and date of birth is correct as per the school records.", regularFont),
                        headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT))));
        documentLayoutData.getDocument().add(table);

    }

    protected DocumentLayoutData generateTransferCertificateLayoutData(Institute institute, DocumentOutput documentOutput,
                                                                       float logoWidth, float logoHeight) throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false, PageSize.A4, 15f, 15f,
                30f, 0f);
        float contentFontSize = 10f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        int instituteId = institute.getInstituteId();
        DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getCambriaFont(),
                getCambriaBoldFont(), contentFontSize, defaultBorderWidth, logoWidth, logoHeight,
                LogoProvider.INSTANCE.getLogo(instituteId), null, null);
        documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
        return documentLayoutData;
    }

    protected void generatePageHeader(DocumentLayoutData documentLayoutData, Institute institute,
                                      StudentTransferCertificateDetails studentTransferCertificateDetails) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();
        PdfFont regularFont = documentLayoutData.getRegularFont();

        float bgImageHeightWidth = 500f;
        //Institute watermark
        int instituteId = institute.getInstituteId();
        generateWatermark(documentLayoutData, instituteId, bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 - 50, bgImageHeightWidth / 2 - 80);

//        generateImage(document, documentLayoutSetup, documentLayoutData.getLogo(), documentLayoutData.getLogoWidth(),
//                documentLayoutData.getLogoHeight(), 30f,
//                documentLayoutSetup.getPageSize().getHeight() * 0.87f, 1.4f, 1.4f);

        generateDynamicImageProvider(documentLayoutData, 30f, documentLayoutSetup.getPageSize().getHeight() * 0.87f,
                1.4f, 1.4f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

        generateImage(document, documentLayoutSetup, LogoProvider.INSTANCE.getSecondLogo(institute.getInstituteId()),
                100, documentLayoutData.getLogoHeight(),
                documentLayoutSetup.getPageSize().getWidth() - 130f - 20f,
                documentLayoutSetup.getPageSize().getHeight() * 0.885f, 1.2f, 1.2f);


        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(regularFont).setFontSize(
                documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.CENTER);


        addRow(table, documentLayoutSetup, Arrays.asList(
            getParagraph(institute.getInstituteName().toUpperCase() + ", NOIDA", EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB).setMultipliedLeading(1.0f)),
                cellLayoutSetup.copy().setFontSize(documentLayoutData.getContentFontSize() + 6).setPdfFont(boldFont));

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("D-33B, Sector 47, Noida, G.B. Nagar (U.P.)").setMultipliedLeading(.95f)),
                cellLayoutSetup.copy().setFontSize(documentLayoutData.getContentFontSize() - 2));

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("www.jpsnoida.com | <EMAIL>")),
                cellLayoutSetup.copy().setFontSize(documentLayoutData.getContentFontSize() - 2));

        document.add(table);

        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("TRANSFER CERTIFICATE").setUnderline()),
                cellLayoutSetup.copy().setPdfFont(boldFont));

        document.add(table);

    }

    protected int generateBasicInformationPage(DocumentLayoutData documentLayoutData, StudentManager studentManager,
            Institute institute, StudentTransferCertificateDetails studentTransferCertificateDetails, int index,
                                               boolean addSchoolInfoData, StudentBasicInfo studentBasicInfo) throws IOException {

        PdfFont boldFont = documentLayoutData.getBoldFont();
        PdfFont regularFont = documentLayoutData.getRegularFont();

        int headlineContentColumn = 3;
        Table headlineTable = getPDFTable(documentLayoutData.getDocumentLayoutSetup(), headlineContentColumn);
        CellLayoutSetup headlineCellLayoutSetup = new CellLayoutSetup();
        headlineCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize())
                .setTextAlignment(TextAlignment.LEFT).setPdfFont(regularFont);

        TCStudentDetails tcStudentDetails = studentTransferCertificateDetails.getTcStudentDetails();
        TCSchoolDetails tcSchoolDetails = studentTransferCertificateDetails.getTcSchoolDetails();
        if(addSchoolInfoData) {

            addRow(headlineTable, documentLayoutData.getDocumentLayoutSetup(), Arrays.asList(

                    new CellData(getKeyValueParagraph("Affiliation No.: ", AFFILIATION_NUMBER, boldFont, boldFont),
                            headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),

                    new CellData(getKeyValueParagraph("School No.: ", SCHOOL_NUMBER, boldFont, boldFont),
                            headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),

                    new CellData(getKeyValueParagraph("UDISE No.: " , "09100107826", boldFont, boldFont),
                            headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))

//                    new CellData(getKeyValueParagraph("Book No.:", EMPTY_TEXT, regularFont, regularFont),
//                            headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
//                    new CellData(getKeyValueParagraph("S.R. No.:", EMPTY_TEXT, regularFont, regularFont),
//                            headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),

                    ));
            documentLayoutData.getDocument().add(headlineTable);
        }

        headlineContentColumn = 3;
        headlineTable = getPDFTable(documentLayoutData.getDocumentLayoutSetup(), headlineContentColumn);

        headlineCellLayoutSetup = new CellLayoutSetup();
        headlineCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT).setPdfFont(regularFont);


        addRow(headlineTable, documentLayoutData.getDocumentLayoutSetup(), Arrays.asList(

                new CellData(getKeyValueParagraph("Status of School: ", "Sr. Secondary", boldFont, boldFont),
                        headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),

                new CellData(getKeyValueParagraph("Renewed upto: " , "31/03/2028", boldFont, boldFont),
                        headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),

                new CellData(getKeyValueParagraph("Admission No.:", tcStudentDetails.getAdmissionNumber(), boldFont, boldFont),
                        headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))

        ));

        addRow(headlineTable, documentLayoutData.getDocumentLayoutSetup(), Arrays.asList(

                new CellData(getKeyValueParagraph("APAAR ID NO. :", StringUtils.isBlank(tcStudentDetails.getApaarIdNo()) ? StringUtils.isBlank(studentBasicInfo.getApaarIdNo()) ? "" : studentBasicInfo.getApaarIdNo() : tcStudentDetails.getApaarIdNo(), boldFont, boldFont),
                        headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),

                new CellData(getKeyValueParagraph("P.E.N.:", StringUtils.isBlank(tcSchoolDetails.getPrimaryEducationAffiliationNumber()) ? StringUtils.isBlank(studentBasicInfo.getPenNumber()) ? "" : studentBasicInfo.getPenNumber() :tcSchoolDetails.getPrimaryEducationAffiliationNumber(), boldFont, boldFont),
                        headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),

                new CellData(getKeyValueParagraph("T.C. No.: " , studentTransferCertificateDetails.getTcNumber(), boldFont, boldFont),
                        headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))

        ));

        documentLayoutData.getDocument().add(headlineTable);

        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();

        int singleContentColumn = 1;

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                        index++, "Name of the student: ", tcStudentDetails.getStudentName(), false, regularFont, boldFont, false, false)),
                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                        index++, "Mother's Name: ", tcStudentDetails.getMotherName(), false, regularFont, boldFont, false, false)),
                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                        index++, "Father's/Guardian's Name: ", tcStudentDetails.getFatherGuardianName(), false, regularFont, boldFont, false, false)),
                singleCellLayoutSetup);

        document.add(table);

        table = getPDFTable(documentLayoutSetup, new float[]{0.035f, 0.065f});
        String dobInFigures = tcStudentDetails.getDob() == null || tcStudentDetails.getDob() <= 0
                ? EMPTY_VALUE : DateUtils.getFormattedDate(tcStudentDetails.getDob());
        String dobInWords = tcStudentDetails.getDob() == null || tcStudentDetails.getDob() <= 0
                ? EMPTY_VALUE : DateUtils.getDateInWords(tcStudentDetails.getDob());

        addRow(table, documentLayoutSetup, Arrays.asList(
                getTransferCertificateKeyValueParagraph(index++, "Date Of Birth (in figures): ", dobInFigures, false, regularFont, boldFont, false, false),
                getKeyValueParagraph("(in words) ", dobInWords, regularFont, boldFont, false, false)), singleCellLayoutSetup);

//        addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("(in words) ", dobInWords, regularFont, boldFont, false, false).setPaddingLeft(20f)), singleCellLayoutSetup);

        document.add(table);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Proof of DOB at the time of admission: ", tcStudentDetails.getProofOfDoBAtTheTimeOfAdmission(),
                false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                        index++, "Nationality: ", tcStudentDetails.getNationality(), false, regularFont, boldFont, false, false)),
                singleCellLayoutSetup);

        boolean isScST = false;
        if(tcStudentDetails.getCategory() != null &&
                (tcStudentDetails.getCategory() == UserCategory.SC || tcStudentDetails.getCategory() == UserCategory.ST)) {
            isScST = true;
        }
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Whether the candidate belongs to Schedule Caste or Schedule Tribe: ",
                EMPTY_TEXT, false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        document.add(table);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);

//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, 2);
//        String dobInFigures = tcStudentDetails.getDob() == null || tcStudentDetails.getDob() <= 0
//                ? EMPTY_VALUE : DateUtils.getFormattedDate(tcStudentDetails.getDob());
//        String dobInWords = tcStudentDetails.getDob() == null || tcStudentDetails.getDob() <= 0
//                ? EMPTY_VALUE : DateUtils.getDateInWords(tcStudentDetails.getDob());
//        addRow(table, documentLayoutSetup, Arrays.asList(
//                getTransferCertificateKeyValueParagraph(index++, "Date Of Birth (in figures): ", dobInFigures),
//                getKeyValueParagraph("(in words): ", dobInWords)),
//                singleCellLayoutSetup);
//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        String dateOfFirstAdmissionWithClass = tcStudentDetails.getAdmissionDate() == null || tcStudentDetails.getAdmissionDate() <= 0
                ? EMPTY_VALUE : DateUtils.getFormattedDate(tcStudentDetails.getAdmissionDate())
                + (StringUtils.isBlank(tcStudentDetails.getAdmissionClass()) ? EMPTY_VALUE : ", Class: " + tcStudentDetails.getAdmissionClass());

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Date of first admission in school with class: ", dateOfFirstAdmissionWithClass, false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        TCStudentLastActiveSessionDetails tcStudentLastActiveSessionDetails =
                studentTransferCertificateDetails.getTcStudentLastActiveSessionDetails();
        document.add(table);

        table = getPDFTable(documentLayoutSetup, new float[]{0.50f, 0.50f});
        addRow(table, documentLayoutSetup, Arrays.asList(
                getTransferCertificateKeyValueParagraph(index++, "Class in which pupil last studied (in figures): ", tcStudentLastActiveSessionDetails.getLastActiveSessionClassInFigures(), false, regularFont, boldFont, false, false),
//                getKeyValueParagraph("(in figures) ", tcStudentLastActiveSessionDetails.getLastActiveSessionClassInFigures(), regularFont, boldFont, false, false),
                getKeyValueParagraph("(in words) ", tcStudentLastActiveSessionDetails.getLastActiveSessionClassInWords(), regularFont, boldFont, false, false)
                ), singleCellLayoutSetup);
        document.add(table);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "School/Board Annual examination last taken with result: ",
                tcStudentLastActiveSessionDetails.getLastExamTakenWithResult(), false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Whether failed, if so once/twice in the same class: ",
                tcStudentLastActiveSessionDetails.getNumberOfTimeExamFailed(), false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        String subjects = CollectionUtils.isEmpty(tcStudentLastActiveSessionDetails.getScholasticCoursesLastActiveSession()) ? EMPTY_TEXT :
                String.join(", " , tcStudentLastActiveSessionDetails.getScholasticCoursesLastActiveSession());
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Subjects Studied: ", subjects, false, regularFont, boldFont, false, false)), singleCellLayoutSetup);
//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, 2);
//
//        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
//                index++, "Whether qualified for promotion in higher class: ",
//                tcStudentLastActiveSessionDetails.getPromotionToHigherClass()),
//                getTransferCertificateKeyValueParagraph(index++, "If so in which class: ",
//                tcStudentLastActiveSessionDetails.getPromotingClassName())),
//                singleCellLayoutSetup);
//
//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, singleContentColumn);

//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, 1);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Whether qualified for promotion in higher class: ",
                tcStudentLastActiveSessionDetails.getPromotionToHigherClass(), false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        document.add(table);

        table = getPDFTable(documentLayoutSetup, new float[]{0.50f, 0.50f});
        addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("If so in which class (in figures): ",
                tcStudentLastActiveSessionDetails.getPromotingClassNameInFigures(), regularFont, boldFont, false, false).setPaddingLeft(20f),
//                getKeyValueParagraph("(in figures) ", tcStudentLastActiveSessionDetails.getPromotingClassNameInFigures(), regularFont, boldFont, false, false),
                getKeyValueParagraph("(in words) ", tcStudentLastActiveSessionDetails.getPromotingClassNameInWords(), regularFont, boldFont, false, false)
        ), singleCellLayoutSetup);

        document.add(table);

        table = getPDFTable(documentLayoutSetup, 1);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Total No. of working days: ", tcStudentLastActiveSessionDetails.getTotalWorkingDays(),
                false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Total No. of working days present: ",
                tcStudentLastActiveSessionDetails.getTotalAttendedDays(), false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Month up to which (pupil had paid) school dues paid: ",
                tcStudentLastActiveSessionDetails.getLastFeesPaid(), false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Any fees concession availed of: If so, the nature of such concession: ",
                tcStudentLastActiveSessionDetails.getDiscountWithNature(), false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        TCOtherDetails tcOtherDetails = studentTransferCertificateDetails.getTcOtherDetails();

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Whether NCC Cadet/Boy Scout/Girl Guide (details may be given): ",
                tcOtherDetails.getNccCadetBoyScoutGirlGuideWithDetails(), false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Whether School Is Under Govt/Minority/Independent Category: ",
                "Independent", false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Games played extra curricular activities in which the pupil usually took part (mention achievement level therein): ",
                tcOtherDetails.getCoCurricularActivities(), false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        TCStudentRelievingDetails tcStudentRelievingDetails = studentTransferCertificateDetails.getTcStudentRelievingDetails();

        String relieveDate = tcStudentRelievingDetails.getRelieveDate() == null || tcStudentRelievingDetails.getRelieveDate() <= 0 ?
                EMPTY_VALUE : DateUtils.getFormattedDate(tcStudentRelievingDetails.getRelieveDate());

        String dateOfApplicationOfCertificate = tcStudentRelievingDetails.getDateOfApplicationOfCertificate() == null || tcStudentRelievingDetails.getDateOfApplicationOfCertificate() <= 0 ?
                EMPTY_VALUE : DateUtils.getFormattedDate(tcStudentRelievingDetails.getDateOfApplicationOfCertificate());

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Date of application of Certificate: ", dateOfApplicationOfCertificate, false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

//        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
//                index++, "General Conduct: ", tcStudentRelievingDetails.getCodeOfConduct(), false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        String datePupilsNameStuckOffTheRolls = tcStudentRelievingDetails.getDatePupilsNameStuckOffTheRolls() == null || tcStudentRelievingDetails.getDatePupilsNameStuckOffTheRolls() <= 0 ?
                EMPTY_VALUE : DateUtils.getFormattedDate(tcStudentRelievingDetails.getDatePupilsNameStuckOffTheRolls());

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Date on which pupils name was stuck off the rolls of the school: ", datePupilsNameStuckOffTheRolls, false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        String tcGenerationDate = studentTransferCertificateDetails.getTcGenerationDate() == null || studentTransferCertificateDetails.getTcGenerationDate() <= 0 ?
                EMPTY_VALUE : DateUtils.getFormattedDate(studentTransferCertificateDetails.getTcGenerationDate());
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Date of issue of Certificate: ", tcGenerationDate, false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

//        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
//                        index++, "Reason for leaving the school: ", tcStudentRelievingDetails.getRelieveReason(), false, regularFont, boldFont, false, false)),
//                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Any other remarks: ", tcStudentRelievingDetails.getRemarks(), false, regularFont, boldFont, false, false)), singleCellLayoutSetup);

        document.add(table);

        return index;
    }

    protected void generateSignatureBox(DocumentLayoutData documentLayoutData)
            throws IOException {

        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PageSize pageSize = documentLayoutSetup.getPageSize();

        int singleContentColumn = 4;

        Table table = getPDFTable(documentLayoutSetup, new float[] {0.20f, 0.19f, 0.01f, 0.195f, 0.01f, 0.195f, 0.20f});

        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(documentLayoutData.getBoldFont())
                .setFontSize(documentLayoutData.getContentFontSize())
                .setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(EMPTY_TEXT, signatureCellLayoutSetup),
                new CellData("Prepared by", signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
                new CellData(EMPTY_TEXT, signatureCellLayoutSetup),
                new CellData("Class Teacher", signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
                new CellData(EMPTY_TEXT, signatureCellLayoutSetup),
                new CellData("Checked by", signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
                new CellData("Principal", signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER))));

        signatureCellLayoutSetup.setPdfFont(documentLayoutData.getRegularFont())
                .setFontSize(documentLayoutData.getContentFontSize())
                .setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData("Name & Signature", signatureCellLayoutSetup),
                new CellData(EMPTY_TEXT, signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT).setBorderBottom(new SolidBorder(documentLayoutData.getDefaultBorderWidth())).setPaddingRight(10f)),
                new CellData(EMPTY_TEXT, signatureCellLayoutSetup),
                new CellData(EMPTY_TEXT, signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT).setBorderBottom(new SolidBorder(documentLayoutData.getDefaultBorderWidth())).setPaddingRight(10f)),
                new CellData(EMPTY_TEXT, signatureCellLayoutSetup),
                new CellData(EMPTY_TEXT, signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER).setBorderBottom(new SolidBorder(documentLayoutData.getDefaultBorderWidth())).setPaddingLeft(10f)),
                new CellData("(Signature & Seal)", signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER))));

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData("Designation", signatureCellLayoutSetup),
                new CellData(EMPTY_TEXT, signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT).setBorderBottom(new SolidBorder(documentLayoutData.getDefaultBorderWidth())).setPaddingRight(10f)),
                new CellData(EMPTY_TEXT, signatureCellLayoutSetup),
                new CellData(EMPTY_TEXT, signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT).setBorderBottom(new SolidBorder(documentLayoutData.getDefaultBorderWidth())).setPaddingRight(10f)),
                new CellData(EMPTY_TEXT, signatureCellLayoutSetup),
                new CellData(EMPTY_TEXT, signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER).setBorderBottom(new SolidBorder(documentLayoutData.getDefaultBorderWidth())).setPaddingLeft(10f)),
                new CellData(EMPTY_TEXT, signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER))));

        table.setFixedPosition(document.getLeftMargin(), document.getBottomMargin() + 30, pageSize.getWidth() - document.getLeftMargin() - document.getRightMargin());

        document.add(table);

        table = getPDFTable(documentLayoutData.getDocumentLayoutSetup(), 1);
        addRow(table, documentLayoutData.getDocumentLayoutSetup(), Collections.singletonList(
                new CellData(getParagraph("If this T.C. is issued by the officiating /In-charge Principal, it will be countersigned by the Manager of the S.M.C.",
                        documentLayoutData.getRegularFont()), signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER))));

        table.setFixedPosition(document.getLeftMargin(), document.getBottomMargin() + 5, pageSize.getWidth() - document.getLeftMargin() - document.getRightMargin());

        document.add(table);

    }
}
