package com.lernen.cloud.pdf.certificates.tuition;

import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.pdf.base.PDFGenerator;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.api.fees.payment.StudentFeeHeadPaymentData;

public abstract class TuitionFeesCertificateGenerator extends PDFGenerator {

    public TuitionFeesCertificateGenerator(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    public abstract DocumentOutput generateTuitionFeesCertificate(Institute institute, Student student,
    String documentName, StudentManager studentManager,StudentFeeHeadPaymentData studentFeeHeadPaymentData);

    protected void generateMetadata(DocumentLayoutData documentLayoutData, float squareBorderMargin, float innerGap) {
        generateMetadata(documentLayoutData,squareBorderMargin, innerGap,1);
    }

    protected void generateMetadata(DocumentLayoutData documentLayoutData, float squareBorderMargin, float innerGap, int pageNumber) {
        generateBorderLayout(documentLayoutData.getDocument(), documentLayoutData.getDocumentLayoutSetup(), pageNumber,
                squareBorderMargin, innerGap);
    }
}
