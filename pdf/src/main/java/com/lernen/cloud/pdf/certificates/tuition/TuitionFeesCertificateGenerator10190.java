package com.lernen.cloud.pdf.certificates.tuition;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.StudentFeeHeadPaymentData;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

public class TuitionFeesCertificateGenerator10190 extends GlobalTuitionFeesCertificateGenerator {
    
    public TuitionFeesCertificateGenerator10190(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(TuitionFeesCertificateGenerator10190.class);

//    public static final float DEFAULT_PAGE_SIDE_MARGIN = 50f;
//	public static final float DEFAULT_TABLE_BORDER_WIDTH = 0.5f;
//	public static final float LOGO_WIDTH = 75f;
//	public static final float LOGO_HEIGHT = 75f;
//	public static final String NA = "NA";

//	private static final String CONTENT_HEADER = "TO WHOM IT MAY CONCERN";
//	private static final String CONTENT_DATE = "Date : ";
//    private static final String CONTENT_1 = "This is to certify that ";
//    private static final String CONTENT_2 = "%s ";//STUDENT NAME
//    private static final String CONTENT_3 = "%s";//(S/D/)
//    private static final String CONTENT_4 = "o ";
//    private static final String CONTENT_5 = "Mr. %s "; // FATHER NAME
//	private static final String CONTENT_14 = "and Mrs. %s "; // Mother NAME
//    private static final String CONTENT_6 = "is a regular student of this institution studying in class ";
//    private static final String CONTENT_7 = "%s "; //CLASS NAME
//    private static final String CONTENT_8 = "during session ";
//    private static final String CONTENT_9 = "%s. "; //SESSION NAME
//    private static final String CONTENT_10 = "%s has paid the yearly tuition fees of ";
//    private static final String CONTENT_11 = "Rs. %s/- "; //TUITION FEE PAID AMOUNT
//    private static final String CONTENT_12 = "in the academic session ";
//    private static final String CONTENT_13 = "%s."; // SESSION NAME

    @Override
    public DocumentOutput generateTuitionFeesCertificate(Institute institute, Student student, String documentName,
            StudentManager studentManager,StudentFeeHeadPaymentData studentFeeHeadPaymentData) {
         try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData documentLayoutData = generateTuitionFeesCertificateLayoutData(institute, documentOutput, 70f,70f);
            DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
            Document document = documentLayoutData.getDocument();
			PdfFont boldFont = getCambriaBoldFont();
			PdfFont regularFont = getCambriaFont();
			float contentFontSize = documentLayoutData.getContentFontSize();

            generateImages(document, documentLayoutSetup, documentLayoutData, institute, student, studentManager);
//            generateHeader(document, documentLayoutSetup, contentFontSize, boldFont, regularFont, student, institute);
			 addBlankLine(document, true, 4);
			 addBlankLine(document, false, 2);
			generateContent(document, documentLayoutSetup, contentFontSize, student, boldFont, regularFont,studentFeeHeadPaymentData);
			generateSignatureBox(document, documentLayoutSetup, contentFontSize, boldFont, regularFont);
			document.close();
			return documentOutput;

         }
         catch (Exception e) {
			logger.error("Error while generating tuition fees certificate for the student {} ", student.getStudentBasicInfo().getAdmissionNumber(), e);
		}
		return null;
    }

	protected void generateImages(Document document, DocumentLayoutSetup documentLayoutSetup,
								  DocumentLayoutData documentLayoutData, Institute institute, Student student,
								  StudentManager studentManager) throws IOException {
		int instituteId = institute.getInstituteId();
		float bgImageHeightWidth = 500f;

		//Watermark
		generateWatermark(documentLayoutData, institute.getInstituteId(), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 - 50, bgImageHeightWidth / 2 - 80);

		//LOGO
		generateImage(documentLayoutData.getDocument(), documentLayoutData.getDocumentLayoutSetup(),
				ImageProvider.INSTANCE.getImage(ImageProvider._10190_LOGO_HEADER), 350, 130,
				(documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() / 2) - 165,
				documentLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.84f);

	}

}
