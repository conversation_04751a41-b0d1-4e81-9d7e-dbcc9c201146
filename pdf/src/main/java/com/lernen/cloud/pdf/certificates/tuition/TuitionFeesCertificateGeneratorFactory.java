package com.lernen.cloud.pdf.certificates.tuition;

import java.util.HashMap;
import java.util.Map;

import com.embrate.cloud.core.lib.utility.AssetProvider;

public class TuitionFeesCertificateGeneratorFactory {
    private static final Map<Integer, TuitionFeesCertificateGenerator> TUITION_FEES_CERTIFICATE_GENERATOR = new HashMap<>();
	private final AssetProvider assetProvider;

	public TuitionFeesCertificateGeneratorFactory(AssetProvider assetProvider) {
        this.assetProvider = assetProvider;
        initializeGenerators();
    }

    private void initializeGenerators() {
        TUITION_FEES_CERTIFICATE_GENERATOR.put(10130, new TuitionFeesCertificateGenerator10130(assetProvider));
		TUITION_FEES_CERTIFICATE_GENERATOR.put(10190, new TuitionFeesCertificateGenerator10190(assetProvider));
		TUITION_FEES_CERTIFICATE_GENERATOR.put(10295, new TuitionFeesCertificateGenerator10295V2(assetProvider));
		TUITION_FEES_CERTIFICATE_GENERATOR.put(10356, new TuitionFeesCertificateGenerator10356(assetProvider));
    }

    public TuitionFeesCertificateGenerator getTuitionFeesCertificateGenerator(int instituteId) {
		if (!TUITION_FEES_CERTIFICATE_GENERATOR.containsKey(instituteId)) {
			return new GlobalTuitionFeesCertificateGenerator(assetProvider);
		}
		return TUITION_FEES_CERTIFICATE_GENERATOR.get(instituteId);
	}
}
