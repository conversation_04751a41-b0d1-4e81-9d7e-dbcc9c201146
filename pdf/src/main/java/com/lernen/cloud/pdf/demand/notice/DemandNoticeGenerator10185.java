package com.lernen.cloud.pdf.demand.notice;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.xobject.PdfFormXObject;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.layout.LayoutArea;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.StudentDueFeesData;
import com.lernen.cloud.core.api.fees.payment.StudentFeesLevelPaymentData;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;

public class DemandNoticeGenerator10185 extends GlobalDemanNoticeGenerator {
    public DemandNoticeGenerator10185(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    private static final String GUARDIAN = "Guardian";
    private static final String DN_CONTENT_PARAGRAPH_1 = "Respected %s, \n Fees for student %s, class %s is due as follows. Please clear the dues as soon as possible to avoid penalties. \n Due installments: %s \n Total Amount: %s/-";

    @Override
    public DocumentOutput generateDemandNotices(Institute institute, List<StudentDueFeesData> dueFeeStudents,
                                                boolean includeFine, String documentName) {
        int instituteId = institute.getInstituteId();
        try {
            DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false, DEFAULT_PAGE_SIZE, 10f, 5f,
                    DEFAULT_PAGE_SIDE_MARGIN, 0f);
            float contentFontSize = 12f;
            float headerFontSize = 15f;

            Document document = initDocument(invoice.getContent(), documentLayoutSetup);
            Image instituteLogo = generateImage1(document, documentLayoutSetup,
                    LogoProvider.INSTANCE.getLogo(instituteId), 50f, 50f);

//			int itemCount = 3;
            int counter = 1;
            for (StudentDueFeesData studentDueFeesData : dueFeeStudents) {

                addBlankLine(document, false, 1);
                generateHeader(document, documentLayoutSetup, institute, headerFontSize, instituteLogo);
                generateContent(document, documentLayoutSetup, contentFontSize, studentDueFeesData, institute, includeFine);
                generateSignatureBox(document, documentLayoutSetup, contentFontSize);
                LayoutArea currentArea = document.getRenderer().getCurrentArea();
                Rectangle rectangle = currentArea.getBBox();
                /**
                 *  rectangle.getHeight() - gives position of last rectangle from bottom,
                 *  so if position of last rectangle is below than 1/3 of the page
                 *  adding next page
                 */
                counter++;
                if(counter < dueFeeStudents.size() && rectangle.getHeight() < (documentLayoutSetup.getPageSize().getHeight() / 3)) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }

//				if (counter % itemCount == 0 && dueFeeStudents.size() != counter) {
//					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
//				}
            }

            document.close();
            return invoice;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public Image generateImage1(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
                                float height) throws MalformedURLException {
        if (image == null) {
            return null;
        }
        Image img = new Image(ImageDataFactory.create(image));
        img.scaleToFit(width, height);
        return img;
//		document.add(img);
    }

    public void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute,
                               float headerFontSize, Image instituteLogo) throws IOException {

        Table table = getPDFTable(documentLayoutSetup, new float[] { 0.15f, 0.7f, 0.15f });

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(getRegularBoldFont()).setTextAlignment(TextAlignment.CENTER);

        Paragraph image = new Paragraph();
        image.add(instituteLogo);
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(image, cellLayoutSetup, 3, 1),
                        new CellData(getParagraph(institute.getInstituteName()), cellLayoutSetup),
                        new CellData(getParagraph(EMPTY_TEXT), cellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(getParagraph(institute.getLetterHeadLine1()), getParagraph(EMPTY_TEXT)),
                cellLayoutSetup.setFontSize(7f).setVerticalAlignment(VerticalAlignment.TOP));
        addRow(table, documentLayoutSetup,
                Arrays.asList(getParagraph(institute.getLetterHeadLine2()), getParagraph(EMPTY_TEXT)), cellLayoutSetup);

        document.add(table);

        int singleContentColumn = 1;
        table = getPDFTable(documentLayoutSetup, singleContentColumn);
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("\n")), cellLayoutSetup);
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("FEE NOTICE")),
                cellLayoutSetup.setFontSize(12f).setPdfFont(getRegularBoldFont()));
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("\n")), cellLayoutSetup);
        document.add(table);

    }

    private void generateContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                 StudentDueFeesData studentDueFeesData, Institute institute, boolean includeFine) throws IOException {
        Student student = studentDueFeesData.getStudent();
        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT);

        String heShe = "He/She";
        String hisHer = "His/Her";
        if (student.getStudentBasicInfo().getGender() == Gender.FEMALE) {
            heShe = "She";
            hisHer = "Her";
        } else if (student.getStudentBasicInfo().getGender() == Gender.MALE) {
            heShe = "He";
            hisHer = "His";
        }
        String installmentsDue = "";
        String delimiter = "";
        for (StudentFeesLevelPaymentData studentFeesLevelPaymentData : studentDueFeesData
                .getStudentFeesLevelPaymentDataList()) {
            installmentsDue = installmentsDue + delimiter + studentFeesLevelPaymentData.getFeesName();
            delimiter = ", ";
        }

        Text t1 = new Text(String.format(DN_CONTENT_PARAGRAPH_1,
                StringUtils.isBlank(student.getStudentFamilyInfo().getFathersName()) ? GUARDIAN
                        : student.getStudentFamilyInfo().getFathersName().trim(),
                student.getStudentBasicInfo().getName(),
                student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayName(), installmentsDue,
                String.valueOf(Math.round(includeFine ? studentDueFeesData.getTotalDueAmountWithFine() : studentDueFeesData.getTotalDueAmount()))));

        Paragraph p1 = new Paragraph(t1);
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(
                        getParagraph(String.format("Date %s",
                                DateUtils.getFormattedDate((int) (System.currentTimeMillis() / 1000l), DATE_FORMAT,
                                        User.DFAULT_TIMEZONE))),
                        cellLayoutSetup.setTextAlignment(TextAlignment.RIGHT))));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(p1, cellLayoutSetup.setTextAlignment(TextAlignment.LEFT))));
        document.add(table);
    }

    private void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize)
            throws IOException {
        int singleContentColumn = 3;

        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.CENTER);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
                signatureCellLayoutSetup);
        addRow(table, documentLayoutSetup,
                Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
                signatureCellLayoutSetup);
        addRow(table, documentLayoutSetup,
                Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
                signatureCellLayoutSetup);
        addRow(table, documentLayoutSetup,
                Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
                signatureCellLayoutSetup);

        addBlankLine(document,true,2);
        int singleContentColumnThree = 3;
        Table table2 = getPDFTable(documentLayoutSetup, singleContentColumnThree);

        generateImage(table2, documentLayoutSetup,0, ImageProvider.INSTANCE.getImage(
                ImageProvider._10185_PRINCIPAL_SIGNATURE), 110f, 70f, -10f, HorizontalAlignment.CENTER);

        document.add(table2);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getParagraph("Principal"), getParagraph("Class Teacher"), getParagraph("Guardian" + String.format(" ( Date: %s)",DateUtils.getFormattedDate((int) (System.currentTimeMillis() / 1000l), DATE_FORMAT,
                                DateUtils.DEFAULT_TIMEZONE)) )),
                signatureCellLayoutSetup);

        table.setMarginTop(-30f);
        document.add(table);
    }

    private String getSonDaughter(Gender gender) {
        if (gender == Gender.FEMALE) {
            return "daughter";
        } else if (gender == Gender.MALE) {
            return "son";
        }
        return "son/daughter";

    }

    protected DocumentOutput getA4PortraitDemandNotice(String documentName, DocumentOutput documentOutput) throws IOException {

        PdfReader reader = new PdfReader(new ByteArrayInputStream(documentOutput.getContent().toByteArray()));
        DocumentOutput invoice = new DocumentOutput(documentName + "_a4.pdf", new ByteArrayOutputStream());
        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
        PdfWriter pdfWriter = new PdfWriter(invoice.getContent());
        PdfDocument pdf = new PdfDocument(pdfWriter);
        Document document = new Document(pdf, documentLayoutSetup.getPageSize());
        document.setMargins(documentLayoutSetup.getTopMargin(), documentLayoutSetup.getSideMargin(),
                documentLayoutSetup.getBottomMargin(), documentLayoutSetup.getSideMargin());

        PdfDocument sourcePdf = new PdfDocument(reader);

        int demandNoticeNumber = 1;
        while(sourcePdf.getNumberOfPages() >= demandNoticeNumber) {
            for(int row = 0; row < 2; row++) {
                if(sourcePdf.getNumberOfPages() < demandNoticeNumber) {
                    break;
                }
                PageSize nUpPageSize = PageSize.A4;
                if((demandNoticeNumber - 1) % 4 == 0) {
                    PdfPage page = pdf.addNewPage(nUpPageSize);
                    PdfCanvas canvas = new PdfCanvas(page);
                }

                PdfPage page = pdf.getPage(pdf.getNumberOfPages());
                PdfCanvas canvas = new PdfCanvas(page);

                for(int column = 0; column < 2; column++) {
                    if(sourcePdf.getNumberOfPages() < demandNoticeNumber) {
                        break;
                    }
                    PdfPage origPage = sourcePdf.getPage(demandNoticeNumber);
                    Rectangle orig = origPage.getPageSize();
                    PdfFormXObject pageCopy = origPage.copyAsFormXObject(pdf);

                    //Add pages to N-up page
                    float x = ((orig.getWidth() + 20)  * column) + 10;
                    float y = (nUpPageSize.getHeight() - ((row + 1) * (orig.getHeight() + 20)));
                    canvas.addXObject(pageCopy, x, y);
                    demandNoticeNumber++;
                }
            }
        }

        // close the documents
        pdf.close();
        sourcePdf.close();
        return invoice;
    }
}
