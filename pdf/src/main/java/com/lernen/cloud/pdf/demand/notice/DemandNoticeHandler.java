package com.lernen.cloud.pdf.demand.notice;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.ws.rs.QueryParam;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import com.google.gson.Gson;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fees.payment.StudentDueFeesData;
import com.lernen.cloud.core.api.fees.payment.reminders.FeePaymentReminderPayload;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.student.StudentManager;

/**
 * 
 * <AUTHOR>
 *
 */
public class DemandNoticeHandler {

	private static final Logger logger = LogManager.getLogger(DemandNoticeHandler.class);

	private static final String DEMAND_NOTICE_DOCUMENT_NAME = "DemandNotice.pdf";

	private final DemandNoticeGeneratorFactory demandNoticeGeneratorFactory;
	private final FeePaymentManager feePaymentManager;
	private final InstituteManager instituteManager;
	private final UserPreferenceSettings userPreferenceSettings;
	private final UserPermissionManager userPermissionManager;

	public DemandNoticeHandler(FeePaymentManager feePaymentManager, InstituteManager instituteManager,
			UserPreferenceSettings userPreferenceSettings, UserPermissionManager userPermissionManager, AssetProvider assetProvider) {
		super();
		this.demandNoticeGeneratorFactory = new DemandNoticeGeneratorFactory(assetProvider);
		this.feePaymentManager = feePaymentManager;
		this.instituteManager = instituteManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.userPermissionManager = userPermissionManager;
	}

	public DocumentOutput generateBulkDemandNotices(int instituteId,
			FeePaymentReminderPayload feePaymentReminderPayload, UUID userId) {

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.DEMAND_NOTICE);

		if (feePaymentReminderPayload == null || feePaymentReminderPayload.getAcademicSessionId() <= 0
				|| CollectionUtils.isEmpty(feePaymentReminderPayload.getStudentIds())) {
			logger.error("Invalid payload for Generating fee notice for {} , payload {}", instituteId,
					feePaymentReminderPayload);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DEMAND_NOTICE_REQUEST,
					"Invalid fee notice request"));

		}

		int academicSessionId = feePaymentReminderPayload.getAcademicSessionId();
		List<UUID> studentIds = feePaymentReminderPayload.getStudentIds();
		int dueFeesDate = feePaymentReminderPayload.getDueDate();

		logger.info("Generating fee notice for {} , student count {}", instituteId,
				feePaymentReminderPayload.getStudentIds().size());

		final List<StudentDueFeesData> studentDueFeesDetailsList = feePaymentManager.getDueFeesStudents(instituteId,
				academicSessionId, dueFeesDate, feePaymentReminderPayload.isComputeFine(),
				feePaymentReminderPayload.getRequiredStanardsCSV(), feePaymentReminderPayload.getTotalSessionCount());
		Map<UUID, StudentDueFeesData> studentDueFeesDataMap = getStudentDueFeesDataMap(studentDueFeesDetailsList);

		List<StudentDueFeesData> requestedStudents = new ArrayList<StudentDueFeesData>();
		for (UUID studentId : studentIds) {
			if (studentDueFeesDataMap.containsKey(studentId)) {
				requestedStudents.add(studentDueFeesDataMap.get(studentId));
			}
		}
		DemandNoticeGenerator demandNoticeGenerator = demandNoticeGeneratorFactory
				.getDemandNoticeGenerator(instituteId);
		return demandNoticeGenerator.generateDemandNotices(instituteManager.getInstitute(instituteId),
				requestedStudents, feePaymentReminderPayload.isComputeFine(), DEMAND_NOTICE_DOCUMENT_NAME);

	}

	private Map<UUID, StudentDueFeesData> getStudentDueFeesDataMap(List<StudentDueFeesData> studentDueFeesDatas) {
		Map<UUID, StudentDueFeesData> studentDueFeesDataMap = new HashMap<>();
		for (StudentDueFeesData studentDueFeesData : studentDueFeesDatas) {
			studentDueFeesDataMap.put(studentDueFeesData.getStudent().getStudentId(), studentDueFeesData);
		}
		return studentDueFeesDataMap;
	}

}
