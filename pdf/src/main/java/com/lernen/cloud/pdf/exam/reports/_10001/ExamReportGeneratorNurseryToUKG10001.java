package com.lernen.cloud.pdf.exam.reports._10001;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.AreaBreakType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportGeneratorNurseryToUKG10001 extends ExamReportGenerator10001 implements IExamReportCardGenerator {
	public ExamReportGeneratorNurseryToUKG10001(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGeneratorNurseryToUKG10001.class);

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
			ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			Image examControllerSignature = getExamControllerSignature();
			generateStudentReport(institute, examReportData.getStudentLite(), reportType, examReportData, examReportCardLayoutData, 1, examControllerSignature);
			examReportCardLayoutData.getDocument().close();

			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
													List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			sortClassExamReports(examReportDataList);
			Image examControllerSignature = getExamControllerSignature();
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReport(institute, examReportData.getStudentLite(), reportType, examReportData,
						examReportCardLayoutData, pageNumber, examControllerSignature);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}

			examReportCardLayoutData.getDocument().close();

			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;

	}

	private void generateStudentReport(Institute institute, StudentLite studentLite, String reportType,
			ExamReportData examReportData, ExamReportCardLayoutData examReportCardLayoutData, int pageNumber, Image examControllerSignature)
			throws IOException {
		addBlankLine(examReportCardLayoutData, false, 1);

		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.85f);
		addBlankLine(examReportCardLayoutData, false, 1);
		generateStudentInformation(examReportCardLayoutData, studentLite);

		addBlankLine(examReportCardLayoutData, true, 1);
		addBlankLine(examReportCardLayoutData, false, 2);
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "Scholastic Subjects",
				getScholasticMarksGridSubjectWidth(reportType), null, null);

		addBlankLine(examReportCardLayoutData, true, 1);
		generateScholasticExamDescription(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportData);

		addBlankLine(examReportCardLayoutData, true, 1);
		generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "Co-Scholastic Subjects",
				getCoScholasticMarksGridSubjectWidth(reportType));

		/*
		 * govt notice line only for session 19 for 10001
		 */
				
		if(examReportData.getStandardMetaData().getAcademicSessionId() == 19) {
			generateCoScholasticStarMarkDescription(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
					examReportData, examReportCardLayoutData);
		}
		
		if(examReportData.getStandardMetaData().getAcademicSessionId() != 19) {
			addBlankLine(examReportCardLayoutData, true, 1);
			generateStudentAttributeTable(examReportCardLayoutData, examReportData);
			addBlankLine(examReportCardLayoutData, true, 1);
		} else {
			addBlankLine(examReportCardLayoutData, false, 2);
		}

		generateResultSummary(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examReportData, reportType);

		addBlankLine(examReportCardLayoutData, false, 2);
		generateRemarksSection(examReportCardLayoutData, examReportData);
		generateSignatureBox(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examControllerSignature);

		generateBorderLayout(examReportCardLayoutData, pageNumber);
	}

	protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
													 float contentFontSize, ExamReportData examReportData) throws IOException {
		Text descTitle = new Text("Description:\n").setFont(getRegularBoldFont()).setFontSize(contentFontSize);

		Text descText = new Text("SA : Summative Assessment, FA : Formative  Assessment").setFontSize(contentFontSize - 2);
		Paragraph desc = new Paragraph();
		desc.add(descTitle).add(descText);
		document.add(desc);
	}

}
