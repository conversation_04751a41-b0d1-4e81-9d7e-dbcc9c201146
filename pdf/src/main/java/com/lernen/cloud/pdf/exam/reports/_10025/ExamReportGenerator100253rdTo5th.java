package com.lernen.cloud.pdf.exam.reports._10025;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData10025;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.examination.report.ExamReportMarksGrid;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportGenerator100253rdTo5th extends ExamReportGenerator10025 implements IExamReportCardGenerator {

	public ExamReportGenerator100253rdTo5th(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator100253rdTo5th.class);

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
			ExamReportData examReportData, String documentName, StudentManager studentManager) {
		LoadItextLiscence();
		try {
			DocumentOutput report = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData10025 examReportCardLayoutData = generateReportCardLayoutData(institute, student,
					reportType, examReportData, report, studentManager);

			generateMetaDataLayout(examReportCardLayoutData, examReportData, institute, student, studentManager,
					reportType);
			generateMarksGrid(examReportCardLayoutData, examReportData);
			generateFooterDataLayout(examReportCardLayoutData, examReportData);
			examReportCardLayoutData.getDocument().close();
			return report;
		} catch (Exception e) {
			logger.error("Error while generating report card for 3rdTo5th institute {}, student {} ",
					institute.getInstituteId(), student.getStudentId(), e);
		}
		return null;
	}

	protected void generateMarksGrid(ExamReportCardLayoutData examReportCardLayoutData, ExamReportData examReportData)
			throws IOException {
		Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid = examReportData
				.getCourseTypeExamReportMarksGrid();

		if (courseTypeExamReportMarksGrid.containsKey(CourseType.SCHOLASTIC)) {

			generateScholasticMarksGrid(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getHindiRegularFont(),
					examReportCardLayoutData.getHindiRegularFont(), examReportCardLayoutData.getContentFontSize(),
					examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "विषय", 0.15f, null,
					"पूर्णांक", "प्राप्तांक", null);
		}
		if (courseTypeExamReportMarksGrid.containsKey(CourseType.COSCHOLASTIC)) {
			addBlankLine(examReportCardLayoutData.getDocument(), true, 1);
			generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getHindiRegularFont(),
					examReportCardLayoutData.getHindiRegularFont(), examReportCardLayoutData.getContentFontSize(),
					examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "विषय", 0.22f, "पूर्णांक",
					"प्राप्तांक");
			addBlankLine(examReportCardLayoutData.getDocument(), true, 1);
			addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		}
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
													List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {
		// TODO Auto-generated method stub
		return null;
	}

}
