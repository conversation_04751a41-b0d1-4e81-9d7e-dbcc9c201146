package com.lernen.cloud.pdf.exam.reports._10025;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamCourseMarks;
import com.lernen.cloud.core.api.examination.ExamDimensionObtainedValues;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.StudentExamMarksDetailsLite;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.lib.examination.ExamMarksUtils;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.Map.Entry;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportGenerator100259thTo10th extends ExamReportGenerator10025 implements IExamReportCardGenerator {

	public ExamReportGenerator100259thTo10th(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator100259thTo10th.class);

	private static final Map<String, Map<String, CourseDisplay>> CUSTOM_COURSE_EXAMS_MAP = new LinkedHashMap<>();
	private static final Map<String, CourseDisplay> SAMAJ_COURSE_EXAMS_MAP = new LinkedHashMap<>();
	private static final Map<String, CourseDisplay> ARTS_COURSE_EXAMS_MAP = new LinkedHashMap<>();
	private static final Map<String, Pair<Double, Double>> DIVISION_MAP = new LinkedHashMap<>();

	static {
		SAMAJ_COURSE_EXAMS_MAP.put("अनिवार्य प्रवृत्ति",
				new CourseDisplay("अनिवार्य प्रवृत्ति", "अनिवार्य प्रवृत्ति", 4));
		SAMAJ_COURSE_EXAMS_MAP.put("वैकल्पिक प्रवृत्ति",
				new CourseDisplay("वैकल्पिक प्रवृत्ति", "वैकल्पिक प्रवृत्ति", 4));
		SAMAJ_COURSE_EXAMS_MAP.put("शिविर", new CourseDisplay("शिविर", "शिविर", 3));

		ARTS_COURSE_EXAMS_MAP.put("कला सैद्धांतिक", new CourseDisplay("कला सैद्धांतिक", "सैद्धांतिक", 4));
		ARTS_COURSE_EXAMS_MAP.put("कला प्रायोगिक प्रथम", new CourseDisplay("कला प्रायोगिक प्रथम", "प्रायो.", 2));
		ARTS_COURSE_EXAMS_MAP.put("कला प्रायोगिक द्वितीय", new CourseDisplay("कला प्रायोगिक द्वितीय", "प्रायो.", 2));
		ARTS_COURSE_EXAMS_MAP.put("प्रस्तुति कार्य", new CourseDisplay("प्रस्तुति कार्य", "प्रस्तुति कार्य", 3));
	}

	static {
		CUSTOM_COURSE_EXAMS_MAP.put("समाजपयोगी उत्पादक कार्य एवं समाज सेवा", SAMAJ_COURSE_EXAMS_MAP);
		CUSTOM_COURSE_EXAMS_MAP.put("कला शिक्षा", ARTS_COURSE_EXAMS_MAP);
	}

	static {
		DIVISION_MAP.put("विशेष योग्यता", new Pair<Double, Double>(1d, 0.75d));
		DIVISION_MAP.put("प्रथम", new Pair<Double, Double>(0.6, 0.75d));
		DIVISION_MAP.put("द्वितीय", new Pair<Double, Double>(0.48d, 0.6d));
		DIVISION_MAP.put("तृतीय", new Pair<Double, Double>(0.36d, 0.48d));
		DIVISION_MAP.put("", new Pair<Double, Double>(0.0d, 0.36d));
	}

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
			ExamReportData examReportData, String documentName, StudentManager studentManager) {
		LoadItextLiscence();
		try {
			DocumentOutput report = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData10025 examReportCardLayoutData = generateReportCardLayoutData(institute, student,
					reportType, examReportData, report, studentManager, false, 14f);

			generateMetaDataLayout(examReportCardLayoutData, examReportData, institute, student, studentManager,
					reportType);
			generateMarksGrid(examReportCardLayoutData, examReportData);
			generateFooterDataLayout(examReportCardLayoutData, examReportData);
			examReportCardLayoutData.getDocument().close();
			return report;
		} catch (Exception e) {
			logger.error("Error while generating report card for 9thTo10th institute {}, student {} ",
					institute.getInstituteId(), student.getStudentId(), e);
		}
		return null;
	}

	protected void generateScholasticStarMarkDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
			PdfFont regularFont, float contentFontSize, ExamReportData examReportData) throws IOException {
		Text descTitle = new Text("* ").setFont(regularFont).setFontSize(contentFontSize);

		Text descText = new Text("राजस्थान अध्ययन के अंक कुल योग में नहीं जोड़े गये हैं। ").setFont(regularFont)
				.setFontSize(contentFontSize - 2);
		Paragraph desc = new Paragraph();
		desc.add(descTitle).add(descText);
		document.add(desc);
	}

	protected void generateMetaDataLayout(ExamReportCardLayoutData10025 examReportCardLayoutData,
			ExamReportData examReportData, Institute institute, Student student, StudentManager studentManager,
			String reportType) throws IOException {
		generateFirstPage(examReportCardLayoutData, examReportData, institute, student, studentManager);

		examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		generateHeader(examReportCardLayoutData, student, institute, reportType, 35f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.86f);
		generateStudentInformation(examReportCardLayoutData, student);
		generateBorderLayout(examReportCardLayoutData, 2);
	}

	protected void generateMarksGrid(ExamReportCardLayoutData examReportCardLayoutData, ExamReportData examReportData)
			throws IOException {
		Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid = examReportData
				.getCourseTypeExamReportMarksGrid();

		if (courseTypeExamReportMarksGrid.containsKey(CourseType.SCHOLASTIC)) {

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(examReportCardLayoutData.getHindiRegularFont())
					.setFontSize(examReportCardLayoutData.getContentFontSize())
					.setBorder(new SolidBorder(examReportCardLayoutData.getDefaultBorderWidth()))
					.setTextAlignment(TextAlignment.CENTER).setPadding(0f);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy();

			generateScholasticMarksGrid(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getHindiRegularFont(),
					examReportCardLayoutData.getHindiRegularFont(), examReportCardLayoutData.getContentFontSize(),
					examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
					ExamReportGenerator.SUBJECT_HINDI, 0.15f, null, ExamReportGenerator.MAX_MARKS_HINDI_SHORT,
					ExamReportGenerator.OBTAINED_MARKS_HINDI_SHORT, courseCellLayoutSetup, marksCellLayoutSetup, null);

			generateScholasticStarMarkDescription(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getHindiRegularFont(),
					examReportCardLayoutData.getContentFontSize(), examReportData);
		}
		if (courseTypeExamReportMarksGrid.containsKey(CourseType.COSCHOLASTIC)) {
			generateCoScholasticGrid(examReportCardLayoutData, examReportData, courseTypeExamReportMarksGrid);
		}
	}

	private void generateCoScholasticGrid(ExamReportCardLayoutData examReportCardLayoutData,
			ExamReportData examReportData, Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid)
			throws IOException {
		CourseType courseType = CourseType.COSCHOLASTIC;
		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(examReportCardLayoutData.getHindiRegularFont())
				.setFontSize(examReportCardLayoutData.getContentFontSize())
				.setBorder(new SolidBorder(examReportCardLayoutData.getDefaultBorderWidth()))
				.setTextAlignment(TextAlignment.CENTER).setPadding(0f);

		CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy();

		ExamReportStructureMetaData examReportStructureMetaData = examReportData.getExamReportStructure()
				.getExamReportStructureMetaData();

		ExamReportMarksGrid examReportMarksGrid = courseTypeExamReportMarksGrid.get(courseType);
		float subjectColumnWidth = 0.18f;

//		boolean showGrade = examReportData.getStandardMetaData().isCoScholasticGradingEnabled();
		// Since we dont want any total marks to be shown, making show grade as true to
		// skip showing total marks row
		boolean showGrade = false;
		float[] columnWidths = getMarksGridWidth(examReportMarksGrid, courseType, false, examReportStructureMetaData,
				subjectColumnWidth, showGrade, true, 0.03f);
		Table headerTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), columnWidths);
		String maxMarksTitle = ExamReportGenerator.MAX_MARKS_HINDI_SHORT;
		String marksObtainedTitle = ExamReportGenerator.OBTAINED_MARKS_HINDI_SHORT;
		addRow(headerTable, examReportCardLayoutData.getDocumentLayoutSetup(),
				getHeaderCells(examReportMarksGrid, examReportData, courseType,
						examReportCardLayoutData.getDefaultBorderWidth(),
						examReportCardLayoutData.getHindiRegularFont(), examReportCardLayoutData.getHindiRegularFont(),
						examReportCardLayoutData.getContentFontSize() - 2,
						examReportCardLayoutData.getContentFontSize(), subjectColumnWidth,
						ExamReportGenerator.SUBJECT_HINDI, showGrade, maxMarksTitle, marksObtainedTitle, true));

		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportMarksGrid.getExamReportCourseMarksRows()) {

			addCustomExamCourseRow(examReportCardLayoutData.getDocumentLayoutSetup(), examReportStructureMetaData,
					courseType, headerTable, marksCellLayoutSetup, courseCellLayoutSetup, examReportCourseMarksRow,
					showGrade, maxMarksTitle, marksObtainedTitle, 1);
		}
		List<StudentExamMarksDetailsLite> studentExamMarksDetailsLiteList = examReportData.getStudentExamMarksDetailsLiteList();

		Map<String, Map<String, ExamCourseMarks>> customCourseExamMap = getCourseMap(studentExamMarksDetailsLiteList);

		int columns = columnWidths.length - 2;
		for (Entry<String, Map<String, CourseDisplay>> courseEntry : CUSTOM_COURSE_EXAMS_MAP.entrySet()) {
			if (!customCourseExamMap.containsKey(courseEntry.getKey())) {
				continue;
			}
//				int marksColSpan = columns / (courseEntry.getValue().size() + 2);
//				int remainingMarksCols = columns % (courseEntry.getValue().size() + 2);

			List<CellData> maxMarksRow = new ArrayList<>();
			List<CellData> obtainedMarksRow = new ArrayList<>();

			maxMarksRow.add(new CellData(courseEntry.getKey(), courseCellLayoutSetup, 2, 1));
			maxMarksRow.add(new CellData(maxMarksTitle, courseCellLayoutSetup));
			obtainedMarksRow.add(new CellData(marksObtainedTitle, courseCellLayoutSetup));

			Double totalColumnMaxMarks = null;
			Double totalColumnObtainedMarks = null;

			for (Entry<String, CourseDisplay> examEntry : courseEntry.getValue().entrySet()) {
				String examName = examEntry.getKey();
				if (!customCourseExamMap.get(courseEntry.getKey()).containsKey(examName)) {
					continue;
				}
				ExamCourseMarks examCourseMarks = customCourseExamMap.get(courseEntry.getKey()).get(examName);
				for (ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
						.getExamDimensionObtainedValues()) {
					if (!examDimensionObtainedValues.getExamDimension().isTotal()) {
						continue;
					}
					String marks = examDimensionObtainedValues.getMaxMarks() == null
							? examEntry.getValue().getDisplayName()
							: examEntry.getValue().getDisplayName() + "-"
									+ String.valueOf(Math.round(examDimensionObtainedValues.getMaxMarks()));
					totalColumnMaxMarks = ExamMarksUtils.addValues(totalColumnMaxMarks,
							examDimensionObtainedValues.getMaxMarks());
					totalColumnObtainedMarks = ExamMarksUtils.addValues(totalColumnObtainedMarks,
							examDimensionObtainedValues.getObtainedMarks());

					maxMarksRow.add(new CellData(marks, marksCellLayoutSetup, 1, examEntry.getValue().getColSpan()));
					addObtainedMarksInCell(marksCellLayoutSetup, showGrade, examEntry.getValue().getColSpan(),
							obtainedMarksRow, ExamReportGridColumnType.EXAM, examDimensionObtainedValues);
				}
			}
			maxMarksRow.add(new CellData(
					totalColumnMaxMarks == null ? ExamReportGenerator.TOTAL_HINDI
							: ExamReportGenerator.TOTAL_HINDI + "-" + String.valueOf(totalColumnMaxMarks),
					marksCellLayoutSetup, 1, 2));

			maxMarksRow.add(new CellData(ExamReportGenerator.GRADE_HINDI, marksCellLayoutSetup, 1, 2));

			obtainedMarksRow
					.add(new CellData(totalColumnObtainedMarks == null ? "-" : String.valueOf(totalColumnObtainedMarks),
							marksCellLayoutSetup, 1, 2));

			Double percent = null;
			if (totalColumnMaxMarks != null && totalColumnObtainedMarks != null && totalColumnMaxMarks > 0d) {
				percent = totalColumnObtainedMarks / totalColumnMaxMarks;
			}
			ExamGrade examGrade = ExamMarksUtils.getExamGradeByPercentage(
					examReportData.getCourseTypeExamGrades().get(CourseType.COSCHOLASTIC), percent);

			obtainedMarksRow
					.add(new CellData(examGrade == null ? "-" : examGrade.getGradeName(), marksCellLayoutSetup, 1, 2));

			maxMarksRow.addAll(obtainedMarksRow);
			addRow(headerTable, examReportCardLayoutData.getDocumentLayoutSetup(), maxMarksRow);
		}

		examReportCardLayoutData.getDocument().add(headerTable);
	}

	private Map<String, Map<String, ExamCourseMarks>> getCourseMap(
			List<StudentExamMarksDetailsLite> studentExamMarksDetailsList) {
		Map<String, Map<String, ExamCourseMarks>> customCourseExamMap = new HashMap<>();
		for (StudentExamMarksDetailsLite studentExamMarksDetails : studentExamMarksDetailsList) {
			String examName = studentExamMarksDetails.getExamMetaData().getExamName();
			if (CollectionUtils.isEmpty(studentExamMarksDetails.getCourseMarksMatrix())) {
				continue;
			}
			if (CollectionUtils.isEmpty(studentExamMarksDetails.getCourseMarksMatrix().get(CourseType.COSCHOLASTIC))) {
				continue;
			}

			List<ExamCourseMarks> examCourseMarksList = studentExamMarksDetails.getCourseMarksMatrix()
					.get(CourseType.COSCHOLASTIC);

			for (ExamCourseMarks examCourseMarks : examCourseMarksList) {
				String courseName = examCourseMarks.getCourse().getCourseName();
				if (!CUSTOM_COURSE_EXAMS_MAP.containsKey(courseName.trim())
						|| !CUSTOM_COURSE_EXAMS_MAP.get(courseName).containsKey(examName.trim())) {
					continue;
				}

				if (!customCourseExamMap.containsKey(courseName)) {
					customCourseExamMap.put(courseName, new HashMap<>());
				}
				customCourseExamMap.get(courseName).put(examName, examCourseMarks);
			}
		}
		return customCourseExamMap;
	}

	public void addCustomExamCourseRow(DocumentLayoutSetup documentLayoutSetup,
			ExamReportStructureMetaData examReportStructureMetaData, CourseType courseType, Table headerTable,
			CellLayoutSetup marksCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup,
			ExamReportCourseMarksRow examReportCourseMarksRow, boolean showGrade, String maxMarksTitle,
			String marksObtainedTitle, int marksColSpan) {
		List<CellData> maxMarksRow = new ArrayList<>();
		List<CellData> obtainedMarksRow = new ArrayList<>();

		maxMarksRow
				.add(new CellData(examReportCourseMarksRow.getCourse().getCourseName(), courseCellLayoutSetup, 2, 1));
		maxMarksRow.add(new CellData(maxMarksTitle, courseCellLayoutSetup));
		obtainedMarksRow.add(new CellData(marksObtainedTitle, courseCellLayoutSetup));
		for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportCourseMarksRow
				.getExamReportCourseMarksColumns()) {

			for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportCourseMarksColumn
					.getExamDimensionObtainedValuesList()) {
				String marks = examDimensionObtainedValues.getMaxMarks() == null ? "-"
						: String.valueOf(Math.round(examDimensionObtainedValues.getMaxMarks()));

				maxMarksRow.add(new CellData(marks, marksCellLayoutSetup, 1, marksColSpan));
				addObtainedMarksInCell(marksCellLayoutSetup, showGrade, marksColSpan, obtainedMarksRow,
						examReportCourseMarksColumn.getExamReportGridColumnType(), examDimensionObtainedValues);
			}
		}
		maxMarksRow.addAll(obtainedMarksRow);
		addRow(headerTable, documentLayoutSetup, maxMarksRow);
	}

	private void addObtainedMarksInCell(CellLayoutSetup marksCellLayoutSetup, boolean showGrade, int marksColSpan,
			List<CellData> row, ExamReportGridColumnType examReportGridColumnType,
			ExamDimensionObtainedValues examDimensionObtainedValues) {
		String marks = null;
		if (showGrade || examReportGridColumnType == ExamReportGridColumnType.GRADE
				|| examReportGridColumnType == ExamReportGridColumnType.EXAM_GRADE) {
			marks = examDimensionObtainedValues.getObtainedGrade() == null ? "-"
					: examDimensionObtainedValues.getObtainedGrade().getGradeName();
		} else if (examReportGridColumnType == ExamReportGridColumnType.PERCENT) {
			marks = examDimensionObtainedValues.getObtainedMarksFraction() == null ? "-"
					: String.valueOf(Math.round(examDimensionObtainedValues.getObtainedMarksFraction() * 1000) / 10d);
		} else {
			marks = examDimensionObtainedValues.getObtainedMarks() == null ? "-"
					: String.valueOf(Math.round(examDimensionObtainedValues.getObtainedMarks()));
		}

		if (marks == null) {
			marks = "-";
		}
		row.add(new CellData(marks, marksCellLayoutSetup, 1, marksColSpan));
	}

	protected void generateFooterDataLayout(ExamReportCardLayoutData examReportCardLayoutData,
			ExamReportData examReportData) throws IOException {

//		generateStudentAttributeTable(examReportCardLayoutData, examReportData);
		generateResultSummary(examReportCardLayoutData, examReportData);

		generateSignatureBox(examReportCardLayoutData);
	}

	protected void generateResultSummary(ExamReportCardLayoutData examReportCardLayoutData,
			ExamReportData examReportData) throws IOException {
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		PdfFont pdfFont = examReportCardLayoutData.getHindiRegularFont();
		int singleContentColumn = 3;

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.25f, 0.25f, 0.25f, 0.25f, });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(examReportCardLayoutData.getContentFontSize())
				.setTextAlignment(TextAlignment.LEFT);

		Paragraph result = getKeyValueParagraph("परिणाम : ",
				examReportData.getExamResultStatus() == null ? "-" : examReportData.getExamResultStatus().name(),
				pdfFont);

		Paragraph promotedClass = getKeyValueParagraph("अगली कक्षा : ",
				examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName(),
				pdfFont);
		Paragraph obtainedMarks = getKeyValueParagraph("प्राप्तांक : ",
				examReportData.getTotalObtainedMarks() == null ? "-"
						: examReportData.getTotalObtainedMarks() * 10 % 10 == 0
								? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
								: String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d),
				pdfFont);
		Paragraph totalMarks = getKeyValueParagraph("पूर्णांक : ", examReportData.getTotalMaxMarks() == null ? "-"
				: String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10), pdfFont);
		Paragraph percentage = getKeyValueParagraph("प्रतिशत : ", examReportData.getPercentage() == null ? "-"
				: String.valueOf(Math.round(examReportData.getPercentage() * 100) / 100d) + "%", pdfFont);
		
//		Paragraph grade = getKeyValueParagraph("श्रेणी : ", getDivision(examReportData.getPercentage()), pdfFont);
//		Paragraph rank = null;
//		if (examReportData.getRank() != null && examReportData.getRank() <= RANK_LIMIT) {
//			rank = getKeyValueParagraph("स्थान : ",
//					examReportData.getRank() == null ? "-" : String.valueOf(examReportData.getRank()), pdfFont);
//		}
		Paragraph totalWorkingDays = getKeyValueParagraph("कुल दिन : ",
				examReportData.getTotalWorkingDays() == null ? "-" : String.valueOf(examReportData.getTotalWorkingDays()), pdfFont);
		Paragraph totalAttendedDays = getKeyValueParagraph("उपस्तिथ दिन : ",
				examReportData.getTotalAttendedDays() == null ? "-" : String.valueOf(examReportData.getTotalAttendedDays()), pdfFont);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(result, cellLayoutSetup), new CellData(promotedClass, cellLayoutSetup),
						new CellData(totalMarks, cellLayoutSetup), new CellData(obtainedMarks, cellLayoutSetup)));
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(new CellData(percentage, cellLayoutSetup), new CellData(grade, cellLayoutSetup),
//						new CellData(rank == null ? new Paragraph(EMPTY_TEXT) : rank, cellLayoutSetup),
//						new CellData(EMPTY_TEXT, cellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(percentage, cellLayoutSetup), new CellData(totalWorkingDays, cellLayoutSetup), 
						new CellData(totalAttendedDays, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));

		examReportCardLayoutData.getDocument().add(table);

		Table remarksTable = getPDFTable(documentLayoutSetup, 1);
		Paragraph remarks = getKeyValueParagraph("विवरण : ",
				examReportData.getRemarks() == null ? "" : examReportData.getRemarks(), pdfFont);
		addRow(remarksTable, documentLayoutSetup, Arrays.asList(remarks), cellLayoutSetup);
		examReportCardLayoutData.getDocument().add(remarksTable);
	}

	private String getDivision(Double percent) {
		if(percent == null) {
			return "";
		}
		Double fraction = percent/100;
		for(Entry<String, Pair<Double, Double>> entry : DIVISION_MAP.entrySet()) {
			if(fraction < entry.getValue().getFirst() && fraction >= entry.getValue().getSecond()){
				return entry.getKey();
			}
		}
		return "";
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
													List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {
		// TODO Auto-generated method stub
		return null;
	}

	private static class CourseDisplay {
		private final String courseName;
		private final String displayName;
		private final int colSpan;

		public CourseDisplay(String courseName, String displayName, int colSpan) {
			this.courseName = courseName;
			this.displayName = displayName;
			this.colSpan = colSpan;
		}

		public String getCourseName() {
			return courseName;
		}

		public String getDisplayName() {
			return displayName;
		}

		public int getColSpan() {
			return colSpan;
		}

	}
}
