package com.lernen.cloud.pdf.exam.reports._10160;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportCourseMarksRow;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.ExamResultStatus;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridHeaderConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridTotalMarksRowConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportGeneratorLandscape10160 extends ExamReportGenerator implements IExamReportCardGenerator {

	public ExamReportGeneratorLandscape10160(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;

	protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";
	protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";

	private static final  String GRADE_A1 = "Outstanding performance and has extraordinary thinking.";
	private static final  String GRADE_A2 = "Excellent effort, follow deadlines and maintain decency.";
	private static final  String GRADE_B1 = "Gracefully takes the task and has tendency to do better.";
	private static final  String GRADE_B2 = "Well behaved, has the capacity to work hard in order to reach heights.";
	private static final  String GRADE_C1 = "Well behaved, has the capacity to work hard in order to reach heights.";
	private static final  String GRADE_C2 = "Average performance but innovative.";
	private static final  String GRADE_D = "Needs to get serious towards studies and maintain sense of humour.";
	private static final  String GRADE_E = "Needs to be very attentive and work hard in order to get promoted.";
	private static final String GRADING_SCHEME = "Grading Scheme : 91 - 100 (A1), 81 - 90 (A2), 71 - 80 (B1), 61 - 70 (B2), 51 - 60 (C1), 41 - 50 (C2), 33 - 40 (D), 32 & Below (E)";
	private static final String SCHOLASTIC_EXAM_DESCRIPTION = "PA : Periodic Assessment, SA : Summative Assessment, SEA : Subject Enrichment Activity, FA : Formative Assessment, NB : Notebook";
	private static final Logger logger = LogManager.getLogger(ExamReportGeneratorLandscape10160.class);

	private static final Map<String, String> MARKS_GRADE_MAP = new LinkedHashMap<>();
	static {
		MARKS_GRADE_MAP.put("91 - 100", "A1");
		MARKS_GRADE_MAP.put("81 - 90", "A2");
		MARKS_GRADE_MAP.put("71 - 80", "B1");
		MARKS_GRADE_MAP.put("61 - 70", "B2");
		MARKS_GRADE_MAP.put("51 - 60", "C1");
		MARKS_GRADE_MAP.put("41 - 50", "C2");
		MARKS_GRADE_MAP.put("33 - 40", "D");
		MARKS_GRADE_MAP.put("32 & Below", "E");
	}

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
			ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			Document document = examReportCardLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
			Standard promotedStandard = studentManager.getNextPropmotionStandard(institute.getInstituteId(),
					examReportData.getStudentLite().getStudentSessionData().getAcademicSessionId(),
					examReportData.getStudentLite().getStudentId());
			generateStudentReport(document, documentLayoutSetup, institute,
					examReportData.getStudentLite(), reportType, examReportData, examReportCardLayoutData, 1,
					promotedStandard);
			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	private void generateStudentReport(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute, StudentLite studentLite, String reportType,
			ExamReportData examReportData, ExamReportCardLayoutData examReportCardLayoutData, int pageNumber,
									   Standard promotedStandard)
			throws IOException {

		float watermarkImageHeightWidth = 400f;
		generateWatermark(examReportCardLayoutData, institute.getInstituteId(),
				watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20, watermarkImageHeightWidth / 2);

		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.86f);
		addBlankLine(examReportCardLayoutData, false, 1);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData);
		generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType,
				new GridConfigs(new GridHeaderConfigs(false), new GridTotalMarksRowConfigs(true, false, true, true, false)),
				"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType));
		generateScholasticExamDescription(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examReportData, examReportCardLayoutData.getRegularFont(),
				examReportCardLayoutData.getBoldFont(), SCHOLASTIC_EXAM_DESCRIPTION);
		generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "Co-Scholastic Subjects",
				getCoScholasticMarksGridSubjectWidth(reportType));
//		generateScholasticExamDescription(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
//				examReportCardLayoutData.getContentFontSize(), examReportData, examReportCardLayoutData.getRegularFont(),
//				examReportCardLayoutData.getBoldFont(), GRADING_SCHEME);
		generateResultSummary(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examReportData, reportType, promotedStandard);
		generateRemarksSection(examReportCardLayoutData, examReportData);
		generateSignatureBox(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examReportCardLayoutData.getDefaultBorderWidth(), pageNumber,
				examReportData.getStandardMetaData().getStandardId());
		generateBorderLayout(examReportCardLayoutData, pageNumber);
	}

	protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
			ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont, String descriptionText) throws IOException {
		Text descText = new Text(descriptionText).setFontSize(contentFontSize - 2).setFont(regularFont);
		Paragraph desc = new Paragraph();
		desc.add(descText);
		document.add(desc);
	}
	private void generateCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
			ExamReportData examReportData, String reportType, GridConfigs gridConfigs, String subjectColumnTitle,
			float subjectColumnWidth) throws IOException {
		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, nonAdditionalSubjects, null);

	}

	protected float getScholasticMarksGridSubjectWidth(String reportType) {
		return 0.1f;
	}

	protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.5f;
		}
		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			return 0.4f;
		}
		return 0.3f;
	}

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4.rotate());
		float contentFontSize = 10f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

		float logoWidth = 75f;
		float logoHeight = 75f;

		ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup,
				getRegularFont(), getRegularBoldFont(), contentFontSize, defaultBorderWidth, logoWidth, logoHeight,
				LogoProvider.INSTANCE.getLogo(institute.getInstituteId()));
		examReportCardLayoutData.setBuildingImage(ImageProvider.INSTANCE.getImage(ImageProvider._10030_BUILDING));
		return examReportCardLayoutData;
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
								  Institute institute, String reportType, float offsetX, float offsetY) throws IOException {

		generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		int singleContentColumn = 1;
		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName())
						.setFontColor(Color.convertRgbToCmyk(new DeviceRgb(57, 65, 158))).setMultipliedLeading(1.2f)),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 12));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1() + ", " + institute.getLetterHeadLine2())
						.setFontColor(Color.convertRgbToCmyk(new DeviceRgb(204, 0, 0)))),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 1));

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		String headerExamTitle = "PROGRESS REPORT ";
		headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 1).setPdfFont(getRegularBoldFont()));
		examReportCardLayoutData.getDocument().add(table);

	}

	protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
											  StudentLite studentLite, ExamReportData examReportData) throws IOException {

		PdfFont boldFont = getRegularBoldFont();
		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize();

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.33f, 0.01f, 0.32f, 0.01f, 0.33f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup fourthCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

		Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName().toUpperCase(), 57, 65, 158, boldFont, boldFont);
		Paragraph fatherName = getKeyValueParagraph("Father Name : ", studentLite.getFathersName().toUpperCase(), 57, 65, 158, boldFont, boldFont);
		Paragraph dob = getKeyValueParagraph("DOB : ",
				studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
						: DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE), 57, 65, 158, boldFont, boldFont);
		Paragraph motherName = getKeyValueParagraph("Mother Name : ", studentLite.getMothersName().toUpperCase(), 57, 65, 158, boldFont, boldFont);
		Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ", studentLite.getAdmissionNumber(), 57, 65, 158, boldFont, boldFont);
		Paragraph classValue = getKeyValueParagraph("Class : ",
				studentLite.getStudentSessionData().getStandardNameWithSection().toUpperCase(), 57, 65, 158, boldFont, boldFont);
		Paragraph rollNumber = getKeyValueParagraph("Roll Number : ", studentLite.getStudentSessionData().getRollNumber(), 57, 65, 158, boldFont, boldFont);
		Paragraph dateOfResultDeclaration = getKeyValueParagraph("Date Of Result Declaration : ", "29-03-2025",
				57, 65, 158, boldFont, boldFont);


		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(classValue, thirdCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(rollNumber, fourthCellLayoutSetup)));

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(fatherName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(motherName, secondCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(dateOfResultDeclaration, fourthCellLayoutSetup)));

		document.add(table);

	}

	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
				documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
	}

	protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
										 float contentFontSize, ExamReportData examReportData, String reportType,
										 Standard promotedStandard) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.33f, 0.01f, 0.32f, 0.01f, 0.33f });

		PdfFont boldFont = getRegularBoldFont();
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup fourthCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

		Paragraph noOfMeetings = getKeyValueParagraph("Number of Meetings : ", examReportData.getTotalWorkingDays() == null ? ""
				: String.valueOf(examReportData.getTotalWorkingDays()), 1, 155, 248, boldFont, boldFont);
		Paragraph noOfPresent = getKeyValueParagraph("Number of presents : ", examReportData.getTotalAttendedDays() == null ? ""
				: String.valueOf(examReportData.getTotalAttendedDays()), 1, 155, 248, boldFont, boldFont);
		Paragraph result = getKeyValueParagraph("Result : ", "-", boldFont);
		Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
				examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName(), 57, 65, 158, boldFont, boldFont);
		if(examReportData.getExamResultStatus() != null){
			switch (examReportData.getExamResultStatus()){
				case PASS:
					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
							57, 65, 158, boldFont, boldFont);
					break;
				case PASS_WITH_GRACE:
				case SUPPLEMENTARY:
				case FAIL:
					result = getKeyValueParagraph("Result : ", "Pass with Grace", 57, 65, 158, boldFont, boldFont);
					promotedClass = getKeyValueParagraph("Promoted to: ",
							promotedStandard == null ? "-" : promotedStandard.getStandardName(), 57, 65, 158, boldFont, boldFont);
					break;
			}
		}


		Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
				examReportData.getTotalObtainedMarks() == null ? "-"
						: examReportData.getTotalObtainedMarks() * 10 % 10 == 0
						? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
						: String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d), 57, 65, 158, boldFont, boldFont);
		Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
				: String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10), 57, 65, 158, boldFont, boldFont);
		Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
				: String.valueOf(Math.round(examReportData.getPercentage() * 100) / 100d) + "%", 57, 65, 158, boldFont, boldFont);
		Paragraph grade = getKeyValueParagraph("Grade : ",
				examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(), 57, 65, 158, boldFont, boldFont);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(obtainedMarks, thirdCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(totalMarks, fourthCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(result, firstCellLayoutSetup)));

		if (reportType.equalsIgnoreCase("ANNUAL")) {
			addRow(table, documentLayoutSetup,
					Arrays.asList(new CellData(promotedClass, secondCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
							new CellData(percentage, thirdCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
							new CellData(grade, fourthCellLayoutSetup)));
		} else {
			addRow(table, documentLayoutSetup,
					Arrays.asList(new CellData(percentage, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
							new CellData(grade, secondCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
							new CellData(EMPTY_TEXT, thirdCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
							new CellData(EMPTY_TEXT, fourthCellLayoutSetup)));
		}

		document.add(table);

	}

	protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
										  ExamReportData examReportData) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(examReportCardLayoutData.getContentFontSize())
				.setTextAlignment(TextAlignment.LEFT);

		Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 1);

		Paragraph remarks = getKeyValueParagraph("Remarks: ",
				examReportData.getTotalGrade() == null ? "" :
						StringUtils.isEmpty(getRemarks(examReportData.getTotalGrade().getGradeName()))
								? "" : getRemarks(examReportData.getTotalGrade().getGradeName()));

		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(remarks),
				cellLayoutSetup);
		examReportCardLayoutData.getDocument().add(remarksTable);
	}

	private String getRemarks(String grade) {
		switch (grade) {

			case "A1":
				return GRADE_A1;
			case "A2":
				return GRADE_A2;
			case "B1":
				return GRADE_B1;
			case "B2":
				return GRADE_B2;
			case "C1":
				return GRADE_C1;
			case "C2":
				return GRADE_C2;
			case "D":
				return GRADE_D;
			case "E":
				return GRADE_E;
			default:
				return null;

		}
	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
										float contentFontSize, float defaultBorderWidth, int pageNumber, UUID standardId) throws IOException {
		int singleContentColumn = 3;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);

		Set<UUID> _nurTo5thStandardIds = new HashSet<>(
				Arrays.asList(
						UUID.fromString("0ee0548f-e748-11ee-a3b6-124ce6a1ab45"),//Play Group
						UUID.fromString("210750b7-9be4-4ce4-ac66-54f0b429fd0a"),//Nur
						UUID.fromString("fb0c5000-a2a8-4f03-a02b-736ebf9b8357"),//LKG
						UUID.fromString("c9ece2a0-05cf-4273-afed-9bc96a203f74"),//UKG
						UUID.fromString("05ab4a40-ea47-4088-982b-75c42304e31f"),//I
						UUID.fromString("865c5c75-d1c2-4adb-9138-e8b816d9ca8e"),//II
						UUID.fromString("9e8c6cae-613f-49d8-b0ee-9bd768a41ed0"),//III
						UUID.fromString("0270291e-6fd7-4ed3-9a1b-4f148e542f58"),//IV
						UUID.fromString("23aad0c6-03a1-4698-952b-7dd60be6f8e5")//V
				)
		);

		Set<UUID> _6thTo8thStandardIds = new HashSet<>(
				Arrays.asList(
						UUID.fromString("7bfb4089-7642-4c01-8f9a-cba6ede1ed15"),//VI
						UUID.fromString("7cb7973b-8260-4c56-8727-d6a0c0143d45"),//VII
						UUID.fromString("fafb2b9a-c92f-4390-97ab-17069ff11be5")//VIII
				)
		);

		Image examInchargeSignatureImage = null;
		if(_nurTo5thStandardIds.contains(standardId)) {
			byte[] examInchargeSignatureByte = ImageProvider.INSTANCE.getImage(ImageProvider._10160_EXAM_INCHARGE_NUR_TO_5TH_SIGNATURE);
			examInchargeSignatureImage = new Image(ImageDataFactory.create(examInchargeSignatureByte));
		}

		if(_6thTo8thStandardIds.contains(standardId)) {
			byte[] examInchargeSignatureByte = ImageProvider.INSTANCE.getImage(ImageProvider._10160_EXAM_INCHARGE_6th_TO_8TH_SIGNATURE);
			examInchargeSignatureImage = new Image(ImageDataFactory.create(examInchargeSignatureByte));

		}

		examInchargeSignatureImage.setWidth(55f);
		examInchargeSignatureImage.setHeight(55f);
		Cell examInchargeSignatureCellImage = new Cell();
		examInchargeSignatureCellImage.add(examInchargeSignatureImage);
		examInchargeSignatureCellImage.setBorder(null);
		examInchargeSignatureCellImage.setHorizontalAlignment(HorizontalAlignment.CENTER);
		examInchargeSignatureCellImage.setTextAlignment(TextAlignment.CENTER);
		examInchargeSignatureCellImage.setPadding(0f);
		examInchargeSignatureCellImage.setPaddingLeft(105f);

		byte[] principalSignatureByte = ImageProvider.INSTANCE.getImage(ImageProvider._10160_PRINCIPAL_SIGNATURE_WITH_TEXT);
		Image principalSignatureImage = new Image(ImageDataFactory.create(principalSignatureByte));
		principalSignatureImage.setWidth(80f);
		principalSignatureImage.setHeight(55f);
		Cell principalSignatureCellImage = new Cell();
		principalSignatureCellImage.add(principalSignatureImage);
		principalSignatureCellImage.setBorder(null);
		principalSignatureCellImage.setHorizontalAlignment(HorizontalAlignment.CENTER);
		principalSignatureCellImage.setTextAlignment(TextAlignment.CENTER);
		principalSignatureCellImage.setPadding(0f);
		principalSignatureCellImage.setPaddingLeft(100f);

		Cell emptySignatureCell1 = new Cell();
		emptySignatureCell1.setBorder(null);

		addRow(table, Arrays.asList(emptySignatureCell1, examInchargeSignatureCellImage, principalSignatureCellImage));

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Class Teacher").setPaddingTop(0f), getParagraph("Exam Incharge").setPaddingTop(0f),
				getParagraph("Principal").setPaddingTop(0f)), signatureCellLayoutSetup);
		table.setFixedPosition(30f, 10f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);
	}

	protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
											  List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			Document document = examReportCardLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
			if(CollectionUtils.isEmpty(examReportDataList)) {
				examReportCardLayoutData.getDocument().close();
				return documentOutput;
			}
			sortClassExamReports(examReportDataList);
			ExamReportData firstExamReportData = examReportDataList.get(0);
			Standard promotedStandard = studentManager.getNextPropmotionStandard(institute.getInstituteId(),
					firstExamReportData.getStudentLite().getStudentSessionData().getAcademicSessionId(),
					firstExamReportData.getStudentLite().getStudentId());
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReport(document, documentLayoutSetup, institute, examReportData.getStudentLite(), reportType, examReportData,
						examReportCardLayoutData, pageNumber, promotedStandard);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}
			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;

	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			float logoWidth = 75f;
			float logoHeight = 75f;
			PdfFont boldFont = getRegularBoldFont();
			PdfFont regularFont = getRegularFont();
			int instituteId = institute.getInstituteId();

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateScholasticMarksGrid(document,
						documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
						"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
						null, "#39419e0");
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}

}
