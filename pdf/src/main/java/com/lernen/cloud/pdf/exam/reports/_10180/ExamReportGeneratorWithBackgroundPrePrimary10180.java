package com.lernen.cloud.pdf.exam.reports._10180;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportCourseMarksRow;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridHeaderConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridTotalMarksRowConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.StringHelper;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * <AUTHOR>
 *
 */
public class ExamReportGeneratorWithBackgroundPrePrimary10180 extends ExamReportGenerator implements IExamReportCardGenerator {

	public ExamReportGeneratorWithBackgroundPrePrimary10180(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGeneratorWithBackgroundPrePrimary10180.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;

	protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";
	protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";

//	dark purple 1b4a74	-		27, 74, 116
	protected static final int darkPurpleR = 27;
	protected static final int darkPurpleG = 74;
	protected static final int darkPurpleB = 116;
	protected static final DeviceRgb darkPurpleDeviceRgb = new DeviceRgb(darkPurpleR, darkPurpleG, darkPurpleB);

//	pink dc2388	-   	220, 35, 136
	protected static final int pinkR = 220;
	protected static final int pinkG = 35;
	protected static final int pinkB = 136;
	protected static final DeviceRgb pinkDeviceRgb = new DeviceRgb(pinkR, pinkG, pinkB);

	protected static final int blueR = 1;
	protected static final int blueG = 155;
	protected static final int blueB = 248;
	protected static final String AFFILIATION_NUMBER = "Affiliation : DEO/P/C/G-1/R/07/239(2012/984)2021/537";
	private static final  String GRADE_A_PLUS = "Excellent";
	private static final  String GRADE_A = "Outstanding";
	private static final  String GRADE_B = "Very good";
	private static final  String GRADE_C = "Good";
	private static final  String GRADE_D = "Satisfactory";

	// private static final Map<String, String> MARKS_GRADE_MAP = new LinkedHashMap<>();
	// static {
	// 	MARKS_GRADE_MAP.put("90 - 100", "A+");
	// 	MARKS_GRADE_MAP.put("70 - 89", "A");
	// 	MARKS_GRADE_MAP.put("50 - 69", "B");
	// 	MARKS_GRADE_MAP.put("30 - 49", "C");
	// 	MARKS_GRADE_MAP.put("00 - 29", "D");
	// }

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
										 ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();
			Document document = examReportCardLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
			generateStudentReport(document, documentLayoutSetup, institute, examReportData.getStudentLite(), reportType, examReportData, examReportCardLayoutData, 1,
					regularFont, boldFont, studentManager);
			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
													List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			Document document = examReportCardLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
			sortClassExamReports(examReportDataList);
			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReport(document, documentLayoutSetup, institute, examReportData.getStudentLite(), reportType, examReportData,
						examReportCardLayoutData, pageNumber, regularFont, boldFont, studentManager);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}
			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;

	}

	private void generateBackgroundImage(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
										 float height, float offsetX, float offsetY) {
		try {
			generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
		} catch (Exception e) {
			logger.error("Exception while adding background image", e);
		}

	}

	private void generateStudentReport(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute, StudentLite studentLite, String reportType,
									   ExamReportData examReportData, ExamReportCardLayoutData examReportCardLayoutData, int pageNumber,
			PdfFont regularFont, PdfFont boldFont, StudentManager studentManager) throws IOException {

		float bgImageWidth = documentLayoutSetup.getPageSize().getWidth();
		float bgImageHeight = documentLayoutSetup.getPageSize().getHeight();

		generateBackgroundImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(
				ImageProvider._10180_REPORT_CARD_BG), bgImageWidth, bgImageHeight, 0f, 0f);

		addBlankLine(examReportCardLayoutData, false, 1);
		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 30f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.88f, regularFont, boldFont);
		addBlankLine(examReportCardLayoutData, false, 2);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, regularFont, boldFont, institute.getInstituteId(), studentManager);
		addBlankLine(examReportCardLayoutData, false, 1);
		GridConfigs gridConfigs = new GridConfigs(new GridHeaderConfigs(false), new GridTotalMarksRowConfigs(
			true, false, false,
			false, false, false, false, false, false, "Overall Grade", "",
			"",  "","","", EMPTY_TEXT, EMPTY_TEXT, EMPTY_TEXT));
		generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType, gridConfigs,
				"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType));
		addBlankLine(examReportCardLayoutData, false, 1);
		addBlankLine(examReportCardLayoutData, false, 1);
		generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "Co-Scholastic Subjects",
				getCoScholasticMarksGridSubjectWidth(reportType));
		addBlankLine(examReportCardLayoutData, false, 3);
		generateGradeBox(document, documentLayoutSetup, examReportCardLayoutData, examReportData, examReportCardLayoutData.getContentFontSize(), examReportCardLayoutData.getDefaultBorderWidth(), boldFont, regularFont);
		addBlankLine(examReportCardLayoutData, false, 18);
		generateSignatureBox(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, regularFont, boldFont);
		generateBorderLayout(examReportCardLayoutData, pageNumber);
	}

	private void generateCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
												   ExamReportData examReportData, String reportType, GridConfigs gridConfigs, String subjectColumnTitle,
												   float subjectColumnWidth) throws IOException {
		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, nonAdditionalSubjects, null);

	}

	protected float getScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.2f;
		}
		return 0.2f;
	}

	protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.3f;
		}
		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			return 0.4f;
		}
		return 0.3f;
	}

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
		float contentFontSize = 12f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
		float logoWidth = 75f;
		float logoHeight = 75f;

		int instituteId = institute.getInstituteId();
		return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
				defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
			Institute institute, String reportType, float offsetX, float offsetY, PdfFont regularFont, PdfFont boldFont) throws IOException {

		generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		int singleContentColumn = 1;
		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(AFFILIATION_NUMBER)),
				cellLayoutSetup.copy().setFontSize(10f).setPdfFont(regularFont).setTextAlignment(TextAlignment.RIGHT));

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName())
						.setFontColor(Color.convertRgbToCmyk(darkPurpleDeviceRgb))),
						cellLayoutSetup.copy().setFontSize(20f));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1())
						.setFontColor(Color.convertRgbToCmyk(pinkDeviceRgb))),
				cellLayoutSetup.copy().setFontSize(12f));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine2())
						.setFontColor(Color.convertRgbToCmyk(pinkDeviceRgb))),
				cellLayoutSetup.copy().setFontSize(12f).setPdfFont(regularFont));
		
		String headerExamTitle = "PROGRESS REPORT ";
		headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()),
				cellLayoutSetup.copy().setFontSize(12f).setPdfFont(boldFont));
		examReportCardLayoutData.getDocument().add(table);

	}

	protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
			StudentLite studentLite, ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont,
											  int instituteId, StudentManager studentManager) throws IOException {

		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize();

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.39f, 0.02f, 0.39f, 0.20f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

		String fatherNameVal = StringUtils.isBlank(studentLite.getFathersName()) ? "" : "Mr. " + studentLite.getFathersName();
		Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName(), darkPurpleR, darkPurpleG, darkPurpleB, boldFont, boldFont);

		Paragraph fatherName = getKeyValueParagraph("Father Name : ", fatherNameVal, darkPurpleR, darkPurpleG, darkPurpleB, boldFont, boldFont);
		Paragraph dob = getKeyValueParagraph("DOB : ", studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
						: DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE), darkPurpleR, darkPurpleG, darkPurpleB, boldFont, boldFont);
		String motherNameVal = StringUtils.isBlank(studentLite.getMothersName()) ? "" : "Mrs. " + studentLite.getMothersName();
		Paragraph motherName = getKeyValueParagraph("Mother Name : ", motherNameVal, darkPurpleR, darkPurpleG, darkPurpleB, boldFont, boldFont);
		Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ", studentLite.getAdmissionNumber(), darkPurpleR, darkPurpleG, darkPurpleB, boldFont, boldFont);
		Paragraph classValue = getKeyValueParagraph("Class : ",
				studentLite.getStudentSessionData().getStandardNameWithSection(), darkPurpleR, darkPurpleG, darkPurpleB, boldFont, boldFont);

		byte[] studentImage = getStudentImage(instituteId, studentLite.getStudentId(), studentManager);
		if (studentImage != null) {
			generateImage(document, documentLayoutSetup, studentImage, 75f, 75f,
					documentLayoutSetup.getPageSize().getWidth()  - 100f,
					(examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.87f) - 110f);

		}
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(fatherName, thirdCellLayoutSetup), new CellData("", secondCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(dob, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(motherName, thirdCellLayoutSetup), new CellData("", secondCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(classValue, thirdCellLayoutSetup), new CellData("", secondCellLayoutSetup)));
		document.add(table);

	}

	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
				documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
	}

	protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, ExamReportData examReportData, String reportType, PdfFont regularFont, PdfFont boldFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
				.setTextAlignment(TextAlignment.LEFT).setPaddingTop(2f).setPaddingBottom(2f);

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		Paragraph rank = null;
		if (examReportData.getRank() != null && examReportData.getRank() > 0 && examReportData.getRank() <= 10) {
			rank = getKeyValueParagraph("Rank : ", String.valueOf(examReportData.getRank()), 1, 155, 248, boldFont, boldFont);
		}

		Paragraph result = getKeyValueParagraph("Result : ", "-", boldFont);
		if(examReportData.getExamResultStatus() != null){
			switch (examReportData.getExamResultStatus()){
				case PASS:
				case PASS_WITH_GRACE:
				case SUPPLEMENTARY:
					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
							2, 48, 32, boldFont, boldFont);
					break;
				case FAIL:
					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
							255, 0, 0, boldFont, boldFont);
					break;
			}
		}

		Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
				examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName(), darkPurpleR, darkPurpleG, darkPurpleB, boldFont, boldFont);
		Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
				examReportData.getTotalObtainedMarks() == null ? "-"
						: examReportData.getTotalObtainedMarks() * 10 % 10 == 0
								? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
								: String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d), darkPurpleR, darkPurpleG, darkPurpleB, boldFont, boldFont);
		Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
				: String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10), darkPurpleR, darkPurpleG, darkPurpleB, boldFont, boldFont);
		Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
				: Math.round(examReportData.getPercentage() * 100) / 100d + "%", darkPurpleR, darkPurpleG, darkPurpleB, boldFont, boldFont);

		if(rank != null) {
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(rank, cellLayoutSetup),
					new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));
		}

		if (reportType.equalsIgnoreCase("ANNUAL")) {
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
					new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(promotedClass, cellLayoutSetup)));
		}

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));


		System.out.println(examReportData.getTotalGrade());
		Paragraph grade = getKeyValueParagraph("Grade : ",
				examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(),
				darkPurpleR, darkPurpleG, darkPurpleB, boldFont, boldFont);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(grade, cellLayoutSetup)));

		document.add(table);

	}

	protected Paragraph generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
			ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize())
				.setTextAlignment(TextAlignment.LEFT);

		Paragraph remarks = getKeyValueParagraph("Remarks: ",
				StringUtils.isBlank(examReportData.getRemarks()) ? examReportData.getTotalGrade() == null ? "" :
						StringUtils.isEmpty(getRemarks(examReportData.getTotalGrade().getGradeName()))
								? "" : getRemarks(examReportData.getTotalGrade().getGradeName())
				: examReportData.getRemarks());

		
		return remarks;
		
	}

	private String getRemarks(String grade) {
		switch (grade) {

			case "A+":
				return GRADE_A_PLUS;
			case "A":
				return GRADE_A;
			case "B":
				return GRADE_B;
			case "C":
				return GRADE_C;
			case "D":
				return GRADE_D;
			default:
				return null;

		}
	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
										float contentFontSize, float defaultBorderWidth, int pageNumber,
										PdfFont regularFont, PdfFont boldFont) throws IOException {

		// PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		// canvas.moveTo(12, 360);
		// canvas.lineTo(583, 360);
		// canvas.setLineWidth(.5f);
		// canvas.closePathStroke();
		int singleContentColumn = 3;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(getParagraph("Parent's Signature"), signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
				new CellData(getParagraph("Class Teacher's Signature"), signatureCellLayoutSetup),
				new CellData(getParagraph("Principal's Signature"), signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));
		document.add(table);
		// table.setFixedPosition(30f, 85f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		// document.add(table);

		// generateImage(document, documentLayoutSetup,
		// 		ImageProvider.INSTANCE.getImage(ImageProvider._10180_PRINCIPAL_SIGNATURE_WITHOUT_TEXT),
		// 		50f, 50f,
		// 		documentLayoutSetup.getPageSize().getWidth() - 100f, 105f);

	}

	private void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup,ExamReportCardLayoutData examReportCardLayoutData, ExamReportData examReportData, float contentFontSize,
								  float defaultBorderWidth, PdfFont boldFont, PdfFont regularFont) throws IOException {
		
		// if(examReportData.getCourseTypeExamGrades() == null || CollectionUtils.isEmpty(examReportData.getCourseTypeExamGrades().get(CourseType.SCHOLASTIC))) {
		// 	return ;
		// }
		Map<String, String> MARKS_GRADE_MAP = new LinkedHashMap<>();
		// List<ExamGrade> grades = examReportData.getCourseTypeExamGrades().get(CourseType.SCHOLASTIC);
		// for (ExamGrade grade : grades) {
        //     MARKS_GRADE_MAP.put(grade.getRangeDisplayName(), grade.getGradeName());
        // }
		MARKS_GRADE_MAP.put("90-100", "A+");
		MARKS_GRADE_MAP.put("70-89", "A");
		MARKS_GRADE_MAP.put("30-49", "C");
		MARKS_GRADE_MAP.put("00-29", "D");
		MARKS_GRADE_MAP.put("50-69", "B");
		float[] columnWidths = new float[] { 0.5f, 0.05f, 0.45f };
		Table table = getPDFTable(documentLayoutSetup, columnWidths);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 2)
				.setTextAlignment(TextAlignment.CENTER);

		CellLayoutSetup remarkCellLayoutSetup = new CellLayoutSetup();
		remarkCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT);

		String backgroundColor = "#fdbd64";
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(getParagraph("Our Grading System"),
						signatureCellLayoutSetup.copy().setBorder(new SolidBorder(defaultBorderWidth)).setBorderBottom(null)
								.setBackgroundColor(backgroundColor)),
				new CellData(getParagraph(""), signatureCellLayoutSetup), new CellData(generateRemarksSection(examReportCardLayoutData, examReportData,regularFont, boldFont), remarkCellLayoutSetup)));
		document.add(table);

		columnWidths = new float[] { 0.125f, 0.125f, 0.125f, 0.125f, 0.5f };
		
		Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 2)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
		CellLayoutSetup markCellLayoutSetup = new CellLayoutSetup();
		markCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize).setTextAlignment(TextAlignment.CENTER);
		List<Map.Entry<String, String>> gradesList = new ArrayList<>(MARKS_GRADE_MAP.entrySet());
		for(int index = 0; index <= gradesList.size() / 2; index++) {
			List<CellData> row = new ArrayList<>();
			if(index == 0 || index %2 != 0) {
			row.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
			row.add(new CellData(gradesList.get(index).getValue(), marksCellLayoutSetup));
			}
			row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getKey(), marksCellLayoutSetup));
			row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getValue(), marksCellLayoutSetup));
			if(gradesList.size() % 2 != 0 && index == gradesList.size() / 2) { 
				row.add(new CellData("", marksCellLayoutSetup));
				row.add(new CellData("", marksCellLayoutSetup));
			}
			row.add(new CellData("", markCellLayoutSetup));
			addRow(headerTable, documentLayoutSetup, row);
		}
		// headerTable.setFixedPosition(25f, 314f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(headerTable);
	}

	protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			float logoWidth = 75f;
			float logoHeight = 75f;
			PdfFont boldFont = getRegularBoldFont();
			PdfFont regularFont = getRegularFont();
			int instituteId = institute.getInstituteId();
			ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, DEFAULT_FONT_SIZE,
					DEFAULT_BORDER_WIDTH, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType, GridConfigs.forOnlyObtainedTotalRow(),
						"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType));
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}

}
