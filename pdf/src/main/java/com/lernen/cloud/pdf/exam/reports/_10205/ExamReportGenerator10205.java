package com.lernen.cloud.pdf.exam.reports._10205;

import com.embrate.cloud.core.api.examination.utility.ExamGridRowAttribute;
import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamDimensionObtainedValues;
import com.lernen.cloud.core.api.examination.ExamGridAdditionalAttributeRow;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridHeaderConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridTotalMarksRowConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 */
public class ExamReportGenerator10205 extends ExamReportGenerator implements IExamReportCardGenerator {
    public ExamReportGenerator10205(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final Logger logger = LogManager.getLogger(ExamReportGenerator10205.class);
    public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
    public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
    public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
    public static final float SQUARE_BORDER_MARGIN = 12f;
    protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";
    protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";
    protected static final String SCHOLASTIC_EXAM_DESCRIPTION = "UT(A) : Unit Term I, UT(B) : Unit Term II, HY : Half Yearly, YL : Yearly, PDS : Personality Development Skills, MM : Max Marks, MO : Marks Obtained";
    protected static final String SCHOLASTIC_EXAM_DESCRIPTION_XI = "UT(A) : Unit Term I, UT(B) : Unit Term II, HY : Half Yearly, YL : Yearly, MM : Max Marks, MO : Marks Obtained";

    private static final String GRADE_A_PLUS = "Excellent. Keep it up.";
    private static final String GRADE_A = "Very Good, could do better.";
    private static final String GRADE_B = "Good, capable of better performance.";
    private static final String GRADE_C = "Fair, needs to put in more effort to perform better.";
    private static final String GRADE_D = "Satisfactory. More practice and sincere effort is required.";
    private static final String GRADE_E = "Unsatisfactory. Needs to work very hard to come up to required standard of the class.";
    
    private static final Map<UUID, String> PROMOTED_CLASS = new LinkedHashMap<>();
    public static final Integer RANK_LIMIT = 10;

    private static final Map<String, String> MARKS_GRADE_MAP = new LinkedHashMap<>();

    static {
        MARKS_GRADE_MAP.put("91 - 100", "A+");
        MARKS_GRADE_MAP.put("81 - 90", "A");
        MARKS_GRADE_MAP.put("71 - 80", "B+");
        MARKS_GRADE_MAP.put("61 - 70", "B");
        MARKS_GRADE_MAP.put("51 - 60", "C");
        MARKS_GRADE_MAP.put("33 - 50", "D");
        MARKS_GRADE_MAP.put("32 & Below", "E(Failed)");
        MARKS_GRADE_MAP.put("", "");
        
        PROMOTED_CLASS.put(UUID.fromString("21635e5b-d5a3-4489-9360-027581381ce6"), "X");
        PROMOTED_CLASS.put(UUID.fromString("e490824e-f2a7-11ef-89e2-0e732193dd21"), "X-CBSE");
        PROMOTED_CLASS.put(UUID.fromString("546cbda1-e1de-11ee-a3b6-124ce6a1ab45"), "XII");
        PROMOTED_CLASS.put(UUID.fromString("05fe172d-f2a8-11ef-89e2-0e732193dd21"), "XII-CBSE");
        PROMOTED_CLASS.put(UUID.fromString("a4db5959-f381-11ef-89e2-0e732193dd21"), "-");
        PROMOTED_CLASS.put(UUID.fromString("af435262-f381-11ef-89e2-0e732193dd21"), "-");

    }

    @Override
    public DocumentOutput generateReport(Institute institute, Student student, String reportType,
                                         ExamReportData examReportData, String documentName, StudentManager studentManager) {
        try {

            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

            generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                    1, regularFont, boldFont, "STUDENT");

            examReportCardLayoutData.getDocument().close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, student {}, reportType {} ",
                    institute.getInstituteId(), student.getStudentId(), reportType, e);
        }
        return null;
    }

    private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData,
                                           Institute institute, ExamReportData examReportData,
                                           StudentManager studentManager, String reportType,
                                           int pageNumber, PdfFont regularFont, PdfFont boldFont,
                                           String reportCardFor) throws IOException {
        List<UUID> standardIdXI = new ArrayList<>(Arrays.asList(UUID.fromString("546cbda1-e1de-11ee-a3b6-124ce6a1ab45"), UUID.fromString("49aa57d0-1e50-11ef-bcc8-124ce6a1ab45")));

        generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
                examReportData, regularFont, boldFont, pageNumber);

        PdfFont boldFontt = getRegularBoldFont();

        generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(), boldFontt, boldFontt,
                examReportCardLayoutData.getContentFontSize(), examReportCardLayoutData.getDefaultBorderWidth(), examReportData, null,
                "Subjects", getScholasticMarksGridSubjectWidth(reportType), null, "MM", "MO",
                "#000000", "Ab");

        GridConfigs gridConfigs = new GridConfigs(new GridHeaderConfigs(false), new GridTotalMarksRowConfigs(false, false,
                true, false, true, false, false,
                false, false, false, false, EMPTY_TEXT, EMPTY_TEXT, "Percentage (%)",
                EMPTY_TEXT, EMPTY_TEXT, EMPTY_TEXT, EMPTY_TEXT, "", EMPTY_TEXT));

        generateAdditionalAttributeRowsInGrid(examReportCardLayoutData, examReportData, gridConfigs, getScholasticMarksGridSubjectWidth(reportType), reportType);

        generateScholasticExamDescription(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
                examReportData, regularFont, boldFont, standardIdXI.contains(examReportData.getStudentLite().getStudentSessionData().getStandardId()) ? SCHOLASTIC_EXAM_DESCRIPTION_XI: SCHOLASTIC_EXAM_DESCRIPTION);

        generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), boldFontt, boldFontt,
                examReportCardLayoutData.getContentFontSize(),
                examReportCardLayoutData.getDefaultBorderWidth(), examReportData, null, "Co-Scholastic Areas",
                getCoScholasticMarksGridSubjectWidth(reportType), "MM", "MO", "Ab");

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

        generateResultSummary(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
                examReportData, reportType, regularFont, boldFont, reportCardFor);

//        generateRemarksSection(examReportCardLayoutData, examReportData, regularFont, boldFont);

        generateSignatureBox(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
                examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, regularFont, boldFont);
    }

    private void generateAdditionalAttributeRowsInGrid(ExamReportCardLayoutData examReportCardLayoutData, ExamReportData examReportData, GridConfigs gridConfigs, float subjectColumnWidth, String reportType) throws IOException {

        if (gridConfigs != null && gridConfigs.getGridTotalMarksRowConfigs() != null) {
            float remainingColumnWidth = 1 - subjectColumnWidth;
            float restColumn = remainingColumnWidth / 14;
            Table headerTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), new float[]{subjectColumnWidth, restColumn * 6, restColumn * 6, restColumn * 2});
            if(reportType.equalsIgnoreCase("HALF_YEARLY")) {
                headerTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), new float[]{subjectColumnWidth, 0.83f});
            }
            GridTotalMarksRowConfigs gridTotalMarksRowConfigs = gridConfigs.getGridTotalMarksRowConfigs();
            boolean addMaxRowInTotalRow = gridTotalMarksRowConfigs.isAddMaxRowInTotalRow();
            boolean addTotalObtainedMarksRow = gridTotalMarksRowConfigs.isAddTotalRow();
            boolean addPercentRowInTotalRow =  gridTotalMarksRowConfigs.isAddPercentRowInTotalRow();
            CourseType courseType = CourseType.SCHOLASTIC;
            ExamReportMarksGrid examReportMarksGrid = examReportData.getCourseTypeExamReportMarksGrid().get(courseType);
            ExamReportStructureMetaData examReportStructureMetaData = examReportData.getExamReportStructure().getExamReportStructureMetaData();
            CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
            marksCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(examReportCardLayoutData.getContentFontSize())
                    .setBorder(new SolidBorder(examReportCardLayoutData.getDefaultBorderWidth())).setTextAlignment(TextAlignment.CENTER);
            boolean showGrade = examReportData.getStandardMetaData().isScholasticGradingEnabled();
            String totalRowHexColorCode = "#000000";
            String emptyColumnValue = "Ab";
            ExamReportCourseStructure examReportCourseStructure = examReportData.getExamReportStructure().getExamReportCourseStructure() == null ? null :
                    CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()) ? null :
                            examReportData.getExamReportStructure().getExamReportCourseStructure().get(courseType);

            CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(getRegularBoldFont())
                    .setTextAlignment(TextAlignment.LEFT);

            CourseTotalMarksRows courseTotalMarksRows = generateCourseTotalMarksInGrid(examReportMarksGrid, examReportStructureMetaData, courseType,
                    marksCellLayoutSetup, courseCellLayoutSetup, showGrade, totalRowHexColorCode, emptyColumnValue,
                    null, examReportCourseStructure, gridConfigs, reportType);
            if(addPercentRowInTotalRow){
                addRow(headerTable, examReportCardLayoutData.getDocumentLayoutSetup(), courseTotalMarksRows.getPercentMarksRow());
            }

            examReportCardLayoutData.getDocument().add(headerTable);
        }
    }

    public CourseTotalMarksRows generateCourseTotalMarksInGrid(ExamReportMarksGrid examReportMarksGrid,
                                                               ExamReportStructureMetaData examReportStructureMetaData, CourseType courseType,
                                                               CellLayoutSetup totalMarksRowCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup,
                                                               boolean showGrade, String totalRowValuesHexColor, String emptyColumnValue,
                                                               List<Integer> rgbCourseNameColorCode, ExamReportCourseStructure examReportCourseStructure,
                                                               GridConfigs gridConfigs, String reportType) throws IOException {
        List<CellData> maxMarksRow = new ArrayList<>();
        List<CellData> obtainedMarksRow = new ArrayList<>();
        List<CellData> percentMarksRow = new ArrayList<>();
        GridTotalMarksRowConfigs gridTotalMarksRowConfigs = gridConfigs.getGridTotalMarksRowConfigs();

        String gridCoursesBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesBackgroundColor();
        String gridCoursesTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesTextColor();

        if(!StringUtils.isBlank(gridCoursesBackgroundColor)) {
            courseCellLayoutSetup.setBackgroundColor(gridCoursesBackgroundColor);
        }

        if(!StringUtils.isBlank(gridCoursesTextColor)) {
            rgbCourseNameColorCode = EColorUtils.hex2Rgb(gridCoursesTextColor);
        }

        String totalRowBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getTotalRowBackgroundColor();
        String totalRowTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getTotalRowTextColor();

        if(!StringUtils.isBlank(totalRowBackgroundColor)) {
            totalMarksRowCellLayoutSetup.setBackgroundColor(totalRowBackgroundColor);
        }

        if(!StringUtils.isBlank(totalRowTextColor)) {
            totalRowValuesHexColor = totalRowTextColor;
        }

        if(!CollectionUtils.isEmpty(rgbCourseNameColorCode)) {
//            maxMarksRow.add(new CellData(getParagraph(gridTotalMarksRowConfigs.getMaxRowTitle(), rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2)), courseCellLayoutSetup));
//            obtainedMarksRow.add(new CellData(getParagraph(gridTotalMarksRowConfigs.getObtainedRowTitle(), rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2)), courseCellLayoutSetup));
            percentMarksRow.add(new CellData(getParagraph(gridTotalMarksRowConfigs.getPercentRowTitle(), rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2)), courseCellLayoutSetup));
        } else {
//            maxMarksRow.add(new CellData(gridTotalMarksRowConfigs.getMaxRowTitle(), courseCellLayoutSetup));
//            obtainedMarksRow.add(new CellData(gridTotalMarksRowConfigs.getObtainedRowTitle(), courseCellLayoutSetup));
            percentMarksRow.add(new CellData(gridTotalMarksRowConfigs.getPercentRowTitle(), courseCellLayoutSetup));
        }

        totalMarksRowCellLayoutSetup.setPdfFont(getRegularBoldFont());
        for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportMarksGrid
                .getExamReportTotalMarksColumns()) {
            if(examReportCourseMarksColumn.isHide()) {
                continue;
            }

            if((!examReportCourseMarksColumn.getId().equalsIgnoreCase("t1_Total") &&
                    !examReportCourseMarksColumn.getId().equalsIgnoreCase("t2_Total") &&
                    !examReportCourseMarksColumn.getId().equalsIgnoreCase("t1_t2_Total"))) {
                continue;
            }

            int dimensionMultiplier = getDimensionMultiplier(examReportStructureMetaData,
                    examReportCourseMarksColumn.getExamReportGridColumnType(), courseType, showGrade);

            for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportCourseMarksColumn
                    .getExamDimensionObtainedValuesList()) {

                if (dimensionMultiplier == 2) {

                    String marks = examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE
                            : String.valueOf(Math.round(examDimensionObtainedValues.getMaxMarks()));

                    List<Integer> rgb = EColorUtils.hex2Rgb(totalRowValuesHexColor);
                    if (CollectionUtils.isEmpty(rgb)) {
//                        maxMarksRow.add(new CellData(marks, totalMarksRowCellLayoutSetup));
//                        obtainedMarksRow.add(new CellData(marks, totalMarksRowCellLayoutSetup));
//                        percentMarksRow.add(new CellData(!gridTotalMarksRowConfigs.isAddTotalPercentColumn() ? EMPTY_TEXT : MAX_PERCENTAGE, totalMarksRowCellLayoutSetup));
                    } else {
//                        maxMarksRow.add(new CellData(getParagraph(marks, rgb.get(0), rgb.get(1), rgb.get(2)),
//                                totalMarksRowCellLayoutSetup));
//                        obtainedMarksRow.add(new CellData(getParagraph(marks, rgb.get(0), rgb.get(1), rgb.get(2)),
//                                totalMarksRowCellLayoutSetup));
//                        percentMarksRow.add(new CellData(getParagraph(!gridTotalMarksRowConfigs.isAddTotalPercentColumn() ? EMPTY_TEXT : MAX_PERCENTAGE, rgb.get(0), rgb.get(1), rgb.get(2)),
//                                totalMarksRowCellLayoutSetup));
                    }
                }

                String maxValue = "";
                String obtainedValue = "";
                String percentValue = "";
                if (showGrade
                        || examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.GRADE
                        || examReportCourseMarksColumn
                        .getExamReportGridColumnType() == ExamReportGridColumnType.EXAM_GRADE
                        || examReportCourseMarksColumn
                        .getExamReportGridColumnType() == ExamReportGridColumnType.SHOW_GRADE_WITH_MARKS_GRID) {
                    String finalGrade = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedGrade() == null
                                    ? null : examDimensionObtainedValues.getObtainedGrade().getGradeName(),
                            examDimensionObtainedValues.getAttendanceStatus());
                    obtainedValue =  finalGrade == null ? emptyColumnValue : finalGrade;
                } else if (examReportCourseMarksColumn
                        .getExamReportGridColumnType() == ExamReportGridColumnType.PERCENT) {
                    maxValue = !gridTotalMarksRowConfigs.isAddTotalPercentColumn() ? EMPTY_TEXT : MAX_PERCENTAGE; // As percent max value should be 100?
                    String finalPercentValue = ExamDimensionObtainedValues.getFinalPercentageValueDisplay(examDimensionObtainedValues.getObtainedMarksFraction() == null
                                    ? null : Math.round(examDimensionObtainedValues.getObtainedMarksFraction() * 100) / 100d,
                            examDimensionObtainedValues.getAttendanceStatus(), gridConfigs.getGridTotalMarksRowConfigs().isShowPercentSymbolColumn(), false);
                    obtainedValue = finalPercentValue == null ? emptyColumnValue : finalPercentValue;
                    percentValue = obtainedValue;
                } else {
//					value = examDimensionObtainedValues.getObtainedMarks() == null ? emptyColumnValue
//							: String.valueOf(Math.round(examDimensionObtainedValues.getObtainedMarks()));

                    /**
                     * By default, rounding off marks, only removing round if specified specially
                     */
                    if(examReportCourseMarksColumn.getRoundOff() != null && !examReportCourseMarksColumn.getRoundOff()) {
                        maxValue = examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE
                                : String.valueOf(NumberUtils.formatDouble(examDimensionObtainedValues.getMaxMarks(), 2));

                        String finalObtainedValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedMarks() == null
                                        ? null : String.valueOf(NumberUtils.formatDouble(examDimensionObtainedValues.getObtainedMarks(), 2)),
                                examDimensionObtainedValues.getAttendanceStatus());

                        obtainedValue = finalObtainedValue == null ? emptyColumnValue : finalObtainedValue;

                        String finalPercentValue = ExamDimensionObtainedValues.getFinalPercentageValueDisplay(examDimensionObtainedValues.getObtainedMarksFraction() == null
                                        ? null : NumberUtils.formatDouble((examDimensionObtainedValues.getObtainedMarksFraction() * 100d), 2),
                                examDimensionObtainedValues.getAttendanceStatus(), gridConfigs.getGridTotalMarksRowConfigs().isShowPercentSymbolColumn(), false);
                        percentValue = finalPercentValue == null ? emptyColumnValue : finalPercentValue;

                    } else {
                        maxValue = examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE
                                : String.valueOf(Math.round(examDimensionObtainedValues.getMaxMarks()));
                        String finalObtainedValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedMarks() == null
                                        ? null : String.valueOf(Math.round(examDimensionObtainedValues.getObtainedMarks())),
                                examDimensionObtainedValues.getAttendanceStatus());

                        obtainedValue = finalObtainedValue == null ? examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE :
                                emptyColumnValue : finalObtainedValue;

                        String finalPercentValue = ExamDimensionObtainedValues.getFinalPercentageValueDisplay(examDimensionObtainedValues.getObtainedMarksFraction() == null
                                        ? null : Math.round(examDimensionObtainedValues.getObtainedMarksFraction() * 100) / 100d,
                                examDimensionObtainedValues.getAttendanceStatus(), gridConfigs.getGridTotalMarksRowConfigs().isShowPercentSymbolColumn(), false);
                        percentValue = finalPercentValue == null ? emptyColumnValue : finalPercentValue;

                    }
                }

                if (maxValue == null) {
                    maxValue = emptyColumnValue;
                }
                if (obtainedValue == null) {
                    obtainedValue = emptyColumnValue;
                }
                if (percentValue == null) {
                    percentValue = emptyColumnValue;
                }
                if(reportType.equalsIgnoreCase("HALF_YEARLY")) {
                    totalMarksRowCellLayoutSetup.setTextAlignment(TextAlignment.RIGHT).setPaddingRight(110f);
                }

                List<Integer> rgb = EColorUtils.hex2Rgb(totalRowValuesHexColor);
                if (CollectionUtils.isEmpty(rgb)) {
//                    maxMarksRow.add(new CellData(maxValue, totalMarksRowCellLayoutSetup));
//                    obtainedMarksRow.add(new CellData(obtainedValue, totalMarksRowCellLayoutSetup));
                    percentMarksRow.add(new CellData(percentValue, totalMarksRowCellLayoutSetup));
                } else {
//                    maxMarksRow.add(new CellData(getParagraph(maxValue, rgb.get(0), rgb.get(1), rgb.get(2)),
//                            totalMarksRowCellLayoutSetup));
//                    obtainedMarksRow.add(new CellData(getParagraph(obtainedValue, rgb.get(0), rgb.get(1), rgb.get(2)),
//                            totalMarksRowCellLayoutSetup));
                    percentMarksRow.add(new CellData(getParagraph(percentValue, rgb.get(0), rgb.get(1), rgb.get(2)),
                            totalMarksRowCellLayoutSetup));
                }
            }
        }

        return new CourseTotalMarksRows(maxMarksRow, obtainedMarksRow, percentMarksRow, null);
    }

    protected float getScholasticMarksGridSubjectWidth(String reportType) {
        return 0.17f;
    }

    protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
        if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
            return 0.5f;
        }
        if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
            return 0.4f;
        }
        return 0.3f;
    }

    protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
            throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
        float contentFontSize = 11f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        float logoWidth = 75f;
        float logoHeight = 75f;

        int instituteId = institute.getInstituteId();
        return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
                defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
    }

    protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
                                          StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
                                          PdfFont regularFont, PdfFont boldFont, int pageNumber) throws IOException {

        generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
                examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.88f, regularFont, boldFont);
        generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, regularFont, boldFont);
        generateBorderLayout(examReportCardLayoutData, pageNumber);
    }

    @Override
    public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
        return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
    }

    protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
                                  Institute institute, String reportType, float offsetX, float offsetY, PdfFont regularFont, PdfFont boldFont) throws IOException {

        generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

        int singleContentColumn = 1;
        Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.CENTER);

        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName())
                        .setFontColor(Color.convertRgbToCmyk(new DeviceRgb(0, 0, 0))).setFontSize(examReportCardLayoutData.getContentFontSize() + 4).setMultipliedLeading(1.2f)),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 4));
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1())
                        .setFontColor(Color.convertRgbToCmyk(new DeviceRgb(0, 0, 0)))),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 1));
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine2())
                        .setFontColor(Color.convertRgbToCmyk(new DeviceRgb(0, 0, 0)))),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 1));

        examReportCardLayoutData.getDocument().add(table);

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

        table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
        String headerExamTitle = "PROGRESS REPORT ";
        headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()),
                cellLayoutSetup.copy().setPdfFont(boldFont));
        examReportCardLayoutData.getDocument().add(table);
        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
    }

    protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
                                              StudentLite studentLite, ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont) throws IOException {

        Document document = examReportCardLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        float contentFontSize = examReportCardLayoutData.getContentFontSize();

        Table table = getPDFTable(documentLayoutSetup, new float[]{0.4f, 0.2f, 0.4f});

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

        CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
        CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
        CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

        Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName(), 0, 0, 0, boldFont, boldFont);
        Paragraph fatherName = getKeyValueParagraph("Father's Name : ", StringUtils.isBlank(studentLite.getFathersName()) ? EMPTY_TEXT : "Mr. " + studentLite.getFathersName(), 0, 0, 0, boldFont, boldFont);
//		Paragraph dob = getKeyValueParagraph("DOB : ",
//				studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
//						: DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE), 57, 65, 158, boldFont, boldFont);
//
        Paragraph admissionNumber = getKeyValueParagraph("Admission Number : ", studentLite.getAdmissionNumber(), 0, 0, 0, boldFont, boldFont);
        Paragraph classValue = getKeyValueParagraph("Class : ",
                studentLite.getStudentSessionData().getStandardNameWithSection(), 0, 0, 0, boldFont, boldFont);
        Paragraph rollNumber = getKeyValueParagraph("Roll Number : ", studentLite.getStudentSessionData().getRollNumber(), 0, 0, 0, boldFont,boldFont);



        Paragraph dob = getKeyValueParagraph("Date Of Birth : ", studentLite.getDateOfBirth() == null ? null :
                DateUtils.getFormattedDate(studentLite.getDateOfBirth()), 0, 0, 0, boldFont, boldFont);

        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(fatherName, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(classValue, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(rollNumber, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
                        new CellData(dob, thirdCellLayoutSetup)));

        document.add(table);

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

    }

    protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
        canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
                documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
                documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
        canvas.stroke();
        canvas.setLineWidth(1f);
        canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
                documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
                documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
        canvas.stroke();
        canvas.setLineWidth(1f);
    }

    protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
                                                     float contentFontSize, ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont, String description) throws IOException {
        Text descTitle = new Text("Description: " + description).setFont(regularFont).setFontSize(contentFontSize - 4);
        Paragraph desc = new Paragraph();
        desc.add(descTitle);
        document.add(desc);
        addBlankLine(document, false, 1);
    }

    protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
                                         float contentFontSize, ExamReportData examReportData,
                                         String reportType, PdfFont regularFont, PdfFont boldFont,
                                         String reportCardFor) throws IOException {

        addBlankLine(document, false, 1);
        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setPaddingTop(2f).setPaddingBottom(2f);

        Table table = getPDFTable(documentLayoutSetup, new float[]{0.5f, 0.05f, 0.45f});

        String attendanceStr = "";
        if (examReportData.getTotalAttendedDays() != null && (examReportData.getTotalWorkingDays() != null && examReportData.getTotalWorkingDays() != 0)) {
            attendanceStr = examReportData.getTotalAttendedDays() + "/" + examReportData.getTotalWorkingDays();
        }

        Paragraph noOfPresent = getKeyValueParagraph("Attendance : ", attendanceStr, 0, 0, 0, boldFont, boldFont);

        Paragraph rank = null;
        if (examReportData.getRank() != null && examReportData.getRank() <= RANK_LIMIT) {
            rank = getKeyValueParagraph("Overall Rank : ", String.valueOf(examReportData.getRank()),
                    0, 0, 0, boldFont, boldFont);
        }

        Paragraph result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus() == null ? "-" :
                            examReportData.getExamResultStatus().getDisplayName(),
                    0, 0, 0, boldFont, boldFont);

        Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
                examReportData.getPromotedTo() == null ? "-" : PROMOTED_CLASS.get(examReportData.getStandardMetaData().getStandardId()) != null ? PROMOTED_CLASS.get(examReportData.getStandardMetaData().getStandardId()) : examReportData.getPromotedTo().getStandardName(), 0, 0, 0, boldFont, boldFont);
        Paragraph obtainedMarks = getKeyValueParagraph("Overall Marks Obtained : ",
                examReportData.getTotalObtainedMarks() == null ? "-"
                        : examReportData.getTotalObtainedMarks() * 10 % 10 == 0
                        ? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
                        : String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d), 0, 0, 0, boldFont, boldFont);
        Paragraph totalMarks = getKeyValueParagraph("Overall Maximum Marks : ", examReportData.getTotalMaxMarks() == null ? "-"
                : String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10), 0, 0, 0, boldFont, boldFont);
        Paragraph percentage = getKeyValueParagraph("Overall Percentage : ", examReportData.getPercentage() == null ? "-"
                : String.valueOf(Math.round(examReportData.getPercentage() * 100) / 100d) + "%", 0, 0, 0, boldFont,boldFont);

        Paragraph grade = getKeyValueParagraph("Grade : ",
                examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(), 0, 0, 0, boldFont, boldFont);

        Paragraph remarks = getKeyValueParagraph("Remarks: ", StringUtils.isBlank(examReportData.getRemarks()) ? getRemarks(examReportData.getPercentage()) : examReportData.getRemarks(),
                boldFont, boldFont);

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(obtainedMarks, cellLayoutSetup)));

        if(reportType.equalsIgnoreCase("HALF_YEARLY")) {
            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(noOfPresent, cellLayoutSetup),
                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));

            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(remarks, cellLayoutSetup),
                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(percentage, cellLayoutSetup)));

            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(EMPTY_TEXT, cellLayoutSetup),
                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(rank == null ? getParagraph(EMPTY_TEXT) : rank, cellLayoutSetup)));
        } else {
            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(promotedClass, cellLayoutSetup),
                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));

            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(noOfPresent, cellLayoutSetup),
                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(percentage, cellLayoutSetup)));

            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(remarks, cellLayoutSetup),
                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(rank == null ? getParagraph(EMPTY_TEXT) : rank, cellLayoutSetup)));
        }

//        if(rank != null && reportType.equalsIgnoreCase("ANNUAL")){
//            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(rank, cellLayoutSetup),
//                    new CellData(EMPTY_TEXT, cellLayoutSetup),new CellData(promotedClass, cellLayoutSetup)));
//        } else if(rank != null){
//            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(rank, cellLayoutSetup),
//                    new CellData(EMPTY_TEXT, cellLayoutSetup),new CellData(EMPTY_TEXT, cellLayoutSetup)));
//        } else if (reportType.equalsIgnoreCase("ANNUAL")) {
//            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(promotedClass, cellLayoutSetup),
//                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));
//        }
        document.add(table);

    }

//    protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
//                                          ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont) throws IOException {
//
//        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
//        cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize())
//                .setTextAlignment(TextAlignment.LEFT);
//
//        Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 1);
//        Paragraph remarks = getKeyValueParagraph("Remarks: ", StringUtils.isBlank(examReportData.getRemarks()) ? getRemarks(examReportData.getPercentage()) : examReportData.getRemarks(),
//                boldFont, boldFont);
//
//        addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(remarks),
//                cellLayoutSetup);
//        examReportCardLayoutData.getDocument().add(remarksTable);
//    }

    private String getRemarks(Double percentage) {
        if(percentage == null) {
            return  EMPTY_TEXT;
        }
        if (95 <= percentage) {
            return GRADE_A_PLUS;
        } else if (85 <= percentage && percentage < 95) {
            return GRADE_A;
        } else if (70 <= percentage && percentage < 85) {
            return GRADE_B;
        } else if (50 <= percentage && percentage < 70) {
            return GRADE_C;
        } else if (33 <= percentage && percentage < 50) {
            return GRADE_D;
        } else {
            return GRADE_E;
        }
    }

    protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
                                        float contentFontSize, float defaultBorderWidth, int pageNumber, PdfFont regularFont, PdfFont boldFont) throws IOException {
        PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
        canvas.moveTo(12, 100);
        canvas.lineTo(583, 100);
        canvas.setLineWidth(.5f);
        canvas.closePathStroke();
        int singleContentColumn = 3;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.CENTER);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Class Teacher"), getParagraph(EMPTY_TEXT),
                getParagraph("Principal")), signatureCellLayoutSetup);
        table.setFixedPosition(30f, 100f, documentLayoutSetup.getPageSize().getWidth() - 100f);
        document.add(table);
        generateGradeBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth, regularFont, boldFont);
    }

    private void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                  float defaultBorderWidth, PdfFont regularFont, PdfFont boldFont) throws IOException {

        Table table = getPDFTable(documentLayoutSetup, 1);
        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 2)
                .setTextAlignment(TextAlignment.CENTER);
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("PERCENTAGE MARKS RANGE")), signatureCellLayoutSetup);
        table.setFixedPosition(10f, 80f, documentLayoutSetup.getPageSize().getWidth());
        document.add(table);

        float[] columnWidths = new float[]{0.24f, 0.24f, 0.24f, 0.24f};
        Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
        CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
        marksCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 4)
                .setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
        List<Map.Entry<String, String>> gradesList = new ArrayList<>(MARKS_GRADE_MAP.entrySet());
        for (int index = 0; index < gradesList.size() / 2; index++) {
            List<CellData> row = new ArrayList<>();
            row.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
            row.add(new CellData(gradesList.get(index).getValue(), marksCellLayoutSetup));
            row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getKey(), marksCellLayoutSetup));
            row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getValue(), marksCellLayoutSetup));
            addRow(headerTable, documentLayoutSetup, row);
        }
        headerTable.setFixedPosition(35f, 15f, documentLayoutSetup.getPageSize().getWidth() - 100f);
        document.add(headerTable);
    }

    protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
        Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
        for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
                .get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
            if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
                nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
            }
        }
        return nonAdditionalSubjects;
    }

    protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
        Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

            @Override
            public int compare(ExamReportData e1, ExamReportData e2) {
                StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

                StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

                if (section1 != null && section2 != null) {
                    int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
                    if (sectionCompare != 0) {
                        return sectionCompare;
                    }
                }

                return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
            }
        });
    }

    @Override
    public DocumentOutput generateClassReport(Institute institute, String reportType,
                                              List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

            sortClassExamReports(examReportDataList);

            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();

            int pageNumber = 1;
            for (ExamReportData examReportData : examReportDataList) {
                generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                        pageNumber, regularFont, boldFont, "CLASS");

                if (pageNumber != examReportDataList.size()) {
                    examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;
            }

            examReportCardLayoutData.getDocument().close();
            return documentOutput;

        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
                    reportType, e);
        }
        return null;
    }

    @Override
    public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
                                                    String documentName, StudentManager studentManager, int studentPerPage) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
            Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

            float logoWidth = 75f;
            float logoHeight = 75f;
            PdfFont boldFont = getRegularBoldFont();
            PdfFont regularFont = getRegularFont();
            int instituteId = institute.getInstituteId();
            ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, DEFAULT_FONT_SIZE,
                    DEFAULT_BORDER_WIDTH, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));

            CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
            marksCellLayoutSetup.setPdfFont(boldFont).setFontSize(DEFAULT_FONT_SIZE)
                    .setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

            CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
                    .setTextAlignment(TextAlignment.LEFT);

            String instituteName = institute.getInstituteName().toUpperCase();
            String sessionName = "";
            if (!CollectionUtils.isEmpty(examReportDataList)) {
                sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
            }
            generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
                    sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

            sortClassExamReports(examReportDataList);
            int pageNumber = 1;
            for (ExamReportData examReportData : examReportDataList) {
                generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
                generateScholasticMarksGrid(document,
                        documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
                        "Subjects", getScholasticMarksGridSubjectWidth(reportType),
                        null, "#000000");
                generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
                if (pageNumber % studentPerPage == 0) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                    generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
                            marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
                } else {
                    addBlankLine(document, false, 1);
                }
                pageNumber++;
            }
            document.close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
                    reportType, e);
        }
        return null;
    }

}
