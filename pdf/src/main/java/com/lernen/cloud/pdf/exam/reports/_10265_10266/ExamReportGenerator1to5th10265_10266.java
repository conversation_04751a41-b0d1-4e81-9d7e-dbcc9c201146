package com.lernen.cloud.pdf.exam.reports._10265_10266;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.StudentPersonalityTraitsResponse;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.StringHelper;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.*;

public class ExamReportGenerator1to5th10265_10266 extends ExamReportGenerator implements IExamReportCardGenerator {
    
    public ExamReportGenerator1to5th10265_10266(AssetProvider assetProvider){
        super(assetProvider);
    }
    private static final Logger logger = LogManager.getLogger(ExamReportGenerator1to5th10265_10266.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;

	protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";
	protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
										 ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			PdfFont regularFont = examReportCardLayoutData.getRegularFont();
			PdfFont boldFont = examReportCardLayoutData.getBoldFont();

			generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
					1, boldFont, regularFont);

			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData,
										   Institute institute, ExamReportData examReportData,
										   StudentManager studentManager, String reportType,
										   int pageNumber, PdfFont boldFont, PdfFont regularFont
										   ) throws IOException {

		generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
				examReportData, pageNumber, boldFont, regularFont);


		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				regularFont, boldFont, examReportCardLayoutData.getContentFontSize() - 1,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
				"Scholastic Area", getScholasticMarksGridSubjectWidth(reportType),
				nonAdditionalSubjects, "MM", "MO", "#ff0000",
				"-");

		if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
			addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
			generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
					examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
					"Co-Scholastic Area: Term-1 (on a 3 point (A-C) Grading Scale)", getCoScholasticMarksGridSubjectWidth(reportType), "-");
		}
        addBlankLine(examReportCardLayoutData, false, 1);
        generatePersonalityTraits(examReportCardLayoutData, examReportData, boldFont, regularFont);

		addBlankLine(examReportCardLayoutData, false, 1);
		generateRemarksSection(examReportCardLayoutData, examReportData, boldFont, regularFont);
        
        addBlankLine(examReportCardLayoutData, false, 1);
        generateAttendanceSummary(examReportCardLayoutData, examReportData, boldFont, regularFont);

		addBlankLine(examReportCardLayoutData, false, 2);
		generateSignatureBox(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, boldFont, regularFont, examReportData.getCourseTypeExamGrades());

		generateBorderLayout(examReportCardLayoutData, pageNumber);
	}

	protected float getScholasticMarksGridSubjectWidth(String reportType) {
		return 0.2f;
	}

	protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.7f;
		}
		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			return 0.4f;
		}
		return 0.3f;
	}

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
		float contentFontSize = 12f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
		float logoWidth = 70f;
		float logoHeight = 70f;

		int instituteId = institute.getInstituteId();
		return new ExamReportCardLayoutData(document, documentLayoutSetup, getCambriaFont(), getCambriaBoldFont(), contentFontSize,
				defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
	}

	protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
										  StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
										  int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.89f,
				boldFont, regularFont);
        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, boldFont, regularFont);
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
								  Institute institute, String reportType, float offsetX, float offsetY,
								  PdfFont boldFont, PdfFont regularFont) throws IOException {

		generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
        generateDynamicImageProvider(examReportCardLayoutData, examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 130f, examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.885f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_SECONDARY_LOGO);
		
        int singleContentColumn = 1;
		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize()+2f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase()).setPaddingLeft(20f).setMarginBottom(-15f)
						.setFontColor(Color.convertRgbToCmyk(EColorUtils.lightBlackDeviceRgb))),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()+10f).setPdfFont(getPoppinsBoldFont()));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()-2f).setPdfFont(regularFont));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()-2f).setPdfFont(regularFont));

		examReportCardLayoutData.getDocument().add(table);
		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
		String headerExamTitle = reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE) ? "FIRST TERM EXAMINATION REPORT CARD"
				: "REPORT CARD FOR ACADEMIC SESSION";
		headerExamTitle += " (" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setFontSize(examReportCardLayoutData.getContentFontSize() + 1)
						.setFontColor(new DeviceRgb(EColorUtils.lightBlackR, EColorUtils.lightBlackG, EColorUtils.lightBlackB))),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()-1f).setPdfFont(boldFont));
		examReportCardLayoutData.getDocument().add(table);
	}

	protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
											  StudentLite studentLite, ExamReportData examReportData,
											  PdfFont boldFont, PdfFont regularFont) throws IOException {

		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize() - 1;

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.13f, 0.47f}).setBorder(new SolidBorder(examReportCardLayoutData.getDefaultBorderWidth()));

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize).setPaddingLeft(5f);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

        Paragraph studentName = getKeyValueParagraph("STUDENT NAME : ", 
				StringUtils.isBlank(studentLite.getName()) ? EMPTY_TEXT:studentLite.getName().toUpperCase(),
				EColorUtils.lightBlckColorList, EColorUtils.lightBlckColorList, boldFont, regularFont);
		Paragraph fatherName = getKeyValueParagraph("FATHER'S NAME : ", 
				StringUtils.isBlank(studentLite.getFathersName()) ? EMPTY_TEXT:studentLite.getFathersName().toUpperCase(),
				EColorUtils.lightBlckColorList, EColorUtils.lightBlckColorList, boldFont,regularFont);
		Paragraph dob = getKeyValueParagraph("DATE OF BIRTH : ",
				studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? EMPTY_TEXT
						: DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE),
				EColorUtils.lightBlckColorList, EColorUtils.lightBlckColorList, boldFont, regularFont);
		Paragraph motherName = getKeyValueParagraph("MOTHER'S NAME : ", 
				StringUtils.isBlank(studentLite.getMothersName()) ? EMPTY_TEXT : studentLite.getMothersName().toUpperCase(),
				EColorUtils.lightBlckColorList, EColorUtils.lightBlckColorList, boldFont, regularFont);
		Paragraph admissionNumber = getKeyValueParagraph("ADMISSION NO. : ", studentLite.getAdmissionNumber().toUpperCase(),
				EColorUtils.lightBlckColorList, EColorUtils.lightBlckColorList, boldFont, regularFont);
		Paragraph classValue = getKeyValueParagraph("CLASS - SECTION : ",
				studentLite.getStudentSessionData().getStandardNameWithSection().toUpperCase(),
				EColorUtils.lightBlckColorList, EColorUtils.lightBlckColorList, boldFont, regularFont);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(fatherName, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(motherName, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(dob, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(classValue, thirdCellLayoutSetup)));
		document.add(table);

		addBlankLine(examReportCardLayoutData.getDocument(), false, 2);
	}

	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
		Color color = WebColors.getRGBColor("#212121");
		canvas.setColor(color, false);
		canvas.setLineWidth(3f);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
				documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
	}
    protected void generatePersonalityTraits(ExamReportCardLayoutData examReportCardLayoutData, ExamReportData examReportData,
                                         PdfFont boldFont, PdfFont regularFont) {
    
        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont)
                    .setFontSize(examReportCardLayoutData.getContentFontSize() - 2)
                    .setTextAlignment(TextAlignment.LEFT)
                    .setBorder(new SolidBorder(examReportCardLayoutData.getDefaultBorderWidth()));
        
        Map<String, String> personalityTraitKeyValueMap = getPersonalityTraitKeyValueMap(examReportData.getStudentPersonalityTraitsResponseList());
        List<Map.Entry<String, String>> gradesList = new ArrayList<>(personalityTraitKeyValueMap.entrySet());

        Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), new float[] { 0.7f, 0.3f });

        Paragraph disciplineHeader = getParagraph("Discipline: Term-1 [on a 3-point grading scale]", EColorUtils.lightBlckColorList, boldFont);
        Paragraph gradeHeader = getParagraph("Grade", EColorUtils.lightBlckColorList, boldFont);

        Cell disciplineHeaderCell = new Cell().add(disciplineHeader).setTextAlignment(TextAlignment.CENTER);
        Cell gradeHeaderCell = new Cell().add(gradeHeader).setTextAlignment(TextAlignment.CENTER);
        
        table.addCell(disciplineHeaderCell);
        table.addCell(gradeHeaderCell);

        for (Map.Entry<String, String> entry : gradesList) {
            String key = entry.getKey();
            String value = entry.getValue();

            Paragraph disciplineParagraph = getParagraph(key, EColorUtils.lightBlckColorList, boldFont);
            Paragraph gradeParagraph = getParagraph(value, EColorUtils.lightBlckColorList, regularFont);

            Cell disciplineCell = new Cell().add(disciplineParagraph).setTextAlignment(TextAlignment.LEFT);
            Cell gradeCell = new Cell().add(gradeParagraph).setTextAlignment(TextAlignment.CENTER);
            
            table.addCell(disciplineCell);
            table.addCell(gradeCell);
        }

        examReportCardLayoutData.getDocument().add(table);
    }

	protected void generateAttendanceSummary(ExamReportCardLayoutData examReportCardLayoutData, ExamReportData examReportData,
										    PdfFont boldFont, PdfFont regularFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize()-1)
				.setTextAlignment(TextAlignment.LEFT).setPaddingTop(2f);

		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), new float[] { 0.2f, 0.8f});
		
		String attendedDays = "-";
		String totalDays = "-";
		
		if(examReportData.getTotalAttendedDays() != null) {
			attendedDays = String.valueOf(examReportData.getTotalAttendedDays());
		}
		if(examReportData. getTotalWorkingDays() != null) {
			totalDays = String.valueOf(examReportData.getTotalWorkingDays());
		}
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph("Attendance : ",EColorUtils.lightBlckColorList), getParagraph(attendedDays+"/"+totalDays)), cellLayoutSetup);

		examReportCardLayoutData.getDocument().add(table);

	}

    private Map<String, String> getPersonalityTraitKeyValueMap(List<StudentPersonalityTraitsResponse> studentPersonalityTraitsResponseList) {
        Map<String, String> personalityTraitKeyValueMap = new HashMap<>();
        if(CollectionUtils.isEmpty(studentPersonalityTraitsResponseList)) {
            return personalityTraitKeyValueMap;
        }

        for(StudentPersonalityTraitsResponse studentPersonalityTraitsResponse : studentPersonalityTraitsResponseList) {
            if(studentPersonalityTraitsResponse.getPersonalityTraitsDetails() == null) {
                continue;
            }
            personalityTraitKeyValueMap.put(studentPersonalityTraitsResponse.getPersonalityTraitsDetails().getPersonalityTraitName(),
                    studentPersonalityTraitsResponse.getResponse());
        }
        return personalityTraitKeyValueMap;
    }

	protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
										  ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont)
				.setFontSize(examReportCardLayoutData.getContentFontSize()-1).setTextAlignment(TextAlignment.CENTER)
				.setBorder(new SolidBorder(examReportCardLayoutData.getDefaultBorderWidth()));

		Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(),
				new float[] { 0.3f, 0.7f});

		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(),
				Arrays.asList(getParagraph("Class Teacher's Remark : ",EColorUtils.lightBlckColorList), getParagraph(examReportData.getRemarks() == null ? "" : examReportData.getRemarks(), EColorUtils.lightBlckColorList,regularFont)),
								cellLayoutSetup);
		examReportCardLayoutData.getDocument().add(remarksTable);
	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
										float contentFontSize, float defaultBorderWidth, int pageNumber,
										PdfFont boldFont, PdfFont regularFont,Map<CourseType, List<ExamGrade>> examReportGradeList) throws IOException {
		Table table = getPDFTable(documentLayoutSetup, new float[]{0.1f,0.15f,0.375f,0.375f});
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);

		Cell Date = new Cell();
		Date.setBorder(null).setTextAlignment(TextAlignment.RIGHT);
        
        Cell DateValue = new Cell();
		DateValue.setBorder(null).setTextAlignment(TextAlignment.LEFT);

		Cell emptySignatureCell1 = new Cell();
		emptySignatureCell1.setBorder(null);

		Cell emptySignatureCell2 = new Cell();
		emptySignatureCell2.setBorder(null);

		addRow(table, Arrays.asList(Date, DateValue, emptySignatureCell1, emptySignatureCell2));
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        String ReportCardGeneratedDate = currentDate.format(formatter);
		addRow(table, documentLayoutSetup, Arrays.asList(
						getParagraph("Date : ", EColorUtils.lightBlckColorList),
                        getParagraph(ReportCardGeneratedDate, EColorUtils.lightBlckColorList, regularFont),
						getParagraph("Signature of Class Teacher", EColorUtils.lightBlckColorList),
						getParagraph("Signature of Principal", EColorUtils.lightBlckColorList)),
				signatureCellLayoutSetup);
				
		table.setFixedPosition(30f, 20f, documentLayoutSetup.getPageSize().getWidth() - 100f); 
		document.add(table);
	}

	protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
											  List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			sortClassExamReports(examReportDataList);

			int pageNumber = 1;
			PdfFont regularFont = examReportCardLayoutData.getRegularFont();
			PdfFont boldFont = examReportCardLayoutData.getBoldFont();

			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
						pageNumber, boldFont, regularFont);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}

			examReportCardLayoutData.getDocument().close();
			return documentOutput;

		} catch (IOException e) {
			e.printStackTrace();
		}

		return null;
	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			PdfFont regularFont = getRegularFont();
			PdfFont boldFont = getRegularBoldFont();

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateScholasticMarksGrid(document,
						documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
						"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
						null, "#ff0000");
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}
}
