package com.lernen.cloud.pdf.exam.reports._10295;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridHeaderConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridTotalMarksRowConfigs;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.*;


/**
 *
 * <AUTHOR>
 *
 */
public class ExamReportGenerator10295 extends ExamReportGenerator implements IExamReportCardGenerator {

	public ExamReportGenerator10295(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator10295.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;
	public static final float STUDENT_IMAGE_WIDTH = 75f;
	public static final float STUDENT_IMAGE_HEIGHT = 75f;

	protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";
	protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";
	protected static final String COSCHOLASTIC_GRADE_DESCRIPTION = "Grading Scheme (Co-Scholastic) - A : Outstanding, B : Good, C : Fair";
	protected static final String SCHOLASTIC_DESCRIPTION = "Description - PT : Periodic Test, MA : Multiple Assessment, PF : Portfolio, SE : Subject Enrichment, HY : Half Yearly, YL : Yearly";
	protected static final String SCHOLASTIC_DESCRIPTION_9th_10th_CLASS = "Description - PT : Periodic Test, MA : Multiple Assessment, PF : Portfolio, SE : Subject Enrichment, PR. : Practical, HY : Half Yearly";
	protected static final String SCHOLASTIC_DESCRIPTION_11th_12th_PCM_CLASS = "Description - UT : Unit Test, Pr/IA : Practical/Internal Assessment\n*Theory/Practical Split - English, Hindi, Mathematics (80/20). Physics, Chemistry, Physical Education, Computer Science (70/30). Painting (30/70), Artificial Intelligence (50/50)";
	protected static final String SCHOLASTIC_DESCRIPTION_11th_12th_PCB_CLASS = "Description - UT : Unit Test, Pr/IA : Practical/Internal Assessment\n*Theory/Practical Split - English, Hindi (80/20). Physics, Chemistry, Biology, Physical Education, Computer Science (70/30). Painting (30/70).  Artificial Intelligence (50/50)";
	protected static final String SCHOLASTIC_DESCRIPTION_11th_12th_COMMERCE_CLASS = "Description - UT : Unit Test, Pr/IA : Practical/Internal Assessment\n*Theory/Practical Split - English, Hindi , Applied Mathematics, Accountancy, Business Studies, Economics: (80/20). Physical Education, Computer Science (70/30). Painting (30/70), Artificial Intelligence (50/50)";
	protected static final String SCHOLASTIC_DESCRIPTION_11th_12th_HUMANITIES_CLASS = "Description - UT : Unit Test, Pr/IA : Practical/Internal Assessment\n*Theory/Practical Split - English, Hindi , Sociology ,History,  Economics: (80/20). Physical Education (70/30). Painting (30/70), Artificial Intelligence (50/50)";
	private static final Map<String, String> MARKS_GRADE_MAP = new LinkedHashMap<>();



//	Class V to IX,  XI.
	protected static final String REMARK_ABOVE_0_AND_BELOW_40 = "Keep pushing forward and never underestimate your potential to succeed.";
	protected static final String REMARK_ABOVE_40_AND_BELOW_60 = "A Satisfactory Performance, Reflecting Your Diligence. Well Done!";
	protected static final String REMARK_ABOVE_60_AND_BELOW_70 = "Steady Progress Throughout the Year. Keep Building on Your Foundation!";
	protected static final String REMARK_ABOVE_70_AND_BELOW_80 = "Steady Progress Throughout the Year. Keep Building on Your Foundation!";
	protected static final String REMARK_ABOVE_80_AND_BELOW_85 = "Positive Attitude Towards Learning. Keep Nurturing It!";
	protected static final String REMARK_ABOVE_85_AND_BELOW_90 = "Your Commitment to Learning Is Noticeable. Keep Up the Good Work!";
	protected static final String REMARK_ABOVE_90_AND_BELOW_95 = "Your Determination Is Admirable. Continue Working Towards Your Goals!";
	protected static final String REMARK_ABOVE_95 = "You've Demonstrated a Solid Understanding of the content. Keep it up!";

	static {
		MARKS_GRADE_MAP.put("91 - 100", "A1");
		MARKS_GRADE_MAP.put("51 - 60", "C1");
		MARKS_GRADE_MAP.put("81 - 90", "A2");
		MARKS_GRADE_MAP.put("41 - 50", "C2");
		MARKS_GRADE_MAP.put("71 - 80", "B1");
		MARKS_GRADE_MAP.put("33 - 40", "D");
		MARKS_GRADE_MAP.put("61 - 70", "B2");
		MARKS_GRADE_MAP.put("32 & Below", "E (Fail)");
	}



	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
										 ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput,
					examReportData.getStandardMetaData().getStandardId());

			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();

			Set<UUID> studentIdSet = new HashSet<>();
			studentIdSet.add(examReportData.getStudentLite().getStudentId());
			Integer startDate = examReportData.getExamReportStructure().getExamReportStructureMetaData().getAttendanceStartDate();
			Integer endDate = examReportData.getExamReportStructure().getExamReportStructureMetaData().getAttendanceEndDate();
			if(startDate != null) {
				startDate = DateUtils.getDayStart(startDate, DateUtils.DEFAULT_TIMEZONE);
			}
			if(endDate != null) {
				endDate = DateUtils.getDayEnd(endDate, DateUtils.DEFAULT_TIMEZONE);
			}
			Map<UUID, Map<AttendanceStatus, Integer>> attendanceStatusIntegerMap = new HashMap<>();
			if(startDate != null && endDate != null) {
				attendanceStatusIntegerMap =
						studentManager.getStudentAttendanceDetails(institute.getInstituteId(),
								examReportData.getStandardMetaData().getAcademicSessionId(),
								studentIdSet, startDate, endDate);
			}

			generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
					1, boldFont, regularFont, attendanceStatusIntegerMap);

			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData,
										   Institute institute, ExamReportData examReportData,
										   StudentManager studentManager, String reportType,
										   int pageNumber, PdfFont boldFont, PdfFont regularFont,
										   Map<UUID, Map<AttendanceStatus, Integer>> attendanceStatusIntegerMap) throws IOException {

		generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
				examReportData, pageNumber, boldFont, regularFont);


		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
		/**
		 * hide dimension max marks is specific to 11th class
		 */
		GridConfigs gridConfigs = new GridConfigs(new GridHeaderConfigs(false), GridTotalMarksRowConfigs.skipTotalRow());
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				regularFont, boldFont, examReportCardLayoutData.getContentFontSize() - 2,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs,
				"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
				nonAdditionalSubjects, "MM", "MO", "#ff0000",
				"-");

		if(!CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()
				.get(CourseType.SCHOLASTIC).getAdditionalCourses())) {
			addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
			generateAdditionalCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, gridConfigs,
					"Additional Subjects", getScholasticMarksGridSubjectWidth(reportType), boldFont, regularFont);
		}

		List<UUID> standardIdList1stTo2nd = new ArrayList<>();
		standardIdList1stTo2nd.add(UUID.fromString("7a62b6f8-c365-419f-8ddd-03482ba6f15e"));
		standardIdList1stTo2nd.add(UUID.fromString("b6666941-72f6-4fd1-bb42-30d377ac7e07"));


        List<UUID> standardIdList9thTo10th = new ArrayList<>();
		standardIdList9thTo10th.add(UUID.fromString("06e0eb7f-eaf1-4975-9563-014b7edbfc35"));
		standardIdList9thTo10th.add(UUID.fromString("b15df564-a279-403f-9532-89558b56b0c7"));


		List<UUID> standarIdList11thTo12thPCM = new ArrayList<>();
		standarIdList11thTo12thPCM.add(UUID.fromString("d5f95f9f-6da7-4f62-939a-bd0379b064f3"));
		standarIdList11thTo12thPCM.add(UUID.fromString("0a7504f4-546e-43fc-bde3-bca68e1be6ca"));

		List<UUID> standardIdList11thTo12thPCB = new ArrayList<>();
		standardIdList11thTo12thPCB.add(UUID.fromString("568dbae5-b563-4547-a71e-7019c6112d2c"));
		standardIdList11thTo12thPCB.add(UUID.fromString("1c9d9597-c1ac-4d23-adb3-a4554a15a58b"));

		List<UUID> standardIdList11thTo12thCOMM = new ArrayList<>();
		standardIdList11thTo12thCOMM.add(UUID.fromString("f7539881-0bd1-4abe-825e-b53f0f431c14"));
		standardIdList11thTo12thCOMM.add(UUID.fromString("65e2c5ad-e9ec-4499-9d76-48b7cb865312"));

		List<UUID> standardIdList11thTo12thHumanities = new ArrayList<>();
		standardIdList11thTo12thHumanities.add(UUID.fromString("0a7504f4-546e-43fc-bde3-bca68e1be6ca"));
		standardIdList11thTo12thHumanities.add(UUID.fromString("dd804e60-ec2c-4cab-a5de-34073f72b9c6"));

		UUID standardId = examReportData.getStandardMetaData().getStandardId();

		String scholasticDescription =  standardIdList9thTo10th.contains(standardId) ? SCHOLASTIC_DESCRIPTION_9th_10th_CLASS :
				                        standarIdList11thTo12thPCM.contains(standardId) ? SCHOLASTIC_DESCRIPTION_11th_12th_PCM_CLASS :
					                	standardIdList11thTo12thPCB.contains(standardId) ? SCHOLASTIC_DESCRIPTION_11th_12th_PCB_CLASS :
							        	standardIdList11thTo12thCOMM.contains(standardId) ? SCHOLASTIC_DESCRIPTION_11th_12th_COMMERCE_CLASS :
										standardIdList11thTo12thHumanities.contains(standardId) ? SCHOLASTIC_DESCRIPTION_11th_12th_HUMANITIES_CLASS :
										standardIdList1stTo2nd.contains(standardId) ? "" :
												SCHOLASTIC_DESCRIPTION;

		if (StringUtils.isEmpty(scholasticDescription)) {
			addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		}
		generateCoScholasticExamDescription(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportData, boldFont, regularFont, scholasticDescription);
		if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
			generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 2  ,
					examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs,
					"Co-Scholastic Subjects", getCoScholasticMarksGridSubjectWidth(reportType), "-");
			generateCoScholasticExamDescription(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
					examReportData, boldFont, regularFont, COSCHOLASTIC_GRADE_DESCRIPTION);
		}

		generateStudentAttributeTable(examReportCardLayoutData, examReportData, boldFont, regularFont);

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		generateResultSummary(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportData, reportType, boldFont, regularFont, studentManager, attendanceStatusIntegerMap);
		generateRemarksSection(examReportCardLayoutData, examReportData, examReportCardLayoutData.getContentFontSize() - 1,
				boldFont, regularFont);
		generateSignatureBox(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, boldFont, regularFont);

		generateBorderLayout(examReportCardLayoutData, pageNumber);
	}

	protected void generateCoScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
													   float contentFontSize, ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont, String description) throws IOException {
		if(org.apache.commons.lang.StringUtils.isBlank(description)) {
			return;
		}
		Text descTitle = new Text(description).setFont(regularFont).setFontSize(contentFontSize - 3);
		Paragraph desc = new Paragraph();
		desc.add(descTitle);
		document.add(desc.setFontColor(new DeviceRgb(0,0,139)));
	}

	protected void generateStudentAttributeTable(ExamReportCardLayoutData examReportCardLayoutData,
												 ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {
		CellLayoutSetup attributeCellLayoutSetup = new CellLayoutSetup();
		attributeCellLayoutSetup.setPdfFont(boldFont)
				.setFontSize(examReportCardLayoutData.getContentFontSize() - 1).setTextAlignment(TextAlignment.CENTER)
				.setBorder(new SolidBorder(1f));

		Table attributesTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(),
				new float[] { 0.20f, 0.13f, 0.20f, 0.13f, 0.21f, 0.13f });

		addRow(attributesTable, examReportCardLayoutData.getDocumentLayoutSetup(),
				Arrays.asList(getParagraph("Height (cm)", EColorUtils.darkBlueColorList, boldFont),
						getParagraph(examReportData.getHeight(), EColorUtils.darkBlueColorList, regularFont),
						getParagraph("Weight (kg)", EColorUtils.darkBlueColorList, boldFont),
						getParagraph(examReportData.getWeight(), EColorUtils.darkBlueColorList, regularFont),
						getParagraph("Blood Group", EColorUtils.darkBlueColorList, boldFont),
						getParagraph(examReportData.getStudentLite().getBloodGroup() == null ? ""
								: examReportData.getStudentLite().getBloodGroup().getDisplayName(), EColorUtils.darkBlueColorList, regularFont)), attributeCellLayoutSetup);

		examReportCardLayoutData.getDocument().add(attributesTable);
	}

	protected void generateAdditionalCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
															   ExamReportData examReportData, GridConfigs gridConfigs,
															   String subjectColumnTitle, float subjectColumnWidth, PdfFont boldFont, PdfFont regularFont) throws IOException {
		ExamReportStructure examReportStructure = examReportData.getExamReportStructure();
		if (examReportStructure == null || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure())
				|| examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) == null
				|| CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC)
				.getAdditionalCourses())) {
			return;
		}

		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				regularFont, boldFont, examReportCardLayoutData.getContentFontSize() - 1,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs,
				subjectColumnTitle, subjectColumnWidth,
				examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses(),
				"MM", "MO", "#ff0000", "-");
	}

	protected float getScholasticMarksGridSubjectWidth(String reportType) {
		return 0.2f;
	}

	protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.5f;
		}
		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			return 0.4f;
		}
		return 0.4f;
	}

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput,
																	UUID standardId)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
		float contentFontSize = standardId != null ?
				standardId.equals(UUID.fromString("06e0eb7f-eaf1-4975-9563-014b7edbfc35")) ? 11f : 12f : 12f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
		float logoWidth = 60f;
		float logoHeight = 60f;

		int instituteId = institute.getInstituteId();
		return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
				defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
	}

	protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
										  StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
										  int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		//Institute watermark
		int instituteId = institute.getInstituteId();
		float watermarkImageHeightWidth = 400f;
		generateWatermark(examReportCardLayoutData, instituteId, watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20,
				watermarkImageHeightWidth / 2);
		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.89f,
				boldFont, regularFont, studentManager);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, boldFont, regularFont);
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
								  Institute institute, String reportType, float offsetX, float offsetY,
								  PdfFont boldFont, PdfFont regularFont, StudentManager studentManager) throws IOException {

		generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY + 10f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		generateLogo(examReportCardLayoutData, institute, ImageProvider.INSTANCE.getImage(ImageProvider._CBSE_LOGO_2),
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - (2.5f * offsetX), offsetY + 10f);

		byte[] studentImage = getStudentImage(institute.getInstituteId(), studentLite.getStudentId(), studentManager);
		if (studentImage != null) {
			generateImage(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), studentImage, STUDENT_IMAGE_WIDTH, STUDENT_IMAGE_HEIGHT,
					examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - offsetX - STUDENT_IMAGE_WIDTH + 12f,
					offsetY - STUDENT_IMAGE_HEIGHT -2f);
		}

		int singleContentColumn = 1;
		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase())
						.setFontColor(new DeviceRgb(27, 56, 121))), cellLayoutSetup.copy().setFontSize(17f));

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1()).setMultipliedLeading(1f)
						.setFontColor(new DeviceRgb(EColorUtils.lightRed1R, EColorUtils.lightRed1G, EColorUtils.lightRed1B))),
				cellLayoutSetup.copy().setFontSize(9f));

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine2()).setMultipliedLeading(1f)
						.setFontColor(new DeviceRgb(EColorUtils.lightRed1R, EColorUtils.lightRed1G, EColorUtils.lightRed1B))),
				cellLayoutSetup.copy().setFontSize(9f));

		examReportCardLayoutData.getDocument().add(table);
		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
		String headerExamTitle = StringHelper.replaceUnderscoreWithHypen(reportType);
		headerExamTitle += " Progress Report -" + studentLite.getStudentSessionData().getShortYearDisplayName() + "";
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()
						.setFontColor(new DeviceRgb(0,0,139))),
				cellLayoutSetup.copy().setFontSize(11f).setPdfFont(boldFont));
		examReportCardLayoutData.getDocument().add(table);
		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
	}

	protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
											  StudentLite studentLite, ExamReportData examReportData,
											  PdfFont boldFont, PdfFont regularFont) throws IOException {

		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize() - 1;

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.35f, 0.1f, 0.35f, 0.2f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup forthCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);

		Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName(),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph fatherName = getKeyValueParagraph("Father Name : ", studentLite.getFathersName(),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph dob = getKeyValueParagraph("DOB : ",
				studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
						: DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph motherName = getKeyValueParagraph("Mother Name : ", studentLite.getMothersName(),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ", studentLite.getAdmissionNumber(),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont).setMultipliedLeading(1f);
		Paragraph classValue = getKeyValueParagraph("Class : ",
				studentLite.getStudentSessionData().getStandardNameWithSection(),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont).setMultipliedLeading(1f);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup), new CellData(fatherName, thirdCellLayoutSetup), new CellData(EMPTY_TEXT, forthCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(dob, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),new CellData(motherName, thirdCellLayoutSetup), new CellData(EMPTY_TEXT, forthCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),new CellData(classValue, thirdCellLayoutSetup), new CellData(EMPTY_TEXT, forthCellLayoutSetup)));
		document.add(table);

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
	}

	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
		Color color = WebColors.getRGBColor("#1b3879");
		canvas.setColor(color, false);
		canvas.setLineWidth(3f);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
				documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
	}

	protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
										 float contentFontSize, ExamReportData examReportData,
										 String reportType, PdfFont boldFont, PdfFont regularFont,
										 StudentManager studentManager, Map<UUID, Map<AttendanceStatus, Integer>> attendanceStatusIntegerMap) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT);

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		Paragraph result = getKeyValueParagraph("Result : ", "-",
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);

		if(examReportData.getExamResultStatus() != null){
			result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
					EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
		}

		Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
				examReportData.getPromotedTo() == null ? "" : examReportData.getPromotedTo().getStandardName(),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);

		String noOfPresent = "-";
		String noOfMeetings = "-";
		boolean attendanceDataSet = false;

		if(examReportData.getTotalAttendedDays() != null && examReportData.getTotalWorkingDays() != null) {
			noOfPresent = examReportData.getTotalAttendedDays() == null ? "-" : examReportData.getTotalAttendedDays().toString();
			noOfMeetings = examReportData.getTotalWorkingDays() == null ? "-" : examReportData.getTotalWorkingDays().toString();
			attendanceDataSet = true;
		}

		if(!attendanceDataSet) {
			if (!CollectionUtils.isEmpty(attendanceStatusIntegerMap)) {
				Map<AttendanceStatus, Integer> map = attendanceStatusIntegerMap.get(examReportData.getStudentLite().getStudentId());
				if (map != null) {
					Integer presentCount = map.get(AttendanceStatus.PRESENT);
					Integer absentCount = map.get(AttendanceStatus.ABSENT);
					Integer leaveCount = map.get(AttendanceStatus.LEAVE);
					Integer halfDayCount = map.get(AttendanceStatus.HALF_DAY);
					noOfPresent = com.lernen.cloud.core.utils.StringHelper.removeTrailingZero(String.valueOf(presentCount + (halfDayCount / 2d)));
					noOfMeetings = String.valueOf(presentCount + absentCount + leaveCount + halfDayCount);
				}
			}
		}

		String attendanceText = noOfPresent + "/" + noOfMeetings;
		Paragraph obtainedMarks = getKeyValueParagraph("Attendance : ", attendanceText,
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont).setMultipliedLeading(1f);

		Paragraph totalMarks = getKeyValueParagraph("Place & Date : ",
				"Noida" + (examReportData.getDateOfResultDeclaration() == null ? "" : " " + DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration())),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont).setMultipliedLeading(1f);

//		Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
//						: Math.round(examReportData.getPercentage() * 100) / 100d + "%",
//				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
//		Paragraph grade = getKeyValueParagraph("Grade : ",
//				examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(),
//				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);

		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
					new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(promotedClass, cellLayoutSetup)));
		}

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));

		document.add(table);

	}

	protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
										  ExamReportData examReportData, float contentFontSize, PdfFont boldFont, PdfFont regularFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT);

		Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 1);

		UUID standardId = examReportData.getStandardMetaData().getStandardId();
		Double percentage = examReportData.getPercentage();
//		Set<UUID> vToXIIClassId = new HashSet<>(Arrays.asList(
//				UUID.fromString("2e635681-5671-42ac-9443-9b5643aecddb"),//V
//				UUID.fromString("6e2fd07e-a192-4ee3-a0d3-99dc4ee77962"),//VI
//				UUID.fromString("4dd40a3d-40f6-4ec2-b2ec-6b76b459eae3"),//VII
//				UUID.fromString("adc959ff-60be-4fff-88db-87e0097f9da5"),//VIII
//				UUID.fromString("06e0eb7f-eaf1-4975-9563-014b7edbfc35"),//IX
//				UUID.fromString("b15df564-a279-403f-9532-89558b56b0c7"),//X
//				UUID.fromString("d5f95f9f-6da7-4f62-939a-bd0379b064f3"),//XI-PCM
//				UUID.fromString("568dbae5-b563-4547-a71e-7019c6112d2c"),//XI-PCB
//				UUID.fromString("f7539881-0bd1-4abe-825e-b53f0f431c14"),//XI-Comm
//				UUID.fromString("aff321ce-e593-4490-b223-eed7a64c50a3"),//XI-Hum
//				UUID.fromString("0a7504f4-546e-43fc-bde3-bca68e1be6ca"),//XII-PCM
//				UUID.fromString("1c9d9597-c1ac-4d23-adb3-a4554a15a58b"),//XII-PCB
//				UUID.fromString("65e2c5ad-e9ec-4499-9d76-48b7cb865312"),//XII-Comm
//				UUID.fromString("dd804e60-ec2c-4cab-a5de-34073f72b9c6")//XII-Hum
//		));

		Paragraph remarks = getKeyValueParagraph("Class Teacher's Remark : ",
				StringUtils.isBlank(examReportData.getRemarks()) ? getRemark(percentage) : examReportData.getRemarks(),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);

		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(remarks),
				cellLayoutSetup);
		examReportCardLayoutData.getDocument().add(remarksTable);
	}

	public static String getRemark(Double percentage) {
		if (percentage == null) {
			return "";
		}
		if (percentage >= 95.0) {
			return REMARK_ABOVE_95;
		} else if (percentage >= 90) {
			return REMARK_ABOVE_90_AND_BELOW_95;
		} else if (percentage >= 85) {
			return REMARK_ABOVE_85_AND_BELOW_90;
		} else if (percentage >= 80) {
			return REMARK_ABOVE_80_AND_BELOW_85;
		} else if (percentage >= 70) {
			return REMARK_ABOVE_70_AND_BELOW_80;
		} else if (percentage >= 60) {
			return REMARK_ABOVE_60_AND_BELOW_70;
		} else if (percentage >= 40) {
			return REMARK_ABOVE_40_AND_BELOW_60;
		} else if (percentage < 40) {
			return REMARK_ABOVE_0_AND_BELOW_40;
		}
		return REMARK_ABOVE_0_AND_BELOW_40;
	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
										float contentFontSize, float defaultBorderWidth, int pageNumber,
										PdfFont boldFont, PdfFont regularFont) throws IOException {
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		canvas.moveTo(12, 100);
		canvas.lineTo(583, 100);
		canvas.setLineWidth(.5f);
		canvas.closePathStroke();
		int singleContentColumn = 3;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(
						getParagraph("Class Teacher").setFontColor(new DeviceRgb(0,0,139)),
						getParagraph("").setFontColor(new DeviceRgb(0,0,139)),
						getParagraph("Principal").setFontColor(new DeviceRgb(0,0,139))),
				signatureCellLayoutSetup);
		table.setFixedPosition(30f, 100f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);
		generateGradeBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth, regularFont, boldFont);
	}

	private void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								  float defaultBorderWidth, PdfFont regularFont, PdfFont boldFont) throws IOException {
		addBlankLine(document, true, 1);

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph("Marks Range")
				.setFontColor(new DeviceRgb(0,0,139)), signatureCellLayoutSetup)));
		table.setFixedPosition(10f, 80f, documentLayoutSetup.getPageSize().getWidth());
		document.add(table);

		float[] columnWidths = new float[] { 0.10f, 0.10f, 0.10f, 0.10f, 0.10f, 0.10f, 0.10f, 0.10f };
		Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 2)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
		addBlankLine(document, false, 1);
		List<CellData> headerList = new ArrayList<>();
		headerList.add(new CellData(getParagraph("Marks").setFontColor(new DeviceRgb(255,255,255)),
				marksCellLayoutSetup.copy().setBackgroundColor("#DF5143")));
		headerList.add(new CellData(getParagraph("Grade").setFontColor(new DeviceRgb(255,255,255)),
				marksCellLayoutSetup.copy().setBackgroundColor("#DF5143")));
		headerList.add(new CellData(getParagraph("Marks").setFontColor(new DeviceRgb(255,255,255)),
				marksCellLayoutSetup.copy().setBackgroundColor("#DF5143")));
		headerList.add(new CellData(getParagraph("Grade").setFontColor(new DeviceRgb(255,255,255)),
				marksCellLayoutSetup.copy().setBackgroundColor("#DF5143")));
		headerList.add(new CellData(getParagraph("Marks").setFontColor(new DeviceRgb(255,255,255)),
				marksCellLayoutSetup.copy().setBackgroundColor("#DF5143")));
		headerList.add(new CellData(getParagraph("Grade").setFontColor(new DeviceRgb(255,255,255)),
				marksCellLayoutSetup.copy().setBackgroundColor("#DF5143")));
		headerList.add(new CellData(getParagraph("Marks").setFontColor(new DeviceRgb(255,255,255)),
				marksCellLayoutSetup.copy().setBackgroundColor("#DF5143")));
		headerList.add(new CellData(getParagraph("Grade").setFontColor(new DeviceRgb(255,255,255)),
				marksCellLayoutSetup.copy().setBackgroundColor("#DF5143")));
		addRow(headerTable, documentLayoutSetup, headerList);
		List<Map.Entry<String, String>> gradesList = new ArrayList<>(MARKS_GRADE_MAP.entrySet());
		for(int index = 0; index < gradesList.size() / 2; index++) {
			List<CellData> row = new ArrayList<>();
			row.add(new CellData(getParagraph(gradesList.get(index).getKey()).setFontColor(new DeviceRgb(0,0,139)), marksCellLayoutSetup));
			row.add(new CellData(getParagraph(gradesList.get(index).getValue()).setFontColor(new DeviceRgb(0,0,139)), marksCellLayoutSetup));
			row.add(new CellData(getParagraph(gradesList.get(gradesList.size() / 2 + index).getKey()).setFontColor(new DeviceRgb(0,0,139)), marksCellLayoutSetup));
			row.add(new CellData(getParagraph(gradesList.get(gradesList.size() / 2 + index).getValue()).setFontColor(new DeviceRgb(0,0,139)), marksCellLayoutSetup));
			addRow(headerTable, documentLayoutSetup, row);
		}
		headerTable.setFixedPosition(50f, 20f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(headerTable);
	}

	protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
											  List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput,
					CollectionUtils.isEmpty(examReportDataList) ? null : examReportDataList.get(0).getStandardMetaData().getStandardId());

			sortClassExamReports(examReportDataList);

			int pageNumber = 1;
			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();

			Set<UUID> studentIdSet = new HashSet<>();
			for(ExamReportData examReportData : examReportDataList) {
				studentIdSet.add(examReportData.getStudentLite().getStudentId());
			}
			Integer startDate = examReportDataList.get(0).getExamReportStructure().getExamReportStructureMetaData().getAttendanceStartDate();
			Integer endDate = examReportDataList.get(0).getExamReportStructure().getExamReportStructureMetaData().getAttendanceEndDate();
			if(startDate != null) {
				startDate = DateUtils.getDayStart(startDate, DateUtils.DEFAULT_TIMEZONE);
			}
			if(endDate != null) {
				endDate = DateUtils.getDayEnd(endDate, DateUtils.DEFAULT_TIMEZONE);
			}
			Map<UUID, Map<AttendanceStatus, Integer>> attendanceStatusIntegerMap = new HashMap<>();
			if(startDate != null && endDate != null) {
				attendanceStatusIntegerMap =
						studentManager.getStudentAttendanceDetails(institute.getInstituteId(),
								examReportDataList.get(0).getStandardMetaData().getAcademicSessionId(),
								studentIdSet, startDate, endDate);
			}

			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
						pageNumber, boldFont, regularFont, attendanceStatusIntegerMap);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}

			examReportCardLayoutData.getDocument().close();
			return documentOutput;

		} catch (IOException e) {
			e.printStackTrace();
		}

		return null;
	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			PdfFont regularFont = getRegularFont();
			PdfFont boldFont = getRegularBoldFont();

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateScholasticMarksGrid(document,
						documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
						"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
						null, "#ff0000");
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}
}