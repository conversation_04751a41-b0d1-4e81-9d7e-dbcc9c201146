package com.lernen.cloud.pdf.exam.reports._10295;


import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamDimensionObtainedValues;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.StudentPersonalityTraitsResponse;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridHeaderConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridTotalMarksRowConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.*;


/**
 *
 * <AUTHOR>
 *
 */
public class ExamReportGenerator3rdTo8th10295 extends ExamReportGenerator10295 implements IExamReportCardGenerator {

    public ExamReportGenerator3rdTo8th10295(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    private static final Logger logger = LogManager.getLogger(ExamReportGenerator3rdTo8th10295.class);
    protected static final String SCHOLASTIC_DESCRIPTION = "Description - PT : Periodic Test, MA : Multiple Assessment, PF : Portfolio, SE : Subject Enrichment, HY : Half Yearly";
    protected static final String SCHOLASTIC_DESCRIPTION_YEARLY = "Description - PT : Periodic Test, MA : Multiple Assessment, PF : Portfolio, SE : Subject Enrichment, HY : Half Yearly, YL : Yearly";

    @Override
    public DocumentOutput generateReport(Institute institute, Student student, String reportType,
                                         ExamReportData examReportData, String documentName, StudentManager studentManager) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput,
                    examReportData.getStandardMetaData().getStandardId());

            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();

            Set<UUID> studentIdSet = new HashSet<>();
            studentIdSet.add(examReportData.getStudentLite().getStudentId());
            Integer startDate = examReportData.getExamReportStructure().getExamReportStructureMetaData().getAttendanceStartDate();
            Integer endDate = examReportData.getExamReportStructure().getExamReportStructureMetaData().getAttendanceEndDate();
            if(startDate != null) {
                startDate = DateUtils.getDayStart(startDate, DateUtils.DEFAULT_TIMEZONE);
            }
            if(endDate != null) {
                endDate = DateUtils.getDayEnd(endDate, DateUtils.DEFAULT_TIMEZONE);
            }
            Map<UUID, Map<AttendanceStatus, Integer>> attendanceStatusIntegerMap = new HashMap<>();
            if(startDate != null && endDate != null) {
                attendanceStatusIntegerMap =
                        studentManager.getStudentAttendanceDetails(institute.getInstituteId(),
                                examReportData.getStandardMetaData().getAcademicSessionId(),
                                studentIdSet, startDate, endDate);
            }


            generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                    1, boldFont, regularFont, attendanceStatusIntegerMap);

            examReportCardLayoutData.getDocument().close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, student {}, reportType {} ",
                    institute.getInstituteId(), student.getStudentId(), reportType, e);
        }
        return null;
    }

    private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData,
                                           Institute institute, ExamReportData examReportData,
                                           StudentManager studentManager, String reportType,
                                           int pageNumber, PdfFont boldFont, PdfFont regularFont,
                                           Map<UUID, Map<AttendanceStatus, Integer>> attendanceStatusIntegerMap) throws IOException {

        generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
                examReportData, pageNumber, boldFont, regularFont);


        Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
        /**
         * hide dimension max marks is specific to 11th class
         */
        GridConfigs gridConfigs = new GridConfigs(new GridHeaderConfigs(false), GridTotalMarksRowConfigs.skipTotalRow());
        generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
                regularFont, boldFont, examReportCardLayoutData.getContentFontSize() - 2,
                examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs,
                "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
                nonAdditionalSubjects, "MM", "MO", "#ff0000",
                "-");

        if(reportType.equalsIgnoreCase("TERM_1")) {
            generateCoScholasticExamDescription(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                    examReportData, boldFont, regularFont, SCHOLASTIC_DESCRIPTION);
            if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
                generateCoScholasticMarksGridSection(examReportCardLayoutData.getDocument(),
                        examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 2,
                        examReportCardLayoutData.getDefaultBorderWidth(), false, examReportData,
                        regularFont, boldFont, reportType);
                generateCoScholasticExamDescription(examReportCardLayoutData.getDocument(),
                        examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                        examReportData, boldFont, regularFont, COSCHOLASTIC_GRADE_DESCRIPTION);
            }
        }

        if(reportType.equalsIgnoreCase("ANNUAL")){
            generateCoScholasticExamDescription(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                    examReportData, boldFont, regularFont, SCHOLASTIC_DESCRIPTION_YEARLY);
            if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
                generateCoScholasticMarksGridSection(examReportCardLayoutData.getDocument(),
                        examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 2,
                        examReportCardLayoutData.getDefaultBorderWidth(), false, examReportData,
                        regularFont, boldFont, reportType);
                generateCoScholasticExamDescription(examReportCardLayoutData.getDocument(),
                        examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                        examReportData, boldFont, regularFont, COSCHOLASTIC_GRADE_DESCRIPTION);
            }
        }

        generateStudentAttributeTable(examReportCardLayoutData, examReportData, boldFont, regularFont);

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        generateResultSummary(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                examReportData, reportType, boldFont, regularFont, studentManager, attendanceStatusIntegerMap);
        generateRemarksSection(examReportCardLayoutData, examReportData, examReportCardLayoutData.getContentFontSize() - 1,
                boldFont, regularFont);
        generateSignatureBox(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, boldFont, regularFont);

        generateBorderLayout(examReportCardLayoutData, pageNumber);
    }
    private void generateCoScholasticMarksGridSection(Document document, DocumentLayoutSetup documentLayoutSetup,
                                                      float contentFontSize, float defaultBorderWidth,  boolean showTotal,
                                                      ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont, String reportType) throws IOException {


        ExamReportMarksGrid examReportCoscholasticMarksGrid = examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.COSCHOLASTIC);
        ExamReportMarksGrid examReportScholasticMarksGrid = examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC);
        Set<UUID> additionalCourseIds = examReportData.getExamReportStructure().getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses();
        float subjectColumnWidth = 0.25f;
        CourseType courseType = CourseType.COSCHOLASTIC;

        float[] columnWidths = reportType.equalsIgnoreCase("ANNUAL")
                ? new float[] { subjectColumnWidth, 0.08f, 0.08f, 0.08f, 0.08f, 0.02f, 0.25f, 0.16f }
                : new float[] { subjectColumnWidth, 0.16f, 0.16f, 0.02f, 0.25f, 0.16f };
        Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);

        CellLayoutSetup emptyCellLayoutSetup = new CellLayoutSetup();
        CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
        marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 1)
                .setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

        CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
                .setTextAlignment(TextAlignment.LEFT);

        addBlankLine(document, false, 1);

        List<CellData> headerList = new ArrayList<>();

        ExamReportCourseStructure examReportCourseStructure = examReportData.getExamReportStructure().getExamReportCourseStructure() == null ? null :
                CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()) ? null :
                        examReportData.getExamReportStructure().getExamReportCourseStructure().get(CourseType.SCHOLASTIC);

        String gridCourseBackgroundColor =  examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesBackgroundColor();
        String gridCourseTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesTextColor();
        String gridHeadingBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridHeadingBackgroundColor();
        String gridHeadingTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridHeadingTextColor();
        CellLayoutSetup courseHeaderCellLayoutSetup = new CellLayoutSetup();
        courseHeaderCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1).setBorder(
                new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

        if(!org.apache.commons.lang3.StringUtils.isBlank(gridCourseBackgroundColor)) {
            courseHeaderCellLayoutSetup.setBackgroundColor(gridCourseBackgroundColor);
        }
        CellLayoutSetup headerCellLayoutSetup = new CellLayoutSetup();
        headerCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1).setBorder(
                new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
        if(!org.apache.commons.lang3.StringUtils.isBlank(gridHeadingBackgroundColor)) {
            headerCellLayoutSetup.setBackgroundColor(gridHeadingBackgroundColor);
        }

        List<Integer> rgbGridHeadingColorCode = null;
        if(!org.apache.commons.lang3.StringUtils.isBlank(gridHeadingTextColor)) {
            rgbGridHeadingColorCode = EColorUtils.hex2Rgb(gridHeadingTextColor);
        }
        List<Integer> rgbGridCourseColorCode = null;
        if(!org.apache.commons.lang3.StringUtils.isBlank(gridCourseTextColor)) {
            rgbGridCourseColorCode = EColorUtils.hex2Rgb(gridCourseTextColor);
        }


        if(CollectionUtils.isEmpty(rgbGridCourseColorCode)) {
            headerList.add(new CellData("Co-Scholastic Subjects (Marks Based)", courseHeaderCellLayoutSetup));
        } else {
            headerList.add(new CellData(getParagraph("Co-Scholastic Subjects (Marks Based)", rgbGridCourseColorCode.get(0),
                    rgbGridCourseColorCode.get(1), rgbGridCourseColorCode.get(2)), courseHeaderCellLayoutSetup.setFontSize(contentFontSize)));
        }

        if (CollectionUtils.isEmpty(rgbGridHeadingColorCode)) {
            headerList.add(new CellData("HY (25)", headerCellLayoutSetup));
            if(reportType.equalsIgnoreCase("ANNUAL"))  {
                headerList.add(new CellData("AN (25)", headerCellLayoutSetup));
                headerList.add(new CellData("Total", headerCellLayoutSetup));
            }
            headerList.add(new CellData("Grade", headerCellLayoutSetup));
        } else {
            headerList.add(new CellData(getParagraph("HY (25)", rgbGridHeadingColorCode.get(0),
                    rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerCellLayoutSetup));
            if(reportType.equalsIgnoreCase("ANNUAL"))  {
                headerList.add(new CellData(getParagraph("AN (25)", rgbGridHeadingColorCode.get(0),
                        rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerCellLayoutSetup));
                headerList.add(new CellData(getParagraph("Total", rgbGridHeadingColorCode.get(0),
                        rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerCellLayoutSetup));
            }
            headerList.add(new CellData(getParagraph("Grade", rgbGridHeadingColorCode.get(0),
                    rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerCellLayoutSetup));
        }

        headerList.add(new CellData("", emptyCellLayoutSetup));
        headerList.addAll(getHeaderCells(examReportCoscholasticMarksGrid,
                examReportData, courseType, defaultBorderWidth, regularFont, boldFont, contentFontSize, contentFontSize,
                subjectColumnWidth, "Co-Scholastic Subjects", true, "MM", "MO", false));
        addRow(headerTable, documentLayoutSetup, headerList);


        Collections.sort(examReportCoscholasticMarksGrid.getExamReportCourseMarksRows(),
                new Comparator<ExamReportCourseMarksRow>() {

                    @Override
                    public int compare(ExamReportCourseMarksRow o1, ExamReportCourseMarksRow o2) {

                        return o1.getCourse().compareTo(o2.getCourse());
                    }
                });
        Collections.sort(examReportScholasticMarksGrid.getExamReportCourseMarksRows(),
                new Comparator<ExamReportCourseMarksRow>() {

                    @Override
                    public int compare(ExamReportCourseMarksRow o1, ExamReportCourseMarksRow o2) {

                        return o1.getCourse().compareTo(o2.getCourse());
                    }
                });
        Map<String, String> coscholasticCourseKeyValueMap = getCoscholasticCourseKeyValueMap(examReportCoscholasticMarksGrid);

        List<Map.Entry<String, String>> coscholasticCourseList = new ArrayList<>(coscholasticCourseKeyValueMap.entrySet());
        int index = 0;

        courseHeaderCellLayoutSetup.setTextAlignment(TextAlignment.LEFT);
        for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportScholasticMarksGrid.getExamReportCourseMarksRows()) {
            if (CollectionUtils.isEmpty(examReportCourseMarksRow.getExamReportCourseMarksColumns())) {
                continue;
            }

            if(!additionalCourseIds.contains(examReportCourseMarksRow.getCourse().getCourseId())) {
                continue;
            }

            List<CellData> row = new ArrayList<>();
            row.add(new CellData(getParagraph(examReportCourseMarksRow.getCourse().getCourseName(), EColorUtils.darkBlueColorList), courseHeaderCellLayoutSetup));
            for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportCourseMarksRow
                    .getExamReportCourseMarksColumns()) {
                if(examReportCourseMarksColumn.getId().equalsIgnoreCase("HY") && examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM){
                    for(ExamDimensionObtainedValues examDimensionObtainedValues : examReportCourseMarksColumn.getExamDimensionObtainedValuesList()){
                        final Double maxMarks = examDimensionObtainedValues.getMaxMarks();
                        final Double minMarks =  examDimensionObtainedValues.getMinMarks();
                        if(examDimensionObtainedValues.getExamDimension().isTotal()){
                            String finalObtainedValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedMarks() == null
                                            ? null : String.valueOf(NumberUtils.formatDouble(examDimensionObtainedValues.getObtainedMarks(), 2)),
                                    examDimensionObtainedValues.getAttendanceStatus(), minMarks, true, " (F)", maxMarks, "", false);
                            String obtainedValue = StringUtils.isEmpty(finalObtainedValue)? "-" : finalObtainedValue;
                            row.add(new CellData(getParagraph(obtainedValue, EColorUtils.darkBlueColorList), marksCellLayoutSetup));
                            break;
                        }

                    }

                }
                if(reportType.equalsIgnoreCase("ANNUAL")) {
                    if(examReportCourseMarksColumn.getId().equalsIgnoreCase("AN") && examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM){
                        for(ExamDimensionObtainedValues examDimensionObtainedValues : examReportCourseMarksColumn.getExamDimensionObtainedValuesList()){
                            final Double maxMarks = examDimensionObtainedValues.getMaxMarks();
                            final Double minMarks =  examDimensionObtainedValues.getMinMarks();
                            if(examDimensionObtainedValues.getExamDimension().isTotal()){
                                String finalObtainedValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedMarks() == null
                                                ? null : String.valueOf(NumberUtils.formatDouble(examDimensionObtainedValues.getObtainedMarks(), 2)),
                                        examDimensionObtainedValues.getAttendanceStatus(), minMarks, true, " (F)", maxMarks, "", false);
                                String obtainedValue = StringUtils.isEmpty(finalObtainedValue)? "-" : finalObtainedValue;
                                row.add(new CellData(getParagraph(obtainedValue, EColorUtils.darkBlueColorList), marksCellLayoutSetup));
                                break;
                            }

                        }

                    }
                }
            }
            for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportCourseMarksRow
                    .getExamReportCourseMarksColumns()) {
                if (CollectionUtils.isEmpty(examReportCourseMarksColumn.getExamDimensionObtainedValuesList())) {
                    continue;
                }
                if(reportType.equalsIgnoreCase("ANNUAL")) {
                    if (examReportCourseMarksColumn.getId().equalsIgnoreCase("HY_AN_Total") && examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.SUM) {
                        ExamDimensionObtainedValues examDimensionObtainedValues = examReportCourseMarksColumn
                                .getExamDimensionObtainedValuesList().get(0);
                        String total = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedMarks() == null
                                        ? null : examDimensionObtainedValues.getObtainedMarks(),
                                examDimensionObtainedValues.getAttendanceStatus());
                        String finalGrade = StringUtils.isEmpty(total)? "-" : total;
                        row.add(new CellData(getParagraph(finalGrade, EColorUtils.darkBlueColorList), marksCellLayoutSetup));
                    }
                    if (examReportCourseMarksColumn.getId().equalsIgnoreCase("HY_AN_TotalGrade") && examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.GRADE) {

                        ExamDimensionObtainedValues examDimensionObtainedValues = examReportCourseMarksColumn
                                .getExamDimensionObtainedValuesList().get(0);
                        String grade = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedGrade() == null
                                        ? null : examDimensionObtainedValues.getObtainedGrade().getGradeName(),
                                examDimensionObtainedValues.getAttendanceStatus());
                        String finalGrade = StringUtils.isEmpty(grade)? "-" : grade;
                        row.add(new CellData(getParagraph(finalGrade, EColorUtils.darkBlueColorList), marksCellLayoutSetup));
                        break;
                    }
                }

                if (examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.GRADE) {
                    ExamDimensionObtainedValues examDimensionObtainedValues = examReportCourseMarksColumn
                            .getExamDimensionObtainedValuesList().get(0);
                    String grade = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedGrade() == null
                                    ? null : examDimensionObtainedValues.getObtainedGrade().getGradeName(),
                            examDimensionObtainedValues.getAttendanceStatus());
                    String finalGrade = StringUtils.isEmpty(grade)? "-" : grade;
                    row.add(new CellData(getParagraph(finalGrade, EColorUtils.darkBlueColorList), marksCellLayoutSetup));
                    break;
                }
            }

            row.add(new CellData(getParagraph("", EColorUtils.darkBlueColorList), emptyCellLayoutSetup));
            if (index < coscholasticCourseList.size()) {
                row.add(new CellData(getParagraph(coscholasticCourseList.get(index).getKey(), EColorUtils.darkBlueColorList),
                        courseHeaderCellLayoutSetup));
                row.add(new CellData(getParagraph(coscholasticCourseList.get(index++).getValue(), EColorUtils.darkBlueColorList),
                        marksCellLayoutSetup));
            } else {
                row.add(new CellData(getParagraph("", EColorUtils.darkBlueColorList), emptyCellLayoutSetup));
                row.add(new CellData(getParagraph("", EColorUtils.darkBlueColorList), emptyCellLayoutSetup));
            }
            addRow(headerTable, documentLayoutSetup, row);
        }

        if (index < coscholasticCourseList.size()) {
            while (index < coscholasticCourseList.size()) {
                List<CellData> remainingRows = new ArrayList<>();
                remainingRows.add(new CellData(getParagraph("", EColorUtils.darkBlueColorList), emptyCellLayoutSetup));
                remainingRows.add(new CellData(getParagraph("", EColorUtils.darkBlueColorList), emptyCellLayoutSetup));
                if(reportType.equalsIgnoreCase("ANNUAL")) {
                    remainingRows.add(new CellData(getParagraph("", EColorUtils.darkBlueColorList), emptyCellLayoutSetup));
                    remainingRows.add(new CellData(getParagraph("", EColorUtils.darkBlueColorList), emptyCellLayoutSetup));
                }
                remainingRows.add(new CellData(getParagraph("", EColorUtils.darkBlueColorList), emptyCellLayoutSetup));
                remainingRows.add(new CellData(getParagraph("", EColorUtils.darkBlueColorList), emptyCellLayoutSetup));
                remainingRows.add(new CellData(getParagraph(coscholasticCourseList.get(index).getKey(), EColorUtils.darkBlueColorList),
                        courseHeaderCellLayoutSetup));
                remainingRows.add(new CellData(getParagraph(coscholasticCourseList.get(index++).getValue(), EColorUtils.darkBlueColorList), marksCellLayoutSetup));
                addRow(headerTable, documentLayoutSetup, remainingRows);
            }
        }

        /**
         * only showing sum in last column
         */
//        if (showTotal) {
//            List<CellData> row = new ArrayList<>();
//            row.add(new CellData(getParagraph("Total", EColorUtils.darkBlueColorList), courseCellLayoutSetup, 1,
//                    columnWidths.length - examReportCoscholasticMarksGrid.getExamReportTotalMarksColumns()
//                            .get(examReportCoscholasticMarksGrid.getExamReportTotalMarksColumns().size() - 1)
//                            .getExamDimensionObtainedValuesList().size()));
//
//            for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportCoscholasticMarksGrid
//                    .getExamReportTotalMarksColumns()
//                    .get(examReportCoscholasticMarksGrid.getExamReportTotalMarksColumns().size() - 1)
//                    .getExamDimensionObtainedValuesList()) {
//                String value = ExamGrade.emptyGrade(examDimensionObtainedValues.getObtainedGrade()) ? "-"
//                        : examDimensionObtainedValues.getObtainedGrade().getGradeName();
//                row.add(new CellData(getParagraph(value, EColorUtils.darkBlueColorList), marksCellLayoutSetup));
//            }
//
//            addRow(headerTable, documentLayoutSetup, row);
//        }

        document.add(headerTable);
    }

    private Map<String, String> getCoscholasticCourseKeyValueMap(ExamReportMarksGrid examReportCoscholasticMarksGrid) {
        Map<String, String> coscholasticCourseKeyValueMap = new HashMap<>();
        if(CollectionUtils.isEmpty(examReportCoscholasticMarksGrid.getExamReportCourseMarksRows())) {
            return coscholasticCourseKeyValueMap;
        }
        for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportCoscholasticMarksGrid.getExamReportCourseMarksRows()) {
            if (CollectionUtils.isEmpty(examReportCourseMarksRow.getExamReportCourseMarksColumns())) {
                coscholasticCourseKeyValueMap.put(examReportCourseMarksRow.getCourse().getCourseName(), "");
                continue;
            }

            for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportCourseMarksRow
                    .getExamReportCourseMarksColumns()) {

                if (CollectionUtils.isEmpty(examReportCourseMarksColumn.getExamDimensionObtainedValuesList())) {
                    coscholasticCourseKeyValueMap.put(examReportCourseMarksRow.getCourse().getCourseName(), "");
                    continue;
                }
                ExamDimensionObtainedValues examDimensionObtainedValues = examReportCourseMarksColumn
                        .getExamDimensionObtainedValuesList().get(0);
                String grade = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedGrade() == null
                                ? null : examDimensionObtainedValues.getObtainedGrade().getGradeName(),
                        examDimensionObtainedValues.getAttendanceStatus());
                String finalGrade = StringUtils.isEmpty(grade)? "-" : grade;
                coscholasticCourseKeyValueMap.put(examReportCourseMarksRow.getCourse().getCourseName(), finalGrade);
            }
        }
        return coscholasticCourseKeyValueMap;
    }

    @Override
    public DocumentOutput generateClassReport(Institute institute, String reportType,
                                              List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput,
                    CollectionUtils.isEmpty(examReportDataList) ? null : examReportDataList.get(0).getStandardMetaData().getStandardId());

            sortClassExamReports(examReportDataList);

            int pageNumber = 1;
            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();

            Set<UUID> studentIdSet = new HashSet<>();
            for(ExamReportData examReportData : examReportDataList) {
                studentIdSet.add(examReportData.getStudentLite().getStudentId());
            }
            Integer startDate = examReportDataList.get(0).getExamReportStructure().getExamReportStructureMetaData().getAttendanceStartDate();
            Integer endDate = examReportDataList.get(0).getExamReportStructure().getExamReportStructureMetaData().getAttendanceEndDate();
            if(startDate != null) {
                startDate = DateUtils.getDayStart(startDate, DateUtils.DEFAULT_TIMEZONE);
            }
            if(endDate != null) {
                endDate = DateUtils.getDayEnd(endDate, DateUtils.DEFAULT_TIMEZONE);
            }
            Map<UUID, Map<AttendanceStatus, Integer>> attendanceStatusIntegerMap = new HashMap<>();
            if(startDate != null && endDate != null) {
                attendanceStatusIntegerMap =
                        studentManager.getStudentAttendanceDetails(institute.getInstituteId(),
                                examReportDataList.get(0).getStandardMetaData().getAcademicSessionId(),
                                studentIdSet, startDate, endDate);
            }

            for (ExamReportData examReportData : examReportDataList) {
                generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                        pageNumber, boldFont, regularFont, attendanceStatusIntegerMap);

                if (pageNumber != examReportDataList.size()) {
                    examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;
            }

            examReportCardLayoutData.getDocument().close();
            return documentOutput;

        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
                                                    String documentName, StudentManager studentManager, int studentPerPage) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
            Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

            PdfFont regularFont = getRegularFont();
            PdfFont boldFont = getRegularBoldFont();

            CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
            marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
                    .setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

            CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
                    .setTextAlignment(TextAlignment.LEFT);

            String instituteName = institute.getInstituteName().toUpperCase();
            String sessionName = "";
            if(!CollectionUtils.isEmpty(examReportDataList)) {
                sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
            }
            generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
                    sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

            sortClassExamReports(examReportDataList);
            int pageNumber = 1;
            for (ExamReportData examReportData : examReportDataList) {
                generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
                generateScholasticMarksGrid(document,
                        documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
                        "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
                        null, "#ff0000");
                generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
                if (pageNumber % studentPerPage == 0) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                    generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
                            marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
                } else {
                    addBlankLine(document, false, 1);
                }
                pageNumber++;
            }
            document.close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
                    reportType, e);
        }
        return null;
    }
}
