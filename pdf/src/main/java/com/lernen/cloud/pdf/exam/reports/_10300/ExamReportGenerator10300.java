package com.lernen.cloud.pdf.exam.reports._10300;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportCourseMarksRow;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;


/**
 *
 * <AUTHOR>
 *
 */
public class ExamReportGenerator10300 extends ExamReportGenerator implements IExamReportCardGenerator {

    public ExamReportGenerator10300(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }
    public static final String DATE_FORMAT = "dd/MM/yyyy";


    private static final Logger logger = LogManager.getLogger(com.lernen.cloud.pdf.exam.reports._10300.ExamReportGenerator10300.class);
    public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
    public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
    public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
    public static final float SQUARE_BORDER_MARGIN = 12f;
    protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";
    protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";
    protected static final String SCHOOL_NAME = "DAYANAND PUBLIC SENIOR SECONDARY SCHOOL";
    public static final float LOGO_WIDTH = 75f;
    public static final float LOGO_HEIGHT = 75f;

    private static final String GRADE_A = "Outstanding performance and has extraordinary thinking.";
    private static final String GRADE_B = "Good and Gracefully takes the task and has tendency to do better.";
    private static final String GRADE_C = "Well behaved, has the capacity to work hard in order to reach heights.";
    private static final String GRADE_D = "Needs to get serious towards studies and maintain sense of humour.";
    private static final String GRADE_E = "Needs to be very attentive and work hard in order to get promoted.";
    

    @Override
    public DocumentOutput generateReport(Institute institute, Student student, String reportType,
                                         ExamReportData examReportData, String documentName, StudentManager studentManager) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
            Document document = examReportCardLayoutData.getDocument();
            DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();
            generateStudentReport(institute.getInstituteId(), studentManager, document, documentLayoutSetup, institute, examReportData.getStudentLite(), reportType, examReportData, examReportCardLayoutData, boldFont, regularFont, 1);
            examReportCardLayoutData.getDocument().close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, student {}, reportType {} ",
                    institute.getInstituteId(), student.getStudentId(), reportType, e);
        }
        return null;
    }



    @Override
    public DocumentOutput generateClassReport(Institute institute, String reportType,
                                              List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

            sortClassExamReports(examReportDataList);
            Document document = examReportCardLayoutData.getDocument();
            DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
            int pageNumber = 1;
            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();
            for (ExamReportData examReportData : examReportDataList) {
                generateStudentReport(institute.getInstituteId(), studentManager, document, documentLayoutSetup, institute, examReportData.getStudentLite(), reportType, examReportData, examReportCardLayoutData, boldFont, regularFont, pageNumber);

                if (pageNumber != examReportDataList.size()) {
                    examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;
            }

            examReportCardLayoutData.getDocument().close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
                    reportType, e);
        }
        return null;

    }

    private void generateStudentReport(int instituteId, StudentManager studentManager, Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute, StudentLite studentLite, String reportType,
                                       ExamReportData examReportData, ExamReportCardLayoutData examReportCardLayoutData , PdfFont boldFont, PdfFont regularFont, int pageNumber)
            throws IOException {

        float watermarkImageHeightWidth = 400f;

        generateWatermark(examReportCardLayoutData, instituteId, watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20,
                watermarkImageHeightWidth / 2);
        addBlankLine(examReportCardLayoutData, false, 1);
        generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
                examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.87f);
        addBlankLine(examReportCardLayoutData, false, 1);
        generateStudentInformation(instituteId, studentManager, examReportCardLayoutData, studentLite, examReportData);
        addBlankLine(examReportCardLayoutData, false, 1);
        generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType, GridConfigs.forSkipTotalRow(),
                "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType));
        addBlankLine(examReportCardLayoutData, false, 1);
        generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
                examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "Co-Scholastic Subjects",
                getCoScholasticMarksGridSubjectWidth(reportType));
        addBlankLine(examReportCardLayoutData, false, 1);
        generateGradeBox(document, documentLayoutSetup, examReportData, examReportCardLayoutData.getContentFontSize(), examReportCardLayoutData.getDefaultBorderWidth(), boldFont, regularFont);
        generateResultSummary(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
                examReportCardLayoutData.getContentFontSize(),examReportCardLayoutData.getBoldFont(),examReportCardLayoutData.getRegularFont(), examReportData, reportType);
        generateRemarksSection(examReportCardLayoutData, examReportData , regularFont, boldFont);
        generateSignatureBox(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
                examReportCardLayoutData.getContentFontSize(), examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, examReportData, boldFont, regularFont);
        generateBorderLayout(examReportCardLayoutData, pageNumber);
    }

    private void generateCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
                                                   ExamReportData examReportData, String reportType, GridConfigs gridConfigs, String subjectColumnTitle,
                                                   float subjectColumnWidth) throws IOException {
        Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
        generateScholasticMarksGrid(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
                examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs, subjectColumnTitle,
                subjectColumnWidth, nonAdditionalSubjects, null);

    }

    protected float getScholasticMarksGridSubjectWidth(String reportType) {

        return 0.2f;
    }

    protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
        return 0.25f;
    }

    protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
            throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
        float contentFontSize = 12f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        float logoWidth = 75f;
        float logoHeight = 75f;

        int instituteId = institute.getInstituteId();
        return new ExamReportCardLayoutData(document, documentLayoutSetup, getRegularFont(), getRegularBoldFont(), contentFontSize,
                defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
    }

    protected void generateMetaDataLayout(int instituteId, ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
                                          StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData) throws IOException {

        generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
                examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.87f);
        generateStudentInformation(instituteId, studentManager, examReportCardLayoutData, studentLite, examReportData);
        generateBorderLayout(examReportCardLayoutData);
    }

    @Override
    public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
        return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
    }

    protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
                                  Institute institute, String reportType, float offsetX, float offsetY) throws IOException {

        generateDynamicImageProvider(examReportCardLayoutData, offsetX,
                examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.835f - 18f,1.3f, 1.3f, institute.getInstituteId(),
                InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

        Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 2);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(examReportCardLayoutData.getRegularFont()).setFontSize(10f);

        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(
                new CellData(getParagraph("Reg No : 911/JAIPUR/2007-08"),
                        cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT).setPaddingLeft(12f).setPdfFont(examReportCardLayoutData.getBoldFont())),
                new CellData(getParagraph("Board Affiliation No : 1122674"),
                        cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT).setPaddingRight(12f).setPdfFont(examReportCardLayoutData.getBoldFont()))));

        examReportCardLayoutData.getDocument().add(table);

        int singleContentColumn = 1;
        table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

        cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(examReportCardLayoutData.getRegularFont()).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(SCHOOL_NAME)),
                cellLayoutSetup.copy().setFontSize(21.5f).setPdfFont(examReportCardLayoutData.getBoldFont()));
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
                cellLayoutSetup.copy().setFontSize(13f).setPdfFont(examReportCardLayoutData.getRegularFont()));
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
                cellLayoutSetup.copy().setFontSize(13f).setPdfFont(examReportCardLayoutData.getRegularFont()));
        examReportCardLayoutData.getDocument().add(table);
        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

        singleContentColumn = 1;
        table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

        cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(examReportCardLayoutData.getRegularFont()).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);

        String headerExamTitle = "PROGRESS REPORT ";
        headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()),
                cellLayoutSetup.copy().setFontSize(13f).setPdfFont(examReportCardLayoutData.getBoldFont()));
        examReportCardLayoutData.getDocument().add(table);
        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

    }

    protected void generateStudentInformation(int instituteId, StudentManager studentManager, ExamReportCardLayoutData examReportCardLayoutData,
                                              StudentLite studentLite, ExamReportData examReportData) throws IOException {

        PdfFont boldFont = examReportCardLayoutData.getBoldFont();
        Document document = examReportCardLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        float contentFontSize = examReportCardLayoutData.getContentFontSize();

        byte[] studentImage = getStudentImage(instituteId, studentLite.getStudentId(), studentManager);
        if (studentImage != null) {
            generateImage(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), studentImage, LOGO_WIDTH + 15f, LOGO_HEIGHT + 15f,
                    examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 40f - LOGO_WIDTH,
                    examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.835f - 15f);
        }

        Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.1f, 0.5f });

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

        CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
        CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
        CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

        Paragraph studentName = getKeyValueParagraph("STUDENT NAME : ", studentLite.getName().toUpperCase(), boldFont, boldFont);
        Paragraph fatherName = getKeyValueParagraph("FATHER NAME : ", studentLite.getFathersName().toUpperCase(), boldFont, boldFont);
        Paragraph dob = getKeyValueParagraph("DOB : ",
                studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
                        : DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT_NUMBER, User.DFAULT_TIMEZONE),  boldFont, boldFont);
        Paragraph motherName = getKeyValueParagraph("MOTHER NAME : ", studentLite.getMothersName().toUpperCase(), boldFont, boldFont);
        Paragraph admissionNumber = getKeyValueParagraph("SR. No. : ", studentLite.getAdmissionNumber(), boldFont, boldFont);
        Paragraph classValue = getKeyValueParagraph("CLASS : ",
                studentLite.getStudentSessionData().getStandardNameWithSection().toUpperCase(),  boldFont, boldFont);
        Paragraph rollNumber = getKeyValueParagraph("ROLL NUMBER : ", studentLite.getStudentSessionData().getRollNumber().toUpperCase(), boldFont, boldFont);
//        Paragraph resultDeclarationDate = getKeyValueParagraph("Result Declaration Date : " , (examReportData.getDateOfResultDeclaration() == null ? "" :
//                        DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration())), boldFont, boldFont);


        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(fatherName, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(dob, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(motherName, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(classValue, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(rollNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData("", thirdCellLayoutSetup)));

        document.add(table);

    }

    protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData) {
        generateBorderLayout(examReportCardLayoutData, 1);
    }

    protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
        canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
                documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
                documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
        canvas.stroke();
        canvas.setLineWidth(1f);
        canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
                documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
                documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
        canvas.stroke();
        canvas.setLineWidth(1f);
    }

    protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
                                         float contentFontSize,PdfFont boldFont, PdfFont regularFont, ExamReportData examReportData, String reportType) throws IOException {



        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setPaddingTop(2f).setPaddingBottom(2f);

        Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

        Paragraph noOfMeetings = getKeyValueParagraph("Number of Meetings : ", examReportData.getTotalWorkingDays() == null ? ""
                : String.valueOf(examReportData.getTotalWorkingDays()),  boldFont, boldFont);
        Paragraph noOfPresent = getKeyValueParagraph("Number of Presents : ", examReportData.getTotalAttendedDays() == null ? ""
                : String.valueOf(examReportData.getTotalAttendedDays()),  boldFont, boldFont);

        Paragraph result = getKeyValueParagraph("Result : ",examReportData.getExamResultStatus().getDisplayName(),
                boldFont, boldFont);

        Paragraph promotedClass = getKeyValueParagraph("Promoted to Class: ",
                examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName(), boldFont, boldFont);
        Paragraph obtainedMarks = getKeyValueParagraph("Obtained Marks : ",
                examReportData.getTotalObtainedMarks() == null ? "-"
                        : examReportData.getTotalObtainedMarks() * 10 % 10 == 0
                        ? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
                        : String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d),  boldFont, boldFont);
        Paragraph totalMarks = getKeyValueParagraph("Total Marks : ", examReportData.getTotalMaxMarks() == null ? "-"
                : String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10),  boldFont, boldFont);
        Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
                : String.valueOf(Math.round(examReportData.getPercentage() * 100) / 100d) + "%",  boldFont, boldFont);

        Paragraph rankParagraph = new Paragraph(EMPTY_TEXT);
        int rank = examReportData.getRank() == null ? 11 : examReportData.getRank();
        if(rank <= 10) {
            rankParagraph = getKeyValueParagraph("Rank : ",
                    examReportData.getRank() == null ? "-" : String.valueOf(examReportData.getRank()));
        }

        Paragraph grade = getKeyValueParagraph("Grade : ",
                examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName());

        Paragraph resultDeclarationDate = getKeyValueParagraph("Result Declaration Date : " , (examReportData.getDateOfResultDeclaration() == null ? "" :
                DateUtils.getDefaultTimezoneFormattedDate(examReportData.getDateOfResultDeclaration(), DATE_FORMAT)), boldFont, boldFont);

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(noOfMeetings, cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(noOfPresent, cellLayoutSetup)));

        if (reportType.equalsIgnoreCase("ANNUAL")) {
            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(promotedClass, cellLayoutSetup)));
        }

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks, cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage, cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(grade, cellLayoutSetup)));
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(resultDeclarationDate, cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(rankParagraph, cellLayoutSetup)));

        document.add(table);

    }

    protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
                                          ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont) throws IOException {

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(examReportCardLayoutData.getBoldFont()).setFontSize(examReportCardLayoutData.getContentFontSize())
                .setTextAlignment(TextAlignment.LEFT);

        Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 1);
        Paragraph remarks = getKeyValueParagraph("Remarks: ", !StringUtils.isBlank(examReportData.getRemarks())
                        ? examReportData.getRemarks() : examReportData.getTotalGrade() == null ? "" :
                        StringUtils.isEmpty(getRemarks(examReportData.getTotalGrade().getGradeName()))
                                ? "" : getRemarks(examReportData.getTotalGrade().getGradeName()), boldFont, boldFont);

        addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(remarks),
                cellLayoutSetup);

        examReportCardLayoutData.getDocument().add(remarksTable);
    }

    private String getRemarks(String grade) {
        switch (grade) {
            case "A":
                return GRADE_A;
            case "B":
                return GRADE_B;
            case "C":
                return GRADE_C;
            case "D":
                return GRADE_D;
            case "E":
                return GRADE_E;
            default:
                return null;

        }
    }



    protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
                                        float contentFontSize, float defaultBorderWidth, int pageNumber, ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {
//        PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
//        canvas.moveTo(12, 94);
//        canvas.lineTo(583, 94);
//        canvas.setLineWidth(.5f);
//        canvas.closePathStroke();
        int singleContentColumn = 3;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.CENTER);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Class Teacher"), getParagraph("Exam Incharge"),
                getParagraph("Principal")), signatureCellLayoutSetup);
        table.setFixedPosition(30f, 15, documentLayoutSetup.getPageSize().getWidth() - 100f);
        document.add(table);

    }

    private void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup, ExamReportData examReportData, float contentFontSize,
                                  float defaultBorderWidth, PdfFont boldFont, PdfFont regularFont) throws IOException {

        Map<CourseType, List<ExamGrade>> gradesList = examReportData.getCourseTypeExamGrades();
        List<ExamGrade> scholasticGrades = gradesList.get(CourseType.SCHOLASTIC);

        boolean hasScholastic = !CollectionUtils.isEmpty(scholasticGrades);
        boolean hasCoScholastic = false;

        if (!hasScholastic && !hasCoScholastic) {
            return;
        }
        float yAxisforMarksRange = hasScholastic && hasCoScholastic ? 130f : 74f;

        float yOffset = hasCoScholastic ? 109f : 53f;
        Table table = getPDFTable(documentLayoutSetup, 1);
        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
                .setTextAlignment(TextAlignment.CENTER);
//        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("MARKS RANGE")), signatureCellLayoutSetup);
//        table.setFixedPosition(10f, yAxisforMarksRange, documentLayoutSetup.getPageSize().getWidth());
        document.add(table);

        CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
        marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 2)
                .setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);


        if (hasScholastic) {
            generateGradeTable(document, documentLayoutSetup, "Scholastic Grading", scholasticGrades, marksCellLayoutSetup,
                    contentFontSize, defaultBorderWidth, boldFont, regularFont, yOffset, CourseType.SCHOLASTIC);
        }
    }

    private void generateGradeTable(Document document, DocumentLayoutSetup documentLayoutSetup, String title, List<ExamGrade> grades, CellLayoutSetup marksCellLayoutSetup,
                                    float contentFontSize, float defaultBorderWidth, PdfFont boldFont, PdfFont regularFont, float yPosition, CourseType courseType) throws IOException {

        Table table = getPDFTable(documentLayoutSetup, 1);
        CellLayoutSetup titleHeadLayoutsSetup = new CellLayoutSetup();

        if(CourseType.SCHOLASTIC == courseType){
            titleHeadLayoutsSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 2)
                    .setBorderTop(new SolidBorder(defaultBorderWidth)).setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderLeft(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
        }
        if(CourseType.COSCHOLASTIC == courseType){
            titleHeadLayoutsSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 2)
                    .setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderLeft(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
        }

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(title)), titleHeadLayoutsSetup.copy().setPdfFont(boldFont));
//        table.setFixedPosition(25f, yPosition, documentLayoutSetup.getPageSize().getWidth() - 100f);
        document.add(table);

        int columnCount = grades.size() + 1;
        float firstColumnWidth = 0.16f;
        float remainingWidth = 1f - firstColumnWidth;
        float columnWidth = remainingWidth / (columnCount - 1);

        float[] columnWidths = new float[columnCount];
        columnWidths[0] = firstColumnWidth;
        Arrays.fill(columnWidths, 1, columnWidths.length, columnWidth);

        Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
        List<CellData> headerList = new ArrayList<>();
        List<CellData> gradeList = new ArrayList<>();

        headerList.add(new CellData("MARKS RANGE", marksCellLayoutSetup.copy().setPdfFont(boldFont)));
        gradeList.add(new CellData("GRADE", marksCellLayoutSetup.copy().setPdfFont(boldFont)));

        for (ExamGrade grade : grades) {
            headerList.add(new CellData(grade.getRangeDisplayName(), marksCellLayoutSetup));
            gradeList.add(new CellData(grade.getGradeName(), marksCellLayoutSetup));
        }

        addRow(headerTable, documentLayoutSetup, headerList);
        addRow(headerTable, documentLayoutSetup, gradeList);
//        headerTable.setFixedPosition(25f, yPosition - 37f, documentLayoutSetup.getPageSize().getWidth() - 100f);
        document.add(headerTable);
    }

    protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
        Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
        for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
                .get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
            if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
                nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
            }
        }
        return nonAdditionalSubjects;
    }

    protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
        Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

            @Override
            public int compare(ExamReportData e1, ExamReportData e2) {
                StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

                StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

                if (section1 != null && section2 != null) {
                    int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
                    if (sectionCompare != 0) {
                        return sectionCompare;
                    }
                }

                return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
            }
        });
    }

    @Override
    public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
                                                    String documentName, StudentManager studentManager, int studentPerPage) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
            Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
            float logoWidth = 75f;
            float logoHeight = 75f;
            PdfFont boldFont = getRegularBoldFont();
            PdfFont regularFont = getRegularFont();
            int instituteId = institute.getInstituteId();
            ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, DEFAULT_FONT_SIZE,
                    DEFAULT_BORDER_WIDTH, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));

            CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
            marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
                    .setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

            CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
                    .setTextAlignment(TextAlignment.LEFT);

            String instituteName = institute.getInstituteName().toUpperCase();

            String sessionName = "";
            if(!CollectionUtils.isEmpty(examReportDataList)) {
                sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
            }
            generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
                    sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

            sortClassExamReports(examReportDataList);
            int pageNumber = 1;
            for (ExamReportData examReportData : examReportDataList) {
                generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
                generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType, GridConfigs.forOnlyObtainedTotalRow(),
                        "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType));
                generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
                if (pageNumber % studentPerPage == 0) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                    generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
                            marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
                } else {
                    addBlankLine(document, false, 1);
                }
                pageNumber++;
            }
            document.close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
                    reportType, e);
        }
        return null;
    }
}


