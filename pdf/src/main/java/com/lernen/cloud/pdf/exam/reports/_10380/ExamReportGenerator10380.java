package com.lernen.cloud.pdf.exam.reports._10380;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportCourseMarksRow;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.ExamResultStatus;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import com.lernen.cloud.pdf.exam.reports._10180.ExamReportGenerator10180;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

public class ExamReportGenerator10380  extends ExamReportGenerator implements IExamReportCardGenerator {
	public ExamReportGenerator10380(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator10180.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;

	protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";
	protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";

	//	dark purple 1b4a74	-		27, 74, 116
	protected static final int darkPurpleR = 27;
	protected static final int darkPurpleG = 74;
	protected static final int darkPurpleB = 116;
	protected static final DeviceRgb darkPurpleDeviceRgb = new DeviceRgb(darkPurpleR, darkPurpleG, darkPurpleB);

	// Red color - #FF0000 - 255, 0, 0
	protected static final int redR = 255;
	protected static final int redG = 0;
	protected static final int redB = 0;
	protected static final DeviceRgb redDeviceRgb = new DeviceRgb(redR, redG, redB);

	// Blue color - #3a62bd - 58, 98, 189
	protected static final int blueR = 58;
	protected static final int blueG = 98;
	protected static final int blueB = 189;
	protected static final DeviceRgb blueDeviceRgb = new DeviceRgb(blueR, blueG, blueB);

	private static final  String GRADE_A = "Outstanding performance and has extraordinary thinking.";
	//	private static final  String GRADE_A2 = "Excellent effort, follow deadlines and maintain decency.";
	private static final  String GRADE_B = "Gracefully takes the task and has tendency to do better.";
	//	private static final  String GRADE_B2 = "Well behaved, has the capacity to work hard in order to reach heights.";
	private static final  String GRADE_C = "Well behaved, has the capacity to work hard in order to reach heights.";
	//	private static final  String GRADE_C2 = "Average performance but innovative.";
	private static final  String GRADE_D = "Needs to get serious towards studies and maintain sense of humour.";
	protected static final String SCHOLASTIC_EXAM_DESCRIPTION = "FA = Formative Assessment, HY= Half Yearly";
//	private static final  String GRADE_E = "Needs to be very attentive and work hard in order to get promoted.";

//	Red : rgb(204, 0, 0)
//	Purple : rgb(57, 65, 158)
//	Blue : rgb(1, 155, 248)

	private static Map<UUID, String> PROMOTE_CLASS_MAP = new HashMap<>();

	static {
		PROMOTE_CLASS_MAP.put(UUID.fromString("eb5f4d80-98f8-444e-b020-f2e76b65145e"), "IV EM");
		PROMOTE_CLASS_MAP.put(UUID.fromString("6ce187b8-e7fb-400f-92e4-088f2269b9b0"), "IV HM");
		PROMOTE_CLASS_MAP.put(UUID.fromString("199025e3-1563-4a60-9dc7-b43a99156a8b"), "V EM");
		PROMOTE_CLASS_MAP.put(UUID.fromString("69c306c8-0f96-447d-9a38-2b8f796b1d78"), "V HM");
		PROMOTE_CLASS_MAP.put(UUID.fromString("b363f1ce-e7e2-43ec-a48c-6bfc68eaf304"), "VI EM");
		PROMOTE_CLASS_MAP.put(UUID.fromString("07c4c023-26cb-4154-95f1-6a326a2588b8"), "VI HM");
		PROMOTE_CLASS_MAP.put(UUID.fromString("06ebfcff-2705-435c-a65c-0fd193d197b8"), "VII EM");
		PROMOTE_CLASS_MAP.put(UUID.fromString("b564b148-ddaa-4690-989b-************"), "VII HM");
		PROMOTE_CLASS_MAP.put(UUID.fromString("b68a9614-13f1-45bb-96e9-2a8c9f5f99b3"), "VIII EM");
		PROMOTE_CLASS_MAP.put(UUID.fromString("82619ba7-ee7c-4f30-8788-dbbe3cfd0562"), "VIII HM");
		PROMOTE_CLASS_MAP.put(UUID.fromString("a89aa910-67c7-4f8a-8674-74b757501cf2"), "IX EM");
		PROMOTE_CLASS_MAP.put(UUID.fromString("b5ff7a0a-6fa4-4189-9d84-54b34896c7a9"), "IX HM");
		PROMOTE_CLASS_MAP.put(UUID.fromString("8a37803f-3bbb-4b6c-804f-d92b321d109c"), "X EM");
		PROMOTE_CLASS_MAP.put(UUID.fromString("5462dfec-782a-4154-9ead-9f10dc2fa50a"), "X HM");
		PROMOTE_CLASS_MAP.put(UUID.fromString("e4e606b7-0fb6-4bea-9a1c-d4104b69919c"), "-");
		PROMOTE_CLASS_MAP.put(UUID.fromString("b4bb2dbb-f515-4702-bfaf-a29b11e66347"), "-");
	}


	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
										 ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();
			Document document = examReportCardLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
			generateStudentReport(document, documentLayoutSetup, institute, examReportData.getStudentLite(), reportType, examReportData, examReportCardLayoutData, 1,
					regularFont, boldFont, studentManager);
			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
											  List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			Document document = examReportCardLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
			sortClassExamReports(examReportDataList);
			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReport(document, documentLayoutSetup, institute, examReportData.getStudentLite(), reportType, examReportData,
						examReportCardLayoutData, pageNumber, regularFont, boldFont, studentManager);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}
			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;

	}

	private void generateStudentReport(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute, StudentLite studentLite, String reportType,
									   ExamReportData examReportData, ExamReportCardLayoutData examReportCardLayoutData, int pageNumber,
									   PdfFont regularFont, PdfFont boldFont, StudentManager studentManager) throws IOException {

		float watermarkImageHeightWidth = 400f;
		//Institute watermark
		int instituteId = institute.getInstituteId();
		generateWatermark(examReportCardLayoutData, instituteId, watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20,
				watermarkImageHeightWidth / 2);
		addBlankLine(examReportCardLayoutData, false, 1);
		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 30f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.86f, regularFont, boldFont);
		addBlankLine(examReportCardLayoutData, false, 2);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, regularFont, boldFont, institute.getInstituteId(), studentManager);
		addBlankLine(examReportCardLayoutData, false, 1);
		generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType, GridConfigs.forOnlyObtainedTotalRow(),
				"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType));
		
		String descriptionText = SCHOLASTIC_EXAM_DESCRIPTION;
		generateScholasticExamDescription(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportData, boldFont, regularFont, descriptionText);
				
		addBlankLine(examReportCardLayoutData, false, 1);
		addBlankLine(examReportCardLayoutData, false, 1);
		generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "Co-Scholastic Subjects",
				getCoScholasticMarksGridSubjectWidth(reportType));
		addBlankLine(examReportCardLayoutData, false, 1);
		generateResultSummary(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examReportData, reportType, regularFont, boldFont);
//		generateRemarksSection(examReportCardLayoutData, examReportData, regularFont, boldFont);
		generateSignatureBox(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),examReportData,
				examReportCardLayoutData.getContentFontSize(), examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, regularFont, boldFont);
		generateBorderLayout(examReportCardLayoutData, pageNumber);
	}

	private void generateCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
												   ExamReportData examReportData, String reportType, GridConfigs gridConfigs, String subjectColumnTitle,
												   float subjectColumnWidth) throws IOException {
		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, nonAdditionalSubjects, null);

	}

	protected float getScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.2f;
		}
		return 0.2f;
	}

	protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.5f;
		}
		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			return 0.4f;
		}
		return 0.3f;
	}

	protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
		float contentFontSize, ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont, String descriptionText) throws IOException {
		Text descTitle = new Text("Description: " + descriptionText).setFontSize(contentFontSize - 4);
		Paragraph desc = new Paragraph();
		desc.add(descTitle);
		document.add(desc);
	}

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
		float contentFontSize = 12f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
		float logoWidth = 80f;
		float logoHeight = 80f;

		int instituteId = institute.getInstituteId();
		instituteId = 10180;
		return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
				defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
	}

//	protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
//			StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
//			PdfFont regularFont, PdfFont boldFont) throws IOException {
//
//		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 15f,
//				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.87f,
//				regularFont, boldFont);
//		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, regularFont, boldFont, institute.getInstituteId(), studentManager);
//		generateBorderLayout(examReportCardLayoutData);
//	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
								  Institute institute, String reportType, float offsetX, float offsetY, PdfFont regularFont, PdfFont boldFont) throws IOException {

		generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		int singleContentColumn = 1;
		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName())
						.setFontColor(Color.convertRgbToCmyk(blueDeviceRgb))),
				cellLayoutSetup.copy().setFontSize(18f));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph("DISE CODE :- 08040607208 , Reg No:- 103/CHURU/2012-13")
						.setFontColor(Color.convertRgbToCmyk(redDeviceRgb))),
				cellLayoutSetup.copy().setFontSize(12f));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(capitalize(institute.getLetterHeadLine1()))
						.setFontColor(Color.convertRgbToCmyk(redDeviceRgb))),
				cellLayoutSetup.copy().setFontSize(12f));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(capitalize(institute.getLetterHeadLine2()))
						.setFontColor(Color.convertRgbToCmyk(redDeviceRgb))),
				cellLayoutSetup.copy().setFontSize(12f).setPdfFont(regularFont));

		String headerExamTitle = "PROGRESS REPORT ";
		headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()),
				cellLayoutSetup.copy().setFontSize(12f).setPdfFont(boldFont));
		examReportCardLayoutData.getDocument().add(table);

	}

	private String capitalize(String str){
		String words[] = str.split("\\s+");
		StringBuilder stringbuilder = new StringBuilder();
		for(String word : words) {
			stringbuilder.append(Character.toUpperCase(word.charAt(0))).append(word.substring(1).toLowerCase()).append(" ");
		}
		String res = stringbuilder.toString();
		return res;
	}

	protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
											  StudentLite studentLite, ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont,
											  int instituteId, StudentManager studentManager) throws IOException {

		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize();

		Table table = getPDFTable(documentLayoutSetup, new float[] {0.5f, 0.1f, 0.4f});

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

		String fatherNameVal = StringUtils.isBlank(studentLite.getFathersName()) ? "" : "Mr. " + studentLite.getFathersName();
		Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName(), blueR, blueG, blueB, boldFont, boldFont);

		Paragraph fatherName = getKeyValueParagraph("Father's Name : ", fatherNameVal, blueR, blueG, blueB, boldFont, boldFont);
		Paragraph dob = getKeyValueParagraph("DOB : ", studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
				: DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE), blueR, blueG, blueB, boldFont, boldFont);
		String motherNameVal = StringUtils.isBlank(studentLite.getMothersName()) ? "" : "Mrs. " + studentLite.getMothersName();
		Paragraph motherName = getKeyValueParagraph("Mother's Name : ", motherNameVal, blueR, blueG, blueB, boldFont, boldFont);
		Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ", studentLite.getAdmissionNumber(), blueR, blueG, blueB, boldFont, boldFont);
		Paragraph classValue = getKeyValueParagraph("Class : ",
				studentLite.getStudentSessionData().getStandardNameWithSection(), blueR, blueG, blueB, boldFont, boldFont);
		Paragraph rollNumber = getKeyValueParagraph("Roll Number : ", studentLite.getStudentSessionData().getRollNumber(), blueR, blueG, blueB, boldFont, boldFont);
		Paragraph dateOfResultDeclaration = getKeyValueParagraph("Date Of Result Declaration : ",
				examReportData.getDateOfResultDeclaration() == null ? "-" : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()),
				blueR, blueG, blueB, boldFont, boldFont);

		addRow(table, documentLayoutSetup,
			Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
					new CellData(fatherName, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
			Arrays.asList(new CellData(dob, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
					new CellData(motherName, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
			Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
					new CellData(classValue, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(rollNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(dateOfResultDeclaration, thirdCellLayoutSetup)));

		document.add(table);

	}

//	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData) {
//		generateBorderLayout(examReportCardLayoutData, 1);
//	}

	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
				documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
	}

	protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
										 float contentFontSize, ExamReportData examReportData, String reportType, PdfFont regularFont, PdfFont boldFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
				.setTextAlignment(TextAlignment.LEFT).setPaddingTop(2f).setPaddingBottom(2f);

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		// Paragraph noOfMeetings = getKeyValueParagraph("Number of Meetings : ", examReportData.getTotalWorkingDays() == null ? ""
		// 		: String.valueOf(examReportData.getTotalWorkingDays()), blueR, blueG, blueB, boldFont, boldFont);
		// Paragraph noOfPresent = getKeyValueParagraph("Number of presents : ", examReportData.getTotalAttendedDays() == null ? ""
		// 		: String.valueOf(examReportData.getTotalAttendedDays()), blueR, blueG, blueB, boldFont, boldFont);

		Paragraph rank = null;
		if (examReportData.getRank() != null && examReportData.getRank() > 0 && examReportData.getRank() <= 10) {
			rank = getKeyValueParagraph("Rank : ", String.valueOf(examReportData.getRank()), blueR, blueG, blueB, boldFont, boldFont);
		}

		Paragraph result = getKeyValueParagraph("Result : ", "-", boldFont);
		if(examReportData.getExamResultStatus() != null){
			switch (examReportData.getExamResultStatus()){
				case PASS:
				case PASS_WITH_GRACE:
				case SUPPLEMENTARY:
					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
							2, 48, 32, boldFont, boldFont);
					break;
				case FAIL:
					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
							255, 0, 0, boldFont, boldFont);
					break;
			}
		}

		String promotedClassText = examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName();
        if(PROMOTE_CLASS_MAP.containsKey(examReportData.getStandardMetaData().getStandardId()) && examReportData.getExamResultStatus() != null && (examReportData.getExamResultStatus() == ExamResultStatus.PASS || examReportData.getExamResultStatus() == ExamResultStatus.PASS_WITH_GRACE)){
                promotedClassText = PROMOTE_CLASS_MAP.get(examReportData.getStandardMetaData().getStandardId());
        }

		Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
				promotedClassText, blueR, blueG, blueB, boldFont, boldFont);
		Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
				examReportData.getTotalObtainedMarks() == null ? "-"
						: examReportData.getTotalObtainedMarks() * 10 % 10 == 0
						? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
						: String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d), blueR, blueG, blueB, boldFont, boldFont);
		Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
				: String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10), blueR, blueG, blueB, boldFont, boldFont);
		Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
				: String.valueOf(Math.round(examReportData.getPercentage() * 100) / 100d) + "%", blueR, blueG, blueB, boldFont, boldFont);

		// addRow(table, documentLayoutSetup, Arrays.asList(new CellData(noOfMeetings, cellLayoutSetup),
		// 		new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(noOfPresent, cellLayoutSetup)));

		if (reportType.equalsIgnoreCase("ANNUAL")) {
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
					new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(promotedClass, cellLayoutSetup)));
		}

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));


		System.out.println(examReportData.getTotalGrade());
		Paragraph grade = getKeyValueParagraph("Grade : ",
				examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(),
				blueR, blueG, blueB, boldFont, boldFont);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(grade, cellLayoutSetup)));

		if(rank != null) {
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(rank, cellLayoutSetup),
					new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));
		}

		document.add(table);

	}

	protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
										  ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize())
				.setTextAlignment(TextAlignment.LEFT);

		Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 1);

		Paragraph remarks = getKeyValueParagraph("Remarks: ",
				StringUtils.isBlank(examReportData.getRemarks()) ? examReportData.getTotalGrade() == null ? "" :
						StringUtils.isEmpty(getRemarks(examReportData.getTotalGrade().getGradeName()))
								? "" : getRemarks(examReportData.getTotalGrade().getGradeName())
						: examReportData.getRemarks());

		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(remarks),
				cellLayoutSetup);
		examReportCardLayoutData.getDocument().add(remarksTable);
	}

	private String getRemarks(String grade) {
		switch (grade) {

			case "A":
				return GRADE_A;
			case "B":
				return GRADE_B;
			case "C":
				return GRADE_C;
			case "D":
				return GRADE_D;
			default:
				return null;

		}
	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,ExamReportData examReportData,
										float contentFontSize, float defaultBorderWidth, int pageNumber,
										PdfFont regularFont, PdfFont boldFont) throws IOException {

		int singleContentColumn = 4;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(getParagraph("Class Teacher"), signatureCellLayoutSetup),
				new CellData(getParagraph("Parent"), signatureCellLayoutSetup),
				new CellData(getParagraph("Exam Controller"), signatureCellLayoutSetup),
				new CellData(getParagraph("Principal"), signatureCellLayoutSetup)));
		table.setFixedPosition(30f, 10f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);

		// generateImage(document, documentLayoutSetup,
		// 		ImageProvider.INSTANCE.getImage(ImageProvider._10180_PRINCIPAL_SIGNATURE_WITHOUT_TEXT),
		// 		50f, 50f,
		// 		documentLayoutSetup.getPageSize().getWidth() - 100f, 105f);


	}

	private void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup, ExamReportData examReportData, float contentFontSize,
								  float defaultBorderWidth, PdfFont boldFont, PdfFont regularFont) throws IOException {
//		addBlankLine(document, false, 1);
		if(examReportData.getCourseTypeExamGrades() == null || CollectionUtils.isEmpty(examReportData.getCourseTypeExamGrades().get(CourseType.SCHOLASTIC))) {
			return ;
		}
		Map<String, String> MARKS_GRADE_MAP = new LinkedHashMap<>();
		List<ExamGrade> grades = examReportData.getCourseTypeExamGrades().get(CourseType.SCHOLASTIC);
		for (ExamGrade grade : grades) {
			MARKS_GRADE_MAP.put(grade.getRangeDisplayName(), grade.getGradeName());
		}

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 2)
				.setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("MARKS RANGE")), signatureCellLayoutSetup);
		if(MARKS_GRADE_MAP.size() % 2 == 0) {
			table.setFixedPosition(10f, 65f, documentLayoutSetup.getPageSize().getWidth());
		} else {
			table.setFixedPosition(10f, 80f, documentLayoutSetup.getPageSize().getWidth());
		}

		document.add(table);

		float[] columnWidths = new float[] { 0.24f, 0.24f, 0.24f, 0.24f };
		Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 4)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
		addBlankLine(document, false, 1);
		List<CellData> headerList = new ArrayList<>();
		headerList.add(new CellData("Marks", marksCellLayoutSetup));
		headerList.add(new CellData("Grade", marksCellLayoutSetup));
		headerList.add(new CellData("Marks", marksCellLayoutSetup));
		headerList.add(new CellData("Grade", marksCellLayoutSetup));
		addRow(headerTable, documentLayoutSetup, headerList);
		List<Map.Entry<String, String>> gradesList = new ArrayList<>(MARKS_GRADE_MAP.entrySet());

		// for(int index = 0; index < gradesList.size() / 2; index++) {
		// 	List<CellData> row = new ArrayList<>();
		// 	row.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
		// 	row.add(new CellData(gradesList.get(index).getValue(), marksCellLayoutSetup));
		// 	row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getKey(), marksCellLayoutSetup));
		// 	row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getValue(), marksCellLayoutSetup));
		// 	addRow(headerTable, documentLayoutSetup, row);
		// }

		for (int index = 0; index < gradesList.size(); index++) {
			List<CellData> row = new ArrayList<>();
			row.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
			row.add(new CellData(gradesList.get(index).getValue(), marksCellLayoutSetup));

			if (index + 1 < gradesList.size()) {
				row.add(new CellData(gradesList.get(index + 1).getKey(), marksCellLayoutSetup));
				row.add(new CellData(gradesList.get(index + 1).getValue(), marksCellLayoutSetup));
			}

			if(gradesList.size() % 2 != 0 && index == gradesList.size()-1) {
				row.add(new CellData("", marksCellLayoutSetup));
				row.add(new CellData("", marksCellLayoutSetup));
			}

			addRow(headerTable, documentLayoutSetup, row);

			index++;
		}

		headerTable.setFixedPosition(35f, 15f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(headerTable);
	}

	protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			float logoWidth = 75f;
			float logoHeight = 75f;
			PdfFont boldFont = getRegularBoldFont();
			PdfFont regularFont = getRegularFont();
			int instituteId = institute.getInstituteId();
			ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, DEFAULT_FONT_SIZE,
					DEFAULT_BORDER_WIDTH, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType, GridConfigs.forOnlyObtainedTotalRow(),
						"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType));
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}

}
