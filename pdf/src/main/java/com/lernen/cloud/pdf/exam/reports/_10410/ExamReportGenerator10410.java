package com.lernen.cloud.pdf.exam.reports._10410;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import com.lernen.cloud.pdf.exam.reports._10001.v2.ExamReportGenerator10001;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.List;

public class ExamReportGenerator10410  extends ExamReportGenerator implements IExamReportCardGenerator {
	public ExamReportGenerator10410(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator10001.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;

	protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";
	protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";
	protected static final String SCHOLASTIC_EXAM_DESCRIPTION_PLAY_GROUP_TO_UKG = "SA : Summative Assessment, FA : Formative  Assessment";
	protected static final String SCHOLASTIC_EXAM_DESCRIPTION_1st_TO_10th = "PT = Periodic Test, NB = Notebook, SE = Subject Enrichment, HY = Half Yearly, YL = Yearly or Annual Exam";
	protected static final String SCHOLASTIC_EXAM_DESCRIPTION_11th_TO_12th = "MM = Max Marks, MO = Marks Obtained";
	private static final String HEADMASTER = "c27374bc-ac23-4470-9702-88a9d5c88d38";
	private static final String DIRECTOR = "aad5db77-f2a7-4f91-aa83-0f3d0af616f9";
	private static final String COORDINATOR = "35f1e385-80cd-453f-a146-7a61ab460fe3";


	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
										 ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();

			generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
					1, boldFont, regularFont);

			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData,
										   Institute institute, ExamReportData examReportData,
										   StudentManager studentManager, String reportType,
										   int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

		generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
				examReportData, pageNumber, boldFont, regularFont);


		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				regularFont, boldFont, examReportCardLayoutData.getContentFontSize() - 1,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forOnlyObtainedTotalRow(),
				"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
				nonAdditionalSubjects, "MM", "MO", "#ff0000",
				"-");

		if(!CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()
				.get(CourseType.SCHOLASTIC).getAdditionalCourses())) {
			addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
			generateAdditionalCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, GridConfigs.forSkipTotalRow(),
					"Additional Subjects", getScholasticMarksGridSubjectWidth(reportType), boldFont, regularFont);
		}

		if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
			addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
			generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
					examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
					"Co-Scholastic Subjects", getCoScholasticMarksGridSubjectWidth(reportType), "-");
		}

		addBlankLine(examReportCardLayoutData, false, 1);
		generateStudentAttributeTable(examReportCardLayoutData, examReportData, boldFont, regularFont);

		generateResultSummary(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportData, reportType, boldFont, regularFont, studentManager);
		generateRemarksSection(examReportCardLayoutData, examReportData, examReportCardLayoutData.getContentFontSize() - 1,
				boldFont, regularFont);
		generateSignatureBox(institute, examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, boldFont, regularFont, examReportData, studentManager);

		generateBorderLayout(examReportCardLayoutData, pageNumber, 6f);
	}


	protected void generateAdditionalCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
															   ExamReportData examReportData, GridConfigs gridConfigs,
															   String subjectColumnTitle, float subjectColumnWidth, PdfFont boldFont, PdfFont regularFont) throws IOException {
		ExamReportStructure examReportStructure = examReportData.getExamReportStructure();
		if (examReportStructure == null || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure())
				|| examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) == null
				|| CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC)
				.getAdditionalCourses())) {
			return;
		}

		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				regularFont, boldFont, examReportCardLayoutData.getContentFontSize() - 1,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs,
				subjectColumnTitle, subjectColumnWidth,
				examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses(),
				"MM", "MO", "#ff0000", "-");
	}

	protected float getScholasticMarksGridSubjectWidth(String reportType) {
		return 0.2f;
	}

	protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.5f;
		}
		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			return 0.4f;
		}
		return 0.3f;
	}

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
		float contentFontSize = 12f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
		float logoWidth = 70f;
		float logoHeight = 70f;

		int instituteId = institute.getInstituteId();
		return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
				defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
	}

	protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
										  StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
										  int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		//Institute watermark
		int instituteId = institute.getInstituteId();
		float watermarkImageHeightWidth = 400f;
		generateWatermark(examReportCardLayoutData,
				instituteId, watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20,
				watermarkImageHeightWidth / 2);
		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.89f,
				boldFont, regularFont);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, boldFont, regularFont);
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
								  Institute institute, String reportType, float offsetX, float offsetY,
								  PdfFont boldFont, PdfFont regularFont) throws IOException {

		generateDynamicImageProvider(examReportCardLayoutData, offsetX - 10f, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

//		generateLogo(examReportCardLayoutData, institute, ImageProvider.INSTANCE.getImage(ImageProvider._CBSE_LOGO_2),
//				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - (2.5f * offsetX), offsetY);

		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 2);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(examReportCardLayoutData.getRegularFont()).setFontSize(10f);

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(
				new CellData(getParagraph(EMPTY_TEXT),
						cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT).setPaddingLeft(10f)),
				new CellData(getParagraph("Affiliation Code : RJGGN25015").setMultipliedLeading(0.85f).setFontColor(new DeviceRgb(EColorUtils.whiteR, EColorUtils.redG, EColorUtils.redB)),
						cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT).setPaddingRight(10f))));

		examReportCardLayoutData.getDocument().add(table);

		int singleContentColumn = 1;
		float contentFontSize = examReportCardLayoutData.getContentFontSize();
		table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize + 2f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase()).setMultipliedLeading(0.85f)
						.setFontColor(Color.convertRgbToCmyk(EColorUtils.redDeviceRgb))),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 5f));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1()).setMultipliedLeading(0.85f).setFontColor(new DeviceRgb(EColorUtils.whiteR, EColorUtils.redG, EColorUtils.redB))),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()).setPdfFont(regularFont));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine2()).setMultipliedLeading(0.85f).setFontColor(new DeviceRgb(EColorUtils.whiteR, EColorUtils.redG, EColorUtils.redB))),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()).setPdfFont(regularFont));


		examReportCardLayoutData.getDocument().add(table);
		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
		String headerExamTitle = StringHelper. replaceUnderscoreWithHypen(reportType);
		headerExamTitle += " Progress Report -" + studentLite.getStudentSessionData().getShortYearDisplayName() + "";
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()
						.setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB)).setMultipliedLeading(0.85f)),
				cellLayoutSetup.copy().setFontSize(contentFontSize).setPdfFont(boldFont));
		examReportCardLayoutData.getDocument().add(table);
		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
	}

	protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
											  StudentLite studentLite, ExamReportData examReportData,
											  PdfFont boldFont, PdfFont regularFont) throws IOException {

		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize() - 1;

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

		Paragraph studentName = getKeyValueParagraph("Student's Name : ", studentLite.getName(),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
		Paragraph fatherName = getKeyValueParagraph("Father's Name : ", studentLite.getFathersName(),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
		Paragraph dob = getKeyValueParagraph("DOB : ",
				studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
						: DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
		Paragraph motherName = getKeyValueParagraph("Mother's Name : ", studentLite.getMothersName(),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
		Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ", studentLite.getAdmissionNumber(),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
		Paragraph classValue = getKeyValueParagraph("Class : ",
				studentLite.getStudentSessionData().getStandardNameWithSection(),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
		Paragraph dateOfResultValue = getKeyValueParagraph("Date of Result Declaration : ", examReportData.getDateOfResultDeclaration() == null || examReportData.getDateOfResultDeclaration() <= 0 ? null : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
		Paragraph rollNumberValue = getKeyValueParagraph("Roll No. : ", StringUtils.isBlank(studentLite.getStudentSessionData().getRollNumber()) ? EMPTY_TEXT :studentLite.getStudentSessionData().getRollNumber(),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName.setMultipliedLeading(0.95f), firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
						new CellData(classValue.setMultipliedLeading(0.95f), thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(fatherName.setMultipliedLeading(0.95f), firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
						new CellData(rollNumberValue.setMultipliedLeading(0.95f), thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(motherName.setMultipliedLeading(0.95f), firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
						new CellData(dob.setMultipliedLeading(0.95f), thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(admissionNumber.setMultipliedLeading(0.95f), firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
						new CellData(dateOfResultValue.setMultipliedLeading(0.95f), thirdCellLayoutSetup)));
		document.add(table);

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
	}

	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber, float lineWidth) {
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
		Color color = WebColors.getRGBColor("#df5143");
		canvas.setColor(color, false);
		canvas.setLineWidth(lineWidth);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
	}

	protected void generateStudentAttributeTable(ExamReportCardLayoutData examReportCardLayoutData,
												 ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {
		CellLayoutSetup attributeCellLayoutSetup = new CellLayoutSetup();
		attributeCellLayoutSetup.setPdfFont(boldFont)
				.setFontSize(examReportCardLayoutData.getContentFontSize() - 1).setTextAlignment(TextAlignment.CENTER)
				.setBorder(new SolidBorder(1f));

		Table attributesTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(),
				new float[] { 0.15f, 0.10f, 0.15f, 0.10f, 0.15f, 0.10f, 0.15f, 0.10f });

		addRow(attributesTable, examReportCardLayoutData.getDocumentLayoutSetup(),
				Arrays.asList(getParagraph("Height (cm)", EColorUtils.darkBlueColorList, boldFont),
						getParagraph(examReportData.getHeight(), EColorUtils.darkBlueColorList, regularFont),
						getParagraph("Weight (kg)", EColorUtils.darkBlueColorList, boldFont),
						getParagraph(examReportData.getWeight(), EColorUtils.darkBlueColorList, regularFont),
						getParagraph("No. of Meetings", EColorUtils.darkBlueColorList, boldFont),
						getParagraph(examReportData.getTotalWorkingDays() == null ? ""
								: String.valueOf(examReportData.getTotalWorkingDays()), EColorUtils.darkBlueColorList, regularFont),
						getParagraph("No. of Presents", EColorUtils.darkBlueColorList, boldFont),
						getParagraph(examReportData.getTotalAttendedDays() == null ? ""
								: String.valueOf(examReportData.getTotalAttendedDays()), EColorUtils.darkBlueColorList, regularFont)),
				attributeCellLayoutSetup);

		examReportCardLayoutData.getDocument().add(attributesTable);
	}

	protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
										 float contentFontSize, ExamReportData examReportData,
										 String reportType, PdfFont boldFont, PdfFont regularFont,
										 StudentManager studentManager) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setPaddingTop(2f).setPaddingBottom(2f);

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		Paragraph result = getKeyValueParagraph("Result : ", "-",
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);

		if(examReportData.getExamResultStatus() != null){
			result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
					EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
		}

		Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
				examReportData.getPromotedTo() == null ? "" : examReportData.getPromotedTo().getStandardName(),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
		Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
				examReportData.getTotalObtainedMarks() == null ? "-"
						: examReportData.getTotalObtainedMarks() * 10 % 10 == 0
						? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
						: String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
		Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
						: String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
		Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
						: Math.round(examReportData.getPercentage() * 100) / 100d + "%",
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
//		Paragraph grade = getKeyValueParagraph("Grade : ",
//				examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(),
//				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);

		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result.setMultipliedLeading(0.95f), cellLayoutSetup),
					new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(promotedClass.setMultipliedLeading(0.95f), cellLayoutSetup)));
		}


		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks.setMultipliedLeading(0.95f), cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks.setMultipliedLeading(0.95f), cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage.setMultipliedLeading(0.95f), cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));

		document.add(table);

	}

	protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
										  ExamReportData examReportData, float contentFontSize, PdfFont boldFont, PdfFont regularFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT);

		Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 1);
		Paragraph remarks = getKeyValueParagraph("Remark : ",
				examReportData.getRemarks() == null ? "" : examReportData.getRemarks(),
				EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);

		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(remarks),
				cellLayoutSetup);
		examReportCardLayoutData.getDocument().add(remarksTable);
	}

	protected void generateSignatureBox(Institute institute, Document document, DocumentLayoutSetup documentLayoutSetup,
										float contentFontSize, float defaultBorderWidth, int pageNumber,
										PdfFont boldFont, PdfFont regularFont, ExamReportData examReportData, StudentManager studentManager) throws IOException {

		byte[] staffSignatureImage = getStaffSignature(institute.getInstituteId(), studentManager, HEADMASTER);
		if(staffSignatureImage != null){
			generateImage(document, documentLayoutSetup,
					staffSignatureImage,
					50f, 30f, 480f, 100f);
		}

		staffSignatureImage = getStaffSignature(institute.getInstituteId(), studentManager, DIRECTOR);
		if(staffSignatureImage != null){
			generateImage(document, documentLayoutSetup,
					staffSignatureImage,
					50f, 30f, 340f, 100f);

		}
		staffSignatureImage = getStaffSignature(institute.getInstituteId(), studentManager, COORDINATOR);
		if(staffSignatureImage != null){
			generateImage(document, documentLayoutSetup,
					staffSignatureImage,
					50f, 30f, 210f, 100f);
		}

		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		canvas.moveTo(12, 94);
		canvas.lineTo(583, 94);
		canvas.setLineWidth(.5f);
		canvas.closePathStroke();
		int singleContentColumn = 4;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER).setPadding(0f);

		Cell emptySignatureCell1 = new Cell();
		emptySignatureCell1.setBorder(null);

		Cell emptySignatureCell2 = new Cell();
		emptySignatureCell2.setBorder(null);

		addRow(table, documentLayoutSetup, Arrays.asList(
						getParagraph("Class Teacher").setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB)).setMargin(0f),
						getParagraph("Coordinator").setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB)).setMargin(0f),
						getParagraph("Director").setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB)),
						getParagraph("Headmaster").setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB))),
				signatureCellLayoutSetup);
		table.setFixedPosition(30f, 94, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);
		generateGradeBox(document, documentLayoutSetup, examReportData, contentFontSize, defaultBorderWidth, boldFont, regularFont);

	}

	private void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup, ExamReportData examReportData, float contentFontSize,
								  float defaultBorderWidth, PdfFont boldFont, PdfFont regularFont) throws IOException {

		Map<CourseType, List<ExamGrade>> gradesList = examReportData.getCourseTypeExamGrades();
		List<ExamGrade> scholasticGrades = gradesList.get(CourseType.SCHOLASTIC);
		List<ExamGrade> coScholasticGrades = gradesList.get(CourseType.COSCHOLASTIC);

		boolean hasScholastic = false;
		boolean hasCoScholastic = !CollectionUtils.isEmpty(coScholasticGrades);

		if (!hasScholastic && !hasCoScholastic) {
			return;
		}
		float yAxisforMarksRange = hasScholastic && hasCoScholastic ? 130f : 74f;

		float yOffset = hasCoScholastic ? 109f : 53f;
		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
				.setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("MARKS RANGE").setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB))), signatureCellLayoutSetup);
		table.setFixedPosition(10f, yAxisforMarksRange, documentLayoutSetup.getPageSize().getWidth());
		document.add(table);

		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 2)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);


//		if (hasScholastic) {
//			generateGradeTable(document, documentLayoutSetup, "Scholastic Grading", scholasticGrades, marksCellLayoutSetup,
//					contentFontSize, defaultBorderWidth, boldFont, regularFont, yOffset, CourseType.SCHOLASTIC);
//		}

		if (hasCoScholastic) {
			float coScholasticPositionY = hasScholastic ? 57f : 53f;
			generateGradeTable(document, documentLayoutSetup, "Co-Scholastic Grading", coScholasticGrades, marksCellLayoutSetup,
							contentFontSize, defaultBorderWidth, boldFont, regularFont, coScholasticPositionY, CourseType.COSCHOLASTIC);
		}
	}

	private void generateGradeTable(Document document, DocumentLayoutSetup documentLayoutSetup, String title, List<ExamGrade> grades, CellLayoutSetup marksCellLayoutSetup,
									float contentFontSize, float defaultBorderWidth, PdfFont boldFont, PdfFont regularFont, float yPosition, CourseType courseType) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup titleHeadLayoutsSetup = new CellLayoutSetup();

		if(CourseType.SCHOLASTIC == courseType){
			titleHeadLayoutsSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 2)
					.setBorderTop(new SolidBorder(defaultBorderWidth)).setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderLeft(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER).setBackgroundColor("#fad473");
		}
		if(CourseType.COSCHOLASTIC == courseType){
			titleHeadLayoutsSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 2)
					.setBorderTop(new SolidBorder(defaultBorderWidth)).setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderLeft(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER).setBackgroundColor("#fad473");
		}

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(title).setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB))), titleHeadLayoutsSetup);
		table.setFixedPosition(25f, yPosition, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);

		int columnCount = grades.size() + 1;
		float firstColumnWidth = 0.13f;
		float remainingWidth = 1f - firstColumnWidth;
		float columnWidth = remainingWidth / (columnCount - 1);

		float[] columnWidths = new float[columnCount];
		columnWidths[0] = firstColumnWidth;
		Arrays.fill(columnWidths, 1, columnWidths.length, columnWidth);

		Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
		List<CellData> headerList = new ArrayList<>();
		List<CellData> gradeList = new ArrayList<>();

		CellLayoutSetup headingCellLayoutSetup = new CellLayoutSetup();
		headingCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 2)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER).setBackgroundColor("#fad473");

		headerList.add(new CellData("MARKS RANGE", headingCellLayoutSetup));
		gradeList.add(new CellData("GRADE", marksCellLayoutSetup));

		for (ExamGrade grade : grades) {
			headerList.add(new CellData(grade.getRangeDisplayName(), headingCellLayoutSetup));
			gradeList.add(new CellData(grade.getGradeName(), marksCellLayoutSetup));
		}

		addRow(headerTable.setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB)), documentLayoutSetup, headerList);
		addRow(headerTable.setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB)), documentLayoutSetup, gradeList);
		headerTable.setFixedPosition(25f, yPosition - 37f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(headerTable);
	}


	protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
											  List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			sortClassExamReports(examReportDataList);

			int pageNumber = 1;
			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();

			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
						pageNumber, boldFont, regularFont);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}

			examReportCardLayoutData.getDocument().close();
			return documentOutput;

		} catch (IOException e) {
			e.printStackTrace();
		}

		return null;
	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			PdfFont regularFont = getRegularFont();
			PdfFont boldFont = getRegularBoldFont();

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateScholasticMarksGrid(document,
						documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
						"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
						null, "#ff0000");
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}

	protected byte[] getStaffSignature(int instituteId, StudentManager studentManager, String staffId) throws IOException {
		if(org.apache.commons.lang.StringUtils.isEmpty(staffId)){
			return null;
		}
		return getStudentSchoolPrincipalSignature(instituteId, UUID.fromString(staffId), studentManager);
	}

}
