package com.lernen.cloud.pdf.gatepass;

import com.embrate.cloud.core.api.frontdesk.GatePassDetails;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.configurations.DocumentPropertiesPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.pdf.base.PDFGenerator;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class GatePassGenerator extends PDFGenerator {

	public GatePassGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	public static final float[] DEFAULT_STUDENT_DETAILS_TABLE_HEADER_WIDTH = { 0.07f, 0.31f, 0.31f, 0.31f };

	public abstract DocumentOutput generateGatePassDocument(Institute institute, GatePassDetails gatePassDetails,
				String documentName, boolean officeCopy, DocumentPropertiesPreferences documentPropertiesPreferences);

}
