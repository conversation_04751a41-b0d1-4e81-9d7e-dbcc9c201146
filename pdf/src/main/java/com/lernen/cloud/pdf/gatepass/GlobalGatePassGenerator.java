package com.lernen.cloud.pdf.gatepass;

import com.embrate.cloud.core.api.frontdesk.GatePassDetails;
import com.embrate.cloud.core.api.frontdesk.GatePassStatus;
import com.embrate.cloud.core.api.frontdesk.GatePassStudentDetails;
import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.DocumentPropertiesPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.transport.Vehicle;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;

public class GlobalGatePassGenerator extends GatePassGenerator {

	public GlobalGatePassGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	public static final float DEFAULT_LOGO_WIDTH = 40f;
	public static final float DEFAULT_LOGO_HEIGHT = 40f;

	@Override
	public DocumentOutput generateGatePassDocument(Institute institute, GatePassDetails gatePassDetails,
												   String documentName, boolean officeCopy, DocumentPropertiesPreferences documentPropertiesPreferences) {
		int instituteId = institute.getInstituteId();
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(officeCopy);
			Document document = initDocument(invoice.getContent(), documentLayoutSetup);
			float contentFontSize = 8f;
			float headerFontSize = 11f;
			float defaultBorderWidth = 0.1f;
			DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
				defaultBorderWidth, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(instituteId), null, null);
			

			/* generateLogo(document, documentLayoutSetup, LogoProvider.INSTANCE.getLogo(instituteId), DEFAULT_LOGO_WIDTH,
					DEFAULT_LOGO_HEIGHT); */
			generateDynamicImageProvider(documentLayoutData, instituteId,InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
			generateHeader(document, documentLayoutSetup, gatePassDetails, institute, headerFontSize,
					defaultBorderWidth);
			generateStudentInformation(document, documentLayoutSetup, contentFontSize, gatePassDetails,
					defaultBorderWidth);
			generateStudentDetailsContent(document, documentLayoutSetup, contentFontSize, gatePassDetails,
					defaultBorderWidth);
			generateReasonSummary(document, documentLayoutSetup, contentFontSize, gatePassDetails,
					defaultBorderWidth);
			generateSignatureBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth);
			if (gatePassDetails.getGatePassStatus() == GatePassStatus.CANCELLED) {
				// addWaterMark(document, documentLayoutSetup, "Cancelled");
				PdfPage pdfPage = document.getPdfDocument().getPage(1);
				Rectangle pagesize = pdfPage.getPageSizeWithRotation();

				float x = documentLayoutSetup.isOfficeCopy() ? pagesize.getWidth() / 8 - 50 : pagesize.getWidth() / 4 + 30;
				float y = documentLayoutSetup.isOfficeCopy() ? pagesize.getHeight() * 0.75f : pagesize.getHeight() * 0.70f;

				addWaterMark(document, documentLayoutSetup,
						ImageProvider.INSTANCE.getImage(ImageProvider.CANCELLED_TEXT_IMAGE2), x, y);
			}
			document.close();
			return invoice;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
							   GatePassDetails gatePassDetails, Institute institute, float headerFontSize,
			float defaultBorderWidth) throws IOException {
		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName())),
				cellLayoutSetup.setFontSize(headerFontSize));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
				cellLayoutSetup.setFontSize(9f));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("SCHOOL GATE PASS")),
				Arrays.asList(getParagraph("SCHOOL GATE PASS")), cellLayoutSetup);

		document.add(table);

		// Student copy section
		singleContentColumn = 2;
		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		cellLayoutSetup.setBorderBottom(new SolidBorder(defaultBorderWidth));

		Paragraph receipt = getKeyValueParagraph("Gatepass No. : ",
				gatePassDetails.getGatePassNumber());

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(receipt, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
						new CellData("(Student Copy)", cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))),
				Arrays.asList(new CellData(receipt, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
						new CellData("(Office Copy)", cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));
		document.add(table);

	}

	public void generateStudentInformation(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, GatePassDetails gatePassDetails, float defaultBorderWidth)
			throws IOException {
		int singleContentColumn = 2;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)
				.setBorderLeft(new SolidBorder(defaultBorderWidth));
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT)
				.setBorderRight(new SolidBorder(defaultBorderWidth));

		Paragraph departureWithPerson = getKeyValueParagraph("Name : ",
				gatePassDetails.getName());

		String dateTimeStr = DateUtils.getFormattedDate(gatePassDetails.getGatePassDate(),
				"dd/MMM/yyyy HH:mm", User.DFAULT_TIMEZONE);
		Paragraph date = getKeyValueParagraph("Date and Time: ", dateTimeStr);

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(departureWithPerson, firstCellLayoutSetup),
				new CellData(date, thirdCellLayoutSetup)));

		Paragraph contactNumber = getKeyValueParagraph("Contact Number : ",
				gatePassDetails.getContactNumber());
		Paragraph relation = getKeyValueParagraph("Relation : ",
				gatePassDetails.getRelation());

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(contactNumber, firstCellLayoutSetup),
				new CellData(relation, thirdCellLayoutSetup)));

		Paragraph address = getKeyValueParagraph("Address : ",
				gatePassDetails.getAddress());

		document.add(table);

		singleContentColumn = 1;
		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		firstCellLayoutSetup.setBorderRight(new SolidBorder(defaultBorderWidth));
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(address, firstCellLayoutSetup.setBorderBottom(new SolidBorder(defaultBorderWidth)))));

		document.add(table);
	}

	public void generateStudentDetailsContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
											  GatePassDetails gatePassDetails, float defaultBorderWidth) throws IOException {

		generateStudentDetailsTable(document, documentLayoutSetup, contentFontSize, gatePassDetails,
				defaultBorderWidth);
	}

	public void generateStudentDetailsTable(Document document, DocumentLayoutSetup documentLayoutSetup,
											float contentFontSize, GatePassDetails gatePassDetails, float defaultBorderWidth)
			throws IOException {

		if (CollectionUtils.isEmpty(gatePassDetails.getGatePassStudentDetailsList())) {
			return;
		}

		Table pdfTable = getPDFTable(documentLayoutSetup, DEFAULT_STUDENT_DETAILS_TABLE_HEADER_WIDTH);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.LEFT);

		addRow(pdfTable, documentLayoutSetup,
			Arrays.asList(new CellData("#", cellLayoutSetup),
					new CellData("Student Name", cellLayoutSetup),
					new CellData("Class", cellLayoutSetup),
					new CellData("Vehicle Details", cellLayoutSetup)));
		int count = 1;
		cellLayoutSetup.setPdfFont(getRegularFont());
		for (GatePassStudentDetails gatePassStudentDetails : gatePassDetails.getGatePassStudentDetailsList()) {
			Vehicle vehicle = gatePassStudentDetails.getVehicle();
			String vehicleDetails = "";
			if(vehicle != null) {
				String vehicleRegistrationNumber = vehicle.getRegistrationNumber();
				String vehicleCode = vehicle.getVehicleCode();
				vehicleDetails = vehicleRegistrationNumber + (StringUtils.isBlank(vehicleCode) ? "" : " (" + vehicleCode + ")");
			}
			addRow(pdfTable, documentLayoutSetup, Arrays.asList(
					new CellData(String.valueOf(count++), cellLayoutSetup),
					new CellData(gatePassStudentDetails.getStudentLite().getName(),
							cellLayoutSetup),
					new CellData(gatePassStudentDetails.getStudentLite().getStudentSessionData()
							.getStandardNameWithSection(), cellLayoutSetup),
					new CellData(vehicleDetails,
							cellLayoutSetup)));
			}
			document.add(pdfTable);
	}

	public void generateReasonSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
									  float contentFontSize, GatePassDetails gatePassDetails, float defaultBorderWidth)
			throws IOException {

		generateReasonTable(document, documentLayoutSetup, contentFontSize, gatePassDetails,
				defaultBorderWidth);
	}

	public void generateReasonTable(Document document, DocumentLayoutSetup documentLayoutSetup,
									float contentFontSize, GatePassDetails gatePassDetails, float defaultBorderWidth)
			throws IOException {
		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup keyCellLayoutSetup = new CellLayoutSetup();
		keyCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth))
				.setBorderRight(new SolidBorder(defaultBorderWidth));

		Paragraph reasonKeyValue = getKeyValueParagraph("Reason : ", gatePassDetails.getReason());
		addRow(table, documentLayoutSetup, Arrays.asList(reasonKeyValue), keyCellLayoutSetup);

		document.add(table);

	}

	public void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
			float defaultBorderWidth) throws IOException {
		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.RIGHT).setBorder(new SolidBorder(defaultBorderWidth))
				.setBorderTop(null);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)),
				signatureCellLayoutSetup.copy().setBorderBottom(null));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Signature")), signatureCellLayoutSetup);
		document.add(table);
	}
}
