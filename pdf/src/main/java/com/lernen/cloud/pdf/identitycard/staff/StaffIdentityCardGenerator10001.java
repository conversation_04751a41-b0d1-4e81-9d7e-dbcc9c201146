package com.lernen.cloud.pdf.identitycard.staff;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.DocumentPropertiesPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.staff.FullStaffDetails;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.*;

import com.lernen.cloud.core.utils.EColorUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public class StaffIdentityCardGenerator10001 extends GlobalStaffIdentityCardGenerator {

	public StaffIdentityCardGenerator10001(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(StaffIdentityCardGenerator10001.class);

	protected static final String frontPageBackgroundColorHexCode = "#c38448";
	protected static final String backPageBackgroundColorHexCode = "#f7dcc2";
	protected static final String blackColorHexCode = EColorUtils.lightBlackHexCode;

	protected static final int instituteNameR = 255;
	protected static final int instituteNameG = 255;
	protected static final int instituteNameB = 255;

	protected static final int staffNameR = 218;
	protected static final int staffNameG = 37;
	protected static final int staffNameB = 30;

	protected static final int blackR = 33;
	protected static final int blackG = 33;
	protected static final int blackB = 33;

	protected static final Map<UUID, String> staffDateOfBirthMap = new HashMap<>();

	static {
		staffDateOfBirthMap.put(UUID.fromString("d52b0cc1-db46-4219-9b1c-20aa02e0e615"), "1 January 1966");
		staffDateOfBirthMap.put(UUID.fromString("1d1e9512-8975-4281-913f-a8a1e3eae84d"), "1 January 1965");
		staffDateOfBirthMap.put(UUID.fromString("c7b95b42-a9cf-4f9e-bdc6-41aa098039fb"), "1 January 1961");
		staffDateOfBirthMap.put(UUID.fromString("617bb9ad-4ecf-4d7f-8d0b-3d2b425e0d9d"), "16 May 1963");
		staffDateOfBirthMap.put(UUID.fromString("9db57b62-68fc-40e9-944a-a287eceff295"), "01 January 1967");
		staffDateOfBirthMap.put(UUID.fromString("90e2c986-a75c-4e48-ac0e-8b267514c00a"), "01 January 1964");
		staffDateOfBirthMap.put(UUID.fromString("87360616-542d-4464-a5aa-bd6df67d0115"), "01 January 1967");
		staffDateOfBirthMap.put(UUID.fromString("262e04ae-7120-4f95-8699-90c9bca22363"), "08 December 1968");
		staffDateOfBirthMap.put(UUID.fromString("67f2f77e-c4d4-419b-8d79-7e4af36eb48a"), "14 November 1968");
		staffDateOfBirthMap.put(UUID.fromString("4aca6bd2-eed3-436f-bfa9-36eef1663550"), "29 November 1966");
		staffDateOfBirthMap.put(UUID.fromString("a3524c5c-780a-494a-b06d-a0501e48f4ad"), "01 January 1970");
		staffDateOfBirthMap.put(UUID.fromString("3d6f4c99-124f-43b9-b41a-a6b5d0845851"), "01 January 1966");
		staffDateOfBirthMap.put(UUID.fromString("dfa7c77f-77af-40bc-a4b7-81860e0df88c"), "01 January 1961");
		staffDateOfBirthMap.put(UUID.fromString("ab10b8aa-47a0-4d27-b06b-692b8fefa86c"), "01 July 1963");
		staffDateOfBirthMap.put(UUID.fromString("78b5bcac-ce6e-4436-b72d-1b2b25a644b5"), "04 November 1967");
	}

	@Override
	public DocumentOutput generateIdentityCard(StaffManager staffManager, Institute institute,
											   FullStaffDetails staff, String documentName, DocumentPropertiesPreferences documentPropertiesPreferences ) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute, documentOutput);
			Document document = documentLayoutData.getDocument();

			PdfFont cambriaBoldFont = getCambriaBoldFont();
			PdfFont cambriaFont = getCambriaFont();

			generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
					documentLayoutData.getDocumentLayoutSetup(), institute, staff, staffManager, 1);
			document.close();
			return documentOutput;
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating idenity cards for institute {}, staff {}",
					institute.getInstituteId(), staff.getStaffId(), e);
		}
		return null;
	}

	private int generateIdentityCardDetails(Document document, DocumentLayoutData documentLayoutData,
											 PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup, Institute institute,
											 FullStaffDetails staff, StaffManager staffManager, int pageNumber) throws IOException {

		/**
		 * Staff Details Front
		 */
		generateStaffFrontPageDetails(document, documentLayoutData, institute, pageNumber, staffManager,
				cambriaFont, cambriaBoldFont, documentLayoutSetup, staff);

		/**
		 * Add new page
		 */
		document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
		pageNumber++;

		/**
		 * Staff Details Back
		 */
		generateStaffBackDetails(document, documentLayoutData, pageNumber,
				cambriaFont, cambriaBoldFont, documentLayoutSetup, institute, staffManager, staff);
		return pageNumber;

	}

	private void generateStaffBackDetails(Document document, DocumentLayoutData documentLayoutData,
										  int pageNumber, PdfFont cambriaFont, PdfFont cambriaBoldFont,
										  DocumentLayoutSetup documentLayoutSetup, Institute institute,
										  StaffManager staffManager, FullStaffDetails staff) throws IOException {

		/**
		 * Top address of school
		 */
		generateBackPageTopAddress(document, documentLayoutSetup, cambriaBoldFont, cambriaFont, pageNumber, staff);


		/**
		 * Instructions
		 */
		generateBackPageInstructions(document, documentLayoutSetup, cambriaBoldFont, cambriaFont);


		/**
		 * School name middle part
		 */
		generateBackPageSchoolName(document, documentLayoutSetup, cambriaBoldFont, cambriaFont);


		/**
		 * School details middle part
		 */
		generateBackPageSchoolDetails(document, documentLayoutSetup, cambriaBoldFont, cambriaFont, pageNumber);

		/**
		 * generate top line canvas
		 */
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		canvas.moveTo(7d, documentLayoutSetup.getPageSize().getHeight() - 39d);
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() - 7d, documentLayoutSetup.getPageSize().getHeight() - 39d);

		Color color = WebColors.getRGBColor(blackColorHexCode);
		canvas.setFillColor(color);
		canvas.setStrokeColor(color);
		canvas.setLineWidth(.8f);
		canvas.closePathStroke();


		/**
		 * School Building image
		 */
		generateImage(document, documentLayoutSetup,
				ImageProvider.INSTANCE.getImage(ImageProvider._10001_BUILDING),
				documentLayoutSetup.getPageSize().getWidth(), 60f,
				0, 0);

		/**
		 * generate bottom line canvas
		 */
		canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		canvas.moveTo(7d, 68d);
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() - 7d, 68d);

		color = WebColors.getRGBColor(blackColorHexCode);
		canvas.setFillColor(color);
		canvas.setStrokeColor(color);
		canvas.setLineWidth(.8f);
		canvas.closePathStroke();
	}

	private void generateBackPageSchoolDetails(Document document, DocumentLayoutSetup documentLayoutSetup, PdfFont cambriaBoldFont,
											   PdfFont cambriaFont, int pageNumber) {
		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont)
				.setFontSize(6f).setTextAlignment(TextAlignment.CENTER).setBackgroundColor(backPageBackgroundColorHexCode).setPaddingBottom(0f).setPaddingTop(0f);

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph("Class Play Group to XII", blackR, blackG, blackG), cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph("Helpline : (01565) 223025, 224330, 9413361144", blackR, blackG, blackG), cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph("N.H.11(W) Sri Dungargarh, Bikaner (Rajasthan) - 331803", blackR, blackG, blackG), cellLayoutSetup)));

		cellLayoutSetup.setTextAlignment(TextAlignment.CENTER).setPdfFont(cambriaBoldFont).setFontSize(4.5f);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph("Website: www.sesomu.org| Email: <EMAIL>",
				blackR, blackG, blackG), cellLayoutSetup.copy().setPaddingBottom(2f))));

		document.add(table);
	}

	private void generateBackPageSchoolName(Document document, DocumentLayoutSetup documentLayoutSetup, PdfFont cambriaBoldFont, PdfFont cambriaFont) {
		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(10f).setTextAlignment(TextAlignment.CENTER).setBackgroundColor(frontPageBackgroundColorHexCode).setPaddingBottom(0f)
				.setPaddingTop(0f);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph("SESOMU SCHOOL", blackR, blackG, blackG).setMultipliedLeading(1),
				cellLayoutSetup.copy().setPdfFont(cambriaBoldFont).setFontSize(15f))));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph("For Quality Education", blackR, blackG, blackG), cellLayoutSetup)));

		document.add(table);
	}

	private void generateBackPageInstructions(Document document, DocumentLayoutSetup documentLayoutSetup, PdfFont cambriaBoldFont, PdfFont cambriaFont) {
		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(5.5f).setTextAlignment(TextAlignment.JUSTIFIED)
				.setBackgroundColor(backPageBackgroundColorHexCode).setPaddingBottom(0f).setPaddingTop(0f).setPaddingLeft(5f).setPaddingRight(5f);

		String currentSessionLastDate = "31 March 2024";
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph("1. This card is not transferable.", blackR, blackG, blackG), cellLayoutSetup.copy().setPaddingTop(1f))));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph("2. This card must be carried by the staff for security & Identification.", blackR, blackG, blackG), cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph("3. Entry in the campus/class will not be allowed without this card.", blackR, blackG, blackG), cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph("4. A penalty of Rs. 100 will be charged if the card is lost.", blackR, blackG, blackG), cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph("5. Use this card to mark your attendance.", blackR, blackG, blackG), cellLayoutSetup)));


		cellLayoutSetup.setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph("If found, please return to :", blackR, blackG, blackG), cellLayoutSetup.copy().setPaddingBottom(1f))));

		document.add(table);
	}

	private void generateBackPageTopAddress(Document document, DocumentLayoutSetup documentLayoutSetup,
											PdfFont cambriaBoldFont, PdfFont cambriaFont, int pageNumber, FullStaffDetails staffDetails) {

		Table table = getPDFTable(documentLayoutSetup, 1);
				CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(8f).setTextAlignment(TextAlignment.CENTER).setBackgroundColor(backPageBackgroundColorHexCode).setPaddingBottom(0f).setPaddingTop(0f);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph("Email", blackR, blackG, blackG).setUnderline(),
				cellLayoutSetup.copy().setPdfFont(cambriaBoldFont).setPaddingTop(1f))));
		document.add(table);
		table = getPDFTable(documentLayoutSetup, 1);
		cellLayoutSetup.setFontSize(8f);
		String emailId = staffDetails.getStaffBasicDetailsWithCategoryDepartDesignation().getPrimaryEmail();
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph(
				emailId, blackR, blackG, blackG), cellLayoutSetup)));
//		if(address.contains(",")) {
//			String address1 = address.substring(0, address.lastIndexOf(",")).trim();
//			address1 = StringUtils.isBlank(address1) ? address1 : address1.toUpperCase();
//			String address2 = address.substring(address.lastIndexOf(",") + 2).trim();
//			address2 = StringUtils.isBlank(address2) ? address2 : address2.toUpperCase();
//			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph(address1, blackR, blackG, blackG), cellLayoutSetup)));
//			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph(address2, staffNameR, staffNameG, staffNameB), cellLayoutSetup.copy().setPdfFont(cambriaBoldFont))));
//		} else {
//			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph(address, blackR, blackG, blackG), cellLayoutSetup)));
//		}
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph("GUIDELINES", staffNameR, staffNameG, staffNameB), cellLayoutSetup.copy().setPdfFont(cambriaBoldFont))));
		document.add(table);
	}

	private void generateStaffFrontPageDetails(Document document, DocumentLayoutData documentLayoutData,
											   Institute institute, int pageNumber, StaffManager staffManager,
											   PdfFont cambriaFont, PdfFont cambriaBoldFont,
											   DocumentLayoutSetup documentLayoutSetup, FullStaffDetails staff) throws IOException {

		/**
		 * Bottom bar, keep this on top as we are
		 * using canvas in generateInstituteHeader which
		 * require document to be created before it,
		 * ow error occurred
		 */
		generateBottomBar(document, cambriaBoldFont, documentLayoutSetup);

		/**
		 * Institute Header
		 */
		generateFrontPageHeader(document, documentLayoutData, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute, pageNumber, staff, staffManager);

		/**
		 * Staff basic details
		 */
		generateStaffBasicDetails(document, cambriaBoldFont, cambriaFont, documentLayoutSetup, staff);


		/**
		 * Barcode
		 */
		generateBarCodeImage(document, documentLayoutSetup);

		/**
		 * director signature
		 */
		float bgImageHeightWidth = 120f;
		generateImage(document, documentLayoutSetup,
				ImageProvider.INSTANCE.getImage(ImageProvider._10001_CHAIRMAN_SIGNATURE),
				30f, 20f,
				(((bgImageHeightWidth / 5) + 10f) * 3) + 15, 10f);
	}

	private void generateBarCodeImage(Document document, DocumentLayoutSetup documentLayoutSetup) throws IOException {

		byte[] image = ImageProvider.INSTANCE.getImage(ImageProvider._10001_ICARD_BARCODE);
		if (image == null) {
			return;
		}

		float imageWidth = documentLayoutSetup.getPageSize().getWidth() - 60f;
		float imageHeight = 16f;

		float imageOffsetX = 15f;
		float imageOffsetY = 10f;

		generateImage(document, documentLayoutSetup, image, imageWidth, imageHeight, imageOffsetX, imageOffsetY);

	}

	private void generateStaffBasicDetails(Document document,
										   PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
										   FullStaffDetails staff) {

		String fatherHusbandName = staff.getStaffBasicDetailsWithCategoryDepartDesignation().getFatherName();
		float subToTop = 0;
		if(isTextGreater(fatherHusbandName, 29)) {
			subToTop += 11;
		}
		float bottom = 30 - subToTop;

		Table table = getPDFTable(documentLayoutSetup, 1).setPaddingRight(10f);
		table.setFixedPosition(0, bottom, documentLayoutSetup.getPageSize().getWidth());

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(7f)
				.setTextAlignment(TextAlignment.CENTER).setPaddingLeft(10f).setPaddingTop(0f).setPaddingBottom(0f);

		Paragraph staffNameParagraph = getParagraph(staff
				.getStaffBasicDetailsWithCategoryDepartDesignation().getName().toUpperCase(), staffNameR, staffNameG, staffNameB);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(staffNameParagraph,
				cellLayoutSetup.copy().setPaddingTop(2f).setFontSize(8f).setPdfFont(cambriaBoldFont))));

		String departmentDesignation = "";
		boolean first = true;
		List<String> departmentDesignationNameList = staff.getStaffBasicDetailsWithCategoryDepartDesignation().getDepartmentDesignationNameList();
		if(!CollectionUtils.isEmpty(departmentDesignationNameList)) {
			for(String name : staff.getStaffBasicDetailsWithCategoryDepartDesignation().getDepartmentDesignationNameList()) {
				if(StringUtils.isBlank(name)) {
					continue;
				}
				if(first) {
					departmentDesignation += name;
					first = false;
				} else {
					departmentDesignation += ", " + name;
				}
			}
		}

		Paragraph departmentDesignationParagraph = getParagraph(departmentDesignation, blackR, blackG, blackB);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(departmentDesignationParagraph,
				cellLayoutSetup.copy().setFontSize(7f))));


		Table table2 = getPDFTable(documentLayoutSetup, new float[] {0.3f, 0.7f});

		cellLayoutSetup.setPaddingLeft(0f);

		Paragraph idKeyParagraph = getParagraph("Id # :", blackR, blackG, blackB);
		Paragraph idValueParagraph = getParagraph(staff
				.getStaffBasicDetailsWithCategoryDepartDesignation().getStaffInstituteId(), blackR, blackG, blackB);
		addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(idKeyParagraph,
						cellLayoutSetup.copy().setFontSize(7f).setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.RIGHT).setPaddingTop(5f)),
				new CellData(idValueParagraph, cellLayoutSetup.copy().setFontSize(7f).setTextAlignment(TextAlignment.LEFT).setPaddingTop(5f))));

		Paragraph fatherHusbandNameKeyParagraph = getParagraph("F/H Name :", blackR, blackG, blackB);
		Paragraph fatherHusbandNameValueParagraph = getParagraph(fatherHusbandName,
				blackR, blackG, blackB);
		addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(fatherHusbandNameKeyParagraph,
						cellLayoutSetup.copy().setFontSize(7f).setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.RIGHT)),
				new CellData(fatherHusbandNameValueParagraph, cellLayoutSetup.copy().setFontSize(7f).setTextAlignment(TextAlignment.LEFT))));
		
		String dateOfBirth = staff.getStaffBasicDetailsWithCategoryDepartDesignation().getDateOfBirth() == null ? ""
				:  DateUtils.getFormattedDate(staff.getStaffBasicDetailsWithCategoryDepartDesignation()
				.getDateOfBirth(), "dd MMMM yyyy", User.DFAULT_TIMEZONE);
		UUID staffId = staff.getStaffId();
		if(staffDateOfBirthMap.containsKey(staffId)) {
			dateOfBirth = staffDateOfBirthMap.get(staffId);
		}
		Paragraph dobKeyParagraph = getParagraph("DOB :", blackR, blackG, blackB);
		Paragraph dobValueParagraph = getParagraph(dateOfBirth, blackR, blackG, blackB);
		addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(dobKeyParagraph,
						cellLayoutSetup.copy().setFontSize(7f).setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.RIGHT)),
				new CellData(dobValueParagraph, cellLayoutSetup.copy().setFontSize(7f).setTextAlignment(TextAlignment.LEFT))));

		Paragraph mobileKeyParagraph = getParagraph("Mobile :", blackR, blackG, blackB);
		Paragraph mobileValueParagraph = getParagraph(staff.getStaffBasicDetailsWithCategoryDepartDesignation().getPrimaryContactNumber(),
				blackR, blackG, blackB);
		addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(mobileKeyParagraph,
						cellLayoutSetup.copy().setFontSize(7f).setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.RIGHT)),
				new CellData(mobileValueParagraph, cellLayoutSetup.copy().setFontSize(7f).setTextAlignment(TextAlignment.LEFT))));

//		Paragraph joiningKeyParagraph = getParagraph("Joining :", blackR, blackG, blackB);
//		Paragraph joiningValueParagraph = getParagraph(staff.getStaffJoiningInfo() == null ? "" : staff.getStaffJoiningInfo().getTentativeDateOfJoining() == null ? "" :
//						DateUtils.getFormattedDate(staff.getStaffJoiningInfo().getTentativeDateOfJoining(),
//								"dd MMMM yyyy", User.DFAULT_TIMEZONE),
//				blackR, blackG, blackB);
//		addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(joiningKeyParagraph,
//						cellLayoutSetup.copy().setFontSize(7f).setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.RIGHT)),
//				new CellData(joiningValueParagraph, cellLayoutSetup.copy().setFontSize(7f).setTextAlignment(TextAlignment.LEFT))));

//		Cell cell1 = new Cell();
//		cell1.setBorder(null);
//		table.addCell(cell1);

		Cell cell2 = new Cell();
		cell2.add(table2);
		cell2.setBorder(null);
		table.addCell(cell2);

		document.add(table);
	}

	private void generateBottomBar(Document document, PdfFont cambriaBoldFont, DocumentLayoutSetup documentLayoutSetup) {

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup bottomBarCellLayoutSetup = new CellLayoutSetup();
		bottomBarCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER)
				.setPaddingBottom(2f).setPaddingTop(2f)
				.setBackgroundColor(frontPageBackgroundColorHexCode);

		table.setFixedPosition(0f, 0f, documentLayoutSetup.getPageSize().getWidth());
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(EMPTY_TEXT, bottomBarCellLayoutSetup.copy())));

		document.add(table);
	}

	private void generateFrontPageHeader(Document document,
										 DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
										 DocumentLayoutSetup documentLayoutSetup, Institute institute, int pageNumber,
										 FullStaffDetails staff, StaffManager staffManager) throws IOException {

		generateBackgroundCanvas(document, documentLayoutSetup, pageNumber);

		generateStaffImage(document, documentLayoutSetup, institute, staffManager, staff);

		generateInstituteDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute);
	}

	private void generateInstituteDetails(Document document,
										  DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
										  DocumentLayoutSetup documentLayoutSetup, Institute institute) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();
		centerCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.LEFT)
				.setPaddingLeft(50f).setBackgroundColor(frontPageBackgroundColorHexCode).setPaddingTop(0f).setPaddingBottom(0f);

		String[] instituteNameArr = institute.getInstituteName().split(" ");
		String firstLineInstituteName = instituteNameArr[0].toUpperCase();
		firstLineInstituteName = "SESOMU";
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(firstLineInstituteName, instituteNameR, instituteNameG, instituteNameB)
						.setCharacterSpacing(1.4f).setMultipliedLeading(1)),
				centerCellLayoutSetup.copy().setFontSize(18f));

		int length = instituteNameArr.length;
		String[] instituteNameRestArr = Arrays.copyOfRange(instituteNameArr, 1, length);
		String instituteNameRest = String.join(" ", instituteNameRestArr).toUpperCase();
		instituteNameRest = "SCHOOL";
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(instituteNameRest, instituteNameR, instituteNameG, instituteNameB)
						.setCharacterSpacing(1.4f).setMultipliedLeading(1)),
				centerCellLayoutSetup.copy().setFontSize(18f));

//		CellLayoutSetup addressCellLayoutSetup = centerCellLayoutSetup.copy().setPdfFont(cambriaFont)
//				.setFontSize(5.5f).setTextAlignment(TextAlignment.CENTER).setPaddingLeft(0f);
//		String addressLine1 = "Opp. Housing Board, Jaipur, Jhunjhunu Bypass, Sikar";
//		String addressLine2 = "Helpline : **********, 01572-299377";
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(addressLine1, blackR, blackG, blackG)), addressCellLayoutSetup.copy().setPaddingTop(.5f));
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(addressLine2, blackR, blackG, blackG)), addressCellLayoutSetup.copy());

		document.add(table);

		generateDynamicImageProvider(documentLayoutData, -8, 4, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
	}

	private void generateBackgroundCanvas(Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber) {

		generateBackgroundRectangle(document, documentLayoutSetup, pageNumber);

		generateImageBackground(document, documentLayoutSetup, pageNumber);

	}

	private void generateImageBackground(Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber) {

		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		Color color = WebColors.getRGBColor(frontPageBackgroundColorHexCode);
		canvas.setFillColor(color);
		canvas.setStrokeColor(color);

		canvas.moveTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.75)), documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.25)), documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.25)), documentLayoutSetup.getPageSize().getHeight()
				- ((documentLayoutSetup.getPageSize().getHeight() / 2) + 10));
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.75)), documentLayoutSetup.getPageSize().getHeight()
				- ((documentLayoutSetup.getPageSize().getHeight() / 2) + 10));
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.75)), documentLayoutSetup.getPageSize().getHeight());

		canvas.setLineWidth(5f);
		canvas.fill();
		canvas.closePathStroke();
	}

	private void generateBackgroundRectangle(Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber) {
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);

		Color color = WebColors.getRGBColor(frontPageBackgroundColorHexCode);
		canvas.setFillColor(color);
		canvas.setStrokeColor(color);

		canvas.moveTo(0, documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth(), documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth(), documentLayoutSetup.getPageSize().getHeight()
				- ((documentLayoutSetup.getPageSize().getHeight() / 2) - 25));
		canvas.lineTo(0, documentLayoutSetup.getPageSize().getHeight()
				- ((documentLayoutSetup.getPageSize().getHeight() / 2) - 25));
		canvas.lineTo(0, documentLayoutSetup.getPageSize().getHeight());
		canvas.setLineWidth(5f);
		canvas.fill();
		canvas.closePathStroke();
	}

	private void generateStaffImage(Document document, DocumentLayoutSetup documentLayoutSetup,
									Institute institute, StaffManager staffManager, FullStaffDetails staff) throws MalformedURLException {

		byte[] image = getStaffImage(institute.getInstituteId(), staff, staffManager);
		if (image == null) {
			return;
		}

		float imageWidth = (documentLayoutSetup.getPageSize().getWidth() / 2) - 10f;
		float imageHeight = (documentLayoutSetup.getPageSize().getWidth() / 2);

		float imageOffsetX = documentLayoutSetup.getPageSize().getWidth()
				- (documentLayoutSetup.getPageSize().getWidth() * (0.75f)) + 5f;
		float imageOffsetY = documentLayoutSetup.getPageSize().getHeight()
				- ((documentLayoutSetup.getPageSize().getHeight() / 2) + 10) + 5f;

		generateImage(document, documentLayoutSetup, image, imageWidth, imageHeight,
				imageOffsetX, imageOffsetY);

	}

	protected DocumentLayoutData generateIdentityCardLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		/**
		 * 	aadhar card size - 8.5cmx5.5cm
		 * 	in inches - 3.34646x2.16535
		 * 	1 inch  = 72 points
		 * 	3.34646*72 & 2.16535*72
		 * 	240.94512f X 155.9052f
		 */
		PageSize pageSize = new PageSize(155.9052f, 240.94512f);
		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize);
		float contentFontSize = 9f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

		float logoWidth = 35f;
		float logoHeight = 35f;

		int instituteId = institute.getInstituteId();
		instituteId = 10001;
		DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(),
				getRegularBoldFont(),
				contentFontSize, 0f, logoWidth, logoHeight,
				LogoProvider.INSTANCE.getLogo(instituteId),
				null, null);

		documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
		return documentLayoutData;
	}

	public DocumentLayoutSetup initDocumentLayoutSetup(PageSize defaultPageSize) {
		return initDocumentLayoutSetup(false, defaultPageSize,
				0f, 0f, 0f, 0f);
	}

	@Override
	public DocumentOutput generateIdentityCards(StaffManager staffManager, Institute institute, List<FullStaffDetails> staffList, String documentName, DocumentPropertiesPreferences documentPropertiesPreferences ) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute, documentOutput);
			Document document = documentLayoutData.getDocument();

			PdfFont cambriaBoldFont = getCambriaBoldFont();
			PdfFont cambriaFont = getCambriaFont();

			int pageNumber = 1;
			for (FullStaffDetails staff : staffList) {

				pageNumber = generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
						documentLayoutData.getDocumentLayoutSetup(), institute, staff, staffManager, pageNumber);

				if (pageNumber != staffList.size() * 2) {
					documentLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}

			documentLayoutData.getDocument().close();

			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating staff identity card institute {}", institute.getInstituteId(), e);
		}
		return null;
	}

}
