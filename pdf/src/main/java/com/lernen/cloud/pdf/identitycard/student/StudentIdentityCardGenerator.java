package com.lernen.cloud.pdf.identitycard.student;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.lernen.cloud.core.api.configurations.StudentIdentityCardPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.base.PDFGenerator;

import java.io.IOException;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class StudentIdentityCardGenerator extends PDFGenerator {

	public StudentIdentityCardGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	public abstract DocumentOutput generateIdentityCard(StudentManager studentManager, Institute institute, StudentTransportDetails studentTransportDetails,
														StudentIdentityCardPreferences studentIdentityCardPreferences, Student student, String documentName, StaffManager staffManager);

	public abstract DocumentOutput generateIdentityCards(StudentManager studentManager, Institute institute, List<StudentTransportDetails> studentTransportDetails,
			StudentIdentityCardPreferences studentIdentityCardPreferences, List<Student> students, String documentName, StaffManager staffManager);

	protected DocumentLayoutData generateIdentityCardLayoutData(Institute institute,
			StudentIdentityCardPreferences studentIdentityCardPreferences, DocumentOutput documentOutput)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false, PageSize.A4, 0f, 0f, 29.75f, 5f);
		float contentFontSize = 9f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

		float logoWidth = studentIdentityCardPreferences != null && studentIdentityCardPreferences.getInstituteLogoWidth() != null
				? studentIdentityCardPreferences.getInstituteLogoWidth() : 35f;
		float logoHeight = studentIdentityCardPreferences != null && studentIdentityCardPreferences.getInstituteLogoHeight() != null
				? studentIdentityCardPreferences.getInstituteLogoHeight() : 35f;

		DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, null, null,
				contentFontSize, defaultBorderWidth, logoWidth, logoHeight,
				LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), null, null);

		documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
		return documentLayoutData;
	}

}
