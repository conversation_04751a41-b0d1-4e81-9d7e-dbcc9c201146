package com.lernen.cloud.pdf.identitycard.student;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.xobject.PdfFormXObject;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.StudentIdentityCardPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.AddressUtils;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentIdentityCardGenerator10010 extends StudentIdentityCardGenerator {

    public StudentIdentityCardGenerator10010(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final Logger logger = LogManager.getLogger(StudentIdentityCardGenerator10010.class);

    @Override
    public DocumentOutput generateIdentityCard(StudentManager studentManager, Institute institute, StudentTransportDetails studentTransportDetails,
                                               StudentIdentityCardPreferences studentIdentityCardPreferences, Student student, String documentName, StaffManager staffManager) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute,
                    studentIdentityCardPreferences, documentOutput);
            Document document = documentLayoutData.getDocument();

            PdfFont cambriaBoldFont = getCambriaBoldFont();
            PdfFont cambriaFont = getCambriaFont();

            generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                    documentLayoutData.getDocumentLayoutSetup(), institute, student, studentIdentityCardPreferences, studentManager, 1);
            document.close();
            return documentOutput;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Error while generating idenity cards for institute {}, student {}",
                    institute.getInstituteId(), student.getStudentId(), e);
        }
        return null;
    }

    private void generateIdentityCardDetails(Document document, DocumentLayoutData documentLayoutData,
                        PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup, Institute institute, Student student,
                        StudentIdentityCardPreferences studentIdentityCardPreferences, StudentManager studentManager, int pageNumber) throws IOException {

        generateWatermark(document, documentLayoutSetup);
        /**
         * Institute Header
         */
        generateInstituteHeader(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                documentLayoutSetup, institute, studentIdentityCardPreferences);

        /**
         * Student Details
         */
        generateStudentDetails(document, cambriaFont, cambriaBoldFont, documentLayoutSetup, student, studentIdentityCardPreferences);

        /**
         * Student Image
         */
        generateStudentImage(document, documentLayoutSetup, documentLayoutData, institute, studentManager, student, studentIdentityCardPreferences);

        /**
         * Bottom bar
         */
        generateBottomBar(document, cambriaBoldFont, documentLayoutSetup, studentIdentityCardPreferences);
    }

    private void generateWatermark(Document document, DocumentLayoutSetup documentLayoutSetup) throws IOException{

		byte[] image = ImageProvider.INSTANCE.getImage(ImageProvider._10010_STUDENT_ID_CARD_WATERMARK);
		if (image == null) {
			return;
		}

		float imageWidth =  documentLayoutSetup.getPageSize().getWidth();
		float imageHeight = documentLayoutSetup.getPageSize().getHeight();

		float imageOffsetX = 0;
		float imageOffsetY = 0;

		generateImage(document, documentLayoutSetup, image, imageWidth, imageHeight,
				imageOffsetX, imageOffsetY);

	}

    protected void generateStudentImage(Document document, DocumentLayoutSetup documentLayoutSetup, DocumentLayoutData documentLayoutData,
                                      Institute institute, StudentManager studentManager, Student student,
                                      StudentIdentityCardPreferences studentIdentityCardPreferences) throws MalformedURLException {

        byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
        if (image == null) {
            image = documentLayoutData.getImageFrame();
        }
        float width = 50f;
        float height = 60f;
        if(studentIdentityCardPreferences != null && studentIdentityCardPreferences.getStudentImageWidth() != null) {
            width = studentIdentityCardPreferences.getStudentImageWidth();
        }
        if(studentIdentityCardPreferences != null && studentIdentityCardPreferences.getStudentImageHeight() != null) {
            height = studentIdentityCardPreferences.getStudentImageHeight();
        }
        generateImage(document, documentLayoutSetup, image, width, height, documentLayoutSetup.getPageSize().getWidth() - 63f,
                42f);
    }

    protected void generateStudentDetails(Document document, PdfFont cambriaFont, PdfFont cambriaBoldFont, DocumentLayoutSetup documentLayoutSetup,
                                        Student student, StudentIdentityCardPreferences studentIdentityCardPreferences) {

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(7f)
                .setTextAlignment(TextAlignment.LEFT).setPaddingLeft(10f).setPaddingTop(0f).setPaddingBottom(0f);


        Table table = getPDFTable(documentLayoutSetup, new float [] { 0.6f, 0.4f });
        CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();

        List<Integer> rgb = studentIdentityCardPreferences != null &&
                !StringUtils.isEmpty(studentIdentityCardPreferences.getiCardDetailsTextColor()) ?
                EColorUtils.hex2Rgb(studentIdentityCardPreferences.getiCardDetailsTextColor()) : EColorUtils.hex2Rgb("#000000");
        centerCellLayoutSetup.setPdfFont(cambriaFont).setTextAlignment(TextAlignment.RIGHT).setPaddingRight(12f).setFontSize(7f);

        Paragraph session = getParagraph("Session : " + student.getStudentAcademicSessionInfoResponse()
                .getAcademicSession().getShortYearDisplayName(), rgb);

        Paragraph studentName = getKeyValueParagraph("Name : ",
                student.getStudentBasicInfo().getName().toUpperCase(), rgb, cambriaBoldFont, cambriaFont);

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(studentName, cellLayoutSetup.copy().setPaddingTop(2f)),
                new CellData(session, centerCellLayoutSetup.copy().setPaddingTop(2f))));

        document.add(table);

        table = getPDFTable(documentLayoutSetup, new float [] { 0.7f, 0.3f });

        Paragraph fatherName = getKeyValueParagraph("Father's Name : ", student.getStudentFamilyInfo().getFathersName(),
                rgb, cambriaBoldFont, cambriaFont);

        Paragraph className = getKeyValueParagraph("Class : ",
                student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection(),
                rgb, cambriaBoldFont, cambriaFont);

        Paragraph classRollNumber = getKeyValueParagraph("Class Roll Number : ",
                student.getStudentAcademicSessionInfoResponse().getRollNumber(),
                rgb, cambriaBoldFont, cambriaFont);

        Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ",
                student.getStudentBasicInfo().getAdmissionNumber(),
                rgb, cambriaBoldFont, cambriaFont);

        Paragraph dob = getKeyValueParagraph("DOB : ",
                student.getStudentBasicInfo().getDateOfBirth() == null
                        || student.getStudentBasicInfo().getDateOfBirth() <= 0 ? ""
                        : DateUtils.getFormattedDate(student.getStudentBasicInfo().getDateOfBirth(),
                        DATE_FORMAT_DOT, User.DFAULT_TIMEZONE), rgb, cambriaBoldFont, cambriaFont);

        Paragraph bloodGroup = getKeyValueParagraph("Blood Group : ",
                student.getStudentMedicalInfo() == null ?
                        "" : student.getStudentMedicalInfo().getBloodGroup() == null ?
                        "" : student.getStudentMedicalInfo().getBloodGroup().getDisplayName(),
                rgb, cambriaBoldFont, cambriaFont);

        Paragraph contactNumber = getKeyValueParagraph("Contact Number : ",
                student.getStudentBasicInfo().getPrimaryContactNumber(),
                rgb, cambriaBoldFont, cambriaFont);

        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(fatherName, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(className, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(classRollNumber, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(admissionNumber, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(dob, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(bloodGroup, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(contactNumber, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));
        document.add(table);

        table = getPDFTable(documentLayoutSetup, 1);
        Paragraph address = getKeyValueParagraph("Address : ", AddressUtils.getStudentAddress(student),
                rgb, cambriaBoldFont, cambriaFont);
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(address, cellLayoutSetup.copy()),
                        new CellData("", cellLayoutSetup.copy())));
        document.add(table);
    }
    protected void generateBottomBar(Document document, PdfFont cambriaBoldFont, DocumentLayoutSetup documentLayoutSetup,
                                     StudentIdentityCardPreferences studentIdentityCardPreferences) {
        generateBottomBar(document, cambriaBoldFont, null, documentLayoutSetup, studentIdentityCardPreferences, false, 2f, 2f, 0f, 0f);

    }

    protected void generateBottomBar(Document document, PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
                                   StudentIdentityCardPreferences studentIdentityCardPreferences, boolean isPrincipalSignRequired, float paddingBottom, float paddingTop, float principalSignPadding, float principalSignFontSize) {

        Table table = getPDFTable(documentLayoutSetup, 1);
        CellLayoutSetup bottomBarCellLayoutSetup = new CellLayoutSetup();
        if(isPrincipalSignRequired) {
            addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData("PRINCIPAL", bottomBarCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT).setPaddingRight(principalSignPadding).setFontSize(principalSignFontSize).setPdfFont(cambriaFont))));

        }

        bottomBarCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER)
                .setPaddingBottom(paddingBottom).setPaddingTop(paddingTop);

        if(studentIdentityCardPreferences != null && !StringUtils.isEmpty(studentIdentityCardPreferences.getFooterBarColor())) {
            bottomBarCellLayoutSetup.setBackgroundColor(studentIdentityCardPreferences.getFooterBarColor());
        }

        table.setFixedPosition(0f, 0f, documentLayoutSetup.getPageSize().getWidth());
        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(EMPTY_TEXT, bottomBarCellLayoutSetup.copy())));

        document.add(table);
    }

    protected void generateInstituteHeader(Document document,
                                         DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
                                         DocumentLayoutSetup documentLayoutSetup, Institute institute,
                                         StudentIdentityCardPreferences studentIdentityCardPreferences) throws IOException {

        Table table = getPDFTable(documentLayoutSetup, 1);
        CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();
        centerCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER)
                .setPaddingLeft(20f);

        if(studentIdentityCardPreferences != null && !StringUtils.isEmpty(studentIdentityCardPreferences.getHeaderBackgroundColor())) {
            centerCellLayoutSetup.setBackgroundColor(studentIdentityCardPreferences.getHeaderBackgroundColor());
        }

        List<Integer> rgbInstituteNameColor = studentIdentityCardPreferences != null &&
                !StringUtils.isEmpty(studentIdentityCardPreferences.getInstituteNameColor()) ?
                EColorUtils.hex2Rgb(studentIdentityCardPreferences.getInstituteNameColor()) : null;
        Float rgbInstituteNameFontSize = studentIdentityCardPreferences != null &&
                studentIdentityCardPreferences.getInstituteNameFontSize() != null ?
                studentIdentityCardPreferences.getInstituteNameFontSize() : 10f;
        addRow(table, documentLayoutSetup, Arrays.asList(
                        getParagraph(institute.getInstituteName().toUpperCase(), rgbInstituteNameColor)),
                centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize).setPaddingTop(0f).setPaddingBottom(0f));


        List<Integer> rgbLetterHead1Color = studentIdentityCardPreferences != null &&
                !StringUtils.isEmpty(studentIdentityCardPreferences.getLetterHead1Color()) ?
                EColorUtils.hex2Rgb(studentIdentityCardPreferences.getLetterHead1Color()) : null;
        Float rgbLetterHead1FontSize = studentIdentityCardPreferences != null &&
                studentIdentityCardPreferences.getLetterHead1FontSize() != null ?
                studentIdentityCardPreferences.getLetterHead1FontSize() : 10f;
        addRow(table, documentLayoutSetup, Arrays.asList(
                getParagraph(institute.getLetterHeadLine1(), rgbLetterHead1Color)), centerCellLayoutSetup.copy().setPdfFont(cambriaFont)
                .setFontSize(rgbLetterHead1FontSize).setPaddingBottom(0f).setPaddingTop(0f));


        List<Integer> rgbLetterHead2Color = studentIdentityCardPreferences != null &&
                !StringUtils.isEmpty(studentIdentityCardPreferences.getLetterHead2Color()) ?
                EColorUtils.hex2Rgb(studentIdentityCardPreferences.getLetterHead2Color()) : null;
        Float rgbLetterHead2FontSize = studentIdentityCardPreferences != null &&
                studentIdentityCardPreferences.getLetterHead2FontSize() != null ?
                studentIdentityCardPreferences.getLetterHead2FontSize() : 10f;
       /*  addRow(table, documentLayoutSetup, Arrays.asList(
                getParagraph(institute.getLetterHeadLine2(), rgbLetterHead2Color)), centerCellLayoutSetup.copy().setPdfFont(cambriaFont)
                .setFontSize(rgbLetterHead2FontSize).setPaddingTop(0f).setPaddingBottom(2f)); */

        document.add(table);

        generateDynamicImageProvider(documentLayoutData, -4, 2, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

    }

    protected DocumentLayoutData generateIdentityCardLayoutData(Institute institute,
                                                                StudentIdentityCardPreferences studentIdentityCardPreferences, DocumentOutput documentOutput)
            throws IOException {

        /**
         * 	aadhar card size - 8.5cmx5.5cm
         * 	in inches - 3.34646x2.16535
         * 	1 inch  = 72 points
         * 	3.34646*72 & 2.16535*72
         * 	240.94512f X 155.9052f
         */
        PageSize pageSize = new PageSize(241f, 156f);
        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize);
        float contentFontSize = 9f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

        float logoWidth = studentIdentityCardPreferences != null && studentIdentityCardPreferences.getInstituteLogoWidth() != null
                ? studentIdentityCardPreferences.getInstituteLogoWidth() : 35f;
        float logoHeight = studentIdentityCardPreferences != null && studentIdentityCardPreferences.getInstituteLogoHeight() != null
                ? studentIdentityCardPreferences.getInstituteLogoHeight() : 35f;

        DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(),
                getRegularBoldFont(),
                contentFontSize, 0f, logoWidth, logoHeight,
                LogoProvider.INSTANCE.getLogo(institute.getInstituteId()),
                null, null);

        documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
        return documentLayoutData;
    }

    public DocumentLayoutSetup initDocumentLayoutSetup(PageSize defaultPageSize) {
        return initDocumentLayoutSetup(false, defaultPageSize,
                0f, 0f, 0f, 0f);
    }

    @Override
    public DocumentOutput generateIdentityCards(StudentManager studentManager, Institute institute, List<StudentTransportDetails> studentTransportDetails,
                                                StudentIdentityCardPreferences studentIdentityCardPreferences, List<Student> students,
                                                String documentName, StaffManager staffManager) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute,
                    studentIdentityCardPreferences, documentOutput);
            Document document = documentLayoutData.getDocument();

            PdfFont cambriaBoldFont = getCambriaBoldFont();
            PdfFont cambriaFont = getCambriaFont();

            int pageNumber = 1;
            for (Student student : students) {

                generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                        documentLayoutData.getDocumentLayoutSetup(), institute, student, studentIdentityCardPreferences, studentManager,
                        pageNumber);

                if (pageNumber != students.size()) {
                    documentLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;

            }

            documentLayoutData.getDocument().close();

            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating student identity card institute {}", institute.getInstituteId(), e);
        }
        return null;
    }

    protected DocumentOutput getA4PortraitIdentityCard(String documentName, DocumentOutput documentOutput, PageSize pageSize, int rowNum,
                                                       int colNum, float spaceBetweenRows) throws IOException {

        PdfReader reader = new PdfReader(new ByteArrayInputStream(documentOutput.getContent().toByteArray()));
        DocumentOutput invoice = new DocumentOutput(documentName + "_a4.pdf", new ByteArrayOutputStream());
        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize);
        PdfWriter pdfWriter = new PdfWriter(invoice.getContent());
        PdfDocument pdf = new PdfDocument(pdfWriter);
        Document document = new Document(pdf, documentLayoutSetup.getPageSize());
        document.setMargins(documentLayoutSetup.getTopMargin(), documentLayoutSetup.getSideMargin(),
                documentLayoutSetup.getBottomMargin(), documentLayoutSetup.getSideMargin());

        PdfDocument sourcePdf = new PdfDocument(reader);

        int iCardNumber = 1;
        while(sourcePdf.getNumberOfPages() >= iCardNumber) {
            for(int row = 0; row < rowNum; row++) {
                if(sourcePdf.getNumberOfPages() < iCardNumber) {
                    break;
                }
                PageSize nUpPageSize = pageSize;
                if((iCardNumber - 1) % (rowNum * colNum) == 0) {
                    PdfPage page = pdf.addNewPage(nUpPageSize);
                    PdfCanvas canvas = new PdfCanvas(page);
                }

                PdfPage page = pdf.getPage(pdf.getNumberOfPages());
                PdfCanvas canvas = new PdfCanvas(page);

                for(int column = 0; column < colNum; column++) {
                    if(sourcePdf.getNumberOfPages() < iCardNumber) {
                        break;
                    }
                    PdfPage origPage = sourcePdf.getPage(iCardNumber);
                    Rectangle orig = origPage.getPageSize();
                    PdfFormXObject pageCopy = origPage.copyAsFormXObject(pdf);

                    //Add pages to N-up page
                    float x = ((orig.getWidth() + 20)  * column) + 40;
                    float y = (nUpPageSize.getHeight() - ((row + 1) * (orig.getHeight() + spaceBetweenRows)));
                    canvas.addXObject(pageCopy, x, y);
                    iCardNumber++;
                }
            }
        }

        // close the documents
        pdf.close();
        sourcePdf.close();
        return invoice;
    }
    protected DocumentOutput getA4LandscapeIdentityCard(String documentName, DocumentOutput documentOutput, PageSize pageSize, int rowNum,
                                                        int colNum, float spaceBetweenRows, float spaceBetweenColumns, float columnStartSpace) throws IOException {
       return getA4LandscapeIdentityCard(documentName,  documentOutput, pageSize,  rowNum,
         colNum,  spaceBetweenRows,  spaceBetweenColumns,  columnStartSpace, 0f);
    }


    protected DocumentOutput getA4LandscapeIdentityCard(String documentName, DocumentOutput documentOutput, PageSize pageSize, int rowNum,
                                                        int colNum, float spaceBetweenRows, float spaceBetweenColumns, float columnStartSpace, float rowTopSpace) throws IOException {

        PdfReader reader = new PdfReader(new ByteArrayInputStream(documentOutput.getContent().toByteArray()));
        DocumentOutput invoice = new DocumentOutput(documentName + "_a4.pdf", new ByteArrayOutputStream());
        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize);
        PdfWriter pdfWriter = new PdfWriter(invoice.getContent());
        PdfDocument pdf = new PdfDocument(pdfWriter);
        Document document = new Document(pdf, documentLayoutSetup.getPageSize());
        document.setMargins(documentLayoutSetup.getTopMargin(), documentLayoutSetup.getSideMargin(),
                documentLayoutSetup.getBottomMargin(), documentLayoutSetup.getSideMargin());

        PdfDocument sourcePdf = new PdfDocument(reader);

        int iCardNumber = 1;
        while(sourcePdf.getNumberOfPages() >= iCardNumber) {
            for(int row = 0; row < rowNum; row++) {
                if(sourcePdf.getNumberOfPages() < iCardNumber) {
                    break;
                }
                PageSize nUpPageSize = pageSize;
                if((iCardNumber - 1) % (rowNum * colNum) == 0) {
                    PdfPage page = pdf.addNewPage(nUpPageSize);
                    PdfCanvas canvas = new PdfCanvas(page);
                }

                PdfPage page = pdf.getPage(pdf.getNumberOfPages());
                PdfCanvas canvas = new PdfCanvas(page);

                for(int column = 0; column < colNum; column++) {
                    if(sourcePdf.getNumberOfPages() < iCardNumber) {
                        break;
                    }
                    PdfPage origPage = sourcePdf.getPage(iCardNumber);
                    Rectangle orig = origPage.getPageSize();
                    PdfFormXObject pageCopy = origPage.copyAsFormXObject(pdf);

                    //Add pages to N-up page
                    float x = ((orig.getWidth() + columnStartSpace)  * column) + spaceBetweenColumns;
                    float y = (nUpPageSize.getHeight() - ((row + 1) * (orig.getHeight() + spaceBetweenRows)) - rowTopSpace);
                    canvas.addXObject(pageCopy, x, y);
                    iCardNumber++;
                }
            }
        }

        // close the documents
        pdf.close();
        sourcePdf.close();
        return invoice;
    }

    protected void generateSessionDetails(Document document,
                                        DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
                                        DocumentLayoutSetup documentLayoutSetup, Institute institute, Student student, StudentIdentityCardPreferences studentIdentityCardPreferences) throws IOException {

        List<Integer> rgbSessionColor = studentIdentityCardPreferences != null &&
                !StringUtils.isEmpty(studentIdentityCardPreferences.getSessionFontColor()) ?
                EColorUtils.hex2Rgb(studentIdentityCardPreferences.getSessionFontColor()) :
                EColorUtils.hex2Rgb("#ffffff");
        Float rgbSessionFontSize =  studentIdentityCardPreferences != null &&
                studentIdentityCardPreferences.getSessionFontSize() != null ?
                studentIdentityCardPreferences.getSessionFontSize() : 8.5f;


        Table table = getPDFTable(documentLayoutSetup, 2);
        CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();
        centerCellLayoutSetup.setPdfFont(cambriaFont).setTextAlignment(TextAlignment.LEFT)
                .setFontSize(rgbSessionFontSize).setPaddingBottom(0f).setPaddingTop(0f);
        if(studentIdentityCardPreferences != null && !StringUtils.isEmpty(studentIdentityCardPreferences.getSessionBackGroundColor())) {
            centerCellLayoutSetup.setBackgroundColor(studentIdentityCardPreferences.getSessionBackGroundColor());
        }

        Paragraph identityCardText = getParagraph("IDENTITY CARD", rgbSessionColor);

        Paragraph session = getParagraph(student.getStudentAcademicSessionInfoResponse()
                .getAcademicSession().getYearDisplayName(), rgbSessionColor);

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(identityCardText, centerCellLayoutSetup.copy().setPaddingLeft(15f)),
                new CellData(session, centerCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT).setPaddingRight(15f))));

        document.add(table);
    }
}
