package com.lernen.cloud.pdf.identitycard.student;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.StudentIdentityCardPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.AddressUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentIdentityCardGenerator10170_10171 extends GlobalStudentIdentityCardGenerator {

    public StudentIdentityCardGenerator10170_10171(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final Logger logger = LogManager.getLogger(StudentIdentityCardGenerator10170_10171.class);

    @Override
    public DocumentOutput generateIdentityCard(StudentManager studentManager, Institute institute, StudentTransportDetails studentTransportDetails,
                                               StudentIdentityCardPreferences studentIdentityCardPreferences, Student student, String documentName, StaffManager staffManager) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute,
                    studentIdentityCardPreferences, documentOutput);
            Document document = documentLayoutData.getDocument();

            PdfFont cambriaBoldFont = getCambriaBoldFont();
            PdfFont cambriaFont = getCambriaFont();

            generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                    documentLayoutData.getDocumentLayoutSetup(), institute, student, studentIdentityCardPreferences, studentManager, 1);
            document.close();
            return documentOutput;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Error while generating idenity cards for institute {}, student {}",
                    institute.getInstituteId(), student.getStudentId(), e);
        }
        return null;
    }

    private void generateIdentityCardDetails(Document document, DocumentLayoutData documentLayoutData,
                        PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup, Institute institute, Student student,
                        StudentIdentityCardPreferences studentIdentityCardPreferences, StudentManager studentManager, int pageNumber) throws IOException {

        /**
         * Institute Header
         */
        generateInstituteHeader(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                documentLayoutSetup, institute, studentIdentityCardPreferences, student);

        /**
         * Student Details
         */
        generateStudentDetails(document, cambriaFont, cambriaBoldFont, documentLayoutSetup, student, studentIdentityCardPreferences);

        /**
         * Student Image
         */
        generateStudentImage(document, documentLayoutSetup, documentLayoutData, institute, studentManager, student, studentIdentityCardPreferences);


        /**
         * Principal Signature
         */
        float bgImageHeightWidth = 120f;
        generateImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(ImageProvider._10170_10171_PRINCIPAL_SIGNATURE),
                30f, 20f,
                documentLayoutSetup.getPageSize().getWidth() - 50f,
                (bgImageHeightWidth / 2) - 54f);

        /**
         * Call icon in header
         */
        generateImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(ImageProvider.CALL_ICON),
                5f, 5f,
                documentLayoutSetup.getPageSize().getWidth() / 2 - 27f,
                documentLayoutSetup.getPageSize().getHeight() / 2 + 41f);

        /**
         * Bottom bar
         */
        generateBottomBar(document, cambriaBoldFont, documentLayoutSetup, studentIdentityCardPreferences);
    }

    protected void generateStudentImage(Document document, DocumentLayoutSetup documentLayoutSetup, DocumentLayoutData documentLayoutData,
                                      Institute institute, StudentManager studentManager, Student student,
                                      StudentIdentityCardPreferences studentIdentityCardPreferences) throws MalformedURLException {

        byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
        if (image == null) {
            image = documentLayoutData.getImageFrame();
        }
        float width = 50f;
        float height = 60f;
//        if(studentIdentityCardPreferences != null && studentIdentityCardPreferences.getStudentImageWidth() != null) {
//            width = studentIdentityCardPreferences.getStudentImageWidth();
//        }
//        if(studentIdentityCardPreferences != null && studentIdentityCardPreferences.getStudentImageHeight() != null) {
//            height = studentIdentityCardPreferences.getStudentImageHeight();
//        }
//        generateImage(document, documentLayoutSetup, image, width, height, documentLayoutSetup.getPageSize().getWidth() - 63f,
//                42f);

        generateImage(document, documentLayoutSetup, image, width, height, documentLayoutSetup.getPageSize().getWidth() - 63f,
                30f);
    }

    protected void generateStudentDetails(Document document, PdfFont cambriaFont, PdfFont cambriaBoldFont, DocumentLayoutSetup documentLayoutSetup,
                                        Student student, StudentIdentityCardPreferences studentIdentityCardPreferences) {

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(7f)
                .setTextAlignment(TextAlignment.LEFT).setPaddingLeft(10f).setPaddingTop(0f).setPaddingBottom(0f);


//        Table table = getPDFTable(documentLayoutSetup, new float [] { 0.7f, 0.3f });
        CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();

        List<Integer> rgb = studentIdentityCardPreferences != null &&
                !StringUtils.isEmpty(studentIdentityCardPreferences.getiCardDetailsTextColor()) ?
                EColorUtils.hex2Rgb(studentIdentityCardPreferences.getiCardDetailsTextColor()) : null;
        rgb = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);

        centerCellLayoutSetup.setPdfFont(cambriaFont).setTextAlignment(TextAlignment.RIGHT).setPaddingRight(12f).setFontSize(7f);

        Table table = getPDFTable(documentLayoutSetup, new float [] { 0.7f, 0.3f });

//        table.setPaddingTop(10f);

        Paragraph classRollNumber = getKeyValueParagraph("Roll No : ",
                student.getStudentAcademicSessionInfoResponse().getRollNumber(),
                rgb, cambriaBoldFont, cambriaFont);
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(classRollNumber, cellLayoutSetup.copy().setPaddingTop(4f)),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy().setPaddingTop(4f))));

        Paragraph studentName = getKeyValueParagraph("Name : ",
                student.getStudentBasicInfo().getName().toUpperCase(), rgb, cambriaBoldFont, cambriaFont);
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(studentName, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));

        Paragraph className = getKeyValueParagraph("Class : ",
                student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection(),
                rgb, cambriaBoldFont, cambriaFont);
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(className, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));

        Paragraph fatherName = getKeyValueParagraph("Father's Name : ", student.getStudentFamilyInfo().getFathersName(),
                rgb, cambriaBoldFont, cambriaFont);
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(fatherName, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));

        Paragraph contactNumber = getKeyValueParagraph("Contact Number : ",
                student.getStudentBasicInfo().getPrimaryContactNumber(),
                rgb, cambriaBoldFont, cambriaFont);
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(contactNumber, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));

        Paragraph address = getKeyValueParagraph("Address : ", AddressUtils.getStudentAddress(student),
                rgb, cambriaBoldFont, cambriaFont);
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(address, cellLayoutSetup.copy()),
                        new CellData("", cellLayoutSetup.copy())));
        document.add(table);
    }

    protected void generateBottomBar(Document document, PdfFont cambriaBoldFont, DocumentLayoutSetup documentLayoutSetup,
                                   StudentIdentityCardPreferences studentIdentityCardPreferences) {

        Table table = getPDFTable(documentLayoutSetup, 1);
        CellLayoutSetup bottomBarCellLayoutSetup = new CellLayoutSetup();
        bottomBarCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER)
                .setPaddingBottom(2f).setPaddingTop(2f);

        if(studentIdentityCardPreferences != null && !StringUtils.isEmpty(studentIdentityCardPreferences.getFooterBarColor())) {
            String footerBarColor = studentIdentityCardPreferences.getFooterBarColor();
            footerBarColor = "#418e45";
            bottomBarCellLayoutSetup.setBackgroundColor(footerBarColor);
        }

        table.setFixedPosition(0f, 0f, documentLayoutSetup.getPageSize().getWidth());
        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(EMPTY_TEXT, bottomBarCellLayoutSetup.copy())));

        document.add(table);
    }

    private void generateInstituteHeader(Document document,
                                         DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
                                         DocumentLayoutSetup documentLayoutSetup, Institute institute,
                                         StudentIdentityCardPreferences studentIdentityCardPreferences, Student student) throws IOException {

        Table table = getPDFTable(documentLayoutSetup, 1);
        CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();
        centerCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER)
                .setPaddingLeft(40f);

        if(studentIdentityCardPreferences != null && !StringUtils.isEmpty(studentIdentityCardPreferences.getHeaderBackgroundColor())) {
            String headerBackgroundColor = studentIdentityCardPreferences.getHeaderBackgroundColor();
            headerBackgroundColor = "#d0e0e3";
            centerCellLayoutSetup.setBackgroundColor(headerBackgroundColor);
        }

        List<Integer> rgbInstituteNameColor = studentIdentityCardPreferences != null &&
                !StringUtils.isEmpty(studentIdentityCardPreferences.getInstituteNameColor()) ?
                EColorUtils.hex2Rgb(studentIdentityCardPreferences.getInstituteNameColor()) : null;
        rgbInstituteNameColor = EColorUtils.hex2Rgb("#383981");

        Float rgbInstituteNameFontSize = studentIdentityCardPreferences != null &&
                studentIdentityCardPreferences.getInstituteNameFontSize() != null ?
                studentIdentityCardPreferences.getInstituteNameFontSize() : 10f;
        rgbInstituteNameFontSize = 11f;

        addRow(table, documentLayoutSetup, Arrays.asList(
                        getParagraph(institute.getInstituteName().toUpperCase(), rgbInstituteNameColor)),
                centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize).setPaddingTop(0f).setPaddingBottom(0f));


        List<Integer> rgbLetterHead1Color = studentIdentityCardPreferences != null &&
                !StringUtils.isEmpty(studentIdentityCardPreferences.getLetterHead1Color()) ?
                EColorUtils.hex2Rgb(studentIdentityCardPreferences.getLetterHead1Color()) : null;
        rgbLetterHead1Color = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);

        Float rgbLetterHead1FontSize = studentIdentityCardPreferences != null &&
                studentIdentityCardPreferences.getLetterHead1FontSize() != null ?
                studentIdentityCardPreferences.getLetterHead1FontSize() : 10f;
        rgbLetterHead1FontSize = 7f;

        addRow(table, documentLayoutSetup, Arrays.asList(
                getParagraph("Radhakishanpura, Sikar 332001", rgbLetterHead1Color)), centerCellLayoutSetup.copy().setPdfFont(cambriaFont)
                .setFontSize(rgbLetterHead1FontSize).setPaddingBottom(0f).setPaddingTop(0f));


        List<Integer> rgbLetterHead2Color = studentIdentityCardPreferences != null &&
                !StringUtils.isEmpty(studentIdentityCardPreferences.getLetterHead2Color()) ?
                EColorUtils.hex2Rgb(studentIdentityCardPreferences.getLetterHead2Color()) : null;
        rgbLetterHead2Color = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);

        Float rgbLetterHead2FontSize = studentIdentityCardPreferences != null &&
                studentIdentityCardPreferences.getLetterHead2FontSize() != null ?
                studentIdentityCardPreferences.getLetterHead2FontSize() : 10f;
        rgbLetterHead2FontSize = 7f;

//        addRow(table, documentLayoutSetup, Arrays.asList(
//                getParagraph("Ph: **********, **********", rgbLetterHead2Color)), centerCellLayoutSetup.copy().setPdfFont(cambriaFont)
//                .setFontSize(rgbLetterHead2FontSize).setPaddingTop(0f).setPaddingBottom(2f));

        addRow(table, documentLayoutSetup, Arrays.asList(
                getParagraph("**********, **********", rgbLetterHead2Color)), centerCellLayoutSetup.copy().setPdfFont(cambriaFont)
                .setFontSize(rgbLetterHead2FontSize).setPaddingTop(0f).setPaddingBottom(2f));

        document.add(table);

        generateDynamicImageProvider(documentLayoutData, -8, 2, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);


        List<Integer> rgbSessionColor = studentIdentityCardPreferences != null &&
                !StringUtils.isEmpty(studentIdentityCardPreferences.getInstituteNameColor()) ?
                EColorUtils.hex2Rgb(studentIdentityCardPreferences.getInstituteNameColor()) : null;
        rgbSessionColor = EColorUtils.hex2Rgb(EColorUtils.WHITE_COLOR_HEX_CODE);

        Float rgbSessionFontSize = studentIdentityCardPreferences != null &&
                studentIdentityCardPreferences.getInstituteNameFontSize() != null ?
                studentIdentityCardPreferences.getInstituteNameFontSize() : 10f;
        rgbSessionFontSize = 7f;
        String sessionBackgroundColor = "#418e45";

        table = getPDFTable(documentLayoutSetup, 2);
        centerCellLayoutSetup = new CellLayoutSetup();
        centerCellLayoutSetup.setPdfFont(cambriaFont).setTextAlignment(TextAlignment.LEFT)
                .setFontSize(rgbSessionFontSize).setPaddingBottom(0f).setPaddingTop(0f);
        centerCellLayoutSetup.setBackgroundColor(sessionBackgroundColor);

        Paragraph identityCardText = getParagraph("IDENTITY CARD", rgbSessionColor);

        Paragraph session = getParagraph(student.getStudentAcademicSessionInfoResponse()
                .getAcademicSession().getYearDisplayName(), rgbSessionColor);

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(identityCardText, centerCellLayoutSetup.copy().setPaddingLeft(10f)),
            new CellData(session, centerCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT).setPaddingRight(10f))));

        document.add(table);

    }

    protected DocumentLayoutData generateIdentityCardLayoutData(Institute institute,
                                                                StudentIdentityCardPreferences studentIdentityCardPreferences, DocumentOutput documentOutput)
            throws IOException {

        /**
         * 	aadhar card size - 8.5cmx5.5cm
         * 	in inches - 3.34646x2.16535
         * 	1 inch  = 72 points
         * 	3.34646*72 & 2.16535*72
         * 	240.94512f X 155.9052f
         */
        PageSize pageSize = new PageSize(241f, 156f);
        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize);
        float contentFontSize = 9f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

        float logoWidth = studentIdentityCardPreferences != null && studentIdentityCardPreferences.getInstituteLogoWidth() != null
                ? studentIdentityCardPreferences.getInstituteLogoWidth() : 35f;
        logoWidth = 35f;
        float logoHeight = studentIdentityCardPreferences != null && studentIdentityCardPreferences.getInstituteLogoHeight() != null
                ? studentIdentityCardPreferences.getInstituteLogoHeight() : 35f;
        logoHeight = 35f;

        int instituteId = institute.getInstituteId();
        DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(),
                getRegularBoldFont(),
                contentFontSize, 0f, logoWidth, logoHeight,
                LogoProvider.INSTANCE.getLogo(instituteId),
                null, null);

        documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
        return documentLayoutData;
    }

    public DocumentLayoutSetup initDocumentLayoutSetup(PageSize defaultPageSize) {
        return initDocumentLayoutSetup(false, defaultPageSize,
                0f, 0f, 0f, 0f);
    }

    @Override
    public DocumentOutput generateIdentityCards(StudentManager studentManager, Institute institute, List<StudentTransportDetails> studentTransportDetails,
                                                StudentIdentityCardPreferences studentIdentityCardPreferences, List<Student> students,
                                                String documentName, StaffManager staffManager) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute,
                    studentIdentityCardPreferences, documentOutput);
            Document document = documentLayoutData.getDocument();

            PdfFont cambriaBoldFont = getCambriaBoldFont();
            PdfFont cambriaFont = getCambriaFont();

            int pageNumber = 1;
            for (Student student : students) {

                generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                        documentLayoutData.getDocumentLayoutSetup(), institute, student, studentIdentityCardPreferences, studentManager,
                        pageNumber);

                if (pageNumber != students.size()) {
                    documentLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;

            }

            documentLayoutData.getDocument().close();

            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating student identity card institute {}", institute.getInstituteId(), e);
        }
        return null;
    }
}
