package com.lernen.cloud.pdf.identitycard.student;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.lernen.cloud.core.api.configurations.StudentIdentityCardPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.AddressUtils;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;

import com.lernen.cloud.core.utils.EColorUtils;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentIdentityCardGeneratorV3_10180 extends GlobalStudentIdentityCardGenerator {

    public StudentIdentityCardGeneratorV3_10180(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final Logger logger = LogManager.getLogger(StudentIdentityCardGeneratorV3_10180.class);

    @Override
    public DocumentOutput generateIdentityCard(StudentManager studentManager, Institute institute, StudentTransportDetails studentTransportDetails,
                                               StudentIdentityCardPreferences studentIdentityCardPreferences, Student student, String documentName, StaffManager staffManager) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute,studentIdentityCardPreferences, documentOutput);
            Document document = documentLayoutData.getDocument();

            PdfFont cambriaBoldFont = getCambriaBoldFont();
            PdfFont cambriaFont = getCambriaFont();

            generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                    documentLayoutData.getDocumentLayoutSetup(), institute, student, studentManager, 1);

            document.close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating idenity cards for institute {}, student {}",
                    institute.getInstituteId(), student.getStudentId(), e);
        }
        return null;
    }

    private int generateIdentityCardDetails(Document document, DocumentLayoutData documentLayoutData,
                                            PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup, Institute institute,
                                            Student student, StudentManager studentManager, int pageNumber) throws IOException {

        /**
         * Student Details Front
         */
        generateStudentFrontPageDetails(document, documentLayoutData, institute, pageNumber, studentManager,
                cambriaFont, cambriaBoldFont, documentLayoutSetup, student);

        /**
         * Add new page
         */
        document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
        pageNumber++;

        /**
         * Student Details Back
         */
        generateStudentBackDetails(document, documentLayoutData, pageNumber,
                cambriaFont, cambriaBoldFont, documentLayoutSetup, institute, studentManager, student);

        return pageNumber;

    }

    private void generateStudentBackDetails(Document document, DocumentLayoutData documentLayoutData,
                                            int pageNumber, PdfFont cambriaFont, PdfFont cambriaBoldFont,
                                            DocumentLayoutSetup documentLayoutSetup, Institute institute,
                                            StudentManager studentManager, Student student) throws IOException {

        
        float bgImageWidth = documentLayoutSetup.getPageSize().getWidth();
        float bgImageHeight = documentLayoutSetup.getPageSize().getHeight();
        /**
         * School instruction image
         */
        generateImage(document, documentLayoutSetup,
            //When I Card needs to be enabled uncomment this code
//                ImageProvider.INSTANCE.getImage(ImageProvider._10180_STUDENT_ICARD_BACK_PAGE),
            //When Outpass needs to be enabled uncomment this code
            ImageProvider.INSTANCE.getImage(ImageProvider._10180_OUTPASS_BACK_PAGE),
                bgImageWidth, bgImageHeight, 0, 0);


    }


    private void generateStudentFrontPageDetails(Document document, DocumentLayoutData documentLayoutData,
                                                 Institute institute, int pageNumber, StudentManager studentManager,
                                                 PdfFont cambriaFont, PdfFont cambriaBoldFont,
                                                 DocumentLayoutSetup documentLayoutSetup, Student student) throws IOException {
        
        float bgImageWidth = documentLayoutSetup.getPageSize().getWidth();
        float bgImageHeight = documentLayoutSetup.getPageSize().getHeight();
        generateImage(document, documentLayoutSetup,
                //When I Card needs to be enabled uncomment this code
//                ImageProvider.INSTANCE.getImage(ImageProvider._10180_STUDENT_ICARD_FRONT_PAGE),
                //When Outpass needs to be enabled uncomment this code
                ImageProvider.INSTANCE.getImage(ImageProvider._10180_OUTPASS_FRONT_PAGE),
        bgImageWidth, bgImageHeight,0f, 0f);

        /**
         * Student Image Header
         */
        generateStudentImage(document, documentLayoutSetup,documentLayoutData, institute, studentManager, student);

        /**
         * Student basic details
         */
        generateStudentBasicDetails(document, cambriaBoldFont, cambriaFont, documentLayoutSetup, student, documentLayoutData);

       
    }


    private void generateStudentBasicDetails(Document document,
                                             PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
                                             Student student,DocumentLayoutData documentLayoutData) {

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(documentLayoutData.getContentFontSize()-3.5f)
                .setTextAlignment(TextAlignment.LEFT).setPaddingLeft(10f).setPaddingTop(0f).setPaddingBottom(0f);


        CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();

        centerCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER).setFontSize(documentLayoutData.getContentFontSize()+1f);

        Table studentNameTable = getPDFTable(documentLayoutSetup, 1);

        addRow(studentNameTable, documentLayoutSetup, Arrays.asList(
                getParagraph(student.getStudentBasicInfo().getName())),centerCellLayoutSetup);
        
        studentNameTable.setFixedPosition(0, 75f, documentLayoutSetup.getPageSize().getWidth()-10f);

        document.add(studentNameTable);

        Table table = getPDFTable(documentLayoutSetup, new float [] { 0.32f, 0.68f});

        addBlankLine(document,false,1);

        String fatherName = student.getStudentFamilyInfo().getFathersName();
        if(StringUtils.isEmpty(fatherName)){
        fatherName = "";
        }
        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(getParagraph("Father's Name"),
                        cellLayoutSetup.copy().setPdfFont(cambriaBoldFont).setVerticalAlignment(VerticalAlignment.TOP)),
                new CellData(getParagraph(": "+fatherName),
                        cellLayoutSetup.copy().setPdfFont(cambriaFont))
        ));

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(getParagraph("Class"),
                        cellLayoutSetup.copy().setPdfFont(cambriaBoldFont)),
                new CellData(getParagraph(": "+student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection()),
                        cellLayoutSetup.copy().setPdfFont(cambriaFont))
                ));

        String contactNo = student.getStudentBasicInfo().getPrimaryContactNumber();
        if(StringUtils.isEmpty(contactNo)){
        contactNo = "";
        }
        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(getParagraph("Contact No."),
                        cellLayoutSetup.copy().setPdfFont(cambriaBoldFont)),
                new CellData(getParagraph(": "+contactNo),
                        cellLayoutSetup.copy().setPdfFont(cambriaFont))
                ));

        String peresentAddress = AddressUtils.getStudentAddress(student.getStudentBasicInfo().getPresentAddress(), null, student.getStudentBasicInfo().getPresentState(), null);
        String address = StringUtils.isBlank(peresentAddress) ? AddressUtils.getStudentAddress(student.getStudentBasicInfo().getPermanentAddress(), null, student.getStudentBasicInfo().getPermanentState(), null) : peresentAddress;
        if(StringUtils.isEmpty(address)){
            address = "";
        }

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(getParagraph("Address"),
                        cellLayoutSetup.copy().setPdfFont(cambriaBoldFont).setVerticalAlignment(VerticalAlignment.TOP)),
                new CellData(getParagraph(": "+address),
                        cellLayoutSetup.copy().setPdfFont(cambriaFont))
              ));
        table.setFixedPosition(0, 26f, documentLayoutSetup.getPageSize().getWidth());
        document.add(table);
    }

    protected void generateStudentImage(Document document, DocumentLayoutSetup documentLayoutSetup, DocumentLayoutData documentLayoutData,
                                        Institute institute, StudentManager studentManager, Student student) throws MalformedURLException {

        byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
        if (image != null) {
        float width = 64.45f;
        float height = 74.25f;

            //When I Card needs to be enabled uncomment this code
//        generateImage(document, documentLayoutSetup, image, width, height, documentLayoutSetup.getPageSize().getWidth() - 110.30f,
//                92.75f);
            //When Outpass needs to be enabled uncomment this code
            generateImage(document, documentLayoutSetup, image, width, height, documentLayoutSetup.getPageSize().getWidth() - 112.50f,
                    92.75f);
        }
    }

    protected DocumentLayoutData generateIdentityCardLayoutData(Institute institute, StudentIdentityCardPreferences studentIdentityCardPreferences, DocumentOutput documentOutput)
            throws IOException {

        /**
         * 	aadhar card size - 8.5cmx5.5cm
         * 	in inches - 3.34646x2.16535
         * 	1 inch  = 72 points
         * 	3.34646*72 & 2.16535*72
         * 	240.94512f X 155.9052f
         */

        PageSize pageSize = new PageSize(155.9052f, 240.94512f);
        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize);
        float contentFontSize = 9f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);


      
        DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(),
                getRegularBoldFont(),
                contentFontSize, 0f, 0f, 0f,
                null,
                null, null);

        return documentLayoutData;
    }

    public DocumentLayoutSetup initDocumentLayoutSetup(PageSize defaultPageSize) {
        return initDocumentLayoutSetup(false, defaultPageSize,
                0f, 0f, 0f, 0f);
    }

    @Override
    public DocumentOutput generateIdentityCards(StudentManager studentManager, Institute institute, List<StudentTransportDetails> studentTransportDetails,
                                                StudentIdentityCardPreferences studentIdentityCardPreferences, List<Student> students,
                                                String documentName, StaffManager staffManager) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute,studentIdentityCardPreferences, documentOutput);
            Document document = documentLayoutData.getDocument();

            PdfFont cambriaBoldFont = getCambriaBoldFont();
            PdfFont cambriaFont = getCambriaFont();

            int pageNumber = 1;
            for (Student student : students) {

                pageNumber = generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                        documentLayoutData.getDocumentLayoutSetup(), institute, student, studentManager, pageNumber);

                if (pageNumber != students.size() * 2) {
                    documentLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;

            }
            documentLayoutData.getDocument().close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating student identity card institute {}", institute.getInstituteId(), e);
        }
        return null;
    }

}
