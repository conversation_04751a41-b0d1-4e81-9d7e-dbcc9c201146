package com.lernen.cloud.pdf.identitycard.student;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.configurations.StudentIdentityCardPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.lib.transport.configuration.TransportAssignmentManager;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
public class StudentIdentityCardHandler {

    private static final Logger logger = LogManager.getLogger(StudentIdentityCardHandler.class);

    private final StudentIdentityCardGeneratorFactory identityCardGeneratorFactory;
    private final InstituteManager instituteManager;
    private final StudentManager studentManager;
    private final UserPreferenceSettings userPreferenceSettings;
    private final UserPermissionManager userPermissionManager;
    private final StaffManager staffManager;
    private final TransportAssignmentManager transportAssignmentManager;

    public StudentIdentityCardHandler(InstituteManager instituteManager, StudentManager studentManager,
                                      UserPreferenceSettings userPreferenceSettings, UserPermissionManager userPermissionManager, StaffManager staffManager, TransportAssignmentManager transportAssignmentManager, AssetProvider assetProvider) {
        this.identityCardGeneratorFactory = new StudentIdentityCardGeneratorFactory(assetProvider);
        this.instituteManager = instituteManager;
        this.studentManager = studentManager;
        this.userPreferenceSettings = userPreferenceSettings;
        this.userPermissionManager = userPermissionManager;
        this.staffManager = staffManager;
        this.transportAssignmentManager = transportAssignmentManager;
    }

    public DocumentOutput generateIdentityCard(int instituteId, int academicSessionId, UUID studentId, UUID userId) {
        if (instituteId <= 0 || academicSessionId <= 0 || studentId == null || userId == null) {
            logger.error("Invalid institute id or academicSessionId or studentId");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS,
                    "Invalid institute id or academicSessionId or studentId"));
        }

        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.GENERATE_STUDENT_IDENTITY_CARD);

        Student student = studentManager.getStudentByAcademicSessionStudentId(instituteId, academicSessionId,
                studentId);

        if (student == null) {
            logger.info(
                    "No student found in instituteId {}, session {}, studentId {}. Skipping idenity card generation.",
                    instituteId, academicSessionId, studentId);
            return null;
        }
        StudentIdentityCardPreferences studentIdentityCardPreferences = userPreferenceSettings
                .getStudentIdentityCardPreferences(instituteId);
        StudentIdentityCardGenerator identityCardGenerator = identityCardGeneratorFactory
                .getIdentityCardGenerator(instituteId);

        String fileName = student.getStudentBasicInfo().getName() + " - Identity Card" + ".pdf";

        StudentTransportDetails studentTransportDetails = transportAssignmentManager.getStudentCurrentTransportDetails(instituteId, academicSessionId,studentId);

        return identityCardGenerator.generateIdentityCard(studentManager, instituteManager.getInstitute(instituteId), studentTransportDetails,
                studentIdentityCardPreferences, student, fileName, staffManager);
    }

    public DocumentOutput generateIdentityCards(int instituteId, int academicSessionId, UUID standardId, Set<Integer> sectionIds,
                                                UUID userId) {

        List<Student> students = studentManager.getClassStudents(instituteId, academicSessionId, standardId, sectionIds);
        List<StudentTransportDetails> studentTransportDetails = new ArrayList<>();
        //TODO: we have to optimise It as it will bring all the student details of the institute
        Map<UUID, StudentTransportDetails> allStudentActiveTransportAssignmentDetails = transportAssignmentManager.getAllStudentActiveTransportAssignmentDetails(instituteId, academicSessionId);
        for(Student student : students){
                studentTransportDetails.add(allStudentActiveTransportAssignmentDetails.get(student.getStudentId()));
        }
        if (CollectionUtils.isEmpty(students)) {
            logger.info(
                    "No student found in instituteId {}, session {}, standard {}. Skipping idenity card generation.",
                    instituteId, academicSessionId, standardId);
            return null;
        }
        logger.info("Generating identity cards for instituteId {}, session {}, standard {} , student count {}",
                instituteId, academicSessionId, standardId, students.size());
        StudentIdentityCardGenerator identityCardGenerator = identityCardGeneratorFactory
                .getIdentityCardGenerator(instituteId);

        StudentIdentityCardPreferences studentIdentityCardPreferences = userPreferenceSettings
                .getStudentIdentityCardPreferences(instituteId);

        Standard standard = students.get(0).getStudentAcademicSessionInfoResponse().getStandard();
        String fileName = standard.getStandardName() + " - Identity Cards" + ".pdf";

        return identityCardGenerator.generateIdentityCards(studentManager, instituteManager.getInstitute(instituteId), studentTransportDetails,
                studentIdentityCardPreferences, students, fileName, staffManager);
    }

    public DocumentOutput generateIdentityCards(int instituteId, int academicSessionId, List<UUID> studentIds, UUID userId) {
        if (instituteId <= 0 || academicSessionId <= 0 || userId == null) {
            logger.error("Invalid institute id or academicSessionId");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS,
                    "Invalid institute id or academicSessionId"));
        }

        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.GENERATE_BULK_STUDENT_IDENTITY_CARD);

        List<Student> students = studentManager.getStudentByAcademicSessionStudentIds(instituteId, academicSessionId, studentIds);
        List<StudentTransportDetails> studentTransportDetails = new ArrayList<>();
        //TODO: we have to optimise It as it will bring all the student details of the institute
        Map<UUID, StudentTransportDetails> allStudentActiveTransportAssignmentDetails = transportAssignmentManager.getAllStudentActiveTransportAssignmentDetails(instituteId, academicSessionId);
        for(Student student : students){
                studentTransportDetails.add(allStudentActiveTransportAssignmentDetails.get(student.getStudentId()));
        }
        if (CollectionUtils.isEmpty(students)) {
            logger.info(
                    "No student found in instituteId {}, session {}. Skipping idenity card generation.",
                    instituteId, academicSessionId);
            return null;
        }
        logger.info("Generating identity cards for instituteId {}, session {} , student count {}",
                instituteId, academicSessionId, students.size());
        StudentIdentityCardGenerator identityCardGenerator = identityCardGeneratorFactory
                .getIdentityCardGenerator(instituteId);

        StudentIdentityCardPreferences studentIdentityCardPreferences = userPreferenceSettings
                .getStudentIdentityCardPreferences(instituteId);

        Standard standard = students.get(0).getStudentAcademicSessionInfoResponse().getStandard();
        String fileName = standard.getStandardName() + " - Identity Cards" + ".pdf";

        return identityCardGenerator.generateIdentityCards(studentManager, instituteManager.getInstitute(instituteId), studentTransportDetails,
                studentIdentityCardPreferences, students, fileName, staffManager);
    }

}
