package com.lernen.cloud.pdf.identitycard.student._10030_10031;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.StudentIdentityCardPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.AddressUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.identitycard.student.GlobalStudentIdentityCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentIdentityCardGeneratorV1_10030_10031 extends GlobalStudentIdentityCardGenerator {

	public StudentIdentityCardGeneratorV1_10030_10031(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(StudentIdentityCardGeneratorV1_10030_10031.class);

	@Override
	public DocumentOutput generateIdentityCard(StudentManager studentManager, Institute institute, StudentTransportDetails studentTransportDetails,
											   StudentIdentityCardPreferences studentIdentityCardPreferences, Student student, String documentName, StaffManager staffManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute, documentOutput, studentIdentityCardPreferences);
			Document document = documentLayoutData.getDocument();

			PdfFont cambriaBoldFont = getCambriaBoldFont();
			PdfFont cambriaFont = getCambriaFont();

			generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
					documentLayoutData.getDocumentLayoutSetup(), institute, student, studentIdentityCardPreferences, studentManager, 1);
			document.close();
			return getA4PortraitIdentityCard(documentName, documentOutput, PageSize.A4, 3, 3, 20f);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating identity cards for institute {}, student {}",
					institute.getInstituteId(), student.getStudentId(), e);
		}
		return null;
	}

	private void generateIdentityCardDetails(Document document, DocumentLayoutData documentLayoutData,
											 PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup, Institute institute, Student student,
											 StudentIdentityCardPreferences studentIdentityCardPreferences, StudentManager studentManager, int pageNumber) throws IOException {

		/**
		 * Student Details Front
		 */
		generateStudentFrontPageDetails(document, documentLayoutData, institute, pageNumber, studentManager,
				cambriaFont, cambriaBoldFont, documentLayoutSetup, student, studentIdentityCardPreferences);
	}

	private void generateStudentFrontPageDetails(Document document, DocumentLayoutData documentLayoutData,
												 Institute institute, int pageNumber, StudentManager studentManager,
												 PdfFont cambriaFont, PdfFont cambriaBoldFont,
												 DocumentLayoutSetup documentLayoutSetup, Student student,
												 StudentIdentityCardPreferences studentIdentityCardPreferences) throws IOException {

		/**
		 * Bottom bar, keep this on top as we are
		 * using canvas in generateInstituteHeader which
		 * require document to be created before it,
		 * ow error occurred
		 */
		generateBottomBar(document, cambriaBoldFont, documentLayoutSetup, studentIdentityCardPreferences);

		/**
		 * Institute Header
		 */
		generateFrontPageHeader(document, documentLayoutData, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute, pageNumber, student, studentManager, studentIdentityCardPreferences);

		/**
		 * Student basic details
		 */
		generateStudentBasicDetails(document, cambriaBoldFont, cambriaFont, documentLayoutSetup, student, studentIdentityCardPreferences);

		generateBarCodeImage(document, documentLayoutSetup);
	}

	private void generateBarCodeImage(Document document, DocumentLayoutSetup documentLayoutSetup) throws IOException {

		byte[] image = ImageProvider.INSTANCE.getImage(ImageProvider._10030_10031_STUDENT_ICARD_BARCODE);
		if (image == null) {
			return;
		}

		float imageWidth = documentLayoutSetup.getPageSize().getWidth() - 10f;
		float imageHeight = 16f;

		float imageOffsetX = 5f;
		float imageOffsetY = 23f;

		generateImage(document, documentLayoutSetup, image, imageWidth, imageHeight, imageOffsetX, imageOffsetY);

	}

	private void generateStudentBasicDetails(Document document,
											 PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
											 Student student, StudentIdentityCardPreferences studentIdentityCardPreferences) {

		Table table = getPDFTable(documentLayoutSetup, 1);

		float subToTop = 0;

		if(!isAddressOneLine(student)) {
			subToTop += 11;
		}

		if(!isFatherNameOneLine(student)) {
			subToTop += 11;
		}

		table.setFixedPosition(0, 62 - subToTop, documentLayoutSetup.getPageSize().getWidth());


		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(7f)
				.setTextAlignment(TextAlignment.CENTER).setPaddingTop(0f).setPaddingBottom(0f);

		List<Integer> rgb = studentIdentityCardPreferences != null &&
				!StringUtils.isEmpty(studentIdentityCardPreferences.getiCardDetailsTextColor()) ?
				EColorUtils.hex2Rgb(studentIdentityCardPreferences.getiCardDetailsTextColor()) : null;

		Paragraph studentNameParagraph = getParagraph(student.getStudentBasicInfo().getName().toUpperCase(), rgb);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(studentNameParagraph,
				cellLayoutSetup.copy().setFontSize(8f).setPdfFont(cambriaBoldFont))));


		cellLayoutSetup.setPaddingLeft(0f).setTextAlignment(TextAlignment.LEFT)
				.setPaddingLeft(15f).setPaddingRight(15f);

		Paragraph admissionNumberKeyValue = getKeyValueParagraph("Admission No. : ",
				student.getStudentBasicInfo().getAdmissionNumber(), rgb, rgb, cambriaBoldFont, cambriaFont);
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(admissionNumberKeyValue, cellLayoutSetup.copy())));

		Paragraph fatherNameKeyValue = getKeyValueParagraph("Father’s Name : ",
				student.getStudentFamilyInfo().getFathersName(), rgb, rgb, cambriaBoldFont, cambriaFont);
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(fatherNameKeyValue, cellLayoutSetup.copy())));

		Paragraph phoneNumberKeyValue = getKeyValueParagraph("Phone : ",
				student.getStudentBasicInfo().getPrimaryContactNumber(), rgb, rgb, cambriaBoldFont, cambriaFont);
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(phoneNumberKeyValue, cellLayoutSetup.copy())));

		Paragraph addressKeyValue = getKeyValueParagraph("Address : ",
				AddressUtils.getStudentAddress(student), rgb, rgb, cambriaBoldFont, cambriaFont);
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(addressKeyValue, cellLayoutSetup.copy())));

		document.add(table);
	}

	private boolean isAddressOneLine(Student student) {
		String address = AddressUtils.getStudentAddress(student);
		if(StringUtils.isEmpty(address)) {
			return true;
		}
		//36 charaters will come in one line
		return address.length() > 35 ? false : true;
	}

	private boolean isFatherNameOneLine(Student student) {
		String fatherName = student.getStudentFamilyInfo() == null ? "" : student.getStudentFamilyInfo().getFathersName();
		if(StringUtils.isEmpty(fatherName)) {
			return true;
		}
		//36 charaters will come in one line
		return fatherName.length() > 20 ? false : true;
	}

	protected void generateBottomBar(Document document, PdfFont cambriaBoldFont, DocumentLayoutSetup documentLayoutSetup,
								   StudentIdentityCardPreferences studentIdentityCardPreferences) {

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup bottomBarCellLayoutSetup = new CellLayoutSetup();
		bottomBarCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER)
				.setFontSize(6f).setPaddingTop(0f).setPaddingBottom(0f);

		if(studentIdentityCardPreferences != null && !StringUtils.isEmpty(studentIdentityCardPreferences.getFooterBarColor())) {
			bottomBarCellLayoutSetup.setBackgroundColor(studentIdentityCardPreferences.getFooterBarColor());
		}

		List<Integer> rgb = studentIdentityCardPreferences != null &&
				!StringUtils.isEmpty(studentIdentityCardPreferences.getInstituteNameColor()) ?
				EColorUtils.hex2Rgb(studentIdentityCardPreferences.getInstituteNameColor()) : null;

		table.setFixedPosition(0f, 0f, documentLayoutSetup.getPageSize().getWidth());
		addRow(table, documentLayoutSetup, Arrays.asList(
				getParagraph("Mo. - 9350545657, 9999043452", rgb)), bottomBarCellLayoutSetup.copy().setPaddingTop(1f));
		addRow(table, documentLayoutSetup, Arrays.asList(
				getParagraph("Email - <EMAIL>", rgb)), bottomBarCellLayoutSetup.copy().setPaddingBottom(1f));
		document.add(table);
	}

	private void generateFrontPageHeader(Document document,
										 DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
										 DocumentLayoutSetup documentLayoutSetup, Institute institute, int pageNumber,
										 Student student, StudentManager studentManager,
										 StudentIdentityCardPreferences studentIdentityCardPreferences) throws IOException {

		generateBackgroundCanvas(document, documentLayoutSetup, pageNumber, cambriaBoldFont, cambriaFont, student);

		generateStudentImage(document, documentLayoutSetup, institute, studentManager, student, studentIdentityCardPreferences);

		generateInstituteDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute, studentIdentityCardPreferences);

		addClassDetails(document, documentLayoutSetup, pageNumber, cambriaBoldFont, cambriaFont, student);

		addSessionDetails(document, documentLayoutSetup, pageNumber, cambriaBoldFont, cambriaFont, student);

	}

	private void generateInstituteDetails(Document document,
										  DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
										  DocumentLayoutSetup documentLayoutSetup, Institute institute,
										  StudentIdentityCardPreferences studentIdentityCardPreferences) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();
		centerCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.LEFT)
				.setPaddingLeft(50f).setPaddingBottom(0f).setPaddingTop(0f);

		if(studentIdentityCardPreferences != null && !StringUtils.isEmpty(studentIdentityCardPreferences.getHeaderBackgroundColor())) {
			centerCellLayoutSetup.setBackgroundColor(studentIdentityCardPreferences.getHeaderBackgroundColor());
		}

		List<Integer> rgb = studentIdentityCardPreferences != null &&
				!StringUtils.isEmpty(studentIdentityCardPreferences.getInstituteNameColor()) ?
				EColorUtils.hex2Rgb(studentIdentityCardPreferences.getInstituteNameColor()) : null;

		List<Integer> blackRGB = studentIdentityCardPreferences != null &&
				!StringUtils.isEmpty(studentIdentityCardPreferences.getiCardDetailsTextColor()) ?
				EColorUtils.hex2Rgb(studentIdentityCardPreferences.getiCardDetailsTextColor()) : null;

		Float rgbInstituteNameFontSize = studentIdentityCardPreferences != null &&
				studentIdentityCardPreferences.getInstituteNameFontSize() != null ?
				studentIdentityCardPreferences.getInstituteNameFontSize() : 10f;
		Float rgbLetterHead1FontSize = studentIdentityCardPreferences != null &&
				studentIdentityCardPreferences.getLetterHead1FontSize() != null ?
				studentIdentityCardPreferences.getLetterHead1FontSize() : 7f;
		Float rgbLetterHead2FontSize = studentIdentityCardPreferences != null &&
				studentIdentityCardPreferences.getLetterHead2FontSize() != null ?
				studentIdentityCardPreferences.getLetterHead2FontSize() : 7f;
		if(institute.getInstituteId() == 10030) {
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""),
							getParagraph("ADARSH VIDYA".toUpperCase(), rgb)),
					centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize));
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""),
							getParagraph("MANDIR".toUpperCase(),rgb)),
					centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize));
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""),
							getParagraph("Piyawali (G.B. Nagar)", blackRGB)),
					centerCellLayoutSetup.copy().setFontSize(rgbLetterHead1FontSize));
		} else if (institute.getInstituteId() == 10031) {
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""),
							getParagraph("ADARSH INTER".toUpperCase(),rgb)),
					centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize));
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""),
							getParagraph("COLLEGE".toUpperCase(),rgb)),
					centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize));
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""),
							getParagraph("Piyawali (G.B. Nagar)", blackRGB)),
					centerCellLayoutSetup.copy().setFontSize(rgbLetterHead1FontSize));
		} else {
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""),
							getParagraph("EMBRATE".toUpperCase(),rgb)),
					centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize));
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""),
							getParagraph("NATIONAL SCHOOL".toUpperCase(),rgb)),
					centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize));
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""),
							getParagraph("Jaipur, Rajasthan", blackRGB)),
					centerCellLayoutSetup.copy().setFontSize(rgbLetterHead1FontSize));
		}

		document.add(table);

		generateDynamicImageProvider(documentLayoutData, -8, 4, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
	}

	private void generateBackgroundCanvas(Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber,
										  PdfFont cambriaBoldFont, PdfFont cambriaFont, Student student) {

		generateBackgroundRectangle(document, documentLayoutSetup, pageNumber);

		generateImageBackground(document, documentLayoutSetup, pageNumber);

	}

	private void addSessionDetails(Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber,
								   PdfFont cambriaBoldFont, PdfFont cambriaFont, Student student) {

		Table table = getPDFTable(documentLayoutSetup, 1);
		float tableWidth = (float) (documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.70)));
		table.setFixedPosition(115, 155, tableWidth);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(7f)
				.setTextAlignment(TextAlignment.LEFT).setPaddingTop(0f).setPaddingBottom(0f);

		List<Integer> rgb = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);
		Paragraph classText = getParagraph("Session", rgb, cambriaBoldFont);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(classText, cellLayoutSetup.copy())));

		Paragraph classValueText = getParagraph(student.getStudentAcademicSessionInfoResponse().getAcademicSession().getShortYearDisplayName(),
				rgb, cambriaFont);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(classValueText, cellLayoutSetup.copy())));

		document.add(table);

	}

	private void addClassDetails(Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber,
								 PdfFont cambriaBoldFont, PdfFont cambriaFont, Student student) {

		Table table = getPDFTable(documentLayoutSetup, 1);
		float tableWidth = (float) (documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.70)));
		table.setFixedPosition(15, 155, tableWidth);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(7f)
				.setTextAlignment(TextAlignment.LEFT).setPaddingTop(0f).setPaddingBottom(0f);

		List<Integer> rgb = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);
		Paragraph classText = getParagraph("Class", rgb, cambriaBoldFont);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(classText, cellLayoutSetup.copy())));

		String standardName = student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection();

		/**
		 *	Check for 11th class of institute 10031,
		 * 	so that it won't overlap with the image of student
		 */
		List<UUID> standardsIds = new ArrayList<UUID>();
		standardsIds.add(UUID.fromString("70faf4c8-47f8-4e69-9e91-61c55c2d6df3"));
		standardsIds.add(UUID.fromString("ad079d09-855b-4696-b007-b3b6e34163b8"));
		if(standardsIds.contains(student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId())) {
			standardName = student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayName();
		}

		Paragraph classValueText = getParagraph(standardName, rgb, cambriaFont);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(classValueText, cellLayoutSetup.copy())));

		document.add(table);

	}

	private void generateImageBackground(Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber) {

		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		Color color = WebColors.getRGBColor("#f9cb9c");
		canvas.setFillColor(color);
		canvas.setStrokeColor(color);

		canvas.moveTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.70)), documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.30)), documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.30)), documentLayoutSetup.getPageSize().getHeight()
				- (documentLayoutSetup.getPageSize().getHeight() / 2));
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.70)), documentLayoutSetup.getPageSize().getHeight()
				- (documentLayoutSetup.getPageSize().getHeight() / 2));
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.70)), documentLayoutSetup.getPageSize().getHeight());

		canvas.setLineWidth(5f);
		canvas.fill();
		canvas.closePathStroke();
	}

	private void generateBackgroundRectangle(Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber) {
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);

		Color color = WebColors.getRGBColor("#f9cb9c");
		canvas.setFillColor(color);
		canvas.setStrokeColor(color);

		canvas.moveTo(0, documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth(), documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth(), documentLayoutSetup.getPageSize().getHeight()
				- ((documentLayoutSetup.getPageSize().getHeight() / 2) - (documentLayoutSetup.getPageSize().getHeight() / 8)));
		canvas.lineTo(0, documentLayoutSetup.getPageSize().getHeight()
				- ((documentLayoutSetup.getPageSize().getHeight() / 2) - (documentLayoutSetup.getPageSize().getHeight() / 8)));
		canvas.lineTo(0, documentLayoutSetup.getPageSize().getHeight());
		canvas.setLineWidth(5f);
		canvas.fill();
		canvas.closePathStroke();
	}

	private void generateStudentImage(Document document, DocumentLayoutSetup documentLayoutSetup,
									  Institute institute, StudentManager studentManager, Student student,
									  StudentIdentityCardPreferences studentIdentityCardPreferences) throws MalformedURLException {

		byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
		if (image == null) {
			return;
		}

		float imageWidth = (documentLayoutSetup.getPageSize().getWidth() / 2) - 26f;
		float imageHeight = (documentLayoutSetup.getPageSize().getWidth() / 2 - 10f);
		if(studentIdentityCardPreferences != null && studentIdentityCardPreferences.getStudentImageWidth() != null) {
			imageWidth = studentIdentityCardPreferences.getStudentImageWidth();
		}
		if(studentIdentityCardPreferences != null && studentIdentityCardPreferences.getStudentImageHeight() != null) {
			imageHeight = studentIdentityCardPreferences.getStudentImageHeight();
		}

		float imageOffsetX = documentLayoutSetup.getPageSize().getWidth()
				- (documentLayoutSetup.getPageSize().getWidth() * (0.70f)) + 5f;
		float imageOffsetY = documentLayoutSetup.getPageSize().getHeight()
				- ((documentLayoutSetup.getPageSize().getHeight() / 2)) + 5f;

		generateImage(document, documentLayoutSetup, image, imageWidth, imageHeight,
				imageOffsetX, imageOffsetY);

	}

	protected DocumentLayoutData generateIdentityCardLayoutData(Institute institute, DocumentOutput documentOutput,
																StudentIdentityCardPreferences studentIdentityCardPreferences)
			throws IOException {

		/**
		 * 	aadhar card size - 8.5cmx5.5cm
		 * 	in inches - 3.34646x2.16535
		 * 	1 inch  = 72 points
		 * 	3.34646*72 & 2.16535*72
		 * 	240.94512f X 155.9052f
		 */
		PageSize pageSize = new PageSize(155.9052f, 240.94512f);
		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize);
		float contentFontSize = 9f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

		float logoWidth = studentIdentityCardPreferences != null && studentIdentityCardPreferences.getInstituteLogoWidth() != null
				? studentIdentityCardPreferences.getInstituteLogoWidth() : 30f;
		float logoHeight = studentIdentityCardPreferences != null && studentIdentityCardPreferences.getInstituteLogoHeight() != null
				? studentIdentityCardPreferences.getInstituteLogoHeight() : 30f;

		DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(),
				getRegularBoldFont(),
				contentFontSize, 0f, logoWidth, logoHeight,
				LogoProvider.INSTANCE.getLogo(institute.getInstituteId()),
				null, null);

		documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
		return documentLayoutData;
	}

	public DocumentLayoutSetup initDocumentLayoutSetup(PageSize defaultPageSize) {
		return initDocumentLayoutSetup(false, defaultPageSize,
				0f, 0f, 0f, 0f);
	}

	@Override
	public DocumentOutput generateIdentityCards(StudentManager studentManager, Institute institute, List<StudentTransportDetails> studentTransportDetails,
												StudentIdentityCardPreferences studentIdentityCardPreferences, List<Student> students,
												String documentName, StaffManager staffManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute, documentOutput, studentIdentityCardPreferences);
			Document document = documentLayoutData.getDocument();

			PdfFont cambriaBoldFont = getCambriaBoldFont();
			PdfFont cambriaFont = getCambriaFont();

			int pageNumber = 1;
			for (Student student : students) {

				generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
						documentLayoutData.getDocumentLayoutSetup(), institute, student, studentIdentityCardPreferences,
						studentManager, pageNumber);

				if (pageNumber != students.size()) {
					documentLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}

			documentLayoutData.getDocument().close();

			return getA4PortraitIdentityCard(documentName, documentOutput, PageSize.A4, 3, 3, 20f);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating student identity card institute {}", institute.getInstituteId(), e);
		}
		return null;
	}
}
