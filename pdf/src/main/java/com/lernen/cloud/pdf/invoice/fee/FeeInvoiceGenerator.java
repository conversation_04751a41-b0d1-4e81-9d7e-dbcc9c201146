package com.lernen.cloud.pdf.invoice.fee;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.configurations.FeeInvoicePreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.FeePaymentInvoiceSummary;
import com.lernen.cloud.core.api.fees.payment.StudentFeesDetailsLite;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.pdf.base.PDFGenerator;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class FeeInvoiceGenerator extends PDFGenerator {

	public FeeInvoiceGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	public static final float[] DEFAULT_ACTIVE_FEE_HEAD_HEADER_WIDTH = { 0.06f, 0.35f, 0.2f, 0.16f, 0.23f };
	public static final float[] DEFAULT_CANCELLED_FEE_HEAD_HEADER_WIDTH = { 0.1f, 0.6f, 0.3f };
	public static final float[] DEFAULT_ACTIVE_MONEY_RECEIPT_FEE_HEAD_HEADER_WIDTH = { 0.1f, 0.6f, 0.3f };
	public static final String OFFICE_COPY_TEXT = "(Office Copy)";
	public static final String STUDENT_COPY_TEXT = "(Student Copy)";
	public static final String BANK_COPY_TEXT = "(Bank Copy)";

	public abstract DocumentOutput generateInvoice(Institute institute,
												   FeePaymentInvoiceSummary feePaymentInvoiceSummary, StudentTransportDetails studentTransportDetails,
												   String documentName, boolean officeCopy, UserType userType, StudentFeesDetailsLite studentFeesDetailsLite,
												   FeeInvoicePreferences feeInvoicePreferences);

	public abstract DocumentOutput generateBulkInvoices(Institute institute,
			List<FeePaymentInvoiceSummary> feePaymentInvoiceSummaryList, String documentName, boolean doubleCopy,
														FeeInvoicePreferences feeInvoicePreferences);

}
