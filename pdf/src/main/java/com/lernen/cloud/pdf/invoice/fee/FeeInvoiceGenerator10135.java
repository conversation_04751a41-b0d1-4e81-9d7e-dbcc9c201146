package com.lernen.cloud.pdf.invoice.fee;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.layout.LayoutArea;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.FeeInvoicePreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.*;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class FeeInvoiceGenerator10135 extends FeeInvoiceGenerator {

    public FeeInvoiceGenerator10135(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final String INVOICE_NOTE = "Fee once paid is not refundable or adjustable under any circumstances.";
    @Override
    public DocumentOutput generateInvoice(Institute institute,
                                          FeePaymentInvoiceSummary feePaymentInvoiceSummary, StudentTransportDetails studentTransportDetails, String documentName, boolean officeCopy, UserType userType, StudentFeesDetailsLite studentFeesDetailsLite, FeeInvoicePreferences feeInvoicePreferences) {


        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            if(userType == UserType.STUDENT) {
                officeCopy = false;
            }
            DocumentLayoutData documentLayoutData = generatefeeInvoiceLayoutData(institute, documentOutput);
            DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
            Document document = documentLayoutData.getDocument();

            int pageNumber = 1;
            boolean addWaterMark = true;
            generateInvoice(documentLayoutData, documentLayoutSetup, document, institute, feePaymentInvoiceSummary, pageNumber, addWaterMark, false);
            addWaterMark = false;
            LayoutArea currentArea = document.getRenderer().getCurrentArea();
            Rectangle rectangle = currentArea.getBBox();

            if(!officeCopy) {
                document.close();
                return documentOutput;
            }

            /**
             *  rectangle.getHeight() - gives position of last rectangle from bottom,
             *  so if position of last rectangle is below than the middle of the page + 20f
             *  adding next page
             */
            if(rectangle.getHeight() < (documentLayoutSetup.getPageSize().getHeight() / 2) + 20) {
                document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                addWaterMark = true;
                pageNumber++;
            } else {
                /**
                 *  adding newline for second slip
                 */
                while(rectangle.getHeight() > ((documentLayoutSetup.getPageSize().getHeight() / 2) - 20)) {
                    addBlankLine(document, false, 1);
                    currentArea = document.getRenderer().getCurrentArea();
                    rectangle = currentArea.getBBox();
                }
            }

            generateInvoice(documentLayoutData, documentLayoutSetup, document, institute, feePaymentInvoiceSummary, pageNumber, addWaterMark, true);
            document.close();
            return documentOutput;

        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    private void generateInvoice(DocumentLayoutData documentLayoutData, DocumentLayoutSetup documentLayoutSetup, Document document, Institute institute,
                                 FeePaymentInvoiceSummary feePaymentInvoiceSummary, int pageNumber, boolean addWaterMark, boolean isOfficeCopy) throws IOException {

        float contentFontSize = 10f;
        float defaultBorderWidth = 0.1f;
        float bgImageHeightWidth = 300f;
        float pageHeight = documentLayoutSetup.getPageSize().getHeight();
        if(addWaterMark) {

            generateBackgroundImage(document, documentLayoutSetup, WatermarkProvider.INSTANCE.getWatermark(
                            institute.getInstituteId()), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth - 150,
                    (bgImageHeightWidth - 240) + (pageHeight / 2));

            generateBackgroundImage(document, documentLayoutSetup, WatermarkProvider.INSTANCE.getWatermark(
                            institute.getInstituteId()), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth - 150,
                    (bgImageHeightWidth - 240));
        }

        if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
                .getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.CANCELLED) {
            generateCancelledWatermark(documentLayoutData, documentLayoutSetup, document, pageNumber, isOfficeCopy);
        }

        generateHeader(documentLayoutData, documentLayoutSetup, document, institute, 45f,
                documentLayoutSetup.getPageSize().getHeight() * 0.87f + 10f,
                defaultBorderWidth, feePaymentInvoiceSummary, pageNumber, isOfficeCopy);

        generateStudentInformation(document, documentLayoutSetup,
                contentFontSize, feePaymentInvoiceSummary, defaultBorderWidth);

        generateFeeContent(document, documentLayoutSetup,
                contentFontSize, feePaymentInvoiceSummary, defaultBorderWidth);

        generateFeePaymentSummary(document, documentLayoutSetup,
                contentFontSize, feePaymentInvoiceSummary, defaultBorderWidth);

        generateSignatureBox(document, documentLayoutSetup,
                contentFontSize, defaultBorderWidth);

        generateNote(document, documentLayoutSetup, contentFontSize);
    }

    private void generateNote(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize) throws IOException {
        Table table = getPDFTable(documentLayoutSetup, 1);
        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT);
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph(INVOICE_NOTE), cellLayoutSetup)));
        document.add(table);
    }

    protected void generateBackgroundImage(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
                                           float height, float offsetX, float offsetY)  throws IOException {
            generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
    }

    private void generateCancelledWatermark(DocumentLayoutData documentLayoutData, DocumentLayoutSetup documentLayoutSetup, Document document,
                                            int pageNumber, boolean isOfficeCopy) throws IOException {

        PdfPage pdfPage = document.getPdfDocument().getPage(pageNumber);
        Rectangle pagesize = pdfPage.getPageSizeWithRotation();

        float x = documentLayoutSetup.isOfficeCopy() ? pagesize.getWidth() / 8 - 50 : pagesize.getWidth() / 4 + 30;
        float y = documentLayoutSetup.isOfficeCopy() ? pagesize.getHeight() * 0.75f : pagesize.getHeight() * 0.70f;

        if(isOfficeCopy) {
            if(pageNumber == 1) {
                y -= (pagesize.getHeight() / 2);
            }
        }
        addWaterMark(document, ImageProvider.INSTANCE.getImage(ImageProvider.CANCELLED_TEXT_IMAGE2), x, y, pageNumber);
    }

    public void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                     float defaultBorderWidth) throws IOException {
        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.RIGHT).setBorder(new SolidBorder(defaultBorderWidth))
                .setBorderTop(null);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)),
                signatureCellLayoutSetup.copy().setBorderBottom(null));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Signature")), signatureCellLayoutSetup);
        document.add(table);
    }

    public void generateFeePaymentSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
                                          float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
            throws IOException {

        if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
                .getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.ACTIVE) {
            generateActiveFeePaymentSummary(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
                    defaultBorderWidth);
        } else {
            generateCancelledFeePaymentSummary(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
                    defaultBorderWidth);
        }
    }

    public void generateCancelledFeePaymentSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
                                                   float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
            throws IOException {

        int singleContentColumn = 2;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup keyCellLayoutSetup = new CellLayoutSetup();
        keyCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth));

        CellLayoutSetup valueCellLayoutSetup = keyCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT)
                .setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderLeft(null);

        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData("Total Paid Amount (INR):", keyCellLayoutSetup),
                        new CellData(String.valueOf(
                                feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getTotalPaidAmount()),
                                valueCellLayoutSetup)));
        if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getTotalFineAmount(), 0d) > 0) {
            addRow(table, documentLayoutSetup,
                    Arrays.asList(new CellData("Total Fine Paid (INR):", keyCellLayoutSetup),
                            new CellData(String.valueOf(
                                    feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getTotalFineAmount()),
                                    valueCellLayoutSetup)));
        }

        if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount(),
                0d) > 0) {
            addRow(table, documentLayoutSetup,
                    Arrays.asList(new CellData("Used Wallet Amount (INR):", keyCellLayoutSetup),
                            new CellData(String.valueOf(
                                    feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount()),
                                    valueCellLayoutSetup)));
        }
        if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getCreditWalletAmount(),
                0d) > 0) {
            addRow(table,
                    documentLayoutSetup, Arrays
                            .asList(new CellData("Credit Wallet Amount (INR):", keyCellLayoutSetup),
                                    new CellData(String.valueOf(feePaymentInvoiceSummary
                                            .getFeePaymentTransactionMetaData().getCreditWalletAmount()),
                                            valueCellLayoutSetup)));
        }

        document.add(table);

        // Amount in words

        singleContentColumn = 1;
        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.RIGHT).setBorder(new SolidBorder(defaultBorderWidth))
                .setBorderBottom(null).setPaddingBottom(10f);

        addRow(table, documentLayoutSetup, Arrays.asList(new Paragraph(EMPTY_TEXT)), signatureCellLayoutSetup);

        document.add(table);

        singleContentColumn = 2;
        table = getPDFTable(documentLayoutSetup, singleContentColumn);
        Paragraph amountsInWord = getKeyValueParagraph("(In Words): ",
                feePaymentInvoiceSummary.getTotalCurrentTransactionPaidAmountInWords() + " Only.");

        Paragraph collectedAmount = getKeyValueParagraph("Collected Amount : ",
                String.valueOf((int) (Math.round(feePaymentInvoiceSummary.getTotalCurrentTransactionPaidAmount()
                        + feePaymentInvoiceSummary.getTotalFineAmount()))));


        CellLayoutSetup amountInWordsCellLayoutSetup = new CellLayoutSetup();
        amountInWordsCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(defaultBorderWidth))
                .setBorderBottom(null).setBorderTop(null).setBorderRight(null);

        CellLayoutSetup collectedAmountCellLayoutSetup = new CellLayoutSetup();
        collectedAmountCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.RIGHT).setBorder(new SolidBorder(defaultBorderWidth))
                .setBorderBottom(null).setBorderTop(null).setBorderLeft(null);

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(amountsInWord, amountInWordsCellLayoutSetup),
                new CellData(collectedAmount, collectedAmountCellLayoutSetup)));

        document.add(table);

        String remark = feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getRemark();
        if(!StringUtils.isBlank(remark)) {
            singleContentColumn = 1;
            table = getPDFTable(documentLayoutSetup, singleContentColumn);
            Paragraph remarks = getKeyValueParagraph("Remarks : ", remark);
            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(remarks, amountInWordsCellLayoutSetup)));
            document.add(table);
        }
    }

    public void generateActiveFeePaymentSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
                                                float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
            throws IOException {
        int singleContentColumn = 2;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup keyCellLayoutSetup = new CellLayoutSetup();
        keyCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth));

        CellLayoutSetup valueCellLayoutSetup = keyCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT)
                .setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderLeft(null);

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Total Fees (INR):", keyCellLayoutSetup),
                new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalAssignedAmount()), valueCellLayoutSetup)));
        if (Double.compare(feePaymentInvoiceSummary.getTotalDiscontAmount(), 0d) > 0) {
            addRow(table, documentLayoutSetup,
                    Arrays.asList(new CellData("Discount Amount (INR):", keyCellLayoutSetup), new CellData(
                            String.valueOf(feePaymentInvoiceSummary.getTotalDiscontAmount()), valueCellLayoutSetup)));
        }

        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData("Paid Amount (INR):", keyCellLayoutSetup),
                        new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalCurrentTransactionPaidAmount()),
                                valueCellLayoutSetup)));
        if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount(),
                0d) > 0) {
            addRow(table, documentLayoutSetup,
                    Arrays.asList(new CellData("Used Wallet Amount (INR):", keyCellLayoutSetup),
                            new CellData(String.valueOf(
                                    feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount()),
                                    valueCellLayoutSetup)));
        }
        if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getCreditWalletAmount(),
                0d) > 0) {
            addRow(table,
                    documentLayoutSetup, Arrays
                            .asList(new CellData("Credit Wallet Amount (INR):", keyCellLayoutSetup),
                                    new CellData(String.valueOf(feePaymentInvoiceSummary
                                            .getFeePaymentTransactionMetaData().getCreditWalletAmount()),
                                            valueCellLayoutSetup)));
        }

        if (Double.compare(feePaymentInvoiceSummary.getTotalFineAmount(), 0d) > 0) {
            addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Fine Amount (INR):", keyCellLayoutSetup),
                    new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalFineAmount()), valueCellLayoutSetup)));
        }

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Due Amount (INR):", keyCellLayoutSetup),
                new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalDueAmount()), valueCellLayoutSetup)));

        document.add(table);

        // Amount in words
        singleContentColumn = 1;
        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.RIGHT).setBorder(new SolidBorder(defaultBorderWidth))
                .setBorderBottom(null).setPaddingBottom(10f);

        addRow(table, documentLayoutSetup, Arrays.asList(new Paragraph(EMPTY_TEXT)), signatureCellLayoutSetup);

        document.add(table);

        singleContentColumn = 2;
        table = getPDFTable(documentLayoutSetup, singleContentColumn);
        Paragraph amountsInWord = getKeyValueParagraph("(In Words): ",
                feePaymentInvoiceSummary.getTotalCurrentTransactionPaidAmountInWords() + " Only.");

        Paragraph collectedAmount = getKeyValueParagraph("Collected Amount : ",
                String.valueOf((int) (Math.round(feePaymentInvoiceSummary.getTotalCurrentTransactionPaidAmount()
                        + feePaymentInvoiceSummary.getTotalFineAmount()))));


        CellLayoutSetup amountInWordsCellLayoutSetup = new CellLayoutSetup();
        amountInWordsCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(defaultBorderWidth))
                .setBorderBottom(null).setBorderTop(null).setBorderRight(null);

        CellLayoutSetup collectedAmountCellLayoutSetup = new CellLayoutSetup();
        collectedAmountCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.RIGHT).setBorder(new SolidBorder(defaultBorderWidth))
                .setBorderBottom(null).setBorderTop(null).setBorderLeft(null);

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(amountsInWord, amountInWordsCellLayoutSetup),
                new CellData(collectedAmount, collectedAmountCellLayoutSetup)));

        document.add(table);

        String remark = feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getRemark();
        if(!StringUtils.isBlank(remark)) {
            singleContentColumn = 1;
            table = getPDFTable(documentLayoutSetup, singleContentColumn);
            amountInWordsCellLayoutSetup.setBorderRight(new SolidBorder(defaultBorderWidth));
            Paragraph remarks = getKeyValueParagraph("Remarks : ", remark);
            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(remarks, amountInWordsCellLayoutSetup)));
            document.add(table);
        }
    }

    public void generateFeeContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                   FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth) throws IOException {

        if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
                .getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.ACTIVE) {
            generateActiveFeeContent(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
                    defaultBorderWidth);
        } else {
         generateCancelledFeeContent(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
                    defaultBorderWidth);
        }
    }

    public void generateCancelledFeeContent(Document document, DocumentLayoutSetup documentLayoutSetup,
                                            float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
            throws IOException {

        if (feePaymentInvoiceSummary.getFeePaymentTransactionDetails() == null || CollectionUtils.isEmpty(
                feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getFeeIdFeeHeadTransactionDetails())) {
            return;
        }

        Table feeTable = getPDFTable(documentLayoutSetup, DEFAULT_CANCELLED_FEE_HEAD_HEADER_WIDTH);

        CellLayoutSetup feeCellLayoutSetup = new CellLayoutSetup();
        feeCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.LEFT);

        addRow(feeTable, documentLayoutSetup, Arrays.asList(new CellData("#", feeCellLayoutSetup),
                new CellData("Particulars", feeCellLayoutSetup), new CellData("Paid Amount(INR)", feeCellLayoutSetup)));

        feeCellLayoutSetup.setPdfFont(getRegularFont());
        int count = 0;
        for (FeeIdFeeHeadTransactionDetails feeIdFeeHeadTransactionDetails : feePaymentInvoiceSummary
                .getFeePaymentTransactionDetails().getFeeIdFeeHeadTransactionDetails()) {
            addRow(feeTable, documentLayoutSetup,
                    Arrays.asList(new CellData(String.valueOf(++count), feeCellLayoutSetup),
                            new CellData(feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo().getFeeName(),
                                    feeCellLayoutSetup),
                            new CellData(String.valueOf(feeIdFeeHeadTransactionDetails.getTotalPaidAmount()),
                                    feeCellLayoutSetup)));
        }
        document.add(feeTable);
    }

    public void generateActiveFeeContent(Document document, DocumentLayoutSetup documentLayoutSetup,
                                         float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
            throws IOException {

        if (CollectionUtils.isEmpty(feePaymentInvoiceSummary.getFeeIdInvoices())) {
            return;
        }

        CellLayoutSetup feeCellLayoutSetup = new CellLayoutSetup();
        feeCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(defaultBorderWidth));

        Table feeTable = getPDFTable(documentLayoutSetup, DEFAULT_ACTIVE_FEE_HEAD_HEADER_WIDTH);
        addRow(feeTable, documentLayoutSetup,
                Arrays.asList(new CellData("#", feeCellLayoutSetup), new CellData("Particulars", feeCellLayoutSetup),
                        new CellData("Amount(INR)", feeCellLayoutSetup), new CellData("Discount", feeCellLayoutSetup),
                        new CellData("Paid Amount", feeCellLayoutSetup)));

        feeCellLayoutSetup.setPdfFont(getRegularFont());

        int count = 0;
        for (FeeIdInvoice feeIdInvoice : feePaymentInvoiceSummary.getFeeIdInvoices()) {
            addRow(feeTable, documentLayoutSetup,
                    Arrays.asList(new CellData(String.valueOf(++count), feeCellLayoutSetup),
                            new CellData(feeIdInvoice.getFeeConfigurationBasicInfo().getFeeName(), feeCellLayoutSetup),
                            new CellData(String.valueOf(feeIdInvoice.getTotalAssignedAmount()), feeCellLayoutSetup),
                            new CellData(String.valueOf(feeIdInvoice.getTotalCurrentTransactionInstantDiscountAmount()), feeCellLayoutSetup),
                            new CellData(String.valueOf(feeIdInvoice.getTotalCurrentTransactionPaidAmount()),
                                    feeCellLayoutSetup)));
        }

        document.add(feeTable);
    }

    public void generateStudentInformation(Document document, DocumentLayoutSetup documentLayoutSetup,
                                           float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
            throws IOException {
        int singleContentColumn = 2;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize);

        CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)
                .setBorderLeft(new SolidBorder(defaultBorderWidth));
        CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT)
                .setBorderRight(new SolidBorder(defaultBorderWidth));

        Paragraph studentName = getKeyValueParagraph("Student's Name : ",
                feePaymentInvoiceSummary.getStudent().getStudentBasicInfo().getName());

        Paragraph date = getKeyValueParagraph("Date: ",
                DateUtils.getFormattedDate(
                        feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getTransactionDate(), DATE_FORMAT,
                        User.DFAULT_TIMEZONE));

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(studentName, firstCellLayoutSetup),
                new CellData(date, thirdCellLayoutSetup)));

        Paragraph fatherName = getKeyValueParagraph("Father's Name : ",
                feePaymentInvoiceSummary.getStudent().getStudentFamilyInfo().getFathersName());
        Paragraph admissionNumber = getKeyValueParagraph("Admission No. ",
                feePaymentInvoiceSummary.getStudent().getStudentBasicInfo().getAdmissionNumber());

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(fatherName, firstCellLayoutSetup),
                new CellData(admissionNumber, thirdCellLayoutSetup)));

        Paragraph motherName = getKeyValueParagraph("Mother's Name : ",
                feePaymentInvoiceSummary.getStudent().getStudentFamilyInfo().getMothersName());

        Paragraph classValue = getKeyValueParagraph("Class : ", feePaymentInvoiceSummary.getStudent()
                .getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection())
                .setFont(getRegularFont());

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(motherName, firstCellLayoutSetup.setBorderBottom(new SolidBorder(defaultBorderWidth))),
                new CellData(classValue, thirdCellLayoutSetup.setBorderBottom(new SolidBorder(defaultBorderWidth)))));

        document.add(table);
    }

    protected void generateHeader(DocumentLayoutData documentLayoutData, DocumentLayoutSetup documentLayoutSetup, Document document,
                                  Institute institute, float offsetX, float offsetY, float defaultBorderWidth,
                                  FeePaymentInvoiceSummary feePaymentInvoiceSummary, int pageNumber, boolean isOfficeCopy) throws IOException {
        
        int instituteId = institute.getInstituteId();
        InstituteDocumentType instituteDocumentType = InstituteDocumentType.INSTITUTE_PRIMARY_LOGO;
        if(isOfficeCopy) {
            if(pageNumber == 1) {
                LayoutArea currentArea = document.getRenderer().getCurrentArea();
                Rectangle rectangle = currentArea.getBBox();
                generateDynamicImageProvider(documentLayoutData, offsetX,  rectangle.getHeight() - (documentLayoutData.getLogoHeight() * 0.75f), instituteId, instituteDocumentType);
            } else {
                generateDynamicImageProvider(documentLayoutData, offsetX, offsetY, instituteId, instituteDocumentType);
            }
        } else {
                generateDynamicImageProvider(documentLayoutData, offsetX, offsetY, instituteId, instituteDocumentType);
        }

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(12f).setTextAlignment(TextAlignment.CENTER);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase())),
                cellLayoutSetup.copy().setFontSize(13f));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
                cellLayoutSetup.copy().setFontSize(11f).setPdfFont(getRegularFont()));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
                cellLayoutSetup.copy().setFontSize(11f).setPdfFont(getRegularFont()));

        document.add(table);
        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addBlankLine(document, false,1);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("School Fee Receipt")),
                cellLayoutSetup.copy().setFontSize(12f).setPdfFont(getRegularBoldFont()));
        document.add(table);

        addBlankLine(document, false,1);

        singleContentColumn = 3;
        table = getPDFTable(documentLayoutSetup, singleContentColumn);
        cellLayoutSetup.setBorderBottom(new SolidBorder(defaultBorderWidth)).setFontSize(10f);

        Paragraph receipt = getKeyValueParagraph("Receipt No. ",
                feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getInvoiceId());

        String str = "";
        if(isOfficeCopy) {
            str = OFFICE_COPY_TEXT;
        } else {
            str = STUDENT_COPY_TEXT;
        }
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(receipt, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
                        new CellData(str, cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
                        new CellData("Session : " +
                                feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse()
                                        .getAcademicSession().getYearDisplayName(),
                                cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));
        document.add(table);
    }

    protected DocumentLayoutData generatefeeInvoiceLayoutData(Institute institute, DocumentOutput documentOutput)
            throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
        float contentFontSize = 12f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        float logoWidth = 80f;
        float logoHeight = 80f;
        int instituteId = institute.getInstituteId();
        return new DocumentLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
                defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId), null, null);
    }

    @Override
    public DocumentOutput generateBulkInvoices(Institute institute, List<FeePaymentInvoiceSummary>
            feePaymentInvoiceSummaryList, String documentName, boolean doubleCopy, FeeInvoicePreferences feeInvoicePreferences) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData documentLayoutData = generatefeeInvoiceLayoutData(institute, documentOutput);
            DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
            Document document = documentLayoutData.getDocument();

            if(CollectionUtils.isEmpty(feePaymentInvoiceSummaryList)) {
                document.close();
                return documentOutput;
            }

            int pageNumber = 1;
            boolean addWaterMark = true;
            for (FeePaymentInvoiceSummary feePaymentInvoiceSummary : feePaymentInvoiceSummaryList) {
                generateInvoice(documentLayoutData, documentLayoutSetup, document, institute,
                        feePaymentInvoiceSummary, pageNumber, addWaterMark, true);

                if (pageNumber != feePaymentInvoiceSummaryList.size()) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;

            }
            document.close();
            return documentOutput;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
