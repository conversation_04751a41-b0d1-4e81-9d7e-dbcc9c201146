package com.lernen.cloud.pdf.invoice.fee;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.FeeInvoicePreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.*;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

import static com.lernen.cloud.core.utils.EColorUtils.purpleDeviceRgb;
import static com.lernen.cloud.core.utils.EColorUtils.redDeviceRgb;

public class FeeInvoiceGenerator10200 extends GlobalShrinkedSizeFeeInvoiceGenerator {

	public FeeInvoiceGenerator10200(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final  String NUR_TO_V_SCHOOL_NAME = "Sardar Patel Memorial Public School";
	private static final  String NUR_TO_V_SCHOOL_ADDRESS = "VILLAGE KAIMRALA DADRI G.B.NAGAR";

	public static final float DEFAULT_LOGO_WIDTH = 95f;
	public static final float DEFAULT_LOGO_HEIGHT = 95f;

	@Override
	public DocumentOutput generateInvoice(Institute institute, FeePaymentInvoiceSummary feePaymentInvoiceSummary,
										  StudentTransportDetails studentTransportDetails, String documentName, boolean officeCopy, UserType userType, StudentFeesDetailsLite studentFeesDetailsLite, FeeInvoicePreferences feeInvoicePreferences) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			if(userType == UserType.STUDENT) {
				officeCopy = false;
			}
			DocumentLayoutData documentLayoutData = generateFeeInvoiceLayoutData(institute, documentOutput);
			DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
			Document document = documentLayoutData.getDocument();
			float contentFontSize = documentLayoutData.getContentFontSize();
			float headerFontSize = 11f;
			float defaultBorderWidth = documentLayoutData.getDefaultBorderWidth();

			Set<UUID> nurToVClassId = new HashSet<>(Arrays.asList(
					UUID.fromString("1e016faf-c45f-4fae-9d83-7c9920e5e4af"),//Nur
					UUID.fromString("fd315459-1d5e-4e11-86ba-1e82c94d5c86"),//LKG
					UUID.fromString("7da76d75-4ae9-4379-a804-4834963c8406"),//UKG
					UUID.fromString("1a24d752-5086-423e-b58e-6ae8a5ce1425"),//I
					UUID.fromString("66760d16-56ec-4ce7-a97f-629f6d69de95"),//II
					UUID.fromString("632af3fb-44cd-424e-af4c-53b5f1398094"),//III
					UUID.fromString("532086e9-81fa-4aa6-92eb-7824b7dc4395"),//IV
					UUID.fromString("f2bddc19-a442-4c26-8310-b1cfbacfd25c")//V
			));

			byte[] logoByte  = generateImageByteArray(institute.getInstituteId(), documentLayoutData, InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
			float logoWidth = documentLayoutData.getLogoWidth();
			float logoHeight = documentLayoutData.getLogoHeight();
			if(nurToVClassId.contains(feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardId())) {
				logoByte = ImageProvider.INSTANCE.getImage(ImageProvider._10200_NUR_TO_V_LOGO);
				logoWidth = 90f;
				logoHeight = 90f;
			}

			generateFeeInvoice(institute, document, documentLayoutSetup, feePaymentInvoiceSummary,
					headerFontSize, defaultBorderWidth, contentFontSize, 1, STUDENT_COPY_TEXT, logoByte, userType, logoWidth, logoHeight);

			document.close();
			return documentOutput;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}


	protected DocumentLayoutData generateFeeInvoiceLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
		float contentFontSize = 8f;
		float defaultBorderWidth = 0.1f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
		int instituteId = institute.getInstituteId();
		return new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(), getRegularBoldFont(), contentFontSize,
				defaultBorderWidth, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(instituteId), null, null);
	}

	public void generateFeeInvoice(Institute institute, Document document, DocumentLayoutSetup documentLayoutSetup,
								   FeePaymentInvoiceSummary feePaymentInvoiceSummary,
								   float headerFontSize, float defaultBorderWidth, float contentFontSize, int pageNumber,
								   String studentCopyText, byte[] logoByte, UserType userType, float logoWidth, float logoHeight) throws IOException {

		if(logoByte != null) {
			float offsetX = 40f;
			float offsetY = documentLayoutSetup.getPageSize().getHeight() - logoHeight - documentLayoutSetup.getTopMargin();

			generateImage(document, documentLayoutSetup, logoByte, logoWidth, logoHeight, offsetX, offsetY, 1f, 1f);
		}


		generateHeader(document, documentLayoutSetup, feePaymentInvoiceSummary, institute, headerFontSize,
				defaultBorderWidth, studentCopyText, userType);
		generateStudentInformation(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
				defaultBorderWidth);
		generateFeeContent(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
				defaultBorderWidth);
		generateFeePaymentSummary(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
				defaultBorderWidth);
		generateSignatureBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth);
		if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
				.getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.CANCELLED) {
			// addWaterMark(document, documentLayoutSetup, "Cancelled");
			PdfPage pdfPage = document.getPdfDocument().getPage(pageNumber);
			Rectangle pagesize = pdfPage.getPageSizeWithRotation();

			float x = documentLayoutSetup.isOfficeCopy() ? pagesize.getWidth() / 8 - 50 : pagesize.getWidth() / 4 + 30;
			float y = documentLayoutSetup.isOfficeCopy() ? pagesize.getHeight() * 0.75f : pagesize.getHeight() * 0.70f;

			addWaterMark(document, ImageProvider.INSTANCE.getImage(ImageProvider.CANCELLED_TEXT_IMAGE2), x, y, pageNumber);
		}
	}

	public void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
							   FeePaymentInvoiceSummary feePaymentInvoiceSummary, Institute institute, float headerFontSize,
							   float defaultBorderWidth, String studentCopyText, UserType userType) throws IOException {
		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setTextAlignment(TextAlignment.CENTER);

		Set<UUID> nurToVClassId = new HashSet<>(Arrays.asList(
				UUID.fromString("1e016faf-c45f-4fae-9d83-7c9920e5e4af"),//Nur
				UUID.fromString("fd315459-1d5e-4e11-86ba-1e82c94d5c86"),//LKG
				UUID.fromString("7da76d75-4ae9-4379-a804-4834963c8406"),//UKG
				UUID.fromString("1a24d752-5086-423e-b58e-6ae8a5ce1425"),//I
				UUID.fromString("66760d16-56ec-4ce7-a97f-629f6d69de95"),//II
				UUID.fromString("632af3fb-44cd-424e-af4c-53b5f1398094"),//III
				UUID.fromString("532086e9-81fa-4aa6-92eb-7824b7dc4395"),//IV
				UUID.fromString("f2bddc19-a442-4c26-8310-b1cfbacfd25c")//V
		));

		if(nurToVClassId.contains(feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardId())) {
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NUR_TO_V_SCHOOL_NAME)),
					cellLayoutSetup.setFontSize(headerFontSize + 10));
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NUR_TO_V_SCHOOL_ADDRESS)),
					cellLayoutSetup.setFontSize(9f));
		} else {
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName())),
					cellLayoutSetup.setFontSize(headerFontSize + 10));
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
					cellLayoutSetup.setFontSize(9f));
		}
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("School Fee Receipt")),
				Arrays.asList(getParagraph("School Fee Receipt")), cellLayoutSetup);

		document.add(table);

		// Student copy section
		singleContentColumn = 3;
		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		cellLayoutSetup.setBorderBottom(new SolidBorder(defaultBorderWidth));

		Paragraph receipt = getKeyValueParagraph("Receipt No. ",
				feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getInvoiceId());

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(receipt, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
						new CellData(studentCopyText, cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
						new CellData(
								feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse()
										.getAcademicSession().getYearDisplayName(),
								cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))),
				Arrays.asList(new CellData(receipt, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
						new CellData(userType == UserType.STUDENT ? STUDENT_COPY_TEXT : OFFICE_COPY_TEXT, cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
						new CellData(
								feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse()
										.getAcademicSession().getYearDisplayName(),
								cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));
		document.add(table);

	}

	@Override
	public DocumentOutput generateBulkInvoices(Institute institute, List<FeePaymentInvoiceSummary>
			feePaymentInvoiceSummaryList, String documentName, boolean doubleCopy, FeeInvoicePreferences feeInvoicePreferences) {
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(doubleCopy);
			float contentFontSize = 8f;
			float headerFontSize = 11f;
			float defaultBorderWidth = 0.1f;
			Document document = initDocument(invoice.getContent(), documentLayoutSetup);

			if(CollectionUtils.isEmpty(feePaymentInvoiceSummaryList)) {
				document.close();
				return invoice;
			}

			int pageNumber = 1;
//			byte[] logoByte = LogoProvider.INSTANCE.getLogo(institute.getInstituteId());
			for (FeePaymentInvoiceSummary feePaymentInvoiceSummary : feePaymentInvoiceSummaryList) {
				generateFeeInvoice(institute, document, documentLayoutSetup, feePaymentInvoiceSummary,
						headerFontSize, defaultBorderWidth, contentFontSize, pageNumber, OFFICE_COPY_TEXT, null, null, 110f, 90f);

				if (pageNumber != feePaymentInvoiceSummaryList.size()) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}
			document.close();
			return invoice;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
}
