package com.lernen.cloud.pdf.invoice.fee;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.configurations.FeeInvoicePreferences;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fees.payment.*;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.InstituteBankAccountDetails;
import com.lernen.cloud.core.api.institute.InstituteHouse;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentInsightManager;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.transport.configuration.TransportAssignmentManager;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;
import com.lernen.cloud.core.api.user.UserType;

import java.util.*;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeeInvoiceHandler {

	private static final Logger logger = LogManager.getLogger(FeeInvoiceHandler.class);

	private final FeeInvoiceGeneratorFactory feeInvoiceGeneratorFactory;
	private final InstituteManager instituteManager;
	private final FeePaymentManager feePaymentManager;
	private final FeePaymentInsightManager feePaymentInsightManager;
	private final UserPreferenceSettings userPreferenceSettings;
	private final UserPermissionManager userPermissionManager;
	private final TransportAssignmentManager transportAssignmentManager;

	public FeeInvoiceHandler(InstituteManager instituteManager, FeePaymentManager feePaymentManager,
							 FeePaymentInsightManager feePaymentInsightManager,
                             UserPreferenceSettings userPreferenceSettings, UserPermissionManager userPermissionManager,
							 TransportAssignmentManager transportAssignmentManager, AssetProvider assetProvider) {
		this.feeInvoiceGeneratorFactory = new FeeInvoiceGeneratorFactory(assetProvider);
		this.instituteManager = instituteManager;
		this.feePaymentManager = feePaymentManager;
		this.feePaymentInsightManager = feePaymentInsightManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.userPermissionManager = userPermissionManager;
		this.transportAssignmentManager = transportAssignmentManager;
	}

	public DocumentOutput generateInvoice(int instituteId, UUID transactionId, Boolean officeCopy,
										  String userTypeStr) {
		if (instituteId <= 0 || transactionId == null) {
			logger.error("Invalid institute id or academicSessionId or staffId or userId");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS,
					"Invalid institute id or academicSessionId or staffId or userId"));
		}

		UserType userType = null;
		if(!StringUtils.isBlank(userTypeStr)) {
			userType = UserType.getUserType(userTypeStr);
		}
//		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.GENERATE_STAFF_IDENTITY_CARD);

		final Institute institute = instituteManager.getInstitute(instituteId);
		if (institute == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to get institute"));
		}
		final FeePaymentInvoiceSummary feePaymentInvoiceSummary = feePaymentManager.getTransactionInvoiceSummary(
				instituteId, transactionId, true);
		if (feePaymentInvoiceSummary == null || feePaymentInvoiceSummary.getFeePaymentTransactionMetaData() == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION,
					"Unable to get fee transaction details"));
		}

		/**
		 * Set house for student - special handling for 10225,26,27,28 institute
		 */
		Student student = getStudentWithHouseDetails(institute.getInstituteId(), feePaymentInvoiceSummary.getStudent());
		feePaymentInvoiceSummary.setStudent(student);

		StudentTransportDetails studentTransportDetails = transportAssignmentManager
				.getStudentCurrentTransportDetails(instituteId, feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getAcademicSessionId(),
						feePaymentInvoiceSummary.getStudent().getStudentId());

		StudentFeesDetailsLite studentFeesDetailsLite = feePaymentManager.getStudentFeesDetailsLite(instituteId,
				feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getAcademicSessionId(),
				feePaymentInvoiceSummary.getStudent().getStudentId());

		final MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);
		FeeInvoiceGenerator feeInvoiceGenerator = feeInvoiceGeneratorFactory.getFeeInvoiceGenerator(instituteId);

		FeeInvoicePreferences feeInvoicePreferences = userPreferenceSettings.getFeeInvoicePreferences(instituteId);

		if(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData() != null && feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getBankAccountId() != null) {
			InstituteBankAccountDetails bankAccountDetails = instituteManager.getInstituteBankAccount(instituteId, feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getBankAccountId());
			feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().setBankAccountDetails(bankAccountDetails);
		}

		return feeInvoiceGenerator.generateInvoice(institute, feePaymentInvoiceSummary, studentTransportDetails,
						feePaymentInvoiceSummary.getStudent().getStudentBasicInfo().getAdmissionNumber()
								+ "_FeeInvoice.pdf", !metaDataPreferences.isOnlyStudentCopyInFeeReceipts(), userType, studentFeesDetailsLite,
				feeInvoicePreferences);
	}

	public Student getStudentWithHouseDetails(int instituteId, Student student) {
		if(student == null) {
			return null;
		}
		List<InstituteHouse> instituteHouseList = instituteManager.getInstituteHouseList(instituteId);
		Map<UUID, InstituteHouse> instituteHouseMap = InstituteHouse.getInstituteHouseMap(instituteHouseList);
		if(CollectionUtils.isEmpty(instituteHouseMap)) {
			return student;
		}
		UUID instituteHouseId = student.getStudentBasicInfo().getInstituteHouseId();
		if(instituteHouseId != null) {
			InstituteHouse instituteHouse = instituteHouseMap.get(instituteHouseId);
			student.getStudentBasicInfo().setInstituteHouse(instituteHouse);
		}
		return student;
	}

	public DocumentOutput generateBulkInvoices(int instituteId, int academicSessionId, Boolean officeCopy, UUID userId,
											   int startDate, int endDate, FeePaymentTransactionStatus feePaymentTransactionStatus) {
		if (instituteId <= 0) {
			logger.error("Invalid institute id or academicSessionId or staffId or userId");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS,
					"Invalid institute id or academicSessionId or staffId or userId"));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.BULK_FEE_INVOICE_GENERATION);

		final Institute institute = instituteManager.getInstitute(instituteId);
		if (institute == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to get institute"));
		}

		List<FeePaymentInvoiceSummary> feePaymentInvoiceSummaryList = feePaymentInsightManager
				.getFeePaymentInvoiceSummaryList(instituteId, academicSessionId, startDate, endDate, feePaymentTransactionStatus);
//		if (CollectionUtils.isNullOrEmpty(feePaymentInvoiceSummaryList)) {
//			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION,
//					"Unable to get fee transaction details"));
//		}

//		final MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);
		FeeInvoicePreferences feeInvoicePreferences = userPreferenceSettings.getFeeInvoicePreferences(instituteId);
		FeeInvoiceGenerator feeInvoiceGenerator = feeInvoiceGeneratorFactory.getFeeInvoiceGenerator(instituteId);
		return feeInvoiceGenerator.generateBulkInvoices(institute, feePaymentInvoiceSummaryList,
				"FeeInvoices.pdf", false, feeInvoicePreferences);
//				officeCopy == null ? !metaDataPreferences.isOnlyStudentCopyInFeeReceipts() : officeCopy);

	}

	public DocumentOutput generateBulkInvoices(int instituteId, int academicSessionId, Boolean officeCopy, UUID userId,
											   String transactionIdStr) {
		if (instituteId <= 0 || StringUtils.isBlank(transactionIdStr)) {
			logger.error("Invalid institute id or trnsaction ids or staffId or userId");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS,
					"Invalid institute id or transaction ids or staffId or userId"));
		}

		Set<UUID> transactionIdSet = convertStrToUUIDSet(transactionIdStr);

//		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.BULK_FEE_INVOICE_GENERATION);

		final Institute institute = instituteManager.getInstitute(instituteId);
		if (institute == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to get institute"));
		}

		List<FeePaymentInvoiceSummary> feePaymentInvoiceSummaryList = feePaymentInsightManager
				.getBulkFeePaymentInvoiceSummaryList(instituteId, academicSessionId, transactionIdSet, null);

		FeeInvoiceGenerator feeInvoiceGenerator = feeInvoiceGeneratorFactory.getFeeInvoiceGenerator(instituteId);
		FeeInvoicePreferences feeInvoicePreferences = userPreferenceSettings.getFeeInvoicePreferences(instituteId);
		return feeInvoiceGenerator.generateBulkInvoices(institute, feePaymentInvoiceSummaryList,
				"BulkFeeInvoices.pdf", false, feeInvoicePreferences);
//				officeCopy == null ? !metaDataPreferences.isOnlyStudentCopyInFeeReceipts() : officeCopy);

	}

	public Set<UUID> convertStrToUUIDSet(String stringIdStr) {
		final Set<UUID> uuidSet = new HashSet<>();
		if (org.apache.commons.lang3.StringUtils.isBlank(stringIdStr)) {
			return uuidSet;
		}

		final String[] feeIdTokens = stringIdStr.split(",");

		for (final String feeId : feeIdTokens) {
			if(org.apache.commons.lang3.StringUtils.isBlank(feeId)) {
				continue;
			}
			uuidSet.add(UUID.fromString(feeId));
		}
		return uuidSet;
	}
}
