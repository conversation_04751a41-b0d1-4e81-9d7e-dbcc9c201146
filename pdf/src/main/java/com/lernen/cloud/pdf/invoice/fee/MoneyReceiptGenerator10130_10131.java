package com.lernen.cloud.pdf.invoice.fee;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.FeeInvoicePreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.FeePaymentInvoiceSummary;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.fees.payment.StudentFeesDetailsLite;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MoneyReceiptGenerator10130_10131 extends GlobalShrinkedSizeMoneyReceiptGenerator {

    public MoneyReceiptGenerator10130_10131(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    @Override
    public DocumentOutput generateInvoice(Institute institute, FeePaymentInvoiceSummary feePaymentInvoiceSummary,
                                          StudentTransportDetails studentTransportDetails, String documentName, boolean officeCopy, UserType userType, StudentFeesDetailsLite studentFeesDetailsLite, FeeInvoicePreferences feeInvoicePreferences) {
        try {
            DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
            if(userType == UserType.STUDENT) {
                officeCopy = false;
            }
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(officeCopy);
            float contentFontSize = 8f;
            /**
             * creating this file just for this height change of institute name
             * TODO: in future this should come from DB.
             */
            float headerFontSize = 7.5f;
            float defaultBorderWidth = 0.1f;
            Document document = initDocument(invoice.getContent(), documentLayoutSetup);
            DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getNunitoRegularFont(), getAleoBoldFont(), null, null, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), getGlacialRegularFont(), getHindiBoldFont());

            generateFeeInvoice(institute, documentLayoutData, feePaymentInvoiceSummary,
                    headerFontSize, defaultBorderWidth, institute.getInstituteId(), contentFontSize,
                    1, STUDENT_COPY_TEXT, userType);

            document.close();
            return invoice;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public DocumentOutput generateBulkInvoices(Institute institute, List<FeePaymentInvoiceSummary>
            feePaymentInvoiceSummaryList, String documentName, boolean doubleCopy, FeeInvoicePreferences feeInvoicePreferences) {
        try {
            DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(doubleCopy);
            float contentFontSize = 8f;
            float headerFontSize = 11f;
            float defaultBorderWidth = 0.1f;
            Document document = initDocument(invoice.getContent(), documentLayoutSetup);
            DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getNunitoRegularFont(), getAleoBoldFont(), null, null, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), getGlacialRegularFont(), getHindiBoldFont());

            if (CollectionUtils.isEmpty(feePaymentInvoiceSummaryList)) {
                document.close();
                return invoice;
            }

            int pageNumber = 1;
            for (FeePaymentInvoiceSummary feePaymentInvoiceSummary : feePaymentInvoiceSummaryList) {
                generateFeeInvoice(institute, documentLayoutData, feePaymentInvoiceSummary,
                        headerFontSize, defaultBorderWidth, institute.getInstituteId(), contentFontSize, pageNumber,
                        OFFICE_COPY_TEXT, null);

                if (pageNumber != feePaymentInvoiceSummaryList.size()) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;

            }
            document.close();
            return invoice;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
