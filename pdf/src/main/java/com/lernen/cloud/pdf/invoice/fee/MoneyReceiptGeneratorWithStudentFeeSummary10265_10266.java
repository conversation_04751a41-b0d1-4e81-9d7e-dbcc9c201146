package com.lernen.cloud.pdf.invoice.fee;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.FeeInvoicePreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.FeePaymentInvoiceSummary;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.fees.payment.StudentFeesDetailsLite;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

public class MoneyReceiptGeneratorWithStudentFeeSummary10265_10266 extends GlobalShrinkedSizeMoneyReceiptGenerator {

    public MoneyReceiptGeneratorWithStudentFeeSummary10265_10266(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final float DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN = 20f;
    private static final float DEFAULT_PAGE_SIDE_MARGIN = 100f;
    
@Override
    public DocumentOutput generateInvoice(Institute institute, FeePaymentInvoiceSummary feePaymentInvoiceSummary,
                                          StudentTransportDetails studentTransportDetails, String documentName, boolean officeCopy, UserType userType, StudentFeesDetailsLite studentFeesDetailsLite, FeeInvoicePreferences feeInvoicePreferences) {
        try {
            DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
            if(userType == UserType.STUDENT) {
                officeCopy = false;
            }
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(officeCopy);
            float contentFontSize = 8f;
            /**
             * creating this file just for this height change of institute name
             * TODO: in future this should come from DB.
             */

            float headerFontSize = 20f;
            float defaultBorderWidth = 0.1f;
            Document document = initDocument(invoice.getContent(), documentLayoutSetup);
            DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getNunitoRegularFont(), getAleoBoldFont(), null, null, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), getGlacialRegularFont(), getHindiBoldFont());
           
            generateFeeInvoice(institute, documentLayoutData, feePaymentInvoiceSummary,
                    headerFontSize, defaultBorderWidth, institute.getInstituteId(), contentFontSize,
                    1, STUDENT_COPY_TEXT, userType, studentFeesDetailsLite);
            document.close();
            return invoice;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public void generateFeeInvoice(Institute institute, DocumentLayoutData documentLayoutData,
                                   FeePaymentInvoiceSummary feePaymentInvoiceSummary,
                                   float headerFontSize, float defaultBorderWidth, int instituteId, float contentFontSize, int pageNumber,
                                   String studentCopyText, UserType userType, StudentFeesDetailsLite studentFeesDetailsLite) throws IOException {
        
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        
        generateDynamicImageProvider(documentLayoutData, 10, 0, instituteId, InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
        
        generateHeader(document, documentLayoutSetup, feePaymentInvoiceSummary, institute, headerFontSize,
                defaultBorderWidth, studentCopyText, userType);
        generateStudentInformation(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
                defaultBorderWidth);
        generateFeeContent(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
                defaultBorderWidth);
        generateFeePaymentSummary(document, documentLayoutSetup, instituteId, contentFontSize, feePaymentInvoiceSummary,
                defaultBorderWidth);
        if(studentFeesDetailsLite != null && studentFeesDetailsLite.getStudentLite() != null &&
                studentFeesDetailsLite.getStudentLite().getStudentId() != null) {
            generateStudentFeeSummary(document, documentLayoutSetup, contentFontSize, defaultBorderWidth, studentFeesDetailsLite);
        }
        generateSignatureBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth);
        if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
                .getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.CANCELLED) {
            PdfPage pdfPage = document.getPdfDocument().getPage(pageNumber);
            Rectangle pagesize = pdfPage.getPageSizeWithRotation();

            float x = documentLayoutSetup.isOfficeCopy() ? pagesize.getWidth() / 8 - 50 : pagesize.getWidth() / 4 + 30;
            float y = documentLayoutSetup.isOfficeCopy() ? pagesize.getHeight() * 0.75f : pagesize.getHeight() * 0.70f;

            addWaterMark(document, ImageProvider.INSTANCE.getImage(ImageProvider.CANCELLED_TEXT_IMAGE2), x, y, pageNumber);
        }
    }

    public void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                     float defaultBorderWidth) throws IOException {
        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.RIGHT).setBorder(new SolidBorder(defaultBorderWidth))
                .setBorderTop(null);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)),
                signatureCellLayoutSetup.copy().setBorderBottom(null));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)),
                signatureCellLayoutSetup.copy().setBorderBottom(null));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)),
                signatureCellLayoutSetup.copy().setBorderBottom(null));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)),
                signatureCellLayoutSetup.copy().setBorderBottom(null));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Signature")), signatureCellLayoutSetup);
        document.add(table);
    }

    private void generateStudentFeeSummary(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                           float defaultBorderWidth, StudentFeesDetailsLite studentFeesDetailsLite) throws IOException {
        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.CENTER).setBorder(new SolidBorder(defaultBorderWidth));

//        addRow(table, documentLayoutSetup, Arrays.asList(new CellData("", signatureCellLayoutSetup.copy().setBorderBottom(null))));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("STUDENT FEE SUMMARY")), signatureCellLayoutSetup.copy().setBorderBottom(null));
        document.add(table);

        singleContentColumn = 3;
        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup boldCellLayoutSetup = new CellLayoutSetup();
        boldCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.CENTER).setBorder(new SolidBorder(defaultBorderWidth)).setBorderRight(null);

        CellLayoutSetup regularCellLayoutSetup = new CellLayoutSetup();
        regularCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.CENTER).setBorder(new SolidBorder(defaultBorderWidth)).setBorderRight(null);

//        addRow(table, documentLayoutSetup, Arrays.asList(
//                new CellData("", regularCellLayoutSetup.copy().setBorderTop(null).setBorderBottom(null).setBorderLeft(new SolidBorder(defaultBorderWidth))),
//                new CellData("", regularCellLayoutSetup.copy().setBorderTop(null).setBorderBottom(null).setBorderLeft(null).setBorderRight(null)),
//                new CellData("", regularCellLayoutSetup.copy().setBorderTop(null).setBorderBottom(null).setBorderRight(new SolidBorder(defaultBorderWidth)))));

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData("Total Fees", boldCellLayoutSetup),
                new CellData("Session Dues", boldCellLayoutSetup),
                new CellData("Due Fees Till Today", boldCellLayoutSetup.copy().setBorderRight(new SolidBorder(defaultBorderWidth)))));

        double totalFees = studentFeesDetailsLite.getTotalFees();
        double sessionDues = studentFeesDetailsLite.getTotalDueFees();
        double duesTillToday = studentFeesDetailsLite.getTotalDueAmountTillToday();
        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(String.valueOf(totalFees), regularCellLayoutSetup),
                new CellData(String.valueOf(sessionDues), regularCellLayoutSetup),
                new CellData(String.valueOf(duesTillToday), regularCellLayoutSetup.copy().setBorderRight(new SolidBorder(defaultBorderWidth)))));

        document.add(table);
    }

    @Override
    public DocumentOutput generateBulkInvoices(Institute institute, List<FeePaymentInvoiceSummary>
            feePaymentInvoiceSummaryList, String documentName, boolean doubleCopy, FeeInvoicePreferences feeInvoicePreferences) {
        try {
            DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(doubleCopy);
            float contentFontSize = 8f;
            float headerFontSize = 20f;
            float defaultBorderWidth = 0.1f;
            Document document = initDocument(invoice.getContent(), documentLayoutSetup);
            DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getNunitoRegularFont(), getAleoBoldFont(), null, null, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), getGlacialRegularFont(), getHindiBoldFont());
            
            if (CollectionUtils.isEmpty(feePaymentInvoiceSummaryList)) {
                document.close();
                return invoice;
            }

            int pageNumber = 1;
            for (FeePaymentInvoiceSummary feePaymentInvoiceSummary : feePaymentInvoiceSummaryList) {
                generateFeeInvoice(institute, documentLayoutData, feePaymentInvoiceSummary,
                        headerFontSize, defaultBorderWidth, institute.getInstituteId(), contentFontSize, pageNumber,
                        OFFICE_COPY_TEXT, null, null);

                if (pageNumber != feePaymentInvoiceSummaryList.size()) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;

            }
            document.close();
            return invoice;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
                               FeePaymentInvoiceSummary feePaymentInvoiceSummary, Institute institute, float headerFontSize,
                               float defaultBorderWidth, String studentCopyText, UserType userType) throws IOException {
        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(getRegularBoldFont()).setTextAlignment(TextAlignment.CENTER);
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName())),
                cellLayoutSetup.setFontSize(headerFontSize));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
                cellLayoutSetup.copy().setFontSize(headerFontSize - 10f));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
                cellLayoutSetup.copy().setFontSize(headerFontSize - 10f));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup.setFontSize(9f));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("School Fee Receipt (" + feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse()
                        .getAcademicSession().getYearDisplayName() + ")")),
                Arrays.asList(getParagraph("School Fee Receipt (" + feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse()
                        .getAcademicSession().getYearDisplayName() + ")")), cellLayoutSetup);

        document.add(table);

        // Student copy section
        singleContentColumn = 3;
        table = getPDFTable(documentLayoutSetup, singleContentColumn);
        cellLayoutSetup.setBorderBottom(new SolidBorder(defaultBorderWidth));

        Paragraph receipt = getKeyValueParagraph("Receipt No.: ",
                feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getInvoiceId());

        Paragraph transactionMode = getKeyValueParagraph("Mode: ",
                feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getTransactionModeDisplayName() == null ? EMPTY_TEXT :
                        feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getTransactionModeDisplayName());

        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(receipt, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
                        new CellData(studentCopyText, cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
                        new CellData(transactionMode, cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))),
                Arrays.asList(new CellData(receipt, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
                        new CellData(userType == UserType.STUDENT ? STUDENT_COPY_TEXT : OFFICE_COPY_TEXT, cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
                        new CellData(transactionMode, cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));
        document.add(table);

    }

    public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
        return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE : DEFAULT_PAGE_SIZE,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
    }

}
