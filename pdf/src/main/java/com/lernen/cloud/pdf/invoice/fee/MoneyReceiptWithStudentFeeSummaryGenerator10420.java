package com.lernen.cloud.pdf.invoice.fee;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.layout.LayoutArea;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.FeeInvoicePreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.*;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MoneyReceiptWithStudentFeeSummaryGenerator10420 extends GlobalMoneyReceiptWithStudentFeeSummaryGenerator {

    public MoneyReceiptWithStudentFeeSummaryGenerator10420(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    public static final float LOGO_WIDTH = 60f;
    public static final float LOGO_HEIGHT = 60f;

    @Override
    public DocumentOutput generateInvoice(Institute institute, FeePaymentInvoiceSummary feePaymentInvoiceSummary,
                                          StudentTransportDetails studentTransportDetails, String documentName, boolean officeCopy, UserType userType, StudentFeesDetailsLite studentFeesDetailsLite, FeeInvoicePreferences feeInvoicePreferences) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            if(userType == UserType.STUDENT) {
                officeCopy = false;
            }
            DocumentLayoutData documentLayoutData = generatefeeInvoiceLayoutData(institute, documentOutput);
            DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
            Document document = documentLayoutData.getDocument();
            float headerFontSize = 10f;

            int instituteId = institute.getInstituteId();
            int pageNumber = 1;
            boolean addWaterMark = true;
            generateFeeInvoice(institute, documentLayoutData, feePaymentInvoiceSummary,
                    headerFontSize, documentLayoutData.getDefaultBorderWidth(), institute.getInstituteId(), documentLayoutData.getContentFontSize(),
                    1, OFFICE_COPY_TEXT, userType, studentFeesDetailsLite, officeCopy);
            addWaterMark = false;
            LayoutArea currentArea = document.getRenderer().getCurrentArea();
            Rectangle rectangle = currentArea.getBBox();

            if(!officeCopy) {
                document.close();
                return documentOutput;
            }

            /**
             *  rectangle.getHeight() - gives position of last rectangle from bottom,
             *  so if position of last rectangle is below than the middle of the page + 20f
             *  adding next page
             */
            if(rectangle.getHeight() < (documentLayoutSetup.getPageSize().getHeight() / 2) + 20) {
                document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                addWaterMark = true;
                pageNumber++;
            } else {
                /**
                 *  adding newline for second slip
                 */
                while(rectangle.getHeight() > ((documentLayoutSetup.getPageSize().getHeight() / 2) - 20)) {
                    addBlankLine(document, false, 1);
                    currentArea = document.getRenderer().getCurrentArea();
                    rectangle = currentArea.getBBox();
                }
            }

            generateFeeInvoice(institute, documentLayoutData, feePaymentInvoiceSummary,
                    headerFontSize, documentLayoutData.getDefaultBorderWidth(), institute.getInstituteId(), documentLayoutData.getContentFontSize(),
                    pageNumber, STUDENT_COPY_TEXT, userType, studentFeesDetailsLite, officeCopy);
            document.close();
            return documentOutput;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    protected DocumentLayoutData generatefeeInvoiceLayoutData(Institute institute, DocumentOutput documentOutput)
            throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
        float contentFontSize = 7f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        int instituteId = institute.getInstituteId();
        return new DocumentLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
                defaultBorderWidth, LOGO_WIDTH, LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(instituteId), null, null);
    }

    public void generateFeeInvoice(Institute institute, DocumentLayoutData documentLayoutData,
                                   FeePaymentInvoiceSummary feePaymentInvoiceSummary,
                                   float headerFontSize, float defaultBorderWidth, int instituteId, float contentFontSize, int pageNumber,
                                   String studentCopyText, UserType userType, StudentFeesDetailsLite studentFeesDetailsLite, boolean isOfficeCopy) throws IOException {

        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();

        float offsetX = 25f;
        float offsetY = (documentLayoutSetup.getPageSize().getHeight() * 0.95f) - documentLayoutData.getLogoHeight();
        InstituteDocumentType instituteDocumentType = InstituteDocumentType.INSTITUTE_PRIMARY_LOGO;
        if(isOfficeCopy) {
            if(pageNumber == 1) {
                LayoutArea currentArea = document.getRenderer().getCurrentArea();
                Rectangle rectangle = currentArea.getBBox();
                generateDynamicImageProvider(documentLayoutData, offsetX,  rectangle.getHeight() - (documentLayoutData.getLogoHeight() * 0.90f), instituteId, instituteDocumentType);
            } else {
                generateDynamicImageProvider(documentLayoutData, offsetX, offsetY, instituteId, instituteDocumentType);
            }
        } else {
            generateDynamicImageProvider(documentLayoutData, offsetX, offsetY, instituteId, instituteDocumentType);
        }

        generateHeader(document, documentLayoutSetup, feePaymentInvoiceSummary, institute, headerFontSize,
                defaultBorderWidth, studentCopyText, userType, isOfficeCopy);
        generateStudentInformation(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
                defaultBorderWidth);
        generateFeeContent(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
                defaultBorderWidth);
        generateFeePaymentSummary(document, documentLayoutSetup, instituteId, contentFontSize, feePaymentInvoiceSummary,
                defaultBorderWidth);
        if(studentFeesDetailsLite != null && studentFeesDetailsLite.getStudentLite() != null &&
                studentFeesDetailsLite.getStudentLite().getStudentId() != null) {
            generateStudentFeeSummary(document, documentLayoutSetup, contentFontSize, defaultBorderWidth, studentFeesDetailsLite);
        }
        generateSignatureBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth);
        if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
                .getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.CANCELLED) {
            PdfPage pdfPage = document.getPdfDocument().getPage(pageNumber);
            Rectangle pagesize = pdfPage.getPageSizeWithRotation();

            float x = documentLayoutSetup.isOfficeCopy() ? pagesize.getWidth() / 8 - 50 : pagesize.getWidth() / 4 + 30;
            float y = documentLayoutSetup.isOfficeCopy() ? pagesize.getHeight() * 0.75f : pagesize.getHeight() * 0.50f;

            addWaterMark(document, ImageProvider.INSTANCE.getImage(ImageProvider.CANCELLED_TEXT_IMAGE2), x, y, pageNumber);
        }
    }

    public void generateStudentFeeSummary(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                          float defaultBorderWidth, StudentFeesDetailsLite studentFeesDetailsLite) throws IOException {
        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.CENTER).setBorder(new SolidBorder(defaultBorderWidth))
                .setBorderTop(null);

//        addRow(table, documentLayoutSetup, Arrays.asList(new CellData("", signatureCellLayoutSetup.copy().setBorderBottom(null))));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("STUDENT FEE SUMMARY")), signatureCellLayoutSetup.copy().setBorderBottom(null).setBorderTop(null));
        document.add(table);

        singleContentColumn = 3;
        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup boldCellLayoutSetup = new CellLayoutSetup();
        boldCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.CENTER).setBorder(new SolidBorder(defaultBorderWidth)).setBorderRight(null);

        CellLayoutSetup regularCellLayoutSetup = new CellLayoutSetup();
        regularCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.CENTER).setBorder(new SolidBorder(defaultBorderWidth)).setBorderRight(null);

//        addRow(table, documentLayoutSetup, Arrays.asList(
//                new CellData("", regularCellLayoutSetup.copy().setBorderTop(null).setBorderBottom(null).setBorderLeft(new SolidBorder(defaultBorderWidth))),
//                new CellData("", regularCellLayoutSetup.copy().setBorderTop(null).setBorderBottom(null).setBorderLeft(null).setBorderRight(null)),
//                new CellData("", regularCellLayoutSetup.copy().setBorderTop(null).setBorderBottom(null).setBorderRight(new SolidBorder(defaultBorderWidth)))));

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData("Total Fees", boldCellLayoutSetup),
                new CellData("Session Dues", boldCellLayoutSetup),
                new CellData("Due Fees Till Today", boldCellLayoutSetup.copy().setBorderRight(new SolidBorder(defaultBorderWidth)))));

        double totalFees = studentFeesDetailsLite.getTotalFees();
        double sessionDues = studentFeesDetailsLite.getTotalDueFees();
        double duesTillToday = studentFeesDetailsLite.getTotalDueAmountTillToday();
        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(String.valueOf(totalFees), regularCellLayoutSetup),
                new CellData(String.valueOf(sessionDues), regularCellLayoutSetup),
                new CellData(String.valueOf(duesTillToday), regularCellLayoutSetup.copy().setBorderRight(new SolidBorder(defaultBorderWidth)))));

        document.add(table);
    }

    public void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
                               FeePaymentInvoiceSummary feePaymentInvoiceSummary, Institute institute, float headerFontSize,
                               float defaultBorderWidth, String studentCopyText, UserType userType, boolean isOfficeCopy) throws IOException {
        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(getRegularBoldFont()).setTextAlignment(TextAlignment.CENTER).setFontSize(headerFontSize);

        String instituteName = institute.getInstituteName();
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(instituteName)),
                cellLayoutSetup.copy().setFontSize(headerFontSize + 2).setPaddingTop(5f));
        String letterHead1 = institute.getLetterHeadLine1();
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(letterHead1)),
                cellLayoutSetup.copy().setFontSize(headerFontSize - 3f));
        String letterHead2 = institute.getLetterHeadLine2();
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(letterHead2)),
                cellLayoutSetup.copy().setFontSize(headerFontSize - 3f));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup.copy().setFontSize(9f));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Fee Receipt (" + feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse()
                        .getAcademicSession().getYearDisplayName() + ")")),
                Arrays.asList(getParagraph("Fee Receipt (" + feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse()
                        .getAcademicSession().getYearDisplayName() + ")")), cellLayoutSetup);

        document.add(table);

        // Student copy section
        singleContentColumn = 3;
        table = getPDFTable(documentLayoutSetup, singleContentColumn);
        cellLayoutSetup.setBorderBottom(new SolidBorder(defaultBorderWidth));
//
//        Paragraph receipt = getKeyValueParagraph("Receipt No.: ",
//                feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getInvoiceId());

        Paragraph transactionMode = getKeyValueParagraph("Mode: ",
                feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getTransactionModeDisplayName() == null ? EMPTY_TEXT :
                        feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getTransactionModeDisplayName());

        String str = "";
        if(isOfficeCopy) {
            str = OFFICE_COPY_TEXT;
        } else {
            str = STUDENT_COPY_TEXT;
        }

        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(EMPTY_TEXT, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
                        new CellData(studentCopyText, cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
                        new CellData(transactionMode, cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))),
                Arrays.asList(new CellData(EMPTY_TEXT, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
                        new CellData(userType == UserType.STUDENT ? STUDENT_COPY_TEXT : str, cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
                        new CellData(transactionMode, cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));
        document.add(table);

    }

    public void generateStudentInformation(Document document, DocumentLayoutSetup documentLayoutSetup,
                                           float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
            throws IOException {

        Table table = getPDFTable(documentLayoutSetup, new float[] { 0.35f, 0.3f, 0.35f });

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize);

        CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)
                .setBorderLeft(new SolidBorder(defaultBorderWidth));
        CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
        CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT)
                .setBorderRight(new SolidBorder(defaultBorderWidth));

        Paragraph studentName = getKeyValueParagraph("Student Name : ",
                feePaymentInvoiceSummary.getStudent().getStudentBasicInfo().getName());

        Paragraph date = getKeyValueParagraph("Date: ",
                DateUtils.getFormattedDate(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getTransactionDate(),
                        DATE_FORMAT, User.DFAULT_TIMEZONE));

        Paragraph receipt = getKeyValueParagraph("Receipt No.: ",
                feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getInvoiceId());

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(studentName, firstCellLayoutSetup),
                new CellData(date, secondCellLayoutSetup.setPaddingLeft(60f)), new CellData(receipt, thirdCellLayoutSetup)));

        Paragraph fatherName = getKeyValueParagraph("Father Name : ",
                feePaymentInvoiceSummary.getStudent().getStudentFamilyInfo().getFathersName());
        Paragraph admissionNumber = getKeyValueParagraph("Admission No. ",
                feePaymentInvoiceSummary.getStudent().getStudentBasicInfo().getAdmissionNumber());
        Paragraph classValue = getKeyValueParagraph("Class : ", feePaymentInvoiceSummary.getStudent()
                .getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection())
                .setFont(getRegularFont());
        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(fatherName, firstCellLayoutSetup.setBorderBottom(new SolidBorder(defaultBorderWidth))),
                new CellData(classValue, secondCellLayoutSetup.setPaddingLeft(60f).setBorderBottom(new SolidBorder(defaultBorderWidth))), new CellData(admissionNumber, thirdCellLayoutSetup.setBorderBottom(new SolidBorder(defaultBorderWidth)))));

//                Paragraph motherName = getKeyValueParagraph("Mother Name : ",
//                feePaymentInvoiceSummary.getStudent().getStudentFamilyInfo().getMothersName());



//        addRow(table, documentLayoutSetup, Arrays.asList(
//                new CellData(motherName, firstCellLayoutSetup.setBorderBottom(new SolidBorder(defaultBorderWidth))),
//                new CellData(classValue, thirdCellLayoutSetup.setBorderBottom(new SolidBorder(defaultBorderWidth)))));

        document.add(table);
    }

    public void generateFeeContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                   FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth) throws IOException {

        if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
                .getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.ACTIVE) {
            generateActiveFeeContent(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
                    defaultBorderWidth);
        } else {
            generateCancelledFeeContent(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
                    defaultBorderWidth);
        }
    }

    public void generateCancelledFeeContent(Document document, DocumentLayoutSetup documentLayoutSetup,
                                            float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
            throws IOException {

        if (feePaymentInvoiceSummary.getFeePaymentTransactionDetails() == null || CollectionUtils.isEmpty(
                feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getFeeIdFeeHeadTransactionDetails())) {
            return;
        }

        int singleFeeContentColumn = 1;
        CellLayoutSetup feeCellLayoutSetup = new CellLayoutSetup();
        feeCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth))
                .setBorderRight(new SolidBorder(defaultBorderWidth));

        int count = 0;
        for (FeeIdFeeHeadTransactionDetails feeIdFeeHeadTransactionDetails : feePaymentInvoiceSummary
                .getFeePaymentTransactionDetails().getFeeIdFeeHeadTransactionDetails()) {
            count++;
            Table feeTable = getPDFTable(documentLayoutSetup, singleFeeContentColumn);
            addRow(feeTable, documentLayoutSetup,
                    Arrays.asList(
                            getParagraph(feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo().getFeeName())),
                    feeCellLayoutSetup);
            document.add(feeTable);

            Table feeHeadTable = getPDFTable(documentLayoutSetup, DEFAULT_CANCELLED_FEE_HEAD_HEADER_WIDTH);

            CellLayoutSetup feeHeadCellLayoutSetup = new CellLayoutSetup();
            feeHeadCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                    .setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.LEFT);

            if (count == 1) {
                addRow(feeHeadTable, documentLayoutSetup,
                        Arrays.asList(new CellData("#", feeHeadCellLayoutSetup),
                                new CellData("Particulars", feeHeadCellLayoutSetup),
                                new CellData("Paid Amount (INR)", feeHeadCellLayoutSetup)));
            }
            int feeHeadCount = 1;
            feeHeadCellLayoutSetup.setPdfFont(getRegularFont());
            for (FeeHeadTransactionAmountsDetails feeHeadTransactionAmountsDetails : feeIdFeeHeadTransactionDetails
                    .getFeeHeadTransactionAmountsDetails()) {
                addRow(feeHeadTable, documentLayoutSetup,
                        Arrays.asList(new CellData(String.valueOf(feeHeadCount++), feeHeadCellLayoutSetup),
                                new CellData(feeHeadTransactionAmountsDetails.getFeeHeadConfiguration().getFeeHead(),
                                        feeHeadCellLayoutSetup),
                                new CellData(String.valueOf(feeHeadTransactionAmountsDetails.getPaidAmount()),
                                        feeHeadCellLayoutSetup)));
            }
            document.add(feeHeadTable);
        }
    }

    public void generateActiveFeeContent(Document document, DocumentLayoutSetup documentLayoutSetup,
                                         float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
            throws IOException {

        if (CollectionUtils.isEmpty(feePaymentInvoiceSummary.getFeeIdInvoices())) {
            return;
        }

        int singleFeeContentColumn = 1;
        CellLayoutSetup feeCellLayoutSetup = new CellLayoutSetup();
        feeCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth))
                .setBorderRight(new SolidBorder(defaultBorderWidth));

        int count = 0;
        for (FeeIdInvoice feeIdInvoice : feePaymentInvoiceSummary.getFeeIdInvoices()) {
            count++;
            Table feeHeadTable = getPDFTable(documentLayoutSetup, DEFAULT_ACTIVE_MONEY_RECEIPT_FEE_HEAD_HEADER_WIDTH);

            CellLayoutSetup feeHeadCellLayoutSetup = new CellLayoutSetup();
            feeHeadCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                    .setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.LEFT);

            if (count == 1) {
                addRow(feeHeadTable, documentLayoutSetup,
                        Arrays.asList(new CellData("#", feeHeadCellLayoutSetup),
                                new CellData("Particulars", feeHeadCellLayoutSetup),
                                new CellData("Paid Amount (INR)", feeHeadCellLayoutSetup)));
            }
            Table feeTable = getPDFTable(documentLayoutSetup, singleFeeContentColumn);
            addRow(feeTable, documentLayoutSetup,
                    Arrays.asList(getParagraph(feeIdInvoice.getFeeConfigurationBasicInfo().getFeeName())),
                    feeCellLayoutSetup);
            document.add(feeTable);
            int feeHeadCount = 1;
            feeHeadCellLayoutSetup.setPdfFont(getRegularFont());
            for (FeeHeadInvoice feeHeadInvoice : feeIdInvoice.getFeeHeadInvoices()) {
                addRow(feeHeadTable, documentLayoutSetup, Arrays.asList(
                        new CellData(String.valueOf(feeHeadCount++), feeHeadCellLayoutSetup),
                        new CellData(feeHeadInvoice.getFeeHeadPaymentDetails().getFeeHeadConfiguration().getFeeHead(),
                                feeHeadCellLayoutSetup),
                        new CellData(String.valueOf(feeHeadInvoice.getFeeHeadTransactionAmounts().getPaidAmount()),
                                feeHeadCellLayoutSetup)));
            }
            document.add(feeHeadTable);
        }

    }

    public void generateFeePaymentSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
                                          int instituteId, float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
            throws IOException {

        if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
                .getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.ACTIVE) {
            generateActiveFeePaymentSummary(document, documentLayoutSetup, contentFontSize, instituteId, feePaymentInvoiceSummary,
                    defaultBorderWidth);
        } else {
            generateCancelledFeePaymentSummary(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
                    defaultBorderWidth);
        }
    }

    public void generateCancelledFeePaymentSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
                                                   float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
            throws IOException {

        int singleContentColumn = 2;
        Table table = getPDFTable(documentLayoutSetup, new float[]{0.7f, 0.3f});

        CellLayoutSetup keyCellLayoutSetup = new CellLayoutSetup();
        keyCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(defaultBorderWidth));

        CellLayoutSetup valueCellLayoutSetup = keyCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)
                .setBorder(new SolidBorder(defaultBorderWidth));

        if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount(),
                0d) > 0) {
            addRow(table, documentLayoutSetup,
                    Arrays.asList(new CellData("Used Wallet Amount (INR):", keyCellLayoutSetup),
                            new CellData(String.valueOf(
                                    feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount()),
                                    valueCellLayoutSetup)));
        }
        if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getCreditWalletAmount(),
                0d) > 0) {
            addRow(table,
                    documentLayoutSetup, Arrays
                            .asList(new CellData("Credit Wallet Amount (INR):", keyCellLayoutSetup),
                                    new CellData(String.valueOf(feePaymentInvoiceSummary
                                            .getFeePaymentTransactionMetaData().getCreditWalletAmount()),
                                            valueCellLayoutSetup)));
        }

        if (Double.compare(feePaymentInvoiceSummary.getTotalFineAmount(), 0d) > 0) {
            addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Fine Amount (INR):", keyCellLayoutSetup),
                    new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalFineAmount()), valueCellLayoutSetup)));
        }

        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData("Paid Amount (INR):", keyCellLayoutSetup),
                        new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalCurrentTransactionPaidAmount()),
                                valueCellLayoutSetup)));

        document.add(table);

        String remark = feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getRemark();
        if (!StringUtils.isBlank(remark)) {
            singleContentColumn = 1;
            table = getPDFTable(documentLayoutSetup, singleContentColumn);

            Paragraph remarkPar = getKeyValueParagraph("Remark: ", remark);
            CellLayoutSetup remarkCellLayoutSetup = new CellLayoutSetup();
            remarkCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                    .setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth))
                    .setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderBottom(new SolidBorder(defaultBorderWidth));
            addRow(table, documentLayoutSetup, Arrays.asList(remarkPar), remarkCellLayoutSetup);
            document.add(table);
        }

        String transactionReference = feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getTransactionReference();
        if (!StringUtils.isBlank(transactionReference)) {
            singleContentColumn = 1;
            table = getPDFTable(documentLayoutSetup, singleContentColumn);

            Paragraph transactionReferencePar = getKeyValueParagraph("Reference: ", transactionReference);
            CellLayoutSetup remarkCellLayoutSetup = new CellLayoutSetup();
            remarkCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                    .setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth))
                    .setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderBottom(new SolidBorder(defaultBorderWidth));
            addRow(table, documentLayoutSetup, Arrays.asList(transactionReferencePar), remarkCellLayoutSetup);
            document.add(table);
        }
    }

    public void generateActiveFeePaymentSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
                                                float contentFontSize, int instituteId, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
            throws IOException {
        CellLayoutSetup keyCellLayoutSetup = new CellLayoutSetup();
        keyCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(defaultBorderWidth));

        CellLayoutSetup valueCellLayoutSetup = keyCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)
                .setBorder(new SolidBorder(defaultBorderWidth));

        int singleContentColumn = 2;
        Table table = getPDFTable(documentLayoutSetup, new float[]{0.7f, 0.3f});

        if (Double.compare(feePaymentInvoiceSummary.getTotalFineAmount(), 0d) > 0) {
            addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Fine Amount (INR):", keyCellLayoutSetup),
                    new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalFineAmount()), valueCellLayoutSetup)));
        }

        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData("Total Fees Paid Amount (INR):", keyCellLayoutSetup),
                        new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalCurrentTransactionPaidAmount()),
                                valueCellLayoutSetup)));

        if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount(),
                0d) > 0) {
            double paidAmountByMode = feePaymentInvoiceSummary.getTotalCurrentTransactionPaidAmount()
                    - feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount();

            addRow(table, documentLayoutSetup,
                    Arrays.asList(new CellData(String.format("Paid Amount (%s) (INR):", feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getTransactionModeDisplayName()), keyCellLayoutSetup),
                            new CellData(String.valueOf(paidAmountByMode),
                                    valueCellLayoutSetup)));

            addRow(table, documentLayoutSetup,
                    Arrays.asList(new CellData("Used Wallet Amount (INR):", keyCellLayoutSetup),
                            new CellData(String.valueOf(
                                    feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount()),
                                    valueCellLayoutSetup)));
        }
        if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getCreditWalletAmount(),
                0d) > 0) {
            addRow(table,
                    documentLayoutSetup, Arrays
                            .asList(new CellData("Credit Wallet Amount (INR):", keyCellLayoutSetup),
                                    new CellData(String.valueOf(feePaymentInvoiceSummary
                                            .getFeePaymentTransactionMetaData().getCreditWalletAmount()),
                                            valueCellLayoutSetup)));
        }



        document.add(table);

        String remark = feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getRemark();
        if (!StringUtils.isBlank(remark)) {
            singleContentColumn = 1;
            table = getPDFTable(documentLayoutSetup, singleContentColumn);

            Paragraph remarkPar = getKeyValueParagraph("Remark: ", remark);
            CellLayoutSetup remarkCellLayoutSetup = new CellLayoutSetup();
            remarkCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                    .setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth))
                    .setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderBottom(new SolidBorder(defaultBorderWidth));
            addRow(table, documentLayoutSetup, Arrays.asList(remarkPar), remarkCellLayoutSetup);
            document.add(table);
        }

        String transactionReference = feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getTransactionReference();
        if (!StringUtils.isBlank(transactionReference)) {
            singleContentColumn = 1;
            table = getPDFTable(documentLayoutSetup, singleContentColumn);

            Paragraph transactionReferencePar = getKeyValueParagraph("Reference: ", transactionReference);
            CellLayoutSetup remarkCellLayoutSetup = new CellLayoutSetup();
            remarkCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                    .setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth))
                    .setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderBottom(new SolidBorder(defaultBorderWidth));
            addRow(table, documentLayoutSetup, Arrays.asList(transactionReferencePar), remarkCellLayoutSetup);
            document.add(table);
        }


    }

    public void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                     float defaultBorderWidth) throws IOException {
        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.RIGHT).setBorder(new SolidBorder(defaultBorderWidth))
                .setBorderTop(null);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), signatureCellLayoutSetup.copy().setBorderBottom(null));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), signatureCellLayoutSetup.copy().setBorderBottom(null));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), signatureCellLayoutSetup.copy().setBorderBottom(null));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), signatureCellLayoutSetup.copy().setBorderBottom(null));
        document.add(table);
        singleContentColumn = 2;
        table = getPDFTable(documentLayoutSetup, singleContentColumn);
        addRow(table, documentLayoutSetup, Arrays.asList( new CellData("Fee once deposited is non-refundable.", signatureCellLayoutSetup.copy().setBorderRight(null).setTextAlignment(TextAlignment.LEFT).setFontSize(8f)),  new CellData("Signature", signatureCellLayoutSetup.setBorderLeft(null))));
        document.add(table);
    }

    @Override
    public DocumentOutput generateBulkInvoices(Institute institute, List<FeePaymentInvoiceSummary>
            feePaymentInvoiceSummaryList, String documentName, boolean doubleCopy, FeeInvoicePreferences feeInvoicePreferences) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData documentLayoutData = generatefeeInvoiceLayoutData(institute, documentOutput);
            DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
            Document document = documentLayoutData.getDocument();
            float contentFontSize = 7f;
            float headerFontSize = 10f;
            float defaultBorderWidth = 0.1f;

            if (CollectionUtils.isEmpty(feePaymentInvoiceSummaryList)) {
                document.close();
                return documentOutput;
            }

            int pageNumber = 1;
            for (FeePaymentInvoiceSummary feePaymentInvoiceSummary : feePaymentInvoiceSummaryList) {
                generateFeeInvoice(institute, documentLayoutData, feePaymentInvoiceSummary,
                        headerFontSize, defaultBorderWidth, institute.getInstituteId(), contentFontSize, pageNumber,
                        OFFICE_COPY_TEXT, null, null, doubleCopy);

                if (pageNumber != feePaymentInvoiceSummaryList.size()) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;

            }
            document.close();
            return documentOutput;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
