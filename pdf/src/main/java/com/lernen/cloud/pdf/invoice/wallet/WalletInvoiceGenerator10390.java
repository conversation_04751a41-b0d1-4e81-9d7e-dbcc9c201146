package com.lernen.cloud.pdf.invoice.wallet;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.api.wallet.BulkWalletTransactionInvoiceDocumentDetails;
import com.embrate.cloud.core.api.wallet.WalletTransactionInvoiceDocumentDetails;
import com.embrate.cloud.core.api.wallet.WalletTransactionPayload;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.FeeInvoicePreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class WalletInvoiceGenerator10390 extends GlobalWalletInvoiceGenerator {

    public static final float DEFAULT_LOGO_WIDTH = 40f;
    public static final float DEFAULT_LOGO_HEIGHT = 40f;

    public WalletInvoiceGenerator10390(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    @Override
    public DocumentOutput generateWalletInvoice(WalletTransactionInvoiceDocumentDetails walletTransactionInvoiceDocumentDetails) {
        try {

            final String documentName = walletTransactionInvoiceDocumentDetails.getDocumentName();
            boolean officeCopy = walletTransactionInvoiceDocumentDetails.isOfficeCopy();
            final UserType userType = walletTransactionInvoiceDocumentDetails.getUserType();
            final Institute institute = walletTransactionInvoiceDocumentDetails.getInstitute();
            final Student student = walletTransactionInvoiceDocumentDetails.getStudent();
            final WalletTransactionPayload walletTransactionPayload = walletTransactionInvoiceDocumentDetails.getWalletTransactionPayload();
            final FeeInvoicePreferences feeInvoicePreferences = walletTransactionInvoiceDocumentDetails.getFeeInvoicePreferences();

            DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
            if (userType == UserType.STUDENT) {
                officeCopy = false;
            }

            PageSize pageSize = feeInvoicePreferences.getPageSize() == null ? PageSize.A5.rotate() : feeInvoicePreferences.getPageSize();
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize, officeCopy);

            float contentFontSize = 7.5f;
            float headerFontSize = 9f;
            float defaultBorderWidth = 0.1f;
            Document document = initDocument(invoice.getContent(), documentLayoutSetup);
            DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getNunitoRegularFont(), getAleoBoldFont(), contentFontSize, defaultBorderWidth, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), getGlacialRegularFont(), getHindiBoldFont());

            generateWalletInvoice(institute, documentLayoutData, walletTransactionPayload, student,
                    headerFontSize, defaultBorderWidth, institute.getInstituteId(), contentFontSize,
                    1, STUDENT_COPY_TEXT, userType);

            document.close();
            return invoice;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public void generateWalletInvoice(Institute institute, DocumentLayoutData documentLayoutData,
                                      WalletTransactionPayload walletTransactionPayload, Student student,
                                      float headerFontSize, float defaultBorderWidth, int instituteId, float contentFontSize, int pageNumber,
                                      String studentCopyText, UserType userType) throws IOException {

        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();

        generateDynamicImageProvider(documentLayoutData, 10, 0, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

        generateHeader(document, documentLayoutSetup, walletTransactionPayload, institute, headerFontSize,
                defaultBorderWidth, studentCopyText, userType, "Imprest Account Receipt");
        generateStudentInformation(document, documentLayoutSetup, contentFontSize, walletTransactionPayload, student,
                defaultBorderWidth);
        generateFeeContent(document, documentLayoutSetup, contentFontSize, walletTransactionPayload,
                defaultBorderWidth, "Imprest Account Recharge");
        generateSignatureBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth, 0f);
    }

    @Override
    public DocumentOutput generateBulkWalletInvoices(BulkWalletTransactionInvoiceDocumentDetails bulkWalletTransactionInvoiceDocumentDetails) {
        try {
            final String documentName = bulkWalletTransactionInvoiceDocumentDetails.getDocumentName();
            final boolean officeCopy = bulkWalletTransactionInvoiceDocumentDetails.isOfficeCopy();
            final Institute institute = bulkWalletTransactionInvoiceDocumentDetails.getInstitute();
            final Map<UUID, Student> studentMap = bulkWalletTransactionInvoiceDocumentDetails.getStudentMap();
            final Map<UUID, WalletTransactionPayload> walletTransactionPayloadMap = bulkWalletTransactionInvoiceDocumentDetails.getWalletTransactionPayloadMap();
            final FeeInvoicePreferences feeInvoicePreferences = bulkWalletTransactionInvoiceDocumentDetails.getFeeInvoicePreferences();

            DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
            PageSize pageSize = feeInvoicePreferences.getPageSize() == null ? PageSize.A5.rotate() : feeInvoicePreferences.getPageSize();
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize, officeCopy);
            float contentFontSize = 7.5f;
            float headerFontSize = 9f;
            float defaultBorderWidth = 0.1f;
            Document document = initDocument(invoice.getContent(), documentLayoutSetup);
            DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getNunitoRegularFont(), getAleoBoldFont(), contentFontSize, defaultBorderWidth, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), getGlacialRegularFont(), getHindiBoldFont());

            if (CollectionUtils.isEmpty(walletTransactionPayloadMap) || CollectionUtils.isEmpty(studentMap)) {
                document.close();
                return invoice;
            }

            int pageNumber = 1;
            for (Map.Entry<UUID, WalletTransactionPayload> walletTransactionPayloadEntry : walletTransactionPayloadMap.entrySet()) {
                UUID studentId = walletTransactionPayloadEntry.getKey();
                WalletTransactionPayload walletTransactionPayload = walletTransactionPayloadEntry.getValue();
                Student student = studentMap.get(studentId);
                generateWalletInvoice(institute, documentLayoutData, walletTransactionPayload, student,
                        headerFontSize, defaultBorderWidth, institute.getInstituteId(), contentFontSize, pageNumber,
                        OFFICE_COPY_TEXT, null);

                if (pageNumber != walletTransactionPayloadMap.size()) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;

            }
            document.close();
            return invoice;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
