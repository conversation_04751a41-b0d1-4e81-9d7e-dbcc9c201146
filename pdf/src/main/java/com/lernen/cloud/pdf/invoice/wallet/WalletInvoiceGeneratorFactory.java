package com.lernen.cloud.pdf.invoice.wallet;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.pdf.invoice.fee.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class WalletInvoiceGeneratorFactory {

	private static final Map<Integer, WalletInvoiceGenerator> INSTITUTE_WALLET_INVOICE_GENERATOR = new HashMap<>();
	private final AssetProvider assetProvider;

	public WalletInvoiceGeneratorFactory(AssetProvider assetProvider) {
        this.assetProvider = assetProvider;
        initializeGenerators();
    }

    private void initializeGenerators() {
		INSTITUTE_WALLET_INVOICE_GENERATOR.put(10390, new WalletInvoiceGenerator10390(assetProvider));
	}

	public WalletInvoiceGenerator getWalletInvoiceGenerator(int instituteId) {
		if (!INSTITUTE_WALLET_INVOICE_GENERATOR.containsKey(instituteId)) {
			return new GlobalWalletInvoiceGenerator(assetProvider);
		}
		return INSTITUTE_WALLET_INVOICE_GENERATOR.get(instituteId);
	}
}
