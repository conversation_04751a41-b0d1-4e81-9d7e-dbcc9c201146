package com.lernen.cloud.pdf.timetable;

import java.util.HashMap;
import java.util.Map;

import com.embrate.cloud.core.lib.utility.AssetProvider;

/**
 * 
 * <AUTHOR>
 *
 */
public class TimetableGeneratorFactory {

	private static final Map<Integer, TimetableGenerator> INSTITUTE_TIMETABLE_GENERATOR = new HashMap<>();
	
	private final AssetProvider assetProvider;

	public  TimetableGeneratorFactory(AssetProvider assetProvider)
	{
		this.assetProvider = assetProvider;
	}
	static {
	}
	
	public TimetableGenerator getTimetableGenerator(int instituteId) {
		if (!INSTITUTE_TIMETABLE_GENERATOR.containsKey(instituteId)) {
			return new GlobalTimetableGenerator(assetProvider);
		}
		return INSTITUTE_TIMETABLE_GENERATOR.get(instituteId);
	}
}
