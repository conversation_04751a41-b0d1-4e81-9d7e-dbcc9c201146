package com.lernen.cloud.pdf.timetable;

import com.embrate.cloud.core.api.timetable.TimetableDetails;
import com.embrate.cloud.core.lib.timetable.TimetableManager;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.configurations.ExaminationPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.pdf.certificates.transfer.TransferCertificateHandler;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.UUID;

public class TimetableHandler {

    private static final Logger logger = LogManager.getLogger(TransferCertificateHandler.class);

    private final TimetableGeneratorFactory timetableGeneratorFactory;
    private final InstituteManager instituteManager;
    private final TimetableManager timetableManager;

    public TimetableHandler(InstituteManager instituteManager, TimetableManager timetableManager, AssetProvider assetProvider) {
        this.timetableGeneratorFactory = new TimetableGeneratorFactory(assetProvider);
        this.instituteManager = instituteManager;
        this.timetableManager = timetableManager;
    }

    public DocumentOutput generateTimetablePdf(int instituteId, int academicSessionId, UUID timetableId) {

        if (instituteId <= 0 || timetableId == null) {
            logger.error("Invalid institute id or timetable id");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid institute id or timetable id"));
        }

        final Institute institute = instituteManager.getInstitute(instituteId);
        if (institute == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to get institute"));
        }

        final TimetableDetails timetableDetails = timetableManager
                .getTimetableDetailsByTimetableId(instituteId, academicSessionId, timetableId);

        if(timetableDetails == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to get institute"));
        }

        TimetableGenerator timetableGenerator = timetableGeneratorFactory.getTimetableGenerator(instituteId);
        String fileName = timetableDetails.getStandard().getDisplayNameWithSection() + "_Timetable.pdf";

        return timetableGenerator.generateTimetable(institute, timetableDetails, fileName);
    }

}
