package com.lernen.cloud.pdf.visitor.identitycard;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.configurations.DocumentPropertiesPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.visitor.VisitorDetails;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.visitor.VisitorDetailsManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.UUID;

public class VisitorIdentityCardHandler {
	private static final Logger logger = LogManager.getLogger(VisitorIdentityCardHandler.class);

	private final VisitorIdentityCardGeneratorFactory visitorIdentityCardGeneratorFactory;
	private final VisitorDetailsManager visitorDetailsManager;
	private final InstituteManager instituteManager;
	private final UserPermissionManager userPermissionManager;
	private final UserPreferenceSettings userPreferenceSettings;

	public VisitorIdentityCardHandler(VisitorDetailsManager visitorDetailsManager, InstituteManager instituteManager, UserPermissionManager userPermissionManager, AssetProvider assetProvider, UserPreferenceSettings userPreferenceSettings) {
		this.visitorIdentityCardGeneratorFactory = new VisitorIdentityCardGeneratorFactory(assetProvider);
		this.visitorDetailsManager = visitorDetailsManager;
		this.instituteManager = instituteManager;
		this.userPermissionManager = userPermissionManager;
		this.userPreferenceSettings = userPreferenceSettings;
	}

	public DocumentOutput generateIdentityCard(int instituteId, int academicSessionId, UUID visitorId, UUID userId) {
		if (instituteId <= 0 || academicSessionId <= 0 || visitorId == null || userId == null) {
			logger.error("Invalid institute id or academicSessionId or visitorId");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS,
					"Invalid institute id or academicSessionId or visitorId"));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.GENERATE_VISITOR_IDENTITY_CARD);

		VisitorDetails visitorDetails = visitorDetailsManager.getVisitorDetailsByVisitorId(instituteId, academicSessionId,
				visitorId);

		if (visitorDetails == null) {
			logger.info("No visitor found in instituteId {}, session {}, visitorId {}. Skipping identity card generation.",
					instituteId, academicSessionId, visitorId);
			return null;
		}
		DocumentPropertiesPreferences documentPropertiesPreferences = userPreferenceSettings.getDocumentPropertiesPreferences(instituteId);
		VisitorIdentityCardGenerator identityCardGenerator = visitorIdentityCardGeneratorFactory.getIdentityCardGenerator(instituteId);

		String fileName = visitorDetails.getVisitorName() + " - Visiting Card" + ".pdf";
		return identityCardGenerator.generateVisitorIdentityCard(visitorDetailsManager, instituteManager.getInstitute(instituteId), visitorDetails, fileName, documentPropertiesPreferences);
	}
}
