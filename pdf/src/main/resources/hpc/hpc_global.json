[{"key": "aam", "name": "All About Me", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITHOUT_BORDER", "child": [{"key": "name", "name": "My Name is", "description": null, "input_data_type": "TEXT", "input_data_count": "SINGLE", "report_card_display_tag": "TEXT", "child": null}, {"key": "likes", "name": "Things I Like", "description": null, "input_data_type": "TEXT", "input_data_count": "SINGLE", "report_card_display_tag": "TEXT", "child": null}, {"key": "address", "name": "I Live In", "description": null, "input_data_type": "TEXT", "input_data_count": "SINGLE", "report_card_display_tag": "TEXT", "child": null}, {"key": "birthday", "name": "My birthday", "description": null, "input_data_type": "TEXT", "input_data_count": "SINGLE", "report_card_display_tag": "TEXT", "child": null}, {"key": "friends", "name": "My friends are", "description": null, "input_data_type": "TEXT", "input_data_count": "SINGLE", "report_card_display_tag": "TEXT", "child": null}, {"key": "favourites", "name": "My favourites:", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITHOUT_BORDER", "child": [{"key": "colours", "name": "Colours", "description": null, "input_data_type": "TEXT", "input_data_count": "SINGLE", "report_card_display_tag": "TEXT", "child": null}, {"key": "foods", "name": "Foods", "description": null, "input_data_type": "TEXT", "input_data_count": "SINGLE", "report_card_display_tag": "TEXT", "child": null}, {"key": "games", "name": "Games", "description": null, "input_data_type": "TEXT", "input_data_count": "SINGLE", "report_card_display_tag": "TEXT", "child": null}, {"key": "animals", "name": "Animals", "description": null, "input_data_type": "TEXT", "input_data_count": "SINGLE", "report_card_display_tag": "TEXT", "child": null}, {"key": "height", "name": "My height is", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TEXT", "child": null}, {"key": "weight", "name": "My weight is", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TEXT", "child": null}]}]}, {"key": "agom", "name": "A glimpse of myself", "description": null, "input_data_type": "IMAGE", "input_data_count": "SINGLE", "report_card_display_tag": "IMAGE", "child": null}, {"key": "agomf", "name": "A glimpse of my family", "description": null, "input_data_type": "IMAGE", "input_data_count": "SINGLE", "report_card_display_tag": "IMAGE", "child": null}, {"key": "competencies", "name": "Competencies", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "pd", "name": "Physical Development", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "alignment": "VERTICAL", "child": [{"key": "pd_cg1", "name": "Curriculum Goal 1 Children develop habits that keep them healthy & safe", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "pd_cg1_c_1_1", "name": "C-1.1: Shows a liking for and understanding of nutri- tious food and does not waste food", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "pd_cg1_c_1_2", "name": "C-1.2: Practices basic self-care and hygiene", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "pd_cg1_c_1_6", "name": "C-1.6: Understands unsafe situations and asks for help", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}, {"key": "pd_cg2", "name": "Curriculum Goal 2 - Children develop sharpness in sensorial perceptions", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "pd_cg2_c_2_1", "name": "C-2.1: Differentiates between shapes, colours, and their shades", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "pd_cg2_c_2_5", "name": "C-2.5: <PERSON><PERSON><PERSON> discrimination in the sense of touch", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "pd_cg2_c_2_6", "name": "C-2.6: <PERSON><PERSON> integrating sensorial perceptions to get a holistic awareness of experiences", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}, {"key": "pd_cg3", "name": "Curriculum Goal 3 - Children develop a fit and flexible body", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "pd_cg3_c_3_2", "name": "C-3.2: Shows balance, coordination and flexibility in various physical activities", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "pd_cg3_c_3_3", "name": "C-3.3: Shows precision and control in working with their hands and fingers", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "pd_cg3_c_3_4", "name": "C-3.4: Shows strength and endurance in carrying, walking and running", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}]}, {"key": "seed", "name": "Socio-emotional & ethical development", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "alignment": "VERTICAL", "child": [{"key": "seed_cg4", "name": "Curriculum Goal 4 - Children develop emotional intelligence", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "seed_cg4_c_4_1", "name": "C-4.1: Starts recognising 'self' as an individual belong to a family and community", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "seed_cg4_c_4_2", "name": "C-4.2: Recognises different emotions and makes deli- berate effort to regulate them appropriately", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "seed_cg4_c_4_3", "name": "C-4.3: Interacts comfortably with other children and adults", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "seed_cg4_c_4_6", "name": "C-4.6: Shows kindness and helpfulness to others (inclu- ding animals, plants) when they are in need", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}, {"key": "seed_cg5", "name": "Curriculum Goal 5 - Children develop a positive attitude towards produc- tive work and service or 'Seva'", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "seed_cg5_c_5_1", "name": "C-5.1: Demonstrates willingness and participation in age -appropriate physical work towards helping others", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}, {"key": "seed_cg6", "name": "Curriculum Goal 6 - Children develop a positive regard for the natural environment around them", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "seed_cg6_c_6_1", "name": "C-6.1: Shows care for and joy in engaging with all life forms", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}]}, {"key": "cd", "name": "Cognitive development", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "alignment": "VERTICAL", "child": [{"key": "cd_cg7", "name": "Curriculum Goal 7 - Children make sense of world around through observation and logical thinking", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "cd_cg7_c_7_1", "name": "C-7.1: Observes and understands different categories of objects and relationships between them", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "cd_cg7_c_7_2", "name": "C-7.2:Observes and understands cause and effect relationships in nature by forming simple hypo- thesis and uses observations to explain their hypothesis", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}, {"key": "cd_cg8", "name": "Curriculum Goal 8 - Children develop mathematical understanding and abilities to recognise the world through quantities, shapes, and measures", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "cd_cg8_c_8_1", "name": "C-8.1: Sorts objects into groups and sub-group based on more than one property", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "cd_cg8_c_8_2", "name": "C-8.2: Identifies and extends simple patterns in their surroundings, shapes, and numbers", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "cd_cg8_c_8_3", "name": "C-8.3: Counts up to 99 both forwards and backwards and in groups of 10s and 20s", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "cd_cg8_c_8_5", "name": "C-8.5: Recognises and uses numerals to represent quantities up to 99 with the understanding of decimal place value system", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "cd_cg8_c_8_6", "name": "C-8.6: Performs addition and subtraction of 2-digit numbers fluently using flexible strategies of composition and decomposition", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "cd_cg8_c_8_8", "name": "C-8.8: Recognises, makes, and classifies basic geome- tric shapes and their observable properties, and understands and explains the relative relation of objects in space", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "cd_cg8_c_8_13", "name": "C-8.13:Formulates and solves simple mathematical pro- blems related to quantities, shapes, space, and measurements", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}]}, {"key": "lld", "name": "Language and literacy development", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "alignment": "VERTICAL", "child": [{"key": "lld_cg9", "name": "Curriculum Goal 9 - Children develop effective communication skills for day-today interactions in two languages", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "lld_cg9_c_9_1", "name": "C-9.1: Listens to and appreciates simple songs, rhymes, and poems", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "lld_cg9_c_9_3", "name": "C-9.3: Converses fluently and can hold a meaningful conversation", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "lld_cg9_c_9_4", "name": "C-9.4: Understands oral instructions for a complex task and gives clear oral instructions for the same to others", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "lld_cg9_c_9_5", "name": "C-9.5: Comprehends narrated/read-out stories and identifies characters, storyline and what the author wants to say", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}, {"key": "lld_cg10", "name": "Curriculum Goal 10 Children develop fluency in reading and writing in Language 1", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "lld_cg10_c_10_3", "name": "C-10.3: Recognises all the letters of the alphabet (forms of akshara) of the script (L1) and uses this knowledge to read and write words", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "lld_cg10_c_10_4", "name": "C-10.4:Reads stories and passages (in L1) with accura- cy and fluency with appropriate pauses and voice modulation", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "lld_cg10_c_10_5", "name": "C-10.5:Reads short stories and comprehends its mea- ning by identifying characters, storyline and what the author wanted to say- on their own (L1)", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "lld_cg10_c_10_6", "name": "C-10.6: Reads short poems and begins to appreciate the poem for its choice of words and imagination", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "lld_cg10_c_10_7", "name": "C-10.7: Reads and comprehends meaning of short news items, instructions and recipes, and publicity material", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}, {"key": "lld_cg11", "name": "Curriculum Goal 11 Children begin to read and write in Language 2", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "lld_cg11_c_11_2", "name": "C-11.2: Recognises most frequently occurring letters of the alphabet (forms of akshara) of the script, and uses this knowledge to read and write simple words and sentences", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}]}, {"key": "acd", "name": "Aesthetic & cultural development", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "alignment": "VERTICAL", "child": [{"key": "acd_cg12", "name": "Curriculum Goal 12 - Children develop abilities and sensibilities in visual and performing arts, and express their emotions through art in meaning- ful and joyful ways", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "acd_cg12_c_12_1", "name": "C-12.1: Explores and plays with a variety of materials and tools to create two-dimensional and three- dimensional artworks in varying sizes", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "acd_cg12_c_12_2", "name": "C-12.2: Explores and plays with own voice, body, spaces, and a variety of objects to create music, role-play, dance, and movement", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "acd_cg12_c_12_3", "name": "C-12.3: Innovates and works imaginatively to express ideas and emotions through the arts", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}]}, {"key": "plh", "name": "Positive learning habits", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "alignment": "VERTICAL", "child": [{"key": "plh_cg13", "name": "Curriculum Goal 13- Children develop habits of learning that allow them to engage actively in formal learning environments like a school classroom", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "plh_cg13_c_13_1", "name": "C-13.1: Attention and intentional action: Acquires skills to plan, focus attention, and direct activities to achieve specific goals", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "plh_cg13_c_13_3", "name": "C-13.3: Observation, wonder, curiosity, and exploration: Observes minute details of objects, wonders and explores using various senses, tinkers with objects, asks questions", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}]}]}, {"key": "lpbtt", "name": "<PERSON><PERSON>'s profile by the teacher", "description": "Teacher must present a narrative summary of the child, highlighting the strengths, challenges and suggestions for improvement.", "input_data_type": "IMAGE", "input_data_count": "SINGLE", "report_card_display_tag": "IMAGE", "child": null}, {"key": "pf", "name": "<PERSON><PERSON><PERSON>s <PERSON><PERSON><PERSON>", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "pf_participating", "name": "My child enjoy participating in", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "pf_supported", "name": "My child can be supported for", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "pf_share", "name": "I would also like to share", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "pf_vaccination", "name": "Have i completed age appropriate vaccination schedule for my child?", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}, {"key": "sa", "name": "Self Assessment", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "sa_enjoy", "name": "Activities that i enjoy the most", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "sa_difficult", "name": "Activities that i find difficult to do", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "sa_activities_friends", "name": "Activities that i enjoy doing with my friends", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}, {"key": "pa", "name": "Peer Assessment", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "pa_helps", "name": "Helps in completing tasks/activity", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "pa_likes", "name": "Likes to play with others", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}, {"key": "pa_shares", "name": "Shares stationary (crayons/glue/chalk) with classmates", "description": null, "input_data_type": "TEXT", "input_data_count": "MULTIPLE", "report_card_display_tag": "TABLE_WITH_BORDER", "child": null}]}, {"key": "lp", "name": "Learner’s portfolio", "description": "Paste pictures/ display selected work done by student in various experiential and inter-disciplinary tasks done in class.", "input_data_type": "IMAGE", "input_data_count": "SINGLE", "report_card_display_tag": "IMAGE", "child": null}, {"key": "swd", "name": "Signature With Date", "description": null, "input_data_type": null, "input_data_count": null, "report_card_display_tag": "TABLE_WITH_BORDER", "child": [{"key": "swd_parent", "name": "Parent/Guardian", "description": null, "input_data_type": "IMAGE", "input_data_count": "MULTIPLE", "report_card_display_tag": "IMAGE", "child": null}, {"key": "swd_class_teacher", "name": "Class Teacher", "description": null, "input_data_type": "IMAGE", "input_data_count": "MULTIPLE", "report_card_display_tag": "IMAGE", "child": null}, {"key": "swd_principal", "name": "Principal", "description": null, "input_data_type": "IMAGE", "input_data_count": "MULTIPLE", "report_card_display_tag": "IMAGE", "child": null}]}]