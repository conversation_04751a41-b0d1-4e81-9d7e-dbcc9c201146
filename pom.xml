<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.embrate.cloud</groupId>
    <artifactId>embrate-backend</artifactId>
    <version>1.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>embrate-backend</name>
    <url>https://www.embrate.com</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
<!--        <spring.version>5.3.9</spring.version>-->
        <spring.version>3.1.4.RELEASE</spring.version>
        <itext.version>4.0.0</itext.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>3.8.1</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.sun.jersey.contribs</groupId>
                <artifactId>jersey-spring</artifactId>
                <version>1.19.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-asm</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-expression</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <!-- To be upgraded -->
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <!-- To be upgraded -->
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <!-- To be upgraded -->
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <!-- To be upgraded -->
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.springframework/spring-aop -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aop</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.8.1</version>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.11</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/joda-time/joda-time -->
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.10</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/postgresql/postgresql -->
            <!-- <dependency> <groupId>postgresql</groupId> <artifactId>postgresql</artifactId>
                <version>9.1-901-1.jdbc4</version> </dependency> -->

            <!-- https://mvnrepository.com/artifact/com.itextpdf/itext7-core -->
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext7-core</artifactId>
                <version>7.2.0</version>
                <type>pom</type>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.itextpdf/html2pdf -->
            <!-- 	<dependency>
                    <groupId>com.itextpdf</groupId>
                    <artifactId>html2pdf</artifactId>
                    <version>2.1.1</version>
                </dependency> -->
            <!-- https://mvnrepository.com/artifact/com.itextpdf.licensing/licensing-base -->
            <dependency>
                <groupId>com.itextpdf.licensing</groupId>
                <artifactId>licensing-base</artifactId>
                <version>4.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>typography</artifactId>
                <version>3.0.0</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.itextpdf/kernel -->
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>kernel</artifactId>
                <version>7.2.0</version>
            </dependency>


            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>2.11.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>2.11.2</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-annotations -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>2.9.8</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-ses</artifactId>
                <version>1.11.703</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-lambda-java-core</artifactId>
                <version>1.2.2</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-lambda</artifactId>
                <version>1.12.420</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-core</artifactId>
                <version>1.12.420</version>
            </dependency>


            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.8</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>1.7</version>
            </dependency>

            <dependency>
                <groupId>com.google.firebase</groupId>
                <artifactId>firebase-admin</artifactId>
                <version>9.3.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.sun.jersey.contribs/jersey-multipart -->
            <dependency>
                <groupId>com.sun.jersey.contribs</groupId>
                <artifactId>jersey-multipart</artifactId>
                <version>1.19.4</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.amazonaws/aws-java-sdk-dynamodb -->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-dynamodb</artifactId>
                <version>1.12.25</version>
            </dependency>


            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.8.5</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>1.11.226</version>
            </dependency>

            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-client</artifactId>
                <version>1.19.4</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/commons-cli/commons-cli -->
            <dependency>
                <groupId>commons-cli</groupId>
                <artifactId>commons-cli</artifactId>
                <version>1.4</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>27.1-jre</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.sun.jersey.contribs/jersey-apache-client4 -->
            <dependency>
                <groupId>com.sun.jersey.contribs</groupId>
                <artifactId>jersey-apache-client4</artifactId>
                <version>1.19.4</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.8</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpcore -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>4.4.11</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpmime -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>4.5.8</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.googlecode.libphonenumber/libphonenumber -->
            <dependency>
                <groupId>com.googlecode.libphonenumber</groupId>
                <artifactId>libphonenumber</artifactId>
                <version>8.10.9</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.9.6</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.jaxrs/jackson-jaxrs-json-provider -->
            <dependency>
                <groupId>com.fasterxml.jackson.jaxrs</groupId>
                <artifactId>jackson-jaxrs-json-provider</artifactId>
                <version>2.9.8</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.5</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-simpledb</artifactId>
                <version>1.12.20</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>3.17</version>
            </dependency>

            <!-- Used to work with the newer excel file format - `.xlsx` -->
            <!-- https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>3.17</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>2.9.6</version>
            </dependency>

            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-bundle</artifactId>
                <version>1.19.4</version>
            </dependency>

            <!-- Removed this as https://stackoverflow.com/questions/16876482/jersey-jackson-and-codehaus-vs-fasterxml
                https://mvnrepository.com/artifact/com.sun.jersey/jersey-json -->
            <!-- <dependency> <groupId>com.sun.jersey</groupId> <artifactId>jersey-json</artifactId>
                <version>1.19.4</version> </dependency> -->


            <!-- https://mvnrepository.com/artifact/com.sun.jersey/jersey-servlet -->
            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-servlet</artifactId>
                <version>1.19.4</version>
            </dependency>


            <!-- https://mvnrepository.com/artifact/javax.servlet/javax.servlet-api -->
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>4.0.1</version>
            </dependency>

            <dependency>
                <groupId>com.sparkpost</groupId>
                <artifactId>sparkpost-lib</artifactId>
                <version>0.21</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.sendgrid/sendgrid-java -->
            <dependency>
                <groupId>com.sendgrid</groupId>
                <artifactId>sendgrid-java</artifactId>
                <version>4.2.1</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.springframework.security.oauth/spring-security-oauth2 -->
            <dependency>
                <groupId>org.springframework.security.oauth</groupId>
                <artifactId>spring-security-oauth2</artifactId>
                <version>1.0.5.RELEASE</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.springframework.security/spring-security-core -->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.springframework.security/spring-security-web -->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-web</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.springframework.security/spring-security-config -->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-config</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.sun.jersey/jersey-server -->
            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-server</artifactId>
                <version>1.19.4</version>
            </dependency>

            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.4</version>
            </dependency>

            <dependency>
                <groupId>com.google.apis</groupId>
                <artifactId>google-api-services-drive</artifactId>
                <version>v3-rev20241027-2.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.google.http-client</groupId>
                <artifactId>google-http-client-gson</artifactId>
                <version>1.43.3</version>
            </dependency>

            <dependency>
                <groupId>com.razorpay</groupId>
                <artifactId>razorpay-java</artifactId>
                <version>1.4.6</version>
            </dependency>

            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>2.7.9</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/io.prometheus/simpleclient -->
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient</artifactId>
                <version>0.16.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/io.prometheus/simpleclient_servlet -->
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_servlet</artifactId>
                <version>0.16.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.reflections/reflections -->
            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>0.10.2</version>
            </dependency>
        </dependencies>



    </dependencyManagement>

    <repositories>
        <repository>
            <id>central</id>
            <name>Maven Repository Switchboard</name>
            <layout>default</layout>
            <url>https://repo1.maven.org/maven2</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>itext</id>
            <name>iText Repository - releases</name>
            <url>https://repo.itextsupport.com/releases</url>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <modules>
        <module>data-server</module>
        <module>core-lib</module>
        <module>core-api</module>
        <module>dao-tier</module>
        <module>core-utils</module>
        <module>emailer</module>
        <module>dev-tools</module>
        <module>velocity-resources</module>
        <module>sms</module>
        <module>whatsapp</module>
        <module>pdf</module>
        <module>push-notifications</module>
        <module>voicecall</module>
        <module>lambda-utils</module>
    </modules>
</project>