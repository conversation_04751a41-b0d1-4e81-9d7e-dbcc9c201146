package com.embrate.cloud.push.notifications;

import com.embrate.cloud.core.api.fee.configuration.FeeAssignmentAmountsPayload;
import com.embrate.cloud.push.notifications.handler.StudentAddComplainPushNotificationHandler;
import com.lernen.cloud.core.api.complainbox.ComplainStatus;
import com.lernen.cloud.core.api.complainbox.StudentComplaintMetadataPayload;
import com.lernen.cloud.core.api.fees.FeeEntity;
import com.lernen.cloud.core.api.fees.FeeHeadAmount;
import com.lernen.cloud.core.api.fees.FeeIdFeeHead;
import com.lernen.cloud.core.lib.fees.configuration.FeeConfigurationManager;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Hello world!
 *
 */
public class App 
{
    public static void main( String[] args )
    {
        System.out.println( "Hello World!" );
        final ApplicationContext context = new ClassPathXmlApplicationContext("push-notifications.xml");

//        final StaffAttendancePushNotificationHandler staffAttendancePushNotificationHandler = context.getBean(StaffAttendancePushNotificationHandler.class);
        StudentComplaintMetadataPayload complaintMetadataPayload = new StudentComplaintMetadataPayload(101, UUID.fromString("cba4bfc1-8706-4f55-a693-023997e4f438"), "new complaint", "complaint is regarding the work of complaint box", "101", "Mukul", UUID.fromString("89d18fa9-3f29-4ba2-b521-b10315a285fe"), ComplainStatus.OPEN, 1, null, UUID.fromString("e5b75d5d-7683-41e3-b952-4dd2fc48c625"), null, null, null);
          final StudentAddComplainPushNotificationHandler studentAddComplainPushNotificationHandler = context.getBean(StudentAddComplainPushNotificationHandler.class);
//        studentAddComplainPushNotificationHandler.sendStudentAddComplainNotificationsAsync(101, UUID.fromString("cba4bfc1-8706-4f55-a693-023997e4f438"), complaintMetadataPayload.getTitle(), UUID.fromString("e5b75d5d-7683-41e3-b952-4dd2fc48c625"));
//        Integer attendanceDate = 1721457101;
//        List<StaffAttendanceInput> staffAttendanceInputList = new ArrayList<>();
//        staffAttendanceInputList.add(new StaffAttendanceInput(UUIDUtils.getUUID("140967ea-c2b2-464a-af50-c304b7a8418d"),
//                new Time(9, 30, 0)));
//        StaffAttendancePayloadV2 staffAttendancePayload = new StaffAttendancePayloadV2(attendanceDate, staffAttendanceInputList);
//        staffAttendancePushNotificationHandler.sendAttendanceUpdateNotificationsAsync(
//                110, AttendanceInputType.WEB, staffAttendancePayload);
//
//        homeworkBroadcastPushNotificationHandler.sendHomeworkBroadcastNotificationsAsync(10030,
//                new ArrayList<>(Arrays.asList(UUID.fromString("b3f5af2a-c4ca-4602-a784-d6f8d8dbf998"))),
//                UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));

        final FeeConfigurationManager  feeConfigurationManager = context.getBean(FeeConfigurationManager.class);
//        &user_id=08dc0556-b8a1-4acd-a6fa-759ca864df9e {'studentId': '0c07c4af-4c9e-4494-a345-cc6f2a228c2b', 'feeIdFeeHeadList': [{'feeId': '27d3c75b-55d9-484e-9eb7-f4f8d58be25a', 'feeHeadAmountList': [{'feeHeadId': '525', 'amount': 200}]}, {'feeId': '1efed782-a16b-4fb8-b4db-ef70fed8c089', 'feeHeadAmountList': [{'feeHeadId': '525', 'amount': 2499}, {'feeHeadId': '526', 'amount': 2000}]}, {'feeId': 'e870781e-5a34-4759-b096-d61f6d9b2fb6', 'feeHeadAmountList': [{'feeHeadId': '525', 'amount': 1200}, {'feeHeadId': '526', 'amount': 1000}]}]}
//        FeeHeadAmount feeHeadAmount = new FeeHeadAmount(525, 200d);
//        FeeIdFeeHead
//        FeeAssignmentAmountsPayload feeAssignmentAmountsPayload = new FeeAssignmentAmountsPayload();
        List<FeeHeadAmount> feeHeadAmountList1 = new ArrayList<>();
        feeHeadAmountList1.add(new FeeHeadAmount(525, 200.0, FeeEntity.STUDENT));

        List<FeeHeadAmount> feeHeadAmountList2 = new ArrayList<>();
        feeHeadAmountList2.add(new FeeHeadAmount(525, 2499.0, FeeEntity.STUDENT));
        feeHeadAmountList2.add(new FeeHeadAmount(526, 2000.0, FeeEntity.STUDENT));

        List<FeeHeadAmount> feeHeadAmountList3 = new ArrayList<>();
        feeHeadAmountList3.add(new FeeHeadAmount(525, 1300.0, FeeEntity.STUDENT));
        feeHeadAmountList3.add(new FeeHeadAmount(526, 1200.0, FeeEntity.STUDENT));

        // Create FeeIdFeeHead objects
        List<FeeIdFeeHead> feeIdFeeHeadList = new ArrayList<>();
        feeIdFeeHeadList.add(new FeeIdFeeHead(UUID.fromString("27d3c75b-55d9-484e-9eb7-f4f8d58be25a"), feeHeadAmountList1));
        feeIdFeeHeadList.add(new FeeIdFeeHead(UUID.fromString("1efed782-a16b-4fb8-b4db-ef70fed8c089"), feeHeadAmountList2));
        feeIdFeeHeadList.add(new FeeIdFeeHead(UUID.fromString("e870781e-5a34-4759-b096-d61f6d9b2fb6"), feeHeadAmountList3));

        // Create the payload
        FeeAssignmentAmountsPayload payload = new FeeAssignmentAmountsPayload();
        payload.setStudentId(UUID.fromString("0c07c4af-4c9e-4494-a345-cc6f2a228c2b"));
        payload.setFeeIdFeeHeadList(feeIdFeeHeadList);
        final boolean updatedAssignedFees = feeConfigurationManager.updateAssignedFeeAmounts(101, 190, payload, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"));
System.out.println(updatedAssignedFees);
//        final FeePaymentReminderPushNotificationHandler feePaymentReminderPushNotificationHandler
//                = context.getBean(FeePaymentReminderPushNotificationHandler.class);
//        int instituteId = 10030;
//        int academicSessionId = 32;
//        DeliveryMode deliveryMode = DeliveryMode.APP;
//        int dueDate = DateUtils.now();
//        boolean computeFine = true;
//        String batchName = "";
//        String requiredStanardsCSV = null;
//        List<UUID> studentIds = new ArrayList<>();
//        studentIds.add(UUID.fromString("d0e0ac6b-1cc6-463c-ba61-c8d152e212a0"));
//        UUID audioTemplateId = null;
//        FeePaymentReminderPayload feePaymentReminderPayload = new FeePaymentReminderPayload(instituteId,
//                academicSessionId, deliveryMode, dueDate, computeFine, batchName, requiredStanardsCSV, studentIds, audioTemplateId);
//        feePaymentReminderPushNotificationHandler.sendFeePaymentReminderNotificationsAsync(10030,
//                feePaymentReminderPayload, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));
    }
}
