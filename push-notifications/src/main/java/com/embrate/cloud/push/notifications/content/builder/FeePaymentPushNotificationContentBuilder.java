/**
 * 
 */
package com.embrate.cloud.push.notifications.content.builder;

import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.lernen.cloud.core.api.user.UserType;

/**
 * <AUTHOR>
 *
 */
public class FeePaymentPushNotificationContentBuilder {
	
	public PushNotificationContent getPushNotificationContent(String invoiceId, String studentName, String standardName, double amount, UserType userType) {
		switch(userType) {
			case STUDENT:
				return getStudentPushNotificationContent(invoiceId);
			case ADMIN:
				return getAdminPushNotificationContent(invoiceId, studentName, standardName, amount);
			default:
				return null;
		}
	}

	private PushNotificationContent getAdminPushNotificationContent(String invoiceId, String studentName, String standardName, double amount) {
		return new PushNotificationContent("Invoice : " + invoiceId,
				"An amount of INR " + amount + " has been collected for " + studentName + " of class " + standardName + ".", null);
	}

	private PushNotificationContent getStudentPushNotificationContent(String invoiceId) {
		return new PushNotificationContent("Fee Payment Received.",
				"Your fees payment is successful. Invoice : " + invoiceId + ".", null);
	}
}