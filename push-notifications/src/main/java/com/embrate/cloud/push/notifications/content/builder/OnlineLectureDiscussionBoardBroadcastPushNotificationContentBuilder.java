/**
 * 
 */
package com.embrate.cloud.push.notifications.content.builder;

import org.apache.commons.lang3.StringUtils;

import com.embrate.cloud.core.api.service.notification.PushNotificationContent;

/**
 * <AUTHOR>
 *
 */
public class OnlineLectureDiscussionBoardBroadcastPushNotificationContentBuilder {
	
	public PushNotificationContent getThreadPushNotificationContent(String discussionTitle, 
			String lectureTitle, String createdUserName) {
		if(StringUtils.isBlank(createdUserName)) {
			return new PushNotificationContent("New Message in Disscusion Board",
					"New Discussion '" +  discussionTitle + "' on lecture '" 
					+ lectureTitle + "'", null);
		} else {
			return new PushNotificationContent("New Message in Disscusion Board",
					"New Discussion '" +  discussionTitle + "' on lecture '" 
					+ lectureTitle + "' has been started by " + createdUserName, null);
		}
	}
	
	public PushNotificationContent getConversationPushNotificationContent(String discussionTitle,
			String lectureTitle,  String createdUserName) {
		if(StringUtils.isBlank(createdUserName)) {
			return new PushNotificationContent("New Message in Disscusion Board", 
					"New Message in discussion '" +  discussionTitle + "' of lecture '"
					+ lectureTitle + "'", null);
		} else {
			return new PushNotificationContent("New Message in Disscusion Board", 
					"New Message in discussion '" +  discussionTitle + "' of lecture '"
					+ lectureTitle + "' by " + createdUserName, null);
		}
	}
	
}
