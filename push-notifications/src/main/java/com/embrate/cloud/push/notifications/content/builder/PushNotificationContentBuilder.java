package com.embrate.cloud.push.notifications.content.builder;

import com.embrate.cloud.core.api.service.communication.templates.*;
import com.embrate.cloud.core.lib.templates.notification.NotificationTemplateManager;
import com.embrate.cloud.push.notifications.handler.AbstractPushNotificationHandler;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.springframework.util.CollectionUtils;

import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @created_at 25/08/23 : 17:00
 **/
public class PushNotificationContentBuilder {

    private static final Logger logger = LogManager.getLogger(PushNotificationContentBuilder.class);
    private final NotificationTemplateManager notificationTemplateManager;

    public PushNotificationContentBuilder(NotificationTemplateManager notificationTemplateManager) {
        this.notificationTemplateManager = notificationTemplateManager;
    }

    public CommunicationTemplate getCommunicationTemplate(UUID templateId){
        if(templateId == null){
            logger.error("Invalid template id");
            return null;
        }
        CommunicationTemplate communicationTemplate = notificationTemplateManager.getCommunicationTemplate(templateId);
        if(communicationTemplate == null || communicationTemplate.getTemplateStatus() != TemplateStatus.APPROVED){
            logger.error("Invalid template configured for templateId {}", templateId);
            return null;
        }
        return communicationTemplate;
    }
    public String generateTextContent(UUID templateId, AbstractPushNotificationHandler.PushNotificationUserMetadata pushNotificationUserMetadata) {
        CommunicationTemplate communicationTemplate = getCommunicationTemplate(templateId);
        if(communicationTemplate != null){
            String notificationText = communicationTemplate.getTemplateValue();
            if(communicationTemplate.getTemplateVariableDetails() == null ||
                    CollectionUtils.isEmpty(communicationTemplate.getTemplateVariableDetails().getTemplateVariableList())) {
                return notificationText;
            }
            Map<TemplateVariableType, String> systemVarMap = getSystemVariables(communicationTemplate.getTemplateVariableDetails(), pushNotificationUserMetadata);
            return renderTemplate(notificationText, communicationTemplate.getTemplateVariableDetails(), systemVarMap, null);
        }
        return null;
    }

    public Map<TemplateVariableType, String> getSystemVariables(TemplateVariableDetails templateVariableDetails,
                    AbstractPushNotificationHandler.PushNotificationUserMetadata pushNotificationUserMetadata){
        Map<TemplateVariableType, String> systemVars = new HashMap<>();
        if(templateVariableDetails == null || org.apache.commons.collections4.CollectionUtils.isEmpty(templateVariableDetails.getTemplateVariableList())){
            logger.warn("No variable found {}", templateVariableDetails);
            return systemVars;
        }

        for(TemplateVariable templateVariable : templateVariableDetails.getTemplateVariableList()){
            if(templateVariable == null || templateVariable.getTemplateVariableType() == null){
                continue;
            }
            if(systemVars.containsKey(templateVariable.getTemplateVariableType())){
                continue;
            }
            switch (templateVariable.getTemplateVariableType()){
                case STUDENT_NAME:
                    systemVars.put(templateVariable.getTemplateVariableType(), pushNotificationUserMetadata.getStudentUserData().getStudentName());
                    break;
                case STANDARD_NAME:
                    systemVars.put(templateVariable.getTemplateVariableType(), pushNotificationUserMetadata.getStudentUserData().getStandardName());
                    break;
                case CURRENT_DATE:
                    systemVars.put(templateVariable.getTemplateVariableType(), DateUtils.getFormattedDate(DateUtils.now()));
                    break;
                case CURRENT_DATE_WITH_TIME:
                    systemVars.put(templateVariable.getTemplateVariableType(), DateUtils.getFormattedDate(DateUtils.now(),
                            DateUtils.DEFAULT_DATE_TIME_FORMAT, DateUtils.DEFAULT_TIMEZONE));
                    break;
                case ADMISSION_NUMBER:
                    systemVars.put(templateVariable.getTemplateVariableType(), pushNotificationUserMetadata.getStudentUserData().getAdmissionNumber());
                    break;
                case DUE_FEE_AMOUNT:
                    systemVars.put(templateVariable.getTemplateVariableType(), String.valueOf(pushNotificationUserMetadata.getStudentUserData().getDueAmount()));
                    break;
                default:
                    break;
            }
        }
        return systemVars;
    }

    public String renderTemplate(String template, TemplateVariableDetails templateVariableDetails, Map<TemplateVariableType, String> systemVarValues, Map<String, String> customVarValues){
        if(StringUtils.isBlank(template) || templateVariableDetails == null || org.apache.commons.collections4.CollectionUtils.isEmpty(templateVariableDetails.getTemplateVariableList())){
            logger.error("Invalid template or variable details {}, for template {}", templateVariableDetails, template);
            return null;
        }

        final VelocityContext context = new VelocityContext();
        for(TemplateVariable templateVariable : templateVariableDetails.getTemplateVariableList()){
            String varValue = null;
            if(templateVariable.getTemplateVariableType() == TemplateVariableType.CUSTOM){
                varValue = customVarValues == null ? null : customVarValues.get(templateVariable.getVarName());
            }else{
                varValue = systemVarValues == null ? null : systemVarValues.get(templateVariable.getTemplateVariableType());
            }
            context.put(templateVariable.getVarName(), varValue);
        }

        final StringWriter renderedContent = new StringWriter();
        Velocity.evaluate(context, renderedContent, template, template);
        return renderedContent.toString();
    }
}
