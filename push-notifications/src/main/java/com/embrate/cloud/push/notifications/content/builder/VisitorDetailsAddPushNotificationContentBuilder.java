package com.embrate.cloud.push.notifications.content.builder;

import com.embrate.cloud.core.api.service.notification.PushNotificationContent;

public class VisitorDetailsAddPushNotificationContentBuilder {
	public PushNotificationContent getPushNotificationContent() {
		return getAdminPushNotificationContent();
	}

	private PushNotificationContent getAdminPushNotificationContent() {
		return new PushNotificationContent("New Visitor.",
				"Visitor has requested an appointment.", null);
	}
}
