package com.embrate.cloud.push.notifications.content.builder;

import com.embrate.cloud.core.api.service.notification.PushNotificationContent;

public class VisitorStatusUpdatePushNotificationContentBuilder {

	public PushNotificationContent getPushNotificationContent() {
		return getAdminPushNotificationContent();
	}

	private PushNotificationContent getAdminPushNotificationContent() {
		return new PushNotificationContent("Visitor Status Updated.",
				"Visitor Appointment status has been updated.", null);
	}
}
