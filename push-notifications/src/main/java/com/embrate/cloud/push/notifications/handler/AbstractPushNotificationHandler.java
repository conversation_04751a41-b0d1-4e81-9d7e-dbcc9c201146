package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.service.notification.BulkPushNotificationPayload;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.api.service.notification.PushNotificationPayload;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserAppToken;
import com.lernen.cloud.core.api.user.UserType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

import static com.embrate.cloud.core.lib.push.notification.PushNotificationManager.FLUTTER_REQUIRED_CLICK_ACTION_VAL;

/**
 * <AUTHOR>
 */

public abstract class AbstractPushNotificationHandler {

    private static final Logger logger = LogManager.getLogger(AbstractPushNotificationHandler.class);
    private final PushNotificationManager pushNotificationManager;


    public AbstractPushNotificationHandler(PushNotificationManager pushNotificationManager) {
        this.pushNotificationManager = pushNotificationManager;
    }

    /**
     * converting institute id from int to Integer to handle multiple institute case.
     */
    public void prepareAndSendBulkNotificationAsync(Integer instituteId, List<User> finalUserList, PushNotificationContent pushNotificationContent, IUpdateNotificationPayload updateNotificationPayload) {
        List<PushNotificationPayload> pushNotificationPayloadList = new ArrayList<>();
        for (User user : finalUserList) {
            if (CollectionUtils.isEmpty(user.getUserAppTokens())) {
                continue;
            }

            Map<String, String> dataPayload = new HashMap<>();
            if(updateNotificationPayload != null){
                updateNotificationPayload.update(dataPayload, user);
            }

            dataPayload.put(PushNotificationManager.USER_ID_KEY, user.getUuid().toString());
            dataPayload.put(PushNotificationManager.FLUTTER_REQUIRED_CLICK_ACTION_KEY, FLUTTER_REQUIRED_CLICK_ACTION_VAL);

            for (UserAppToken userAppToken : user.getUserAppTokens()) {
                pushNotificationPayloadList.add(new PushNotificationPayload(pushNotificationContent.getTitle(),
                        pushNotificationContent.getBody(), pushNotificationContent.getImageURL(), userAppToken.getToken(), dataPayload));
            }
        }

        if (CollectionUtils.isEmpty(pushNotificationPayloadList)) {
            logger.error("No registered users found for notification for institute {} ", instituteId);
            return;
        }

        logger.info("Sending push notifications for registered users {} for institute {}",
                pushNotificationPayloadList.size(), instituteId);


        pushNotificationManager.sendBulkNotificationsAsync(instituteId,
                new BulkPushNotificationPayload(pushNotificationPayloadList));
    }

    public class PushNotificationUserMetadata {

        private final UUID userId;

        private final UserType userType;

        private final StudentUserData studentUserData;

        public PushNotificationUserMetadata(UUID userId, UserType userType, StudentUserData studentUserData) {
            this.userId = userId;
            this.userType = userType;
            this.studentUserData = studentUserData;
        }

        public UUID getUserId() {
            return userId;
        }

        public UserType getUserType() {
            return userType;
        }

        public StudentUserData getStudentUserData() {
            return studentUserData;
        }

    }

    public class StudentUserData {

        private final String admissionNumber;

        private final String studentName;

        private final String standardName;

        private final double dueAmount;

        public StudentUserData(String admissionNumber, String studentName, String standardName, double dueAmount) {
            this.admissionNumber = admissionNumber;
            this.studentName = studentName;
            this.standardName = standardName;
            this.dueAmount = dueAmount;
        }

        public String getAdmissionNumber() {
            return admissionNumber;
        }

        public String getStudentName() {
            return studentName;
        }

        public String getStandardName() {
            return standardName;
        }

        public double getDueAmount() {
            return dueAmount;
        }
    }
}

