package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.service.notification.BellNotificationPayload;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.push.notifications.content.builder.AppointmentDetailsAddPushNotificationContentBuilder;
import com.lernen.cloud.core.api.appointment.StudentAppointmentDetails;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.lib.appointment.AppointmentDetailsManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

public class AppointmentDetailsAddPushNotificationHandler extends  AbstractPushNotificationHandler {
    private static final Logger logger = LogManager.getLogger(AppointmentDetailsAddPushNotificationHandler.class);
    public static final String APPOINTMENT_ID = "appointmentId";
    public static final String ADMIN_ADD_APPOINTMENT = "adminAddAppointment";
    private final UserManager userManager;
    private final PushNotificationManager pushNotificationManager;
    private final AppointmentDetailsManager appointmentDetailsManager;
    private final UserPermissionManager userPermissionManager;
    private final AppointmentDetailsAddPushNotificationContentBuilder appointmentDetailsAddPushNotificationContentBuilder;

    public AppointmentDetailsAddPushNotificationHandler(UserManager userManager, PushNotificationManager pushNotificationManager, AppointmentDetailsManager appointmentDetailsManager, UserPermissionManager userPermissionManager, AppointmentDetailsAddPushNotificationContentBuilder appointmentDetailsAddPushNotificationContentBuilder) {
        super(pushNotificationManager);
        this.userManager = userManager;
        this.pushNotificationManager = pushNotificationManager;
        this.appointmentDetailsManager = appointmentDetailsManager;
        this.userPermissionManager = userPermissionManager;
        this.appointmentDetailsAddPushNotificationContentBuilder = appointmentDetailsAddPushNotificationContentBuilder;
    }

    public void sendAppointmentDetailsAddNotificationsAsync(int instituteId, UUID appointmentId, int academicSessionId, UUID userId) {
        Thread t = new Thread(new Runnable() {
            @Override
            public void run() {
                sendAppointmentNotifications(instituteId, appointmentId, academicSessionId, userId);
            }
        });
        t.start();
    }

    public void sendAppointmentNotifications(int instituteId, UUID appointmentId, int academicSessionId, UUID userId) {
        if (instituteId <= 0) {
            logger.error("Invalid institute {} ", instituteId);
            return;
        }

        final StudentAppointmentDetails studentAppointmentDetails = appointmentDetailsManager.getAppointmentDetailsByAppointmentId(instituteId, userId, academicSessionId, appointmentId);
        if (studentAppointmentDetails == null) {
            return;
        }
        BellNotificationPayload bellNotificationPayload = sendAdminAppointmentNotifications(instituteId, appointmentId, studentAppointmentDetails, userId);
        if (bellNotificationPayload == null) {
            return;
        }

        boolean notificationAdded = pushNotificationManager.addNotification(bellNotificationPayload);

        if (!notificationAdded) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
                            "Invalid bell notification details."));
        }
    }

    private BellNotificationPayload sendAdminAppointmentNotifications(int instituteId, UUID appointmentId, StudentAppointmentDetails studentAppointmentDetails, UUID userId) {
        User raisedFor = userManager.getUser(studentAppointmentDetails.getStaffLite().getStaffId());
        List<User> adminUserList = userPermissionManager.getUserByUserPermission(instituteId, new HashSet<>(Arrays.asList(AuthorisationRequiredAction.PARENT_APPOINTMENT_ADMIN_ACCESS)));
        Set<User> finalUserSet = new HashSet<>();

        if (raisedFor != null) {
            finalUserSet.add(raisedFor);
        }

        if (!CollectionUtils.isEmpty(adminUserList)) {
            for (User user : adminUserList) {
                if (!user.getUserStatus().equals(UserStatus.ENABLED)) {
                    continue;
                }
                finalUserSet.add(user);
            }
        }

        if (CollectionUtils.isEmpty(finalUserSet)) {
            return null;
        }

        Map<String, String> metaData = new HashMap<String, String>();
        metaData.put(APPOINTMENT_ID, appointmentId == null ? null : appointmentId.toString());

        PushNotificationContent adminPushNotificationContent = appointmentDetailsAddPushNotificationContentBuilder.getPushNotificationContent();

        UUID finalAppointmentId = appointmentId;
        prepareAndSendBulkNotificationAsync(instituteId, new ArrayList<>(finalUserSet), adminPushNotificationContent, new IUpdateNotificationPayload() {
            @Override
            public void update(Map<String, String> dataPayload, User user) {
                dataPayload.put(PushNotificationManager.CLICK_ACTION, ADMIN_ADD_APPOINTMENT);
                dataPayload.put(APPOINTMENT_ID, finalAppointmentId == null ? "" : finalAppointmentId.toString());
            }
        });

        return new BellNotificationPayload(instituteId, new ArrayList<>(finalUserSet), adminPushNotificationContent.getTitle(), adminPushNotificationContent.getBody(),
                NotificationEntity.STUDENT_ADD_APPOINTMENT, appointmentId, metaData);
    }
}
