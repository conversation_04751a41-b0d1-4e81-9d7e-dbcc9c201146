/**
 * 
 */
package com.embrate.cloud.push.notifications.handler;

import java.util.UUID;

import com.embrate.cloud.core.api.discussionboard.ChannelDetailsPayload;
import com.embrate.cloud.core.api.discussionboard.DiscussionBoardEntity;
import com.embrate.cloud.core.lib.discussionboard.DiscussionBoardManager;

/**
 * <AUTHOR>
 *
 */
public class DiscussionBoardBroadcastPushNotificationHandler {

	private final HomeworkDiscussionBoardBroadcastPushNotificationHandler homeworkDiscussionBoardBroadcastPushNotificationHandler;
	private final OnlineLectureDiscussionBoardBroadcastPushNotificationHandler onlineLectureDiscussionBoardBroadcastPushNotificationHandler;
	private final DiscussionBoardManager discussionBoardManager;

	/**
	 * @param homeworkDiscussionBoardBroadcastPushNotificationHandler
	 * @param discussionBoardManager
	 */
	public DiscussionBoardBroadcastPushNotificationHandler(
			HomeworkDiscussionBoardBroadcastPushNotificationHandler homeworkDiscussionBoardBroadcastPushNotificationHandler,
			OnlineLectureDiscussionBoardBroadcastPushNotificationHandler onlineLectureDiscussionBoardBroadcastPushNotificationHandler,
			DiscussionBoardManager discussionBoardManager) {
		this.homeworkDiscussionBoardBroadcastPushNotificationHandler = homeworkDiscussionBoardBroadcastPushNotificationHandler;
		this.onlineLectureDiscussionBoardBroadcastPushNotificationHandler = onlineLectureDiscussionBoardBroadcastPushNotificationHandler;
		this.discussionBoardManager = discussionBoardManager;
	}

	public void sendDiscussionBoardBroadcastNotificationsAsync(int instituteId, UUID entityId, boolean isThread, UUID createdUserId) {
		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				sendDiscussionBoardBroadcastNotifications(instituteId, entityId, isThread, createdUserId);
			}
		});
		t.start();
	}

	public void sendDiscussionBoardBroadcastNotifications(int instituteId, UUID entityId, boolean isThread, UUID createdUserId) {
		ChannelDetailsPayload channelDetailsPayload = null;
		if(isThread) {
			channelDetailsPayload = discussionBoardManager.getChannelDetailsByThreadId(instituteId, 
					entityId);
		}
		else {
			channelDetailsPayload = discussionBoardManager.getChannelDetailsByConversationId(instituteId, 
					entityId);
		}
		if(channelDetailsPayload != null) {
			if(channelDetailsPayload.getDiscussionBoardEntity() == DiscussionBoardEntity.ONLINE_LECTURE) {
				onlineLectureDiscussionBoardBroadcastPushNotificationHandler.sendDiscussionBoardBroadcastNotifications(
						instituteId, channelDetailsPayload.getEntityId(), entityId, isThread, createdUserId);
			}
			else if(channelDetailsPayload.getDiscussionBoardEntity() == DiscussionBoardEntity.HOMEWORK) {
				homeworkDiscussionBoardBroadcastPushNotificationHandler.sendDiscussionBoardBroadcastNotifications(
						instituteId, channelDetailsPayload.getEntityId(), entityId, isThread, createdUserId);
			}
		}
	}
}
