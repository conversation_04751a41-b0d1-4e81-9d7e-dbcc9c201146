package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.frontdesk.GatePassDetails;
import com.embrate.cloud.core.api.frontdesk.GatePassMetadata;
import com.embrate.cloud.core.api.service.notification.BellNotificationPayload;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.lib.frontdesk.FrontDeskManager;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.push.notifications.content.builder.GatePassPushNotificationContentBuilder;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

public class GatePassPushNotificationHandler  extends AbstractPushNotificationHandler {

    public static final String STUDENT_GATE_PASS = "studentGatePass";
    public static final String STUDENT_GATE_PASS_CANCELLED = "studentGatePassCancelled";
    public static final String GATE_PASS_ID = "gatePassId";

    private static final Logger logger = LogManager.getLogger(GatePassPushNotificationHandler.class);
    private final PushNotificationManager pushNotificationManager;
    private final GatePassPushNotificationContentBuilder gatePassPushNotificationContentBuilder;
    private final FrontDeskManager frontDeskManager;

    public GatePassPushNotificationHandler(PushNotificationManager pushNotificationManager,
                                           GatePassPushNotificationContentBuilder gatePassPushNotificationContentBuilder,
                                           FrontDeskManager frontDeskManager) {
        super(pushNotificationManager);
        this.pushNotificationManager = pushNotificationManager;
        this.gatePassPushNotificationContentBuilder = gatePassPushNotificationContentBuilder;
        this.frontDeskManager = frontDeskManager;
    }

    public void sendGatePassBroadcastNotificationsAsync(int instituteId, UUID gatePassId, UUID createdUserId, boolean isHosteller) {
        Thread t = new Thread(new Runnable() {
            @Override
            public void run() {
                sendGatePassBroadcastNotifications(instituteId, gatePassId, createdUserId, isHosteller);
            }
        });
        t.start();
    }

    public void sendGatePassBroadcastNotifications(int instituteId, UUID gatePassId, UUID createdUserId, boolean isHosteller) {
        if (instituteId <= 0 || gatePassId == null) {
            logger.error("Invalid institute {} or gate-pass {} ", instituteId, gatePassId);
            return;
        }

        GatePassDetails gatePassDetails = frontDeskManager.getGatePassDetailsById(instituteId, gatePassId);
        if (gatePassDetails == null) {
            logger.error("No gate pass details found for notification for institute {}, gatePassId {} ", instituteId, gatePassId);
            return;
        }

        String dateStr = DateUtils.getFormattedDate(gatePassDetails.getGatePassDate(), "dd MMMM yyyy", User.DFAULT_TIMEZONE);
        String timeStr = DateUtils.getFormattedDate(gatePassDetails.getGatePassDate(), "HH:mm", User.DFAULT_TIMEZONE);
        String withPerson = gatePassDetails.getName();

        PushNotificationContent pushNotificationContent = gatePassPushNotificationContentBuilder
                .getPushNotificationContent(dateStr, timeStr, withPerson, false, isHosteller);

        List<User> users = frontDeskManager.getGatePassUsers(instituteId, gatePassId);
        if (CollectionUtils.isEmpty(users)) {
            logger.error("No user found for notification for institute {}, gatePassId {} ", instituteId, gatePassId);
            return;
        }

        List<User> finalUserList = new ArrayList<User>();
        for (User user : users) {
            if (!user.getUserStatus().equals(UserStatus.ENABLED)) {
                continue;
            }
            if (!user.getUuid().equals(createdUserId)) {
                finalUserList.add(user);
            }
        }

        Map<String, String> metaData = new HashMap<String, String>();
        metaData.put(GATE_PASS_ID, gatePassId == null ? null : gatePassId.toString());
        boolean notificationAdded = pushNotificationManager.addNotification(new BellNotificationPayload(
                instituteId, finalUserList, pushNotificationContent.getTitle(), pushNotificationContent.getBody(),
                NotificationEntity.GATE_PASS, gatePassId, metaData));

        if (!notificationAdded) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
                            "Invalid bell notification details."));
        }


        prepareAndSendBulkNotificationAsync(instituteId, finalUserList, pushNotificationContent, new IUpdateNotificationPayload() {
            @Override
            public void update(Map<String, String> dataPayload, User user) {
                dataPayload.put(PushNotificationManager.CLICK_ACTION, STUDENT_GATE_PASS);
            }
        });
    }

    public void sendGatePassCancelledNotificationAsync(int instituteId, UUID gatePassId, UUID createdUserId, boolean isHoteller) {
        Thread t = new Thread(new Runnable() {
            @Override
            public void run() {
                sendGatePassCancelledNotification(instituteId, gatePassId, createdUserId, isHoteller);
            }
        });
        t.start();
    }

    public void sendGatePassCancelledNotification(int instituteId, UUID gatePassId, UUID createdUserId, boolean isHosteller) {
        if (instituteId <= 0 || gatePassId == null) {
            logger.error("Invalid institute {} or gate-pass {} ", instituteId, gatePassId);
            return;
        }

        GatePassMetadata gatePassMetadata = frontDeskManager.getGatePassMetadataById(instituteId, gatePassId);
        if (gatePassMetadata == null) {
            logger.error("No gate pass details found for notification for institute {}, gatePassId {} ", instituteId, gatePassId);
            return;
        }

        String dateStr = DateUtils.getFormattedDate(gatePassMetadata.getGatePassDate(), "dd MMMM yyyy", User.DFAULT_TIMEZONE);
        String timeStr = DateUtils.getFormattedDate(gatePassMetadata.getGatePassDate(), "HH:mm", User.DFAULT_TIMEZONE);
        String withPerson = gatePassMetadata.getName();

        PushNotificationContent pushNotificationContent = gatePassPushNotificationContentBuilder
                .getPushNotificationContent(dateStr, timeStr, withPerson, true, isHosteller);

        List<User> users = frontDeskManager.getGatePassUsers(instituteId, gatePassId);
        if (CollectionUtils.isEmpty(users)) {
            logger.error("No user found for notification for institute {}, gatePassId {} ", instituteId, gatePassId);
            return;
        }

        List<User> finalUserList = new ArrayList<User>();
        for (User user : users) {
            if (!user.getUserStatus().equals(UserStatus.ENABLED)) {
                continue;
            }
            if (!user.getUuid().equals(createdUserId)) {
                finalUserList.add(user);
            }
        }

        Map<String, String> metaData = new HashMap<String, String>();
        metaData.put(GATE_PASS_ID, gatePassId == null ? null : gatePassId.toString());
        boolean notificationAdded = pushNotificationManager.addNotification(new BellNotificationPayload(
                instituteId, finalUserList, pushNotificationContent.getTitle(), pushNotificationContent.getBody(),
                NotificationEntity.GATE_PASS_CANCELLED, gatePassId, metaData));

        if (!notificationAdded) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
                            "Invalid bell notification details."));
        }


        prepareAndSendBulkNotificationAsync(instituteId, finalUserList, pushNotificationContent, new IUpdateNotificationPayload() {
            @Override
            public void update(Map<String, String> dataPayload, User user) {
                dataPayload.put(PushNotificationManager.CLICK_ACTION, STUDENT_GATE_PASS_CANCELLED);
            }
        });
    }
}
