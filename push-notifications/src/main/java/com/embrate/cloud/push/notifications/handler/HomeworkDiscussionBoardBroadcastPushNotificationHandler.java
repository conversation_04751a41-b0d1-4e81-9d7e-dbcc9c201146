/**
 * 
 */
package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.discussionboard.ThreadDetailsPayload;
import com.embrate.cloud.core.api.homework.HomeworkDetails;
import com.embrate.cloud.core.api.service.notification.BellNotificationPayload;
import com.embrate.cloud.core.api.service.notification.MulticastPushNotificationMessage;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.lib.discussionboard.DiscussionBoardManager;
import com.embrate.cloud.core.lib.homework.HomeworkManager;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.push.notifications.content.builder.HomeworkDiscussionBoardBroadcastPushNotificationContentBuilder;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserNotificationDeviceData;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

import static com.embrate.cloud.core.lib.push.notification.PushNotificationManager.FLUTTER_REQUIRED_CLICK_ACTION_VAL;

/**
 * <AUTHOR>
 *
 */
public class HomeworkDiscussionBoardBroadcastPushNotificationHandler extends  AbstractPushNotificationHandler{
	
	private static final Logger logger = LogManager.getLogger(HomeworkDiscussionBoardBroadcastPushNotificationHandler.class);
	public static final String STUDENT_NEW_HOMEWORK_DISCUSSION_THREAD_MESSAGE = "studentNewHomeworkDiscussionThreadMessage";
	public static final String HOMEWORK_DETAILS = "homeworkDetails";
	private static final Gson GSON = SharedConstants.GSON;
	public static final String HOMEWORK_ID = "homeworkId";
	public static final String THREAD_ID = "threadId";
	public static final String CONVERSATION_ID = "conversationId";
	
	private final HomeworkDiscussionBoardBroadcastPushNotificationContentBuilder homeworkDiscussionBoardBroadcastPushNotificationContentBuilder;
	private final HomeworkManager homeworkManager;
	private final UserManager userManager;
	private final PushNotificationManager pushNotificationManager;
	private final UserPreferenceSettings userPreferenceSettings;
	private final UserPermissionManager userPermissionManager;
	private final DiscussionBoardManager discussionBoardManager;
	
	public HomeworkDiscussionBoardBroadcastPushNotificationHandler(PushNotificationManager pushNotificationManager, 
			UserManager userManager, HomeworkManager homeworkManager, HomeworkDiscussionBoardBroadcastPushNotificationContentBuilder 
			homeworkDiscussionBoardBroadcastPushNotificationContentBuilder, 
			UserPreferenceSettings userPreferenceSettings,
			UserPermissionManager userPermissionManager, DiscussionBoardManager discussionBoardManager) {
		super(pushNotificationManager);
		this.pushNotificationManager = pushNotificationManager;
		this.userManager = userManager;
		this.homeworkManager = homeworkManager;
		this.homeworkDiscussionBoardBroadcastPushNotificationContentBuilder = 
				homeworkDiscussionBoardBroadcastPushNotificationContentBuilder;
		this.userPreferenceSettings = userPreferenceSettings;
		this.userPermissionManager = userPermissionManager;
		this.discussionBoardManager = discussionBoardManager;
	}

	public void sendDiscussionBoardBroadcastNotifications(int instituteId, String entityIdStr, UUID createdElementId, 
			boolean isThread, UUID createdUserId) {
		if (instituteId <= 0 || StringUtils.isBlank(entityIdStr)) {
			logger.error("Invalid institute {} or channelId {} ", instituteId, entityIdStr);
			return;
		}
		UUID entityId = UUID.fromString(entityIdStr);
		
		HomeworkDetails homeworkDetails = homeworkManager.getHomeworkDetails(instituteId, entityId);
		
		if(homeworkDetails == null) {
			logger.error("Error as no homework with for institute {}, entityId {} exists", instituteId, entityId);
			return;
		}

		Map<String, String> metaData = new HashMap<String, String>();
		metaData.put(HOMEWORK_ID, homeworkDetails.getHomeworkId() == null ? null : homeworkDetails.getHomeworkId().toString());
		
		List<User> users = homeworkManager.getHomeworkUsers(instituteId, entityId);		
		List<User> adminUsers = userManager.getAllAdminUser(instituteId);
		if (!CollectionUtils.isEmpty(adminUsers)) {
			users.addAll(adminUsers);
		}
		
		if (CollectionUtils.isEmpty(users)) {
			logger.error("No user found for notification for institute {} ,entities {} ", instituteId, entityId);
			return;
		}
		
		String createdUserName = "";
		List<User> finalUserList = new ArrayList<User>();
		for(User user : users) {
			if(!user.getUserStatus().equals(UserStatus.ENABLED)) {
				continue;
			}
			if(!user.getUuid().equals(createdUserId)) {
				finalUserList.add(user);
			} else {
				createdUserName = user.getFullName();
			}
		}
		
		PushNotificationContent pushNotificationContent = null;
		if(isThread) {
			final ThreadDetailsPayload threadDetails = discussionBoardManager.getThreadDetailsByThreadId(instituteId, createdElementId);
			metaData.put(THREAD_ID, createdElementId == null ? null : createdElementId.toString());
			pushNotificationContent = homeworkDiscussionBoardBroadcastPushNotificationContentBuilder
					.getThreadPushNotificationContent(threadDetails.getName(), homeworkDetails.getTitle(), createdUserName);
		}
		else {
			final ThreadDetailsPayload threadDetails = discussionBoardManager.getThreadDetailsByConversationId(instituteId, createdElementId);
			metaData.put(CONVERSATION_ID, createdElementId == null ? null : createdElementId.toString());
			metaData.put(THREAD_ID, threadDetails.getThreadId() == null ? null : threadDetails.getThreadId().toString());
			pushNotificationContent = homeworkDiscussionBoardBroadcastPushNotificationContentBuilder
					.getConversationPushNotificationContent(threadDetails.getName(), homeworkDetails.getTitle(), createdUserName);
		}
		
		if(pushNotificationContent == null) {
			logger.error("Error while fetching notification content for institute {}, entityId {} ", instituteId, entityId);
			return;
		}
		
		boolean notificationAdded = pushNotificationManager.addNotification(new BellNotificationPayload(
				instituteId, finalUserList, pushNotificationContent.getTitle(), pushNotificationContent.getBody(), 
				NotificationEntity.HOMEWORK_DISCUSSION, createdElementId, metaData));
		
		if(!notificationAdded) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
							"Invalid bell notification details."));
		}


		prepareAndSendBulkNotificationAsync(instituteId, finalUserList, pushNotificationContent, new IUpdateNotificationPayload() {
			@Override
			public void update(Map<String, String> dataPayload, User user) {
				dataPayload.put(PushNotificationManager.CLICK_ACTION, STUDENT_NEW_HOMEWORK_DISCUSSION_THREAD_MESSAGE);
				dataPayload.put(HOMEWORK_DETAILS, GSON.toJson(homeworkDetails));
			}
		});

		// Multicast
//		List<UserNotificationDeviceData> userNotificationDeviceDataList = new ArrayList<>();
//
//		for (User user : finalUserList) {
//			if (CollectionUtils.isEmpty(user.getUserAppTokens())) {
//				continue;
//			}
//			userNotificationDeviceDataList.add(new UserNotificationDeviceData(user.getUuid(), user.getUserAppTokens()));
//		}
//
//		if (CollectionUtils.isEmpty(userNotificationDeviceDataList)) {
//			logger.error("No registered users found for notification for institute {} , entities {} ", instituteId,
//					entityId);
//			return;
//		}
//
//		logger.info("Sending push notifications for registered users {} for institute {} , entities {}",
//				userNotificationDeviceDataList.size(), instituteId, entityId);
//
//		Map<String, String> dataPayload = new HashMap<>();
//		dataPayload.put(PushNotificationManager.CLICK_ACTION, STUDENT_NEW_HOMEWORK_DISCUSSION_THREAD_MESSAGE);
//		dataPayload.put(HOMEWORK_DETAILS, GSON.toJson(homeworkDetails));
//		dataPayload.put(PushNotificationManager.FLUTTER_REQUIRED_CLICK_ACTION_KEY, FLUTTER_REQUIRED_CLICK_ACTION_VAL);
//
//		pushNotificationManager.sendMultiCastNotification(instituteId,
//				new MulticastPushNotificationMessage(pushNotificationContent.getTitle(),
//						pushNotificationContent.getBody(), pushNotificationContent.getImageURL(),
//						userNotificationDeviceDataList, dataPayload));
		
	}
}
