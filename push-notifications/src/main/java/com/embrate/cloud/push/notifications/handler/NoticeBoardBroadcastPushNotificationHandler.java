/**
 * 
 */
package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.noticeboard.NoticeDetails;
import com.embrate.cloud.core.api.service.notification.*;
import com.embrate.cloud.core.lib.noticeboard.NoticeBoardManager;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.push.notifications.content.builder.NoticeBoardBroadcastPushNotificationContentBuilder;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 *
 */
public class NoticeBoardBroadcastPushNotificationHandler extends AbstractPushNotificationHandler {
	
	private static final Logger logger = LogManager.getLogger(NoticeBoardBroadcastPushNotificationHandler.class);
	
	public static final String NEW_NOTICE_UPLOAD = "newNoticeUpload";
	public static final String NOTICE_ID = "noticeId";
	public static final String ADMIN_NOTICE = "adminNotice";
	private final PushNotificationManager pushNotificationManager;
	private final NoticeBoardBroadcastPushNotificationContentBuilder noticeBoardBroadcastPushNotificationContentBuilder;
	private final NoticeBoardManager noticeBoardManager;
	private final UserPreferenceSettings userPreferenceSettings;
	private final UserPermissionManager userPermissionManager;
	private final UserManager userManager;

	public NoticeBoardBroadcastPushNotificationHandler(PushNotificationManager pushNotificationManager,
			NoticeBoardBroadcastPushNotificationContentBuilder noticeBoardBroadcastPushNotificationContentBuilder,
			NoticeBoardManager noticeBoardManager, UserPreferenceSettings userPreferenceSettings,
			UserPermissionManager userPermissionManager, UserManager userManager) {
		super(pushNotificationManager);
		this.pushNotificationManager = pushNotificationManager;
		this.noticeBoardBroadcastPushNotificationContentBuilder = noticeBoardBroadcastPushNotificationContentBuilder;
		this.noticeBoardManager = noticeBoardManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.userPermissionManager = userPermissionManager;
		this.userManager = userManager;
	}

	public void sendNoticeBoardBroadcastNotificationsAsync(int instituteId, int academicSessionId, List<UUID> noticeIds,
			UUID createdUserId) {
		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				sendNoticeBoardBroadcastNotifications(instituteId, academicSessionId, noticeIds, createdUserId);
			}
		});
		t.start();
	}

	public void sendNoticeBoardBroadcastNotifications(int instituteId, int academicSessionId, List<UUID> noticeIds,
			UUID createdUserId) {
		if (instituteId <= 0 || CollectionUtils.isEmpty(noticeIds)) {
			logger.error("Invalid institute {} or noticeIds {} ", instituteId, noticeIds);
			return;
		}

		boolean isSingleNotice = false;
		NoticeDetails noticeDetails = null;
		String title = "";
		if(noticeIds.size() == 1) {
			noticeDetails = noticeBoardManager.getNoticeDetailsById(instituteId, noticeIds.get(0));
			isSingleNotice = true;
			title = noticeDetails.getTitle();
		}


		List<BellNotificationPayload> bellNotificationPayloadList = new ArrayList<>();
		/**
		 * student notification
		 */
		sendStudentNoticeNotifications(instituteId, academicSessionId, noticeIds, noticeDetails, createdUserId, isSingleNotice, title, bellNotificationPayloadList);
		/**
		 * admin notification
		 */
		sendAdminNoticeNotifications(instituteId, noticeDetails, isSingleNotice, title, bellNotificationPayloadList);

		if(CollectionUtils.isEmpty(bellNotificationPayloadList)) {
			return;
		}

		boolean notificationAdded = pushNotificationManager.addBulkNotification(bellNotificationPayloadList);

		if (!notificationAdded) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
							"Invalid bell notification details."));
		}

////		PushNotificationContent pushNotificationContent = noticeBoardBroadcastPushNotificationContentBuilder
////				.getPushNotificationContent();
//
//		List<User> users = noticeBoardManager.
//				getNoticeBoardUsers(instituteId, academicSessionId, new HashSet<UUID>(noticeIds));
//
//		List<User> finalUserList = new ArrayList<>();
//		for(User user : users) {
//			if(!user.getUserStatus().equals(UserStatus.ENABLED)) {
//				continue;
//			}
//			finalUserList.add(user);
//		}
//
//		if (CollectionUtils.isEmpty(finalUserList)) {
//			logger.error("No user found for notification for institute {} ,noticeIds {} ", instituteId, noticeIds);
//			return;
//		}
//
//
//		UUID noticeId = null;
//		if(noticeIds.size() == 1) {
//			noticeId = noticeIds.get(0);
//		}
//
//		Map<String, UUID> metaData = new HashMap<String, UUID>();
//		metaData.put(NOTICE_ID, noticeId);
//
//		boolean notificationAdded = pushNotificationManager.addNotification(new BellNotificationPayload(
//				instituteId, finalUserList, pushNotificationContent.getTitle(), pushNotificationContent.getBody(),
//				NotificationEntity.NOTICE, noticeId, metaData));
//
//		if(!notificationAdded) {
//			throw new ApplicationException(
//					new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
//							"Invalid bell notification details."));
//		}
//
//		final String noticeIdStr = noticeId == null ? null : noticeId.toString();
//		prepareAndSendBulkNotificationAsync(instituteId, finalUserList, pushNotificationContent, new IUpdateNotificationPayload() {
//			@Override
//			public void update(Map<String, String> dataPayload, User user) {
//				dataPayload.put(PushNotificationManager.CLICK_ACTION, NEW_NOTICE_UPLOAD);
//				dataPayload.put(NOTICE_ID, noticeIdStr);
//			}
//		});

		// Multicast
//		List<UserNotificationDeviceData> userNotificationDeviceDataList = new ArrayList<>();
//
//		for (User user : users) {
//			if (CollectionUtils.isEmpty(user.getUserAppTokens())) {
//				continue;
//			}
//			userNotificationDeviceDataList.add(new UserNotificationDeviceData(user.getUuid(), user.getUserAppTokens()));
//		}
//
//		if (CollectionUtils.isEmpty(userNotificationDeviceDataList)) {
//			logger.error("No registered users found for notification for institute {} , noticeIds {} ", instituteId,
//					noticeIds);
//			return;
//		}
//
//		logger.info("Sending push notifications for registered users {} for institute {} , noticeIds {}",
//				userNotificationDeviceDataList.size(), instituteId, noticeIds);
//
//		Map<String, String> dataPayload = new HashMap<>();
//		dataPayload.put(PushNotificationManager.CLICK_ACTION, NEW_NOTICE_UPLOAD);
//		dataPayload.put(PushNotificationManager.FLUTTER_REQUIRED_CLICK_ACTION_KEY, FLUTTER_REQUIRED_CLICK_ACTION_VAL);
//
//		pushNotificationManager.sendMultiCastNotification(instituteId,
//				new MulticastPushNotificationMessage(pushNotificationContent.getTitle(),
//						pushNotificationContent.getBody(), pushNotificationContent.getImageURL(),
//						userNotificationDeviceDataList, dataPayload));
	}

	private void sendAdminNoticeNotifications(int instituteId, NoticeDetails noticeDetails, boolean isSingleNotice, String title, List<BellNotificationPayload> bellNotificationPayloadList) {

		/**
		 * notification for admin
		 */
		PushNotificationPreferences pushNotificationPreferences = userPreferenceSettings.getPushNotificationPreferences(instituteId);
		Set<String> userNames = pushNotificationPreferences.getAdminNoticeNotificationUserNames();
		if(CollectionUtils.isEmpty(userNames)) {
			return;
		}

		List<User> adminUserList = userManager.getUsersByUserNames(userNames);
		if(CollectionUtils.isEmpty(adminUserList)) {
			return;
		}

		UUID noticeId = null;
		if(noticeDetails != null) {
			noticeId = noticeDetails.getNoticeId();
		}

		Map<String, String> metaData = new HashMap<String, String>();
		metaData.put(NOTICE_ID, noticeId == null ? null : noticeId.toString());

		PushNotificationContent adminPushNotificationContent = noticeBoardBroadcastPushNotificationContentBuilder.getPushNotificationContent(isSingleNotice, title, UserType.ADMIN);
		bellNotificationPayloadList.add(new BellNotificationPayload(
				instituteId, adminUserList, adminPushNotificationContent.getTitle(), adminPushNotificationContent.getBody(),
				NotificationEntity.ADMIN_NOTICE, noticeId, metaData));

		UUID finalNoticeId = noticeId;
		prepareAndSendBulkNotificationAsync(instituteId, adminUserList, adminPushNotificationContent, new IUpdateNotificationPayload() {
			@Override
			public void update(Map<String, String> dataPayload, User user) {
				dataPayload.put(PushNotificationManager.CLICK_ACTION, ADMIN_NOTICE);
				dataPayload.put(NOTICE_ID, finalNoticeId == null ? "" : finalNoticeId.toString());
			}
		});
	}

	private void sendStudentNoticeNotifications(int instituteId, int academicSessionId, List<UUID> noticeIds, NoticeDetails noticeDetails, UUID createdUserId, boolean isSingleNotice, String title, List<BellNotificationPayload> bellNotificationPayloadList) {

		/**
		 * notification for student
		 */
		List<User> users = noticeBoardManager.getNoticeBoardUsers(instituteId, academicSessionId, new HashSet<UUID>(noticeIds));
		if (CollectionUtils.isEmpty(users)) {
			logger.error("No user found for notification for institute {}, notices {} ", instituteId, noticeIds);
			return;
		}

		List<User> finalUserList = new ArrayList<User>();
		for(User user : users) {
			if(!user.getUserStatus().equals(UserStatus.ENABLED)) {
				continue;
			}
			if(!user.getUuid().equals(createdUserId)) {
				finalUserList.add(user);
			}
		}
		UUID noticeId = null;
		if(noticeDetails != null) {
			noticeId = noticeDetails.getNoticeId();
		}

		Map<String, String> metaData = new HashMap<String, String>();
		metaData.put(NOTICE_ID, noticeId == null ? null : noticeId.toString());

		PushNotificationContent studentPushNotificationContent = noticeBoardBroadcastPushNotificationContentBuilder.getPushNotificationContent(isSingleNotice, title, UserType.STUDENT);

		bellNotificationPayloadList.add(new BellNotificationPayload(
				instituteId, finalUserList, studentPushNotificationContent.getTitle(), studentPushNotificationContent.getBody(),
				NotificationEntity.NOTICE, noticeId, metaData));

		UUID finalNoticeId = noticeId;
		prepareAndSendBulkNotificationAsync(instituteId, finalUserList, studentPushNotificationContent, new IUpdateNotificationPayload() {
			@Override
			public void update(Map<String, String> dataPayload, User user) {
				dataPayload.put(PushNotificationManager.CLICK_ACTION, NEW_NOTICE_UPLOAD);
				dataPayload.put(NOTICE_ID, finalNoticeId == null ? "" : finalNoticeId.toString());
			}
		});

	}
}
