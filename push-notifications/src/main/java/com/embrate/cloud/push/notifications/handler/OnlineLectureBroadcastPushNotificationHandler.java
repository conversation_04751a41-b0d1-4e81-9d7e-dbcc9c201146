package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.service.notification.BellNotificationPayload;
import com.embrate.cloud.core.api.service.notification.MulticastPushNotificationMessage;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.lib.lecture.LectureManager;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.push.notifications.content.builder.OnlineLectureBroadcastPushNotificationContentBuilder;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserNotificationDeviceData;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

import static com.embrate.cloud.core.lib.push.notification.PushNotificationManager.FLUTTER_REQUIRED_CLICK_ACTION_VAL;

/**
 * 
 * <AUTHOR>
 *
 */
public class OnlineLectureBroadcastPushNotificationHandler extends AbstractPushNotificationHandler {

	public static final String STUDENT_NEW_LECTURE_UPLOAD = "studentNewLectureUpload";
	public static final String LECTURE_ID = "lectureId";
	
	private static final Logger logger = LogManager.getLogger(OnlineLectureBroadcastPushNotificationHandler.class);
	private final PushNotificationManager pushNotificationManager;
	private final OnlineLectureBroadcastPushNotificationContentBuilder onlineLectureBroadcastPushNotificationContentBuilder;
	private final LectureManager lectureManager;
	private final UserPreferenceSettings userPreferenceSettings;
	private final UserPermissionManager userPermissionManager;



	public OnlineLectureBroadcastPushNotificationHandler(PushNotificationManager pushNotificationManager,
			OnlineLectureBroadcastPushNotificationContentBuilder onlineLectureBroadcastPushNotificationContentBuilder,
			LectureManager lectureManager, UserPreferenceSettings userPreferenceSettings,
			UserPermissionManager userPermissionManager) {
		super(pushNotificationManager);
		this.pushNotificationManager = pushNotificationManager;
		this.onlineLectureBroadcastPushNotificationContentBuilder = onlineLectureBroadcastPushNotificationContentBuilder;
		this.lectureManager = lectureManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.userPermissionManager = userPermissionManager;
	}

	public void sendLectureBroadcastNotificationsAsync(int instituteId, List<UUID> lectureIds, UUID createdUserId) {
		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				sendLectureBroadcastNotifications(instituteId, lectureIds, createdUserId);
			}
		});
		t.start();
	}

	public void sendLectureBroadcastNotifications(int instituteId, List<UUID> lectureIds, UUID createdUserId) {
		if (instituteId <= 0 || CollectionUtils.isEmpty(lectureIds)) {
			logger.error("Invalid institute {} or lectures {} ", instituteId, lectureIds);
			return;
		}

		PushNotificationContent pushNotificationContent = onlineLectureBroadcastPushNotificationContentBuilder
				.getPushNotificationContent();

		List<User> users = lectureManager.getLectureUsers(instituteId, new HashSet<UUID>(lectureIds));
		if (CollectionUtils.isEmpty(users)) {
			logger.error("No user found for notification for institute {} ,lectures {} ", instituteId, lectureIds);
			return;
		}
		
		List<User> finalUserList = new ArrayList<User>();
		for(User user : users) {
			if(!user.getUserStatus().equals(UserStatus.ENABLED)) {
				continue;
			}
			if(!user.getUuid().equals(createdUserId)) {
				finalUserList.add(user);
			}
		}
		
		UUID lectureId = null;
		if(lectureIds.size() == 1) {
			lectureId = lectureIds.get(0);
		}

		Map<String, String> metaData = new HashMap<String, String>();
		metaData.put(LECTURE_ID, lectureId == null ? null : lectureId.toString());
		boolean notificationAdded = pushNotificationManager.addNotification(new BellNotificationPayload(
				instituteId, finalUserList, pushNotificationContent.getTitle(), pushNotificationContent.getBody(), 
				NotificationEntity.ONLINE_LECTURE, lectureId, metaData));
		
		if(!notificationAdded) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
							"Invalid bell notification details."));
		}


		prepareAndSendBulkNotificationAsync(instituteId, finalUserList, pushNotificationContent, new IUpdateNotificationPayload() {
			@Override
			public void update(Map<String, String> dataPayload, User user) {
				dataPayload.put(PushNotificationManager.CLICK_ACTION, STUDENT_NEW_LECTURE_UPLOAD);
			}
		});

		//Multicast
//		List<UserNotificationDeviceData> userNotificationDeviceDataList = new ArrayList<>();
//
//		for (User user : finalUserList) {
//			if (CollectionUtils.isEmpty(user.getUserAppTokens())) {
//				continue;
//			}
//			userNotificationDeviceDataList.add(new UserNotificationDeviceData(user.getUuid(), user.getUserAppTokens()));
//		}
//
//		if (CollectionUtils.isEmpty(userNotificationDeviceDataList)) {
//			logger.error("No registered users found for notification for institute {} , lectures {} ", instituteId,
//					lectureIds);
//			return;
//		}
//
//		logger.info("Sending push notifications for registered users {} for institute {} , lectures {}",
//				userNotificationDeviceDataList.size(), instituteId, lectureIds);
//
//		Map<String, String> dataPayload = new HashMap<>();
//		dataPayload.put(PushNotificationManager.CLICK_ACTION, STUDENT_NEW_LECTURE_UPLOAD);
//		dataPayload.put(PushNotificationManager.FLUTTER_REQUIRED_CLICK_ACTION_KEY, FLUTTER_REQUIRED_CLICK_ACTION_VAL);
//
//		pushNotificationManager.sendMultiCastNotification(instituteId,
//				new MulticastPushNotificationMessage(pushNotificationContent.getTitle(),
//						pushNotificationContent.getBody(), pushNotificationContent.getImageURL(),
//						userNotificationDeviceDataList, dataPayload));

	}
}
