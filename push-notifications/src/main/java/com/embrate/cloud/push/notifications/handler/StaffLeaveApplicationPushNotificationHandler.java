package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.leave.management.staff.StaffLeaveDetails;
import com.embrate.cloud.core.api.service.notification.BellNotificationPayload;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.lib.leave.management.StaffLeaveManagementManager;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.push.notifications.content.builder.StaffLeaveApplicationPushNotificationContentBuilder;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 *
 */


public class StaffLeaveApplicationPushNotificationHandler extends AbstractPushNotificationHandler{
    
    private static final Logger logger = LogManager.getLogger(StaffLeaveApplicationPushNotificationHandler.class);
    public static final String TRANSACTION_ID = "transactionId";
    public static final String STAFF_LEAVE_APPLICATION = "StaffLeaveApplication";
    private final PushNotificationManager pushNotificationManager;
    private final StaffLeaveManagementManager staffLeaveManagementManager;
    private final UserPermissionManager userPermissionManager;
    private final StaffLeaveApplicationPushNotificationContentBuilder staffLeaveApplicationPushNotificationContentBuilder;

    public StaffLeaveApplicationPushNotificationHandler( PushNotificationManager pushNotificationManager, StaffLeaveManagementManager staffLeaveManagementManager, UserPermissionManager userPermissionManager, StaffLeaveApplicationPushNotificationContentBuilder staffLeaveApplicationPushNotificationContentBuilder) {
        super(pushNotificationManager);
        this.pushNotificationManager = pushNotificationManager;
        this.staffLeaveManagementManager = staffLeaveManagementManager;
        this.userPermissionManager = userPermissionManager;
        this.staffLeaveApplicationPushNotificationContentBuilder = staffLeaveApplicationPushNotificationContentBuilder;
    }

    public void sendStaffLeaveApplicationPushNotificationsAsync(int instituteId, Set<UUID>transactionIdSet, int academicSessionId) {
        Thread t = new Thread(new Runnable() {
            @Override
            public void run() {
                sendStaffLeaveApplicationNotifications(instituteId, academicSessionId, transactionIdSet);
            }
        });
        t.start();
    }

    public void sendStaffLeaveApplicationNotifications(int instituteId, int academicSessionId, Set<UUID> transactionIdSet) {
        if (instituteId <= 0) {
            logger.error("Invalid institute {} ", instituteId);
            return;
        }
        //here we are fetching the first element of set assuming the first transaction id will fetch all the details of student leave application
        UUID transactionId = new ArrayList<>(transactionIdSet).get(0);
        
        StaffLeaveDetails staffLeaveDetails = staffLeaveManagementManager.getLeaveByTransactionId(instituteId, academicSessionId, transactionId);

        BellNotificationPayload bellNotificationPayload = sendStaffLeaveApplicationNotifications(instituteId , academicSessionId, transactionId, staffLeaveDetails);
        if (bellNotificationPayload == null) {
            return;
        }

        boolean notificationAdded = pushNotificationManager.addNotification(bellNotificationPayload);

        if (!notificationAdded) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
                            "Invalid bell notification details."));
        }
    }

    private BellNotificationPayload sendStaffLeaveApplicationNotifications(int instituteId, int academicSessionId, UUID transactionId, StaffLeaveDetails staffLeaveDetails) {
        List<User> adminUserList = userPermissionManager.getUserByUserPermission(instituteId, new HashSet<>(Arrays.asList(AuthorisationRequiredAction.STAFF_LEAVE_APPLICATION_ADMIN_ACCESS)));
        List<User> finalUserList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(adminUserList)) {
            for (User user : adminUserList) {
                if (!user.getUserStatus().equals(UserStatus.ENABLED)) {
                    continue;
                }
                finalUserList.add(user);
            }

        }

        if (CollectionUtils.isEmpty(finalUserList)) {
            return null;
        }

        Map<String, String> metaData = new HashMap<String, String>();
        metaData.put(TRANSACTION_ID, transactionId == null ? null : transactionId.toString());

        PushNotificationContent adminPushNotificationContent = staffLeaveApplicationPushNotificationContentBuilder.getPushNotificationContent(staffLeaveDetails.getStaffLite().getName());

        UUID finalTransactionId = transactionId;
        prepareAndSendBulkNotificationAsync(instituteId, finalUserList, adminPushNotificationContent, new IUpdateNotificationPayload() {
            @Override
            public void update(Map<String, String> dataPayload, User user) {
                dataPayload.put(PushNotificationManager.CLICK_ACTION, STAFF_LEAVE_APPLICATION);
                dataPayload.put(TRANSACTION_ID, finalTransactionId == null ? "" : finalTransactionId.toString());
            }
        });

        return  new BellNotificationPayload(instituteId, finalUserList, adminPushNotificationContent.getTitle(), adminPushNotificationContent.getBody(),
                NotificationEntity.STAFF_LEAVE_APPLICATION, transactionId, metaData);
    }

}
