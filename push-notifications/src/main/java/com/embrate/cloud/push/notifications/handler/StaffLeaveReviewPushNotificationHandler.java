package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.leave.management.user.LeaveReviewPayload;
import com.embrate.cloud.core.api.leave.management.staff.StaffLeaveDetails;
import com.embrate.cloud.core.api.leave.management.student.StudentLeaveDetails;
import com.embrate.cloud.core.api.service.notification.BellNotificationPayload;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.lib.leave.management.StaffLeaveManagementManager;
import com.embrate.cloud.core.lib.leave.management.StudentLeaveManagementManager;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.push.notifications.content.builder.StaffLeaveReviewPushNotificationContentBuilder;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 *
 */

public class StaffLeaveReviewPushNotificationHandler extends AbstractPushNotificationHandler{
    private static final Logger logger = LogManager.getLogger(StaffLeaveReviewPushNotificationHandler.class);

	private static final String STAFF_LEAVE_REVIEWED = "staffLeaveReviewed";
	private static final String STAFF_LEAVE_DETAILS = "staffLeaveDetails";
	private static final String TRANSACTION_ID = "transactionId";
	private static final Gson GSON = SharedConstants.GSON;
	private final PushNotificationManager pushNotificationManager;
	private final StaffLeaveReviewPushNotificationContentBuilder staffLeaveReviewPushNotificationContentBuilder;
	private final StaffLeaveManagementManager staffLeaveManagementManager;
	private final UserManager userManager;

	public StaffLeaveReviewPushNotificationHandler(PushNotificationManager pushNotificationManager,
													 StaffLeaveReviewPushNotificationContentBuilder staffLeaveReviewPushNotificationContentBuilder,
													 StaffLeaveManagementManager staffLeaveManagementManager,
                                                     UserManager userManager) {
		super(pushNotificationManager);
		this.pushNotificationManager = pushNotificationManager;
		this.staffLeaveReviewPushNotificationContentBuilder = staffLeaveReviewPushNotificationContentBuilder;
		this.staffLeaveManagementManager = staffLeaveManagementManager;
		this.userManager = userManager;
	}

	public void sendStaffLeaveReviewedNotificationsAsync(
			int instituteId, int academicSessionId, LeaveReviewPayload leaveReviewPayload) {
		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				sendStaffLeaveReviewedNotifications(instituteId, academicSessionId, leaveReviewPayload);
			}
		});
		t.start();
	}

	public void sendStaffLeaveReviewedNotifications(int instituteId, int academicSessionId, LeaveReviewPayload leaveReviewPayload) {
		if (instituteId <= 0 || academicSessionId <= 0 || leaveReviewPayload == null) {
			logger.error("Invalid institute {} or academicSessionId {} or payload {}", instituteId, academicSessionId, leaveReviewPayload);
			return;
		}
		StaffLeaveDetails staffLeaveDetails = staffLeaveManagementManager.getLeaveByTransactionId(instituteId, leaveReviewPayload.getAcademicSessionId(),
				leaveReviewPayload.getTransactionId());

		if(staffLeaveDetails == null || staffLeaveDetails.getStaffId() == null) {
			logger.error("Invalid leave details {}", staffLeaveDetails);
			return;
		}
		Set<UUID> userIdSet = new HashSet<>();
		userIdSet.add(staffLeaveDetails.getStaffId());
		List<User> users = userManager.getUsers(instituteId, userIdSet);
		if (CollectionUtils.isEmpty(users)) {
			logger.error("No user found for notification for institute {}, staffId {}", instituteId, staffLeaveDetails.getStaffId());
			return;
		}

		List<User> finalUserList = new ArrayList<User>();
		for(User user : users) {
			if(!user.getUserStatus().equals(UserStatus.ENABLED)) {
				continue;
			}
			finalUserList.add(user);
		}

		Map<String, String> metaData = new HashMap<String, String>();
		metaData.put(TRANSACTION_ID, leaveReviewPayload.getTransactionId() == null ? null : leaveReviewPayload.getTransactionId().toString());

		PushNotificationContent pushNotificationContent = staffLeaveReviewPushNotificationContentBuilder
				.getPushNotificationContent(staffLeaveDetails.getTransactionStatus());

		boolean notificationAdded = pushNotificationManager.addNotification(new BellNotificationPayload(
				instituteId, finalUserList, pushNotificationContent.getTitle(), pushNotificationContent.getBody(), 
				NotificationEntity.STAFF_LEAVE_REVIEWED, leaveReviewPayload.getTransactionId(), metaData));
		
		if(!notificationAdded) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
							"Invalid bell notification details."));
		}


		prepareAndSendBulkNotificationAsync(instituteId, finalUserList, pushNotificationContent, new IUpdateNotificationPayload() {
			@Override
			public void update(Map<String, String> dataPayload, User user) {
				dataPayload.put(PushNotificationManager.CLICK_ACTION, STAFF_LEAVE_REVIEWED);
				dataPayload.put(STAFF_LEAVE_DETAILS, GSON.toJson(staffLeaveDetails));
			}
		});
	}
}
