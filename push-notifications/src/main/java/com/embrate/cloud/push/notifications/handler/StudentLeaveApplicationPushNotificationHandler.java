package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.leave.management.student.StudentLeaveDetails;
import com.embrate.cloud.core.api.service.notification.BellNotificationPayload;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.lib.leave.management.StudentLeaveManagementManager;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.push.notifications.content.builder.StudentLeaveApplicationPushNotificationContentBuilder;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.institute.StandardWithStaffDetails;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.lib.institute.InstituteManagementManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

public class StudentLeaveApplicationPushNotificationHandler extends AbstractPushNotificationHandler{
    private static final Logger logger = LogManager.getLogger(StudentLeaveApplicationPushNotificationHandler.class);
    public static final String TRANSACTION_ID = "transactionId";
    public static final String ADMIN_STUDENT_LEAVE_APPLICATION = "adminStudentLeaveApplication";
    private final UserManager userManager;
    private final PushNotificationManager pushNotificationManager;
    private final StudentLeaveManagementManager studentLeaveManagementManager;
    private final UserPermissionManager userPermissionManager;
    private final InstituteManagementManager instituteManagementManager;
    private final StudentLeaveApplicationPushNotificationContentBuilder studentLeaveApplicationPushNotificationContentBuilder;

    public StudentLeaveApplicationPushNotificationHandler(UserManager userManager, PushNotificationManager pushNotificationManager, StudentLeaveManagementManager studentLeaveManagementManager, UserPermissionManager userPermissionManager, InstituteManagementManager instituteManagementManager, StudentLeaveApplicationPushNotificationContentBuilder studentLeaveApplicationPushNotificationContentBuilder) {
        super(pushNotificationManager);
        this.userManager = userManager;
        this.pushNotificationManager = pushNotificationManager;
        this.studentLeaveManagementManager = studentLeaveManagementManager;
        this.userPermissionManager = userPermissionManager;
        this.instituteManagementManager = instituteManagementManager;
        this.studentLeaveApplicationPushNotificationContentBuilder = studentLeaveApplicationPushNotificationContentBuilder;
    }

    public void sendStudentLeaveApplicationPushNotificationsAsync(int instituteId, Set<UUID>transactionIdSet, int academicSessionId) {
        Thread t = new Thread(new Runnable() {
            @Override
            public void run() {
                sendStudentLeaveApplicationNotifications(instituteId, academicSessionId, transactionIdSet);
            }
        });
        t.start();
    }

    public void sendStudentLeaveApplicationNotifications(int instituteId, int academicSessionId, Set<UUID> transactionIdSet) {
        if (instituteId <= 0) {
            logger.error("Invalid institute {} ", instituteId);
            return;
        }
        //here we are fetching the first element of set assuming the first transaction id will fetch all the details of student leave application
        UUID transactionId = new ArrayList<>(transactionIdSet).get(0);
        StudentLeaveDetails studentLeaveDetails = studentLeaveManagementManager.getLeaveByTransactionId(instituteId, academicSessionId, transactionId);

        BellNotificationPayload bellNotificationPayload = sendAdminStudentLeaveApplicationNotifications(instituteId , academicSessionId, transactionId, studentLeaveDetails);
        if (bellNotificationPayload == null) {
            return;
        }

        boolean notificationAdded = pushNotificationManager.addNotification(bellNotificationPayload);

        if (!notificationAdded) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
                            "Invalid bell notification details."));
        }
    }

    private BellNotificationPayload sendAdminStudentLeaveApplicationNotifications(int instituteId, int academicSessionId, UUID transactionId, StudentLeaveDetails studentLeaveDetails) {
        StandardSections section = studentLeaveDetails.getStudentLite().getStudentSessionData().getStandardSection();
        Integer sectionId = null;
        if (section != null) {
            sectionId = section.getSectionId();
        }
        StandardWithStaffDetails staffDetails = instituteManagementManager.getClassTeacherByStudentDetails(instituteId, academicSessionId, studentLeaveDetails.getStudentLite().getStudentSessionData().getStandardId(), sectionId);
        User classTeacherDetailsUser = null;
        if (staffDetails != null && staffDetails.getStaffLite() != null) {
         classTeacherDetailsUser = userManager.getUser(staffDetails.getStaffLite().getStaffId());
        }
        List<User> adminUserList = userPermissionManager.getUserByUserPermission(instituteId, new HashSet<>(Arrays.asList(AuthorisationRequiredAction.ACCESS_ALL_CLASSES_STUDENT_LEAVE_DATA)));
        List<User> finalUserList = new ArrayList<>();
        if (classTeacherDetailsUser != null) {
            finalUserList.add(classTeacherDetailsUser);
        }

        if (!CollectionUtils.isEmpty(adminUserList)) {
            for (User user : adminUserList) {
                if (!user.getUserStatus().equals(UserStatus.ENABLED)) {
                    continue;
                }
                if (classTeacherDetailsUser == null) {
                    finalUserList.add(user);
                } else if (!user.getUuid().equals(classTeacherDetailsUser.getUuid())) {
                    finalUserList.add(user);
                }
            }

        }

        if (CollectionUtils.isEmpty(finalUserList)) {
            return null;
        }

        Map<String, String> metaData = new HashMap<String, String>();
        metaData.put(TRANSACTION_ID, transactionId == null ? null : transactionId.toString());

        PushNotificationContent adminPushNotificationContent = studentLeaveApplicationPushNotificationContentBuilder.getPushNotificationContent();

        UUID finalTransactionId = transactionId;
        prepareAndSendBulkNotificationAsync(instituteId, finalUserList, adminPushNotificationContent, new IUpdateNotificationPayload() {
            @Override
            public void update(Map<String, String> dataPayload, User user) {
                dataPayload.put(PushNotificationManager.CLICK_ACTION, ADMIN_STUDENT_LEAVE_APPLICATION);
                dataPayload.put(TRANSACTION_ID, finalTransactionId == null ? "" : finalTransactionId.toString());
            }
        });

        return  new BellNotificationPayload(instituteId, finalUserList, adminPushNotificationContent.getTitle(), adminPushNotificationContent.getBody(),
                NotificationEntity.STUDENT_LEAVE_APPLICATION, transactionId, metaData);
    }


    }
