<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-3.0.xsd 
	http://www.springframework.org/schema/context
	http://www.springframework.org/schema/context/spring-context-3.0.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

	<context:annotation-config />
	<context:property-placeholder
		location="classpath*:core-lib-${lernen_env}.properties, classpath*:dao-tier-${lernen_env}.properties"
		system-properties-mode="OVERRIDE" />

	<import resource="classpath:core-lib.xml" />
	<import resource="classpath:core-utils.xml" />

	<bean id="onlineLectureBroadcastPushNotificationContentBuilder"
		class="com.embrate.cloud.push.notifications.content.builder.OnlineLectureBroadcastPushNotificationContentBuilder">
	</bean>

	<bean id="onlineLectureBroadcastPushNotificationHandler"
		class="com.embrate.cloud.push.notifications.handler.OnlineLectureBroadcastPushNotificationHandler">
		<constructor-arg name="pushNotificationManager"
			ref="pushNotificationManager" />
		<constructor-arg
			name="onlineLectureBroadcastPushNotificationContentBuilder"
			ref="onlineLectureBroadcastPushNotificationContentBuilder" />
		<constructor-arg name="lectureManager"
			ref="lectureManager" />
		<constructor-arg name="userPreferenceSettings"
			ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager"
			ref="userPermissionManager" />

	</bean>
	
	<bean id="homeworkBroadcastPushNotificationContentBuilder"
		class="com.embrate.cloud.push.notifications.content.builder.HomeworkBroadcastPushNotificationContentBuilder">
	</bean>
	<bean id="complainBoxPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.ComplainBoxPushNotificationContentBuilder">
	</bean>


	<bean id="homeworkBroadcastPushNotificationHandler"
		class="com.embrate.cloud.push.notifications.handler.HomeworkBroadcastPushNotificationHandler">
		<constructor-arg name="pushNotificationManager"
			ref="pushNotificationManager" />
		<constructor-arg
			name="homeworkBroadcastPushNotificationContentBuilder"
			ref="homeworkBroadcastPushNotificationContentBuilder" />
		<constructor-arg name="homeworkManager"
			ref="homeworkManager" />
		<constructor-arg name="userPreferenceSettings"
			ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager"
			ref="userPermissionManager" />
		<constructor-arg name="userManager"
						 ref="userManager" />
	</bean>

	<bean id="studentAddComplainPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.StudentAddComplainPushNotificationHandler">
		<constructor-arg name="pushNotificationManager"
						 ref="pushNotificationManager"/>
		<constructor-arg
				name="complainBoxPushNotificationContentBuilder"
				ref="complainBoxPushNotificationContentBuilder"/>
		<constructor-arg name="userManager"
						 ref="userManager"/>
		<constructor-arg name="complainBoxManager" ref="complainBoxManager"/>

	</bean>
	
	<bean id="discussionBoardBroadcastPushNotificationHandler"
		class="com.embrate.cloud.push.notifications.handler.DiscussionBoardBroadcastPushNotificationHandler">
		<constructor-arg name="homeworkDiscussionBoardBroadcastPushNotificationHandler"
			ref="homeworkDiscussionBoardBroadcastPushNotificationHandler" />	
		<constructor-arg name="onlineLectureDiscussionBoardBroadcastPushNotificationHandler"
			ref="onlineLectureDiscussionBoardBroadcastPushNotificationHandler" />	
		<constructor-arg name="discussionBoardManager"
			ref="discussionBoardManager" />
	</bean>
	
	<bean id="homeworkDiscussionBoardBroadcastPushNotificationContentBuilder"
		class="com.embrate.cloud.push.notifications.content.builder.HomeworkDiscussionBoardBroadcastPushNotificationContentBuilder">
	</bean>
	
	<bean id="onlineLectureDiscussionBoardBroadcastPushNotificationContentBuilder"
		class="com.embrate.cloud.push.notifications.content.builder.OnlineLectureDiscussionBoardBroadcastPushNotificationContentBuilder">
	</bean>
	
	<bean id="homeworkDiscussionBoardBroadcastPushNotificationHandler"
		class="com.embrate.cloud.push.notifications.handler.HomeworkDiscussionBoardBroadcastPushNotificationHandler">
		<constructor-arg name="pushNotificationManager"
			ref="pushNotificationManager" />	
		<constructor-arg name="userManager"
			ref="userManager" />
		<constructor-arg name="homeworkManager"
			ref="homeworkManager" />	
		<constructor-arg
			name="homeworkDiscussionBoardBroadcastPushNotificationContentBuilder"
			ref="homeworkDiscussionBoardBroadcastPushNotificationContentBuilder" />
		<constructor-arg name="userPreferenceSettings"
			ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager"
			ref="userPermissionManager" />
		<constructor-arg name="discussionBoardManager"
			ref="discussionBoardManager" />
	</bean>
	
	<bean id="onlineLectureDiscussionBoardBroadcastPushNotificationHandler"
		class="com.embrate.cloud.push.notifications.handler.OnlineLectureDiscussionBoardBroadcastPushNotificationHandler">
		<constructor-arg name="pushNotificationManager"
			ref="pushNotificationManager" />	
		<constructor-arg name="userManager"
			ref="userManager" />
		<constructor-arg name="lectureManager"
			ref="lectureManager" />	
		<constructor-arg
			name="onlineLectureDiscussionBoardBroadcastPushNotificationContentBuilder"
			ref="onlineLectureDiscussionBoardBroadcastPushNotificationContentBuilder" />
		<constructor-arg name="userPreferenceSettings"
			ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager"
			ref="userPermissionManager" />
		<constructor-arg name="discussionBoardManager"
			ref="discussionBoardManager" />
	</bean>
	
	<bean id="noticeBoardBroadcastPushNotificationContentBuilder"
		class="com.embrate.cloud.push.notifications.content.builder.NoticeBoardBroadcastPushNotificationContentBuilder">
	</bean>
	
	<bean id="noticeBoardBroadcastPushNotificationHandler"
		class="com.embrate.cloud.push.notifications.handler.NoticeBoardBroadcastPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />	
		<constructor-arg name="noticeBoardManager" ref="noticeBoardManager" />	
		<constructor-arg name="noticeBoardBroadcastPushNotificationContentBuilder"
			ref="noticeBoardBroadcastPushNotificationContentBuilder" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="userManager" ref="userManager" />
	</bean>


	<bean id="scratchCardBroadcastPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.ScratchCardBroadcastPushNotificationContentBuilder">
	</bean>

	<bean id="scratchCardBroadcastPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.ScratchCardBroadcastPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="scratchCardBroadcastPushNotificationContentBuilder"
						 ref="scratchCardBroadcastPushNotificationContentBuilder" />
		<constructor-arg name="userManager" ref="userManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
	</bean>

	<bean id="homeworkMarkingUpdatePushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.HomeworkMarkingUpdatePushNotificationContentBuilder">
	</bean>

	<bean id="homeworkMarkingUpdatePushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.HomeworkMarkingUpdatePushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="homeworkMarkingUpdatePushNotificationContentBuilder" ref="homeworkMarkingUpdatePushNotificationContentBuilder" />
		<constructor-arg name="homeworkManager" ref="homeworkManager" />
		<constructor-arg name="userManager" ref="userManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
	</bean>

	<bean id="attendanceUpdatePushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.AttendanceUpdatePushNotificationContentBuilder">
	</bean>

	<bean id="attendanceUpdatePushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.AttendanceUpdatePushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="attendanceUpdatePushNotificationContentBuilder" ref="attendanceUpdatePushNotificationContentBuilder" />
		<constructor-arg name="userManager" ref="userManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="attendanceManager" ref="attendanceManager" />
	</bean>


	<bean id="customPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.CustomPushNotificationContentBuilder">
	</bean>

	<bean id="customPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.CustomPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="customPushNotificationContentBuilder" ref="customPushNotificationContentBuilder" />
		<constructor-arg name="userManager" ref="userManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
	</bean>

	<bean id="timetablePublishedPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.TimetablePublishedPushNotificationContentBuilder">
	</bean>

	<bean id="timetablePublishedPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.TimetablePublishedPushNotificationHandler">
		<constructor-arg name="pushNotificationManager"
						 ref="pushNotificationManager" />
		<constructor-arg
				name="timetablePublishedPushNotificationContentBuilder"
				ref="timetablePublishedPushNotificationContentBuilder" />
		<constructor-arg name="timetableManager"
						 ref="timetableManager" />
		<constructor-arg name="userPreferenceSettings"
						 ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager"
						 ref="userPermissionManager" />
	</bean>

	<bean id="gatePassPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.GatePassPushNotificationContentBuilder">
	</bean>

	<bean id="gatePassPushNotificationHandler" class="com.embrate.cloud.push.notifications.handler.GatePassPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="gatePassPushNotificationContentBuilder" ref="gatePassPushNotificationContentBuilder" />
		<constructor-arg name="frontDeskManager" ref="frontDeskManager" />
	</bean>

	<bean id="transportTrackingPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.TransportTrackingPushNotificationContentBuilder">
	</bean>

	<bean id="transportTrackingPushNotificationHandler" class="com.embrate.cloud.push.notifications.handler.TransportTrackingPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="transportTrackingPushNotificationContentBuilder" ref="transportTrackingPushNotificationContentBuilder" />
		<constructor-arg name="transportTrackingManger" ref="transportTrackingManger" />
		<constructor-arg name="userManager" ref="userManager" />
	</bean>

	<bean id="studentDiaryPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.StudentDiaryPushNotificationContentBuilder">
	</bean>

	<bean id="studentDiaryPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.StudentDiaryPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="studentDiaryPushNotificationContentBuilder" ref="studentDiaryPushNotificationContentBuilder" />
		<constructor-arg name="studentDiaryManager" ref="studentDiaryManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
	</bean>

	<bean id="staffDiaryPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.StaffDiaryPushNotificationContentBuilder">
	</bean>

	<bean id="staffDiaryPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.StaffDiaryPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="staffDiaryPushNotificationContentBuilder" ref="staffDiaryPushNotificationContentBuilder" />
		<constructor-arg name="staffDiaryManager" ref="staffDiaryManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
	</bean>

	<bean id="feePaymentPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.FeePaymentPushNotificationContentBuilder">
	</bean>

	<bean id="feePaymentPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.FeePaymentPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="feePaymentPushNotificationContentBuilder" ref="feePaymentPushNotificationContentBuilder" />
		<constructor-arg name="userManager" ref="userManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="feePaymentManager" ref="feePaymentManager" />
	</bean>

	<bean id="pushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.PushNotificationContentBuilder">
		<constructor-arg name="notificationTemplateManager" ref="notificationTemplateManager" />
	</bean>

	<bean id="feePaymentReminderPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.FeePaymentReminderPushNotificationContentBuilder">
		<constructor-arg name="notificationTemplateManager" ref="notificationTemplateManager" />
	</bean>

	<bean id="feePaymentReminderPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.FeePaymentReminderPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="feePaymentReminderPushNotificationContentBuilder" ref="feePaymentReminderPushNotificationContentBuilder" />
		<constructor-arg name="userManager" ref="userManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="feePaymentManager" ref="feePaymentManager" />
	</bean>

	<bean id="birthdayPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.BirthdayPushNotificationContentBuilder">
	</bean>

	<bean id="birthdayPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.BirthdayPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="birthdayPushNotificationContentBuilder" ref="birthdayPushNotificationContentBuilder" />
		<constructor-arg name="userManager" ref="userManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="instituteManager" ref="instituteManager" />
	</bean>

	<bean id="studentLeaveReviewPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.StudentLeaveReviewPushNotificationContentBuilder">
	</bean>

	<bean id="studentLeaveReviewPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.StudentLeaveReviewPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="studentLeaveReviewPushNotificationContentBuilder" ref="studentLeaveReviewPushNotificationContentBuilder" />
		<constructor-arg name="studentLeaveManagementManager" ref="studentLeaveManagementManager" />
		<constructor-arg name="userManager" ref="userManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
	</bean>

	<bean id="staffLeaveReviewPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.StaffLeaveReviewPushNotificationContentBuilder">
	</bean>

	<bean id="staffLeaveReviewPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.StaffLeaveReviewPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="staffLeaveReviewPushNotificationContentBuilder" ref="staffLeaveReviewPushNotificationContentBuilder" />
		<constructor-arg name="staffLeaveManagementManager" ref="staffLeaveManagementManager" />
		<constructor-arg name="userManager" ref="userManager" />
	</bean>

	<bean id="staffAttendancePushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.StaffAttendancePushNotificationContentBuilder">
	</bean>

	<bean id="staffAttendancePushNotificationContentBuilderV2"
		  class="com.embrate.cloud.push.notifications.content.builder.StaffAttendancePushNotificationContentBuilderV2">
	</bean>

	<bean id="staffAttendancePushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.StaffAttendancePushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="staffAttendancePushNotificationContentBuilder" ref="staffAttendancePushNotificationContentBuilder" />
		<constructor-arg name="userManager" ref="userManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="staffAttendanceManager" ref="staffAttendanceManager" />
	</bean>

	<bean id="staffAttendancePushNotificationHandlerV2"
		  class="com.embrate.cloud.push.notifications.handler.StaffAttendancePushNotificationHandlerV2">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="userManager" ref="userManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="staffManager" ref="staffManager" />
		<constructor-arg name="staffAttendancePushNotificationContentBuilderV2" ref="staffAttendancePushNotificationContentBuilderV2" />
	</bean>

	<bean id="staffAttendanceStatusPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.StaffAttendanceStatusPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="staffAttendancePushNotificationContentBuilder" ref="staffAttendancePushNotificationContentBuilder" />
		<constructor-arg name="userManager" ref="userManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
		<constructor-arg name="staffAttendanceManager" ref="staffAttendanceManager" />
	</bean>

	<bean id="studentComplaintResponsePushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.StudentComplaintResponsePushNotificationContentBuilder">
	</bean>

	<bean id="studentComplaintResponsePushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.StudentComplaintResponsePushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="studentComplaintResponsePushNotificationContentBuilder" ref="studentComplaintResponsePushNotificationContentBuilder" />
		<constructor-arg name="complainBoxManager" ref="complainBoxManager" />
		<constructor-arg name="userManager" ref="userManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
	</bean>

	<bean id="studentReportCardPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.StudentReportCardPushNotificationContentBuilder">
	</bean>

	<bean id="studentReportCardPublishPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.StudentReportCardPublishPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager" />
		<constructor-arg name="studentReportCardPushNotificationContentBuilder" ref="studentReportCardPushNotificationContentBuilder" />
		<constructor-arg name="userManager" ref="userManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="userPermissionManager" ref="userPermissionManager" />
	</bean>

	<bean id="appointmentDetailsAddPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.AppointmentDetailsAddPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager"/>
		<constructor-arg name="userManager" ref="userManager"/>
		<constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
		<constructor-arg name="appointmentDetailsManager" ref="appointmentDetailsManager"/>
		<constructor-arg name="appointmentDetailsAddPushNotificationContentBuilder"
						 ref="appointmentDetailsAddPushNotificationContentBuilder"/>
	</bean>

	<bean id="visitorDetailsAddPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.VisitorDetailsAddPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager"/>
		<constructor-arg name="userManager" ref="userManager"/>
		<constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
		<constructor-arg name="visitorDetailsManager" ref="visitorDetailsManager"/>
		<constructor-arg name="visitorDetailsAddPushNotificationContentBuilder"
						 ref="visitorDetailsAddPushNotificationContentBuilder"/>
	</bean>

	<bean id="visitorStatusUpdatePushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.VisitorStatusUpdatePushNotificationHandler">
		<constructor-arg name="userManager" ref="userManager"/>
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager"/>
		<constructor-arg name="visitorDetailsManager" ref="visitorDetailsManager"/>
		<constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
		<constructor-arg name="visitorStatusUpdatePushNotificationContentBuilder"
						 ref="visitorStatusUpdatePushNotificationContentBuilder"/>
	</bean>

	<bean id="visitorStatusUpdatePushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.VisitorStatusUpdatePushNotificationContentBuilder">
	</bean>

	<bean id="visitorDetailsAddPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.VisitorDetailsAddPushNotificationContentBuilder">
	</bean>

	<bean id="studentLeaveApplicationPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.StudentLeaveApplicationPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager"/>
		<constructor-arg name="userManager" ref="userManager"/>
		<constructor-arg name="studentLeaveManagementManager" ref="studentLeaveManagementManager"/>
		<constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
		<constructor-arg name="instituteManagementManager" ref="instituteManagementManager"/>
		<constructor-arg name="studentLeaveApplicationPushNotificationContentBuilder"
						 ref="studentLeaveApplicationPushNotificationContentBuilder"/>
	</bean>
	<bean id="staffLeaveApplicationPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.StaffLeaveApplicationPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager"/>
		<constructor-arg name="staffLeaveManagementManager" ref="staffLeaveManagementManager"/>
		<constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
		<constructor-arg name="staffLeaveApplicationPushNotificationContentBuilder"
						 ref="staffLeaveApplicationPushNotificationContentBuilder"/>
	</bean>

	<bean id="appointmentStatusUpdatePushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.AppointmentStatusUpdatePushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager"/>
		<constructor-arg name="userManager" ref="userManager"/>
		<constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
		<constructor-arg name="appointmentDetailsManager" ref="appointmentDetailsManager"/>
		<constructor-arg name="appointmentStatusUpdatePushNotificationContentBuilder"
						 ref="appointmentStatusUpdatePushNotificationContentBuilder"/>
	</bean>

	<bean id="vehicleDocumentRenewalReminderPushNotificationHandler"
		  class="com.embrate.cloud.push.notifications.handler.VehicleDocumentRenewalReminderPushNotificationHandler">
		<constructor-arg name="pushNotificationManager" ref="pushNotificationManager"/>
		<constructor-arg name="vehicleDocumentRenewalReminderPushNotificationContentBuilder" ref="vehicleDocumentRenewalReminderPushNotificationContentBuilder"/>
		<constructor-arg name="userPermissionManager" ref="userPermissionManager"/>
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="transportConfigurationManager" ref="transportConfigurationManager"/>
		<constructor-arg name="instituteManager" ref="instituteManager"/>
	</bean>

	<bean id="appointmentStatusUpdatePushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.AppointmentStatusUpdatePushNotificationContentBuilder">
	</bean>

	<bean id="appointmentDetailsAddPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.AppointmentDetailsAddPushNotificationContentBuilder">
	</bean>

	<bean id="studentLeaveApplicationPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.StudentLeaveApplicationPushNotificationContentBuilder">
	</bean>

	<bean id="staffLeaveApplicationPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.StaffLeaveApplicationPushNotificationContentBuilder">
	</bean>

	<bean id="vehicleDocumentRenewalReminderPushNotificationContentBuilder"
		  class="com.embrate.cloud.push.notifications.content.builder.VehicleDocumentRenewalReminderPushNotificationContentBuilder">
	</bean>

	<context:component-scan
		base-package="com.embrate.cloud.push.notifications" />
</beans>
