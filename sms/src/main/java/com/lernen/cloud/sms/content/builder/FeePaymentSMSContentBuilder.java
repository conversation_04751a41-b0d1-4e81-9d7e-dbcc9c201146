package com.lernen.cloud.sms.content.builder;

import com.embrate.cloud.core.lib.templates.notification.NotificationTemplateManager;
import com.lernen.cloud.core.api.common.NameFormat;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.fees.payment.FeePaymentInvoiceSummary;
import com.lernen.cloud.core.api.sms.SMSContentPayload;
import com.lernen.cloud.core.api.sms.SMSPreferences;
import com.lernen.cloud.core.api.sms.SMSTemplateContent;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.NameFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;

import java.io.StringWriter;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeePaymentSMSContentBuilder extends SMSContentBuilder {
	private static final Logger logger = LogManager.getLogger(FeePaymentSMSContentBuilder.class);

	private static final String STUDENT_NAME_KEY = "student_name";
	private static final String CLASS_NAME_KEY = "class_name";
	private static final String INVOICE_ID_KEY = "invoice_id";
	private static final String COLLECTED_BY_KEY = "collected_by";
	private static final String CANCELLED_BY_KEY = "cancelled_by";
	private static final String TOTAL_FEE_AMOUNT_KEY = "total_fee_amount";
	private static final String COLLECTED_AMOUNT_KEY = "collected_amount";
	private static final String WALLET_DEBIT_AMOUNT_KEY = "wallet_debit_amount";
	private static final String PAYMENT_DATE_KEY = "payment_date";
	private static final String INSTITUTE_NAME_KEY = "institute_name";
	private static final String ADMISSION_NUMBER = "admission_number";
	private static final String CANCELLED_ON_KEY = "cancelled_on";

	public FeePaymentSMSContentBuilder(NotificationTemplateManager notificationTemplateManager) {
		super(notificationTemplateManager);
	}

	public SMSContentPayload generateFeePaymentTextContent(FeePaymentInvoiceSummary feePaymentInvoiceSummary,
			SMSPreferences smsPreferences, String instituteName, boolean useTemplate) {
		if (feePaymentInvoiceSummary == null) {
			logger.error("Invalid transaction details");
			throw new EmbrateRunTimeException("Invalid transaction details");
		}
		if (StringUtils.isBlank(instituteName)) {
			logger.error("Invalid institute name for institute {} ", feePaymentInvoiceSummary.getStudent()
					.getStudentAcademicSessionInfoResponse().getStandard().getInstituteId());
			throw new EmbrateRunTimeException("Invalid institute name for configured for sms");
		}

		String feePaymentSMSText = null;
		String dltTemplateId = null;
		if(useTemplate){
			UUID templateId = smsPreferences.getFeePaymentSMSTemplateId();
			SMSTemplateContent smsTemplateContentPayload = getSMSTemplateContent(templateId);
			if(smsTemplateContentPayload != null){
				feePaymentSMSText = smsTemplateContentPayload.getTemplate();
				dltTemplateId = smsTemplateContentPayload.getDltTemplateId();
			}
		} else{
			feePaymentSMSText = smsPreferences.getFeePaymentSMSText();
		}


		if (StringUtils.isBlank(feePaymentSMSText)) {
			logger.error("Fee payment sms text is not configured for institute {}", feePaymentInvoiceSummary
					.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getInstituteId());
			throw new EmbrateRunTimeException("Fee payment sms text is not configured for institute");
		}

		NameFormat feePaymentSMSStudentNameFormat = smsPreferences.getFeePaymentSMSStudentNameFormat();
		if (smsPreferences.getFeePaymentSMSStudentNameFormat() == null) {
			feePaymentSMSStudentNameFormat = NameFormat.FULL_NAME;
		}

		String studentName = feePaymentInvoiceSummary.getStudent().getStudentBasicInfo().getName();
		String formattedStudentName = NameFormatUtils.getFormattedName(studentName, feePaymentSMSStudentNameFormat);
		String standardWithSectionName = feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse()
				.getStandard().getDisplayNameWithSection();

		final VelocityContext context = new VelocityContext();

		context.put(STUDENT_NAME_KEY, formattedStudentName);
		context.put(CLASS_NAME_KEY, standardWithSectionName);
		context.put(INVOICE_ID_KEY, feePaymentInvoiceSummary.getFeePaymentTransactionDetails()
				.getFeePaymentTransactionMetaData().getInvoiceId());
		context.put(COLLECTED_BY_KEY, feePaymentInvoiceSummary.getFeePaymentTransactionDetails()
				.getFeePaymentTransactionMetaData().getTransactionAddedByUser().getFirstName());
		context.put(COLLECTED_AMOUNT_KEY,
				Math.round(feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getTotalPaidAmount()));
		context.put(WALLET_DEBIT_AMOUNT_KEY, Math.round(feePaymentInvoiceSummary.getFeePaymentTransactionDetails()
				.getFeePaymentTransactionMetaData().getDebitWalletAmount()));
		context.put(TOTAL_FEE_AMOUNT_KEY,
				Math.round(feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getTotalPaidAmount()
						+ feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData()
								.getDebitWalletAmount()));
		context.put(PAYMENT_DATE_KEY, DateUtils.getFormattedDate(feePaymentInvoiceSummary
				.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getTransactionDate()));
		context.put(PAYMENT_DATE_KEY, DateUtils.getFormattedDate(feePaymentInvoiceSummary
				.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getTransactionDate()));
		context.put(INSTITUTE_NAME_KEY, instituteName);

		final StringWriter smsContent = new StringWriter();
		Velocity.evaluate(context, smsContent, feePaymentSMSText, feePaymentSMSText);
		return new SMSContentPayload(smsContent.toString(), dltTemplateId) ;

	}

	public SMSContentPayload generateFeePaymentAdminTextContent(FeePaymentInvoiceSummary feePaymentInvoiceSummary,
																			SMSPreferences smsPreferences, String instituteName) {
		if (feePaymentInvoiceSummary == null) {
			logger.error("Invalid transaction details");
			throw new EmbrateRunTimeException("Invalid transaction details");
		}
		if (StringUtils.isBlank(instituteName)) {
			logger.error("Invalid institute name for institute {} ", feePaymentInvoiceSummary.getStudent()
					.getStudentAcademicSessionInfoResponse().getStandard().getInstituteId());
			throw new EmbrateRunTimeException("Invalid institute name for configured for sms");
		}

		String feePaymentSMSText = null;
		String dltTemplateId = null;
			UUID templateId = smsPreferences.getFeePaymentAdminSMSTemplateId();
			SMSTemplateContent smsTemplateContentPayload = getSMSTemplateContent(templateId);
			if(smsTemplateContentPayload != null){
				feePaymentSMSText = smsTemplateContentPayload.getTemplate();
				dltTemplateId = smsTemplateContentPayload.getDltTemplateId();
			}

		if (StringUtils.isBlank(feePaymentSMSText)) {
			logger.error("Fee payment sms text is not configured for institute {}", feePaymentInvoiceSummary
					.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getInstituteId());
			throw new EmbrateRunTimeException("Fee payment sms text is not configured for institute");
		}

		NameFormat feePaymentSMSStudentNameFormat = smsPreferences.getFeePaymentSMSStudentNameFormat();
		if (smsPreferences.getFeePaymentSMSStudentNameFormat() == null) {
			feePaymentSMSStudentNameFormat = NameFormat.FULL_NAME;
		}

		String studentName = feePaymentInvoiceSummary.getStudent().getStudentBasicInfo().getName();
		String formattedStudentName = NameFormatUtils.getFormattedName(studentName, feePaymentSMSStudentNameFormat);
		String standardWithSectionName = feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse()
				.getStandard().getDisplayNameWithSection();
		String admissionNumber = feePaymentInvoiceSummary.getStudent().getStudentBasicInfo().getAdmissionNumber();

		final VelocityContext context = new VelocityContext();

		context.put(STUDENT_NAME_KEY, formattedStudentName);
		context.put(CLASS_NAME_KEY, standardWithSectionName);
		context.put(INVOICE_ID_KEY, feePaymentInvoiceSummary.getFeePaymentTransactionDetails()
				.getFeePaymentTransactionMetaData().getInvoiceId());
		context.put(COLLECTED_BY_KEY, feePaymentInvoiceSummary.getFeePaymentTransactionDetails()
				.getFeePaymentTransactionMetaData().getTransactionAddedByUser().getFirstName());
		context.put(COLLECTED_AMOUNT_KEY,
				Math.round(feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getTotalPaidAmount()));
		context.put(WALLET_DEBIT_AMOUNT_KEY, Math.round(feePaymentInvoiceSummary.getFeePaymentTransactionDetails()
				.getFeePaymentTransactionMetaData().getDebitWalletAmount()));
		context.put(TOTAL_FEE_AMOUNT_KEY,
				Math.round(feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getTotalPaidAmount()
						+ feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData()
						.getDebitWalletAmount()));
		context.put(PAYMENT_DATE_KEY, DateUtils.getFormattedDate(feePaymentInvoiceSummary
				.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getTransactionDate()));
		context.put(ADMISSION_NUMBER, admissionNumber);
		context.put(INSTITUTE_NAME_KEY, instituteName);

		final StringWriter smsContent = new StringWriter();
		Velocity.evaluate(context, smsContent, feePaymentSMSText, feePaymentSMSText);
		return new SMSContentPayload(smsContent.toString(), dltTemplateId);

	}
	public SMSContentPayload generateFeePaymentCancellationTextContent(FeePaymentInvoiceSummary feePaymentInvoiceSummary,
			SMSPreferences smsPreferences, String instituteName, boolean useTemplate) {
		if (feePaymentInvoiceSummary == null) {
			logger.error("Invalid transaction details");
			return null;
		}
		if (StringUtils.isBlank(instituteName)) {
			logger.error("Invalid institute name for institute {} ", feePaymentInvoiceSummary.getStudent()
					.getStudentAcademicSessionInfoResponse().getStandard().getInstituteId());
			return null;
		}

		String feePaymentCancellationSMSText = null;
		String dltTemplateId = null;
		if(useTemplate){
			UUID templateId = smsPreferences.getFeePaymentCancellationSMSTemplateId();
			SMSTemplateContent smsTemplateContentPayload = getSMSTemplateContent(templateId);
			if(smsTemplateContentPayload != null){
				feePaymentCancellationSMSText = smsTemplateContentPayload.getTemplate();
				dltTemplateId = smsTemplateContentPayload.getDltTemplateId();
			}
		} else{
			feePaymentCancellationSMSText = smsPreferences.getFeePaymentCancellationSMSText();
		}

		if (StringUtils.isBlank(feePaymentCancellationSMSText)) {
			logger.error("Fee payment cancellation sms text is not configured for institute {}", feePaymentInvoiceSummary
					.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getInstituteId());
			return null;
		}

		NameFormat studentNameFormat = smsPreferences.getFeePaymentCancellationSMSStudentNameFormat();
		if (smsPreferences.getFeePaymentSMSStudentNameFormat() == null) {
			studentNameFormat = NameFormat.FULL_NAME;
		}

		String studentName = feePaymentInvoiceSummary.getStudent().getStudentBasicInfo().getName();
		String formattedStudentName = NameFormatUtils.getFormattedName(studentName, studentNameFormat);
		String standardWithSectionName = feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse()
				.getStandard().getDisplayNameWithSection();

		final VelocityContext context = new VelocityContext();

		context.put(STUDENT_NAME_KEY, formattedStudentName);
		context.put(CLASS_NAME_KEY, standardWithSectionName);
		context.put(INVOICE_ID_KEY, feePaymentInvoiceSummary.getFeePaymentTransactionDetails()
				.getFeePaymentTransactionMetaData().getInvoiceId());
		context.put(COLLECTED_BY_KEY, feePaymentInvoiceSummary.getFeePaymentTransactionDetails()
				.getFeePaymentTransactionMetaData().getTransactionAddedByUser().getFirstName());
		context.put(CANCELLED_BY_KEY, feePaymentInvoiceSummary.getFeePaymentTransactionDetails()
				.getFeePaymentTransactionMetaData().getTransactionUpdatedByUser().getFirstName());
		context.put(COLLECTED_AMOUNT_KEY,
				Math.round(feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getTotalPaidAmount()));
		context.put(WALLET_DEBIT_AMOUNT_KEY, Math.round(feePaymentInvoiceSummary.getFeePaymentTransactionDetails()
				.getFeePaymentTransactionMetaData().getDebitWalletAmount()));
		context.put(TOTAL_FEE_AMOUNT_KEY,
				Math.round(feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getTotalPaidAmount()
						+ feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData()
								.getDebitWalletAmount()));
//		context.put(PAYMENT_DATE_KEY, DateUtils.getFormattedDate(feePaymentInvoiceSummary
//				.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getTransactionDate()));
//		context.put(PAYMENT_DATE_KEY, DateUtils.getFormattedDate(feePaymentInvoiceSummary
//				.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getTransactionDate()));
		context.put(PAYMENT_DATE_KEY, DateUtils.getFormattedDate(feePaymentInvoiceSummary
						.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getUpdatedAt()));
		context.put(INSTITUTE_NAME_KEY, instituteName);

		final StringWriter smsContent = new StringWriter();
		Velocity.evaluate(context, smsContent, feePaymentCancellationSMSText, feePaymentCancellationSMSText);
		return new SMSContentPayload(smsContent.toString(), dltTemplateId) ;

	}

	public SMSContentPayload generateFeePaymentCancellationAdminTextContent(FeePaymentInvoiceSummary feePaymentInvoiceSummary,
						SMSPreferences smsPreferences, String instituteName) {
		if (feePaymentInvoiceSummary == null) {
			logger.error("Invalid transaction details");
			return null;
		}
		if (StringUtils.isBlank(instituteName)) {
			logger.error("Invalid institute name for institute {} ", feePaymentInvoiceSummary.getStudent()
					.getStudentAcademicSessionInfoResponse().getStandard().getInstituteId());
			return null;
		}

		String feePaymentCancellationAdminSMSText = null;
		String dltTemplateId = null;
		UUID templateId = smsPreferences.getFeePaymentCancellationAdminSMSTemplateId();
		SMSTemplateContent smsTemplateContentPayload = getSMSTemplateContent(templateId);
		if(smsTemplateContentPayload != null){
			feePaymentCancellationAdminSMSText = smsTemplateContentPayload.getTemplate();
			dltTemplateId = smsTemplateContentPayload.getDltTemplateId();
		}

		if (StringUtils.isBlank(feePaymentCancellationAdminSMSText)) {
			logger.error("Fee payment cancellation admin sms text is not configured for institute {}", feePaymentInvoiceSummary
					.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getInstituteId());
			return null;
		}

		NameFormat studentNameFormat = NameFormat.FULL_NAME;

		String studentName = feePaymentInvoiceSummary.getStudent().getStudentBasicInfo().getName();
		String formattedStudentName = NameFormatUtils.getFormattedName(studentName, studentNameFormat);
		String admissionNumber = feePaymentInvoiceSummary.getStudent().getStudentBasicInfo().getAdmissionNumber();

		final VelocityContext context = new VelocityContext();

		context.put(INVOICE_ID_KEY, feePaymentInvoiceSummary.getFeePaymentTransactionDetails()
				.getFeePaymentTransactionMetaData().getInvoiceId());
		context.put(TOTAL_FEE_AMOUNT_KEY,
				Math.round(feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getTotalPaidAmount()
						+ feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData()
						.getDebitWalletAmount()));
		context.put(STUDENT_NAME_KEY, formattedStudentName);
		context.put(ADMISSION_NUMBER, admissionNumber);
		context.put(CANCELLED_BY_KEY, feePaymentInvoiceSummary.getFeePaymentTransactionDetails()
				.getFeePaymentTransactionMetaData().getTransactionUpdatedByUser().getFirstName());
		context.put(CANCELLED_ON_KEY, DateUtils.getFormattedDate(feePaymentInvoiceSummary
				.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getUpdatedAt(),
				DateUtils.DEFAULT_DATE_TIME_FORMAT, DateUtils.DEFAULT_TIMEZONE));
		context.put(INSTITUTE_NAME_KEY, instituteName);

		final StringWriter smsContent = new StringWriter();
		Velocity.evaluate(context, smsContent, feePaymentCancellationAdminSMSText, feePaymentCancellationAdminSMSText);
		return new SMSContentPayload(smsContent.toString(), dltTemplateId) ;

	}
}
