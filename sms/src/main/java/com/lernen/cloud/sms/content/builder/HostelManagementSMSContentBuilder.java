//package com.lernen.cloud.sms.content.builder;
//
//import com.embrate.cloud.core.lib.templates.notification.NotificationTemplateManager;
//import com.lernen.cloud.core.api.common.NameFormat;
//import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
//import com.lernen.cloud.core.api.sms.SMSContentPayload;
//import com.lernen.cloud.core.api.sms.SMSPreferences;
//import com.lernen.cloud.core.api.sms.SMSTemplateContent;
//import com.lernen.cloud.core.api.student.StudentLite;
//import com.lernen.cloud.core.api.user.User;
//import com.lernen.cloud.core.utils.DateUtils;
//import com.lernen.cloud.core.utils.NameFormatUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.logging.log4j.LogManager;
//import org.apache.logging.log4j.Logger;
//import org.apache.velocity.VelocityContext;
//import org.apache.velocity.app.Velocity;
//
//import java.io.StringWriter;
//import java.util.UUID;
//
//public class HostelManagementSMSContentBuilder extends SMSContentBuilder {
//
//
////    Dear Parents,
////    Kindly note that your ward ${student_name} of class ${standard_name} is departing from hostel with ${person_with} at ${current_date_with_time}.
////
////    Thanks,
////    ${institute_name}
////(EMBRTE)
//
//	private static final Logger logger = LogManager.getLogger(HostelManagementSMSContentBuilder.class);
//
//	private static final String STUDENT_NAME_KEY = "student_name";
//	private static final String STANDARD_NAME_KEY = "standard_name";
//	private static final String INSTITUTE_NAME_KEY = "institute_name";
//	private static final String DEPARTURE_WITH_NAME = "departure_with_name";
//	private static final String CURRENT_DATE_WITH_TIME = "current_date_with_time";
//
//	public HostelManagementSMSContentBuilder(NotificationTemplateManager notificationTemplateManager) {
//		super(notificationTemplateManager);
//	}
//
//	public SMSContentPayload generateEarlyDepartureTextContent(int instituteId, StudentLite studentLite, String departureWithName, Integer departureTime,
//															   SMSPreferences smsPreferences, String instituteName) {
//		if (studentLite == null) {
//			logger.error("Invalid early departure details");
//			throw new EmbrateRunTimeException("Invalid early departure details");
//		}
//		if (StringUtils.isBlank(instituteName)) {
//			logger.error("Invalid institute name for institute {} ", instituteId);
//			throw new EmbrateRunTimeException("Invalid institute name for configured for sms");
//		}
//
//		String earlyDepartureSMSText = null;
//		String dltTemplateId = null;
//		UUID templateId = smsPreferences.getEarlyDepartureSMSTemplateId();
//		SMSTemplateContent smsTemplateContentPayload = getSMSTemplateContent(templateId);
//		if(smsTemplateContentPayload != null){
//			earlyDepartureSMSText = smsTemplateContentPayload.getTemplate();
//			dltTemplateId = smsTemplateContentPayload.getDltTemplateId();
//		}
//
//		if (StringUtils.isBlank(earlyDepartureSMSText)) {
//			logger.error("Student Early Departure sms text is not configured for institute {}", instituteId);
//			throw new EmbrateRunTimeException("Student Early Departure sms text is not configured for institute");
//		}
//
//		String studentName = studentLite.getName();
//		String formattedStudentName = NameFormatUtils.getFormattedName(studentName, NameFormat.FULL_NAME);
//		String standardWithSectionName = studentLite.getStudentSessionData().getStandardNameWithSection();
//
//		String dateTimeStr = DateUtils.getFormattedDate(departureTime,
//				"dd/MMM/yyyy HH:mm", User.DFAULT_TIMEZONE);
//		final VelocityContext context = new VelocityContext();
//
//		context.put(STUDENT_NAME_KEY, formattedStudentName);
//		context.put(STANDARD_NAME_KEY, standardWithSectionName);
//		context.put(INSTITUTE_NAME_KEY, instituteName);
//		context.put(DEPARTURE_WITH_NAME, departureWithName);
//		context.put(CURRENT_DATE_WITH_TIME, dateTimeStr);
//
//		final StringWriter smsContent = new StringWriter();
//		Velocity.evaluate(context, smsContent, earlyDepartureSMSText, earlyDepartureSMSText);
//		return new SMSContentPayload(smsContent.toString(), dltTemplateId) ;
//
//	}
//}
