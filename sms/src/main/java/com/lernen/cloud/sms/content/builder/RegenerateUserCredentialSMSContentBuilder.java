package com.lernen.cloud.sms.content.builder;

import com.embrate.cloud.core.lib.templates.notification.NotificationTemplateManager;
import com.lernen.cloud.core.api.common.NameFormat;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.sms.SMSContentPayload;
import com.lernen.cloud.core.api.sms.SMSPreferences;
import com.lernen.cloud.core.api.sms.SMSTemplateContent;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.api.user.authentication.RegeneratePasswordUserData;
import com.lernen.cloud.core.utils.NameFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;

import java.io.StringWriter;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class RegenerateUserCredentialSMSContentBuilder extends SMSContentBuilder {
	private static final Logger logger = LogManager.getLogger(RegenerateUserCredentialSMSContentBuilder.class);

	private static final String INSTITUTE_NAME_KEY = "institute_name";
	private static final String USER_PASSWORD_KEY = "user_password";
	private static final String USER_ID_KEY = "user_id";
	private static final String USER_NAME_KEY = "user_name";
	private static final String USERS_NAME_KEY = "users_name";

	private static final String REGENERATE_PASSWORD_SMS_TEXT = "${institute_name}\n\nCredentials for ${user_name}:\nID: ${user_id}\nPassword: ${user_password}\nAndroid: www.embrate.com/apk\nWeb portal: www.embrate.com/core/login";

	public RegenerateUserCredentialSMSContentBuilder(NotificationTemplateManager notificationTemplateManager) {
		super(notificationTemplateManager);
	}

	public <T> SMSContentPayload generateTextContent(UserType userType, RegeneratePasswordUserData<T> regeneratePasswordUserData,
													 SMSPreferences smsPreferences, String instituteName, boolean useTemplate) {
		if (StringUtils.isBlank(instituteName)) {
			logger.error("Invalid institute name for institute {} ",
					regeneratePasswordUserData.getUser().getInstituteId());
			throw new EmbrateRunTimeException("Invalid institute name for configured for sms");
		}

		String regenerateSMSText = null;
		String dltTemplateId = null;
		if(useTemplate){
			UUID templateId = smsPreferences.getRegenerateCredSMSTemplateId();
			SMSTemplateContent smsTemplateContentPayload = getSMSTemplateContent(templateId);
			if(smsTemplateContentPayload != null){
				regenerateSMSText = smsTemplateContentPayload.getTemplate();
				dltTemplateId = smsTemplateContentPayload.getDltTemplateId();
			}
		} else{
			regenerateSMSText = REGENERATE_PASSWORD_SMS_TEXT;
		}


		final VelocityContext context = new VelocityContext();

		String userName = regeneratePasswordUserData.getUser().getFirstName();
		String userId = regeneratePasswordUserData.getUser().getUserName();
		String password = regeneratePasswordUserData.getNewPassword();

		String formattedUsersName = NameFormatUtils.getFormattedName(userName, NameFormat.FIRST_NAME_ONLY);

		if(useTemplate){
			context.put(USERS_NAME_KEY, formattedUsersName);
			context.put(USER_NAME_KEY, userId);
			context.put(USER_PASSWORD_KEY, password);
			context.put(INSTITUTE_NAME_KEY, instituteName);
		} else{
			context.put(USER_ID_KEY, userId);
			context.put(USER_PASSWORD_KEY, password);
			context.put(USER_NAME_KEY, formattedUsersName);
			context.put(INSTITUTE_NAME_KEY, instituteName);
		}

		final StringWriter smsContent = new StringWriter();
		Velocity.evaluate(context, smsContent, regenerateSMSText, regenerateSMSText);
		return new SMSContentPayload(smsContent.toString(), dltTemplateId);
	}
}
