package com.lernen.cloud.sms.content.builder;

import com.embrate.cloud.core.lib.templates.notification.NotificationTemplateManager;
import com.lernen.cloud.core.api.common.NameFormat;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.sms.SMSContentPayload;
import com.lernen.cloud.core.api.sms.SMSPreferences;
import com.lernen.cloud.core.api.sms.SMSTemplateContent;
import com.lernen.cloud.core.api.student.AdmitStudentResponse;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.authentication.RegeneratePasswordUserData;
import com.lernen.cloud.core.utils.NameFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;

import java.io.StringWriter;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class StudentSMSContentBuilder  extends SMSContentBuilder {
    private static final Logger logger = LogManager.getLogger(StudentSMSContentBuilder.class);

    private static final String STUDENT_NAME_KEY = "student_name";
    private static final String STANDARD_NAME_KEY = "standard_name";
    private static final String INSTITUTE_NAME_KEY = "institute_name";
    private static final String REGISTRATION_NUMBER_KEY = "registration_number";
    private static final String USERNAME = "username";
    private static final String PASSWORD = "password";
    private static final String FATHER_NAME = "father_name";

    public StudentSMSContentBuilder(NotificationTemplateManager notificationTemplateManager) {
        super(notificationTemplateManager);
    }

    public SMSContentPayload generateRegistrationTextContent(Student student,
                                                             SMSPreferences smsPreferences, String instituteName) {
        if (student == null) {
            logger.error("Invalid transaction details");
            throw new EmbrateRunTimeException("Invalid transaction details");
        }
        if (StringUtils.isBlank(instituteName)) {
            logger.error("Invalid institute name for institute {} ", student
                    .getStudentAcademicSessionInfoResponse().getStandard().getInstituteId());
            throw new EmbrateRunTimeException("Invalid institute name for configured for sms");
        }

        String registrationSMSText = null;
        String dltTemplateId = null;
        UUID templateId = smsPreferences.getStudentRegistrationSMSTemplateId();
        SMSTemplateContent smsTemplateContentPayload = getSMSTemplateContent(templateId);
        if(smsTemplateContentPayload != null){
            registrationSMSText = smsTemplateContentPayload.getTemplate();
            dltTemplateId = smsTemplateContentPayload.getDltTemplateId();
        }

        if (StringUtils.isBlank(registrationSMSText)) {
            logger.error("Student Registration sms text is not configured for institute {}", student
                    .getStudentAcademicSessionInfoResponse().getStandard().getInstituteId());
            throw new EmbrateRunTimeException("Student Registration sms text is not configured for institute");
        }

        String studentName = student.getStudentBasicInfo().getName();
        String formattedStudentName = NameFormatUtils.getFormattedName(studentName, NameFormat.FULL_NAME);
        String standardWithSectionName = student.getStudentAcademicSessionInfoResponse()
                .getStandard().getDisplayNameWithSection();
        String registrationNumber = student.getStudentBasicInfo().getRegistrationNumber();

        final VelocityContext context = new VelocityContext();

        context.put(STUDENT_NAME_KEY, formattedStudentName);
        context.put(STANDARD_NAME_KEY, standardWithSectionName);
        context.put(INSTITUTE_NAME_KEY, instituteName);
        context.put(REGISTRATION_NUMBER_KEY, registrationNumber);

        final StringWriter smsContent = new StringWriter();
        Velocity.evaluate(context, smsContent, registrationSMSText, registrationSMSText);
        return new SMSContentPayload(smsContent.toString(), dltTemplateId) ;

    }

    public SMSContentPayload generateAdmissionTextContent(Student student,
            SMSPreferences smsPreferences, String instituteName, RegeneratePasswordUserData regeneratePasswordUserData, AdmitStudentResponse admitStudentResponse) {

        if (student == null) {
            logger.error("Invalid transaction details");
            throw new EmbrateRunTimeException("Invalid transaction details");
        }
        if (StringUtils.isBlank(instituteName)) {
            logger.error("Invalid institute name for institute {} ", student
                    .getStudentAcademicSessionInfoResponse().getStandard().getInstituteId());
            throw new EmbrateRunTimeException("Invalid institute name for configured for sms");
        }

        String admissionSMSText = null;
        String dltTemplateId = null;
        UUID templateId = null;
        if(admitStudentResponse.getStudentUserCreated() != null && admitStudentResponse.getStudentUserCreated()) {
            templateId = smsPreferences.getStudentAdmissionWithCredentialsSMSTemplateId();
        } else {
            templateId = smsPreferences.getStudentAdmissionWithoutCredentialsSMSTemplateId();
        }
        if(templateId == null) {
            logger.error("Student Admission sms template is not configured for institute {}", student
                    .getStudentAcademicSessionInfoResponse().getStandard().getInstituteId());
            throw new EmbrateRunTimeException("Student Admission sms template is not configured for institute");
        }

        SMSTemplateContent smsTemplateContentPayload = getSMSTemplateContent(templateId);
        if(smsTemplateContentPayload != null){
            admissionSMSText = smsTemplateContentPayload.getTemplate();
            dltTemplateId = smsTemplateContentPayload.getDltTemplateId();
        }

        if (StringUtils.isBlank(admissionSMSText)) {
            logger.error("Student Admission sms text is not configured for institute {}", student
                    .getStudentAcademicSessionInfoResponse().getStandard().getInstituteId());
            throw new EmbrateRunTimeException("Student Admission sms text is not configured for institute");
        }

        String studentName = student.getStudentBasicInfo().getName();
        String formattedStudentName = NameFormatUtils.getFormattedName(studentName, NameFormat.FULL_NAME);
        String standardWithSectionName = student.getStudentAcademicSessionInfoResponse()
                .getStandard().getDisplayNameWithSection();

        final VelocityContext context = new VelocityContext();

        context.put(STUDENT_NAME_KEY, formattedStudentName);
        context.put(STANDARD_NAME_KEY, standardWithSectionName);
        context.put(INSTITUTE_NAME_KEY, instituteName);

        if(admissionSMSText.contains(FATHER_NAME)) {
            context.put(FATHER_NAME, student.getStudentFamilyInfo() == null ? "" : StringUtils.isEmpty(student.getStudentFamilyInfo().getFathersName()) ? "" :
                    student.getStudentFamilyInfo().getFathersName());
        }

        if (admitStudentResponse.getStudentUserCreated() != null &&
                admitStudentResponse.getStudentUserCreated() && regeneratePasswordUserData != null) {
            context.put(USERNAME, regeneratePasswordUserData.getUser().getUserName());
            context.put(PASSWORD, regeneratePasswordUserData.getNewPassword());
        }

        final StringWriter smsContent = new StringWriter();
        Velocity.evaluate(context, smsContent, admissionSMSText, admissionSMSText);
        return new SMSContentPayload(smsContent.toString(), dltTemplateId) ;

    }
}
