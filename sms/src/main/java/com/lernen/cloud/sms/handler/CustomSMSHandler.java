package com.lernen.cloud.sms.handler;

import com.embrate.cloud.core.api.service.communication.CommunicationServiceTransactionType;
import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.notification.NotificationType;
import com.lernen.cloud.core.api.sms.CustomNotificationPayload;
import com.lernen.cloud.core.api.sms.SMSContentPayload;
import com.lernen.cloud.core.api.sms.SMSPayloadWrapper;
import com.lernen.cloud.core.api.sms.SMSResponse;
import com.lernen.cloud.core.api.sms.msg91.UserSMSPayload;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.sms.content.builder.CustomSMSContentBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 */
public class CustomSMSHandler {
    private static final Logger logger = LogManager.getLogger(CustomSMSHandler.class);

    private final SMSManager smsManager;
    private final StudentManager studentManager;
    private final StaffManager staffManager;
    private final InstituteManager instituteManager;
    private final UserPreferenceSettings userPreferenceSettings;
    private final CustomSMSContentBuilder customSMSContentBuilder;

    public CustomSMSHandler(SMSManager smsManager, StudentManager studentManager, StaffManager staffManager,
                            InstituteManager instituteManager, UserPreferenceSettings userPreferenceSettings, CustomSMSContentBuilder customSMSContentBuilder) {
        this.smsManager = smsManager;
        this.studentManager = studentManager;
        this.staffManager = staffManager;
        this.instituteManager = instituteManager;
        this.userPreferenceSettings = userPreferenceSettings;
        this.customSMSContentBuilder = customSMSContentBuilder;
    }

    public SMSResponse bulkSend(CustomNotificationPayload customNotificationPayload, UUID userId) {
        final MetaDataPreferences metaDataPreferences = userPreferenceSettings
                .getMetaDataPreferences(customNotificationPayload.getInstituteId());

        if (!metaDataPreferences.isSmsServiceEnabled()) {
            logger.info("SMS service is not enabled for institute {}", customNotificationPayload.getInstituteId());
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "SMS service is not enabled for your institute. Please contact support team for enabling this feature."));
        }

        if (StringUtils.isBlank(metaDataPreferences.getInstituteNameInSMS())) {
            logger.info("Institute name not configured for SMS service for institute {}", customNotificationPayload.getInstituteId());
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Institute name is not configured for sending SMS. Please contact support team."));
        }
        if (!validateNotificationPayload(customNotificationPayload)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid sms request"));
        }

        List<SMSUserMetadata> smsUserMetadataList = getSMSUserMetadata(customNotificationPayload);

        if (CollectionUtils.isEmpty(smsUserMetadataList)) {
            logger.info("No valid user present to send sms for institute {}",
                    customNotificationPayload.getInstituteId());
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No valid user to send sms"));
        }

        UUID batchId = UUID.randomUUID();

        ISMSPayloadBuilder smsPayloadBuilder = null;
        if (customNotificationPayload.getSmsTemplateId() == null) {
            smsPayloadBuilder = getSMSPayloadBuilder(customNotificationPayload.getInstituteId(),
                    customNotificationPayload.getAcademicSessionId(), smsUserMetadataList,
                    customNotificationPayload.getUserType(), batchId, customNotificationPayload.getBatchName(),
                    customNotificationPayload.getMessage());
        } else {
            smsPayloadBuilder = getTemplateBasedSMSPayloadBuilder(customNotificationPayload.getInstituteId(),
                    customNotificationPayload.getAcademicSessionId(), smsUserMetadataList,
                    customNotificationPayload.getUserType(), batchId, customNotificationPayload.getBatchName(),
                    customNotificationPayload.getSmsTemplateId(), customNotificationPayload.getCustomVariables(),
                    metaDataPreferences.getInstituteNameInSMS());
        }
        SMSResponse smsResponse = smsManager.sendSMSAsync(customNotificationPayload.getInstituteId(), smsPayloadBuilder,
                userId);

        if (!smsResponse.isSuccess()) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, smsResponse.getFailureReason()));
        }

        return smsResponse;
    }

    private List<SMSUserMetadata> getSMSUserMetadata(CustomNotificationPayload customNotificationPayload) {
        List<SMSUserMetadata> smsUserMetadataList = new ArrayList<>();
        switch (customNotificationPayload.getUserType()) {
            case STUDENT:
                Set<StudentStatus> studentStatusSet = new HashSet<>();
                studentStatusSet.add(StudentStatus.ENROLMENT_PENDING);
                studentStatusSet.add(StudentStatus.ENROLLED);
                studentStatusSet.add(StudentStatus.NSO);
                studentStatusSet.add(StudentStatus.RELIEVED);
                List<Student> students = studentManager.getStudentByAcademicSessionStudentIds(
                        customNotificationPayload.getInstituteId(), customNotificationPayload.getAcademicSessionId(),
                        new ArrayList<>(customNotificationPayload.getUserIds()), studentStatusSet);

                if (CollectionUtils.isEmpty(students)) {
                    logger.info("No student present to send sms for institute {}",
                            customNotificationPayload.getInstituteId());
                    throw new ApplicationException(
                            new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No student present to send sms"));
                }
                for (Student student : students) {
                    smsUserMetadataList.add(new SMSUserMetadata(student.getStudentId(),
                            UserType.STUDENT, new StudentUserData(student.getStudentBasicInfo().getAdmissionNumber(),
                            student.getStudentBasicInfo().getName(), student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection()),
                            student.getStudentBasicInfo().getPrimaryContactNumber()));
                }
                break;
            case STAFF:
                List<Staff> staffs = staffManager.getStaff(customNotificationPayload.getInstituteId());
                if (CollectionUtils.isEmpty(staffs)) {
                    logger.info("No staff present to send sms for institute {}",
                            customNotificationPayload.getInstituteId());
                    throw new ApplicationException(
                            new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No staff present to send sms"));
                }

                for (Staff staff : staffs) {
                    if (customNotificationPayload.getUserIds().contains(staff.getStaffId())) {
                        smsUserMetadataList.add(new SMSUserMetadata(staff.getStaffId(), UserType.STAFF, null,
                                staff.getStaffBasicInfo().getPrimaryContactNumber()));
                    }
                }
                break;
            default:
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "User type not supported to send sms"));
        }
        return smsUserMetadataList;
    }

    private boolean validateNotificationPayload(CustomNotificationPayload customNotificationPayload) {
        if (customNotificationPayload == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid notification payload"));
        }
        if (customNotificationPayload.getInstituteId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute"));
        }

        if (customNotificationPayload.getAcademicSessionId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session"));
        }
        if (customNotificationPayload.getDeliveryMode() != DeliveryMode.SMS) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid delivery mode. Only SMS is supported."));
        }
        if (StringUtils.isBlank(customNotificationPayload.getBatchName())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Batch name is must"));
        }

        if (customNotificationPayload.getSmsTemplateId() == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid sms template"));
        }
        if (customNotificationPayload.getUserType() == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user type"));
        }
        if (MapUtils.isNotEmpty(customNotificationPayload.getCustomVariables())) {
            for (Map.Entry<String, String> entry : customNotificationPayload.getCustomVariables().entrySet()) {
                String variableValue = entry.getValue();
//                if (StringUtils.isBlank(variableValue) || variableValue.length() > 30) {
                //TODO: add check for case where variable value is null for custom variables
                if (!StringUtils.isBlank(variableValue) && variableValue.length() > 30) {
                    logger.error("Custom variable {} is invalid or exceeds 30 char limit", variableValue);
                    throw new ApplicationException(
                            new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Custom variable is invalid or exceeds 30 char limit"));
                }
            }
        }
        AcademicSession academicSession = instituteManager
                .getAcademicSessionByAcademicSessionId(customNotificationPayload.getAcademicSessionId());
        if (academicSession == null || academicSession.getInstituteId() != customNotificationPayload.getInstituteId()) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session"));
        }
        return true;
    }

    private ISMSPayloadBuilder getSMSPayloadBuilder(int instituteId, int academicSessionId,
                                                    List<SMSUserMetadata> smsUserMetadataList, UserType userType, UUID batchId, String batchName,
                                                    String message) {
        return new ISMSPayloadBuilder() {

            @Override
            public boolean previewPayloadIsFinal() {
                return true;
            }

            @Override
            public SMSPayloadWrapper getSMSPreviewPayload() {
                return getSMSPayloadWrapper(instituteId, academicSessionId, smsUserMetadataList, userType, batchId,
                        batchName, message);
            }

            @Override
            public List<UserSMSPayload> getFinalUserSMSPayload() {
                return null;
            }

            @Override
            public boolean executeSMSAction() {
                return true;
            }
        };
    }

    private ISMSPayloadBuilder getTemplateBasedSMSPayloadBuilder(int instituteId, int academicSessionId,
                                                                 List<SMSUserMetadata> smsUserMetadataList, UserType userType, UUID batchId, String batchName,
                                                                 UUID templateId, Map<String, String> customVariables,
                                                                 String instituteName) {
        return new ISMSPayloadBuilder() {

            @Override
            public boolean previewPayloadIsFinal() {
                return true;
            }

            @Override
            public SMSPayloadWrapper getSMSPreviewPayload() {
                return getTemplateBasedSMSPayloadWrapper(instituteId, academicSessionId, smsUserMetadataList, userType, batchId,
                        batchName, templateId, customVariables, instituteName);
            }

            @Override
            public List<UserSMSPayload> getFinalUserSMSPayload() {
                return null;
            }

            @Override
            public boolean executeSMSAction() {
                return true;
            }
        };
    }

    private SMSPayloadWrapper getSMSPayloadWrapper(int instituteId, int academicSessionId,
                                                   List<SMSUserMetadata> smsUserMetadataList, UserType userType, UUID batchId, String batchName,
                                                   String message) {
        if (userType == null || StringUtils.isBlank(message) || CollectionUtils.isEmpty(smsUserMetadataList)) {
            logger.error("Invalid meta data or message");
            return SMSPayloadWrapper.failurePayload("Invalid meta data or message");
        }

        List<UserSMSPayload> userSMSPayloadList = new ArrayList<UserSMSPayload>();

        for (SMSUserMetadata smsUserMetadata : smsUserMetadataList) {
            Map<String, Object> metaData = new HashMap<>();
            userSMSPayloadList.add(new UserSMSPayload(smsUserMetadata.getUserId(),
                    Arrays.asList(smsUserMetadata.getPrimaryContactNumber()), message, metaData, null, null));
        }

        return new SMSPayloadWrapper(academicSessionId, userType, NotificationType.CUSTOM, CommunicationServiceTransactionType.CUSTOM,
                userSMSPayloadList, batchId, batchName);
    }

    private SMSPayloadWrapper getTemplateBasedSMSPayloadWrapper(int instituteId, int academicSessionId,
                    List<SMSUserMetadata> smsUserMetadataList, UserType userType, UUID batchId, String batchName,
                    UUID templateId, Map<String, String> customVariables, String instituteName) {
        if (userType == null || templateId == null || CollectionUtils.isEmpty(smsUserMetadataList)) {
            logger.error("Invalid meta data or templateId");
            return SMSPayloadWrapper.failurePayload("Invalid meta data or templateId");
        }

        List<UserSMSPayload> userSMSPayloadList = new ArrayList<UserSMSPayload>();

        for (SMSUserMetadata smsUserMetadata : smsUserMetadataList) {
            Map<String, Object> metaData = new HashMap<>();
            SMSContentPayload smsContentPayload = customSMSContentBuilder.generateTextContent(templateId, customVariables,
                    smsUserMetadata, instituteName);
            userSMSPayloadList.add(new UserSMSPayload(smsUserMetadata.getUserId(),
                    Arrays.asList(smsUserMetadata.getPrimaryContactNumber()), smsContentPayload.getContent(), metaData, null, smsContentPayload.getDltTemplateId()));
        }

        return new SMSPayloadWrapper(academicSessionId, userType, NotificationType.CUSTOM, CommunicationServiceTransactionType.CUSTOM,
                userSMSPayloadList, batchId, batchName);
    }

    public class SMSUserMetadata {

        private final UUID userId;

        private final UserType userType;

        private final StudentUserData studentUserData;

        private final String primaryContactNumber;

        public SMSUserMetadata(UUID userId, UserType userType, StudentUserData studentUserData, String primaryContactNumber) {
            this.userId = userId;
            this.userType = userType;
            this.studentUserData = studentUserData;
            this.primaryContactNumber = primaryContactNumber;
        }

        public UUID getUserId() {
            return userId;
        }

        public UserType getUserType() {
            return userType;
        }

        public StudentUserData getStudentUserData() {
            return studentUserData;
        }

        public String getPrimaryContactNumber() {
            return primaryContactNumber;
        }

    }

    public class StudentUserData {

        private final String admissionNumber;

        private final String studentName;

        private final String standardName;

        public StudentUserData(String admissionNumber, String studentName, String standardName) {
            this.admissionNumber = admissionNumber;
            this.studentName = studentName;
            this.standardName = standardName;
        }

        public String getAdmissionNumber() {
            return admissionNumber;
        }

        public String getStudentName() {
            return studentName;
        }

        public String getStandardName() {
            return standardName;
        }
    }

}
