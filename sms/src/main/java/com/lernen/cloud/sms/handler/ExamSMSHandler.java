package com.lernen.cloud.sms.handler;

import com.amazonaws.util.CollectionUtils;
import com.embrate.cloud.core.api.service.communication.CommunicationServiceTransactionType;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.examination.ExamDimensionObtainedValues;
import com.lernen.cloud.core.api.examination.ExamEvaluationType;
import com.lernen.cloud.core.api.examination.StudentExamMarksDetails;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.notification.NotificationType;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.sms.*;
import com.lernen.cloud.core.api.sms.msg91.UserSMSPayload;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.examination.ExaminationManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.sms.content.builder.ExamMarksObtainedSMSContentBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

public class ExamSMSHandler {

    private static final Logger logger = LogManager.getLogger(ExamSMSHandler.class);
    private static final String EXAM_MARKS_DETAILS_KEY = "exam_marks_details";
    private static final Gson GSON = new Gson();
    private final ExamMarksObtainedSMSContentBuilder examMarksObtainedSMSContentBuilder;
    private final SMSManager smsManager;
    private final UserPreferenceSettings userPreferenceSettings;
    private final UserPermissionManager userPermissionManager;
    private final ExaminationManager examinationManager;

    public ExamSMSHandler(ExamMarksObtainedSMSContentBuilder examMarksObtainedSMSContentBuilder, SMSManager smsManager,
                          UserPreferenceSettings userPreferenceSettings, UserPermissionManager userPermissionManager,
                          ExaminationManager examinationManager) {
        this.examMarksObtainedSMSContentBuilder = examMarksObtainedSMSContentBuilder;
        this.smsManager = smsManager;
        this.userPreferenceSettings = userPreferenceSettings;
        this.userPermissionManager = userPermissionManager;
        this.examinationManager = examinationManager;
    }

    public SMSResponse bulkSend(int instituteId, CustomNotificationPayload customNotificationPayload, boolean sendToAbsentees, UUID examId,
                                UUID courseId, Integer sectionId, UUID userId) {

        if (instituteId <= 0) {
            logger.error("invalid institute id.");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
        }

        if (userId == null) {
            logger.error("invalid userId");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user Id"));
        }

        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.SEND_EXAMINATION_SMS);

        final MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);
        if (!metaDataPreferences.isSmsServiceEnabled()) {
            logger.info("SMS service is not enabled for institute {}", instituteId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "SMS service is not enabled for your institute. Please contact support team for enabling this feature."));
        }

        if (StringUtils.isBlank(metaDataPreferences.getInstituteNameInSMS())) {
            logger.info("Institute name not configured for SMS service for institute {}", instituteId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Institute name is not configured for sending SMS. Please contact support team."));
        }

        if (examId == null) {
            logger.error("invalid exam id");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid exam id."));
        }

        if (courseId == null) {
            logger.error("invalid course id");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid course id."));
        }

        if (customNotificationPayload == null) {
            logger.error("invalid customNotificationPayload");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid customNotificationPayload"));
        }

        if (CollectionUtils.isNullOrEmpty(customNotificationPayload.getUserIds())) {
            logger.error("invalid user list");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user list."));
        }

        Map<UUID, StudentExamMarksDetails> studentExamMarksDetailsMap = getStudentExamMarksDetailsMap(
                examinationManager.getClassMarksByCourseId(instituteId, examId, courseId, sectionId));
        Map<UUID, StudentExamMarksDetails> sendObtainedMarksSMS = new HashMap<UUID, StudentExamMarksDetails>();
        Map<UUID, StudentExamMarksDetails> sendAbsenteeSMS = new HashMap<UUID, StudentExamMarksDetails>();
        for(UUID uuid : customNotificationPayload.getUserIds()) {
            StudentExamMarksDetails studentExamMarksDetails = studentExamMarksDetailsMap.get(uuid);
            for(ExamDimensionObtainedValues examDimensionObtainedValues :
                    studentExamMarksDetails.getExamCoursesAllDimensionsMarks().get(0).getExamDimensionObtainedValues()) {
                if(!examDimensionObtainedValues.getExamDimension().isTotal()) {
                    continue;
                }
                if(examDimensionObtainedValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.GRADE) {
                    if(examDimensionObtainedValues.getObtainedGrade() != null && examDimensionObtainedValues.getObtainedGrade().getGradeId() != null &&
                            examDimensionObtainedValues.getObtainedGrade().getGradeId() > 0) {
                        sendObtainedMarksSMS.put(uuid, studentExamMarksDetails);
                    } else {
                        sendAbsenteeSMS.put(uuid, studentExamMarksDetails);
                    }
                } else {
                    if(examDimensionObtainedValues.getObtainedMarks() != null) {
                        sendObtainedMarksSMS.put(uuid, studentExamMarksDetails);
                    } else {
                        sendAbsenteeSMS.put(uuid, studentExamMarksDetails);
                    }
                }
            }
        }
        return bulkSend(instituteId, sendObtainedMarksSMS, sendAbsenteeSMS, customNotificationPayload, userId, sendToAbsentees);
    }

    private Map<UUID, StudentExamMarksDetails> getStudentExamMarksDetailsMap(List<StudentExamMarksDetails> studentExamMarksDetailsList) {
        Map<UUID, StudentExamMarksDetails> studentExamMarksDetailsMap = new HashMap<UUID, StudentExamMarksDetails>();
        for(StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList) {
            if(!studentExamMarksDetailsMap.containsKey(studentExamMarksDetails.getStudent().getStudentId())) {
                studentExamMarksDetailsMap.put(studentExamMarksDetails.getStudent().getStudentId(), studentExamMarksDetails);
            }
        }
        return studentExamMarksDetailsMap;
    }

    public SMSResponse bulkSend(int instituteId, Map<UUID, StudentExamMarksDetails> sendObtainedMarksSMS,
                                Map<UUID, StudentExamMarksDetails> sendAbsenteeSMS,
                                CustomNotificationPayload customNotificationPayload, UUID userId,
                                boolean sendToAbsentees) {



        SMSPreferences smsPreferences = userPreferenceSettings.getSMSPreferences(instituteId);
        int academicSessionId = customNotificationPayload.getAcademicSessionId();
        String batchName = customNotificationPayload.getBatchName();

        UUID batchId = UUID.randomUUID();
        SMSResponse finalSMSResponse = null;

        if(sendObtainedMarksSMS != null && sendObtainedMarksSMS.size() > 0) {
            SMSResponse smsResponse = smsManager.sendSMSAsync(instituteId,
                    getSMSPayloadBuilder(customNotificationPayload, smsPreferences,
                            academicSessionId, batchName, sendObtainedMarksSMS, batchId, NotificationType.EXAM_OBTAINED_MARKS,
                            CommunicationServiceTransactionType.EXAM_OBTAINED_MARKS), userId);
            finalSMSResponse = smsResponse;
            if (!smsResponse.isSuccess()) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, smsResponse.getFailureReason()));
            }
        }

        if(!sendToAbsentees || !(sendAbsenteeSMS != null && sendAbsenteeSMS.size() > 0)) {
            return finalSMSResponse;
        }

        finalSMSResponse = smsManager.sendSMSAsync(instituteId,
                getSMSPayloadBuilder(customNotificationPayload, smsPreferences,
                        academicSessionId, batchName, sendAbsenteeSMS, batchId,
                        NotificationType.EXAM_ABSENTEE,
                        CommunicationServiceTransactionType.EXAM_ABSENTEE),
                userId);

        if (!finalSMSResponse.isSuccess()) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, finalSMSResponse.getFailureReason()));
        }

        return finalSMSResponse;
    }

    private ISMSPayloadBuilder getSMSPayloadBuilder(CustomNotificationPayload customNotificationPayload,
                                                    SMSPreferences smsPreferences, int academicSessionId, String batchName,
                                                    Map<UUID, StudentExamMarksDetails> studentExamMarksDetailsMap, UUID batchId,
                                                    NotificationType notificationType, CommunicationServiceTransactionType communicationServiceTransactionType) {
        return new ISMSPayloadBuilder() {

            @Override
            public boolean previewPayloadIsFinal() {
                return true;
            }

            @Override
            public SMSPayloadWrapper getSMSPreviewPayload() {
                return bulkStudentSend(smsPreferences,
                        academicSessionId, batchName, studentExamMarksDetailsMap, batchId,
                        notificationType, communicationServiceTransactionType, customNotificationPayload.getCustomVariables());
            }

            @Override
            public List<UserSMSPayload> getFinalUserSMSPayload() {
                return null;
            }

            @Override
            public boolean executeSMSAction() {
                return true;
            }
        };
    }

    private SMSPayloadWrapper bulkStudentSend(SMSPreferences smsPreferences, int academicSessionId,
                                              String batchName, Map<UUID, StudentExamMarksDetails> studentExamMarksDetailsMap, UUID batchId,
                                              NotificationType notificationType, CommunicationServiceTransactionType communicationServiceTransactionType,
                                              Map<String, String> customVariables) {

        return getSMSPayloadWrapper(academicSessionId, smsPreferences, studentExamMarksDetailsMap, batchId,
                batchName, notificationType, communicationServiceTransactionType, customVariables);
    }

    private SMSPayloadWrapper getSMSPayloadWrapper(int academicSessionId,
                                                   SMSPreferences smsPreferences, Map<UUID, StudentExamMarksDetails> studentExamMarksDetailsMap, UUID batchId,
                                                   String batchName, NotificationType notificationType,
                                                   CommunicationServiceTransactionType communicationServiceTransactionType, Map<String, String> customVariables) {
        if (studentExamMarksDetailsMap == null || studentExamMarksDetailsMap == null || studentExamMarksDetailsMap.size() <= 0) {
            logger.error("Invalid student exam details");
            return SMSPayloadWrapper.failurePayload("Invalid student exam details");
        }

        List<UserSMSPayload> userSMSPayloadList = new ArrayList<UserSMSPayload>();
        for (StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsMap.values()) {
            Map<String, Object> metaData = new HashMap<>();
            SMSContentPayload textMessage = examMarksObtainedSMSContentBuilder.generateTextContent(studentExamMarksDetails, smsPreferences,
                    customVariables, notificationType);

            if(textMessage == null) {
                continue;
            }

            String primaryContactNumber = studentExamMarksDetails.getStudent().getStudentBasicInfo().getPrimaryContactNumber();
            if (StringUtils.isBlank(primaryContactNumber)) {
                logger.error("Contact number not present for student {}. Skipping exam marks SMS.",
                        studentExamMarksDetails.getStudent().getStudentId());
                continue;
            }

            metaData.put(EXAM_MARKS_DETAILS_KEY, GSON.toJson(studentExamMarksDetails.getExamCoursesAllDimensionsMarks().get(0).getExamDimensionObtainedValues()));

            userSMSPayloadList.add(new UserSMSPayload(studentExamMarksDetails.getStudent().getStudentId(),
                    Arrays.asList(primaryContactNumber), textMessage.getContent(), metaData, null, textMessage.getDltTemplateId()));
        }
        return new SMSPayloadWrapper(academicSessionId, UserType.STUDENT, notificationType, communicationServiceTransactionType,
                userSMSPayloadList, batchId, batchName);
    }
}
