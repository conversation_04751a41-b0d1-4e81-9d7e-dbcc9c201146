package com.lernen.cloud.sms.report.handler;

import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.exception.SMSRunTimeException;
import com.lernen.cloud.core.api.notification.NotificationDetails;
import com.lernen.cloud.core.api.notification.NotificationStatus;
import com.lernen.cloud.core.api.notification.NotificationStatusResponse;
import com.lernen.cloud.core.lib.notifications.NotificationManager;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 
 * Fetches the status of SMS sent via all configured services and updates the
 * status
 * 
 * <AUTHOR>
 *
 */
public class SMSStatusUpdateHandler {
	
	private static final Logger logger = LogManager.getLogger(SMSStatusUpdateHandler.class);

	private static final int SECONDS_IN_DAY = 3600 * 24;

	private final NotificationManager notificationManager;

	private final SMSStatusUpdaterServiceFactory smsStatusUpdaterServiceFactory;

	private static final Set<CommunicationServiceProvider> COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE = new HashSet<>();

	static {
		COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE.add(CommunicationServiceProvider.PC_EXPERT_SMS);
		COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE.add(CommunicationServiceProvider.WEBPAY_SMS);
	}

	public SMSStatusUpdateHandler(NotificationManager notificationManager,
			SMSStatusUpdaterServiceFactory smsStatusUpdaterServiceFactory) {
		this.notificationManager = notificationManager;
		this.smsStatusUpdaterServiceFactory = smsStatusUpdaterServiceFactory;
	}

	public void updateSMSStatusAsync(int pastLookupDays) {
		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				updateSMSStatus(pastLookupDays);
			}
		});
		t.start();
	}

	public void updateSMSStatus(int pastLookupDays) {
		int now = DateUtils.now();
		int startTime = now - pastLookupDays * SECONDS_IN_DAY;

		int start = DateUtils.getDayStart(startTime, DateUtils.DEFAULT_TIMEZONE);
		int end = DateUtils.getDayEnd(now, DateUtils.DEFAULT_TIMEZONE);

		List<NotificationDetails> notificationDetailsList = notificationManager
				.getNotificationsInRange(COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE, DeliveryMode.SMS, start, end);
		if (notificationDetailsList == null) {
			logger.error(
					"Error while getting sent sms for COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE {}, s duration {} - {}. Skipping run...",
					COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE, start, end);
			return;
		}

		if (CollectionUtils.isEmpty(notificationDetailsList)) {
			logger.info(
					"No unknown status SMS found in COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE {}, duration {} - {}. Skipping run...",
					COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE, start, end);
			return;
		}

		logger.info(
				"Found {} unknown status SMS for COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE {} , in duration {} - {}. Fetching status report...",
				notificationDetailsList.size(), COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE, start, end);
		int total = 0;
		int success = 0;
		int failure = 0;
		for (CommunicationServiceProvider communicationServiceProvider : COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE) {
			logger.info("Updating status of communicationServiceProvider {} ", communicationServiceProvider);

			ISMSStatusUpdaterService smsStatusUpdaterService = null;
			try {
				smsStatusUpdaterService = smsStatusUpdaterServiceFactory
						.getSMSStatusUpdaterService(communicationServiceProvider);
			} catch (Exception e) {
				logger.error(
						"communicationServiceProvider {} not supported for fetching status. Skipping all records ....",
						communicationServiceProvider);
				continue;
			}

			if (smsStatusUpdaterService == null) {
				logger.error(
						"communicationServiceProvider {} is not configured for fetching status. Skipping all records ....",
						communicationServiceProvider);
				continue;
			}

			CountInfo countInfo = updateSMSStatus(communicationServiceProvider, smsStatusUpdaterService,
					notificationDetailsList);

			total += countInfo.getTotal();
			success += countInfo.getSuccess();
			failure += countInfo.getFailure();

			logger.info("Updated total records {} for service {}, success {}, failure {}", countInfo.getTotal(),
					communicationServiceProvider, countInfo.getSuccess(), countInfo.getFailure());
		}
		logger.info(
				"Updated sms report for {} unknown status SMS in duration {} - {}. Success {}, Failure {}, COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE {}",
				total, start, end, success, failure, COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE);
	}

	private CountInfo updateSMSStatus(CommunicationServiceProvider communicationServiceProvider,
			ISMSStatusUpdaterService smsStatusUpdaterService, List<NotificationDetails> notificationDetailsList) {
		int total = 0;
		int success = 0;
		int failure = 0;

		for (NotificationDetails notificationDetails : notificationDetailsList) {
			if (notificationDetails.getCommunicationServiceProvider() != communicationServiceProvider) {
				continue;
			}
			total++;
			String jobId = notificationDetails.getExternalUniqueId();
			int instituteId = notificationDetails.getInstituteId();
			if (StringUtils.isBlank(jobId) || instituteId <= 0) {
				logger.error("Invalid job id for record {}. Skipping entry...", notificationDetails);
				failure++;
				continue;
			}

			try {
				NotificationStatusResponse notificationStatusResponse = smsStatusUpdaterService
						.getNotificationStatus(instituteId, jobId);
				if (notificationStatusResponse == null || notificationStatusResponse.getNotificationStatus() == null) {
					logger.error("Unable to get notification status for jobId {}. Skipping...", jobId);
					failure++;
					continue;
				}

				if (notificationManager.updateNotification(notificationDetails.getNotificationId(), notificationStatusResponse.getNotificationStatus(),
						notificationStatusResponse.getNotificationStatus() == NotificationStatus.DELIVERED
								? notificationStatusResponse.getStatusUpdateTime()
								: null)) {
					logger.info("Successfully updated the status for jobId {} to {}, delivery time {}", jobId,
							notificationStatusResponse.getNotificationStatus(),
							notificationStatusResponse.getStatusUpdateTime());
					success++;
				} else {
					logger.error("Failed to updated the status for jobId {} to {}, delivery time {}", jobId,
							notificationStatusResponse.getNotificationStatus(),
							notificationStatusResponse.getStatusUpdateTime());
					failure++;
				}
			} catch (SMSRunTimeException e) {
				logger.error("Error while updating sms report for job id {}", jobId, e);
				failure++;
			} catch (Exception e) {
				logger.error("Exception while updating sms report for job id {}", jobId, e);
				failure++;
			}
		}
		return new CountInfo(total, success, failure);
	}

	private class CountInfo {

		private final int total;
		private final int success;
		private final int failure;

		public CountInfo(int total, int success, int failure) {
			this.total = total;
			this.success = success;
			this.failure = failure;
		}

		public int getTotal() {
			return total;
		}

		public int getSuccess() {
			return success;
		}

		public int getFailure() {
			return failure;
		}

	}

}
