package com.lernen.cloud.sms.report.handler;

import com.embrate.cloud.core.api.sms.webpay.WebPaySMSReportResponse;
import com.embrate.cloud.core.api.sms.webpay.WebPaySMSStatus;
import com.lernen.cloud.core.api.exception.SMSRunTimeException;
import com.lernen.cloud.core.api.notification.NotificationStatus;
import com.lernen.cloud.core.api.notification.NotificationStatusResponse;
import com.lernen.cloud.core.api.sms.SMSPreferences;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.rest.RestAPIHandler;
import com.lernen.cloud.core.utils.rest.RestClient;
import com.sun.jersey.api.client.ClientHandlerException;
import com.sun.jersey.api.client.ClientResponse;
import com.sun.jersey.api.client.UniformInterfaceException;
import com.sun.jersey.api.client.WebResource;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.ws.rs.core.MediaType;
import java.net.URI;
import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class WebPaySMSStatusUpdaterService implements ISMSStatusUpdaterService {
	private static final Logger logger = LogManager.getLogger(WebPaySMSStatusUpdaterService.class);

	private static final String SMS_STATUS_REPORT_API = "http://login.webpayservices.in/sms-panel/api/http/index.php";
	private static final String API_KEY_QUERY_PARAM_KEY = "apikey";
	private static final String API_REQUEST_QUERY_PARAM_KEY = "apirequest";
	private static final String FORMAT_QUERY_PARAM_KEY = "JSON";
	private static final String JOB_ID_QUERY_PARAM_KEY = "messageid";
	private static final String USER_NAME_QUERY_PARAM_KEY = "username";

//	private static final String USER_NAME = "demo webpay";
//	private static final String USER_NAME = "embrate";
//	private static final String API_KEY = "70D92-C6585";
	private static final String API_REQUEST = "DeliveryReport";
	private static final String FORMAT = "JSON";

	private final RestClient restClient;

	private final UserPreferenceSettings userPreferenceSettings;

	public WebPaySMSStatusUpdaterService(RestClient restClient, UserPreferenceSettings userPreferenceSettings) {
		this.restClient = restClient;
		this.userPreferenceSettings = userPreferenceSettings;
	}

	@Override
	public NotificationStatusResponse getNotificationStatus(int instituteId, String jobId) {
		Map<String, String> smsReportQueryParams = createSMSReportQueryParams(instituteId, jobId);
		ClientResponse response = null;
		try {
			WebResource webResource = restClient.resource(new URI(SMS_STATUS_REPORT_API));
			webResource = RestAPIHandler.addQueryParam(webResource, smsReportQueryParams);
			logger.info("Fetching sms report with URI {}", webResource.getURI());
			response = webResource.type(MediaType.APPLICATION_JSON).get(ClientResponse.class);
			if (response.getStatus() == HttpStatus.SC_OK) {
				String responseData = response.getEntity(String.class);
				if (StringUtils.isBlank(responseData)) {
					logger.error(
							"Error response from WebPay jobId {}, {}. SMS report failed due to error {}, status code {}",
							jobId, SMS_STATUS_REPORT_API, responseData, response.getStatus());
					throw new SMSRunTimeException("Error response from PC Expert service provider.");
				}
				logger.info("webpay report response for jobId {} ,  {} for ", jobId, responseData);
				WebPaySMSReportResponse webPaySMSReportResponse = SharedConstants.OBJECT_MAPPER.readValue(responseData,
						WebPaySMSReportResponse.class);

				return getNotificationStatus(jobId, webPaySMSReportResponse);

			} else {
				logger.error("Unable to fetch SMS report via WebPay {} due to error code {}", SMS_STATUS_REPORT_API,
						response.getStatus());
				throw new SMSRunTimeException("Error response from WebPay service provider.");
			}
		} catch (UniformInterfaceException | ClientHandlerException e) {
			logger.error("Unable to perform get call to URL {}", SMS_STATUS_REPORT_API, e);
			throw new SMSRunTimeException("Exception to perform get status call", e);
		} catch (final Exception e) {
			logger.error("Exception occured while performing get call to URL {}", SMS_STATUS_REPORT_API, e);
			throw new SMSRunTimeException("Exception to perform get status call", e);
		} finally {
			if (response != null) {
				response.close();
			}
		}

	}

	private Map<String, String> createSMSReportQueryParams(int instituteId, String jobId) {
		Map<String, String> sendSMSQueryParams = new HashMap<>();

		SMSPreferences smsPreferences = userPreferenceSettings.getSMSPreferences(instituteId);
		sendSMSQueryParams.put(USER_NAME_QUERY_PARAM_KEY, smsPreferences.getSmsSenderServiceUsername());
		sendSMSQueryParams.put(API_KEY_QUERY_PARAM_KEY, smsPreferences.getSmsSenderServiceApikey());
		sendSMSQueryParams.put(API_REQUEST_QUERY_PARAM_KEY, API_REQUEST);
		sendSMSQueryParams.put(JOB_ID_QUERY_PARAM_KEY, jobId);
		sendSMSQueryParams.put(FORMAT_QUERY_PARAM_KEY, FORMAT);
		return sendSMSQueryParams;

	}

	private NotificationStatusResponse getNotificationStatus(String jobId,
			WebPaySMSReportResponse webPaySMSReportResponse) {
		Long deliveryTime = null;
		if (StringUtils.isEmpty(webPaySMSReportResponse.getStatus())) {
			return null;
		}

		NotificationStatus notificationStatus = WebPaySMSStatus
				.getNotificationStatus(webPaySMSReportResponse.getStatus());
		if (notificationStatus == null) {
			logger.warn("Unable to process delivery status {}. Marking it as failed",
					webPaySMSReportResponse.getStatus());
			notificationStatus = NotificationStatus.FAILED;
		} else if (notificationStatus == NotificationStatus.DELIVERED) {
			/**
			 * WebPay service does not give the delivery time. Making the status fetching
			 * time as the delivered time
			 */
			deliveryTime = System.currentTimeMillis();
		}

		return new NotificationStatusResponse(notificationStatus, deliveryTime);

	}

}
