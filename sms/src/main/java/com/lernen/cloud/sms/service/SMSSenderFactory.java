package com.lernen.cloud.sms.service;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.lernen.cloud.core.api.common.CommunicationServiceProvider;

/**
 * 
 * <AUTHOR>
 *
 */
public class SMSSenderFactory {
	private static final Logger logger = LogManager.getLogger(SMSSenderFactory.class);

	private final MSG91SMSSender msg91SMSSender;
	private final PCExpertSMSSender pcExpertSMSSender;
	private final WebPaySMSSender webPaySMSSender;

	public SMSSenderFactory(MSG91SMSSender msg91SMSSender, PCExpertSMSSender pcExpertSMSSender, WebPaySMSSender webPaySMSSender) {
		this.msg91SMSSender = msg91SMSSender;
		this.pcExpertSMSSender = pcExpertSMSSender;
		this.webPaySMSSender = webPaySMSSender;
	}

	public ISMSSender getSMSSender(CommunicationServiceProvider smsProvider) {
		switch (smsProvider) {
		case MSG91_SMS:
			return msg91SMSSender;
		case PC_EXPERT_SMS:
			return pcExpertSMSSender;
		case WEBPAY_SMS:
			return webPaySMSSender;
		default:
			logger.error("Service provider {} not supported", smsProvider);
			throw new UnsupportedOperationException("Service provider " + smsProvider + " not supported");
		}
	}
}