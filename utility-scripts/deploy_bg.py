import subprocess
import re
import time
import requests

TOMCAT_BASE_PATH = "/home/<USER>/embrate/server/apache-tomcat-9.0.91-{dep_flag}/"
HEALTH_URL = "http://127.0.0.1:{port}/systime"
PORT_MAP = {'blue' : 8081, 'green' : 8082}

def poll_api(target_dep, interval=5):
	while True:
		try:
			response = requests.get(HEALTH_URL.format(port = PORT_MAP[target_dep]))
			if response.status_code == 200:
				print("API returned 200. Exiting loop.")
				break
			else:
				print(f"API returned {response.status_code}. Retrying in {interval} seconds.")
		except requests.RequestException as e:
			print(f"An error occurred: {e}. Retrying in {interval} seconds.")

		time.sleep(interval)



def copy_war(target_dep, version = ""):
	if version == "":
		file_path = "/tmp/data-server.war"
	else:
		file_path = "/tmp/data-server-"+str(version)+".war"
	copy_cmd = "sudo cp "+ file_path + " " + TOMCAT_BASE_PATH.format(dep_flag = target_dep) + "webapps/ROOT.war"
	print(copy_cmd)
	subprocess.call(copy_cmd, shell=True)
	print("Done copying war file. version = "+str(version))

def start_tomcat_server(target_dep):
	print(f"Starting tomcat server for {target_dep}")
	start_cmd = "sudo sh " + TOMCAT_BASE_PATH.format(dep_flag = target_dep) + "bin/startup.sh"
	print(start_cmd)
	subprocess.call(start_cmd, shell=True)
	print(f"Started tomcat server for {target_dep}")

def stop_tomcat_server(target_dep):
	print(f"Stopping tomcat server for {target_dep}")
	stop_cmd = "sudo sh " + TOMCAT_BASE_PATH.format(dep_flag = target_dep) + "bin/shutdown.sh"
	print(stop_cmd)
	subprocess.call(stop_cmd, shell=True)
	print(f"Stopped tomcat server for {target_dep}")

def restart_nginx_server(target_dep):
	print(f"Restarting nginx server for {target_dep}")
	restart_cmd = "sudo nginx -t && sudo systemctl restart nginx"
	print(restart_cmd)
	subprocess.call(restart_cmd, shell=True)
	print(f"Restarted nginx server for {target_dep}")


def read_nginx_config(file_path):
	with open(file_path, 'r') as file:
		content = file.read()
	return content

def get_proxy_pass(content):
	match = re.search(r'proxy_pass\s+(http://\S+);', content)
	if match:
		return match.group(1)
	return None

def replace_proxy_pass(content, new_value):
	updated_content = re.sub(r'proxy_pass\s+http://\S+;', f'proxy_pass {new_value};', content)
	return updated_content

def write_nginx_config(file_path, content):
	with open(file_path, 'w') as file:
		file.write(content)

def main():
	file_path = '/etc/nginx/sites-available/tomcat-server'
	# file_path = '/Users/<USER>/tomcat-server'
	next_bg_map = {'blue' : 'green', 'green' : 'blue'}
	# Read the current configuration
	config_content = read_nginx_config(file_path)

	# Get the current proxy_pass value
	current_proxy_pass = get_proxy_pass(config_content)
	if current_proxy_pass:
		print(f"Current proxy_pass value: {current_proxy_pass}")
	else:
		print("No proxy_pass value found.")
		return

	current_dep = current_proxy_pass.strip().split("http://")[1]
	target_dep = next_bg_map[current_dep]
	print(f"next dep value = {target_dep}")
	copy_war(target_dep)
	start_tomcat_server(target_dep)
	# Poll for systime health of new server
	poll_api(target_dep)
	# Replace the current proxy_pass value with the new value
	updated_content = replace_proxy_pass(config_content, 'http://{target_dep}'.format(target_dep = target_dep))

	# Write the updated configuration back to the file
	write_nginx_config(file_path, updated_content)
	print(f"proxy_pass value updated to: {target_dep}")

	restart_nginx_server(target_dep)

	stop_tomcat_server(current_dep)

if __name__ == "__main__":
	main()
