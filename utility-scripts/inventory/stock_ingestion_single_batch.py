import requests
import json
import time
from datetime import datetime

headers =  {"Content-Type": "application/json"}

# BASE_URL = "https://api.embrate.com"
# ACCESS_TOKEN = ""

# BASE_URL = "http://127.0.0.1:8080/data-server"
# ACCESS_TOKEN = ""

REGISTER_STOCK_API_URL = BASE_URL + "/2.1/inventory/product?institute_id={institute_id}&user_id={user_id}&access_token={access_token}"

def get_stock_payload(bulk_stock_payload, institute_id, start_col):
    bulk_ingest_stock_payload = []
    count = 0
    productPayloadList = []

    category_map = {}

    category_map['BOOKS'] = 1263
    category_map['FMCG'] = 1497
    category_map['GENERAL'] = 1496
    category_map['NOTE BOOK'] = 1266
    category_map['STATIONERY'] = 1262
    category_map['UNIFORM'] = 1498

    for stock_payload in bulk_stock_payload:

#         uncomment below code for batch data ingestion
        batchName = '1'
        batchDescription = ''
        initialQuantity = stock_payload['initialQuantity']
        categoryName = stock_payload['category']
        sellingPrice = stock_payload['sellingPrice']
        discount = None
        discountInPercent = 0
        purchasePrice = stock_payload['purchasePrice']
        mrp = stock_payload['mrp']

        categoryId = category_map[categoryName.upper()]

        productBatchPayloadList = []
        productBatchPayload = {'batchName' : batchName, 'description' : batchDescription, 'initialQuantity' : initialQuantity, 'sellingPrice' : sellingPrice, 'discount' : discount, 'discountInPercent' : discountInPercent, 'purchasePrice' : purchasePrice, 'mrp' : mrp}
        productBatchPayloadList.append(productBatchPayload)

#         comment below line for batch data ingestion
#         productBatchPayloadList = []

        name = stock_payload['name']
#         categoryId = 1445
#         categoryId = 1446
        brandId = None
        description = ''

        print(categoryId)

        productPayloadList.append({'productMetadataPayload' : {'name' : name, 'categoryId' : categoryId, "brandId" : brandId, "description" : description}, 'productBatchPayloadList' : productBatchPayloadList})

        count += 1

    newProductRequest = {'productPayloadList' : productPayloadList}
    bulk_ingest_stock_payload.append(newProductRequest)

    return bulk_ingest_stock_payload

def ingest_stock(institute_id, user_id, stock_payload):
    try:
        print("\n adding stock = " + str(stock_payload) + " \n")
        response = requests.post(REGISTER_STOCK_API_URL.format(institute_id = institute_id, user_id = user_id, access_token = ACCESS_TOKEN), data=json.dumps(stock_payload), headers=headers)
        print(response)
        if response.status_code == 200:
            data = json.loads(response.content)
            print(data)
            print("Success result " + str(data))
            return len(data) > 0
        else:
            print("Error " + str(response.status_code) + " for stock_payload " + str(stock_payload) + " error = " + str(response.text))
            return None
    except Exception as e:
        print("Error for stock_payload " + str(stock_payload), type(e).__name__, e)
        print(e)
        return None

def read_file(path, start_col):
    file = open(path, 'r')
    delimiter = '|'
    count = 0
    bulk_stock_payload = []
    for line in file:
        if count == 0:
            count += 1
            continue
        stock_payload = {}
        line = line.split("\n")[0]
        stock_payload['name'] = line.split(delimiter)[0]
        stock_payload['category'] = line.split(delimiter)[1]
        stock_payload['sellingPrice'] = float(line.split(delimiter)[2]) if line.split(delimiter)[2] != '' else None
        stock_payload['mrp'] = float(line.split(delimiter)[3]) if line.split(delimiter)[3] != '' else None
        stock_payload['initialQuantity'] = float(line.split(delimiter)[4]) if line.split(delimiter)[4] != '' else None
        stock_payload['purchasePrice'] = float(line.split(delimiter)[5]) if line.split(delimiter)[5] != '' else None
        bulk_stock_payload.append(stock_payload)
        count += 1

    return bulk_stock_payload

def run(file_path, institute_id, user_id, start_col, dry_run):
    bulk_stock_payload = read_file(file_path, start_col)
    final_bulk_stock_payload = get_stock_payload(bulk_stock_payload, institute_id, start_col)

    total = 0
    stockIngestionSuccess = 0
    stockIngestionFailure = 0

    count = 0
    for stock_payload in final_bulk_stock_payload:
        print(stock_payload)
        if(dry_run):
            continue

        total+=1
        stock_id = None
        success = ingest_stock(institute_id, user_id, stock_payload)
        if success:
            stockIngestionSuccess += 1
        else:
            stockIngestionFailure+=1

        count += 1

    print("Final total stock = {total}, stockIngestionSuccess = {stockIngestionSuccess}, stockIngestionFailure = {stockIngestionFailure}".format(total = total, stockIngestionSuccess = stockIngestionSuccess, stockIngestionFailure = stockIngestionFailure))


# run('/Users/<USER>/Desktop/10390_inventory_stock_data_final.csv', 10390, "6dae5e9a-f7b8-46f1-8046-82ff10fa5148", 0, True)
# run('/Users/<USER>/Desktop/10390_inventory_stock_data_final.csv', 10390, "6dae5e9a-f7b8-46f1-8046-82ff10fa5148", 0, False)

# run('/Users/<USER>/Desktop/10420_inventory_stock_data_final.csv', 10420, "72dc38e4-f653-4219-aa9b-8ccb918e9b89", 0, True)
# run('/Users/<USER>/Desktop/10420_inventory_stock_data_final.csv', 10420, "72dc38e4-f653-4219-aa9b-8ccb918e9b89", 0, False)
