package com.embrate.cloud.voicecall.handler;

import com.embrate.cloud.core.api.service.communication.UserCommunicationServicePayload;
import com.lernen.cloud.core.api.voicecall.VoiceCallPayloadWrapper;

import java.util.List;

/**
 * This is class to generate voice call audio payload to be sent
 * 
 * <AUTHOR>
 *
 */
public interface IVoiceCallPayloadBuilder<T extends UserCommunicationServicePayload> {

	/**
	 * Sample payload that will be sent. It has to be ensured that character count
	 * does not change in final SMS payload with respect to preview payload. This
	 * payload is to determine the number of credits would be deducted based on SMS
	 * content
	 * 
	 * @return
	 */
	public VoiceCallPayloadWrapper<T> getUserVoiceCallPreviewPayload();

	/**
	 * Any action that has to be performed before actually sending out the sms. This
	 * step comes after validating SMS availability etc
	 * 
	 * @return
	 */
	public boolean executeVoiceCallAction();

	/**
	 * Flag to specify whether preview payload is final payload that has to be sent
	 * to users. If this is true, getFinalUserSMSPayload will be irrelevant
	 * 
	 * @return
	 */
	public boolean previewPayloadIsFinal();

	/**
	 * Final SMS Payload that has to be sent
	 * 
	 * @return
	 */
	public List<T> getFinalUserVoiceCallPayload();
}
