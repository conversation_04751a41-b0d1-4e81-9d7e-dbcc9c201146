package com.embrate.cloud.voicecall.report.handler;

import com.embrate.cloud.core.api.service.communication.templates.TemplateStatus;
import com.lernen.cloud.core.api.notification.NotificationStatusResponse;
import com.lernen.cloud.core.api.voicecall.VoiceCallStatusResponse;

import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface IVoiceStatusUpdaterService {

	public VoiceCallStatusResponse getNotificationStatus(String jobId, Map<String, Object> metadata);

	public TemplateStatus getVoiceTemplateStatus(String voiceId);
}
