package com.embrate.cloud.voicecall.report.handler;

import com.embrate.cloud.core.api.service.communication.templates.CommunicationTemplate;
import com.embrate.cloud.core.api.service.communication.templates.ExternalServiceTemplateData;
import com.embrate.cloud.core.api.service.communication.templates.TemplateStatus;
import com.embrate.cloud.core.lib.service.communication.CommunicationServiceManager;
import com.embrate.cloud.core.lib.templates.notification.NotificationTemplateManager;
import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.notification.NotificationDetails;
import com.lernen.cloud.core.api.notification.NotificationStatus;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.api.voicecall.VoiceCallStatusResponse;
import com.lernen.cloud.core.lib.notifications.NotificationManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 
 * Fetches the status of voice call sent via all configured services and updates the
 * status
 * 
 * <AUTHOR>
 *
 */
public class VoiceStatusUpdateHandler {

	private static final Logger logger = LogManager.getLogger(VoiceStatusUpdateHandler.class);

	private static final int SECONDS_IN_DAY = 3600 * 24;

	private final NotificationManager notificationManager;

	private final VoiceStatusUpdaterServiceFactory voiceStatusUpdaterServiceFactory;

	private final CommunicationServiceManager communicationServiceManager;

	private final NotificationTemplateManager notificationTemplateManager;

	private static final Set<CommunicationServiceProvider> COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE = new HashSet<>();
	private static final Set<TemplateStatus> UNKNOWN_TEMPLATE_STATUS = new HashSet<>();

	static {
		COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE.add(CommunicationServiceProvider.DATAGEN_AUDIO_VOICECALL);
	}

	static {
		UNKNOWN_TEMPLATE_STATUS.add(TemplateStatus.UNKNOWN);
		UNKNOWN_TEMPLATE_STATUS.add(TemplateStatus.PENDING);
	}

	public VoiceStatusUpdateHandler(NotificationManager notificationManager, VoiceStatusUpdaterServiceFactory voiceStatusUpdaterServiceFactory, CommunicationServiceManager communicationServiceManager, NotificationTemplateManager notificationTemplateManager) {
		this.notificationManager = notificationManager;
		this.voiceStatusUpdaterServiceFactory = voiceStatusUpdaterServiceFactory;
		this.communicationServiceManager = communicationServiceManager;
		this.notificationTemplateManager = notificationTemplateManager;
	}

	public void updateVoiceCallStatusAsync(int pastLookupDays) {
		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				updateVoiceCallStatus(pastLookupDays);
			}
		});
		t.start();
	}

	public void updateVoiceTemplateStatusAsync() {
		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				updateVoiceTemplateStatus();
			}
		});
		t.start();
	}

	public void updateVoiceCallStatus(int pastLookupDays) {
		int now = DateUtils.now();
		int startTime = now - pastLookupDays * SECONDS_IN_DAY;

		int start = DateUtils.getDayStart(startTime, DateUtils.DEFAULT_TIMEZONE);
		int end = DateUtils.getDayEnd(now, DateUtils.DEFAULT_TIMEZONE);

		List<NotificationDetails> notificationDetailsList = notificationManager
				.getNotificationsInRange(COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE, DeliveryMode.CALL, start, end);
		if (notificationDetailsList == null) {
			logger.error(
					"Error while getting sent voice calls for COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE {}, s duration {} - {}. Skipping run...",
					COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE, start, end);
			return;
		}

		if (CollectionUtils.isEmpty(notificationDetailsList)) {
			logger.info(
					"No unknown status voice call found in COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE {}, duration {} - {}. Skipping run...",
					COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE, start, end);
			return;
		}

		logger.info(
				"Found {} unknown status voice calls for COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE {} , in duration {} - {}. Fetching status report...",
				notificationDetailsList.size(), COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE, start, end);
		int total = 0;
		int success = 0;
		int failure = 0;
		for (CommunicationServiceProvider communicationServiceProvider : COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE) {
			logger.info("Updating status of communicationServiceProvider {} ", communicationServiceProvider);

			IVoiceStatusUpdaterService voiceStatusUpdaterService = null;
			try {
				voiceStatusUpdaterService = voiceStatusUpdaterServiceFactory
						.getVoiceStatusUpdaterService(communicationServiceProvider);
			} catch (Exception e) {
				logger.error(
						"communicationServiceProvider {} not supported for fetching status. Skipping all records ....",
						communicationServiceProvider);
				continue;
			}

			if (voiceStatusUpdaterService == null) {
				logger.error(
						"communicationServiceProvider {} is not configured for fetching status. Skipping all records ....",
						communicationServiceProvider);
				continue;
			}

			CountInfo countInfo = updateVoiceCallStatus(communicationServiceProvider, voiceStatusUpdaterService,
					notificationDetailsList);

			total += countInfo.getTotal();
			success += countInfo.getSuccess();
			failure += countInfo.getFailure();

			logger.info("Updated total records {} for service {}, success {}, failure {}", countInfo.getTotal(),
					communicationServiceProvider, countInfo.getSuccess(), countInfo.getFailure());
		}
		logger.info(
				"Updated voice report for {} unknown status voice call in duration {} - {}. Success {}, Failure {}, COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE {}",
				total, start, end, success, failure, COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE);
	}

	public void updateVoiceTemplateStatus() {

		List<CommunicationTemplate> communicationTemplateList = notificationTemplateManager.getCommunicationTemplates(UNKNOWN_TEMPLATE_STATUS);

		if (communicationTemplateList == null) {
			logger.error(
					"Error while getting sent templates for COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE {}. Skipping run...",
					COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE);
			return;
		}

		if (CollectionUtils.isEmpty(communicationTemplateList)) {
			logger.info(
					"No unknown status templates found in COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE {}. Skipping run...",
					COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE);
			return;
		}

		logger.info(
				"Found {} unknown status templates for COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE {}. Fetching status report...",
				communicationTemplateList.size(), COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE);
		int total = 0;
		int success = 0;
		int failure = 0;
		for (CommunicationTemplate communicationTemplate : communicationTemplateList) {
			/**
			 * Only checking voice call template. Can be extended in generic service for other delivery modes
			 */
			if (communicationTemplate.getDeliveryMode() != DeliveryMode.CALL) {
				continue;
			}

			logger.info("Updating status of communicationTemplate {} ", communicationTemplate);
			List<ExternalServiceTemplateData> externalServiceTemplateDataList = notificationTemplateManager.getExternalTemplateMapping(communicationTemplate.getTemplateId());
			if(externalServiceTemplateDataList == null){
				logger.error("Error while getting external template for communicationTemplate {}", communicationTemplate);
				continue;
			}

			if(CollectionUtils.isEmpty(externalServiceTemplateDataList)){
				logger.error("No external template found for communicationTemplate {}", communicationTemplate);
				continue;
			}

			updateVoiceTemplateStatus(communicationTemplate, externalServiceTemplateDataList);
		}
	}

	private void updateVoiceTemplateStatus(CommunicationTemplate communicationTemplate, List<ExternalServiceTemplateData> externalServiceTemplateDataList) {
		int externalTemplatesForApproval = 0;
		int templatesApproved = 0;
		for(ExternalServiceTemplateData externalServiceTemplateData : externalServiceTemplateDataList) {
			externalTemplatesForApproval++;
			if(externalServiceTemplateData.getTemplateStatus() == TemplateStatus.APPROVED) {
				templatesApproved++;
			}

			if(!UNKNOWN_TEMPLATE_STATUS.contains(externalServiceTemplateData.getTemplateStatus())) {
				logger.info("Template status is known for externalServiceTemplateData {}", externalServiceTemplateData);
				continue;
			}

			if(!COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE.contains(externalServiceTemplateData.getCommunicationServiceProvider())){
				logger.warn("{} does not exist in COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE {}", externalServiceTemplateData.getCommunicationServiceProvider(),COMMUNICATION_SERVICE_PROVIDERS_TO_UPDATE);
				continue;
			}
			CommunicationServiceProvider communicationServiceProvider = externalServiceTemplateData.getCommunicationServiceProvider();
			logger.info("Updating status of communicationServiceProvider {} ", communicationServiceProvider);

			IVoiceStatusUpdaterService voiceStatusUpdaterService = null;
			try {
				voiceStatusUpdaterService = voiceStatusUpdaterServiceFactory
						.getVoiceStatusUpdaterService(communicationServiceProvider);
			} catch (Exception e) {
				logger.error(
						"communicationServiceProvider {} not supported for fetching status. Skipping all records ....",
						communicationServiceProvider);
				continue;
			}

			if (voiceStatusUpdaterService == null) {
				logger.error(
						"communicationServiceProvider {} is not configured for fetching status. Skipping all records ....",
						communicationServiceProvider);
				continue;
			}

			TemplateStatus templateStatus = updateVoiceTemplateStatus(communicationServiceProvider, voiceStatusUpdaterService,
					externalServiceTemplateData);

			if(templateStatus == TemplateStatus.APPROVED) {
				templatesApproved++;
			}
		}

		if(externalTemplatesForApproval == templatesApproved) {
			logger.info("All external templates are approved. Updating the internal template status for communicationTemplate {}", communicationTemplate);
			if(notificationTemplateManager.updateCommunicationTemplateStatus(communicationTemplate.getTemplateId(), TemplateStatus.APPROVED)){
				logger.info("Successfully updated the template status to approved for communicationTemplate {}", communicationTemplate);
			}else {
				logger.error("Failed to updated the template status to approved for communicationTemplate {}", communicationTemplate);
			}
		}else {
			logger.warn("All external templates are not approved yet. Skipping template status to approved for communicationTemplate {}", communicationTemplate);
		}
	}

	private CountInfo updateVoiceCallStatus(CommunicationServiceProvider communicationServiceProvider,
										IVoiceStatusUpdaterService voiceStatusUpdaterService, List<NotificationDetails> notificationDetailsList) {
		int total = 0;
		int success = 0;
		int failure = 0;

		for (NotificationDetails notificationDetails : notificationDetailsList) {
			if (notificationDetails.getCommunicationServiceProvider() != communicationServiceProvider) {
				continue;
			}
			total++;
			String jobId = notificationDetails.getExternalUniqueId();
			if (StringUtils.isBlank(jobId)) {
				logger.error("Invalid job id for record {}. Skipping entry...", notificationDetails);
				failure++;
				continue;
			}

			try {
				VoiceCallStatusResponse voiceCallStatusResponse = voiceStatusUpdaterService
						.getNotificationStatus(jobId, notificationDetails.getMetaData());
				if (voiceCallStatusResponse == null || voiceCallStatusResponse.getNotificationStatus() == null) {
					logger.error("Unable to get notification status for jobId {}. Skipping...", jobId);
					failure++;
					continue;
				}

				Integer refundCredits = null;
				/**
				 * Make sure to handle voice call retries. It may happen that call is unanswered first and then it got answered
				 */

				Map<String, Object> metaData = notificationDetails.getMetaData();
				if(metaData == null){
					metaData = new HashMap<>();
				}

				metaData.put("voiceCallStatusResponse" , SharedConstants.GSON.toJson(voiceCallStatusResponse));

				if(voiceCallStatusResponse.getNotificationStatus() == NotificationStatus.UNANSWERED){
					logger.info("Job id {} is unanswered, refunding the credits for notification {}",  jobId, notificationDetails);
					if(communicationServiceManager.refundServiceTransaction(notificationDetails.getInstituteId(), notificationDetails.getCommunicationServiceProvider(), UserType.SYSTEM, SharedConstants.SYSTEM_USER_ID, notificationDetails.getCreditsUsed(), notificationDetails.getCreditTransactionId())){
						logger.info("Job id {} is unanswered, credit refund {} successful for notification {}",  jobId, notificationDetails.getCreditsUsed(), notificationDetails);
						refundCredits = notificationDetails.getCreditsUsed();
					}else{
						logger.error("Job id {} is unanswered, failed to refund credit{} for notification {}",  jobId, notificationDetails.getCreditsUsed(), notificationDetails);
					}
				}

				if (notificationManager.updateNotificationWithRefund(notificationDetails.getNotificationId(), voiceCallStatusResponse.getNotificationStatus(),
						voiceCallStatusResponse.getNotificationStatus() == NotificationStatus.ANSWERED
								? voiceCallStatusResponse.getStatusUpdateTime()
								: null, refundCredits, metaData)) {
					logger.info("Successfully updated the status for jobId {} to {}, delivery time {}", jobId,
							voiceCallStatusResponse.getNotificationStatus(),
							voiceCallStatusResponse.getStatusUpdateTime());
					success++;
				} else {
					logger.error("Failed to updated the status for jobId {} to {}, delivery time {}", jobId,
							voiceCallStatusResponse.getNotificationStatus(),
							voiceCallStatusResponse.getStatusUpdateTime());
					failure++;
				}
			} catch (EmbrateRunTimeException e) {
				logger.error("Error while updating voice report for job id {}", jobId, e);
				failure++;
			} catch (Exception e) {
				logger.error("Exception while updating voice report for job id {}", jobId, e);
				failure++;
			}
		}
		return new CountInfo(total, success, failure);
	}

	private TemplateStatus updateVoiceTemplateStatus(CommunicationServiceProvider communicationServiceProvider,
											IVoiceStatusUpdaterService voiceStatusUpdaterService, ExternalServiceTemplateData externalServiceTemplateData ) {

			String externalTemplateId = externalServiceTemplateData.getExternalTemplateId();
			if (StringUtils.isBlank(externalTemplateId)) {
				logger.error("Invalid externalTemplateId for record {}. Skipping entry...", externalServiceTemplateData);
				return null;
			}

			try {
				TemplateStatus templateStatus = voiceStatusUpdaterService
						.getVoiceTemplateStatus(externalTemplateId);
				if (templateStatus == null) {
					logger.error("Unable to get template status for externalTemplateId {}. Skipping...", externalTemplateId);
					return null;
				}


				if (notificationTemplateManager.updateExternalTemplateStatus(externalServiceTemplateData.getTemplateId(), externalServiceTemplateData.getExternalTemplateId(), templateStatus)) {
					logger.info("Successfully updated the template status for externalServiceTemplateData {} to {}", externalServiceTemplateData,
							templateStatus);

					return templateStatus;
				} else {
					logger.info("Failed to updated the template status for externalServiceTemplateData {} to {}", externalServiceTemplateData,
							templateStatus);
					return null;
				}
			} catch (EmbrateRunTimeException e) {
				logger.error("Error while updating voice template externalServiceTemplateData {}", externalServiceTemplateData, e);
			} catch (Exception e) {
				logger.error("Exception while updating voice template externalServiceTemplateData {}", externalServiceTemplateData, e);
			}
			return null;
		}

	private class CountInfo {

		private final int total;
		private final int success;
		private final int failure;

		public CountInfo(int total, int success, int failure) {
			this.total = total;
			this.success = success;
			this.failure = failure;
		}

		public int getTotal() {
			return total;
		}

		public int getSuccess() {
			return success;
		}

		public int getFailure() {
			return failure;
		}

	}

}
