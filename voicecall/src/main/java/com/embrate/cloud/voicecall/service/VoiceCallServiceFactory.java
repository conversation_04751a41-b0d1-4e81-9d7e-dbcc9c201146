package com.embrate.cloud.voicecall.service;

import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class VoiceCallServiceFactory {

    private static final Logger logger = LogManager.getLogger(VoiceCallServiceFactory.class);

    private final DatagenVoiceCallService datagenVoiceCallService;

    public VoiceCallServiceFactory(DatagenVoiceCallService datagenVoiceCallService) {
        this.datagenVoiceCallService = datagenVoiceCallService;
    }

    public IVoiceCallService getVoiceCallService(CommunicationServiceProvider communicationServiceProvider) {
        switch (communicationServiceProvider) {
            case DATAGEN_AUDIO_VOICECALL:
            case DATAGEN_TEXT_VOICECALL:
                return datagenVoiceCallService;
            default:
                logger.error("Voice call service provider {} not supported", communicationServiceProvider);
                throw new UnsupportedOperationException("Voice call Service provider " + communicationServiceProvider + " not supported");
        }
    }
}
