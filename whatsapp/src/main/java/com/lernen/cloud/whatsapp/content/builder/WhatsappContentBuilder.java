package com.lernen.cloud.whatsapp.content.builder;

import com.embrate.cloud.core.api.service.communication.templates.*;
import com.embrate.cloud.core.api.whatsapp.WhatsappTemplateContent;
import com.embrate.cloud.core.lib.templates.notification.NotificationTemplateManager;
import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.whatsapp.handler.CustomWhatsappHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;

import java.io.StringWriter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public abstract class WhatsappContentBuilder {
    private static final Logger logger = LogManager.getLogger(WhatsappContentBuilder.class);
    private final NotificationTemplateManager notificationTemplateManager;

    public WhatsappContentBuilder(NotificationTemplateManager notificationTemplateManager) {
        this.notificationTemplateManager = notificationTemplateManager;
    }

    public WhatsappTemplateContent getWhatsappTemplateContent(UUID templateId){
        if(templateId == null){
            logger.error("Invalid template id");
            return null;
        }
        CommunicationTemplate communicationTemplate = notificationTemplateManager.getCommunicationTemplate(templateId);
        if(communicationTemplate == null || communicationTemplate.getTemplateStatus() != TemplateStatus.APPROVED){
            logger.error("Invalid template configured for templateId {}", templateId);
            return null;
        }
        ExternalServiceTemplateData externalServiceTemplateData = notificationTemplateManager.getExternalTemplateMapping(CommunicationServiceProvider.WEBPAY_WHATSAPP, templateId);

        return new WhatsappTemplateContent(communicationTemplate.getTemplateValue(), communicationTemplate.getTemplateVariableDetails(), externalServiceTemplateData.getExternalTemplateId());
    }

    public LinkedHashMap<TemplateVariableType, String> getSystemVariables(TemplateVariableDetails templateVariableDetails,
                                                                CustomWhatsappHandler.WhatsappUserMetadata whatsappUserMetadata,
                                                                String instituteName){
        LinkedHashMap<TemplateVariableType, String> systemVars = new LinkedHashMap<>();
        if(templateVariableDetails == null || CollectionUtils.isEmpty(templateVariableDetails.getTemplateVariableList())){
            logger.warn("No variable found {}", templateVariableDetails);
            return systemVars;
        }

        for(TemplateVariable templateVariable : templateVariableDetails.getTemplateVariableList()){
            if(templateVariable == null || templateVariable.getTemplateVariableType() == null){
                continue;
            }
            if(systemVars.containsKey(templateVariable.getTemplateVariableType())){
                continue;
            }
            switch (templateVariable.getTemplateVariableType()){
                case STUDENT_NAME:
                    systemVars.put(templateVariable.getTemplateVariableType(), whatsappUserMetadata.getStudentUserData().getStudentName());
                    break;
                case STANDARD_NAME:
                    systemVars.put(templateVariable.getTemplateVariableType(), whatsappUserMetadata.getStudentUserData().getStandardName());
                    break;
                case CURRENT_DATE:
                    systemVars.put(templateVariable.getTemplateVariableType(), DateUtils.getFormattedDate(DateUtils.now()));
                    break;
                case CURRENT_DATE_WITH_TIME:
                    systemVars.put(templateVariable.getTemplateVariableType(), DateUtils.getFormattedDate(DateUtils.now(),
                            DateUtils.DEFAULT_DATE_TIME_FORMAT, DateUtils.DEFAULT_TIMEZONE));
                    break;
                case INSTITUTE_NAME:
                    systemVars.put(templateVariable.getTemplateVariableType(), instituteName);
                    break;
                case ADMISSION_NUMBER:
                    systemVars.put(templateVariable.getTemplateVariableType(), whatsappUserMetadata.getStudentUserData().getAdmissionNumber());
                    break;
                default:
                    break;
            }
        }
        return systemVars;
    }

    public String renderTemplate(String template, TemplateVariableDetails templateVariableDetails, Map<TemplateVariableType, String> systemVarValues, Map<String, String> customVarValues){
        if(StringUtils.isBlank(template) || templateVariableDetails == null || CollectionUtils.isEmpty(templateVariableDetails.getTemplateVariableList())){
            logger.error("Invalid template or variable details {}, for template {}", templateVariableDetails, template);
            return null;
        }

        final VelocityContext context = new VelocityContext();
        for(TemplateVariable templateVariable : templateVariableDetails.getTemplateVariableList()){
            String varValue = null;
            if(templateVariable.getTemplateVariableType() == TemplateVariableType.CUSTOM){
                varValue = customVarValues == null ? null : customVarValues.get(templateVariable.getVarName());
            }else{
                varValue = systemVarValues == null ? null : systemVarValues.get(templateVariable.getTemplateVariableType());
            }
            context.put(templateVariable.getVarName(), varValue);
        }

        final StringWriter renderedContent = new StringWriter();
        Velocity.evaluate(context, renderedContent, template, template);
        return renderedContent.toString();
    }

}
