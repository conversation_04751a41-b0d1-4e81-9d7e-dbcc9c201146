package com.lernen.cloud.whatsapp.handler;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import com.embrate.cloud.core.api.service.communication.*;
import com.embrate.cloud.core.api.whatsapp.UserWhatsappPayload;
import com.embrate.cloud.core.api.whatsapp.WhatsappPayloadWrapper;
import com.embrate.cloud.core.api.whatsapp.WhatsappPreferences;
import com.embrate.cloud.core.api.whatsapp.WhatsappResponse;
import com.embrate.cloud.core.lib.service.communication.CommunicationServiceManager;
import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.whatsapp.service.WhatsappSenderFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.notification.NotificationDetails;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.utils.PhoneNumberUtils;
import com.lernen.cloud.whatsapp.service.IWhatsappSender;

import static com.lernen.cloud.core.utils.PhoneNumberUtils.DEFAULT_COUNTRY;

/**
 * 
 * <AUTHOR>
 *
 */
public class WhatsappManager {

	private static final Logger logger = LogManager.getLogger(WhatsappManager.class);

	private final WhatsappSenderFactory whatsappSenderFactory;
	private final CommunicationServiceManager communicationServiceManager;
	private final UserPreferenceSettings userPreferenceSettings;

	public WhatsappManager(WhatsappSenderFactory whatsappSenderFactory, CommunicationServiceManager communicationServiceManager, UserPreferenceSettings userPreferenceSettings) {
		this.whatsappSenderFactory = whatsappSenderFactory;
		this.communicationServiceManager = communicationServiceManager;
		this.userPreferenceSettings = userPreferenceSettings;
	}

	public Double balance(int instituteId) {
		final WhatsappPreferences whatsappPreferences = userPreferenceSettings.getWhatsappPreferences(instituteId);

		if (!whatsappPreferences.isWhatsappServiceEnabled()) {
			logger.info("Whatsapp service is not enabled for institute {}", instituteId);
			return null;
		}

		CommunicationServiceProvider communicationServiceProvider = whatsappPreferences.getWhatsappProvider();
		IWhatsappSender whatsappSender = whatsappSenderFactory.getWhatsappSender(communicationServiceProvider);
		if (whatsappSender == null) {
			logger.error("Whatsapp service not setup for instituteId {}. Skipping Whatsapp!", instituteId);
			return null;
		}

		return whatsappSender.balance(instituteId);
	}

	public String uploadMedia(int instituteId, FileData image) {
		final WhatsappPreferences whatsappPreferences = userPreferenceSettings.getWhatsappPreferences(instituteId);

		if (!whatsappPreferences.isWhatsappServiceEnabled()) {
			logger.info("Whatsapp service is not enabled for institute {}", instituteId);
			return null;
		}

		CommunicationServiceProvider communicationServiceProvider = whatsappPreferences.getWhatsappProvider();
		IWhatsappSender whatsappSender = whatsappSenderFactory.getWhatsappSender(communicationServiceProvider);
		if (whatsappSender == null) {
			logger.error("Whatsapp service not setup for instituteId {}. Skipping Whatsapp!", instituteId);
			return null;
		}

		return whatsappSender.uploadMedia(instituteId, image);
	}

	public WhatsappResponse sendWhatsappAsync(int instituteId, IWhatsappPayloadBuilder whatsappPayloadBuilder, UUID userId) {
		return sendWhatsapp(instituteId, whatsappPayloadBuilder, userId, false);
	}

	public WhatsappResponse sendWhatsapp(int instituteId, IWhatsappPayloadBuilder whatsappPayloadBuilder, UUID userId,
										 boolean synchronous) {

		final WhatsappPreferences whatsappPreferences = userPreferenceSettings.getWhatsappPreferences(instituteId);

		if (!whatsappPreferences.isWhatsappServiceEnabled()) {
			logger.info("Whatsapp service is not enabled for institute {}", instituteId);
			return WhatsappResponse.failureResponse("Whatsapp service is not enabled for your institute");
		}

		CommunicationServiceProvider communicationServiceProvider = whatsappPreferences.getWhatsappProvider();
		IWhatsappSender whatsappSender = whatsappSenderFactory.getWhatsappSender(communicationServiceProvider);
		if (whatsappSender == null) {
			logger.error("Whatsapp service not setup for instituteId {}. Skipping Whatsapp!", instituteId);
			return WhatsappResponse.failureResponse("Whatsapp service not setup for instituteId.");
		}

		WhatsappPayloadWrapper previewWhatsappPayloadWrapper = whatsappPayloadBuilder.getWhatsappPreviewPayload();
		if (previewWhatsappPayloadWrapper == null) {
			logger.error("Empty Whatsapp content for instituteId {} for user {}. Skipping Whatsapp!", instituteId, userId);
			return WhatsappResponse.failureResponse("Invalid Whatsapp details.");
		}

		if (StringUtils.isNotBlank(previewWhatsappPayloadWrapper.getErrorReason())) {
			logger.error("Payload error {} for instituteId {} for user {}. Skipping Whatsapp!",
					previewWhatsappPayloadWrapper.getErrorReason(), instituteId, userId);
			return WhatsappResponse.failureResponse(previewWhatsappPayloadWrapper.getErrorReason());
		}

		if (!validateWhatsappPayloadWrapper(instituteId, userId, previewWhatsappPayloadWrapper)) {
			logger.error("Invalid WhatsappPayloadWrapper for instituteId {} for user {} of batchId {}. Skipping Whatsapp!",
					instituteId, userId, previewWhatsappPayloadWrapper.getBatchId());
			return WhatsappResponse.failureResponse("Invalid Whatsapp details.");
		}

		Integer academicSessionId = previewWhatsappPayloadWrapper.getAcademicSessionId();
		UserType userType = previewWhatsappPayloadWrapper.getUserType();
		UserWhatsappCreditData previewUserWhatsappCreditData = getUserWhatsappCreditData(instituteId,
				previewWhatsappPayloadWrapper.getUserWhatsappPayload());

		if (CollectionUtils.isEmpty(previewUserWhatsappCreditData.getUserWhatsappPayloadList())) {
			logger.error("No valid user to send Whatsapp for instituteId {} for user {} of batchId {}. Skipping Whatsapp!",
					instituteId, userId, previewWhatsappPayloadWrapper.getBatchId());
			return WhatsappResponse
					.failureResponse("No valid user found to send Whatsapp. Please check contact details and Whatsapp content.");
		}

		logger.info(
				"Total Whatsapp to be send {}, total credit {}, unique users {}, for institute {}, userType {}, payload {}",
				previewUserWhatsappCreditData.getUserCount(), previewUserWhatsappCreditData.getWhatsappCredit(),
				previewUserWhatsappCreditData.getUserWhatsappPayloadList().size(), instituteId, userType,
				previewWhatsappPayloadWrapper);

//		CommunicationServiceTransactionResponse whatsappTransactionResponse = communicationServiceManager.validateAndTransaction(
//				instituteId, communicationServiceProvider,
//				previewUserWhatsappCreditData.getWhatsappCredit(), userType, userId,
//				previewWhatsappPayloadWrapper.getWhatsappTransactionType(), whatsappPreferences.getBufferWhatsappCount());

//		if (whatsappTransactionResponse == null || whatsappTransactionResponse.getTransactionId() == null) {
//			logger.error("Failed to add Whatsapp transaction. Not sending Whatsapp for institute {} and payload {}", instituteId,
//					previewWhatsappPayloadWrapper);
//			return WhatsappResponse.failureResponse(whatsappTransactionResponse == null ? "Unable to perform Whatsapp transaction"
//					: whatsappTransactionResponse.getErrorReason());
//		}

		List<UserWhatsappPayload> finalUserWhatsappPayloadList = getFinalWhatsappPayload(instituteId, whatsappPayloadBuilder,
				previewUserWhatsappCreditData);

		if (CollectionUtils.isEmpty(finalUserWhatsappPayloadList)) {
			logger.error(
					"No final valid user to send Whatsapp for instituteId {} for user {}. Reverting transaction and skipping Whatsapp!",
					instituteId, userId);
//			communicationServiceManager.refundServiceTransaction(instituteId, communicationServiceProvider, userType, userId, previewUserWhatsappCreditData.getWhatsappCredit(),
//					whatsappTransactionResponse.getTransactionId());
			return WhatsappResponse.failureResponse(
					"Unable to send Whatsapp for given users. If any credits are deducted, they will be refunded.");
		}

		WhatsappServiceHandler whatsappServiceHandler = new WhatsappServiceHandler(whatsappSender);
		if (!synchronous) {
			sendWhatsappAsync(instituteId, communicationServiceProvider, userId, academicSessionId, previewWhatsappPayloadWrapper, finalUserWhatsappPayloadList,
					userType, whatsappServiceHandler, null);
			return WhatsappResponse.successResponse();
		} else {
			sendWhatsapp(instituteId, communicationServiceProvider, userId, academicSessionId, previewWhatsappPayloadWrapper,
					finalUserWhatsappPayloadList, userType, whatsappServiceHandler,
					null);

			return WhatsappResponse.successResponse();
		}

	}

	private void sendWhatsappAsync(int instituteId, CommunicationServiceProvider communicationServiceProvider, UUID userId, Integer academicSessionId,
								   WhatsappPayloadWrapper previewWhatsappPayloadWrapper, List<UserWhatsappPayload> finalUserWhatsappPayloadList, UserType userType,
								   WhatsappServiceHandler whatsappServiceHandler, UUID transactionId) {

		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				sendWhatsapp(instituteId, communicationServiceProvider, userId, academicSessionId, previewWhatsappPayloadWrapper, finalUserWhatsappPayloadList,
						userType, whatsappServiceHandler, transactionId);
			}
		});
		t.start();
	}

	private List<UserWhatsappPayload> getFinalWhatsappPayload(int instituteId, IWhatsappPayloadBuilder whatsappPayloadBuilder,
															  UserWhatsappCreditData previewUserWhatsappCreditData) {
		try {
			if (!whatsappPayloadBuilder.executeWhatsappAction()) {
				logger.error("Error while executing Whatsapp action for institute {}", instituteId);
				return null;
			}

			if (whatsappPayloadBuilder.previewPayloadIsFinal()) {
				return previewUserWhatsappCreditData.getUserWhatsappPayloadList();
			}

			List<UserWhatsappPayload> finalUserWhatsappPayloadListWithoutValidation = whatsappPayloadBuilder.getFinalUserWhatsappPayload();
			//Credits are updated in here for each payload
			List<UserWhatsappPayload> finalUserWhatsappPayloadList = validateAndGetFinalWhatsappPayload(instituteId, previewUserWhatsappCreditData, finalUserWhatsappPayloadListWithoutValidation);
			if (finalUserWhatsappPayloadList == null) {
				return null;
			}

			return finalUserWhatsappPayloadList;

		} catch (Exception e) {
			logger.error(
					"Error while executing Whatsapp action and getting final payload for instituteId {}, previewUserWhatsappCreditData {} ",
					instituteId, previewUserWhatsappCreditData, e);
			return null;
		}
	}

	private void sendWhatsapp(int instituteId, CommunicationServiceProvider communicationServiceProvider, UUID userId, Integer academicSessionId,
							  WhatsappPayloadWrapper previewWhatsappPayloadWrapper, List<UserWhatsappPayload> finalUserWhatsappPayloadList, UserType userType, WhatsappServiceHandler whatsappServiceHandler, UUID transactionId) {

		communicationServiceManager.executeService(instituteId,
				communicationServiceProvider, academicSessionId, previewWhatsappPayloadWrapper.getNotificationType(),
				previewWhatsappPayloadWrapper.getBatchId(), previewWhatsappPayloadWrapper.getBatchName(),
				finalUserWhatsappPayloadList, previewWhatsappPayloadWrapper.getUserType(), whatsappServiceHandler, transactionId, false);
	}

	private boolean validateWhatsappPayloadWrapper(int instituteId, UUID userId, WhatsappPayloadWrapper whatsappPayloadWrapper) {

		if (CollectionUtils.isEmpty(whatsappPayloadWrapper.getUserWhatsappPayload())) {
			logger.error("Empty Whatsapp content for instituteId {} for user {}. Skipping Whatsapp!", instituteId, userId);
			return false;
		}

		if (whatsappPayloadWrapper.getNotificationType() == null) {
			logger.error("Invalid Whatsapp details for instituteId {} for user {}. Skipping Whatsapp!", instituteId, userId);
			return false;
		}
		return true;
	}

	private List<UserWhatsappPayload> validateAndGetFinalWhatsappPayload(int instituteId, UserWhatsappCreditData previewUserWhatsappCreditData,
																		 List<UserWhatsappPayload> finalUserWhatsappPayloadList) {

		if (CollectionUtils.isEmpty(finalUserWhatsappPayloadList)) {
			logger.error("Empty final Whatsapp payload list for instituteId {}", instituteId);
			return null;
		}

		UserWhatsappCreditData finalUserWhatsappCreditData = getUserWhatsappCreditData(instituteId, finalUserWhatsappPayloadList);
		if (finalUserWhatsappCreditData.getWhatsappCredit() != previewUserWhatsappCreditData.getWhatsappCredit()) {
			logger.error("Final Whatsapp credit {} does not match with preview Whatsapp credits {} , instituteId {}",
					finalUserWhatsappCreditData.getWhatsappCredit(), previewUserWhatsappCreditData.getWhatsappCredit(), instituteId);
			return null;
		}

		if (finalUserWhatsappCreditData.getUserCount() != previewUserWhatsappCreditData.getUserCount()) {
			logger.error("Final Whatsapp user count {} does not match with preview Whatsapp user count {}, instituteId {}",
					finalUserWhatsappCreditData.getUserCount(), previewUserWhatsappCreditData.getUserCount(), instituteId);
			return null;
		}

		if (finalUserWhatsappCreditData.getUserWhatsappPayloadList().size() != previewUserWhatsappCreditData.getUserWhatsappPayloadList()
				.size()) {
			logger.error(
					"Final Whatsapp unique user count {} does not match with preview Whatsapp unique user count {}, instituteId {}",
					finalUserWhatsappCreditData.getUserWhatsappPayloadList().size(),
					previewUserWhatsappCreditData.getUserWhatsappPayloadList().size(), instituteId);
			return null;
		}

		return finalUserWhatsappCreditData.getUserWhatsappPayloadList();
	}

	private UserWhatsappCreditData getUserWhatsappCreditData(int instituteId, List<UserWhatsappPayload> inputUserWhatsappPayloadList) {

		List<UserWhatsappPayload> userWhatsappPayload = new ArrayList<UserWhatsappPayload>();
		int whatsappCount = 0;
		int userCount = 0;
		for (UserWhatsappPayload payload : inputUserWhatsappPayloadList) {
			List<String> mobileNumbers = new ArrayList<String>();

			if (CollectionUtils.isEmpty(payload.getDestinationChannelIds())) {
				logger.error("Empty contacts for instituteId {} for user {}. Skipping Whatsapp!", instituteId,
						payload.getUserId());
				continue;
			}
//			if (StringUtils.isBlank(payload.getMessagePayload())) {
//				logger.error("Empty message for instituteId {} for user {}, Skipping Whatsapp!", instituteId,
//						payload.getUserId());
//				continue;
//			}

			int userWhatsappCount = 0;
			String message = payload.getMessagePayload();
			for (String number : payload.getDestinationChannelIds()) {
				final String e164PhoneNumber = PhoneNumberUtils.getE164FormattedNumber(number, DEFAULT_COUNTRY);
				if (StringUtils.isBlank(e164PhoneNumber)) {
					logger.error("Invalid contact number {} for user {}, instituteId {} . Skipping Whatsapp!", number,
							payload.getUserId(), instituteId);
					continue;
				}

				Integer whatsappCredits = NotificationDetails.getWhatsappCredits(message);
				userWhatsappCount += whatsappCredits;
				whatsappCount += whatsappCredits;
				userCount++;
				mobileNumbers.add(number);
			}

			if (!CollectionUtils.isEmpty(mobileNumbers)) {
				userWhatsappPayload.add(new UserWhatsappPayload(payload.getUserId(), payload.getMessagePayload(), mobileNumbers,
						payload.getDltTemplateId(),
						payload.getImageLink(), payload.getHeaderVariableValues(), payload.getBodyVariableValues()));
			}
		}

		return new UserWhatsappCreditData(userCount, whatsappCount, userWhatsappPayload);
	}

	private class UserWhatsappCreditData {

		private final int userCount;
		private final int whatsappCredit;
		private final List<UserWhatsappPayload> userWhatsappPayloadList;

		public UserWhatsappCreditData(int userCount, int whatsappCredit, List<UserWhatsappPayload> userWhatsappPayloadList) {
			this.userCount = userCount;
			this.whatsappCredit = whatsappCredit;
			this.userWhatsappPayloadList = userWhatsappPayloadList;
		}

		public int getUserCount() {
			return userCount;
		}

		public int getWhatsappCredit() {
			return whatsappCredit;
		}

		public List<UserWhatsappPayload> getUserWhatsappPayloadList() {
			return userWhatsappPayloadList;
		}

		@Override
		public String toString() {
			return "UserWhatsappCreditData{" +
					"userCount=" + userCount +
					", whatsappCredit=" + whatsappCredit +
					", userWhatsappPayloadList=" + userWhatsappPayloadList +
					'}';
		}
	}
}
