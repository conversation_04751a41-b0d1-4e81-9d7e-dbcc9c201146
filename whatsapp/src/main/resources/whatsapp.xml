<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-3.0.xsd 
	http://www.springframework.org/schema/context
	http://www.springframework.org/schema/context/spring-context-3.0.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

	<context:annotation-config />
	<context:property-placeholder
		location="classpath*:core-lib-${lernen_env}.properties, classpath*:dao-tier-${lernen_env}.properties"
		system-properties-mode="OVERRIDE" />
		
	<import resource="classpath:core-lib.xml" />
	<import resource="classpath:core-utils.xml" />

	<bean id="webPayWhatsappSender" class="com.lernen.cloud.whatsapp.service.WebPayWhatsappSender">
		<constructor-arg name="restAPIHandler" ref="restAPIHandler" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
	</bean>

	<bean id="whatsappSenderFactory" class="com.lernen.cloud.whatsapp.service.WhatsappSenderFactory">
		<constructor-arg name="webPayWhatsappSender" ref="webPayWhatsappSender" />
	</bean>

	<bean id="whatsappManager" class="com.lernen.cloud.whatsapp.handler.WhatsappManager">
		<constructor-arg name="whatsappSenderFactory" ref="whatsappSenderFactory" />
		<constructor-arg name="communicationServiceManager" ref="communicationServiceManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
	</bean>

	<bean id="customWhatsappHandler" class="com.lernen.cloud.whatsapp.handler.CustomWhatsappHandler">
		<constructor-arg name="whatsappManager" ref="whatsappManager" />
		<constructor-arg name="studentManager" ref="studentManager" />
		<constructor-arg name="staffManager" ref="staffManager" />
		<constructor-arg name="instituteManager" ref="instituteManager" />
		<constructor-arg name="userPreferenceSettings" ref="userPreferenceSettings" />
		<constructor-arg name="customWhatsappContentBuilder" ref="customWhatsappContentBuilder" />
	</bean>

	<bean id="customWhatsappContentBuilder" class="com.lernen.cloud.whatsapp.content.builder.CustomWhatsappContentBuilder">
		<constructor-arg name="notificationTemplateManager" ref="notificationTemplateManager" />
	</bean>

	<context:component-scan base-package="com.lernen.cloud.whatsapp" />
	<!-- <aop:aspectj-autoproxy proxy-target-class="true" /> -->
</beans>
